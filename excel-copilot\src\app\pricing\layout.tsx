import { Metadata } from 'next';
import { ReactNode } from 'react';

import StripeScriptProvider from '@/components/billing/stripe-script-provider';

export const metadata: Metadata = {
  title: 'Planos e Preços | Excel Copilot',
  description: 'Conheça nossos planos e escolha o que melhor atende às suas necessidades.',
};

export default function PricingLayout({ children }: { children: ReactNode }) {
  return (
    <div className="flex flex-col min-h-screen items-center justify-center py-12">
      <div className="container px-4 md:px-6">
        <StripeScriptProvider>{children}</StripeScriptProvider>
      </div>
    </div>
  );
}
