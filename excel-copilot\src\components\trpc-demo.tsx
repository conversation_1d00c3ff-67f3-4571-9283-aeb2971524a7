'use client';

import { useRouter } from 'next/navigation';
import { useState } from 'react';

import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import Input from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Spinner } from '@/components/ui/spinner';
import { trpc } from '@/lib/trpc';

// Definir interface para workbook
interface Workbook {
  id: string;
  name: string;
  description?: string | null;
  userId: string;
  isPublic: boolean;
  createdAt: Date;
  updatedAt: Date;
  lastAccessedAt?: Date;
  sheets: Array<{ id: string; name: string }>;
}

export function TRPCDemo() {
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const router = useRouter();

  // Usando tRPC para buscar dados - procedimento correto é workbook_getAll
  const workbooksQuery = trpc.workbook_getAll.useQuery();

  // Mutation para criar workbook
  const createWorkbookMutation = trpc.workbook_create.useMutation({
    onSuccess: () => {
      setTitle('');
      setDescription('');
      workbooksQuery.refetch();
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    createWorkbookMutation.mutate({
      name: title,
      description,
    });
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Criar Nova Planilha</CardTitle>
          <CardDescription>Use o formulário abaixo para criar uma nova planilha</CardDescription>
        </CardHeader>
        <form onSubmit={handleSubmit}>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="title">Título</Label>
              <Input
                id="title"
                value={title}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setTitle(e.target.value)}
                required
              />
            </div>
            <div>
              <label
                htmlFor="description"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300"
              >
                Descrição
              </label>
              <textarea
                id="description"
                className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-800 dark:border-gray-700 dark:text-white"
                placeholder="Descreva seu produto"
                value={description}
                onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) =>
                  setDescription(e.target.value)
                }
              />
            </div>
          </CardContent>
          <CardFooter>
            <Button type="submit" disabled={createWorkbookMutation.isLoading}>
              {createWorkbookMutation.isLoading ? <Spinner className="mr-2" /> : null}
              Criar Planilha
            </Button>
          </CardFooter>
        </form>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Suas Planilhas</CardTitle>
          <CardDescription>Lista de todas as suas planilhas</CardDescription>
        </CardHeader>
        <CardContent>
          {workbooksQuery.isLoading ? (
            <div className="flex justify-center py-4">
              <Spinner />
            </div>
          ) : workbooksQuery.data?.length ? (
            <ul className="space-y-2">
              {workbooksQuery.data.map((workbook: Workbook) => (
                <li
                  key={workbook.id}
                  className="flex justify-between items-center p-2 border rounded"
                >
                  <div className="flex flex-col">
                    <span className="font-medium">{workbook.name}</span>
                    {workbook.description && (
                      <span className="text-xs text-muted-foreground">{workbook.description}</span>
                    )}
                    <span className="text-xs text-muted-foreground">
                      Atualizado em: {new Date(workbook.updatedAt).toLocaleDateString()}
                    </span>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => router.push(`/workbook/${workbook.id}`)}
                  >
                    Abrir
                  </Button>
                </li>
              ))}
            </ul>
          ) : (
            <p className="text-center text-muted-foreground py-4">Nenhuma planilha encontrada</p>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
