/**
 * Configurações explícitas para autenticação durante desenvolvimento
 *
 * Este módulo centraliza todas as flags relacionadas à autenticação,
 * especialmente para ambientes de desenvolvimento, tornando os
 * comportamentos mais previsíveis e explícitos.
 */

import { logger } from '@/lib/logger';

// Determinar ambiente atual
const NODE_ENV = process.env.NODE_ENV || 'development';
const IS_DEVELOPMENT = NODE_ENV === 'development';
const IS_TEST = NODE_ENV === 'test';
const IS_PRODUCTION = NODE_ENV === 'production';

// PRODUÇÃO: Usuário demo completamente desativado
const USE_DEMO_USER = false;

// Registrar configuração ao inicializar
if (USE_DEMO_USER) {
  logger.warn('MODO DE DESENVOLVIMENTO: Autenticação com usuário demo está ATIVADA');

  if (IS_PRODUCTION) {
    logger.error('ALERTA DE SEGURANÇA: Usuário demo ativado em ambiente de produção!');
  }
} else if (IS_DEVELOPMENT) {
  logger.info('Autenticação com usuário demo está DESATIVADA mesmo em modo de desenvolvimento');
}

// Usuário demo padrão
const DEMO_USER = {
  id: 'demo-user',
  name: 'Usuário Demo',
  email: '<EMAIL>',
};

/**
 * Configurações de autenticação
 */
export const AUTH_FLAGS = {
  /**
   * Se true, permite acesso com usuário demo em desenvolvimento sem autenticação real.
   * Pode ser configurado via AUTH_USE_DEMO_USER=true|false no .env
   */
  USE_DEMO_USER,

  /**
   * Dados do usuário demo
   */
  DEMO_USER,

  /**
   * Tempo de expiração da sessão do usuário demo (30 dias)
   */
  DEMO_SESSION_EXPIRY: 30 * 24 * 60 * 60 * 1000,

  /**
   * Se true, logs de autenticação detalhados serão exibidos
   * Pode ser configurado via AUTH_VERBOSE_LOGGING=true|false no .env
   */
  VERBOSE_LOGGING: process.env.AUTH_VERBOSE_LOGGING === 'true' || IS_DEVELOPMENT,

  /**
   * Ambiente atual
   */
  IS_DEVELOPMENT,
  IS_TEST,
  IS_PRODUCTION,
};
