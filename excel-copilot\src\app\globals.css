/* Importar a fonte Inter do Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Esquema de cores do Excel Copilot - estilo Office */
@layer base {
  :root {
    /* Base Colors */
    --background: 0 0% 98%; /* Ligeiramente menos branco */
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 99%; /* Ligeiramente menos branco */
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 99%; /* Ligeiramente menos branco */
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 97%; /* Ligeiramente mais escuro */
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;

    /* Layout Variables */
    --header-height: 4rem;
    --sidebar-width: 18rem;
    --content-width: min(1200px, 100% - 2rem);
    --page-padding-x: clamp(1rem, 5vw, 2rem);
    --component-spacing: clamp(1rem, 3vw, 3rem);

    /* Animation Durations */
    --duration-fast: 0.15s;
    --duration-medium: 0.3s;
    --duration-slow: 0.5s;

    /* Typography - Fluid sizes that scale with viewport */
    --font-size-xs: clamp(0.75rem, 0.7rem + 0.25vw, 0.875rem);
    --font-size-sm: clamp(0.875rem, 0.8rem + 0.25vw, 1rem);
    --font-size-base: clamp(1rem, 0.9rem + 0.5vw, 1.125rem);
    --font-size-lg: clamp(1.125rem, 1rem + 0.625vw, 1.25rem);
    --font-size-xl: clamp(1.25rem, 1.1rem + 0.75vw, 1.5rem);
    --font-size-2xl: clamp(1.5rem, 1.3rem + 1vw, 1.875rem);
    --font-size-3xl: clamp(1.875rem, 1.6rem + 1.25vw, 2.25rem);
    --font-size-4xl: clamp(2.25rem, 2rem + 1.5vw, 3rem);

    /* Line Heights */
    --line-height-tight: 1.2;
    --line-height-normal: 1.5;
    --line-height-loose: 1.75;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);

    /* Performance Metrics */
    --lcp-elem-bg: transparent; /* Target background for LCP element */
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 48%;

    /* Dark theme specific */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.3);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.4), 0 2px 4px -2px rgb(0 0 0 / 0.4);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.4), 0 4px 6px -4px rgb(0 0 0 / 0.4);
    --lcp-elem-bg: hsl(var(--background)); /* Dark bg for LCP */
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground min-h-screen;
    font-feature-settings:
      'rlig' 1,
      'calt' 1;
    line-height: var(--line-height-normal);
    font-size: var(--font-size-base);
  }

  html.dark {
    color-scheme: dark;
  }

  html.light {
    color-scheme: light;
  }

  /* Evitar flash durante a transição de tema */
  html.transitioning-theme * {
    @apply transition-colors duration-300;
  }

  /* Melhorar renderização de fontes */
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply font-medium tracking-tight;
  }

  h1 {
    @apply text-4xl sm:text-5xl;
  }

  h2 {
    @apply text-3xl sm:text-4xl;
  }

  h3 {
    @apply text-2xl sm:text-3xl;
  }

  html {
    -webkit-text-size-adjust: 100%;
    text-size-adjust: 100%;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    scroll-behavior: smooth;
    height: 100%;
  }

  /* Optimize for performance */
  img,
  video {
    max-width: 100%;
    height: auto;
    /* Prevent content layout shifts */
    content-visibility: auto;
    contain: content;
  }

  /* Focus styles */
  :focus-visible {
    @apply outline-none ring-2 ring-ring ring-offset-2 ring-offset-background;
  }

  /* Remove tap highlight on mobile */
  * {
    -webkit-tap-highlight-color: transparent;
  }

  /* Container queries support */
  @supports (container-type: inline-size) {
    .cq-container {
      container-type: inline-size;
    }

    .cq-sm-only {
      display: none;
    }

    @container (max-width: 640px) {
      .cq-sm-only {
        display: block;
      }
      .cq-md-up {
        display: none;
      }
    }
  }

  /* Typography styles */
  h1,
  .h1 {
    font-size: var(--font-size-4xl);
    line-height: var(--line-height-tight);
    font-weight: 700;
    letter-spacing: -0.025em;
  }

  h2,
  .h2 {
    font-size: var(--font-size-3xl);
    line-height: var(--line-height-tight);
    font-weight: 700;
    letter-spacing: -0.025em;
  }

  h3,
  .h3 {
    font-size: var(--font-size-2xl);
    line-height: var(--line-height-tight);
    font-weight: 600;
  }

  h4,
  .h4 {
    font-size: var(--font-size-xl);
    line-height: var(--line-height-tight);
    font-weight: 600;
  }

  h5,
  .h5 {
    font-size: var(--font-size-lg);
    line-height: var(--line-height-normal);
    font-weight: 600;
  }

  h6,
  .h6 {
    font-size: var(--font-size-base);
    line-height: var(--line-height-normal);
    font-weight: 600;
  }

  p {
    margin-bottom: 1em;
  }

  small {
    font-size: var(--font-size-xs);
  }

  /* Print styles */
  @media print {
    html,
    body {
      background-color: white !important;
      color: black !important;
    }

    .no-print {
      display: none !important;
    }
  }

  /* Tipografia responsiva para dispositivos móveis */
  @media (max-width: 480px) {
    body {
      word-break: break-word;
      overflow-wrap: break-word;
    }

    .mobile-text-ellipsis {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 100%;
    }

    .mobile-clamp-1 {
      display: -webkit-box;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    .mobile-clamp-2 {
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
  }
}

/* Classes utilitárias para o Excel Copilot */
@layer utilities {
  /* Layout helpers */
  .layout-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(min(250px, 100%), 1fr));
    gap: var(--component-spacing);
  }

  /* Truncate text with ellipsis */
  .truncate-2 {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
  }

  .truncate-3 {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    overflow: hidden;
  }

  /* Accessibility helpers */
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
  }

  /* Performance optimization - hint for content-visibility */
  .content-visibility-auto {
    content-visibility: auto;
    contain-intrinsic-size: 1px 5000px; /* Estimated size */
  }

  /* Scrollbar styling */
  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--muted)) transparent;
  }

  .custom-scrollbar::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: hsl(var(--muted));
    border-radius: 20px;
  }

  /* Animations */
  .animate-fade-in {
    animation: fadeIn var(--duration-medium) ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp var(--duration-medium) ease-out;
  }

  @keyframes fadeIn {
    0% {
      opacity: 0;
    }
    100% {
      opacity: 1;
    }
  }

  @keyframes slideUp {
    0% {
      transform: translateY(10px);
      opacity: 0;
    }
    100% {
      transform: translateY(0);
      opacity: 1;
    }
  }
}

/* Estilos específicos do Excel Copilot */
.excel-grid {
  @apply border-collapse w-full;
}

.excel-cell {
  @apply border border-border p-2 whitespace-nowrap overflow-hidden text-ellipsis;
  min-width: 80px;
}

.excel-header-cell {
  @apply bg-muted font-medium text-sm;
  background-color: hsl(125, 55%, 95%);
}

.excel-data-cell {
  @apply text-sm;
}

.excel-numeric {
  @apply text-right;
}

.excel-date {
  @apply text-center;
}

.excel-formula {
  @apply italic;
  color: hsl(125, 55%, 43%);
}

/* Melhorias nos cards para parecer mais Office */
.card-excel {
  @apply rounded-md border border-border bg-card p-4 shadow-sm transition-all duration-200;
}

.card-excel:hover {
  @apply shadow-md;
  border-color: hsl(125, 55%, 43%);
}

/* Tour highlight ajustado para cor verde Excel */
.tour-highlight {
  position: relative;
  z-index: 55 !important;
  box-shadow: 0 0 0 4px hsl(var(--primary)) !important;
  border-radius: 0.25rem;
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%,
  100% {
    box-shadow: 0 0 0 4px hsl(var(--primary) / 0.6) !important;
  }
  50% {
    box-shadow: 0 0 0 4px hsl(var(--primary) / 1) !important;
  }
}

/* Estilos específicos para parecer com Excel */
.excel-header {
  background-color: hsl(125, 55%, 43%);
  color: white;
  font-weight: 600;
}

.excel-ribbon {
  background-color: hsl(125, 55%, 43%);
  border-bottom: 1px solid hsl(125, 55%, 38%);
  padding: 0.5rem;
  display: flex;
  gap: 1rem;
}

.excel-ribbon-button {
  padding: 0.5rem;
  border-radius: 0.25rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  color: white;
  font-size: 0.75rem;
}

.excel-ribbon-button:hover {
  background-color: hsl(125, 55%, 38%);
}

.excel-ribbon-divider {
  width: 1px;
  background-color: hsl(125, 55%, 38%);
}

/* Melhorias na visualização de gráficos */
.bar-chart-container {
  height: 180px;
  width: 100%;
  display: flex;
  align-items: flex-end;
  justify-content: space-around;
  padding: 0 1rem;
}

.bar-chart-bar {
  width: 2rem;
  background-color: hsl(var(--primary) / 0.8);
  border-top-left-radius: 0.25rem;
  border-top-right-radius: 0.25rem;
  transition: height 0.5s ease;
}

@media (prefers-reduced-motion: reduce) {
  .bar-chart-bar {
    transition: none;
  }
}

.bar-chart-label {
  font-size: 0.75rem;
  color: hsl(var(--muted-foreground));
  margin-top: 0.25rem;
  text-align: center;
}

/* Melhorias para acessibilidade */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

/* Melhorias no foco e interação */
.focus-visible-ring {
  @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 focus-visible:ring-offset-background;
}

/* Melhorias na responsividade */
.fluid-container {
  width: 100%;
  max-width: 72rem; /* 1152px */
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .fluid-container {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .fluid-container {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

/* High priority UI elements - optimization for LCP */
.lcp-target {
  background-color: var(--lcp-elem-bg);
  transition: background-color var(--duration-medium) ease;
}

/* Otimização para usuários que preferem menos movimento */
@media (prefers-reduced-motion: reduce) {
  *,
  ::before,
  ::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .custom-animation,
  .animate-spin,
  .animate-pulse,
  .animate-bounce {
    animation: none !important;
  }

  /* Alternativas estáticas para indicadores de carregamento */
  .loading-indicator {
    opacity: 0.7 !important;
  }

  /* Desativando efeitos de hover que usam transformações */
  .hover-transform {
    transform: none !important;
  }
}

/* Melhorias na acessibilidade - contraste e foco */
.text-enhanced-contrast {
  color: hsl(var(--foreground) / 0.9) !important; /* Aumentar contraste de texto */
}

.dark .text-enhanced-contrast {
  color: hsl(var(--foreground) / 0.95) !important; /* Ainda mais contraste no modo escuro */
}

/* Botões e interativos com melhor visualização de foco */
.a11y-focus:focus-visible {
  outline: 3px solid hsl(var(--primary)) !important;
  outline-offset: 2px !important;
  box-shadow: 0 0 0 3px hsl(var(--background)) !important;
  position: relative;
  z-index: 10;
}

/* Classes auxiliares para componentes em dispositivos pequenos */
@media (max-width: 480px) {
  .mobile-enhanced-tap {
    min-height: 44px; /* Tamanho mínimo para alvos de toque em mobile */
    min-width: 44px;
    padding: 0.5rem !important;
  }

  .mobile-focus-visible {
    position: relative;
  }

  .mobile-focus-visible:focus::after {
    content: '';
    position: absolute;
    top: -4px;
    left: -4px;
    right: -4px;
    bottom: -4px;
    border: 2px solid hsl(var(--primary));
    border-radius: inherit;
    z-index: 1;
  }

  /* Aumentar contraste para textos pequenos em mobile */
  .small-text {
    font-weight: 500 !important; /* Ligeramente mais forte */
    color: hsl(var(--foreground) / 0.95) !important;
  }
}
