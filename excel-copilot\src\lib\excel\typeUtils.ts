import { ExcelOperationType } from '@/types/index';

/**
 * Converte uma string para o tipo de operação do enum ExcelOperationType
 * Esta função é útil quando temos strings literais e precisamos convertê-las para o enum
 * @param type String representando o tipo de operação
 * @returns Valor do enum correspondente ou undefined
 */
export function stringToExcelOperationType(type: string): ExcelOperationType | undefined {
  // Converter para maiúsculas para normalizar
  const normalizedType = type.toUpperCase();

  // Verificar se existe no enum
  const enumValue = Object.entries(ExcelOperationType).find(
    ([_, value]) => value === normalizedType
  );

  return enumValue ? (enumValue[1] as ExcelOperationType) : undefined;
}

/**
 * Converte um tipo de operação do enum ExcelOperationType para string
 * @param type Tipo de operação Excel
 * @returns String representando o tipo
 */
export function excelOperationTypeToString(type: ExcelOperationType): string {
  return type;
}

/**
 * Cria uma operação Excel com o tipo correto a partir de uma string
 * @param type String representando o tipo de operação
 * @param data Dados da operação
 * @returns Objeto de operação Excel com o tipo correto
 */
export function createExcelOperation(
  type: string,
  data: any
): { type: ExcelOperationType; data: any } {
  const operationType = stringToExcelOperationType(type);

  if (!operationType) {
    throw new Error(`Tipo de operação inválido: ${type}`);
  }

  return {
    type: operationType,
    data,
  };
}

/**
 * Normaliza o tipo de operação para garantir que é um ExcelOperationType válido
 * Retorna o tipo original se for válido, ou tenta converter de string se necessário
 * @param type Tipo de operação (pode ser string ou ExcelOperationType)
 * @returns ExcelOperationType válido ou undefined
 */
export function normalizeOperationType(
  type: string | ExcelOperationType
): ExcelOperationType | undefined {
  if (typeof type === 'string') {
    return stringToExcelOperationType(type);
  }

  return type;
}
