import { extractGroup } from '@/utils/regex-utils';

import { ExcelOperationType } from '../../types';
import { ExcelOperation } from '../excel/types';
import { columnLetterToIndex } from '../utils';

/**
 * Interface para configuração de gráficos avançados
 */
export interface AdvancedChartConfig {
  // Configuração básica do gráfico
  type: ChartType;
  sourceRange: string;
  destinationRange?: string;
  title?: string;

  // Eixos e séries
  xAxis?: AxisConfig;
  yAxis?: AxisConfig;
  series?: SeriesConfig[];

  // Estilo e formatação
  colors?: string[];
  theme?: 'default' | 'monochrome' | 'gradient' | 'pastel' | 'bold' | 'vintage';
  animation?: boolean;

  // Layout e legenda
  legend?: LegendConfig;
  grid?: GridConfig;
  paddingConfig?: {
    top?: number;
    right?: number;
    bottom?: number;
    left?: number;
  };

  // Opções avançadas
  annotations?: AnnotationConfig[];
  subcharts?: boolean; // Para mini gráficos de visão geral
  responsive?: boolean;
  stacked?: boolean;
}

/**
 * Tipos disponíveis de gráficos avançados
 */
export type ChartType =
  | 'area-spline'
  | 'area-step'
  | 'bar-grouped'
  | 'bar-stacked'
  | 'bar-stacked100'
  | 'bubble'
  | 'bullet'
  | 'candlestick'
  | 'chord'
  | 'combination'
  | 'donut'
  | 'funnel'
  | 'gantt'
  | 'gauge'
  | 'heatmap'
  | 'line-step'
  | 'lollipop'
  | 'marimekko'
  | 'nightingale'
  | 'polar'
  | 'pyramid'
  | 'radar'
  | 'sankey'
  | 'scatter-3d'
  | 'stream'
  | 'sunburst'
  | 'treemap'
  | 'violin'
  | 'wordcloud';

/**
 * Configuração de eixo
 */
export interface AxisConfig {
  title?: string;
  min?: number;
  max?: number;
  tickFormat?: string;
  gridLines?: boolean;
  showLabels?: boolean;
  labelAngle?: number;
  labelTruncate?: number;
}

/**
 * Configuração de série de dados
 */
export interface SeriesConfig {
  name?: string;
  type?: 'line' | 'bar' | 'area' | 'scatter';
  color?: string;
  lineStyle?: 'solid' | 'dashed' | 'dotted';
  markerType?: 'circle' | 'square' | 'triangle' | 'diamond' | 'none';
  fillOpacity?: number;
  visible?: boolean;
  stack?: string;
}

/**
 * Configuração de legenda
 */
export interface LegendConfig {
  show?: boolean;
  position?: 'top' | 'bottom' | 'left' | 'right';
  orientation?: 'horizontal' | 'vertical';
  clickable?: boolean;
}

/**
 * Configuração de grade
 */
export interface GridConfig {
  x?: boolean;
  y?: boolean;
  color?: string;
  opacity?: number;
  dashed?: boolean;
}

/**
 * Configuração de anotações
 */
export interface AnnotationConfig {
  type: 'line' | 'rect' | 'text';
  value?: number | [number, number];
  text?: string;
  color?: string;
  position?: 'start' | 'middle' | 'end';
}

/**
 * Extrai operações de gráficos avançados a partir do texto de comando
 */
export function extractAdvancedChartOperations(text: string): ExcelOperation[] {
  const operations: ExcelOperation[] = [];

  // Padrões para diferentes tipos de gráficos
  const patterns = [
    // Padrão para gráfico de radar
    {
      regex:
        /(?:crie|adicione|gere|insira)\s+(?:um)?\s+gráfico\s+(?:de\s+)?(radar|teia\s+de\s+aranha)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,
      handler: (matches: RegExpMatchArray): ExcelOperation | null => {
        const chartType = matches[1]?.toLowerCase().includes('radar') ? 'radar' : 'radar';
        const sourceRange = matches[2]?.trim();
        const title = matches[3] || matches[4] || matches[5];

        if (!sourceRange) {
          return null;
        }

        return {
          type: ExcelOperationType.ADVANCED_CHART,
          data: {
            type: chartType as ChartType,
            sourceRange,
            title: title || 'Gráfico de Radar',
            legend: {
              show: true,
              position: 'right',
            },
            animation: true,
          },
        };
      },
    },

    // Padrão para gráfico de bolhas
    {
      regex:
        /(?:crie|adicione|gere|insira)\s+(?:um)?\s+gráfico\s+(?:de\s+)?(?:bolhas?|bubble)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,
      handler: (matches: RegExpMatchArray): ExcelOperation | null => {
        const sourceRange = matches[1]?.trim();
        const title = matches[2] || matches[3] || matches[4];

        if (!sourceRange) {
          return null;
        }

        return {
          type: ExcelOperationType.ADVANCED_CHART,
          data: {
            type: 'bubble',
            sourceRange,
            title: title || 'Gráfico de Bolhas',
            xAxis: {
              title: 'Eixo X',
              gridLines: true,
            },
            yAxis: {
              title: 'Eixo Y',
              gridLines: true,
            },
            legend: {
              show: true,
              position: 'bottom',
            },
          },
        };
      },
    },

    // Padrão para gráfico de funil
    {
      regex:
        /(?:crie|adicione|gere|insira)\s+(?:um)?\s+gráfico\s+(?:de\s+)?(?:funil|funnel)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,
      handler: (matches: RegExpMatchArray): ExcelOperation | null => {
        const sourceRange = matches[1]?.trim();
        const title = matches[2] || matches[3] || matches[4];

        if (!sourceRange) {
          return null;
        }

        return {
          type: ExcelOperationType.ADVANCED_CHART,
          data: {
            type: 'funnel',
            sourceRange,
            title: title || 'Gráfico de Funil',
            legend: {
              show: true,
              position: 'right',
            },
            annotations: [
              {
                type: 'text',
                text: '%',
              },
            ],
          },
        };
      },
    },

    // Padrão para gráfico de área-spline
    {
      regex:
        /(?:crie|adicione|gere|insira)\s+(?:um)?\s+gráfico\s+(?:de\s+)?(?:área[\s-]spline|área[\s-]curva)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?(?:\s+(?:empilhado|stacked))?/i,
      handler: (matches: RegExpMatchArray): ExcelOperation | null => {
        const sourceRange = matches[1]?.trim();
        const title = matches[2] || matches[3] || matches[4];
        const isStacked =
          matches[0]?.toLowerCase().includes('empilhado') ||
          matches[0]?.toLowerCase().includes('stacked');

        if (!sourceRange) {
          return null;
        }

        return {
          type: ExcelOperationType.ADVANCED_CHART,
          data: {
            type: 'area-spline',
            sourceRange,
            title: title || 'Gráfico de Área Spline',
            stacked: isStacked,
            xAxis: {
              gridLines: false,
            },
            yAxis: {
              gridLines: true,
            },
            grid: {
              y: true,
              x: false,
            },
          },
        };
      },
    },

    // Padrão para gráfico de barras agrupadas
    {
      regex:
        /(?:crie|adicione|gere|insira)\s+(?:um)?\s+gráfico\s+(?:de\s+)?(?:barras?[\s-]agrupadas?|barras?[\s-]grouped|barras?[\s-]clusters?)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,
      handler: (matches: RegExpMatchArray): ExcelOperation | null => {
        const sourceRange = matches[1]?.trim();
        const title = matches[2] || matches[3] || matches[4];

        if (!sourceRange) {
          return null;
        }

        return {
          type: ExcelOperationType.ADVANCED_CHART,
          data: {
            type: 'bar-grouped',
            sourceRange,
            title: title || 'Gráfico de Barras Agrupadas',
            xAxis: {
              gridLines: false,
            },
            yAxis: {
              gridLines: true,
              title: 'Valores',
            },
            legend: {
              show: true,
              position: 'bottom',
              orientation: 'horizontal',
            },
          },
        };
      },
    },

    // Padrão para mapa de calor (heatmap)
    {
      regex:
        /(?:crie|adicione|gere|insira)\s+(?:um)?\s+(?:gráfico\s+de\s+)?(?:mapa\s+de\s+calor|heatmap)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,
      handler: (matches: RegExpMatchArray): ExcelOperation | null => {
        const sourceRange = matches[1]?.trim();
        const title = matches[2] || matches[3] || matches[4];

        if (!sourceRange) {
          return null;
        }

        return {
          type: ExcelOperationType.ADVANCED_CHART,
          data: {
            type: 'heatmap',
            sourceRange,
            title: title || 'Mapa de Calor',
            legend: {
              show: true,
              position: 'right',
            },
            grid: {
              x: false,
              y: false,
            },
          },
        };
      },
    },

    // Padrão para gráfico de bolha 3D
    {
      regex:
        /(?:crie|adicione|gere|insira)\s+(?:um)?\s+gráfico\s+(?:de\s+)?(?:bolhas?[\s-]3[dD]|scatter[\s-]3[dD]|dispersão[\s-]3[dD])(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,
      handler: (matches: RegExpMatchArray): ExcelOperation | null => {
        const sourceRange = matches[1]?.trim();
        const title = matches[2] || matches[3] || matches[4];

        if (!sourceRange) {
          return null;
        }

        return {
          type: ExcelOperationType.ADVANCED_CHART,
          data: {
            type: 'scatter-3d',
            sourceRange,
            title: title || 'Gráfico de Dispersão 3D',
            animation: true,
            xAxis: {
              title: 'Eixo X',
            },
            yAxis: {
              title: 'Eixo Y',
            },
          },
        };
      },
    },

    // Padrão para gráfico de rosca (donut)
    {
      regex:
        /(?:crie|adicione|gere|insira)\s+(?:um)?\s+gráfico\s+(?:de\s+)?(?:rosca|donut|doughnut)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,
      handler: (matches: RegExpMatchArray): ExcelOperation | null => {
        const sourceRange = matches[1]?.trim();
        const title = matches[2] || matches[3] || matches[4];

        if (!sourceRange) {
          return null;
        }

        return {
          type: ExcelOperationType.ADVANCED_CHART,
          data: {
            type: 'donut',
            sourceRange,
            title: title || 'Gráfico de Rosca',
            legend: {
              show: true,
              position: 'right',
            },
            annotations: [
              {
                type: 'text',
                text: '%',
              },
            ],
          },
        };
      },
    },

    // Padrão para gráfico de sankey
    {
      regex:
        /(?:crie|adicione|gere|insira)\s+(?:um)?\s+gráfico\s+(?:de\s+)?(?:sankey|fluxo)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,
      handler: (matches: RegExpMatchArray): ExcelOperation | null => {
        const sourceRange = matches[1]?.trim();
        const title = matches[2] || matches[3] || matches[4];

        if (!sourceRange) {
          return null;
        }

        return {
          type: ExcelOperationType.ADVANCED_CHART,
          data: {
            type: 'sankey',
            sourceRange,
            title: title || 'Diagrama de Sankey',
            animation: true,
          },
        };
      },
    },

    // Padrão para gráfico de treemap
    {
      regex:
        /(?:crie|adicione|gere|insira)\s+(?:um)?\s+gráfico\s+(?:de\s+)?(?:treemap|mapa\s+de\s+árvore)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,
      handler: (matches: RegExpMatchArray): ExcelOperation | null => {
        const sourceRange = matches[1]?.trim();
        const title = matches[2] || matches[3] || matches[4];

        if (!sourceRange) {
          return null;
        }

        return {
          type: ExcelOperationType.ADVANCED_CHART,
          data: {
            type: 'treemap',
            sourceRange,
            title: title || 'Gráfico Treemap',
            legend: {
              show: false,
            },
          },
        };
      },
    },

    // Padrão para gráfico de nuvem de palavras
    {
      regex:
        /(?:crie|adicione|gere|insira)\s+(?:um[a])?\s+(?:gráfico\s+de\s+)?(?:nuvem\s+de\s+palavras|wordcloud|tag\s+cloud)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,
      handler: (matches: RegExpMatchArray): ExcelOperation | null => {
        const sourceRange = matches[1]?.trim();
        const title = matches[2] || matches[3] || matches[4];

        if (!sourceRange) {
          return null;
        }

        return {
          type: ExcelOperationType.ADVANCED_CHART,
          data: {
            type: 'wordcloud',
            sourceRange,
            title: title || 'Nuvem de Palavras',
            animation: true,
            colors: [
              '#1f77b4',
              '#ff7f0e',
              '#2ca02c',
              '#d62728',
              '#9467bd',
              '#8c564b',
              '#e377c2',
              '#7f7f7f',
              '#bcbd22',
              '#17becf',
            ],
          },
        };
      },
    },
  ];

  // Testar cada padrão
  for (const { regex, handler } of patterns) {
    const matches = text.match(regex);
    if (matches) {
      const operation = handler(matches);
      if (operation) {
        operations.push(operation);
      }
    }
  }

  return operations;
}

/**
 * Executa operação de gráfico avançado
 */
export async function executeAdvancedChartOperation(
  sheetData: any,
  operation: ExcelOperation
): Promise<{ updatedData: any; resultSummary: string }> {
  try {
    const { type, sourceRange, title } = operation.data;

    // Validar dados de entrada
    if (!sourceRange) {
      return {
        updatedData: sheetData,
        resultSummary: 'Erro: Intervalo de origem não especificado para o gráfico.',
      };
    }

    // Extrair dados da origem
    let sourceData: any[] = [];
    try {
      sourceData = await extractDataFromSource(sheetData, sourceRange);
    } catch (error) {
      return {
        updatedData: sheetData,
        resultSummary: `Erro ao extrair dados de origem: ${error instanceof Error ? error.message : String(error)}`,
      };
    }

    if (sourceData.length === 0) {
      return {
        updatedData: sheetData,
        resultSummary: 'Erro: Não foram encontrados dados no intervalo especificado.',
      };
    }

    // Encontrar um destino adequado para o gráfico
    const destinationRange =
      operation.data.destinationRange || findSuitableChartDestination(sheetData);

    // Criar configuração do gráfico
    const chartConfig = {
      id: generateChartId(),
      type,
      sourceRange,
      destinationRange,
      title: title || `Gráfico ${type.charAt(0).toUpperCase() + type.slice(1)}`,
      ...operation.data,
      data: sourceData,
    };

    // Atualizar dados da planilha
    const updatedData = {
      ...sheetData,
      charts: [...(sheetData.charts || []), chartConfig],
    };

    // Traduzir nomes de tipos de gráficos para português para a mensagem
    const chartTypeMap: Record<string, string> = {
      'area-spline': 'área spline',
      'area-step': 'área com degraus',
      'bar-grouped': 'barras agrupadas',
      'bar-stacked': 'barras empilhadas',
      'bar-stacked100': 'barras empilhadas 100%',
      bubble: 'bolhas',
      bullet: 'bullet',
      candlestick: 'candlestick',
      chord: 'diagrama de acordes',
      combination: 'combinado',
      donut: 'rosca',
      funnel: 'funil',
      gantt: 'gantt',
      gauge: 'medidor',
      heatmap: 'mapa de calor',
      'line-step': 'linha com degraus',
      lollipop: 'pirulito',
      marimekko: 'marimekko',
      nightingale: 'rosa de Nightingale',
      polar: 'polar',
      pyramid: 'pirâmide',
      radar: 'radar',
      sankey: 'sankey',
      'scatter-3d': 'dispersão 3D',
      stream: 'stream',
      sunburst: 'sunburst',
      treemap: 'mapa de árvore',
      violin: 'violino',
      wordcloud: 'nuvem de palavras',
    };

    const chartTypeName = chartTypeMap[type as string] || type;

    return {
      updatedData,
      resultSummary: `Gráfico de ${chartTypeName} criado com sucesso usando os dados do intervalo ${sourceRange}.`,
    };
  } catch (error) {
    return {
      updatedData: sheetData,
      resultSummary: `Erro ao criar gráfico: ${error instanceof Error ? error.message : String(error)}`,
    };
  }
}

/**
 * Extrai dados da fonte especificada
 */
async function extractDataFromSource(sheetData: any, sourceRange: string): Promise<any[]> {
  // Se for uma tabela com nome
  if (sourceRange.startsWith('[') && sourceRange.endsWith(']')) {
    const tableName = sourceRange.slice(1, -1);
    if (sheetData.tables && sheetData.tables[tableName]) {
      return sheetData.tables[tableName];
    }
    throw new Error(`Tabela '${tableName}' não encontrada`);
  }

  // Se for um intervalo de células (ex: A1:D10)
  const rangeMatch = sourceRange.match(/([A-Z]+)(\d+):([A-Z]+)(\d+)/);
  if (rangeMatch) {
    // Extrair e verificar valores com fallbacks seguros para evitar undefined
    const startColStr = extractGroup(rangeMatch, 1, 'A');
    const startRowStr = extractGroup(rangeMatch, 2, '1');
    const endColStr = extractGroup(rangeMatch, 3, 'A');
    const endRowStr = extractGroup(rangeMatch, 4, '1');

    // Converter letras de coluna para índices garantindo valores válidos
    const startColIndex = columnLetterToIndex(startColStr);
    const endColIndex = columnLetterToIndex(endColStr);

    // Converter linhas para índices com verificação de números válidos
    const startRowIndex = Math.max(0, parseInt(startRowStr, 10) - 1);
    const endRowIndex = Math.max(0, parseInt(endRowStr, 10) - 1);

    // Extrair cabeçalhos e dados
    const headers = [];
    for (let col = startColIndex; col <= endColIndex; col++) {
      const header = sheetData.rows?.[startRowIndex]?.cells?.[col]?.value || `Coluna${col + 1}`;
      headers.push(header);
    }

    // Extrair dados em formato de array de objetos
    const data = [];
    for (let row = startRowIndex + 1; row <= endRowIndex; row++) {
      const rowData: Record<string, any> = {};
      for (let col = startColIndex; col <= endColIndex; col++) {
        const headerIndex = col - startColIndex;
        if (headerIndex >= 0 && headerIndex < headers.length) {
          rowData[headers[headerIndex]] = sheetData.rows?.[row]?.cells?.[col]?.value;
        }
      }
      data.push(rowData);
    }

    return data;
  }

  throw new Error(`Formato de intervalo '${sourceRange}' não reconhecido`);
}

/**
 * Encontra um destino adequado para o gráfico
 */
function findSuitableChartDestination(sheetData: any): string {
  // Por padrão, colocar gráficos abaixo dos dados existentes
  let maxRowIndex = 0;

  if (sheetData.rows) {
    maxRowIndex = Object.keys(sheetData.rows).reduce((max, current) => {
      const index = parseInt(current, 10);
      return isNaN(index) ? max : Math.max(max, index);
    }, 0);
  }

  // Deixar algumas linhas de espaço após os dados
  const startRow = maxRowIndex + 3;

  // Definir uma área adequada para o gráfico (10 linhas x 8 colunas)
  return `A${startRow}:H${startRow + 15}`;
}

/**
 * Gera um ID único para o gráfico
 */
function generateChartId(): string {
  return 'chart_' + Math.random().toString(36).substring(2, 15);
}
