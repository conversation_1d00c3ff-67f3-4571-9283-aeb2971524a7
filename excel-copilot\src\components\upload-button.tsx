'use client';

import { Upload } from 'lucide-react';
import { useSession } from 'next-auth/react';
import { ChangeEvent, useRef } from 'react';
import { toast } from 'sonner';

import { Button } from '@/components/ui/button';
import { useExcelFile } from '@/hooks/useExcelFile';
import { storageService } from '@/lib/supabase/storage';

interface UploadButtonProps {
  onUpload: (data: any) => void;
  workbookId?: string; // Para salvar no Supabase Storage
  variant?: 'default' | 'outline' | 'ghost';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  maxSize?: number; // em bytes, padrão 10MB
  saveToSupabase?: boolean; // Se deve salvar no Supabase Storage
}

/**
 * Botão para carregar arquivo Excel
 */
export function UploadButton({
  onUpload,
  workbookId,
  variant = 'default',
  size = 'sm',
  maxSize,
  saveToSupabase = false,
}: UploadButtonProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { importExcel, isLoading } = useExcelFile();
  const { data: session } = useSession();

  const handleClick = () => {
    // Aciona o seletor de arquivos nativo
    fileInputRef.current?.click();
  };

  const handleFileChange = async (e: ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    try {
      // Salvar no Supabase Storage se solicitado
      if (saveToSupabase && workbookId && session?.user) {
        toast.loading('Salvando arquivo no Supabase...', { id: 'supabase-upload' });

        const uploadResult = await storageService.uploadExcelFile(
          file,
          (session.user as any).id || session.user.email || 'unknown',
          workbookId,
          {
            fileName: file.name,
            upsert: true,
          }
        );

        toast.success('Arquivo salvo no Supabase!', {
          id: 'supabase-upload',
          description: `Tamanho: ${Math.round(uploadResult.size / 1024)}KB`,
        });
      }

      // Usar o hook para importar o arquivo
      await importExcel(file, {
        onSuccess: onUpload,
        ...(maxSize ? { maxSize } : {}),
        trackAnalytics: true,
      });
    } catch (error) {
      console.error('Erro no upload:', error);
      toast.error('Erro ao processar arquivo', {
        description: error instanceof Error ? error.message : 'Erro desconhecido',
      });
    }

    // Resetar o input para permitir recarregar o mesmo arquivo
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <>
      <input
        type="file"
        ref={fileInputRef}
        onChange={handleFileChange}
        accept=".xlsx,.xls"
        style={{ display: 'none' }}
      />

      <Button
        variant={variant}
        size={size}
        onClick={() => handleClick()}
        disabled={isLoading}
        className="bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-2"
      >
        {isLoading ? (
          <>
            <div className="h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
            Carregando...
          </>
        ) : (
          <>
            <Upload className="h-4 w-4 mr-2" />
            Importar Excel
          </>
        )}
      </Button>
    </>
  );
}
