/**
 * Teste manual do fluxo OAuth
 * Execute este arquivo para verificar se o OAuth está funcionando
 */

const https = require('https');
const http = require('http');

// Configurações
const BASE_URL = 'http://localhost:3000';
const TIMEOUT = 10000;

/**
 * Faz uma requisição HTTP simples
 */
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const protocol = url.startsWith('https') ? https : http;
    const timeout = setTimeout(() => {
      reject(new Error('Timeout'));
    }, TIMEOUT);

    const req = protocol.get(url, options, res => {
      clearTimeout(timeout);
      let data = '';

      res.on('data', chunk => {
        data += chunk;
      });

      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          body: data,
        });
      });
    });

    req.on('error', err => {
      clearTimeout(timeout);
      reject(err);
    });
  });
}

/**
 * Testa o endpoint de health check da autenticação
 */
async function testAuthHealth() {
  console.log('🔍 Testando health check da autenticação...');

  try {
    const response = await makeRequest(`${BASE_URL}/api/auth/health`);

    if (response.statusCode === 200) {
      console.log('✅ Health check OK');

      try {
        const data = JSON.parse(response.body);
        console.log('📊 Status da autenticação:');
        console.log(`   - Status: ${data.status}`);
        console.log(`   - Providers: ${data.providers?.length || 0}`);
        console.log(`   - Database: ${data.database ? '✅' : '❌'}`);
        console.log(`   - Environment: ${data.environment}`);

        if (data.providers) {
          console.log('🔑 Providers configurados:');
          data.providers.forEach(provider => {
            console.log(`   - ${provider.name}: ${provider.configured ? '✅' : '❌'}`);
          });
        }

        return data;
      } catch (parseError) {
        console.log('⚠️ Resposta não é JSON válido');
        console.log('📄 Resposta raw:', response.body.substring(0, 200));
      }
    } else {
      console.log(`❌ Health check falhou: ${response.statusCode}`);
      console.log('📄 Resposta:', response.body.substring(0, 200));
    }
  } catch (error) {
    console.log(`❌ Erro ao testar health check: ${error.message}`);
  }
}

/**
 * Testa se os endpoints OAuth estão acessíveis
 */
async function testOAuthEndpoints() {
  console.log('\n🔍 Testando endpoints OAuth...');

  const endpoints = ['/api/auth/signin', '/api/auth/providers', '/api/auth/csrf'];

  for (const endpoint of endpoints) {
    try {
      const response = await makeRequest(`${BASE_URL}${endpoint}`);
      console.log(
        `   ${endpoint}: ${response.statusCode === 200 ? '✅' : '❌'} (${response.statusCode})`
      );
    } catch (error) {
      console.log(`   ${endpoint}: ❌ (${error.message})`);
    }
  }
}

/**
 * Testa se as variáveis de ambiente estão configuradas
 */
function testEnvironmentVariables() {
  console.log('\n🔍 Verificando variáveis de ambiente...');

  const requiredVars = [
    'AUTH_NEXTAUTH_SECRET',
    'AUTH_NEXTAUTH_URL',
    'AUTH_GOOGLE_CLIENT_ID',
    'AUTH_GOOGLE_CLIENT_SECRET',
    'AUTH_GITHUB_CLIENT_ID',
    'AUTH_GITHUB_CLIENT_SECRET',
  ];

  const missingVars = [];

  requiredVars.forEach(varName => {
    if (process.env[varName]) {
      console.log(`   ${varName}: ✅`);
    } else {
      console.log(`   ${varName}: ❌ (não definida)`);
      missingVars.push(varName);
    }
  });

  if (missingVars.length > 0) {
    console.log(`\n⚠️ Variáveis ausentes: ${missingVars.join(', ')}`);
    return false;
  }

  console.log('\n✅ Todas as variáveis OAuth estão configuradas!');
  return true;
}

/**
 * Função principal
 */
async function main() {
  console.log('🚀 Teste do Fluxo OAuth - Excel Copilot\n');

  // Carregar variáveis de ambiente do .env.local
  try {
    require('dotenv').config({ path: '.env.local' });
  } catch (error) {
    console.log('⚠️ Não foi possível carregar .env.local');
  }

  // Teste 1: Variáveis de ambiente
  const envOk = testEnvironmentVariables();

  // Teste 2: Health check
  await testAuthHealth();

  // Teste 3: Endpoints OAuth
  await testOAuthEndpoints();

  console.log('\n📋 Resumo:');
  console.log(`   - Variáveis de ambiente: ${envOk ? '✅' : '❌'}`);
  console.log('   - Para testar login completo: acesse http://localhost:3000/auth/signin');
  console.log('   - Para debug detalhado: verifique os logs do npm run dev');

  console.log('\n🔗 URLs de teste:');
  console.log('   - Login: http://localhost:3000/auth/signin');
  console.log('   - Health: http://localhost:3000/api/auth/health');
  console.log('   - Providers: http://localhost:3000/api/auth/providers');
}

// Executar se chamado diretamente
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { testAuthHealth, testOAuthEndpoints, testEnvironmentVariables };
