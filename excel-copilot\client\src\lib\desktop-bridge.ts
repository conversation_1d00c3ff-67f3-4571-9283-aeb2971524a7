import { useState, useEffect, useCallback } from 'react';

import { WebSocketStatus, WebSocketMessage, OperationResult, ExcelOperation } from '../types/index';

/**
 * Desktop Bridge Client - Handles communication with Excel Copilot Desktop
 */
class DesktopBridgeClient {
  private ws: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectTimeout: NodeJS.Timeout | null = null;
  private baseReconnectDelay = 1000; // 1 second
  private messageHandlers = new Map<string, (data: WebSocketMessage) => void>();
  private statusListeners: ((status: WebSocketStatus) => void)[] = [];
  private currentStatus: WebSocketStatus = WebSocketStatus.Disconnected;

  constructor(private serverUrl: string = 'ws://localhost:8080') {}

  /**
   * Add a status change listener
   */
  public addStatusListener(listener: (status: WebSocketStatus) => void): () => void {
    this.statusListeners.push(listener);
    listener(this.currentStatus); // Call immediately with current status

    return () => {
      this.statusListeners = this.statusListeners.filter(l => l !== listener);
    };
  }

  /**
   * Update the connection status
   */
  private setStatus(status: WebSocketStatus): void {
    if (this.currentStatus !== status) {
      this.currentStatus = status;
      this.statusListeners.forEach(listener => listener(status));
    }
  }

  /**
   * Get the current connection status
   */
  public getStatus(): WebSocketStatus {
    return this.currentStatus;
  }

  /**
   * Connect to the desktop bridge
   */
  public async connect(): Promise<void> {
    if (this.ws?.readyState === WebSocket.OPEN) {
      return;
    }

    if (this.ws?.readyState === WebSocket.CONNECTING) {
      return;
    }

    // Clean up existing connection if any
    if (this.ws) {
      this.ws.onopen = null;
      this.ws.onclose = null;
      this.ws.onerror = null;
      this.ws.onmessage = null;
      this.ws.close();
      this.ws = null;
    }

    this.setStatus(WebSocketStatus.Connecting);

    return new Promise<void>((resolve, reject) => {
      try {
        this.ws = new WebSocket(this.serverUrl);

        this.ws.onopen = () => {
          this.setStatus(WebSocketStatus.Connected);
          this.reconnectAttempts = 0;
          resolve();
        };

        this.ws.onclose = event => {
          this.setStatus(WebSocketStatus.Disconnected);
          this.ws = null;
          this.scheduleReconnect();
          reject(new Error(`Connection closed: ${event.code} ${event.reason}`));
        };

        this.ws.onerror = _event => {
          this.setStatus(WebSocketStatus.Error);
          reject(new Error('WebSocket connection error'));
        };

        this.ws.onmessage = event => {
          try {
            const message = JSON.parse(event.data) as WebSocketMessage;

            // Handle response based on message id
            if (message.id && this.messageHandlers.has(message.id)) {
              const handler = this.messageHandlers.get(message.id);
              if (handler) {
                handler(message);
                this.messageHandlers.delete(message.id);
              }
            }
          } catch (error) {
            console.error(
              'Error parsing message:',
              error instanceof Error ? error.message : String(error)
            );
          }
        };
      } catch (error) {
        this.setStatus(WebSocketStatus.Error);
        reject(error instanceof Error ? error : new Error(String(error)));
      }
    });
  }

  /**
   * Disconnect from the desktop bridge
   */
  public disconnect(): void {
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
      this.reconnectTimeout = null;
    }

    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }

    this.setStatus(WebSocketStatus.Disconnected);
  }

  /**
   * Schedule a reconnection attempt with exponential backoff
   */
  private scheduleReconnect(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.warn('Maximum reconnection attempts reached');
      this.setStatus(WebSocketStatus.Failed);
      return;
    }

    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
    }

    const delay = this.baseReconnectDelay * Math.pow(2, this.reconnectAttempts);
    this.reconnectAttempts++;

    this.reconnectTimeout = setTimeout(() => {
      this.connect().catch(() => {
        // Silent catch - we'll retry again if needed
      });
    }, delay);
  }

  /**
   * Execute an operation on the Excel file
   */
  public async executeOperation(operation: ExcelOperation): Promise<OperationResult> {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      throw new Error('Not connected to desktop bridge');
    }

    return new Promise<OperationResult>((resolve, reject) => {
      const id = `op-${Date.now()}`;

      // Set up handler for the response
      this.messageHandlers.set(id, (response: WebSocketMessage) => {
        if (response.error) {
          reject(new Error(response.error));
        } else {
          // Ensure we have a valid OperationResult
          if (response.data && typeof response.data === 'object' && 'success' in response.data) {
            // Safe to cast since we verified it has the required property
            resolve({
              success: Boolean(response.data.success),
              data: response.data.data,
              error: response.data.error,
            } as OperationResult);
          } else {
            // Return default success result
            resolve({ success: true });
          }
        }
      });

      // Send the operation request
      try {
        const ws = this.ws;
        if (ws) {
          ws.send(
            JSON.stringify({
              id,
              type: 'operation',
              operation: operation.type,
              data: operation.data,
            })
          );
        }

        // Set a timeout to avoid hanging promises
        setTimeout(() => {
          if (this.messageHandlers.has(id)) {
            this.messageHandlers.delete(id);
            reject(new Error('Operation timed out'));
          }
        }, 30000); // 30 second timeout
      } catch (error) {
        this.messageHandlers.delete(id);
        reject(error instanceof Error ? error : new Error(String(error)));
      }
    });
  }

  /**
   * Open an Excel file
   */
  public async openFile(filePath: string): Promise<OperationResult> {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      throw new Error('Not connected to desktop bridge');
    }

    return new Promise<OperationResult>((resolve, reject) => {
      const id = `open-${Date.now()}`;

      // Set up handler for the response
      this.messageHandlers.set(id, (response: WebSocketMessage) => {
        if (response.error) {
          reject(new Error(response.error));
        } else {
          // Ensure we have a valid OperationResult
          if (response.data && typeof response.data === 'object' && 'success' in response.data) {
            // Safe to cast since we verified it has the required property
            resolve({
              success: Boolean(response.data.success),
              data: response.data.data,
              error: response.data.error,
            } as OperationResult);
          } else {
            // Return default success result
            resolve({ success: true });
          }
        }
      });

      // Send the open file request
      try {
        const ws = this.ws;
        if (ws) {
          ws.send(
            JSON.stringify({
              id,
              type: 'open_file',
              filePath,
            })
          );
        }

        // Set a timeout to avoid hanging promises
        setTimeout(() => {
          if (this.messageHandlers.has(id)) {
            this.messageHandlers.delete(id);
            reject(new Error('Operation timed out'));
          }
        }, 30000); // 30 second timeout
      } catch (error) {
        this.messageHandlers.delete(id);
        reject(error instanceof Error ? error : new Error(String(error)));
      }
    });
  }
}

// Create a singleton instance
const bridgeClient = new DesktopBridgeClient();

/**
 * Hook for using the desktop bridge in React components
 */
export function useDesktopBridge() {
  const [status, setStatus] = useState<WebSocketStatus>(bridgeClient.getStatus());
  const [isReady, setIsReady] = useState<boolean>(status === WebSocketStatus.Connected);

  useEffect(() => {
    // Update isReady when status changes
    setIsReady(status === WebSocketStatus.Connected);
  }, [status]);

  useEffect(() => {
    // Register status listener
    const unsubscribe = bridgeClient.addStatusListener(setStatus);

    // Try to connect automatically
    bridgeClient.connect().catch((_error: unknown) => {
      // Silent catch - we'll retry later
      // Auto-connect failed - using logger would be better
    });

    // Clean up on unmount
    return () => {
      unsubscribe();
    };
  }, []);

  const connect = useCallback(async (): Promise<boolean> => {
    try {
      await bridgeClient.connect();
      return true;
    } catch (error) {
      console.error(
        'Failed to connect to desktop bridge:',
        error instanceof Error ? error.message : String(error)
      );
      return false;
    }
  }, []);

  const disconnect = useCallback((): void => {
    bridgeClient.disconnect();
  }, []);

  const executeOperation = useCallback(
    async (operation: ExcelOperation): Promise<OperationResult> => {
      return bridgeClient.executeOperation(operation);
    },
    []
  );

  const openFile = useCallback(async (filePath: string): Promise<OperationResult> => {
    return bridgeClient.openFile(filePath);
  }, []);

  return {
    status,
    isReady,
    connect,
    disconnect,
    executeOperation,
    openFile,
  };
}

export default useDesktopBridge;
