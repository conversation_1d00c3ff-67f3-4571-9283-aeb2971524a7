/**
 * Sistema de rastreamento de uso de APIs legadas
 * Centraliza logs e métricas sobre uso de rotas depreciadas
 */

// Contador de uso para rastrear chamadas de API legadas
const usageCount: Record<string, number> = {};

/**
 * Registra o uso de uma API legada
 * @param path Caminho da API acessada
 * @param userAgent User-agent do cliente
 * @param referer Referer da requisição
 */
export function trackLegacyApiUsage(path: string, userAgent?: string, referer?: string): void {
  // Incrementar contador
  if (!usageCount[path]) {
    usageCount[path] = 0;
  }
  usageCount[path]++;

  // Log com informações do solicitante
  console.warn(
    `[DEPRECATED API] Rota legada acessada: ${path}. ` +
      `Contador: ${usageCount[path]}. ` +
      `User-Agent: ${userAgent || 'Não informado'}. ` +
      `Referer: ${referer || 'Não informado'}`
  );
}

/**
 * Retorna estatísticas de uso de APIs legadas
 * @returns Mapa de caminhos e contagem de uso
 */
export function getLegacyApiUsageStats(): Record<string, number> {
  return { ...usageCount };
}

/**
 * Limpa as estatísticas de uso (útil para testes)
 */
export function resetLegacyApiUsageStats(): void {
  Object.keys(usageCount).forEach(key => {
    delete usageCount[key];
  });
}
