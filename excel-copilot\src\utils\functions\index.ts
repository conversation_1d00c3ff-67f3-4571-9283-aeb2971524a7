/**
 * Funções puras utilitárias
 *
 * Este módulo contém funções utilitárias puras que não dependem de estado
 * ou efeitos colaterais. São funções que transformam entradas em saídas
 * de forma determinística e podem ser reusadas em qualquer contexto.
 */

/**
 * Combina classes CSS com suporte a condicionais
 * @param inputs Classes CSS a serem combinadas
 * @returns String de classes CSS combinadas
 */
export function cn(...inputs: (string | undefined | null | false)[]): string {
  return inputs.filter(Boolean).join(' ');
}

/**
 * Formata um número como moeda brasileira
 * @param value Valor numérico a ser formatado
 * @returns String formatada como moeda (R$ 1.234,56)
 */
export function formatCurrency(value: number): string {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL',
  }).format(value);
}

/**
 * Formata uma data para exibição no formato brasileiro
 * @param date Data a ser formatada
 * @returns String no formato DD/MM/YYYY HH:MM
 */
export function formatDate(date: Date | string): string {
  if (!date) return '';

  const d = typeof date === 'string' ? new Date(date) : date;

  return new Intl.DateTimeFormat('pt-BR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(d);
}

/**
 * Converte uma string para formato de título (primeira letra de cada palavra maiúscula)
 * @param str String a ser convertida
 * @returns String formatada como título
 */
export function toTitleCase(str: string): string {
  return str
    .toLowerCase()
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

/**
 * Verifica se um objeto está vazio
 * @param obj Objeto a ser verificado
 * @returns true se o objeto estiver vazio
 */
export function isObjectEmpty(obj: Record<string, any>): boolean {
  return Object.keys(obj).length === 0;
}

/**
 * Gera um ID único aleatório (semelhante ao nanoid)
 * @param length Comprimento do ID (padrão: 10)
 * @returns String aleatória única
 */
export function generateId(length = 10): string {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';

  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * characters.length));
  }

  return result;
}

/**
 * Atrasa a execução por um tempo determinado
 * @param ms Tempo em milissegundos
 * @returns Promise que resolve após o tempo especificado
 */
export function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Trunca uma string para um comprimento máximo com elipses no final
 * @param str String a ser truncada
 * @param maxLength Comprimento máximo
 * @returns String truncada
 */
export function truncateString(str: string, maxLength: number): string {
  if (!str) return '';
  if (str.length <= maxLength) return str;
  return `${str.substring(0, maxLength)}...`;
}

/**
 * Remove acentos e caracteres especiais de uma string
 * @param text Texto a ser normalizado
 * @returns Texto sem acentos e caracteres especiais
 */
export function normalizeText(text: string): string {
  if (!text) return '';
  return text
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '')
    .toLowerCase();
}
