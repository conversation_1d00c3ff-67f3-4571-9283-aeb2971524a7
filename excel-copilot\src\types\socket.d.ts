// Socket.io personalizado
declare module 'socket.io' {
  interface Socket {
    data: {
      userId: string;
      userName: string;
      activity?: string;
      position?: {
        sheetId: string;
        row?: number;
        col?: number;
        cell?: string;
        range?: string;
      };
    };
  }
}

// Tipos para eventos do servidor
export interface ServerToClientEvents {
  cell_changed: (data: CellUpdateMessage) => void;
  user_activity: (data: UserActivityMessage) => void;
  collaborator_joined: (userId: string) => void;
  collaborator_left: (userId: string) => void;
  error: (data: { type: string; message: string }) => void;
  pong: (data: { timestamp: number }) => void;
}

// Tipos para eventos do cliente
export interface ClientToServerEvents {
  join_room: (data: JoinRoomMessage) => void;
  leave_room: (data: LeaveRoomMessage) => void;
  cell_update: (data: CellUpdateMessage) => void;
  user_activity: (data: UserActivityMessage) => void;
  heartbeat: () => void;
}

// Tipos para dados inter-servidor
export interface InterServerEvents {
  ping: () => void;
}

// Dados do socket
export interface SocketData {
  userId: string;
  userName: string;
  activity?: string;
  position?: {
    sheetId: string;
    row?: number;
    col?: number;
    cell?: string;
    range?: string;
  };
}

// Tipos para mensagens
export interface CellUpdateMessage {
  workbookId: string;
  sheetId: string;
  cell: {
    row: number;
    col: number;
    value: unknown;
    formula?: string;
    style?: Record<string, unknown>;
  };
  userId?: string;
  userName?: string;
  timestamp?: number;
}

export interface UserActivityMessage {
  workbookId: string;
  sheetId: string;
  activity: string;
  position?: {
    row?: number;
    col?: number;
    cell?: string;
    range?: string;
  };
  userId?: string;
  userName?: string;
  timestamp?: number;
}

export interface JoinRoomMessage {
  workbookId: string;
}

export interface LeaveRoomMessage {
  workbookId: string;
}

// Tipo unificado para todas as mensagens
export type SocketMessage =
  | ({ type: 'join_room' } & JoinRoomMessage)
  | ({ type: 'leave_room' } & LeaveRoomMessage)
  | ({ type: 'cell_update' } & CellUpdateMessage)
  | ({ type: 'user_activity' } & UserActivityMessage)
  | { type: 'heartbeat' };

// Tipo para respostas de erro
export interface SocketErrorResponse {
  type: 'error';
  message: string;
  code?: string;
  details?: unknown;
}

export type WebSocketMessage =
  | CellUpdateMessage
  | UserActivityMessage
  | JoinRoomMessage
  | LeaveRoomMessage;
