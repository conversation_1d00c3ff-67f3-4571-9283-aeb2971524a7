/**
 * @jest-environment node
 */

// Testes unitários para o mock server
import { setupServer, MockServerInstance } from '@/server/test-utils/mock-server';

describe('Mock Server', () => {
  let server: MockServerInstance;

  beforeEach(() => {
    // Cria uma nova instância do server para cada teste
    server = setupServer();
  });

  test('deve registrar e executar handlers corretamente', () => {
    // Adicionar um handler de teste
    server.addHandler({
      path: '/api/test',
      method: 'GET',
      handler: () => {
        return {
          status: 200,
          body: {
            message: 'Success',
            data: { id: 1, name: 'Test' },
          },
        };
      },
    });

    // Executar a requisição
    const response = server.handleRequest('/api/test', 'GET');

    // Verificar se a resposta contém os dados esperados
    expect(response.status).toBe(200);
    expect(response.body).toHaveProperty('message', 'Success');
    expect((response.body as any).data).toHaveProperty('id', 1);
    expect((response.body as any).data).toHaveProperty('name', 'Test');
  });

  test('deve tratar parâmetros de rota corretamente', () => {
    // Adicionar um handler com parâmetros
    server.addHandler({
      path: '/api/users/:id',
      method: 'GET',
      handler: req => {
        return {
          status: 200,
          body: {
            userId: req.params.id,
          },
        };
      },
    });

    // Executar a requisição com um ID específico
    const response = server.handleRequest('/api/users/123', 'GET');

    // Verificar se o parâmetro foi capturado corretamente
    expect(response.status).toBe(200);
    expect(response.body).toHaveProperty('userId', '123');
  });

  test('deve processar body de requisição corretamente', () => {
    // Adicionar um handler que utiliza dados do body
    server.addHandler({
      path: '/api/data',
      method: 'POST',
      handler: req => {
        return {
          status: 201,
          body: {
            received: req.body,
          },
        };
      },
    });

    // Executar a requisição com body
    const response = server.handleRequest('/api/data', 'POST', { name: 'Test Name' });

    // Verificar se o body foi processado corretamente
    expect(response.status).toBe(201);
    expect((response.body as any).received).toHaveProperty('name', 'Test Name');
  });

  test('deve retornar 404 para rotas não encontradas', () => {
    // Executar requisição para rota inexistente
    const response = server.handleRequest('/api/nonexistent', 'GET');

    // Verificar resposta de erro
    expect(response.status).toBe(404);
    expect(response.body).toHaveProperty('error');
  });

  test('deve limpar handlers após resetHandlers()', () => {
    // Adicionar um handler de teste
    server.addHandler({
      path: '/api/test-reset',
      method: 'GET',
      handler: () => {
        return {
          status: 200,
          body: { message: 'Success' },
        };
      },
    });

    // Verificar que o handler funciona
    const response1 = server.handleRequest('/api/test-reset', 'GET');
    expect(response1.status).toBe(200);

    // Resetar handlers
    server.resetHandlers();

    // Verificar que o handler não existe mais
    const response2 = server.handleRequest('/api/test-reset', 'GET');
    expect(response2.status).toBe(404);
  });

  test('deve sobrescrever handlers existentes', () => {
    // Adicionar um handler inicial
    server.addHandler({
      path: '/api/test-override',
      method: 'GET',
      handler: () => {
        return {
          status: 200,
          body: { version: 1 },
        };
      },
    });

    // Verificar resposta inicial
    const response1 = server.handleRequest('/api/test-override', 'GET');
    expect((response1.body as any).version).toBe(1);

    // Sobrescrever o handler
    server.addHandler({
      path: '/api/test-override',
      method: 'GET',
      handler: () => {
        return {
          status: 200,
          body: { version: 2 },
        };
      },
    });

    // Verificar resposta com handler atualizado
    const response2 = server.handleRequest('/api/test-override', 'GET');
    expect((response2.body as any).version).toBe(2);
  });

  test('deve suportar diferentes métodos HTTP', () => {
    // Adicionar handlers para diferentes métodos
    server.addHandler({
      path: '/api/methods',
      method: 'GET',
      handler: () => {
        return {
          status: 200,
          body: { method: 'GET' },
        };
      },
    });

    server.addHandler({
      path: '/api/methods',
      method: 'POST',
      handler: () => {
        return {
          status: 201,
          body: { method: 'POST' },
        };
      },
    });

    // Testar cada método
    const getResponse = server.handleRequest('/api/methods', 'GET');
    expect((getResponse.body as any).method).toBe('GET');

    const postResponse = server.handleRequest('/api/methods', 'POST');
    expect((postResponse.body as any).method).toBe('POST');
  });

  test('deve lidar com erros nos handlers', () => {
    // Adicionar handler que lança um erro
    server.addHandler({
      path: '/api/error',
      method: 'GET',
      handler: () => {
        throw new Error('Erro de teste');
      },
    });

    // Verificar que o erro é propagado
    expect(() => {
      server.handleRequest('/api/error', 'GET');
    }).toThrow('Erro de teste');
  });
});
