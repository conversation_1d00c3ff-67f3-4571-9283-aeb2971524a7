/**
 * Classe base abstrata para serviços de IA
 * Centraliza lógica comum e define interface padrão
 */

import { logger } from '@/lib/logger';

// ===== INTERFACES E TIPOS =====

/**
 * Opções para envio de mensagem
 */
export interface MessageOptions {
  /** Contexto adicional para a mensagem */
  context?: string;
  /** Temperatura para controle de criatividade (0-1) */
  temperature?: number;
  /** Número máximo de tokens na resposta */
  maxTokens?: number;
  /** ID da sessão para continuidade */
  sessionId?: string;
  /** Metadados adicionais */
  metadata?: Record<string, unknown>;
}

/**
 * Resposta do serviço de IA
 */
export interface AIResponse {
  /** Conteúdo da resposta */
  content: string;
  /** Tokens utilizados na requisição */
  tokensUsed?: number;
  /** Tempo de resposta em ms */
  responseTime?: number;
  /** ID da resposta para rastreamento */
  responseId?: string;
  /** Metadados adicionais */
  metadata?: Record<string, unknown>;
}

/**
 * Estatísticas do serviço
 */
export interface ServiceStats {
  /** Total de mensagens processadas */
  totalMessages: number;
  /** Total de tokens utilizados */
  totalTokens: number;
  /** Tempo médio de resposta */
  averageResponseTime: number;
  /** Taxa de sucesso (0-1) */
  successRate: number;
  /** Última atualização das estatísticas */
  lastUpdated: Date;
  /** Estatísticas por período */
  periodStats?: {
    hourly: number;
    daily: number;
    monthly: number;
  };
}

/**
 * Configuração do serviço
 */
export interface ServiceConfig {
  /** Nome do serviço */
  name: string;
  /** Versão do serviço */
  version: string;
  /** Configurações específicas */
  settings: Record<string, unknown>;
  /** Se o cache está habilitado */
  cacheEnabled: boolean;
  /** TTL do cache em segundos */
  cacheTTL: number;
  /** Limite de rate limiting */
  rateLimit?: {
    requests: number;
    window: number; // em segundos
  };
}

// ===== CLASSE BASE ABSTRATA =====

/**
 * Classe base para todos os serviços de IA
 * Implementa funcionalidades comuns como cache, rate limiting, estatísticas
 */
export abstract class BaseAIService {
  protected isInitialized = false;
  protected config: ServiceConfig;
  protected stats: ServiceStats;
  protected cache = new Map<string, { data: AIResponse; timestamp: number }>();
  protected rateLimitMap = new Map<string, { count: number; resetTime: number }>();

  constructor(config: ServiceConfig) {
    this.config = config;
    this.stats = {
      totalMessages: 0,
      totalTokens: 0,
      averageResponseTime: 0,
      successRate: 1,
      lastUpdated: new Date(),
    };
  }

  // ===== MÉTODOS ABSTRATOS (DEVEM SER IMPLEMENTADOS) =====

  /**
   * Inicializa o serviço de IA
   * Deve ser implementado por cada serviço específico
   */
  protected abstract initialize(): Promise<boolean>;

  /**
   * Envia mensagem para o serviço de IA
   * Implementação específica de cada provedor
   */
  protected abstract sendMessageToProvider(
    message: string,
    options?: MessageOptions
  ): Promise<AIResponse>;

  /**
   * Valida se o serviço está configurado corretamente
   */
  protected abstract validateConfiguration(): boolean;

  // ===== MÉTODOS PÚBLICOS =====

  /**
   * Inicializa o serviço se ainda não foi inicializado
   */
  public async ensureInitialized(): Promise<boolean> {
    if (this.isInitialized) {
      return true;
    }

    try {
      logger.info(`Inicializando serviço de IA: ${this.config.name}`);

      if (!this.validateConfiguration()) {
        logger.error(`Configuração inválida para ${this.config.name}`);
        return false;
      }

      const success = await this.initialize();
      this.isInitialized = success;

      if (success) {
        logger.info(`Serviço ${this.config.name} inicializado com sucesso`);
      } else {
        logger.error(`Falha ao inicializar ${this.config.name}`);
      }

      return success;
    } catch (error) {
      logger.error(`Erro ao inicializar ${this.config.name}:`, error);
      return false;
    }
  }

  /**
   * Envia mensagem com todas as funcionalidades (cache, rate limiting, etc.)
   */
  public async sendMessage(message: string, options: MessageOptions = {}): Promise<AIResponse> {
    const startTime = Date.now();

    try {
      // Verificar se está inicializado
      if (!(await this.ensureInitialized())) {
        throw new Error(`Serviço ${this.config.name} não está inicializado`);
      }

      // Verificar rate limiting
      if (!this.checkRateLimit(options.sessionId || 'default')) {
        throw new Error('Rate limit excedido');
      }

      // Verificar cache
      const cacheKey = this.generateCacheKey(message, options);
      if (this.config.cacheEnabled) {
        const cached = this.getFromCache(cacheKey);
        if (cached) {
          logger.debug(`Cache hit para ${this.config.name}`);
          return cached;
        }
      }

      // Enviar mensagem para o provedor
      const response = await this.sendMessageToProvider(message, options);

      // Calcular tempo de resposta
      const responseTime = Date.now() - startTime;
      response.responseTime = responseTime;

      // Atualizar estatísticas
      this.updateStats(response, true);

      // Salvar no cache
      if (this.config.cacheEnabled) {
        this.saveToCache(cacheKey, response);
      }

      logger.debug(`Mensagem processada por ${this.config.name} em ${responseTime}ms`);
      return response;
    } catch (error) {
      const responseTime = Date.now() - startTime;
      logger.error(`Erro em ${this.config.name}:`, error);

      // Atualizar estatísticas de erro
      this.updateStats({ content: '', responseTime }, false);

      throw error;
    }
  }

  /**
   * Retorna estatísticas do serviço
   */
  public getStats(): ServiceStats {
    return { ...this.stats };
  }

  /**
   * Limpa o cache do serviço
   */
  public clearCache(): void {
    this.cache.clear();
    logger.info(`Cache limpo para ${this.config.name}`);
  }

  /**
   * Redefine estatísticas
   */
  public resetStats(): void {
    this.stats = {
      totalMessages: 0,
      totalTokens: 0,
      averageResponseTime: 0,
      successRate: 1,
      lastUpdated: new Date(),
    };
    logger.info(`Estatísticas redefinidas para ${this.config.name}`);
  }

  // ===== MÉTODOS PROTEGIDOS (UTILITÁRIOS) =====

  /**
   * Verifica rate limiting
   */
  protected checkRateLimit(sessionId: string): boolean {
    if (!this.config.rateLimit) {
      return true;
    }

    const now = Date.now();
    const limit = this.rateLimitMap.get(sessionId);

    if (!limit || now > limit.resetTime) {
      // Resetar contador
      this.rateLimitMap.set(sessionId, {
        count: 1,
        resetTime: now + this.config.rateLimit.window * 1000,
      });
      return true;
    }

    if (limit.count >= this.config.rateLimit.requests) {
      return false;
    }

    limit.count++;
    return true;
  }

  /**
   * Gera chave para cache
   */
  protected generateCacheKey(message: string, options: MessageOptions): string {
    const key = JSON.stringify({ message, options });
    return Buffer.from(key).toString('base64');
  }

  /**
   * Recupera item do cache
   */
  protected getFromCache(key: string): AIResponse | null {
    const cached = this.cache.get(key);
    if (!cached) {
      return null;
    }

    const now = Date.now();
    const isExpired = now - cached.timestamp > this.config.cacheTTL * 1000;

    if (isExpired) {
      this.cache.delete(key);
      return null;
    }

    return cached.data;
  }

  /**
   * Salva item no cache
   */
  protected saveToCache(key: string, response: AIResponse): void {
    this.cache.set(key, {
      data: response,
      timestamp: Date.now(),
    });

    // Limpar cache antigo se necessário
    if (this.cache.size > 1000) {
      this.cleanupCache();
    }
  }

  /**
   * Limpa itens expirados do cache
   */
  protected cleanupCache(): void {
    const now = Date.now();
    const ttlMs = this.config.cacheTTL * 1000;

    for (const [key, value] of this.cache.entries()) {
      if (now - value.timestamp > ttlMs) {
        this.cache.delete(key);
      }
    }
  }

  /**
   * Atualiza estatísticas do serviço
   */
  protected updateStats(response: AIResponse, success: boolean): void {
    this.stats.totalMessages++;

    if (response.tokensUsed) {
      this.stats.totalTokens += response.tokensUsed;
    }

    if (response.responseTime) {
      // Calcular média móvel do tempo de resposta
      const currentAvg = this.stats.averageResponseTime;
      const count = this.stats.totalMessages;
      this.stats.averageResponseTime = (currentAvg * (count - 1) + response.responseTime) / count;
    }

    // Calcular taxa de sucesso
    const successCount = Math.round(this.stats.successRate * (this.stats.totalMessages - 1));
    const newSuccessCount = success ? successCount + 1 : successCount;
    this.stats.successRate = newSuccessCount / this.stats.totalMessages;

    this.stats.lastUpdated = new Date();
  }
}
