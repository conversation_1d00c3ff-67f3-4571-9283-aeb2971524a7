{"root": true, "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": 2020, "sourceType": "module", "ecmaFeatures": {"jsx": true}}, "extends": ["next/core-web-vitals", "eslint:recommended", "plugin:@typescript-eslint/recommended", "plugin:import/errors", "plugin:import/warnings", "plugin:import/typescript", "prettier"], "plugins": ["@typescript-eslint", "import", "unused-imports", "prettier"], "rules": {"react/react-in-jsx-scope": "off", "react/prop-types": "off", "@typescript-eslint/explicit-module-boundary-types": "off", "@typescript-eslint/no-unused-vars": ["warn", {"argsIgnorePattern": "^_", "varsIgnorePattern": "^_", "ignoreRestSiblings": true}], "@typescript-eslint/no-explicit-any": "warn", "unused-imports/no-unused-imports": "warn", "import/order": ["warn", {"groups": ["builtin", "external", "internal", "parent", "sibling", "index"], "newlines-between": "always", "alphabetize": {"order": "asc", "caseInsensitive": true}}], "import/no-named-as-default": "off", "import/no-named-as-default-member": "off", "no-console": ["warn", {"allow": ["warn", "error", "info", "debug"]}], "@next/next/no-html-link-for-pages": "off", "prettier/prettier": ["warn", {"endOfLine": "auto"}], "no-restricted-imports": ["warn", {"patterns": [{"group": ["next/router"], "message": "Import de next/router é desencorajado. Use next/navigation para App Router. Consulte MIGRATION_STATUS.md para mais informações."}]}], "no-restricted-syntax": ["warn", {"selector": "ExportDefaultDeclaration > ArrowFunctionExpression[async=true]", "message": "Exports default de funções assíncronas são característicos do Pages Router. No App Router, use export const GET/POST/etc. Consulte MIGRATION_STATUS.md para mais informações."}]}, "settings": {"react": {"version": "detect"}, "import/resolver": {"typescript": {}}}, "overrides": [{"files": ["**/__tests__/**/*", "**/*.test.ts", "**/*.test.tsx", "test-*.js"], "rules": {"no-console": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-unused-vars": "off"}}, {"files": ["src/types/**/*.ts", "src/types/**/*.d.ts"], "rules": {"@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-unused-vars": "off"}}, {"files": ["src/lib/excel/**/*.ts", "src/lib/operations/**/*.ts", "src/lib/ai/**/*.ts", "src/server/ai/**/*.ts", "src/lib/bridge/**/*.ts", "src/lib/middleware/**/*.ts", "src/middleware/**/*.ts", "src/lib/errors.ts", "src/lib/logger.ts", "src/lib/telemetry.ts", "src/utils/**/*.ts"], "rules": {"@typescript-eslint/no-explicit-any": "off", "no-console": "off"}}, {"files": ["src/components/**/*.tsx", "src/app/**/*.tsx"], "rules": {"@typescript-eslint/no-explicit-any": "off", "react-hooks/exhaustive-deps": "warn"}}, {"files": ["src/lib/bridge/**/*.ts", "src/hooks/useSocket.ts", "src/lib/desktop-bridge-*.ts"], "rules": {"no-console": "off"}}, {"files": ["**/withServerValidation.ts", "**/withErrorHandling.ts", "**/csrf-protection.ts"], "rules": {"@typescript-eslint/no-unused-vars": "off"}}, {"files": ["src/app/**/*.ts", "src/app/**/*.tsx"], "rules": {"no-restricted-imports": ["error", {"patterns": [{"group": ["next/router"], "message": "Next/router não é compatível com App Router. Use next/navigation."}]}]}}, {"files": ["scripts/**/*.js"], "rules": {"no-console": "off", "@typescript-eslint/no-var-requires": "off"}}, {"files": ["src/components/client-scripts.tsx", "src/components/rsc-error-suppressor.tsx", "src/components/**/debug*.tsx", "src/components/**/error*.tsx"], "rules": {"no-console": ["warn", {"allow": ["warn", "error", "info", "debug", "log"]}]}}]}