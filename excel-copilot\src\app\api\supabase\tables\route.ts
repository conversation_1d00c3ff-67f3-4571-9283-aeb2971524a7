import { NextRequest, NextResponse } from 'next/server';

import { logger } from '@/lib/logger';
import { SupabaseMonitoringService } from '@/lib/supabase-integration';
import { apiRateLimiter } from '@/middleware/rate-limit';
import { ApiResponse } from '@/utils/api-response';

// Forçar o modo dinâmico para essa rota
export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

/**
 * GET /api/supabase/tables
 * Obtém informações sobre as tabelas do banco de dados Supabase
 */
export async function GET(request: NextRequest) {
  try {
    // Aplicar rate limiting
    const rateLimitResult = await apiRateLimiter(request, new NextResponse());
    if (rateLimitResult) {
      return rateLimitResult;
    }

    // Verificar se temos as credenciais necessárias
    const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    const projectUrl = process.env.SUPABASE_URL;

    if (!serviceRoleKey || !projectUrl) {
      return ApiResponse.error(
        'Credenciais do Supabase não configuradas',
        'SUPABASE_NOT_CONFIGURED',
        500
      );
    }

    // Criar instância do serviço de monitoramento
    const supabaseService = new SupabaseMonitoringService({
      serviceRoleKey,
      projectUrl,
    });

    // Obter parâmetros da query
    const { searchParams } = new URL(request.url);
    const includeDetails = searchParams.get('details') === 'true';
    const limit = parseInt(searchParams.get('limit') || '50');
    const sortBy = searchParams.get('sort') || 'size'; // size, name, rows

    // Obter resumo das tabelas
    const databaseSummary = await supabaseService.getDatabaseSummary();

    // Ordenar tabelas conforme solicitado
    const sortedTables = [...databaseSummary.tables];
    switch (sortBy) {
      case 'name':
        sortedTables.sort((a, b) => a.name.localeCompare(b.name));
        break;
      case 'rows':
        sortedTables.sort((a, b) => (b.row_count || 0) - (a.row_count || 0));
        break;
      case 'size':
      default:
        sortedTables.sort((a, b) => (b.bytes || 0) - (a.bytes || 0));
        break;
    }

    // Limitar resultados
    const limitedTables = sortedTables.slice(0, limit);

    // Preparar resposta
    const response = {
      summary: {
        totalTables: databaseSummary.totalTables,
        totalSize: databaseSummary.totalSize,
        largestTable: databaseSummary.largestTables[0]
          ? {
              name: databaseSummary.largestTables[0].name,
              size: databaseSummary.largestTables[0].size,
              schema: databaseSummary.largestTables[0].schema,
            }
          : null,
      },
      tables: limitedTables.map(table => ({
        name: table.name,
        schema: table.schema,
        size: table.size || '0 Bytes',
        bytes: table.bytes || 0,
        ...(includeDetails && {
          rls_enabled: table.rls_enabled,
          rls_forced: table.rls_forced,
          row_count: table.row_count || 0,
          dead_row_count: table.dead_row_count || 0,
          seq_scan_count: table.seq_scan_count || 0,
          idx_scan_count: table.idx_scan_count || 0,
        }),
      })),
      pagination: {
        limit,
        total: databaseSummary.totalTables,
        showing: limitedTables.length,
        hasMore: databaseSummary.totalTables > limit,
      },
      filters: {
        sortBy,
        includeDetails,
      },
      timestamp: new Date().toISOString(),
    };

    logger.info('Tabelas Supabase obtidas com sucesso', {
      totalTables: databaseSummary.totalTables,
      showing: limitedTables.length,
      sortBy,
    });

    return ApiResponse.success(response);
  } catch (error) {
    logger.error('Erro ao obter tabelas do Supabase', { error });

    if (error instanceof Error) {
      return ApiResponse.error(
        `Erro ao acessar banco de dados: ${error.message}`,
        'SUPABASE_DATABASE_ERROR',
        500
      );
    }

    return ApiResponse.error('Erro interno do servidor', 'INTERNAL_ERROR', 500);
  }
}

/**
 * POST /api/supabase/tables
 * Executa operações específicas nas tabelas (análise, otimização, etc.)
 */
export async function POST(request: NextRequest) {
  try {
    // Aplicar rate limiting
    const rateLimitResult = await apiRateLimiter(request, new NextResponse());
    if (rateLimitResult) {
      return rateLimitResult;
    }

    const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    const projectUrl = process.env.SUPABASE_URL;

    if (!serviceRoleKey || !projectUrl) {
      return ApiResponse.error(
        'Credenciais do Supabase não configuradas',
        'SUPABASE_NOT_CONFIGURED',
        500
      );
    }

    const body = await request.json();
    const { action, tableName, options: _options } = body;

    const supabaseService = new SupabaseMonitoringService({
      serviceRoleKey,
      projectUrl,
    });

    switch (action) {
      case 'analyze': {
        // Análise detalhada de uma tabela específica
        if (!tableName) {
          return ApiResponse.error(
            'Nome da tabela é obrigatório para análise',
            'MISSING_TABLE_NAME',
            400
          );
        }

        const databaseSummary = await supabaseService.getDatabaseSummary();
        const table = databaseSummary.tables.find(t => t.name === tableName);

        if (!table) {
          return ApiResponse.error(`Tabela '${tableName}' não encontrada`, 'TABLE_NOT_FOUND', 404);
        }

        const analysis = {
          table: {
            name: table.name,
            schema: table.schema,
            size: table.size,
            bytes: table.bytes,
            row_count: table.row_count || 0,
            dead_row_count: table.dead_row_count || 0,
          },
          performance: {
            seq_scan_count: table.seq_scan_count || 0,
            seq_tup_read: table.seq_tup_read || 0,
            idx_scan_count: table.idx_scan_count || 0,
            idx_tup_fetch: table.idx_tup_fetch || 0,
            scan_efficiency:
              table.idx_scan_count && table.seq_scan_count
                ? (table.idx_scan_count / (table.idx_scan_count + table.seq_scan_count)) * 100
                : 0,
          },
          security: {
            rls_enabled: table.rls_enabled || false,
            rls_forced: table.rls_forced || false,
            replica_identity: table.replica_identity || 'default',
          },
          recommendations: [] as Array<{
            type: string;
            priority: string;
            message: string;
            action: string;
          }>,
        };

        // Gerar recomendações
        const recommendations = [];
        if (!table.rls_enabled) {
          recommendations.push({
            type: 'security',
            priority: 'high',
            message: 'Considere habilitar Row Level Security (RLS) para esta tabela',
            action: 'enable_rls',
          });
        }

        if (
          table.dead_row_count &&
          table.row_count &&
          table.dead_row_count / table.row_count > 0.1
        ) {
          recommendations.push({
            type: 'performance',
            priority: 'medium',
            message: 'Tabela tem muitas linhas mortas, considere executar VACUUM',
            action: 'vacuum_table',
          });
        }

        if (
          table.seq_scan_count &&
          table.idx_scan_count &&
          table.seq_scan_count > table.idx_scan_count
        ) {
          recommendations.push({
            type: 'performance',
            priority: 'medium',
            message:
              'Tabela tem mais sequential scans que index scans, considere adicionar índices',
            action: 'add_indexes',
          });
        }

        analysis.recommendations = recommendations;

        return ApiResponse.success({
          action: 'table_analyzed',
          tableName,
          analysis,
          timestamp: new Date().toISOString(),
        });
      }

      case 'refresh_stats': {
        // Forçar atualização das estatísticas das tabelas
        const refreshedSummary = await supabaseService.getDatabaseSummary();

        return ApiResponse.success({
          action: 'stats_refreshed',
          summary: {
            totalTables: refreshedSummary.totalTables,
            totalSize: refreshedSummary.totalSize,
            largestTables: refreshedSummary.largestTables.slice(0, 5).map(table => ({
              name: table.name,
              size: table.size,
              bytes: table.bytes,
            })),
          },
          timestamp: new Date().toISOString(),
        });
      }

      default:
        return ApiResponse.error(`Ação '${action}' não suportada`, 'UNSUPPORTED_ACTION', 400);
    }
  } catch (error) {
    logger.error('Erro no POST tabelas Supabase', { error });

    if (error instanceof Error) {
      return ApiResponse.error(
        `Erro na operação: ${error.message}`,
        'SUPABASE_OPERATION_ERROR',
        500
      );
    }

    return ApiResponse.error('Erro interno do servidor', 'INTERNAL_ERROR', 500);
  }
}
