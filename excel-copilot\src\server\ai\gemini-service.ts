import * as fs from 'fs';
import * as path from 'path';

import { VertexAI } from '@google-cloud/vertexai';
import { z } from 'zod';

import { ENV } from '@/config/unified-environment';
import { logger } from '@/lib/logger';

/**
 * Tipos de erro específicos para o serviço Gemini
 */
export enum GeminiErrorType {
  TIMEOUT = 'timeout',
  API_UNAVAILABLE = 'api_unavailable',
  INVALID_REQUEST = 'invalid_request',
  CONTENT_FILTERED = 'content_filtered',
  RATE_LIMITED = 'rate_limited',
  UNKNOWN = 'unknown',
  INVALID_RESPONSE_FORMAT = 'invalid_response_format',
  CONTEXT_LIMIT_EXCEEDED = 'context_limit_exceeded',
  TOKEN_LIMIT_EXCEEDED = 'token_limit_exceeded',
  RETRY_FAILED = 'retry_failed',
}

/**
 * Classe de erro personalizada
 */
export class GeminiServiceError extends Error {
  type: GeminiErrorType;
  details?: any;
  recoverable: boolean;

  constructor(
    message: string,
    type: GeminiErrorType = GeminiErrorType.UNKNOWN,
    details?: any,
    recoverable: boolean = false
  ) {
    super(message);
    this.name = 'GeminiServiceError';
    this.type = type;
    this.details = details;
    this.recoverable = recoverable;
  }
}

/**
 * Interface para opções de envio de mensagem
 */
export interface SendMessageOptions {
  /**
   * Ignorar cache e sempre fazer uma nova requisição
   */
  bypassCache?: boolean;

  /**
   * ID do usuário para isolar cache entre usuários diferentes
   * Se não fornecido, o cache será compartilhado (use apenas para conteúdo público)
   */
  userId?: string;

  /**
   * Configurações de temperatura para controlar a criatividade da resposta
   * Valores mais altos (>0.7) são mais criativos, valores mais baixos (<0.3) são mais determinísticos
   * Padrão: 0.4 para equilíbrio entre criatividade e precisão
   */
  temperature?: number;

  /**
   * Configurações para priorizar estruturas específicas na resposta
   */
  responseStructure?: {
    /**
     * Solicitar resposta em formato JSON
     */
    preferJson?: boolean;

    /**
     * Solicitar respostas mais concisas
     */
    preferConcise?: boolean;

    /**
     * Estrutura esperada do JSON (para validação)
     */
    jsonSchema?: Record<string, any>;
  };

  /**
   * Configurações para contexto de Excel
   */
  excelContext?: {
    /**
     * Planilha ativa
     */
    activeSheet?: string;

    /**
     * Seleção atual
     */
    selection?: string;

    /**
     * Cabeçalhos disponíveis
     */
    headers?: string[];

    /**
     * Operações recentes
     */
    recentOperations?: string[];
  };

  /**
   * Adicionar instruções específicas ao prompt
   */
  systemPrompt?: string;

  /**
   * Timeout para a requisição em ms (padrão: 60000 - 60 segundos)
   */
  timeout?: number;
}

// Prompt system padrão para processamento de Excel
const DEFAULT_EXCEL_SYSTEM_PROMPT = `
Você é um assistente especializado em Excel, capaz de ajudar a realizar operações com planilhas.
Para todas as perguntas, forneça respostas concisas e diretas.
Quando o usuário solicitar uma operação no Excel, interprete o comando e responda com:

1. Uma explicação curta do que será feito
2. As operações exatas a serem executadas no formato JSON

Quando responder com JSON, use o seguinte formato:
{
  "operations": [
    {
      "type": "TIPO_DA_OPERAÇÃO",
      "data": { ... parâmetros específicos ... }
    }
  ],
  "explanation": "Breve explicação do que foi feito"
}

Tipos de operações disponíveis:
- FORMULA: Para aplicar fórmulas em células
- FILTER: Para filtrar dados
- SORT: Para ordenar dados
- CHART: Para criar ou modificar gráficos
- COLUMN_OPERATION: Para operações com colunas
- CONDITIONAL_FORMAT: Para formatação condicional
- PIVOT_TABLE: Para criar ou modificar tabelas dinâmicas
- ADVANCED_VISUALIZATION: Para visualizações avançadas
- DATA_ANALYSIS: Para análise estatística de dados

Seja preciso na interpretação dos comandos do usuário e forneça os parâmetros corretos para cada tipo de operação.
`;

/**
 * Serviço para integração com a API do Google Gemini através do Vertex AI
 */
export class GeminiService {
  private static instance: GeminiService;
  private vertexAI: VertexAI | null = null;
  private vertexModel: any = null;
  private requestCache: Map<string, { response: string; timestamp: number }> = new Map();
  private requestsInProgress: Map<string, Promise<string>> = new Map();
  private cacheCleanupInterval: NodeJS.Timeout | null = null;

  // Contadores para monitoramento
  private requestCount: number = 0;
  private errorCount: number = 0;
  private cacheHitCount: number = 0;

  private constructor() {
    // Verificar se estamos em modo mock para desenvolvimento
    if (ENV.FEATURES.USE_MOCK_AI) {
      logger.info('Usando modo mock para respostas de IA.');
      // Configure limpeza periódica de cache
      this.cacheCleanupInterval = setInterval(() => this.cleanupCache(), 3600000); // Limpar cache a cada hora
      return;
    }

    // Tentar inicializar apenas o Vertex AI
    if (ENV.VERTEX_AI.ENABLED && this.initVertexAI()) {
      logger.info('Serviço Vertex AI inicializado com sucesso');
    } else {
      // Não temos Vertex AI e não estamos em modo mock
      logger.warn(
        'Vertex AI não pôde ser inicializado e o modo mock está desativado. O serviço de IA não estará disponível.'
      );
    }

    // Configurar limpeza periódica de cache
    this.cacheCleanupInterval = setInterval(() => this.cleanupCache(), 3600000); // Limpar cache a cada hora
  }

  /**
   * Inicializa o cliente Vertex AI com as credenciais do arquivo JSON
   */
  private initVertexAI(): boolean {
    try {
      let projectId = ENV.VERTEX_AI.PROJECT_ID;
      let credentials: any = null;

      // Verificar se temos credenciais como string JSON na variável de ambiente
      if (process.env.VERTEX_AI_CREDENTIALS) {
        try {
          // Limpar caracteres de controle inválidos do JSON
          credentials = JSON.parse(process.env.VERTEX_AI_CREDENTIALS);

          // Validar se é um service account válido
          if (!credentials.type || credentials.type !== 'service_account') {
            logger.error('Credenciais devem ser de uma service account');
            return false;
          }

          if (credentials.project_id && !projectId) {
            projectId = credentials.project_id;
          }
          logger.info('Usando credenciais do Vertex AI da variável de ambiente');
        } catch (parseError) {
          logger.error('Erro ao analisar VERTEX_AI_CREDENTIALS:', parseError);
          return false;
        }
      }

      // Verificar se existe um caminho configurado explicitamente
      if (ENV.VERTEX_AI.CREDENTIALS_PATH) {
        // Verificar se o arquivo existe
        if (!fs.existsSync(ENV.VERTEX_AI.CREDENTIALS_PATH)) {
          logger.error(
            `Arquivo de credenciais do Vertex AI não encontrado em: ${ENV.VERTEX_AI.CREDENTIALS_PATH}`
          );
          return false;
        }

        // Definir a variável de ambiente para o SDK do Vertex AI
        process.env.GOOGLE_APPLICATION_CREDENTIALS = ENV.VERTEX_AI.CREDENTIALS_PATH;
        logger.info(`Usando credenciais do caminho configurado: ${ENV.VERTEX_AI.CREDENTIALS_PATH}`);
      } else if (credentials) {
        // Se temos credenciais como objeto, criar um arquivo temporário
        const tempCredentialsPath = path.join(process.cwd(), '.vertex-credentials-temp.json');
        fs.writeFileSync(tempCredentialsPath, JSON.stringify(credentials, null, 2));
        process.env.GOOGLE_APPLICATION_CREDENTIALS = tempCredentialsPath;
        logger.info('Usando credenciais temporárias do Vertex AI');
      } else {
        // Tentar usar o arquivo vertex-credentials.json na raiz do projeto
        const credentialPath = path.join(process.cwd(), 'vertex-credentials.json');

        if (fs.existsSync(credentialPath)) {
          // Definir a variável de ambiente para o SDK do Vertex AI
          process.env.GOOGLE_APPLICATION_CREDENTIALS = credentialPath;
          logger.info(`Usando arquivo de credenciais padrão: ${credentialPath}`);
        } else if (!process.env.GOOGLE_APPLICATION_CREDENTIALS) {
          logger.error(
            'Nenhum arquivo de credenciais do Vertex AI encontrado e variável GOOGLE_APPLICATION_CREDENTIALS não está definida'
          );
          return false;
        }
      }

      // Tentar ler as credenciais do arquivo para extrair o project_id se ainda não temos
      if (
        !projectId &&
        process.env.GOOGLE_APPLICATION_CREDENTIALS &&
        fs.existsSync(process.env.GOOGLE_APPLICATION_CREDENTIALS)
      ) {
        try {
          const credentialsFromFile = JSON.parse(
            fs.readFileSync(process.env.GOOGLE_APPLICATION_CREDENTIALS, 'utf8')
          );
          projectId = credentialsFromFile.project_id;
          logger.info(`Project ID extraído das credenciais: ${projectId}`);
        } catch (parseError) {
          logger.error('Erro ao analisar arquivo de credenciais:', parseError);
          return false;
        }
      }

      if (!projectId) {
        logger.error('Project ID do Vertex AI não encontrado nas credenciais ou configurações');
        return false;
      }

      // Inicializar o cliente do Vertex AI
      const location = ENV.VERTEX_AI.LOCATION || 'us-central1';
      this.vertexAI = new VertexAI({
        project: projectId,
        location: location,
      });

      // Obter instância do modelo - usar o caminho correto para o modelo Gemini
      const modelName = ENV.VERTEX_AI.MODEL_NAME || 'gemini-1.5-pro';

      // Acessar o modelo através da API preview (necessário para modelos Gemini)
      this.vertexModel = this.vertexAI.preview.getGenerativeModel({
        model: modelName,
      });

      logger.info(
        `Vertex AI inicializado com sucesso: projeto=${projectId}, região=${location}, modelo=${modelName}`
      );
      return true;
    } catch (error) {
      logger.error('Erro ao inicializar Vertex AI:', error);
      return false;
    }
  }

  /**
   * Obtém a instância singleton do serviço
   */
  public static getInstance(): GeminiService {
    if (!GeminiService.instance) {
      GeminiService.instance = new GeminiService();
    }
    return GeminiService.instance;
  }

  /**
   * Limpa o cache de respostas
   * @param userId Se fornecido, limpa apenas o cache deste usuário específico
   */
  public clearCache(userId?: string): void {
    if (userId) {
      // Limpar apenas cache do usuário específico
      for (const [key, _] of this.requestCache) {
        if (key.includes(`user:${userId}:`)) {
          this.requestCache.delete(key);
        }
      }
    } else {
      // Limpar todo o cache
      this.requestCache.clear();
    }
  }

  /**
   * Limpa entradas antigas do cache
   */
  private cleanupCache(): void {
    const now = Date.now();
    const maxAge = ENV.CACHE.AI_CACHE_TTL * 1000; // Converter de segundos para ms

    for (const [key, value] of this.requestCache) {
      if (now - value.timestamp > maxAge) {
        this.requestCache.delete(key);
      }
    }
  }

  /**
   * Desliga o serviço e libera recursos
   */
  public async shutdown(): Promise<void> {
    // Limpar o temporizador de limpeza de cache
    if (this.cacheCleanupInterval) {
      clearInterval(this.cacheCleanupInterval);
      this.cacheCleanupInterval = null;
    }

    // Limpar caches
    this.requestCache.clear();
    this.requestsInProgress.clear();

    logger.info('Serviço Google Gemini desligado com sucesso');
  }

  /**
   * Gera uma chave de cache para a combinação de mensagem, histórico e opções
   */
  private generateCacheKey(
    message: string,
    history: Array<{ role: string; content: string }> = [],
    options: SendMessageOptions = {}
  ): string {
    // Incluir ID do usuário na chave de cache, se fornecido
    const userPrefix = options.userId ? `user:${options.userId}:` : '';

    // Combinar mensagem, histórico e opções específicas em uma única string
    const historyStr = history.map(h => `${h.role}:${h.content}`).join('|');
    const optionsStr = JSON.stringify({
      temperature: options.temperature,
      responseStructure: options.responseStructure,
      excelContext: options.excelContext,
    });

    return `${userPrefix}${message}|${historyStr}|${optionsStr}`;
  }

  /**
   * Processa um comando relacionado ao Excel e retorna uma resposta
   * @param command O comando para processar
   * @param context Contexto adicional (opcional)
   */
  public async processCommand(command: string, context?: string): Promise<string> {
    try {
      // Preparar histórico e opções
      const history: Array<{ role: string; content: string }> = [];

      // Adicionar contexto ao histórico, se fornecido
      if (context) {
        history.push({
          role: 'user',
          content: `Contexto adicional: ${context}`,
        });

        history.push({
          role: 'model',
          content: 'Entendido. Utilizarei este contexto para processar seu próximo comando.',
        });
      }

      // Configurar opções para processamento de comandos Excel
      const options: SendMessageOptions = {
        temperature: 0.2, // Temperatura baixa para respostas mais determinísticas
        responseStructure: {
          preferJson: true,
        },
        systemPrompt: DEFAULT_EXCEL_SYSTEM_PROMPT,
      };

      // Enviar comando para processamento
      return await this.sendMessage(command, history, options);
    } catch (error) {
      logger.error('Erro ao processar comando Excel:', error);

      if (error instanceof GeminiServiceError) {
        throw error;
      }

      throw new GeminiServiceError(
        'Não foi possível processar o comando Excel',
        GeminiErrorType.UNKNOWN,
        error
      );
    }
  }

  /**
   * Envia uma mensagem para o Gemini com suporte a streaming, compatível com Vercel AI SDK
   * @param message Mensagem a ser enviada
   * @param history Histórico de mensagens
   * @param options Opções adicionais
   * @returns Stream de resposta
   */
  public async streamMessage(
    message: string,
    history: Array<{ role: string; content: string }> = [],
    options: SendMessageOptions = {}
  ): Promise<ReadableStream<Uint8Array>> {
    try {
      // Verificar modos de mock para desenvolvimento/testes
      if (ENV.FEATURES.USE_MOCK_AI) {
        return this.createMockStream(message, options);
      }

      // Verificar se temos o Vertex AI disponível
      if (this.vertexAI && this.vertexModel) {
        return this.streamVertexAIMessage(message, history, options);
      }

      // Se chegarmos aqui, não temos Vertex AI nem mock
      throw new GeminiServiceError(
        'Serviço de IA não está disponível. Verifique a configuração do Vertex AI ou ative o modo mock.',
        GeminiErrorType.API_UNAVAILABLE
      );
    } catch (error: unknown) {
      // Converter para GeminiServiceError se ainda não for
      const geminiError =
        error instanceof GeminiServiceError
          ? error
          : new GeminiServiceError(
              error instanceof Error ? error.message : 'Erro desconhecido no serviço de streaming',
              GeminiErrorType.UNKNOWN,
              error
            );

      // Log do erro
      logger.error('Erro ao processar streaming de IA:', {
        error: geminiError.message,
        type: geminiError.type,
        details: geminiError.details,
      });

      // Criar um stream que emite o erro e fecha
      const encoder = new TextEncoder();
      const errorStream = new ReadableStream({
        start(controller) {
          controller.enqueue(
            encoder.encode(
              JSON.stringify({
                error: geminiError.message,
                type: geminiError.type,
              })
            )
          );
          controller.close();
        },
      });

      return errorStream;
    }
  }

  /**
   * Envia uma mensagem para o Gemini API e retorna a resposta
   * @param message A mensagem a ser enviada
   * @param history Histórico de conversa anterior (opcional)
   * @param options Opções adicionais para a requisição (opcional)
   */
  public async sendMessage(
    message: string,
    history: Array<{ role: string; content: string }> = [],
    options: SendMessageOptions = {}
  ): Promise<string> {
    try {
      // Gerar chave de cache
      const cacheKey = this.generateCacheKey(message, history, options);

      // Verificar se já tem uma requisição em andamento com a mesma chave
      if (this.requestsInProgress.has(cacheKey)) {
        return await this.requestsInProgress.get(cacheKey)!;
      }

      // Verificar cache se não estiver configurado para ignorar
      if (!options.bypassCache) {
        const cachedResponse = this.requestCache.get(cacheKey);
        if (cachedResponse) {
          // Incrementar contador de cache hits
          this.cacheHitCount++;
          logger.debug('Usando resposta em cache para a mensagem:', { message, cacheKey });
          return cachedResponse.response;
        }
      }

      // Verificar modos de mock para desenvolvimento
      if (ENV.FEATURES.USE_MOCK_AI) {
        const mockResponse = this.getMockResponse(message, options);

        // Guardar resposta no cache, se não estiver configurado para ignorar
        if (!options.bypassCache) {
          this.requestCache.set(cacheKey, {
            response: mockResponse,
            timestamp: Date.now(),
          });
        }

        return mockResponse;
      }

      // Verificar se temos o Vertex AI disponível
      if (!this.vertexAI || !this.vertexModel) {
        throw new GeminiServiceError(
          'Serviço de IA não está disponível. Verifique a configuração do Vertex AI ou ative o modo mock.',
          GeminiErrorType.API_UNAVAILABLE
        );
      }

      // Criar nova requisição e guardar a promessa para evitar requisições duplicadas
      const requestPromise = this.makeVertexAIRequest(message, history, options);
      this.requestsInProgress.set(cacheKey, requestPromise);

      try {
        // Aguardar resposta
        const response = await requestPromise;

        // Guardar resposta no cache, se não estiver configurado para ignorar
        if (!options.bypassCache) {
          this.requestCache.set(cacheKey, {
            response,
            timestamp: Date.now(),
          });
        }

        return response;
      } finally {
        // Remover a requisição da lista de em andamento
        this.requestsInProgress.delete(cacheKey);
      }
    } catch (error) {
      logger.error('Erro ao enviar mensagem para Vertex AI:', error);

      if (error instanceof GeminiServiceError) {
        throw error;
      }

      throw new GeminiServiceError(
        'Não foi possível enviar mensagem para o serviço de IA',
        GeminiErrorType.UNKNOWN,
        error
      );
    }
  }

  /**
   * Faz a requisição real para o Vertex AI
   */
  private async makeRequest(
    message: string,
    history: Array<{ role: string; content: string }> = [],
    options: SendMessageOptions = {}
  ): Promise<string> {
    // Verificar modo mock
    if (ENV.FEATURES.USE_MOCK_AI) {
      return this.getMockResponse(message, options);
    }

    // Usar Vertex AI
    if (this.vertexAI && this.vertexModel) {
      return this.makeVertexAIRequest(message, history, options);
    }

    // Nem modo mock nem Vertex AI estão disponíveis
    throw new GeminiServiceError(
      'Serviço de IA não está disponível. Verifique a configuração do Vertex AI ou ative o modo mock.',
      GeminiErrorType.API_UNAVAILABLE
    );
  }

  /**
   * Faz requisição para o Vertex AI
   */
  private async makeVertexAIRequest(
    message: string,
    history: Array<{ role: string; content: string }> = [],
    options: SendMessageOptions = {}
  ): Promise<string> {
    try {
      if (!this.vertexModel) {
        throw new GeminiServiceError(
          'Modelo Vertex AI não inicializado',
          GeminiErrorType.API_UNAVAILABLE
        );
      }

      const systemPrompt = options.systemPrompt || DEFAULT_EXCEL_SYSTEM_PROMPT;
      const excelContext = options.excelContext;

      // Preparar contexto do Excel
      let contextStr = '';
      if (excelContext) {
        contextStr += '\n\nContexto da planilha atual:';

        if (excelContext.activeSheet) {
          contextStr += `\n- Planilha ativa: ${excelContext.activeSheet}`;
        }

        if (excelContext.selection) {
          contextStr += `\n- Seleção atual: ${excelContext.selection}`;
        }

        if (excelContext.headers && excelContext.headers.length > 0) {
          contextStr += `\n- Cabeçalhos disponíveis: ${excelContext.headers.join(', ')}`;
        }

        if (excelContext.recentOperations && excelContext.recentOperations.length > 0) {
          contextStr += '\n- Operações recentes:';
          excelContext.recentOperations.forEach(op => {
            contextStr += `\n  * ${op}`;
          });
        }
      }

      // Preparar as mensagens incluindo o histórico
      const messages = [];

      // Adicionar o system prompt como primeira mensagem
      messages.push({
        role: 'system',
        parts: [{ text: systemPrompt + contextStr }],
      });

      // Adicionar histórico de mensagens
      for (const msg of history) {
        messages.push({
          role: msg.role === 'user' ? 'user' : 'assistant',
          parts: [{ text: msg.content }],
        });
      }

      // Adicionar a mensagem atual do usuário
      messages.push({
        role: 'user',
        parts: [{ text: message }],
      });

      // Configurações de geração
      const generationConfig = {
        temperature: options.temperature || 0.4,
        topP: 0.95,
        topK: 40,
        maxOutputTokens: 4096,
      };

      // Configurações de segurança
      const safetySettings = [
        {
          category: 'HARM_CATEGORY_HARASSMENT',
          threshold: 'BLOCK_ONLY_HIGH',
        },
        {
          category: 'HARM_CATEGORY_HATE_SPEECH',
          threshold: 'BLOCK_ONLY_HIGH',
        },
        {
          category: 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
          threshold: 'BLOCK_ONLY_HIGH',
        },
        {
          category: 'HARM_CATEGORY_DANGEROUS_CONTENT',
          threshold: 'BLOCK_ONLY_HIGH',
        },
      ];

      // Fazer a requisição ao Vertex AI
      const result = await this.vertexModel.generateContent({
        contents: messages,
        generationConfig,
        safetySettings,
      });

      // Extrair o texto da resposta
      const responseText = result.response.candidates[0].content.parts[0].text;

      // Processar a resposta conforme as configurações
      return this.processResponse(responseText, options);
    } catch (error: any) {
      this.errorCount++;

      let errorMessage = 'Erro desconhecido na comunicação com o Vertex AI';
      let errorType = GeminiErrorType.UNKNOWN;

      if (error.message.includes('deadline') || error.message.includes('timeout')) {
        errorMessage = 'A requisição para o Vertex AI excedeu o tempo limite';
        errorType = GeminiErrorType.TIMEOUT;
      } else if (error.message.includes('safety') || error.message.includes('blocked')) {
        errorMessage = 'O conteúdo foi filtrado por questões de segurança';
        errorType = GeminiErrorType.CONTENT_FILTERED;
      } else if (error.message.includes('rate') || error.message.includes('quota')) {
        errorMessage = 'Limite de requisições excedido. Tente novamente mais tarde';
        errorType = GeminiErrorType.RATE_LIMITED;
      } else if (
        error.message.includes('authentication') ||
        error.message.includes('credentials')
      ) {
        errorMessage = 'Erro de autenticação com o Vertex AI';
        errorType = GeminiErrorType.API_UNAVAILABLE;
      }

      throw new GeminiServiceError(errorMessage, errorType, error);
    }
  }

  /**
   * Processa a resposta recebida da API
   */
  private processResponse(responseText: string, options: SendMessageOptions): string {
    // Verificar se a resposta contém conteúdo potencialmente inseguro
    if (this.containsUnsafeContent(responseText)) {
      throw new GeminiServiceError(
        'A resposta foi filtrada por possível conteúdo inadequado.',
        GeminiErrorType.CONTENT_FILTERED,
        { responseText }
      );
    }

    // Processar a resposta de acordo com as configurações
    if (options.responseStructure?.preferJson) {
      try {
        // Extrair objeto JSON se a resposta contiver um
        const jsonMatch =
          responseText.match(/```(?:json)?\s*(\{[\s\S]*?\})\s*```/) ||
          responseText.match(/(\{[\s\S]*?\})/);

        if (jsonMatch && jsonMatch[1]) {
          // Temos um JSON, verificar estrutura se schema fornecido
          const jsonStr = jsonMatch[1].trim();
          const jsonData = JSON.parse(jsonStr);

          if (options.responseStructure.jsonSchema) {
            // Implementação da validação de schema usando Zod
            try {
              // Converter o esquema JSON fornecido para um esquema Zod
              // Esta é uma abordagem simples que suporta os tipos básicos
              const zodSchema = this.convertJsonSchemaToZod(options.responseStructure.jsonSchema);

              // Validar o JSON contra o schema Zod
              const validationResult = zodSchema.safeParse(jsonData);

              if (!validationResult.success) {
                logger.warn('Validação de schema JSON falhou:', {
                  errors: validationResult.error.format(),
                  data: jsonData,
                });

                throw new GeminiServiceError(
                  'A resposta da IA não está no formato esperado.',
                  GeminiErrorType.INVALID_RESPONSE_FORMAT,
                  { errors: validationResult.error.format(), data: jsonData }
                );
              }

              // Se chegou aqui, a validação foi bem-sucedida
              return JSON.stringify(validationResult.data);
            } catch (schemaError) {
              // Erro ao processar o schema
              logger.warn('Erro ao validar schema JSON:', { error: schemaError, jsonData });
              // Continuar e retornar o JSON como está, sem validação
            }
          }

          // Retornar apenas o objeto JSON como string
          return jsonStr;
        }
      } catch (error) {
        logger.warn('Falha ao processar JSON na resposta:', { error, responseText });
        // Continuar e retornar a resposta completa em caso de erro
      }
    }

    // Retornar a resposta como está
    return responseText.trim();
  }

  /**
   * Converte um esquema JSON simples para um esquema Zod
   * @param jsonSchema Schema JSON
   * @returns Schema Zod equivalente
   */
  private convertJsonSchemaToZod(jsonSchema: Record<string, any>): z.ZodType {
    // Se for um objeto, criar um ZodObject
    if (jsonSchema.type === 'object') {
      const shape: Record<string, z.ZodType> = {};

      // Processar propriedades
      if (jsonSchema.properties) {
        for (const [key, prop] of Object.entries<any>(jsonSchema.properties)) {
          let fieldSchema = this.convertJsonPropertyToZod(prop);

          // Verificar se o campo é obrigatório
          const isRequired = jsonSchema.required?.includes(key);
          if (!isRequired) {
            fieldSchema = fieldSchema.optional();
          }

          shape[key] = fieldSchema;
        }
      }

      return z.object(shape);
    }

    // Se for um array, criar um ZodArray
    if (jsonSchema.type === 'array') {
      const itemSchema = jsonSchema.items
        ? this.convertJsonPropertyToZod(jsonSchema.items)
        : z.any();

      return z.array(itemSchema);
    }

    // Para outros tipos, usar um schema genérico
    return z.any();
  }

  /**
   * Converte uma propriedade de schema JSON para um tipo Zod
   * @param prop Propriedade do schema JSON
   * @returns Tipo Zod equivalente
   */
  private convertJsonPropertyToZod(prop: any): z.ZodType {
    switch (prop.type) {
      case 'string': {
        let stringSchema = z.string();
        if (prop.minLength !== undefined) stringSchema = stringSchema.min(prop.minLength);
        if (prop.maxLength !== undefined) stringSchema = stringSchema.max(prop.maxLength);
        if (prop.pattern) stringSchema = stringSchema.regex(new RegExp(prop.pattern));
        return stringSchema;
      }

      case 'number':
      case 'integer': {
        let numberSchema = prop.type === 'integer' ? z.number().int() : z.number();
        if (prop.minimum !== undefined) numberSchema = numberSchema.min(prop.minimum);
        if (prop.maximum !== undefined) numberSchema = numberSchema.max(prop.maximum);
        return numberSchema;
      }

      case 'boolean':
        return z.boolean();

      case 'object':
        return this.convertJsonSchemaToZod(prop);

      case 'array':
        return z.array(prop.items ? this.convertJsonPropertyToZod(prop.items) : z.any());

      default:
        return z.any();
    }
  }

  /**
   * Verifica se o texto contém conteúdo que deveria ser filtrado
   */
  private containsUnsafeContent(text: string): boolean {
    const unsafePatterns = [
      /não posso (ajudar|responder|fornecer|compartilhar|discutir) (isso|tal conteúdo|tal solicitação)/i,
      /como modelo de IA, não posso/i,
      /I'm (unable|not able) to (provide|assist|respond|reply)/i,
      /I cannot (provide|assist|respond|reply)/i,
      /I don't (feel comfortable|think it's appropriate)/i,
    ];

    return unsafePatterns.some(pattern => pattern.test(text));
  }

  /**
   * Retorna uma resposta mock para uso em testes e desenvolvimento
   */
  private getMockResponse(message: string, options?: SendMessageOptions): string {
    const lowercaseMessage = message.toLowerCase();

    if (options?.responseStructure?.preferJson) {
      // Simular resposta JSON para comandos Excel
      if (lowercaseMessage.includes('soma') || lowercaseMessage.includes('somar')) {
        return JSON.stringify(
          {
            operations: [
              {
                type: 'FORMULA',
                data: {
                  formula: 'SUM',
                  range: 'A1:A10',
                  targetCell: 'A12',
                },
              },
            ],
            explanation: 'Somando valores de A1 até A10 e colocando o resultado em A12.',
          },
          null,
          2
        );
      }

      if (lowercaseMessage.includes('média') || lowercaseMessage.includes('media')) {
        return JSON.stringify(
          {
            operations: [
              {
                type: 'FORMULA',
                data: {
                  formula: 'AVERAGE',
                  range: 'B1:B20',
                  targetCell: 'B22',
                },
              },
            ],
            explanation:
              'Calculando a média dos valores de B1 até B20 e colocando o resultado em B22.',
          },
          null,
          2
        );
      }

      if (lowercaseMessage.includes('gráfico') || lowercaseMessage.includes('grafico')) {
        return JSON.stringify(
          {
            operations: [
              {
                type: 'CHART',
                data: {
                  chartType: 'BAR',
                  dataRange: 'A1:B10',
                  title: 'Gráfico de barras',
                  hasLegend: true,
                },
              },
            ],
            explanation: 'Criando um gráfico de barras com os dados de A1 até B10.',
          },
          null,
          2
        );
      }

      if (lowercaseMessage.includes('filtro') || lowercaseMessage.includes('filtrar')) {
        return JSON.stringify(
          {
            operations: [
              {
                type: 'FILTER',
                data: {
                  range: 'A1:D20',
                  column: 'B',
                  condition: 'GREATER_THAN',
                  value: 100,
                },
              },
            ],
            explanation:
              'Aplicando filtro para mostrar apenas valores maiores que 100 na coluna B.',
          },
          null,
          2
        );
      }

      // Resposta padrão em formato JSON
      return JSON.stringify(
        {
          operations: [
            {
              type: 'OPERATION',
              data: {
                action: 'MOCK_RESPONSE',
                description: 'Esta é uma resposta simulada para desenvolvimento.',
              },
            },
          ],
          explanation: 'Resposta simulada para fins de desenvolvimento e teste.',
        },
        null,
        2
      );
    }

    // Resposta em texto simples se não requisitou JSON
    if (
      lowercaseMessage.includes('olá') ||
      lowercaseMessage.includes('ola') ||
      lowercaseMessage.includes('oi') ||
      lowercaseMessage.includes('hello')
    ) {
      return 'Olá! Sou o assistente Excel. Como posso ajudar você hoje?';
    }

    if (lowercaseMessage.includes('soma') || lowercaseMessage.includes('somar')) {
      return 'Para somar valores, você pode usar a função SUM(). Por exemplo, para somar os valores de A1 até A10, use =SUM(A1:A10).';
    }

    if (lowercaseMessage.includes('média') || lowercaseMessage.includes('media')) {
      return 'Para calcular a média, você pode usar a função AVERAGE(). Por exemplo, para calcular a média dos valores de B1 até B10, use =AVERAGE(B1:B10).';
    }

    if (lowercaseMessage.includes('gráfico') || lowercaseMessage.includes('grafico')) {
      return "Para criar um gráfico, selecione os dados que deseja incluir no gráfico e depois vá para a aba 'Inserir' e escolha o tipo de gráfico desejado, como gráfico de barras, linhas ou pizza.";
    }

    // Resposta padrão
    return (
      'Esta é uma resposta simulada para desenvolvimento e teste. Sua mensagem foi: ' + message
    );
  }

  /**
   * Retorna estatísticas do serviço para monitoramento
   */
  public getServiceStats(): {
    requestCount: number;
    errorCount: number;
    cacheHitCount: number;
    cacheSize: number;
    activeRequests: number;
  } {
    return {
      requestCount: this.requestCount,
      errorCount: this.errorCount,
      cacheHitCount: this.cacheHitCount,
      cacheSize: this.requestCache.size,
      activeRequests: this.requestsInProgress.size,
    };
  }

  /**
   * Cria um stream simulado para testes e desenvolvimento
   */
  private createMockStream(
    message: string,
    options: SendMessageOptions = {}
  ): ReadableStream<Uint8Array> {
    const encoder = new TextEncoder();
    const mockResponse = this.getMockResponse(message, options);

    // Dividir em chunks para simular streaming
    const chunks: string[] = [];
    const chunkSize = 10;
    for (let i = 0; i < mockResponse.length; i += chunkSize) {
      chunks.push(mockResponse.slice(i, i + chunkSize));
    }

    return new ReadableStream({
      async start(controller) {
        for (const chunk of chunks) {
          controller.enqueue(encoder.encode(chunk));
          // Simular delay
          await new Promise(resolve => setTimeout(resolve, 50));
        }
        controller.close();
      },
    });
  }

  /**
   * Cria um stream de resposta usando o Vertex AI
   */
  private async streamVertexAIMessage(
    message: string,
    history: Array<{ role: string; content: string }> = [],
    options: SendMessageOptions = {}
  ): Promise<ReadableStream<Uint8Array>> {
    // Implementação de streaming via Vertex AI
    const encoder = new TextEncoder();

    // Verificar se o modelo está disponível
    if (!this.vertexModel) {
      throw new GeminiServiceError(
        'Modelo Vertex AI não inicializado',
        GeminiErrorType.API_UNAVAILABLE
      );
    }

    try {
      const systemPrompt = options.systemPrompt || DEFAULT_EXCEL_SYSTEM_PROMPT;

      // Preparar contexto do Excel
      let excelContextText = '';
      if (options.excelContext) {
        const ctx = options.excelContext;
        excelContextText = `
Contexto atual do Excel:
${ctx.activeSheet ? `- Planilha ativa: ${ctx.activeSheet}` : ''}
${ctx.selection ? `- Seleção atual: ${ctx.selection}` : ''}
${ctx.headers && ctx.headers.length > 0 ? `- Cabeçalhos disponíveis: ${ctx.headers.join(', ')}` : ''}
${ctx.recentOperations && ctx.recentOperations.length > 0 ? `- Operações recentes: ${ctx.recentOperations.join(', ')}` : ''}
`;
      }

      // Converter o histórico para o formato do Vertex AI
      const contents = [
        {
          role: 'user',
          parts: [{ text: `${systemPrompt}\n\n${excelContextText}\n\n${message}` }],
        },
      ];

      // Adicionar o histórico de mensagens, se existir
      if (history && history.length > 0) {
        for (const item of history) {
          contents.push({
            role: item.role === 'user' ? 'user' : 'model',
            parts: [{ text: item.content }],
          });
        }
      }

      // Configurações para geração de conteúdo
      const generationConfig = {
        temperature: options.temperature ?? 0.4,
        topK: 40,
        topP: 0.95,
        maxOutputTokens: options.responseStructure?.preferConcise ? 1024 : 2048,
      };

      // Definir as configurações de segurança
      const safetySettings = [
        {
          category: 'HARM_CATEGORY_HARASSMENT',
          threshold: 'BLOCK_MEDIUM_AND_ABOVE',
        },
        {
          category: 'HARM_CATEGORY_HATE_SPEECH',
          threshold: 'BLOCK_MEDIUM_AND_ABOVE',
        },
        {
          category: 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
          threshold: 'BLOCK_MEDIUM_AND_ABOVE',
        },
        {
          category: 'HARM_CATEGORY_DANGEROUS_CONTENT',
          threshold: 'BLOCK_MEDIUM_AND_ABOVE',
        },
      ];

      // Iniciar o streaming
      logger.debug('Iniciando streaming com Vertex AI', { messageLength: message.length });
      const startTime = Date.now();

      // Capturar referência para o modelo para usar dentro da função start
      const vertexModel = this.vertexModel;

      // Criar stream para envio ao cliente
      return new ReadableStream({
        async start(controller) {
          try {
            // Criar a requisição para o Vertex AI
            const request = {
              contents,
              generationConfig,
              safetySettings,
            };

            // Fazer a chamada para streaming
            const streamingResult = await vertexModel.generateContentStream(request);

            // Processar cada parte do stream
            for await (const chunk of streamingResult.stream) {
              // Obter o texto da resposta
              const textChunk = chunk.candidates[0]?.content?.parts[0]?.text || '';
              if (textChunk) {
                // Enviar o chunk para o cliente
                controller.enqueue(encoder.encode(textChunk));
              }
            }

            // Calcular estatísticas
            const duration = Date.now() - startTime;
            logger.debug('Streaming do Vertex AI concluído', {
              duration,
              messageLength: message.length,
            });

            // Finalizar o stream
            controller.close();
          } catch (error: unknown) {
            logger.error('Erro durante streaming do Vertex AI', { error });

            // Enviar mensagem de erro
            controller.enqueue(
              encoder.encode(
                JSON.stringify({
                  error: 'Ocorreu um erro ao processar sua solicitação',
                  type: GeminiErrorType.UNKNOWN,
                })
              )
            );

            // Fechar o stream com erro
            controller.error(error);
          }
        },
      });
    } catch (error) {
      // Criar um stream com mensagem de erro
      return new ReadableStream({
        start(controller) {
          controller.enqueue(
            encoder.encode(
              JSON.stringify({
                error: 'Erro ao iniciar streaming com Vertex AI',
                message: error instanceof Error ? error.message : String(error),
                type: GeminiErrorType.API_UNAVAILABLE,
              })
            )
          );
          controller.close();
        },
      });
    }
  }
}
