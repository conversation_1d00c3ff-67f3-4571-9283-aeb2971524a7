'use client';

import React from 'react';

import { cn } from '@/lib/utils';

interface SpinnerProps extends React.HTMLAttributes<HTMLDivElement> {
  size?: 'sm' | 'md' | 'lg';
  variant?: 'primary' | 'secondary' | 'neutral';
}

/**
 * Componente Spinner para indicar carregamento
 */
export function Spinner({
  className,
  size = 'md',
  variant = 'primary',
  ...props
}: SpinnerProps): React.ReactNode {
  const sizeClasses = {
    sm: 'h-4 w-4 border-2',
    md: 'h-8 w-8 border-3',
    lg: 'h-12 w-12 border-4',
  };

  const variantClasses = {
    primary: 'border-primary',
    secondary: 'border-secondary',
    neutral: 'border-gray-300 dark:border-gray-700',
  };

  return (
    <div
      className={cn(
        'inline-block rounded-full border-t-transparent animate-spin',
        sizeClasses[size],
        variantClasses[variant],
        className
      )}
      {...props}
      role="status"
      aria-label="Carregando"
    />
  );
}

export default Spinner;
