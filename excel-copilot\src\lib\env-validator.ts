/**
 * Validador centralizado de variáveis de ambiente
 *
 * Este módulo fornece uma única fonte de verdade para validação
 * de variáveis de ambiente em toda a aplicação.
 */

import { ENV } from '../config/unified-environment';

import { logger } from './logger';

/**
 * Remove emojis de uma string para exibição em logs
 */
function removeEmojis(text: string): string {
  // Remove emojis específicos usados nas categorias
  return text.replace(/🔐|🗄️|💳|🤖|⚡|🚀|📋|🐙|📊|🏗️/g, '').trim();
}

// Exportar env para uso em toda a aplicação
export const env = {
  GOOGLE_CLIENT_ID: process.env.AUTH_GOOGLE_CLIENT_ID || '',
  GOOGLE_CLIENT_SECRET: process.env.AUTH_GOOGLE_CLIENT_SECRET || '',
  GITHUB_CLIENT_ID: process.env.AUTH_GITHUB_CLIENT_ID || '',
  GITHUB_CLIENT_SECRET: process.env.AUTH_GITHUB_CLIENT_SECRET || '',
  NEXTAUTH_SECRET: process.env.AUTH_NEXTAUTH_SECRET || '',
  NEXTAUTH_URL: process.env.AUTH_NEXTAUTH_URL || '',
  DATABASE_URL: process.env.DB_DATABASE_URL || '',
  VERTEX_AI_PROJECT_ID: process.env.AI_VERTEX_PROJECT_ID || '',
  VERTEX_AI_LOCATION: process.env.AI_VERTEX_LOCATION || '',
};

// Exportar uma versão compatível da configuração para uso em ambientes Edge
export function getEdgeConfig() {
  return {
    features: {
      useMockAI: ENV.FEATURES.USE_MOCK_AI,
      skipAuthProviders: process.env.AUTH_SKIP_PROVIDERS === 'true',
    },
  };
}

export interface EnvValidationResult {
  valid: boolean;
  missing: string[];
  details?: Record<string, unknown>;
}

// Lista centralizada de variáveis de ambiente obrigatórias
const REQUIRED_ENV_VARS = {
  all: ['DB_DATABASE_URL', 'AUTH_NEXTAUTH_SECRET', 'AUTH_NEXTAUTH_URL'],
  production: ['AUTH_GOOGLE_CLIENT_ID', 'AUTH_GOOGLE_CLIENT_SECRET', 'AI_VERTEX_PROJECT_ID'],
};

/**
 * Valida se todas as variáveis de ambiente obrigatórias estão definidas
 * @param strict Se true, valida também variáveis apenas obrigatórias em produção
 * @returns Resultado da validação
 */
export function validateEnvironment(strict = false): EnvValidationResult {
  const isProd = ENV.IS_PRODUCTION;
  const isVercel = process.env.VERCEL === '1';
  const disableValidation = process.env.DEV_DISABLE_VALIDATION === 'true';

  // Bypass para Vercel ou quando validação está explicitamente desativada
  if (isVercel || disableValidation) {
    logger.warn(
      'Validação de ambiente: Ignorando validações porque estamos em ambiente Vercel ou DISABLE_ENV_VALIDATION=true'
    );
    return {
      valid: true,
      missing: [],
      details: {
        environment: ENV.NODE_ENV,
        strict,
        checkedVars: 0,
        validationDisabled: true,
      },
    };
  }

  // PRODUÇÃO: Remover fallback para mock - sempre validar todas as variáveis

  // Determinar quais variáveis verificar baseado no ambiente
  const varsToCheck = [...REQUIRED_ENV_VARS.all];

  if (isProd || strict) {
    varsToCheck.push(...REQUIRED_ENV_VARS.production);
  }

  // Verificar quais variáveis estão faltando
  const missing = varsToCheck.filter(name => !process.env[name]);

  const result: EnvValidationResult = {
    valid: missing.length === 0,
    missing,
    details: {
      environment: ENV.NODE_ENV,
      strict,
      checkedVars: varsToCheck.length,
    },
  };

  // Log baseado no resultado - apenas no servidor
  if (!result.valid && typeof window === 'undefined') {
    if (isProd) {
      logger.error(`Variáveis de ambiente necessárias não configuradas: ${missing.join(', ')}`);
    } else {
      logger.warn(`Ambiente de desenvolvimento com variáveis ausentes: ${missing.join(', ')}`);
    }
  }

  return result;
}

/**
 * Verifica se o ambiente está configurado corretamente para o contexto atual
 * @param context Contexto da verificação para logs
 * @returns true se a configuração for válida, false caso contrário
 */
export function assertValidEnvironment(context = 'aplicação'): boolean {
  const validation = validateEnvironment();

  if (!validation.valid && ENV.IS_PRODUCTION) {
    logger.error(`Configuração inválida para ${context}: ${validation.missing.join(', ')}`);
    return false;
  }

  return true;
}

// Tipos para validação
type EnvVarConfig = {
  required: boolean;
  validate?: (value: string) => boolean;
  errorMessage?: string;
  production?: boolean; // Indica se é obrigatório apenas em produção
};

// Configuração das variáveis críticas com validações robustas
const ENV_CONFIG: Record<string, EnvVarConfig> = {
  // Banco de dados
  DATABASE_URL: {
    required: true,
    validate: value => {
      if (!value) return false;
      const validPrefixes = ['mysql://', 'postgresql://', 'postgres://'];
      return validPrefixes.some(prefix => value.startsWith(prefix));
    },
    errorMessage:
      '🔴 DATABASE_URL deve começar com mysql://, postgresql:// ou postgres:// e conter credenciais válidas',
  },

  // NextAuth - Configurações críticas de segurança
  NEXTAUTH_SECRET: {
    required: true,
    validate: value => {
      if (!value) return false;
      if (value.length < 32) return false;
      // Verifica se não é um valor padrão inseguro
      const unsafeDefaults = ['your-secret-here', 'change-me', 'secret', 'nextauth-secret'];
      return !unsafeDefaults.includes(value.toLowerCase());
    },
    errorMessage:
      '🔴 NEXTAUTH_SECRET deve ter pelo menos 32 caracteres e não pode ser um valor padrão inseguro',
  },
  NEXTAUTH_URL: {
    required: true,
    validate: value => {
      if (!value) return false;
      try {
        const url = new URL(value);
        // Em produção, não deve ser localhost
        if (process.env.NODE_ENV === 'production' && url.hostname === 'localhost') {
          return false;
        }
        return url.protocol === 'http:' || url.protocol === 'https:';
      } catch {
        return false;
      }
    },
    errorMessage: '🔴 NEXTAUTH_URL deve ser uma URL válida (https:// em produção, não localhost)',
  },

  // OAuth Providers - Obrigatórios em produção
  GOOGLE_CLIENT_ID: {
    required: false,
    production: true,
    validate: value => !!value && value.endsWith('.apps.googleusercontent.com'),
    errorMessage: '🔴 GOOGLE_CLIENT_ID deve terminar com .apps.googleusercontent.com',
  },
  GOOGLE_CLIENT_SECRET: {
    required: false,
    production: true,
    validate: value => !!value && value.startsWith('GOCSPX-') && value.length > 20,
    errorMessage: '🔴 GOOGLE_CLIENT_SECRET deve começar com GOCSPX- e ter mais de 20 caracteres',
  },
  GITHUB_CLIENT_ID: {
    required: false,
    production: true,
    validate: value => !!value && /^[a-f0-9]{20}$/.test(value),
    errorMessage: '🔴 GITHUB_CLIENT_ID deve ser um hash hexadecimal de 20 caracteres',
  },
  GITHUB_CLIENT_SECRET: {
    required: false,
    production: true,
    validate: value => !!value && /^[a-f0-9]{40}$/.test(value),
    errorMessage: '🔴 GITHUB_CLIENT_SECRET deve ser um hash hexadecimal de 40 caracteres',
  },

  // Vertex AI
  VERTEX_AI_PROJECT_ID: {
    required: false,
    validate: value => !value || /^[a-z][-a-z0-9]{4,28}[a-z0-9]$/.test(value),
    errorMessage:
      '🔴 VERTEX_AI_PROJECT_ID deve seguir o formato do Google Cloud (6-30 chars, lowercase, hífens)',
  },
  VERTEX_AI_LOCATION: {
    required: false,
    validate: value => !value || /^[a-z]+-[a-z]+\d+$/.test(value),
    errorMessage:
      '🔴 VERTEX_AI_LOCATION deve ser uma região válida do Google Cloud (ex: us-central1)',
  },

  // Stripe - Validações rigorosas para pagamentos
  STRIPE_SECRET_KEY: {
    required: false,
    production: true,
    validate: value => {
      if (!value) return false;
      // Permite chaves de teste em build local ou desenvolvimento
      const isLocalBuild = process.env.BUILD_MODE === 'local' || process.env.VERCEL !== '1';
      const isDevelopment = process.env.NODE_ENV === 'development';

      if (process.env.NODE_ENV === 'production' && !isLocalBuild && !isDevelopment) {
        // Em produção real, exige chave live
        if (!value.startsWith('sk_live_')) {
          return false;
        }
      }
      return value.startsWith('sk_') && value.length > 20;
    },
    errorMessage:
      '🔴 STRIPE_SECRET_KEY deve começar com sk_live_ em produção e ter mais de 20 caracteres',
  },
  NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY: {
    required: false,
    production: true,
    validate: value => {
      if (!value) return false;
      // Permite chaves de teste em build local ou desenvolvimento
      const isLocalBuild = process.env.BUILD_MODE === 'local' || process.env.VERCEL !== '1';
      const isDevelopment = process.env.NODE_ENV === 'development';

      if (process.env.NODE_ENV === 'production' && !isLocalBuild && !isDevelopment) {
        // Em produção real, exige chave live
        if (!value.startsWith('pk_live_')) {
          return false;
        }
      }
      return value.startsWith('pk_') && value.length > 20;
    },
    errorMessage: '🔴 NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY deve começar com pk_live_ em produção',
  },

  // Supabase - Validações para banco e storage
  NEXT_PUBLIC_SUPABASE_URL: {
    required: false,
    production: true,
    validate: value => {
      if (!value) return false;
      try {
        const url = new URL(value);
        return url.hostname.endsWith('.supabase.co') && url.protocol === 'https:';
      } catch {
        return false;
      }
    },
    errorMessage:
      '🔴 NEXT_PUBLIC_SUPABASE_URL deve ser uma URL válida do Supabase (https://*.supabase.co)',
  },
  NEXT_PUBLIC_SUPABASE_ANON_KEY: {
    required: false,
    production: true,
    validate: value => !!value && value.startsWith('eyJ') && value.length > 100,
    errorMessage: '🔴 NEXT_PUBLIC_SUPABASE_ANON_KEY deve ser um JWT válido (começa com eyJ)',
  },

  // Redis - Validações para cache
  UPSTASH_REDIS_REST_URL: {
    required: false,
    validate: value => !value || (value.startsWith('https://') && value.includes('.upstash.io')),
    errorMessage: '🔴 UPSTASH_REDIS_REST_URL deve ser uma URL válida do Upstash',
  },
  UPSTASH_REDIS_REST_TOKEN: {
    required: false,
    validate: value => !value || (value.length > 20 && /^[A-Za-z0-9]+$/.test(value)),
    errorMessage: '🔴 UPSTASH_REDIS_REST_TOKEN deve ser um token alfanumérico válido',
  },
};

/**
 * Valida as variáveis de ambiente críticas em runtime
 * @returns {boolean} Se as variáveis são válidas
 */
export function validateCriticalEnvVars(): boolean {
  // No cliente, sempre retornar true para evitar validações desnecessárias
  if (typeof window !== 'undefined') {
    return true;
  }
  const errors: string[] = [];
  // Determinar se estamos em produção considerando tanto NODE_ENV quanto NEXT_PUBLIC_FORCE_PRODUCTION
  const forceProduction = process.env.DEV_FORCE_PRODUCTION === 'true';
  const isProduction = forceProduction || process.env.NODE_ENV === 'production';
  const isVercelBuild = process.env.VERCEL === '1';

  // Detectar se estamos em build time local (produção sem plataforma de deploy)
  const isBuildTime =
    isProduction &&
    !isVercelBuild &&
    !process.env.RAILWAY &&
    !process.env.HEROKU &&
    !process.env.NETLIFY;

  // Se estiver em build da Vercel, ignorar validações críticas
  if (isVercelBuild) {
    logger.warn('Ambiente Vercel detectado. Ignorando validações críticas para permitir build.');
    return true;
  }

  // Checar uso do arquivo de credenciais
  const usesCredentialsFile =
    process.env.GOOGLE_APPLICATION_CREDENTIALS || process.env.VERTEX_AI_CREDENTIALS_PATH;

  // Verificar variáveis necessárias
  const envVars = process.env;

  for (const [key, config] of Object.entries(ENV_CONFIG)) {
    // Se for obrigatório apenas em produção e não estamos em produção, ignorar
    if (config.production && !isProduction) {
      continue;
    }

    // Se a variável é obrigatória mas estamos usando mocks, ignorar algumas
    if (key.startsWith('VERTEX_AI_') && ENV.FEATURES.USE_MOCK_AI) {
      continue;
    }

    // Se é variável do Vertex AI mas estamos usando arquivo de credenciais, ignorar
    if (key === 'AI_VERTEX_PROJECT_ID' && usesCredentialsFile) {
      continue;
    }

    // Verificar se existe quando obrigatória
    if (config.required && (!envVars[key] || envVars[key] === '')) {
      errors.push(`Variável de ambiente ${key} é obrigatória mas não está definida`);
      continue;
    }

    // Se tem valor e tem validador, validar
    if (envVars[key] && config.validate && !config.validate(envVars[key])) {
      errors.push(config.errorMessage || `Variável de ambiente ${key} tem valor inválido`);
    }
  }

  // Lógica de dependências
  if (ENV.VERTEX_AI.ENABLED && !ENV.FEATURES.USE_MOCK_AI) {
    if (
      !usesCredentialsFile &&
      (!process.env.AI_VERTEX_PROJECT_ID || !process.env.AI_VERTEX_LOCATION)
    ) {
      errors.push(
        'VERTEX_AI_PROJECT_ID e VERTEX_AI_LOCATION são obrigatórios quando VERTEX_AI_ENABLED=true e USE_MOCK_AI=false'
      );
    }
  }

  // Se estamos em produção REAL (não build local)
  if (isProduction && !isBuildTime) {
    // Mock AI deve estar desativado em produção real
    if (ENV.FEATURES.USE_MOCK_AI) {
      errors.push('USE_MOCK_AI deve ser false em ambiente de produção');
    }

    // Em produção real, NEXTAUTH_URL não deve apontar para localhost
    if (process.env.AUTH_NEXTAUTH_URL && process.env.AUTH_NEXTAUTH_URL.includes('localhost')) {
      errors.push('NEXTAUTH_URL não deve apontar para localhost em ambiente de produção');
    }
  }

  // Em build local, apenas avisar sobre configurações de desenvolvimento
  if (isProduction && isBuildTime) {
    if (process.env.AUTH_NEXTAUTH_URL && process.env.AUTH_NEXTAUTH_URL.includes('localhost')) {
      logger.warn(
        '⚠️ Build local detectado com NEXTAUTH_URL=localhost - configure para produção real'
      );
    }
    if (ENV.FEATURES.USE_MOCK_AI) {
      logger.warn('⚠️ Build local detectado com USE_MOCK_AI=true - configure para produção real');
    }
  }

  // Exibir erros e interromper em produção
  if (errors.length > 0) {
    logger.error('Problemas com variáveis de ambiente críticas:');
    errors.forEach(error => logger.error(`- ${error}`));
    logger.error('Verifique suas configurações e tente novamente.');

    // Em produção REAL (não build local), devemos falhar o startup
    if (isProduction && !isVercelBuild && !isBuildTime) {
      throw new Error('Variáveis de ambiente inválidas em produção');
    }

    // Durante build local, apenas avisar mas não falhar
    if (isBuildTime) {
      logger.warn('⚠️ Build local com configurações de desenvolvimento - OK para testes');
      return true; // Permitir build local mesmo com "erros"
    }

    return false;
  }

  return true;
}

/**
 * Categorias de variáveis de ambiente para logging detalhado
 */
const ENV_CATEGORIES = {
  BASIC: {
    name: '🏗️ Configurações Básicas',
    vars: ['NODE_ENV', 'APP_NAME', 'APP_VERSION', 'APP_URL'],
    required: true,
  },
  AUTH: {
    name: '🔐 Autenticação (NextAuth)',
    vars: [
      'AUTH_NEXTAUTH_SECRET',
      'AUTH_NEXTAUTH_URL',
      'AUTH_GOOGLE_CLIENT_ID',
      'AUTH_GOOGLE_CLIENT_SECRET',
      'AUTH_GITHUB_CLIENT_ID',
      'AUTH_GITHUB_CLIENT_SECRET',
    ],
    required: true,
  },
  DATABASE: {
    name: '🗄️ Banco de Dados',
    vars: [
      'DB_DATABASE_URL',
      'DB_DIRECT_URL',
      'SUPABASE_URL',
      'SUPABASE_ANON_KEY',
      'SUPABASE_SERVICE_ROLE_KEY',
    ],
    required: true,
  },
  PAYMENTS: {
    name: '💳 Pagamentos (Stripe)',
    vars: ['STRIPE_SECRET_KEY', 'NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY', 'STRIPE_WEBHOOK_SECRET'],
    required: true,
  },
  AI: {
    name: '🤖 Inteligência Artificial',
    vars: [
      'AI_ENABLED',
      'AI_USE_MOCK',
      'AI_VERTEX_PROJECT_ID',
      'AI_VERTEX_LOCATION',
      'AI_VERTEX_MODEL',
    ],
    required: false,
  },
  CACHE: {
    name: '⚡ Cache (Redis)',
    vars: ['UPSTASH_REDIS_REST_URL', 'UPSTASH_REDIS_REST_TOKEN'],
    required: false,
  },
  MCP_VERCEL: {
    name: '🚀 MCP Vercel',
    vars: ['MCP_VERCEL_TOKEN', 'MCP_VERCEL_PROJECT_ID', 'MCP_VERCEL_TEAM_ID'],
    required: false,
  },
  MCP_LINEAR: {
    name: '📋 MCP Linear',
    vars: ['MCP_LINEAR_API_KEY'],
    required: false,
  },
  MCP_GITHUB: {
    name: '🐙 MCP GitHub',
    vars: ['MCP_GITHUB_TOKEN'],
    required: false,
  },
  MONITORING: {
    name: '📊 Monitoramento (Sentry)',
    vars: ['SENTRY_DSN', 'SENTRY_ORG', 'SENTRY_PROJECT'],
    required: false,
  },
};

/**
 * Verifica o status de uma categoria de variáveis
 */
function checkCategoryStatus(category: (typeof ENV_CATEGORIES)[keyof typeof ENV_CATEGORIES]) {
  const status = {
    name: category.name,
    configured: 0,
    total: category.vars.length,
    missing: [] as string[],
    present: [] as string[],
    isComplete: false,
    isRequired: category.required,
  };

  category.vars.forEach(varName => {
    const value = process.env[varName];
    if (value && value.trim() !== '') {
      status.configured++;
      status.present.push(varName);
    } else {
      status.missing.push(varName);
    }
  });

  status.isComplete = status.configured === status.total;
  return status;
}

/**
 * Gera logs detalhados das variáveis de ambiente
 */
/* eslint-disable no-console */
function logDetailedEnvironmentStatus(): void {
  const isDev = ENV.NODE_ENV === 'development';
  const isProd = ENV.NODE_ENV === 'production';

  // Cabeçalho da seção
  console.log('\n==================================================');
  console.log('📋 VERIFICAÇÃO DE VARIÁVEIS DE AMBIENTE');
  console.log('==================================================');

  // Informações básicas do ambiente
  console.log(
    `\n🚀 [${new Date().toLocaleTimeString('pt-BR')}] Iniciando validação de configuração`
  );
  console.log(`   Ambiente: ${ENV.NODE_ENV}`);
  console.log(`   URL da Aplicação: ${process.env.APP_URL || 'não definida'}`);
  console.log(`   Modo Build: ${process.env.BUILD_MODE || 'padrão'}`);
  console.log(`   Vercel: ${process.env.VERCEL === '1' ? 'Sim' : 'Não'}`);

  const categoryStatuses = Object.values(ENV_CATEGORIES).map(checkCategoryStatus);

  // Log de cada categoria com formatação melhorada
  categoryStatuses.forEach((status, index) => {
    const percentage = Math.round((status.configured / status.total) * 100);
    const statusIcon = status.isComplete ? '✅' : status.isRequired ? '❌' : '⚠️';
    const statusText = status.isComplete
      ? 'COMPLETA'
      : status.isRequired
        ? 'INCOMPLETA'
        : 'PARCIAL';

    console.log(
      `${statusIcon} ${status.name}: ${status.configured}/${status.total} (${percentage}%) - ${statusText}`
    );

    // Detalhes das variáveis ausentes em desenvolvimento
    if (isDev && status.missing.length > 0) {
      console.log(`   Ausentes: ${status.missing.join(', ')}`);
    }

    // Informações específicas importantes
    if (status.name.includes('Autenticação') && status.present.length > 0) {
      const hasGoogle = status.present.some(v => v.includes('GOOGLE'));
      const hasGitHub = status.present.some(v => v.includes('GITHUB'));
      console.log(`✅    OAuth Providers: CONFIGURADO`);
      console.log(`   ${hasGoogle ? 'Google ✅' : 'Google ❌'}`);
      console.log(`   ${hasGitHub ? 'GitHub ✅' : 'GitHub ❌'}`);
    }

    if (status.name.includes('Inteligência Artificial') && status.present.length > 0) {
      const aiEnabled = process.env.AI_ENABLED === 'true';
      const useMock = process.env.AI_USE_MOCK === 'true';
      const aiModel = process.env.AI_VERTEX_MODEL || 'não definido';

      console.log(`✅    Configuração IA: ${aiEnabled ? 'HABILITADA' : 'DESABILITADA'}`);
      console.log(`   Modo: ${useMock ? 'Mock/Simulação' : 'Produção/Real'}`);
      console.log(`   Modelo: ${aiModel}`);
      console.log(`   Projeto: ${process.env.AI_VERTEX_PROJECT_ID || 'não definido'}`);
    }

    if (status.name.includes('MCP') && status.present.length > 0) {
      const integrationName = status.name.replace(/🚀 MCP |📋 MCP |🐙 MCP /g, '');
      console.log(`✅    ${integrationName} Integration: ATIVA`);
      console.log(`   Variáveis configuradas: ${status.configured}/${status.total}`);
    }

    // Progresso visual
    const progressBar =
      '█'.repeat(Math.floor(percentage / 5)) + '░'.repeat(20 - Math.floor(percentage / 5));
    console.log(
      `[${progressBar}] ${Math.round(((index + 1) / categoryStatuses.length) * 100)}% - ${removeEmojis(status.name)}`
    );
  });

  // Resumo geral com formatação melhorada
  const totalRequired = categoryStatuses.filter(s => s.isRequired);
  const completeRequired = totalRequired.filter(s => s.isComplete);
  const totalOptional = categoryStatuses.filter(s => !s.isRequired);
  const completeOptional = totalOptional.filter(s => s.isComplete);

  console.log('\n==================================================');
  console.log('📋 RESUMO DA VALIDAÇÃO');
  console.log('==================================================');

  // Status final
  const allRequiredComplete = completeRequired.length === totalRequired.length;
  if (allRequiredComplete) {
    console.log('\n✅ SUCESSO: Todas as configurações obrigatórias estão completas!');
    logger.info('Variaveis de ambiente validadas com sucesso');

    if (completeOptional.length > 0) {
      console.log(`✅ Integrações Bonus: ATIVAS`);
      console.log(`   ${completeOptional.length} integrações opcionais funcionando`);
      console.log(`   MCPs, monitoramento e cache configurados`);
      logger.info(`Bonus: ${completeOptional.length} integracoes opcionais ativas!`);
    }
  } else {
    const missingRequired = totalRequired.filter(s => !s.isComplete);
    const missingNames = missingRequired.map(s => removeEmojis(s.name));

    if (isProd) {
      console.log(`\n🔴 ERRO: Configuração incompleta para produção: ${missingNames.join(', ')}`);
      logger.error(`❌ Configuração incompleta para produção: ${missingNames.join(', ')}`);
    } else {
      console.log(`✅ Status de Desenvolvimento: PARCIAL`);
      console.log(`   Configurações ausentes: ${missingNames.join(', ')}`);
      console.log(`   Aplicação funcionará com funcionalidades limitadas`);
      logger.warn(
        `⚠️ Algumas configurações obrigatórias ausentes (modo desenvolvimento): ${missingNames.join(', ')}`
      );
    }
  }

  // Log final com informações úteis
  console.log(`✅ Informações de Debug: DISPONÍVEL`);
  console.log(
    `   Configurações Obrigatórias: ${completeRequired.length}/${totalRequired.length} ✅`
  );
  console.log(`   Integrações Opcionais: ${completeOptional.length}/${totalOptional.length} 🎯`);
  console.log(`   Total de Categorias: ${categoryStatuses.length}`);
  console.log(
    `   Percentual Geral: ${Math.round(((completeRequired.length + completeOptional.length) / categoryStatuses.length) * 100)}%`
  );
}
/* eslint-enable no-console */

/**
 * Inicia a validação automaticamente em importação
 * Deve ser importado durante inicialização da aplicação
 */
export function initEnvValidation(): void {
  // Não iniciar validação no cliente
  if (typeof window !== 'undefined') {
    return;
  }

  try {
    // Log detalhado das variáveis de ambiente
    logDetailedEnvironmentStatus();

    // Validação crítica para produção
    const isValid = validateCriticalEnvVars();

    if (!isValid && ENV.IS_PRODUCTION) {
      logger.error('🔴 Falha crítica na validação de variáveis de ambiente');
      throw new Error('Configuração de ambiente inválida para produção');
    }
  } catch (error) {
    logger.error('🔴 Falha crítica na validação de variáveis de ambiente');
    throw error;
  }
}
