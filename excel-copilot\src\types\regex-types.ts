// Tipos para expressões regulares usadas nas operações
export type RegexPattern = string | RegExp;

// Padrões de expressão regular comuns
export const CELL_REFERENCE_REGEX = /\$?[A-Za-z]+\$?[0-9]+/;
export const RANGE_REFERENCE_REGEX = /\$?[A-Za-z]+\$?[0-9]+:\$?[A-Za-z]+\$?[0-9]+/;
export const FORMULA_REGEX = /^=/;
export const FUNCTION_REGEX = /[A-Z0-9.]+\(/i;

// Interface para resultados de correspondência de regex
export interface RegexMatchResult {
  match: string;
  index: number;
  groups?: Record<string, string>;
  safeGroup?: (index: number) => string;
  hasGroup?: (index: number) => boolean;
}

// Tipos para funções de utilidade regex
export type RegexMatcher = (text: string, pattern: RegexPattern) => RegexMatchResult[] | null;
export type RegexReplacer = (text: string, pattern: RegexPattern, replacement: string) => string;
export type RegexExtractor = (
  match: RegExpMatchArray | null,
  groupIndex: number,
  defaultValue?: string
) => string;
export type RegexTester = (pattern: RegExp, text: string) => boolean;

// Tipos para validação baseada em regex
export interface RegexValidator {
  pattern: RegexPattern;
  message: string;
  test: (value: string) => boolean;
}
