/**
 * Sistema unificado de animações para o Excel Copilot
 * ---------------------------------------------------------
 * Este arquivo centraliza todas as animações da aplicação para garantir consistência
 * entre diferentes componentes e interações. Usar este sistema em vez de
 * definir animações inline em cada componente.
 *
 * Como usar:
 * 1. Importe as variantes ou a função getAnimationProps
 * 2. Aplique as variantes em componentes Framer Motion
 *
 * Exemplo:
 * ```tsx
 * import { motion } from "framer-motion";
 * import { getAnimationProps } from "@/lib/animations";
 *
 * function MyComponent() {
 *   return <motion.div {...getAnimationProps('fadeIn')}>Conteúdo animado</motion.div>
 * }
 * ```
 */

import { MotionProps } from 'framer-motion';

// Constantes para animações
export const ANIMATION_DURATION = 0.5;
export const ANIMATION_EASE = [0.23, 0.1, 0.36, 1];

// Definição de variantes de animação disponíveis
export type AnimationVariant = 'fadeIn' | 'slideIn' | 'list' | 'listItem' | 'card' | 'page';

// Variantes específicas para cards interativos
export const cardVariants = {
  hover: {
    y: -10,
    scale: 1.02,
    boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
  },
  tap: {
    scale: 0.98,
  },
};

// Variantes de animação pré-definidas
const VARIANTS: Record<AnimationVariant, MotionProps> = {
  // Fade in básico
  fadeIn: {
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    exit: { opacity: 0 },
    transition: {
      duration: ANIMATION_DURATION,
      ease: ANIMATION_EASE,
    },
  },

  // Slide para cima com fade
  slideIn: {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: 10 },
    transition: {
      duration: ANIMATION_DURATION,
      ease: ANIMATION_EASE,
    },
  },

  // Container para animações em sequência (stagger)
  list: {
    initial: {},
    animate: {
      transition: {
        staggerChildren: 0.1,
      },
    },
  },

  // Item dentro de um container stagger
  listItem: {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    exit: { opacity: 0 },
    transition: {
      duration: ANIMATION_DURATION,
      ease: ANIMATION_EASE,
    },
  },

  // Animação para cards interativos
  card: {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    exit: { opacity: 0 },
    transition: {
      duration: ANIMATION_DURATION,
      ease: ANIMATION_EASE,
    },
  },

  // Transição de página
  page: {
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    exit: { opacity: 0 },
    transition: {
      duration: 0.3,
      ease: ANIMATION_EASE,
    },
  },
};

/**
 * Retorna props de animação para usar com componentes motion
 * @param variant Variante de animação a ser usada
 * @returns Props do Framer Motion configuradas
 */
export function getAnimationProps(variant: AnimationVariant): MotionProps {
  return VARIANTS[variant];
}
