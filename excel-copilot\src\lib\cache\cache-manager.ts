import { logger } from '@/lib/logger';

import { redisClient } from './redis-client';

/**
 * TTL padrão para diferentes tipos de cache (em segundos)
 */
export const CACHE_TTL = {
  DASHBOARD_METRICS: 5 * 60, // 5 minutos
  WORKBOOK_METADATA: 15 * 60, // 15 minutos
  AI_COMMAND_RESULTS: 60 * 60, // 1 hora
  USER_SESSION: 30 * 60, // 30 minutos
  TEMPLATES: 24 * 60 * 60, // 24 horas
  POPULAR_COMMANDS: 2 * 60 * 60, // 2 horas
} as const;

/**
 * Prefixos para organizar chaves do cache
 */
export const CACHE_KEYS = {
  DASHBOARD_METRICS: (userId: string) => `dashboard:metrics:${userId}`,
  WORKBOOK_LIST: (userId: string) => `workbooks:list:${userId}`,
  WORKBOOK_METADATA: (workbookId: string) => `workbook:meta:${workbookId}`,
  AI_COMMAND: (commandHash: string) => `ai:command:${commandHash}`,
  USER_PERMISSIONS: (userId: string, workbookId: string) => `permissions:${userId}:${workbookId}`,
  RECENT_ACTIVITY: (userId: string) => `activity:recent:${userId}`,
  TEMPLATES: () => 'templates:popular',
  POPULAR_AI_COMMANDS: () => 'ai:commands:popular',
  USER_SESSION_DATA: (userId: string) => `session:${userId}`,
} as const;

/**
 * Interface para métricas de cache
 */
interface CacheMetrics {
  hits: number;
  misses: number;
  sets: number;
  deletes: number;
  errors: number;
}

/**
 * Gerenciador de cache tipado com métricas
 */
class CacheManager {
  private metrics: CacheMetrics = {
    hits: 0,
    misses: 0,
    sets: 0,
    deletes: 0,
    errors: 0,
  };

  /**
   * Busca um valor do cache com parsing automático
   */
  async get<T>(key: string): Promise<T | null> {
    try {
      const value = await redisClient.get(key);
      
      if (value === null) {
        this.metrics.misses++;
        return null;
      }

      this.metrics.hits++;
      return JSON.parse(value) as T;
    } catch (error) {
      this.metrics.errors++;
      logger.error('💥 Erro ao buscar do cache:', error, { key });
      return null;
    }
  }

  /**
   * Salva um valor no cache com serialização automática
   */
  async set<T>(key: string, value: T, ttlSeconds?: number): Promise<boolean> {
    try {
      const serialized = JSON.stringify(value);
      const success = await redisClient.set(key, serialized, ttlSeconds);
      
      if (success) {
        this.metrics.sets++;
      } else {
        this.metrics.errors++;
      }
      
      return success;
    } catch (error) {
      this.metrics.errors++;
      logger.error('💥 Erro ao salvar no cache:', error, { key });
      return false;
    }
  }

  /**
   * Remove um valor do cache
   */
  async delete(key: string): Promise<boolean> {
    try {
      const success = await redisClient.del(key);
      
      if (success) {
        this.metrics.deletes++;
      }
      
      return success;
    } catch (error) {
      this.metrics.errors++;
      logger.error('💥 Erro ao deletar do cache:', error, { key });
      return false;
    }
  }

  /**
   * Verifica se uma chave existe no cache
   */
  async exists(key: string): Promise<boolean> {
    try {
      return await redisClient.exists(key);
    } catch (error) {
      this.metrics.errors++;
      logger.error('💥 Erro ao verificar existência no cache:', error, { key });
      return false;
    }
  }

  /**
   * Busca ou executa uma função e cacheia o resultado
   */
  async getOrSet<T>(
    key: string,
    fetchFunction: () => Promise<T>,
    ttlSeconds?: number
  ): Promise<T | null> {
    // Tentar buscar do cache primeiro
    const cached = await this.get<T>(key);
    if (cached !== null) {
      return cached;
    }

    try {
      // Executar função para obter dados
      const data = await fetchFunction();
      
      // Cachear resultado
      await this.set(key, data, ttlSeconds);
      
      return data;
    } catch (error) {
      logger.error('💥 Erro ao executar função para cache:', error, { key });
      return null;
    }
  }

  /**
   * Invalida cache por padrão (para colaboração em tempo real)
   */
  async invalidatePattern(pattern: string): Promise<number> {
    try {
      const deletedCount = await redisClient.flushPattern(pattern);
      this.metrics.deletes += deletedCount;
      
      logger.info('🧹 Cache invalidado por padrão:', pattern, `${deletedCount} chaves`);
      return deletedCount;
    } catch (error) {
      this.metrics.errors++;
      logger.error('💥 Erro ao invalidar padrão do cache:', error, { pattern });
      return 0;
    }
  }

  /**
   * Invalida cache relacionado a um usuário
   */
  async invalidateUserCache(userId: string): Promise<void> {
    const patterns = [
      `dashboard:metrics:${userId}`,
      `workbooks:list:${userId}`,
      `activity:recent:${userId}`,
      `session:${userId}`,
      `permissions:${userId}:*`,
    ];

    await Promise.all(
      patterns.map(pattern => this.invalidatePattern(pattern))
    );

    logger.info('👤 Cache do usuário invalidado:', userId);
  }

  /**
   * Invalida cache relacionado a um workbook
   */
  async invalidateWorkbookCache(workbookId: string): Promise<void> {
    const patterns = [
      `workbook:meta:${workbookId}`,
      `permissions:*:${workbookId}`,
    ];

    await Promise.all(
      patterns.map(pattern => this.invalidatePattern(pattern))
    );

    logger.info('📊 Cache do workbook invalidado:', workbookId);
  }

  /**
   * Pré-aquecimento de cache para templates populares
   */
  async warmupPopularTemplates(): Promise<void> {
    try {
      logger.info('🔥 Iniciando pré-aquecimento de templates populares...');
      
      // Simular dados de templates populares (em produção, buscar do banco)
      const popularTemplates = [
        { id: 'financial', name: 'Controle Financeiro', usage: 1250 },
        { id: 'dashboard', name: 'Dashboard', usage: 980 },
        { id: 'calculator', name: 'Calculadora', usage: 750 },
      ];

      await this.set(
        CACHE_KEYS.TEMPLATES(),
        popularTemplates,
        CACHE_TTL.TEMPLATES
      );

      logger.info('✅ Templates populares pré-aquecidos');
    } catch (error) {
      logger.error('💥 Erro no pré-aquecimento de templates:', error);
    }
  }

  /**
   * Pré-aquecimento de comandos de IA populares
   */
  async warmupPopularAICommands(): Promise<void> {
    try {
      logger.info('🔥 Iniciando pré-aquecimento de comandos de IA populares...');
      
      const popularCommands = [
        'Criar planilha de controle financeiro com categorias',
        'Adicionar gráfico de barras para vendas mensais',
        'Calcular soma total da coluna',
        'Criar tabela dinâmica com dados',
        'Formatar células como moeda',
      ];

      await this.set(
        CACHE_KEYS.POPULAR_AI_COMMANDS(),
        popularCommands,
        CACHE_TTL.POPULAR_COMMANDS
      );

      logger.info('✅ Comandos de IA populares pré-aquecidos');
    } catch (error) {
      logger.error('💥 Erro no pré-aquecimento de comandos de IA:', error);
    }
  }

  /**
   * Executa pré-aquecimento completo do cache
   */
  async warmupCache(): Promise<void> {
    logger.info('🚀 Iniciando pré-aquecimento completo do cache...');
    
    await Promise.all([
      this.warmupPopularTemplates(),
      this.warmupPopularAICommands(),
    ]);

    logger.info('🎉 Pré-aquecimento do cache concluído');
  }

  /**
   * Obtém métricas do cache
   */
  getMetrics(): CacheMetrics & { hitRate: number } {
    const total = this.metrics.hits + this.metrics.misses;
    const hitRate = total > 0 ? (this.metrics.hits / total) * 100 : 0;

    return {
      ...this.metrics,
      hitRate: Math.round(hitRate * 100) / 100,
    };
  }

  /**
   * Reseta métricas do cache
   */
  resetMetrics(): void {
    this.metrics = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
      errors: 0,
    };
  }

  /**
   * Obtém estatísticas detalhadas do Redis
   */
  async getDetailedStats(): Promise<any> {
    const redisStats = await redisClient.getStats();
    const cacheMetrics = this.getMetrics();

    return {
      redis: redisStats,
      cache: cacheMetrics,
      timestamp: new Date().toISOString(),
    };
  }
}

export const cacheManager = new CacheManager();
