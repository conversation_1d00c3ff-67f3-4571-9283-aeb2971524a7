import { Users, Wifi, WifiOff } from 'lucide-react';
import React from 'react';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { useUserPresence } from '@/hooks/useWorkbookRealtime';

interface OnlineUsersProps {
  workbookId: string | null;
  className?: string;
}

export function OnlineUsers({ workbookId, className = '' }: OnlineUsersProps) {
  const { onlineUsers, isConnected, onlineCount } = useUserPresence(workbookId);

  if (!workbookId) {
    return null;
  }

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      {/* Indicador de conexão */}
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <div className="flex items-center space-x-1">
              {isConnected ? (
                <Wifi className="h-4 w-4 text-green-500" />
              ) : (
                <WifiOff className="h-4 w-4 text-red-500" />
              )}
              <Badge variant={isConnected ? 'default' : 'destructive'} className="text-xs">
                {isConnected ? 'Conectado' : 'Desconectado'}
              </Badge>
            </div>
          </TooltipTrigger>
          <TooltipContent>
            <p>{isConnected ? 'Conectado ao Real-time' : 'Desconectado do Real-time'}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>

      {/* Contador de usuários online */}
      {onlineCount > 0 && (
        <div className="flex items-center space-x-1">
          <Users className="h-4 w-4 text-gray-500" />
          <span className="text-sm text-gray-600">{onlineCount} online</span>
        </div>
      )}

      {/* Lista de avatares dos usuários online */}
      <div className="flex -space-x-2">
        {onlineUsers
          .filter(user => user.isOnline)
          .slice(0, 5) // Mostrar apenas os primeiros 5
          .map(user => (
            <TooltipProvider key={user.userId}>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="relative">
                    <Avatar className="h-8 w-8 border-2 border-white">
                      <AvatarImage
                        src={`https://api.dicebear.com/7.x/initials/svg?seed=${user.userName}`}
                        alt={user.userName}
                      />
                      <AvatarFallback className="text-xs">
                        {user.userName.substring(0, 2).toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    {/* Indicador de status online */}
                    <div className="absolute -bottom-0.5 -right-0.5 h-3 w-3 rounded-full bg-green-500 border-2 border-white" />
                  </div>
                </TooltipTrigger>
                <TooltipContent>
                  <div className="text-center">
                    <p className="font-medium">{user.userName}</p>
                    <p className="text-xs text-gray-500">Online agora</p>
                    {user.cursor && (
                      <p className="text-xs text-blue-500">Editando: {user.cursor.cellAddress}</p>
                    )}
                  </div>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          ))}
      </div>

      {/* Indicador de mais usuários se houver */}
      {onlineCount > 5 && (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="flex items-center justify-center h-8 w-8 rounded-full bg-gray-200 border-2 border-white text-xs font-medium text-gray-600">
                +{onlineCount - 5}
              </div>
            </TooltipTrigger>
            <TooltipContent>
              <p>Mais {onlineCount - 5} usuários online</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      )}
    </div>
  );
}

/**
 * Componente compacto para mostrar apenas o contador
 */
export function OnlineUsersCompact({ workbookId, className = '' }: OnlineUsersProps) {
  const { onlineCount, isConnected } = useUserPresence(workbookId);

  if (!workbookId || onlineCount === 0) {
    return null;
  }

  return (
    <div className={`flex items-center space-x-1 ${className}`}>
      <div className={`h-2 w-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
      <span className="text-sm text-gray-600">{onlineCount} online</span>
    </div>
  );
}

/**
 * Componente detalhado para sidebar
 */
export function OnlineUsersList({ workbookId, className = '' }: OnlineUsersProps) {
  const { onlineUsers, isConnected } = useUserPresence(workbookId);

  if (!workbookId) {
    return null;
  }

  const onlineUsersList = onlineUsers.filter(user => user.isOnline);
  const offlineUsersList = onlineUsers.filter(user => !user.isOnline);

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Status de conexão */}
      <div className="flex items-center space-x-2">
        {isConnected ? (
          <Wifi className="h-4 w-4 text-green-500" />
        ) : (
          <WifiOff className="h-4 w-4 text-red-500" />
        )}
        <span className="text-sm font-medium">
          {isConnected ? 'Colaboração Ativa' : 'Modo Offline'}
        </span>
      </div>

      {/* Usuários online */}
      {onlineUsersList.length > 0 && (
        <div>
          <h4 className="text-sm font-medium text-gray-900 mb-2">
            Online ({onlineUsersList.length})
          </h4>
          <div className="space-y-2">
            {onlineUsersList.map(user => (
              <div key={user.userId} className="flex items-center space-x-3">
                <div className="relative">
                  <Avatar className="h-6 w-6">
                    <AvatarImage
                      src={`https://api.dicebear.com/7.x/initials/svg?seed=${user.userName}`}
                      alt={user.userName}
                    />
                    <AvatarFallback className="text-xs">
                      {user.userName.substring(0, 2).toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  <div className="absolute -bottom-0.5 -right-0.5 h-2 w-2 rounded-full bg-green-500 border border-white" />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">{user.userName}</p>
                  {user.cursor && (
                    <p className="text-xs text-blue-500">Editando {user.cursor.cellAddress}</p>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Usuários offline recentes */}
      {offlineUsersList.length > 0 && (
        <div>
          <h4 className="text-sm font-medium text-gray-500 mb-2">Recentemente Online</h4>
          <div className="space-y-2">
            {offlineUsersList.slice(0, 3).map(user => (
              <div key={user.userId} className="flex items-center space-x-3 opacity-60">
                <Avatar className="h-6 w-6">
                  <AvatarImage
                    src={`https://api.dicebear.com/7.x/initials/svg?seed=${user.userName}`}
                    alt={user.userName}
                  />
                  <AvatarFallback className="text-xs">
                    {user.userName.substring(0, 2).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1 min-w-0">
                  <p className="text-sm text-gray-600 truncate">{user.userName}</p>
                  <p className="text-xs text-gray-400">
                    Visto {new Date(user.lastSeen).toLocaleTimeString()}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Estado vazio */}
      {onlineUsersList.length === 0 && offlineUsersList.length === 0 && (
        <div className="text-center py-4">
          <Users className="h-8 w-8 text-gray-300 mx-auto mb-2" />
          <p className="text-sm text-gray-500">Nenhum colaborador ativo</p>
        </div>
      )}
    </div>
  );
}
