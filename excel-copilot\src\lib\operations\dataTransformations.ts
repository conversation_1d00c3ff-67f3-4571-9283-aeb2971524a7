import { extractGroup } from '@/utils/regex-utils';

import { ExcelOperation } from '../excel';

/**
 * Tipos de operações de filtro
 */
export type FilterOperator =
  | 'EQUALS'
  | 'GREATER_THAN'
  | 'LESS_THAN'
  | 'CONTAINS'
  | 'BETWEEN'
  | 'NOT_EQUALS';

/**
 * Interface para dados de operação de filtro
 */
export interface FilterOperationData {
  column: string;
  operator: FilterOperator;
  value: any;
  value2?: any; // Para operações BETWEEN
}

/**
 * Interface para dados de operação de ordenação
 */
export interface SortOperationData {
  column: string;
  direction: 'ASC' | 'DESC';
}

/**
 * Executa uma operação de filtro
 * @param sheetData Dados da planilha
 * @param operation Operação a ser executada
 * @returns Dados atualizados e resumo da operação
 */
export async function executeFilterOperation(
  sheetData: any,
  operation: ExcelOperation
): Promise<{ updatedData: any; resultSummary: string }> {
  try {
    const { column, operator, value, value2 } = operation.data as FilterOperationData;

    // Criar cópia dos dados
    const updatedData = { ...sheetData };

    // Verificar se estamos trabalhando com dados em formato de linhas/colunas
    if (!updatedData.rows || !updatedData.headers) {
      throw new Error('Formato de dados não suportado para operações de filtro');
    }

    // Encontrar o índice da coluna
    let colIndex = -1;

    if (/^[A-Z]+$/.test(column)) {
      // Converter letra de coluna para índice
      let index = 0;
      for (let i = 0; i < column.length; i++) {
        index = index * 26 + (column.charCodeAt(i) - 65);
      }
      colIndex = index;
    } else {
      // Procurar pelo nome da coluna nos headers
      colIndex = updatedData.headers.findIndex(
        (header: string) => header.toLowerCase() === column.toLowerCase()
      );
    }

    if (colIndex === -1 || colIndex >= updatedData.headers.length) {
      throw new Error(`Coluna '${column}' não encontrada`);
    }

    // Aplicar filtro
    const filteredRows = updatedData.rows.filter((row: any[]) => {
      const cellValue = row[colIndex];
      const rowValue =
        typeof cellValue === 'object' && cellValue !== null
          ? cellValue.result || cellValue.display || cellValue.value
          : cellValue;

      switch (operator) {
        case 'EQUALS':
          return rowValue == value;
        case 'NOT_EQUALS':
          return rowValue != value;
        case 'GREATER_THAN':
          return Number(rowValue) > Number(value);
        case 'LESS_THAN':
          return Number(rowValue) < Number(value);
        case 'CONTAINS':
          return String(rowValue).toLowerCase().includes(String(value).toLowerCase());
        case 'BETWEEN':
          return Number(rowValue) >= Number(value) && Number(rowValue) <= Number(value2);
        default:
          return true;
      }
    });

    // Atualizar dados
    updatedData.rows = filteredRows;
    updatedData.filtered = true;
    updatedData.filterCriteria = {
      column: updatedData.headers[colIndex],
      operator,
      value,
      value2,
    };

    // Mapeamento de operadores para texto
    const operatorText = {
      EQUALS: 'igual a',
      NOT_EQUALS: 'diferente de',
      GREATER_THAN: 'maior que',
      LESS_THAN: 'menor que',
      CONTAINS: 'contém',
      BETWEEN: 'entre',
    };

    const resultSummary = `Filtrada coluna ${updatedData.headers[colIndex]} ${operatorText[operator]} ${value}${operator === 'BETWEEN' ? ` e ${value2}` : ''}. ${filteredRows.length} linha(s) encontrada(s)`;

    return {
      updatedData,
      resultSummary,
    };
  } catch (error: unknown) {
    console.error('Erro ao executar operação de filtro:', error);
    throw new Error(
      `Falha ao aplicar filtro: ${error instanceof Error ? error.message : String(error)}`
    );
  }
}

/**
 * Executa uma operação de ordenação
 * @param sheetData Dados da planilha
 * @param operation Operação a ser executada
 * @returns Dados atualizados e resumo da operação
 */
export async function executeSortOperation(
  sheetData: any,
  operation: ExcelOperation
): Promise<{ updatedData: any; resultSummary: string }> {
  try {
    const { column, direction } = operation.data as SortOperationData;

    // Criar cópia dos dados
    const updatedData = { ...sheetData };

    // Verificar se estamos trabalhando com dados em formato de linhas/colunas
    if (!updatedData.rows || !updatedData.headers) {
      throw new Error('Formato de dados não suportado para operações de ordenação');
    }

    // Encontrar o índice da coluna
    let colIndex = -1;

    if (/^[A-Z]+$/.test(column)) {
      // Converter letra de coluna para índice
      let index = 0;
      for (let i = 0; i < column.length; i++) {
        index = index * 26 + (column.charCodeAt(i) - 65);
      }
      colIndex = index;
    } else {
      // Procurar pelo nome da coluna nos headers
      colIndex = updatedData.headers.findIndex(
        (header: string) => header.toLowerCase() === column.toLowerCase()
      );
    }

    if (colIndex === -1 || colIndex >= updatedData.headers.length) {
      throw new Error(`Coluna '${column}' não encontrada`);
    }

    // Ordenar as linhas
    updatedData.rows.sort((a: any[], b: any[]) => {
      const aValue = a[colIndex];
      const bValue = b[colIndex];

      const aCompare =
        typeof aValue === 'object' && aValue !== null
          ? aValue.result || aValue.display || aValue.value
          : aValue;
      const bCompare =
        typeof bValue === 'object' && bValue !== null
          ? bValue.result || bValue.display || bValue.value
          : bValue;

      // Determinar se os valores são numéricos
      const aNum = Number(aCompare);
      const bNum = Number(bCompare);
      const isNumeric = !isNaN(aNum) && !isNaN(bNum);

      // Comparar de acordo com o tipo
      let comparison;
      if (isNumeric) {
        comparison = aNum - bNum;
      } else {
        comparison = String(aCompare).localeCompare(String(bCompare));
      }

      // Aplicar direção de ordenação
      return direction === 'ASC' ? comparison : -comparison;
    });

    // Atualizar metadados
    updatedData.sorted = true;
    updatedData.sortCriteria = {
      column: updatedData.headers[colIndex],
      direction,
    };

    const directionText = direction === 'ASC' ? 'crescente' : 'decrescente';
    const resultSummary = `Ordenada coluna ${updatedData.headers[colIndex]} em ordem ${directionText}`;

    return {
      updatedData,
      resultSummary,
    };
  } catch (error: unknown) {
    console.error('Erro ao executar operação de ordenação:', error);
    throw new Error(
      `Falha ao ordenar dados: ${error instanceof Error ? error.message : String(error)}`
    );
  }
}

/**
 * Extrai operações de filtro de um texto
 * @param text Texto a ser analisado
 * @returns Array de operações de filtro encontradas
 */
export function extractFilterOperations(text: string): ExcelOperation[] {
  const operations: ExcelOperation[] = [];

  // Padrões para detectar comandos de filtro
  const patterns = [
    // Filtro "maior que"
    {
      regex:
        /filtr[eo]\s+a\s+coluna\s+([A-Za-z0-9_\s]+)\s+(?:onde|para|com)\s+(?:valores?\s+)?(?:seja|sejam|for|forem)?\s*>\s*([0-9.,]+)/gi,
      operator: 'GREATER_THAN' as FilterOperator,
    },
    // Filtro "maior que" alternativo
    {
      regex:
        /filtr[eo]\s+a\s+coluna\s+([A-Za-z0-9_\s]+)\s+(?:onde|para|com)\s+(?:valores?\s+)?(?:maior(?:es)?\s+(?:que|do\s+que))\s+([0-9.,]+)/gi,
      operator: 'GREATER_THAN' as FilterOperator,
    },
    // Filtro "menor que"
    {
      regex:
        /filtr[eo]\s+a\s+coluna\s+([A-Za-z0-9_\s]+)\s+(?:onde|para|com)\s+(?:valores?\s+)?(?:seja|sejam|for|forem)?\s*<\s*([0-9.,]+)/gi,
      operator: 'LESS_THAN' as FilterOperator,
    },
    // Filtro "menor que" alternativo
    {
      regex:
        /filtr[eo]\s+a\s+coluna\s+([A-Za-z0-9_\s]+)\s+(?:onde|para|com)\s+(?:valores?\s+)?(?:menor(?:es)?\s+(?:que|do\s+que))\s+([0-9.,]+)/gi,
      operator: 'LESS_THAN' as FilterOperator,
    },
    // Filtro "igual a"
    {
      regex:
        /filtr[eo]\s+a\s+coluna\s+([A-Za-z0-9_\s]+)\s+(?:onde|para|com)\s+(?:valores?\s+)?(?:seja|sejam|for|forem)?\s*(?:=|igual\s+a)\s*['"]?([^'"]+)['"]?/gi,
      operator: 'EQUALS' as FilterOperator,
    },
    // Filtro "contém"
    {
      regex:
        /filtr[eo]\s+a\s+coluna\s+([A-Za-z0-9_\s]+)\s+(?:onde|para|com)\s+(?:valores?\s+)?(?:contenha|contém|contem|contenha[m])\s+['"]?([^'"]+)['"]?/gi,
      operator: 'CONTAINS' as FilterOperator,
    },
    // Filtro "entre"
    {
      regex:
        /filtr[eo]\s+a\s+coluna\s+([A-Za-z0-9_\s]+)\s+(?:onde|para|com)\s+(?:valores?\s+)?(?:entre|esteja[m]?\s+entre)\s+([0-9.,]+)\s+e\s+([0-9.,]+)/gi,
      operator: 'BETWEEN' as FilterOperator,
    },
  ];

  // Iterar por cada padrão e extrair correspondências
  patterns.forEach(({ regex, operator }) => {
    let match;
    while ((match = regex.exec(text)) !== null) {
      const column = extractGroup(match, 1, '');
      const value = extractGroup(match, 2, '').replace(/['"]/g, '');
      const value2 = operator === 'BETWEEN' ? extractGroup(match, 3, '') : undefined;

      // Converter valores para número se aplicável
      const processedValue = !isNaN(Number(value.replace(',', '.')))
        ? Number(value.replace(',', '.'))
        : value;

      const processedValue2 =
        value2 && !isNaN(Number(value2.replace(',', '.')))
          ? Number(value2.replace(',', '.'))
          : value2;

      // Adicionar a operação detectada
      operations.push({
        type: 'FILTER',
        data: {
          column,
          operator,
          value: processedValue,
          value2: processedValue2,
        },
      });
    }
  });

  return operations;
}

/**
 * Extrai operações de ordenação de um texto
 * @param text Texto a ser analisado
 * @returns Array de operações de ordenação encontradas
 */
export function extractSortOperations(text: string): ExcelOperation[] {
  const operations: ExcelOperation[] = [];

  // Padrões para detectar comandos de ordenação
  const patterns = [
    // Ordenação crescente
    {
      regex:
        /orden[ea]\s+a\s+coluna\s+([A-Za-z0-9_\s]+)\s+(?:em\s+ordem\s+)?(crescente|ascendente|alfabética)/gi,
      direction: 'ASC' as 'ASC' | 'DESC',
    },
    // Ordenação decrescente
    {
      regex:
        /orden[ea]\s+a\s+coluna\s+([A-Za-z0-9_\s]+)\s+(?:em\s+ordem\s+)?(decrescente|descendente)/gi,
      direction: 'DESC' as 'ASC' | 'DESC',
    },
  ];

  // Iterar por cada padrão e extrair correspondências
  patterns.forEach(({ regex, direction }) => {
    let match;
    while ((match = regex.exec(text)) !== null) {
      const column = extractGroup(match, 1, '');

      // Adicionar a operação detectada
      operations.push({
        type: 'SORT',
        data: {
          column,
          direction,
        },
      });
    }
  });

  return operations;
}
