'use client';

import { ReactNode } from 'react';

import { useReducedMotion } from '@/hooks/useReducedMotion';
import { cn } from '@/lib/utils';

export type AnimationType =
  | 'fade-in'
  | 'fade-in-up'
  | 'fade-in-down'
  | 'fade-in-left'
  | 'fade-in-right'
  | 'scale-in'
  | 'scale-in-up'
  | 'bounce-in'
  | 'spin-in';

export interface AnimationWrapperProps {
  /** Conteúdo a ser renderizado com animação */
  children: ReactNode;
  /** Tipo de animação a ser aplicada */
  animation: AnimationType;
  /** Atraso antes de iniciar a animação (em ms) */
  delay?: 'none' | 'short' | 'medium' | 'long';
  /** Duração da animação */
  duration?: 'fast' | 'normal' | 'slow';
  /** Classes CSS adicionais */
  className?: string;
  /** Se a animação deve iniciar automaticamente - padrão true */
  autoPlay?: boolean;
  /** Se a animação deve repetir - padr<PERSON> false */
  repeat?: boolean;
}

/**
 * Componente que envolve o conteúdo com animações acessíveis,
 * respeitando as preferências de movimento reduzido do usuário.
 */
export function AnimationWrapper({
  children,
  animation,
  delay = 'none',
  duration = 'normal',
  className,
  autoPlay = true,
  repeat = false,
}: AnimationWrapperProps) {
  const prefersReducedMotion = useReducedMotion();

  // Se o usuário prefere movimento reduzido, renderiza sem animação
  if (prefersReducedMotion) {
    return <div className={className}>{children}</div>;
  }

  // Mapeamento de tipos de animação para classes Tailwind
  const animationClasses: Record<AnimationType, string> = {
    'fade-in': 'animate-fade-in',
    'fade-in-up': 'animate-fade-in-up',
    'fade-in-down': 'animate-fade-in-down',
    'fade-in-left': 'animate-fade-in-left',
    'fade-in-right': 'animate-fade-in-right',
    'scale-in': 'animate-scale-in',
    'scale-in-up': 'animate-scale-in-up',
    'bounce-in': 'animate-bounce-in',
    'spin-in': 'animate-spin-in',
  };

  // Mapeamento de atrasos para classes
  const delayClasses: Record<NonNullable<AnimationWrapperProps['delay']>, string> = {
    none: '',
    short: 'animation-delay-200',
    medium: 'animation-delay-500',
    long: 'animation-delay-1000',
  };

  // Mapeamento de durações para classes
  const durationClasses: Record<NonNullable<AnimationWrapperProps['duration']>, string> = {
    fast: 'animation-duration-300',
    normal: 'animation-duration-500',
    slow: 'animation-duration-1000',
  };

  // Classes para controle de reprodução e repetição
  const controlClasses = {
    'animation-paused': !autoPlay,
    'animation-running': autoPlay,
    'animation-repeat': repeat,
    'animation-once': !repeat,
    'animation-fill-forwards': true,
  };

  return (
    <div
      className={cn(
        animationClasses[animation],
        delayClasses[delay],
        durationClasses[duration],
        controlClasses,
        className
      )}
      style={{
        // Fallback inline styles para navegadores que não suportam as classes
        animationPlayState: !autoPlay ? 'paused' : 'running',
        animationIterationCount: repeat ? 'infinite' : '1',
        animationFillMode: 'forwards',
      }}
    >
      {children}
    </div>
  );
}
