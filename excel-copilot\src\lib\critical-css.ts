/**
 * CSS crítico para carregamento rápido
 * Contém apenas os estilos essenciais para o First Contentful Paint (FCP)
 * Extração manual de estilos críticos do globals.css
 */
export const criticalCSS = `
  :root {
    --background: 0 0% 98%;
    --foreground: 222 47% 11%;
    --primary: 125 55% 43%;
    --primary-foreground: 0 0% 100%;
    --secondary: 210 14% 97%;
    --secondary-foreground: 222 47% 11%;
    --border: 214.3 32% 88%;
    --radius: 0.375rem;
  }
  
  .dark {
    --background: 222 47% 6%;
    --foreground: 210 40% 98%;
    --primary: 125 55% 50%;
    --primary-foreground: 0 0% 100%;
    --secondary: 217 33% 17%;
    --secondary-foreground: 210 40% 98%;
    --border: 217 33% 20%;
  }
  
  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
    margin: 0;
    padding: 0;
    font-family: var(--font-inter, system-ui, sans-serif);
  }
  
  .bg-background {
    background-color: hsl(var(--background));
  }
  
  .text-foreground {
    color: hsl(var(--foreground));
  }
  
  /* Estilos essenciais de layout */
  .min-h-screen {
    min-height: 100vh;
  }
  
  .flex {
    display: flex;
  }
  
  .flex-col {
    flex-direction: column;
  }
  
  .flex-1 {
    flex: 1;
  }
  
  .relative {
    position: relative;
  }
  
  .antialiased {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  
  /* Prevenir FOUC durante carregamento */
  html.transitioning-theme * {
    transition: none !important;
  }

  /* Animação de loading essencial */
  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }
  
  .animate-spin {
    animation: spin 1s linear infinite;
  }
  
  /* Melhorias de acessibilidade */
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
  }
`;

/**
 * Ordem de carregamento de estilos:
 * 1. CSS crítico em linha (layout.tsx)
 * 2. globals.css via importação (com prefetch)
 * 3. Componentes com CSS em JS (via Tailwind)
 */
