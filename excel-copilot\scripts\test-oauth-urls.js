#!/usr/bin/env node

/**
 * Script para testar URLs de OAuth e identificar problemas de configuração
 */

const https = require('https');
const { URL } = require('url');

// URLs base do projeto
const PRODUCTION_URL = 'https://excel-copilot-eight.vercel.app';
const CALLBACK_PATHS = {
  google: '/api/auth/callback/google',
  github: '/api/auth/callback/github',
};

// Função para fazer requisições HTTP
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port || 443,
      path: urlObj.pathname + urlObj.search,
      method: options.method || 'GET',
      headers: {
        'User-Agent': 'OAuth-Test-Script/1.0',
        ...options.headers,
      },
    };

    const req = https.request(requestOptions, res => {
      let data = '';
      res.on('data', chunk => (data += chunk));
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          body: data,
          url: url,
        });
      });
    });

    req.on('error', reject);
    req.setTimeout(10000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    if (options.body) {
      req.write(options.body);
    }
    req.end();
  });
}

// Função para testar URLs de callback
async function testCallbackUrls() {
  console.log('🔍 Testando URLs de callback OAuth...\n');

  for (const [provider, path] of Object.entries(CALLBACK_PATHS)) {
    const callbackUrl = `${PRODUCTION_URL}${path}`;
    console.log(`📍 Testando ${provider}: ${callbackUrl}`);

    try {
      const response = await makeRequest(callbackUrl);
      console.log(`   Status: ${response.statusCode}`);

      if (response.statusCode === 400) {
        console.log('   ✅ Callback URL acessível (400 é esperado sem parâmetros OAuth)');
      } else if (response.statusCode === 404) {
        console.log('   ❌ Callback URL não encontrada (404)');
      } else if (response.statusCode === 500) {
        console.log('   ⚠️  Erro interno do servidor (500)');
      } else {
        console.log(`   ℹ️  Status inesperado: ${response.statusCode}`);
      }

      // Verificar headers importantes
      if (response.headers['content-type']) {
        console.log(`   Content-Type: ${response.headers['content-type']}`);
      }
    } catch (error) {
      console.log(`   ❌ Erro ao acessar: ${error.message}`);
    }
    console.log('');
  }
}

// Função para testar endpoints de autenticação
async function testAuthEndpoints() {
  console.log('🔍 Testando endpoints de autenticação...\n');

  const endpoints = [
    '/api/auth/session',
    '/api/auth/csrf',
    '/api/auth/providers',
    '/api/auth/signin',
    '/api/auth/debug',
  ];

  for (const endpoint of endpoints) {
    const url = `${PRODUCTION_URL}${endpoint}`;
    console.log(`📍 Testando: ${endpoint}`);

    try {
      const response = await makeRequest(url);
      console.log(`   Status: ${response.statusCode}`);

      if (response.statusCode === 200) {
        console.log('   ✅ Endpoint funcionando');

        // Para alguns endpoints, mostrar parte da resposta
        if (endpoint === '/api/auth/providers' || endpoint === '/api/auth/debug') {
          try {
            const data = JSON.parse(response.body);
            if (endpoint === '/api/auth/providers') {
              const providerNames = Object.keys(data);
              console.log(`   Providers: ${providerNames.join(', ')}`);
            } else if (endpoint === '/api/auth/debug') {
              console.log(`   Ready: ${data.ready}`);
              console.log(`   Issues: ${data.issues.length}`);
            }
          } catch (e) {
            // Não é JSON válido, tudo bem
          }
        }
      } else {
        console.log(`   ⚠️  Status: ${response.statusCode}`);
      }
    } catch (error) {
      console.log(`   ❌ Erro: ${error.message}`);
    }
    console.log('');
  }
}

// Função para verificar configurações OAuth
async function checkOAuthConfig() {
  console.log('🔍 Verificando configurações OAuth...\n');

  try {
    const debugResponse = await makeRequest(`${PRODUCTION_URL}/api/auth/debug`);

    if (debugResponse.statusCode === 200) {
      const debugData = JSON.parse(debugResponse.body);

      console.log('📊 Configurações encontradas:');
      console.log(
        `   Google Client ID: ${debugData.authConfig.GOOGLE_CLIENT_ID ? '✅ Configurado' : '❌ Não configurado'}`
      );
      console.log(
        `   Google Client Secret: ${debugData.authConfig.GOOGLE_CLIENT_SECRET ? '✅ Configurado' : '❌ Não configurado'}`
      );
      console.log(
        `   GitHub Client ID: ${debugData.authConfig.GITHUB_CLIENT_ID ? '✅ Configurado' : '❌ Não configurado'}`
      );
      console.log(
        `   GitHub Client Secret: ${debugData.authConfig.GITHUB_CLIENT_SECRET ? '✅ Configurado' : '❌ Não configurado'}`
      );
      console.log(
        `   NextAuth Secret: ${debugData.authConfig.NEXTAUTH_SECRET ? '✅ Configurado' : '❌ Não configurado'}`
      );
      console.log(`   NextAuth URL: ${debugData.authConfig.NEXTAUTH_URL}`);
      console.log(`   Environment: ${debugData.environment}`);
      console.log(`   Ready: ${debugData.ready ? '✅ Sim' : '❌ Não'}`);

      if (debugData.issues.length > 0) {
        console.log('\n⚠️  Problemas encontrados:');
        debugData.issues.forEach(issue => console.log(`   - ${issue}`));
      }
    } else {
      console.log('❌ Não foi possível acessar o endpoint de debug');
    }
  } catch (error) {
    console.log(`❌ Erro ao verificar configurações: ${error.message}`);
  }
  console.log('');
}

// Função principal
async function main() {
  console.log('🚀 Iniciando testes de OAuth para Excel Copilot\n');
  console.log(`🌐 URL de produção: ${PRODUCTION_URL}\n`);

  await checkOAuthConfig();
  await testAuthEndpoints();
  await testCallbackUrls();

  console.log('📋 URLs que devem estar configuradas nos provedores OAuth:');
  console.log(`   Google Console: ${PRODUCTION_URL}${CALLBACK_PATHS.google}`);
  console.log(`   GitHub App: ${PRODUCTION_URL}${CALLBACK_PATHS.github}`);
  console.log('\n✅ Testes concluídos!');
}

// Executar se chamado diretamente
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { testCallbackUrls, testAuthEndpoints, checkOAuthConfig };
