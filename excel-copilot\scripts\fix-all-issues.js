/**
 * Script mestre para corrigir todos os problemas identificados no projeto Excel Copilot
 * Este script executa todas as correções em sequência conforme o plano definido
 */
const { execSync } = require('child_process');
const readline = require('readline');

// Lista de scripts a serem executados em ordem
const scripts = [
  {
    name: 'Correção de Linting e Formatação',
    command: 'npm run lint:fix:wizard',
    description: 'Corrige problemas de linting em fases incrementais',
  },
  {
    name: 'Unificação de Definições de Tipos Excel',
    command: 'npm run fix:excel-type-unify',
    description: 'Unifica as definições de tipos incompatíveis de ExcelOperationType',
  },
  {
    name: 'Correção de Tipos de Operação Excel',
    command: 'npm run fix:excel-types',
    description: 'Corrige problemas de tipagem nas operações Excel',
  },
  {
    name: 'Correção de Await em Testes',
    command: 'npm run fix:missing-await',
    description: 'Adiciona await em chamadas de Promise nos testes',
  },
  {
    name: 'Correção de Outros Problemas de Tipo',
    command: 'npm run fix:types',
    description: 'Corrige outros problemas de tipagem, incluindo null checks',
  },
  {
    name: 'Verificação de Erros Restantes',
    command: 'npm run typecheck',
    description: 'Verifica se ainda existem erros de tipagem',
  },
  {
    name: 'Execução de Testes Simples',
    command: 'npm run test:simple',
    description: 'Executa testes básicos para verificar se as correções funcionaram',
  },
];

// Criar interface para perguntas ao usuário
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
});

/**
 * Executa um comando com try/catch para não interromper o fluxo em caso de erro
 * @param {string} command Comando a ser executado
 * @param {boolean} continueOnError Se deve continuar mesmo se houver erro
 * @returns {boolean} True se executou com sucesso
 */
function executeCommand(command, continueOnError = true) {
  try {
    console.log(`\n🚀 Executando: ${command}`);
    execSync(command, { stdio: 'inherit' });
    console.log('✅ Comando executado com sucesso!');
    return true;
  } catch (error) {
    console.log(`\n⚠️ Erro ao executar comando: ${command}`);
    console.log('📋 Detalhes do erro:');
    console.log(error.message);

    if (!continueOnError) {
      console.log('❌ Interrompendo execução devido ao erro.');
      return false;
    }

    console.log('⚠️ Continuando com o próximo comando...');
    return false;
  }
}

/**
 * Pergunta ao usuário se deseja continuar após um erro
 * @returns {Promise<boolean>} True se o usuário deseja continuar
 */
function askToContinue() {
  return new Promise(resolve => {
    rl.question('\n⚠️ Deseja continuar com o próximo script? (s/N): ', answer => {
      resolve(answer.toLowerCase() === 's');
    });
  });
}

/**
 * Função principal que executa todos os scripts em sequência
 */
async function main() {
  console.log('🔧 INICIANDO CORREÇÃO COMPLETA DO PROJETO EXCEL COPILOT 🔧');
  console.log('Este script executará todas as correções em sequência conforme o plano definido');
  console.log('=================================================================================');

  for (let i = 0; i < scripts.length; i++) {
    const { name, command, description } = scripts[i];

    console.log(`\n\n📌 ETAPA ${i + 1}/${scripts.length}: ${name}`);
    console.log(`💡 ${description}`);
    console.log('----------------------------------------------------------------------');

    const success = executeCommand(command, false);

    if (!success) {
      const shouldContinue = await askToContinue();
      if (!shouldContinue) {
        console.log('\n❌ Execução interrompida pelo usuário.');
        break;
      }
    }
  }

  console.log(
    '\n\n================================================================================='
  );
  console.log('✨ PROCESSO DE CORREÇÃO FINALIZADO ✨');
  console.log('Verifique os resultados e execute testes adicionais conforme necessário.');
  console.log('Sugestões para próximos passos:');
  console.log('1. Execute npm run test para executar todos os testes');
  console.log('2. Execute npm run lint para verificar problemas de linting restantes');
  console.log('3. Verifique manualmente arquivos que ainda apresentem erros');

  rl.close();
}

// Executar script principal
main().catch(error => {
  console.error('❌ Erro fatal durante execução:', error);
  process.exit(1);
});
