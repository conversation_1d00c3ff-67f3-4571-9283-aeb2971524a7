import { defineConfig, devices } from '@playwright/test';

/**
 * Read environment variables from file.
 * https://github.com/motdotla/dotenv
 */
// require('dotenv').config();

/**
 * @see https://playwright.dev/docs/test-configuration
 */
export default defineConfig({
  testDir: './__tests__/e2e',

  /* Timeout global para testes */
  timeout: 60 * 1000,

  expect: {
    timeout: 10000,
    toHaveScreenshot: {
      threshold: 0.2,
      mode: 'strict',
    },
    toMatchSnapshot: {
      threshold: 0.2,
    },
  },

  /* Executar testes em paralelo */
  fullyParallel: true,

  /* Falhar build se houver testes sem retry */
  forbidOnly: !!process.env.CI,

  /* Retry em CI apenas */
  retries: process.env.CI ? 2 : 0,

  /* Opt out de parallel tests em CI */
  workers: process.env.CI ? 1 : undefined,

  /* Reporter para usar localmente e em CI */
  reporter: [
    ['html'],
    ['json', { outputFile: 'test-results/results.json' }],
    ['junit', { outputFile: 'test-results/results.xml' }],
    ...(process.env.CI ? [['github']] : [['list']]),
  ],
  /* Configuração global para todos os testes */
  use: {
    /* URL base para usar em actions como `await page.goto('/')` */
    baseURL: process.env.PLAYWRIGHT_TEST_BASE_URL || 'http://localhost:3000',

    /* Coletar trace em retry de testes falhados */
    trace: 'on-first-retry',

    /* Screenshots apenas em falhas */
    screenshot: 'only-on-failure',

    /* Vídeos apenas em falhas */
    video: 'retain-on-failure',

    /* Timeout para actions */
    actionTimeout: 10000,

    /* Timeout para navegação */
    navigationTimeout: 30000,
  },

  /* Configure projects for major browsers */
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },

    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] },
    },

    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] },
    },

    /* Test against mobile viewports. */
    {
      name: 'Mobile Chrome',
      use: { ...devices['Pixel 5'] },
    },
    {
      name: 'Mobile Safari',
      use: { ...devices['iPhone 12'] },
    },
  ],

  /* Run your local dev server before starting the tests */
  webServer: {
    command: 'npm run dev',
    url: 'http://localhost:3000',
    reuseExistingServer: !process.env.CI,
    stdout: 'pipe',
    stderr: 'pipe',
  },
});
