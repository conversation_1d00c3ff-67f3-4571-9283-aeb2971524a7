# 🚀 Configuração de Produção - Excel Copilot

## 📋 **Checklist de Deploy**

### ✅ **1. Variáveis de Ambiente Obrigatórias**

Configure as seguintes variáveis na Vercel:

```bash
# NextAuth Configuration
NEXTAUTH_SECRET="[GERAR_CHAVE_SEGURA_32_CHARS]"
NEXTAUTH_URL="https://excel-copilot-eight.vercel.app"

# Google OAuth
GOOGLE_CLIENT_ID="[OBTER_DO_GOOGLE_CONSOLE]"
GOOGLE_CLIENT_SECRET="[OBTER_DO_GOOGLE_CONSOLE]"

# GitHub OAuth
GITHUB_CLIENT_ID="[OBTER_DO_GITHUB_SETTINGS]"
GITHUB_CLIENT_SECRET="[OBTER_DO_GITHUB_SETTINGS]"

# Database
DATABASE_URL="[SUA_DATABASE_URL_SUPABASE]"
DIRECT_URL="[SUA_DIRECT_URL_SUPABASE]"

# Supabase
NEXT_PUBLIC_SUPABASE_URL="[SUA_SUPABASE_URL]"
NEXT_PUBLIC_SUPABASE_ANON_KEY="[SUA_SUPABASE_ANON_KEY]"
SUPABASE_SERVICE_ROLE_KEY="[SUA_SUPABASE_SERVICE_ROLE_KEY]"

# Environment
NODE_ENV="production"
```

### ✅ **2. Configuração OAuth Providers**

#### **Google OAuth Console:**

1. Acesse: https://console.developers.google.com/
2. Vá para "Credentials" > "OAuth 2.0 Client IDs"
3. Configure:
   - **Authorized JavaScript origins:** `https://excel-copilot-eight.vercel.app`
   - **Authorized redirect URIs:** `https://excel-copilot-eight.vercel.app/api/auth/callback/google`

#### **GitHub OAuth App:**

1. Acesse: https://github.com/settings/developers
2. Clique em "New OAuth App"
3. Configure:
   - **Application name:** Excel Copilot
   - **Homepage URL:** `https://excel-copilot-eight.vercel.app`
   - **Authorization callback URL:** `https://excel-copilot-eight.vercel.app/api/auth/callback/github`

### ✅ **3. Comandos de Verificação**

Execute estes comandos para verificar a configuração:

```bash
# 1. Verificar configuração local
node scripts/diagnose-auth.js

# 2. Testar build de produção
npm run build

# 3. Verificar tipos TypeScript
npm run type-check

# 4. Executar testes
npm test
```

### ✅ **4. Verificação Pós-Deploy**

Após o deploy, teste os seguintes endpoints:

```bash
# Health check geral
curl https://excel-copilot-eight.vercel.app/api/auth/health

# Teste de configuração OAuth
curl https://excel-copilot-eight.vercel.app/api/auth/test-config

# Verificar providers disponíveis
curl https://excel-copilot-eight.vercel.app/api/auth/providers

# Verificar CSRF token
curl https://excel-copilot-eight.vercel.app/api/auth/csrf
```

### ✅ **5. Teste de Login**

1. Acesse: https://excel-copilot-eight.vercel.app
2. Clique em "Login"
3. Teste login com Google
4. Teste login with GitHub
5. Verifique se o redirecionamento funciona corretamente

## 🔧 **Troubleshooting**

### **Erro: "Configuration Error"**

**Causa:** Variáveis de ambiente ausentes ou incorretas

**Solução:**

1. Verifique se todas as variáveis obrigatórias estão configuradas na Vercel
2. Execute: `curl https://excel-copilot-eight.vercel.app/api/auth/health`
3. Verifique os logs da Vercel em tempo real

### **Erro: "OAuth Callback Error"**

**Causa:** URLs de callback incorretas nos providers

**Solução:**

1. Verifique se as URLs de callback estão exatamente como especificado acima
2. Certifique-se de que não há espaços ou caracteres extras
3. Teste com: `curl https://excel-copilot-eight.vercel.app/api/auth/test-config`

### **Erro: "Database Connection Failed"**

**Causa:** Problemas na conexão com Supabase

**Solução:**

1. Verifique se `DATABASE_URL` e `DIRECT_URL` estão corretas
2. Confirme que o projeto Supabase está ativo
3. Execute as migrações se necessário: `npm run db:migrate:deploy`

## 📊 **Monitoramento**

### **Endpoints de Monitoramento:**

- **Health Check:** `/api/auth/health`
- **Configuração OAuth:** `/api/auth/test-config`
- **Status Geral:** `/api/health`

### **Logs Importantes:**

Monitore estes logs na Vercel:

```
✅ Login aprovado: { provider: 'google', userEmail: '...' }
🔄 Redirecionamento OAuth: { url: '...', baseUrl: '...' }
📋 Sessão criada: { userId: '...', email: '...' }
```

### **Alertas de Erro:**

Configure alertas para:

```
❌ Erro no callback signIn
❌ Erro no redirecionamento OAuth
🚫 Acesso não autorizado a rota protegida
```

## 🔐 **Segurança**

### **Configurações Implementadas:**

- ✅ Cookies seguros (httpOnly, sameSite, secure)
- ✅ Validação de redirecionamentos
- ✅ Rate limiting para OAuth
- ✅ Logging de auditoria
- ✅ Validação de variáveis de ambiente
- ✅ Proteção CSRF

### **Recomendações Adicionais:**

1. **Rotação de Secrets:** Altere `NEXTAUTH_SECRET` periodicamente
2. **Monitoramento:** Configure alertas para tentativas de login suspeitas
3. **Backup:** Mantenha backup das configurações OAuth
4. **Auditoria:** Revise logs de autenticação regularmente

## 🚀 **Deploy Automatizado**

### **Script de Deploy:**

```bash
#!/bin/bash
# deploy.sh

echo "🚀 Iniciando deploy do Excel Copilot..."

# 1. Verificar configuração
echo "1. Verificando configuração..."
node scripts/diagnose-auth.js || exit 1

# 2. Executar testes
echo "2. Executando testes..."
npm test || exit 1

# 3. Verificar tipos
echo "3. Verificando tipos TypeScript..."
npm run type-check || exit 1

# 4. Build de produção
echo "4. Executando build..."
npm run build || exit 1

# 5. Deploy para Vercel
echo "5. Fazendo deploy..."
vercel --prod

# 6. Verificar deploy
echo "6. Verificando deploy..."
sleep 30
curl -f https://excel-copilot-eight.vercel.app/api/auth/health || exit 1

echo "✅ Deploy concluído com sucesso!"
```

### **GitHub Actions (Opcional):**

```yaml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run tests
        run: npm test

      - name: Type check
        run: npm run type-check

      - name: Build
        run: npm run build

      - name: Deploy to Vercel
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.ORG_ID }}
          vercel-project-id: ${{ secrets.PROJECT_ID }}
          vercel-args: '--prod'
```

## 📞 **Suporte**

Se encontrar problemas:

1. **Verifique os logs:** Vercel Dashboard > Functions > View Function Logs
2. **Execute diagnóstico:** `node scripts/diagnose-auth.js`
3. **Teste configuração:** `curl https://excel-copilot-eight.vercel.app/api/auth/test-config`
4. **Consulte documentação:** Verifique este arquivo e os arquivos de troubleshooting

---

**✅ Configuração implementada com sucesso!**
**🔒 Sistema de autenticação robusto e seguro**
**📊 Monitoramento e logging completos**
**🚀 Pronto para produção**
