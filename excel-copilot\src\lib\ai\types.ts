/**
 * Tipos relacionados ao processamento de IA e operações Excel
 */

// Opções para o parser de comandos da IA
export interface AICommandParserOptions {
  /** Se deve retornar estrutura JSON parseada */
  parseJson?: boolean;

  /** Se deve tentar extrair operações mesmo quando parcialmente compreendidas */
  allowPartialMatch?: boolean;

  /** Formato específico a ser usado na resposta */
  responseFormat?: 'json' | 'text' | 'operations';

  /** Tempo limite para processamento em ms */
  timeout?: number;
}

// Opções específicas para consumir API de IA
export interface AIApiOptions {
  /** Chave da API */
  apiKey?: string;

  /** Modelo a ser usado */
  model?: string;

  /** Temperatura (0.0 a 1.0) */
  temperature?: number;

  /** Limite de tokens na resposta */
  maxTokens?: number;

  /** Tempo limite da requisição em ms */
  timeout?: number;

  /** Se deve usar a versão mockada da API */
  useMock?: boolean;
}

// Contexto de uma planilha para enviar para a IA
export interface SpreadsheetContext {
  /** Nome da planilha ativa */
  activeSheet?: string;

  /** Seleção atual (ex: "A1:B10") */
  selection?: string;

  /** Cabeçalhos disponíveis */
  headers?: string[];

  /** Operações recentes realizadas */
  recentOperations?: string[];

  /** Estatísticas gerais (para melhor contextualização) */
  stats?: {
    rows: number;
    columns: number;
    nonEmptyCells: number;
    hasFormulas: boolean;
    hasCharts: boolean;
  };
}

// Resultado de uma operação da IA
export interface AIOperationResult {
  /** Se a operação foi bem sucedida */
  success: boolean;

  /** Mensagem descritiva do resultado */
  message: string;

  /** Se ocorreu algum erro */
  error?: string;

  /** Dados resultantes da operação */
  data?: any;

  /** Operações que foram executadas */
  executedOperations?: string[];

  /** Tempo de execução em ms */
  executionTime?: number;
}

// Estatísticas de uso da API de IA
export interface AIUsageStats {
  /** Total de tokens consumidos */
  totalTokens: number;

  /** Total de requisições realizadas */
  totalRequests: number;

  /** Tempo médio de resposta em ms */
  averageResponseTime: number;

  /** Taxa de sucesso (0.0 a 1.0) */
  successRate: number;

  /** Taxa de perplexidade (métrica de qualidade) */
  perplexityRate?: number;
}
