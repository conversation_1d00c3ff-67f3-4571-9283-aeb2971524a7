'use client';

import { X } from 'lucide-react';
import { memo, useCallback, useMemo, useEffect } from 'react';

import { Button } from '@/components/ui/button';

// Cache para células editadas anteriormente para evitar flash de renderização
const cellValueCache = new Map<string, string>();

// Estatísticas de uso do cache para monitoramento
let cacheMisses = 0;
let cacheHits = 0;

/**
 * Limpa o cache de valores de células
 * @param force Se true, limpa todo o cache, caso contrário só limpa se atingir o tamanho máximo
 */
export const clearCellCache = (force = false): void => {
  if (force) {
    cellValueCache.clear();
    cacheMisses = 0;
    cacheHits = 0;
    return;
  }

  limitCacheSize();
};

/**
 * Retorna estatísticas de uso do cache
 */
export const getCacheStats = (): {
  hits: number;
  misses: number;
  size: number;
  hitRate: number;
} => {
  const total = cacheHits + cacheMisses;
  return {
    hits: cacheHits,
    misses: cacheMisses,
    size: cellValueCache.size,
    hitRate: total === 0 ? 0 : cacheHits / total,
  };
};

// Componente de célula otimizado com memoização profunda
export const OptimizedCell = memo(
  ({
    rowIndex,
    colIndex,
    value,
    isModified,
    readOnly,
    onCellChange,
    header,
  }: {
    rowIndex: number;
    colIndex: number;
    value: any;
    isModified: boolean;
    readOnly: boolean;
    onCellChange: (row: number, col: number, value: string) => void;
    header: string;
  }) => {
    // Gerar key única para a célula
    const cellKey = `${rowIndex}-${colIndex}`;

    // Atualizar o cache quando o valor muda
    if (value !== undefined && value !== null) {
      cellValueCache.set(cellKey, String(value));
    }

    // Valor para renderizar - usar cache se valor está undefined para evitar "flash" de branco
    const displayValue =
      value ??
      (() => {
        const cachedValue = cellValueCache.get(cellKey);
        if (cachedValue) {
          cacheHits++;
          return cachedValue;
        }
        cacheMisses++;
        return '';
      })();

    // Memoizar o handler de onChange para evitar recriação a cada renderização
    const handleChange = useCallback(
      (e: React.ChangeEvent<HTMLInputElement>) => {
        const newValue = e.target.value;
        cellValueCache.set(cellKey, newValue);
        onCellChange(rowIndex, colIndex, newValue);
      },
      [rowIndex, colIndex, onCellChange, cellKey]
    );

    // Classe de transição apenas quando necessário
    const transitionClass = isModified
      ? 'bg-blue-50 dark:bg-blue-900/30 transition-colors duration-1000'
      : '';

    // Memoizar a classe para evitar recálculo
    const cellClass = useMemo(
      () => `table-cell p-1 border border-border ${transitionClass}`,
      [transitionClass]
    );

    return (
      <div
        className={cellClass}
        role="gridcell"
        aria-colindex={colIndex + 1}
        aria-rowindex={rowIndex + 1}
      >
        <input
          type="text"
          value={displayValue}
          readOnly={readOnly}
          onChange={handleChange}
          className="w-full bg-transparent border-0 focus:ring-1 focus:ring-blue-500 p-1"
          aria-label={`Célula ${header}${rowIndex + 1}`}
        />
      </div>
    );
  },
  // Comparador personalizado para evitar re-renderizações desnecessárias
  (prevProps, nextProps) => {
    return (
      prevProps.value === nextProps.value &&
      prevProps.isModified === nextProps.isModified &&
      prevProps.readOnly === nextProps.readOnly
    );
  }
);
OptimizedCell.displayName = 'OptimizedCell';

// Wrapper de virtualized row com limpeza de cache
export const VirtualizedRowWrapper = memo(
  ({
    visibleRows,
    totalRows,
    virtualizer,
  }: {
    visibleRows: number;
    totalRows: number;
    virtualizer: any;
  }) => {
    // Limpar o cache quando há uma grande mudança no número de rows
    useEffect(() => {
      if (totalRows > 1000) {
        const _rowsThreshold = totalRows > 5000 ? 500 : 1000;

        // Limpar células de linhas não visíveis quando temos muitas linhas
        const allKeys = Array.from(cellValueCache.keys());
        const rowsToKeep = new Set<number>();

        // Manter as linhas visíveis e um buffer
        const bufferSize = 20;
        const startRow = Math.max(0, virtualizer.range.startIndex - bufferSize);
        const endRow = Math.min(totalRows - 1, virtualizer.range.endIndex + bufferSize);

        for (let i = startRow; i <= endRow; i++) {
          rowsToKeep.add(i);
        }

        // Remover células de linhas não visíveis
        allKeys.forEach(key => {
          const rowStr = key.split('-')[0];
          if (rowStr) {
            const row = parseInt(rowStr, 10);
            if (!rowsToKeep.has(row)) {
              cellValueCache.delete(key);
            }
          }
        });
      }
    }, [visibleRows, totalRows, virtualizer]);

    return null;
  }
);
VirtualizedRowWrapper.displayName = 'VirtualizedRowWrapper';

// Componente de linha da tabela otimizado
export const OptimizedRow = memo(
  ({
    rowIndex,
    rowData,
    headers,
    modifiedCellsMap,
    readOnly,
    onCellChange,
    onRemoveRow,
  }: {
    rowIndex: number;
    rowData: any[];
    headers: string[];
    modifiedCellsMap: Record<string, boolean>;
    readOnly: boolean;
    onCellChange: (row: number, col: number, value: string) => void;
    onRemoveRow: (index: number) => void;
  }) => {
    // Memoizar o handler de remoção de linha
    const handleRemoveRow = useCallback(() => {
      onRemoveRow(rowIndex);
    }, [rowIndex, onRemoveRow]);

    // Memoizar células para evitar recriação desnecessária
    const cells = useMemo(() => {
      return rowData.map((cell, colIndex) => {
        const cellKey = `${rowIndex}-${colIndex}`;
        const isModified = modifiedCellsMap[cellKey] || false;

        return (
          <OptimizedCell
            key={cellKey}
            rowIndex={rowIndex}
            colIndex={colIndex}
            value={cell}
            isModified={isModified}
            readOnly={readOnly}
            onCellChange={onCellChange}
            header={headers[colIndex] || ''}
          />
        );
      });
    }, [rowData, rowIndex, modifiedCellsMap, readOnly, onCellChange, headers]);

    // Memoizar o botão de remoção
    const removeButton = useMemo(() => {
      if (readOnly) return null;

      return (
        <div className="table-cell w-10 text-center">
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0"
            onClick={handleRemoveRow}
            aria-label={`Remover linha ${rowIndex + 1}`}
          >
            <X className="h-3 w-3" />
          </Button>
        </div>
      );
    }, [readOnly, handleRemoveRow, rowIndex]);

    return (
      <>
        {/* Número da linha */}
        <div className="table-cell w-10 text-center text-xs text-muted-foreground bg-muted">
          {rowIndex + 1}
        </div>

        {/* Células de dados (memoizadas) */}
        {cells}

        {/* Botão de remover linha (memoizado) */}
        {removeButton}
      </>
    );
  },
  // Comparador personalizado aprimorado para evitar re-renderizações desnecessárias
  (prevProps, nextProps) => {
    // Fast-path para propriedades que sempre devem causar re-render se mudarem
    if (
      prevProps.readOnly !== nextProps.readOnly ||
      prevProps.rowIndex !== nextProps.rowIndex ||
      prevProps.rowData.length !== nextProps.rowData.length ||
      prevProps.headers.length !== nextProps.headers.length
    ) {
      return false;
    }

    // Usar um método mais eficiente para arrays pequenos
    if (prevProps.rowData.length <= 10) {
      // Verificar se alguma célula foi modificada
      for (let i = 0; i < prevProps.rowData.length; i++) {
        if (prevProps.rowData[i] !== nextProps.rowData[i]) {
          return false;
        }

        const cellKey = `${prevProps.rowIndex}-${i}`;
        if (prevProps.modifiedCellsMap[cellKey] !== nextProps.modifiedCellsMap[cellKey]) {
          return false;
        }
      }
      return true;
    } else {
      // Para arrays maiores, usar comparação mais otimizada
      const prevModifiedKeys = Object.keys(prevProps.modifiedCellsMap).filter(
        key => key.startsWith(`${prevProps.rowIndex}-`) && prevProps.modifiedCellsMap[key]
      );
      const nextModifiedKeys = Object.keys(nextProps.modifiedCellsMap).filter(
        key => key.startsWith(`${nextProps.rowIndex}-`) && nextProps.modifiedCellsMap[key]
      );

      if (prevModifiedKeys.length !== nextModifiedKeys.length) {
        return false;
      }

      if (prevModifiedKeys.some(key => !nextProps.modifiedCellsMap[key])) {
        return false;
      }

      // Verificação rápida de valores de array usando JSON stringify para grandes arrays
      // Só usar quando necessário pois tem custo de performance
      return JSON.stringify(prevProps.rowData) === JSON.stringify(nextProps.rowData);
    }
  }
);
OptimizedRow.displayName = 'OptimizedRow';

// Limitar tamanho do cache para evitar memory leaks
function limitCacheSize(maxSize = 10000) {
  if (cellValueCache.size > maxSize) {
    // Remover os primeiros 1000 itens para evitar operações repetidas
    const keysToDelete = Array.from(cellValueCache.keys()).slice(0, 1000);
    keysToDelete.forEach(key => cellValueCache.delete(key));
  }
}

// Utilitário para criar o mapa de células modificadas com cache automático
export const createModifiedCellsMap = (
  lastModifiedCells?: Array<{ row: number; col: number }> | null,
  lastModifiedCell?: { row: number; col: number } | null
): Record<string, boolean> => {
  const map: Record<string, boolean> = {};

  if (lastModifiedCells && lastModifiedCells.length > 0) {
    lastModifiedCells.forEach(cell => {
      map[`${cell.row}-${cell.col}`] = true;
    });
  } else if (lastModifiedCell) {
    map[`${lastModifiedCell.row}-${lastModifiedCell.col}`] = true;
  }

  // Limitar tamanho do cache periodicamente
  limitCacheSize();

  return map;
};
