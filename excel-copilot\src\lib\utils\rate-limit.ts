import { Redis } from '@upstash/redis';

// Cliente Redis para armazenar dados de rate limit
// Usando Upstash Redis por ser serverless-friendly
const redis = Redis.fromEnv();

interface RateLimitResult {
  success: boolean; // Se a requisição está dentro do limite
  limit: number; // Limite total
  remaining: number; // Requisições restantes
  reset: number; // Timestamp em ms quando o limite será resetado
  error?: string; // Mensagem de erro, se houver
}

// Interface para tipagem do resultado do zrange com scores
interface RedisZrangeResult {
  member: string;
  score: number;
}

/**
 * Implementa rate limiting baseado em Redis
 * @param identifier Identificador único (userId, IP, etc)
 * @param maxRequests Número máximo de requisições permitidas no intervalo
 * @param window Duração da janela em segundos (padrão: 60s)
 */
export async function redisRateLimit(
  identifier: string,
  maxRequests: number = 30,
  window: number = 60
): Promise<RateLimitResult> {
  // Criar chave única para este identificador
  const key = `ratelimit:${identifier}`;

  const now = Date.now();
  const windowMs = window * 1000;

  try {
    // Usando transação Redis para garantir atomicidade
    const results = await redis
      .pipeline()
      .zremrangebyscore(key, 0, now - windowMs) // Remover entradas expiradas
      .zadd(key, { score: now, member: now.toString() }) // Adicionar requisição atual
      .zcount(key, now - windowMs, '+inf') // Contar requisições na janela
      .pexpire(key, windowMs) // Definir TTL da chave
      .exec();

    // Obter contagem de requisições na janela atual
    const requestCount = (results?.[2] as number) || 0;

    // Verificar se excedeu o limite
    const success = requestCount <= maxRequests;
    const remaining = Math.max(0, maxRequests - requestCount);

    // Calcular quando o limite será resetado
    // Se não temos dados de quando a janela começou, usamos o tempo atual
    const oldestTimestamp = await redis.zrange(key, 0, 0, { withScores: true });
    const reset =
      oldestTimestamp.length > 0
        ? (oldestTimestamp[0] as RedisZrangeResult).score + windowMs
        : now + windowMs;

    return {
      success,
      limit: maxRequests,
      remaining,
      reset,
    };
  } catch (error) {
    console.error('[RATE_LIMIT_ERROR]', error);

    // Em caso de erro, NÃO permitir a requisição por segurança
    return {
      success: false,
      limit: maxRequests,
      remaining: 0,
      reset: now + windowMs,
      error: 'Erro ao verificar limites de taxa. Limite temporário aplicado por segurança.',
    };
  }
}

/**
 * Versão simplificada para casos onde Redis não está disponível
 * Usa um Map em memória (não ideal para ambientes distribuídos)
 */
const inMemoryStore = new Map<string, { timestamps: number[]; reset: number }>();

export function inMemoryRateLimit(
  identifier: string,
  maxRequests: number = 30,
  window: number = 60
): RateLimitResult {
  const now = Date.now();
  const windowMs = window * 1000;

  // Inicializar ou obter dados do usuário
  if (!inMemoryStore.has(identifier)) {
    inMemoryStore.set(identifier, {
      timestamps: [],
      reset: now + windowMs,
    });
  }

  const userData = inMemoryStore.get(identifier)!;

  // Limpar requisições antigas
  userData.timestamps = userData.timestamps.filter(timestamp => timestamp > now - windowMs);

  // Adicionar requisição atual
  userData.timestamps.push(now);

  // Se a primeira requisição na janela, definir reset time
  if (userData.timestamps.length === 1) {
    userData.reset = now + windowMs;
  }

  // Verificar se excedeu o limite
  const success = userData.timestamps.length <= maxRequests;
  const remaining = Math.max(0, maxRequests - userData.timestamps.length);

  // Limpar o store se ficar muito grande para evitar vazamento de memória
  if (inMemoryStore.size > 10000) {
    const oldEntries = [...inMemoryStore.entries()]
      .filter(([_, data]) => data.reset < now)
      .map(([key]) => key);

    for (const key of oldEntries) {
      inMemoryStore.delete(key);
    }
  }

  return {
    success,
    limit: maxRequests,
    remaining,
    reset: userData.reset,
  };
}

// Implementação de rate limiter baseado em Redis com fallback para memória
export const rateLimit =
  process.env.REDIS_URL || process.env.UPSTASH_REDIS_REST_URL ? redisRateLimit : inMemoryRateLimit;
