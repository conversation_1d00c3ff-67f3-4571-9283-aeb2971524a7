import { NextRequest, NextResponse } from 'next/server';

export const dynamic = 'force-dynamic';

/**
 * Endpoint para capturar erros OAuth em tempo real
 * Usado para debug quando o login falha
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);

    // Capturar todos os parâmetros da URL
    const params = Object.fromEntries(searchParams.entries());

    // Informações sobre a requisição
    const requestInfo = {
      timestamp: new Date().toISOString(),
      url: request.url,
      method: request.method,
      headers: Object.fromEntries(request.headers.entries()),
      userAgent: request.headers.get('user-agent'),
      referer: request.headers.get('referer'),
      origin: request.headers.get('origin'),
    };

    // Análise específica de erros OAuth
    const oauthAnalysis = {
      hasError: !!params.error,
      errorType: params.error || null,
      errorDescription: params.error_description || null,
      state: params.state || null,
      code: params.code || null,
      scope: params.scope || null,
    };

    // Determinar o provedor baseado no referer ou state
    let provider = 'unknown';
    if (requestInfo.referer?.includes('google')) {
      provider = 'google';
    } else if (requestInfo.referer?.includes('github')) {
      provider = 'github';
    } else if (params.state) {
      // Tentar extrair do state (NextAuth geralmente inclui informações do provedor)
      try {
        const stateDecoded = decodeURIComponent(params.state);
        if (stateDecoded.includes('google')) provider = 'google';
        if (stateDecoded.includes('github')) provider = 'github';
      } catch {
        // Ignorar erro de decode
      }
    }

    // Log detalhado para console (visível nos logs da Vercel)
    // eslint-disable-next-line no-console
    console.log('🔍 [OAUTH-CAPTURE] Erro OAuth capturado:', {
      provider,
      error: oauthAnalysis.errorType,
      description: oauthAnalysis.errorDescription,
      timestamp: requestInfo.timestamp,
      referer: requestInfo.referer,
    });

    // Análise de problemas comuns
    const commonIssues = [];

    if (oauthAnalysis.errorType === 'access_denied') {
      commonIssues.push('Usuário rejeitou as permissões ou cancelou o login');
    }

    if (oauthAnalysis.errorType === 'redirect_uri_mismatch') {
      commonIssues.push('URL de callback não está configurada corretamente no provedor OAuth');
      commonIssues.push(
        `Verifique se a URL está exatamente: ${process.env.AUTH_NEXTAUTH_URL}/api/auth/callback/${provider}`
      );
    }

    if (oauthAnalysis.errorType === 'invalid_client') {
      commonIssues.push('Client ID ou Client Secret incorretos');
      commonIssues.push('Verifique as variáveis de ambiente na Vercel');
    }

    if (oauthAnalysis.errorType === 'invalid_request') {
      commonIssues.push('Parâmetros da requisição OAuth inválidos');
      commonIssues.push('Possível problema na configuração do NextAuth.js');
    }

    if (!oauthAnalysis.hasError && !oauthAnalysis.code) {
      commonIssues.push('Nenhum erro ou código de autorização detectado');
      commonIssues.push('Possível problema no fluxo de redirecionamento');
    }

    // Resposta estruturada
    const response = {
      status: oauthAnalysis.hasError ? 'error' : 'info',
      message: oauthAnalysis.hasError
        ? `Erro OAuth detectado: ${oauthAnalysis.errorType}`
        : 'Captura de dados OAuth',
      provider,
      oauth: oauthAnalysis,
      request: requestInfo,
      allParams: params,
      commonIssues,
      nextSteps: [
        'Verifique as configurações OAuth no provedor',
        'Confirme as URLs de callback',
        'Verifique as variáveis de ambiente na Vercel',
        'Teste novamente o login',
      ],
    };

    // Se houver erro, redirecionar para a página de login com informações
    if (oauthAnalysis.hasError && oauthAnalysis.errorType) {
      const errorUrl = new URL('/auth/signin', request.url);
      errorUrl.searchParams.set('error', oauthAnalysis.errorType);
      if (oauthAnalysis.errorDescription) {
        errorUrl.searchParams.set('error_description', oauthAnalysis.errorDescription);
      }
      errorUrl.searchParams.set('provider', provider);
      errorUrl.searchParams.set('debug', 'true');

      return NextResponse.redirect(errorUrl);
    }

    // Se não houver erro, retornar dados para debug
    return NextResponse.json(response);
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('🚨 [OAUTH-CAPTURE] Erro ao capturar dados OAuth:', error);

    return NextResponse.json(
      {
        status: 'error',
        message: 'Erro ao capturar dados OAuth',
        error: error instanceof Error ? error.message : 'Erro desconhecido',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

/**
 * Endpoint POST para receber dados de erro do frontend
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // eslint-disable-next-line no-console
    console.log('🔍 [OAUTH-CAPTURE] Erro enviado pelo frontend:', {
      timestamp: new Date().toISOString(),
      data: body,
      userAgent: request.headers.get('user-agent'),
    });

    return NextResponse.json({
      status: 'success',
      message: 'Erro OAuth capturado com sucesso',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('🚨 [OAUTH-CAPTURE] Erro ao processar dados do frontend:', error);

    return NextResponse.json(
      {
        status: 'error',
        message: 'Erro ao processar dados do frontend',
        error: error instanceof Error ? error.message : 'Erro desconhecido',
      },
      { status: 500 }
    );
  }
}
