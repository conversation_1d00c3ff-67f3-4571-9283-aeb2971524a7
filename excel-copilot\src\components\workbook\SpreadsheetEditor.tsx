'use client';

// Importações principais mantidas para compatibilidade
import { useVirtualizer } from '@tanstack/react-virtual';
import {
  ChevronRight,
  FileSpreadsheet,
  BarChart,
  Undo,
  Redo,
  Save,
  Loader2,
  ArrowRight,
  Sparkles,
  MessageSquare,
  X,
  ChevronLeft,
  KeyboardIcon,
  FullscreenIcon,
  AlertCircle,
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useState, useEffect, useRef, useCallback, KeyboardEvent, useMemo, memo } from 'react';
import { toast } from 'sonner';

import { ChatInput } from '@/components/chat-interface/ChatInput';
import { CommandFeedback, CommandFeedbackData } from '@/components/command-feedback';
import { CommandPreview } from '@/components/command-preview';
import { ExportButton } from '@/components/export-button';
import { OnlineUsers } from '@/components/realtime/OnlineUsers';
import {
  AlertDialog,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { ActionButton, OptimizedButton } from '@/components/ui/optimized-button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { UploadButton } from '@/components/upload-button';
import type { CommandInterpretation } from '@/hooks/useAIChat';
import { useAIChat } from '@/hooks/useAIChat';
// import { useDesktopBridge } from '@/hooks/useDesktopBridge'; // Removido - não existe mais
import { useExcelOperations } from '@/hooks/useExcelOperations';
import { useWorkbookRealtime } from '@/hooks/useWorkbookRealtime';
import { feedbackService } from '@/lib/ai/feedback-service';
import { logger } from '@/lib/logger';

import {
  OptimizedRow,
  createModifiedCellsMap,
  VirtualizedRowWrapper,
} from './OptimizedTableComponents';

// Tipos para o editor de planilha
interface SpreadsheetData {
  headers: string[];
  rows: any[][];
  charts?: any[];
  name: string;
}

interface SpreadsheetEditorProps {
  workbookId: string;
  initialData?: SpreadsheetData;
  readOnly?: boolean;
  onSave?: (data: SpreadsheetData) => Promise<void>;
  initialCommand?: string | null;
}

// Interface para o resultado das operações
interface _ExcelOperationResult {
  updatedData: SpreadsheetData;
  resultSummary: string[];
  modifiedCells?: Array<{ row: number; col: number }>;
}

// Exemplos de comandos comuns para sugestões rápidas
const QUICK_COMMAND_EXAMPLES = [
  { text: 'Crie uma tabela de controle de horas', icon: <FileSpreadsheet className="h-3 w-3" /> },
  { text: 'Adicione validação de dados na coluna B', icon: <ArrowRight className="h-3 w-3" /> },
  { text: 'Gere um gráfico de barras com os dados', icon: <BarChart className="h-3 w-3" /> },
  { text: 'Calcule a média da coluna C', icon: <ArrowRight className="h-3 w-3" /> },
  { text: 'Formate a tabela com cores alternadas', icon: <ArrowRight className="h-3 w-3" /> },
];

// Passos do tutorial para novos usuários
const TUTORIAL_STEPS = [
  {
    title: 'Bem-vindo ao Excel Copilot',
    content:
      'Este assistente permite criar e editar planilhas através de comandos em linguagem natural.',
    target: 'header',
  },
  {
    title: 'Assistente de IA',
    content:
      "Aqui você pode digitar comandos como 'Crie uma tabela de vendas' ou 'Calcule a média da coluna B'.",
    target: 'ai-assistant',
  },
  {
    title: 'Sugestões Rápidas',
    content: 'Exemplos de comandos que você pode usar. Clique em um deles para executar.',
    target: 'suggestions',
  },
  {
    title: 'Planilha Interativa',
    content:
      'Sua planilha será atualizada automaticamente conforme seus comandos. Você também pode editar células manualmente.',
    target: 'spreadsheet',
  },
];

// Interface para keyboard shortcuts
interface KeyboardShortcut {
  key: string;
  description: string;
  action: () => void;
  modifiers?: {
    ctrl?: boolean;
    alt?: boolean;
    shift?: boolean;
  };
}

// Memoizando comandos rápidos para evitar recriação desnecessária
const MemoizedQuickCommandButton = memo(
  ({
    command,
    onClick,
  }: {
    command: { text: string; icon: React.ReactNode };
    onClick: () => void;
  }) => (
    <Button
      variant="ghost"
      className="h-8 px-2 text-sm justify-start w-full hover:bg-accent"
      onClick={onClick}
    >
      <span className="mr-2">{command.icon}</span>
      <span className="truncate">{command.text}</span>
    </Button>
  )
);
MemoizedQuickCommandButton.displayName = 'MemoizedQuickCommandButton';

/**
 * Editor de planilha com suporte a comandos por IA
 */
export function SpreadsheetEditor({
  workbookId,
  initialData,
  readOnly = false,
  onSave,
  initialCommand,
}: SpreadsheetEditorProps) {
  // Estado da planilha
  const [spreadsheetData, setSpreadsheetData] = useState<SpreadsheetData>(
    initialData || {
      headers: ['A', 'B', 'C'],
      rows: [
        ['', '', ''],
        ['', '', ''],
        ['', '', ''],
      ],
      charts: [],
      name: 'Nova Planilha',
    }
  );

  // Estado de histórico para desfazer/refazer
  const [history, setHistory] = useState<SpreadsheetData[]>([]);
  const [historyIndex, setHistoryIndex] = useState(-1);
  const [isSaving, setIsSaving] = useState(false);
  const [showCommandPalette, setShowCommandPalette] = useState(false);
  const [isMobileView, setIsMobileView] = useState(false);
  const [showMobileChat, setShowMobileChat] = useState(false);
  const [lastModifiedCell, setLastModifiedCell] = useState<{ row: number; col: number } | null>(
    null
  );

  // Novos estados para as melhorias
  const [aiPanelCollapsed, setAiPanelCollapsed] = useState(false);
  const [_showAiIndicator, setShowAiIndicator] = useState(false);
  const [_showTutorial, setShowTutorial] = useState(false);
  const [tutorialStep, setTutorialStep] = useState(0);
  const [_isFirstVisit, setIsFirstVisit] = useState(false);

  // Ref para a tabela para rolagem
  const tableRef = useRef<HTMLDivElement>(null);

  // Ref para controlar timers
  const timersRef = useRef<NodeJS.Timeout[]>([]);

  // Novos estados para melhorias na interação com IA
  const [commandInterpretation, setCommandInterpretation] = useState<string | null>(null);
  const [pendingCommand, setPendingCommand] = useState<{ id: string; command: string } | null>(
    null
  );
  const [showFeedback, setShowFeedback] = useState(false);
  const [lastExecutedCommand, setLastExecutedCommand] = useState<{
    id: string;
    command: string;
  } | null>(null);

  // Estado para armazenar mensagens do chat
  const [_chatHistory, _setChatHistory] = useState<
    { role: 'user' | 'assistant'; content: string }[]
  >([]);

  // Referência para o hook de chat AI
  const aiChatRef = useRef<ReturnType<typeof useAIChat> | null>(null);

  // Função para adicionar e rastrear timers para limpeza
  const createTimer = useCallback((callback: () => void, delay: number): NodeJS.Timeout => {
    const timer = setTimeout(() => {
      callback();
      // Remover automaticamente da lista após execução
      timersRef.current = timersRef.current.filter(t => t !== timer);
    }, delay);

    // Adicionar à lista de timers ativos
    timersRef.current.push(timer);
    return timer;
  }, []);

  // Memoizar o histórico para evitar recriações desnecessárias
  const addToHistory = useCallback(
    (data: SpreadsheetData) => {
      // Evitar adicionar ao histórico se os dados são iguais ao último item
      if (history.length > 0) {
        const lastItem = history[history.length - 1];
        // Comparação simples para evitar adicionar o mesmo estado duas vezes
        if (JSON.stringify(lastItem) === JSON.stringify(data)) {
          return;
        }
      }

      // Limitar histórico a 20 entradas
      const newHistory = history.slice(0, historyIndex + 1).slice(-19);
      setHistory([...newHistory, JSON.parse(JSON.stringify(data))]);
      setHistoryIndex(newHistory.length);
    },
    [history, historyIndex]
  );

  // Hook de operações Excel
  const {
    processExcelCommand,
    isProcessing: isExcelProcessing,
    lastModifiedCells,
  } = useExcelOperations({
    onDataChange: setSpreadsheetData,
    onAddHistory: addToHistory,
  });

  // Hook de Real-time para colaboração
  const {
    isConnected: isRealtimeConnected,
    updateCursor,
    broadcastCellChange,
  } = useWorkbookRealtime(workbookId);

  // Declarando handleAIResponse antes de useAIChat
  const _handleAIResponse = useCallback(
    (response: string) => {
      // Processar o comando Excel com o novo hook
      return processExcelCommand(response, spreadsheetData);
    },
    [processExcelCommand, spreadsheetData]
  );

  // Função para lidar com interpretação de comandos da IA
  const handleCommandInterpreted = useCallback((interpretation: CommandInterpretation) => {
    setCommandInterpretation(interpretation.interpretation);
    setPendingCommand({
      id: interpretation.commandId || interpretation._commandId || '',
      command: interpretation.interpretation,
    });
  }, []);

  // Manejar operações em lote vindas do AI
  const handleOperations = useCallback(
    (operations: any[]) => {
      if (!operations || !Array.isArray(operations) || operations.length === 0) return;

      addToHistory(spreadsheetData);

      setSpreadsheetData(prevData => {
        // Clone seguro dos dados
        const newData = { ...prevData };
        newData.headers = Array.isArray(prevData.headers) ? [...prevData.headers] : [];
        newData.rows = Array.isArray(prevData.rows) ? [...prevData.rows] : [];

        // Lista para rastrear células modificadas
        const modifiedCells: Array<{ row: number; col: number }> = [];

        operations.forEach(op => {
          // Verificar se a operação é válida
          if (!op || typeof op !== 'object') return;

          if (
            op.type === 'cell_update' &&
            typeof op.row === 'number' &&
            typeof op.col === 'number'
          ) {
            // Garantir que a linha existe
            if (!Array.isArray(newData.rows[op.row])) {
              newData.rows[op.row] = Array(newData.headers.length).fill('');
            }

            // Garantir que o índice de coluna é válido e que temos arrays válidos
            if (
              op.row >= 0 &&
              op.col >= 0 &&
              Array.isArray(newData.headers) &&
              op.col < newData.headers.length &&
              Array.isArray(newData.rows) &&
              newData.rows[op.row] !== undefined &&
              Array.isArray(newData.rows[op.row])
            ) {
              try {
                // Acesso seguro garantido pelas verificações acima
                if (newData.rows[op.row] && typeof op.col === 'number') {
                  // Acesso direto após verificação usando type assertion
                  (newData.rows[op.row] as any[])[op.col] = op.value;
                  modifiedCells.push({ row: op.row, col: op.col });
                }
              } catch (err) {
                logger.error('SpreadsheetEditor: Erro ao atualizar célula', err, {
                  row: op.row,
                  col: op.col,
                  operation: op.type,
                  workbookId,
                });
              }
            }
          } else if (op.type === 'add_row') {
            const newRow = Array(newData.headers.length).fill('');
            newData.rows.push(newRow);
          } else if (
            op.type === 'add_column' &&
            Array.isArray(newData.headers) &&
            Array.isArray(newData.rows)
          ) {
            newData.headers.push(op.name || `Coluna ${newData.headers.length + 1}`);
            newData.rows.forEach((row, idx) => {
              if (Array.isArray(row)) {
                newData.rows[idx] = [...row, ''];
              } else {
                newData.rows[idx] = Array(newData.headers.length).fill('');
              }
            });
          }
        });

        // Usar operador de coalescência nula para garantir um valor não nulo
        if (modifiedCells.length > 0) {
          setLastModifiedCell(modifiedCells[0] || null);
        }
        return newData;
      });
    },
    [spreadsheetData, addToHistory, setLastModifiedCell]
  );

  // Inicialização do hook useAIChat com ref
  const {
    sendMessage,
    isProcessing,
    confirmAndExecute,
    cancelCommand,
    pendingInterpretation,
    messages,
    error,
    clearMessages,
    commandStatus,
  } = useAIChat({
    workbookId,
    onMessageReceived: (content: string) => {
      if (content) {
        try {
          const jsonContent = JSON.parse(content);
          if (jsonContent.operations) {
            handleOperations(jsonContent.operations);
          }
        } catch (err) {
          logger.error('SpreadsheetEditor: Erro ao processar resposta da IA', err, {
            content: content?.substring(0, 100),
            workbookId,
          });
        }
      }
    },
    onInterpretation: handleCommandInterpreted,
  });

  // Atribuir à ref
  useEffect(() => {
    aiChatRef.current = {
      sendMessage,
      isProcessing,
      confirmAndExecute,
      cancelCommand,
      pendingInterpretation,
      messages,
      error,
      clearMessages,
      commandStatus,
    };
  }, [
    sendMessage,
    isProcessing,
    confirmAndExecute,
    cancelCommand,
    pendingInterpretation,
    messages,
    error,
    clearMessages,
    commandStatus,
  ]);

  // Desktop Bridge removido - não é mais necessário para SaaS web

  // Estado para monitorar o uso da API e exibir alertas
  const [apiUsageInfo, setApiUsageInfo] = useState<{ used: number; limit: number } | null>(null);
  const [showUpgradeModal, setShowUpgradeModal] = useState(false);
  const router = useRouter();

  // Verificar se é visualização mobile
  useEffect(() => {
    const checkMobileView = () => {
      setIsMobileView(window.innerWidth < 768);
    };

    checkMobileView();
    window.addEventListener('resize', checkMobileView);

    return () => {
      window.removeEventListener('resize', checkMobileView);
    };
  }, []);

  // Efeito para limpeza de recursos ao desmontar o componente
  useEffect(() => {
    // Retornar função de limpeza
    return () => {
      // Limpar todos os timers ativos
      timersRef.current.forEach(timer => clearTimeout(timer));
      timersRef.current = [];
    };
  }, []);

  // Função para desfazer
  const handleUndo = useCallback(() => {
    if (historyIndex > 0) {
      setHistoryIndex(historyIndex - 1);
      setSpreadsheetData(JSON.parse(JSON.stringify(history[historyIndex - 1])));
      toast.info('Ação desfeita');
    }
  }, [history, historyIndex]);

  // Função para refazer
  const handleRedo = useCallback(() => {
    if (historyIndex < history.length - 1) {
      setHistoryIndex(historyIndex + 1);
      setSpreadsheetData(JSON.parse(JSON.stringify(history[historyIndex + 1])));
      toast.info('Ação refeita');
    }
  }, [history, historyIndex]);

  // Inicializar histórico ao carregar dados iniciais
  useEffect(() => {
    if (initialData) {
      setSpreadsheetData(initialData);
      setHistory([JSON.parse(JSON.stringify(initialData))]);
      setHistoryIndex(0);
    }
  }, [initialData]);

  // Função para salvar spreadsheet com useCallback
  const saveSpreadsheet = useCallback(async () => {
    if (readOnly) return;

    try {
      setIsSaving(true);

      // Se temos uma função onSave personalizada
      if (onSave) {
        await onSave(spreadsheetData);
        toast.success('Planilha salva com sucesso');
        return;
      }

      // Implementação padrão de salvamento
      const response = await fetch(`/api/workbooks/${workbookId}/sheets`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: spreadsheetData.name || 'Sem nome',
          data: JSON.stringify(spreadsheetData),
        }),
      });

      if (!response.ok) {
        throw new Error('Erro ao salvar planilha');
      }

      toast.success('Planilha salva com sucesso');
    } catch (error) {
      logger.error('SpreadsheetEditor: Erro ao salvar planilha', error, {
        workbookId,
        spreadsheetName: spreadsheetData.name,
        readOnly,
      });
      toast.error('Erro ao salvar planilha');
    } finally {
      setIsSaving(false);
    }
  }, [workbookId, spreadsheetData, onSave, readOnly]);

  // Função otimizada para enviar comandos
  const handleSendCommand = useCallback(
    (command: string) => {
      if (!command.trim()) return;

      sendMessage(command);
    },
    [sendMessage]
  );

  // Calcular mapa de células modificadas de forma otimizada
  const modifiedCellsMap = useMemo(
    () => createModifiedCellsMap(lastModifiedCells, lastModifiedCell),
    [lastModifiedCells, lastModifiedCell]
  );

  // Substituir a função handleCellChange por uma versão otimizada com Real-time
  const handleCellChange = useCallback(
    async (rowIndex: number, colIndex: number, value: string) => {
      if (readOnly || typeof rowIndex !== 'number' || typeof colIndex !== 'number') return;

      // Verificar se os índices são válidos
      if (rowIndex < 0 || colIndex < 0) return;

      // Adicionar ao histórico antes de modificar
      addToHistory(spreadsheetData);

      // Atualizar dados usando o padrão funcional para evitar mutações
      setSpreadsheetData(prevData => {
        // Verificar que temos dados válidos
        if (!prevData || !Array.isArray(prevData.headers)) return prevData;

        const newData = { ...prevData };
        // Clone profundo da linha específica que está sendo alterada
        newData.rows = Array.isArray(prevData.rows) ? [...prevData.rows] : [];

        // Garantir que a linha existe e é um array
        if (!Array.isArray(newData.rows[rowIndex])) {
          newData.rows[rowIndex] = Array(newData.headers.length).fill('');
        } else {
          newData.rows[rowIndex] = [...newData.rows[rowIndex]];
        }

        // Verificar se o índice da coluna é válido
        if (colIndex < newData.headers.length) {
          newData.rows[rowIndex][colIndex] = value;
        }

        return newData;
      });

      // Atualizar célula modificada para feedback visual
      setLastModifiedCell({ row: rowIndex, col: colIndex });

      // Broadcast da mudança para outros usuários via Real-time
      if (isRealtimeConnected) {
        try {
          const cellAddress = `${String.fromCharCode(65 + colIndex)}${rowIndex + 1}`;
          await broadcastCellChange('sheet1', cellAddress, value);

          // Atualizar cursor do usuário
          await updateCursor('sheet1', cellAddress);
        } catch (error) {
          console.error('Erro ao enviar mudança via Real-time:', error);
        }
      }
    },
    [
      readOnly,
      addToHistory,
      spreadsheetData,
      setLastModifiedCell,
      isRealtimeConnected,
      broadcastCellChange,
      updateCursor,
    ]
  );

  // Função para adicionar coluna otimizada
  const handleAddColumn = () => {
    if (readOnly) return;

    addToHistory(spreadsheetData);

    setSpreadsheetData(prevData => {
      const lastHeader =
        prevData.headers.length > 0 ? prevData.headers[prevData.headers.length - 1] : null;

      // Gerar novo nome de coluna baseado na última coluna
      let nextHeader: string;
      if (lastHeader && /^[A-Z]$/.test(lastHeader)) {
        // Se a última coluna for uma letra única, gerar próxima letra
        nextHeader = String.fromCharCode((lastHeader.charCodeAt(0) || 64) + 1);
      } else {
        nextHeader = `Coluna ${prevData.headers.length + 1}`;
      }

      // Clonar e atualizar dados
      const newData = { ...prevData };
      newData.headers = [...prevData.headers, nextHeader];

      // Adicionar o valor vazio para a nova coluna em cada linha
      newData.rows = Array.isArray(prevData.rows)
        ? prevData.rows.map(row =>
            Array.isArray(row) ? [...row, ''] : Array(newData.headers.length).fill('')
          )
        : [];

      return newData;
    });

    toast.success('Coluna adicionada');
  };

  // Função para adicionar linha
  const handleAddRow = useCallback(() => {
    if (readOnly) return;

    // Adicionar ao histórico antes de modificar
    addToHistory(spreadsheetData);

    // Criar uma nova linha vazia com o mesmo número de colunas
    const newRow = Array(spreadsheetData.headers.length).fill('');

    // Adicionar a nova linha
    setSpreadsheetData(prevData => {
      const newData = { ...prevData };
      newData.rows = Array.isArray(prevData.rows) ? [...prevData.rows, newRow] : [newRow];
      return newData;
    });

    toast.success('Linha adicionada');
  }, [readOnly, spreadsheetData, addToHistory]);

  // Função para remover linha
  const handleRemoveRow = (rowIndex: number) => {
    if (readOnly) return;

    // Adicionar ao histórico antes de modificar
    addToHistory(spreadsheetData);

    // Remover a linha
    const newRows = [...spreadsheetData.rows];
    newRows.splice(rowIndex, 1);

    setSpreadsheetData({
      ...spreadsheetData,
      rows: newRows,
    });
  };

  // Função para remover coluna
  const handleRemoveColumn = (colIndex: number) => {
    if (readOnly) return;

    // Adicionar ao histórico antes de modificar
    addToHistory(spreadsheetData);

    // Remover coluna do header e de cada linha
    const newHeaders = [...spreadsheetData.headers];
    newHeaders.splice(colIndex, 1);

    const newRows = spreadsheetData.rows.map(row => {
      const newRow = [...row];
      newRow.splice(colIndex, 1);
      return newRow;
    });

    setSpreadsheetData({
      ...spreadsheetData,
      headers: newHeaders,
      rows: newRows,
    });
  };

  // Função para lidar com cliques nos exemplos de comandos rápidos
  const _handleExampleClick = useCallback(
    (exampleText: string) => {
      // Verificar se é possível enviar comandos
      if (isExcelProcessing || readOnly) {
        toast.info('Aguarde', {
          description: 'Espere o comando atual terminar antes de enviar outro.',
          duration: 2000,
        });
        return;
      }

      // Enviar o comando para o chat
      sendMessage(exampleText);

      // Efeito visual de feedback
      toast.success('Comando enviado', {
        description: `Executando: "${exampleText}"`,
        duration: 2000,
      });

      // Fechar paleta de comandos se estiver aberta
      if (showCommandPalette) {
        setShowCommandPalette(false);
      }
    },
    [isExcelProcessing, readOnly, sendMessage, showCommandPalette]
  );

  // Implementação dos botões de histórico e comandos
  const _handleViewHistory = () => {
    // Implementação para visualizar histórico completo
    // Poderia abrir um modal ou navegar para uma página de histórico
    // console.log('Ver histórico completo');
    toast.info('Histórico de comandos', {
      description: 'Funcionalidade em desenvolvimento',
    });
  };

  const _handleCommandsClick = useCallback(() => {
    // Abrir a paleta de comandos
    setShowCommandPalette(true);
  }, []);

  // Botões de navegação
  const _handleNavigationClick = useCallback(
    (route: string) => {
      if (isSaving || isExcelProcessing) {
        toast.info('Aguarde', {
          description: 'Concluindo operações atuais antes de navegar...',
          duration: 2000,
        });
        return;
      }

      // Verificar se há alterações não salvas
      if (history.length > 1 && history[historyIndex] !== initialData) {
        if (confirm('Há alterações não salvas. Deseja sair mesmo assim?')) {
          window.location.href = route;
        }
      } else {
        window.location.href = route;
      }
    },
    [history, historyIndex, initialData, isSaving, isExcelProcessing]
  );

  // Função para conectar com Excel desktop
  const _handleConnectExcel = useCallback(() => {
    toast.info('Conectando com Excel Desktop', {
      description: 'Iniciando conexão com o aplicativo Excel...',
      duration: 3000,
    });

    // Conectar usando o DesktopBridge
    desktopBridge
      .connect()
      .then(success => {
        if (success) {
          toast.success('Excel conectado', {
            description: 'Sincronização de dados ativada entre o navegador e Excel desktop',
            duration: 3000,
          });
        } else {
          toast.error('Falha na conexão', {
            description:
              'Não foi possível conectar ao Excel. Verifique se o Excel Copilot Desktop está instalado e em execução.',
            duration: 4000,
          });
        }
      })
      .catch(error => {
        console.error('Erro na conexão:', error);
        toast.error('Erro na conexão', {
          description: 'Ocorreu um erro ao tentar conectar ao Excel.',
          duration: 4000,
        });
      });
  }, [desktopBridge]);

  // Verificar primeira visita para mostrar tutorial
  useEffect(() => {
    const hasVisitedBefore = localStorage.getItem('excel_copilot_visited');
    if (!hasVisitedBefore) {
      setIsFirstVisit(true);
      setShowTutorial(true);
      localStorage.setItem('excel_copilot_visited', 'true');
    }
  }, []);

  // Função para mostrar o indicador de IA (definida fora do useEffect)
  const showIndicator = useCallback(() => {
    setShowAiIndicator(true);
    createTimer(() => {
      setShowAiIndicator(false);
    }, 3000);
  }, [createTimer]);

  // Efeito para mostrar o indicador de IA observando
  useEffect(() => {
    // Adicionar listener para eventos de edição
    const tableElement = tableRef.current;
    if (tableElement) {
      tableElement.addEventListener('input', showIndicator);
      tableElement.addEventListener('click', showIndicator);
    }

    return () => {
      if (tableElement) {
        tableElement.removeEventListener('input', showIndicator);
        tableElement.removeEventListener('click', showIndicator);
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [createTimer]);

  // Função para avançar no tutorial
  const _handleNextTutorialStep = useCallback(() => {
    if (tutorialStep < TUTORIAL_STEPS.length - 1) {
      setTutorialStep(tutorialStep + 1);
    } else {
      setShowTutorial(false);
    }
  }, [tutorialStep]);

  // Função para fechar o tutorial
  const _handleCloseTutorial = useCallback(() => {
    setShowTutorial(false);
  }, []);

  const [_showKeyboardShortcuts, setShowKeyboardShortcuts] = useState(false);
  const [isFullScreen, setIsFullScreen] = useState(false);

  // Definição dos atalhos de teclado
  const getKeyboardShortcuts = useCallback(
    (): KeyboardShortcut[] => [
      {
        key: 's',
        description: 'Salvar planilha',
        action: saveSpreadsheet,
        modifiers: { ctrl: true },
      },
      {
        key: 'z',
        description: 'Desfazer última ação',
        action: handleUndo,
        modifiers: { ctrl: true },
      },
      {
        key: 'y',
        description: 'Refazer última ação',
        action: handleRedo,
        modifiers: { ctrl: true },
      },
      {
        key: '+',
        description: 'Adicionar linha',
        action: handleAddRow,
        modifiers: { ctrl: true, shift: true },
      },
      {
        key: '=',
        description: 'Adicionar coluna',
        action: handleAddColumn,
        modifiers: { ctrl: true, shift: true },
      },
      {
        key: 'k',
        description: 'Abrir paleta de comandos',
        action: () => setShowCommandPalette(true),
        modifiers: { ctrl: true },
      },
      {
        key: '/',
        description: 'Focar no chat assistente',
        action: () => document.getElementById('chat-input')?.focus(),
        modifiers: { ctrl: true },
      },
      {
        key: 'f',
        description: 'Alternar modo tela cheia',
        action: () => setIsFullScreen(!isFullScreen),
        modifiers: { ctrl: true, shift: true },
      },
      {
        key: 'Escape',
        description: 'Fechar diálogos/painéis abertos',
        action: () => {
          setShowCommandPalette(false);
          setShowKeyboardShortcuts(false);
          if (isFullScreen) setIsFullScreen(false);
        },
      },
    ],
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [handleUndo, handleRedo, handleAddRow, handleAddColumn, isFullScreen]
  );

  // Handler para todos os atalhos de teclado
  const handleKeyDown = useCallback(
    (e: KeyboardEvent<HTMLDivElement> | any) => {
      // Ignorar eventos em inputs de texto
      if (
        e.target.tagName === 'INPUT' ||
        e.target.tagName === 'TEXTAREA' ||
        e.target.isContentEditable
      ) {
        return;
      }

      const shortcuts = getKeyboardShortcuts();

      for (const shortcut of shortcuts) {
        if (
          e.key.toLowerCase() === shortcut.key.toLowerCase() &&
          (!shortcut.modifiers?.ctrl || e.ctrlKey) &&
          (!shortcut.modifiers?.alt || e.altKey) &&
          (!shortcut.modifiers?.shift || e.shiftKey)
        ) {
          e.preventDefault();
          shortcut.action();
          return;
        }
      }
    },
    [getKeyboardShortcuts]
  );

  // Aplicar efeito de tela cheia
  useEffect(() => {
    const element = document.documentElement;

    if (isFullScreen) {
      if (element.requestFullscreen) {
        element.requestFullscreen();
      }
    } else {
      if (document.fullscreenElement && document.exitFullscreen) {
        document.exitFullscreen();
      }
    }
  }, [isFullScreen]);

  // Adicionar listener de teclado global
  useEffect(() => {
    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleKeyDown]);

  // Estado para controlar a visibilidade das sugestões
  const [_showSuggestions, setShowSuggestions] = useState(true);
  const [_inputText, setInputText] = useState('');

  // Função para lidar com mudanças no input do chat
  const handleChatInputChange = (text: string) => {
    setInputText(text);
    setShowSuggestions(text.trim() === '');
  };

  // Processar comando inicial se fornecido
  useEffect(() => {
    if (initialCommand && !isExcelProcessing) {
      // Pequeno delay para garantir que tudo está inicializado
      const timer = setTimeout(() => {
        if (sendMessage) {
          sendMessage(initialCommand);
        }
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [initialCommand, sendMessage, isExcelProcessing]);

  // Função para buscar informações de uso da API
  useEffect(() => {
    const fetchApiUsage = async () => {
      try {
        const response = await fetch('/api/user/api-usage');
        if (response.ok) {
          const data = await response.json();
          setApiUsageInfo({
            used: data.apiCallsUsed,
            limit: data.apiCallsLimit,
          });

          // Se o usuário estiver acima de 80% do limite e no plano gratuito, mostrar modal
          if (data.plan === 'free' && data.apiCallsUsed / data.apiCallsLimit >= 0.8) {
            setShowUpgradeModal(true);
          }
        }
      } catch (error) {
        console.error('Erro ao buscar informações de uso:', error);
      }
    };

    fetchApiUsage();
    // Verificar uso a cada 5 comandos
    const checkInterval = sendMessage.length % 5 === 0;
    if (checkInterval && sendMessage.length > 0) {
      fetchApiUsage();
    }
  }, [sendMessage.length]);

  // Funções para lidar com upgrade
  const handleUpgradeProClick = () => {
    router.push('/pricing');
  };

  const handleTrialClick = () => {
    router.push('/api/checkout/trial');
  };

  // Interface de chat no layout mobile
  const renderMobileChat = () => (
    <div
      className={`
      fixed inset-0 bg-background/95 backdrop-blur-sm z-50 flex flex-col
      ${showMobileChat ? 'translate-y-0' : 'translate-y-full'}
      transition-transform duration-300 ease-in-out
    `}
    >
      <div className="flex items-center justify-between p-4 border-b">
        <h3 className="font-semibold flex items-center">
          <Sparkles className="h-4 w-4 mr-2 text-primary" />
          Excel Copilot
        </h3>
        <Button variant="ghost" size="icon" onClick={() => setShowMobileChat(false)}>
          <X className="h-5 w-5" />
        </Button>
      </div>
      <ScrollArea className="flex-1 p-4">
        {messages.length === 0 ? (
          <div className="text-center py-10 text-muted-foreground">
            <Sparkles className="h-8 w-8 mx-auto mb-3 text-primary/60" />
            <h3 className="text-lg font-medium mb-1">Excel Copilot</h3>
            <p className="text-sm max-w-sm mx-auto">
              Envie comandos em linguagem natural para manipular sua planilha
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {messages.map((msg: { role: string; content: string }, index: number) => (
              <div
                key={index}
                className={`
                  p-3 rounded-lg max-w-[85%]
                  ${
                    msg.role === 'user'
                      ? 'bg-primary text-primary-foreground ml-auto'
                      : 'bg-muted text-foreground border'
                  }
                `}
              >
                {msg.content}
              </div>
            ))}
          </div>
        )}
      </ScrollArea>
      <div className="p-4 border-t">
        <ChatInput
          onSendMessage={sendMessage}
          isLoading={isExcelProcessing}
          placeholder="Digite um comando..."
          showExamples={messages.length === 0}
        />
      </div>
    </div>
  );

  // Renderizar comandos rápidos de forma memoizada
  const renderQuickCommands = useMemo(() => {
    return QUICK_COMMAND_EXAMPLES.map((command, index) => (
      <MemoizedQuickCommandButton
        key={index}
        command={command}
        onClick={() => handleSendCommand(command.text)}
      />
    ));
  }, [handleSendCommand]);

  // Refs para virtualização
  const tableContainerRef = useRef<HTMLDivElement>(null);
  const headerRef = useRef<HTMLDivElement>(null);

  // Configuração de virtualização para linhas
  const rowVirtualizer = useVirtualizer({
    count: spreadsheetData.rows.length,
    getScrollElement: () => tableContainerRef.current,
    estimateSize: () => 36,
    overscan: 10,
  });

  // Adicionar componente para gerenciar o cache de células em grandes planilhas
  const memoryManager = useMemo(
    () => (
      <VirtualizedRowWrapper
        visibleRows={rowVirtualizer.getVirtualItems().length}
        totalRows={spreadsheetData.rows.length}
        virtualizer={rowVirtualizer}
      />
    ),
    [rowVirtualizer, spreadsheetData.rows.length]
  );

  // Handler para confirmação de execução de comando
  const handleExecuteCommand = useCallback(async () => {
    if (!aiChatRef.current || !pendingCommand) return;

    setCommandInterpretation(null);

    try {
      const result = await aiChatRef.current.confirmAndExecute();

      if (result) {
        // Armazenar o último comando executado para feedback
        setLastExecutedCommand(pendingCommand);
        setShowFeedback(true);

        // Limpar após 5 segundos se não houver interação
        setTimeout(() => {
          if (showFeedback) {
            setShowFeedback(false);
          }
        }, 5000);
      }
    } catch (error) {
      console.error('Erro ao executar comando:', error);
      toast.error('Erro ao executar o comando');
    } finally {
      setPendingCommand(null);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pendingCommand, aiChatRef]);

  // Handler para cancelamento de comando
  const handleCancelCommand = useCallback(() => {
    if (!aiChatRef.current) return;

    aiChatRef.current.cancelCommand();
    setCommandInterpretation(null);
    setPendingCommand(null);
  }, [aiChatRef]);

  // Handler para envio de feedback
  const handleFeedbackSubmit = useCallback(async (feedback: CommandFeedbackData) => {
    try {
      await feedbackService.storeFeedback(feedback);
      setShowFeedback(false);
      setLastExecutedCommand(null);
    } catch (error) {
      console.error('Erro ao enviar feedback:', error);
      toast.error('Não foi possível enviar seu feedback');
    }
  }, []);

  // Handler para fechar o feedback sem enviar
  const handleDismissFeedback = useCallback(() => {
    setShowFeedback(false);
    setLastExecutedCommand(null);
  }, []);

  // Render principal
  return (
    <div className="flex flex-col h-full w-full relative">
      {/* Barra de ferramentas superior */}
      <div className="flex justify-between items-center border-b p-2 bg-background/80 backdrop-blur-sm">
        <div className="flex items-center gap-1.5">
          <OptimizedButton
            variant="ghost"
            size="icon"
            onClick={handleUndo}
            disabled={historyIndex <= 0 || readOnly}
            title="Desfazer (Ctrl+Z)"
          >
            <Undo className="h-4 w-4" />
          </OptimizedButton>
          <OptimizedButton
            variant="ghost"
            size="icon"
            onClick={handleRedo}
            disabled={historyIndex >= history.length - 1 || readOnly}
            title="Refazer (Ctrl+Y)"
          >
            <Redo className="h-4 w-4" />
          </OptimizedButton>

          <span className="w-px h-4 bg-border mx-1" />

          <OptimizedButton
            variant="ghost"
            size="icon"
            onClick={saveSpreadsheet}
            disabled={isSaving || readOnly}
            title="Salvar (Ctrl+S)"
          >
            {isSaving ? <Loader2 className="h-4 w-4 animate-spin" /> : <Save className="h-4 w-4" />}
          </OptimizedButton>

          <div className="hidden md:flex gap-1 ml-2">
            <ExportButton
              workbookId={workbookId}
              workbookName={spreadsheetData.name}
              sheets={[{ name: spreadsheetData.name, data: spreadsheetData }]}
            />
            <UploadButton
              workbookId={workbookId}
              saveToSupabase={true}
              onUpload={data => {
                if (data && data.sheets && data.sheets.length > 0) {
                  addToHistory(spreadsheetData);
                  setSpreadsheetData(data.sheets[0].data);
                }
              }}
            />
          </div>
        </div>

        <div className="items-center gap-2 hidden md:flex">
          {/* Componente de usuários online */}
          <OnlineUsers workbookId={workbookId} className="mr-2" />

          <Button
            variant="ghost"
            size="sm"
            className="h-8 gap-1 text-xs"
            onClick={() => setAiPanelCollapsed(!aiPanelCollapsed)}
          >
            {aiPanelCollapsed ? (
              <ChevronLeft className="h-3 w-3" />
            ) : (
              <ChevronRight className="h-3 w-3" />
            )}
            {aiPanelCollapsed ? 'Mostrar' : 'Ocultar'} AI
          </Button>

          <Button
            variant="outline"
            size="sm"
            className="h-8 gap-1 text-xs"
            onClick={() => setShowCommandPalette(true)}
          >
            <KeyboardIcon className="h-3 w-3" />
            Comandos
          </Button>

          <Button
            variant="outline"
            size="sm"
            className="h-8 gap-1 text-xs"
            onClick={() => {
              /* Implementar modo de tela cheia */
            }}
          >
            <FullscreenIcon className="h-3 w-3" />
            Tela Cheia
          </Button>
        </div>

        <div className="flex md:hidden">
          <Button
            variant="outline"
            size="sm"
            className="h-8"
            onClick={() => setShowMobileChat(true)}
          >
            <MessageSquare className="h-4 w-4 mr-2" />
            AI Chat
          </Button>
        </div>
      </div>

      {/* Container principal */}
      <div className="flex flex-1 overflow-hidden">
        {/* Conteúdo principal - editor de planilha */}
        <div className="flex-1 overflow-hidden relative">
          {/* Adicionar o gerenciador de memória para otimização */}
          {memoryManager}

          <div
            ref={tableContainerRef}
            className="overflow-auto border border-border rounded-md"
            style={{ height: '400px', width: '100%' }}
          >
            <div className="table w-full relative">
              {/* Cabeçalho da tabela */}
              <div ref={headerRef} className="table-header-group sticky top-0 bg-background z-10">
                <div className="table-row">
                  <div className="table-cell w-10 text-center text-xs font-medium bg-muted">#</div>
                  {spreadsheetData.headers.map((header, index) => (
                    <div key={index} className="table-cell p-2 font-medium bg-muted">
                      {header}
                      {!readOnly && (
                        <ActionButton
                          variant="ghost"
                          size="sm"
                          className="h-6 w-6 p-0 ml-1"
                          actionId={`column-${index}`}
                          onAction={() => handleRemoveColumn(index)}
                          aria-label={`Remover coluna ${header}`}
                        >
                          <X className="h-3 w-3" />
                        </ActionButton>
                      )}
                    </div>
                  ))}
                  <div className="table-cell w-10 bg-muted"></div>
                </div>
              </div>

              {/* Corpo da tabela virtualizado - versão otimizada */}
              <div
                className="table-row-group relative"
                style={{ height: `${rowVirtualizer.getTotalSize()}px` }}
              >
                {rowVirtualizer.getVirtualItems().map(virtualRow => {
                  const rowData = spreadsheetData.rows[virtualRow.index] || [];
                  return (
                    <div
                      key={virtualRow.index}
                      className="table-row"
                      style={{
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        width: '100%',
                        height: `${virtualRow.size}px`,
                        transform: `translateY(${virtualRow.start}px)`,
                      }}
                    >
                      <OptimizedRow
                        rowIndex={virtualRow.index}
                        rowData={rowData}
                        headers={spreadsheetData.headers}
                        modifiedCellsMap={modifiedCellsMap}
                        readOnly={readOnly}
                        onCellChange={handleCellChange}
                        onRemoveRow={handleRemoveRow}
                      />
                    </div>
                  );
                })}
              </div>
            </div>
          </div>

          {/* Indicador visual de alterações */}
          {lastModifiedCells && lastModifiedCells.length > 0 && (
            <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-primary text-primary-foreground px-3 py-1.5 rounded-full text-sm font-medium shadow-lg animate-in fade-in slide-in-from-bottom-5 duration-300">
              {lastModifiedCells.length === 1
                ? 'Célula atualizada'
                : `${lastModifiedCells.length} células atualizadas`}
            </div>
          )}
        </div>

        {/* Painel lateral de IA - visível apenas em desktop */}
        <div
          className={`
            h-full border-l overflow-hidden transition-all duration-300 ease-in-out
            ${aiPanelCollapsed ? 'w-0 opacity-0' : 'w-80 opacity-100'}
            hidden md:block
          `}
          data-tutorial-target="ai-assistant"
        >
          <div className="flex flex-col h-full">
            <div className="p-3 border-b flex items-center justify-between">
              <h3 className="font-semibold text-sm flex items-center">
                <Sparkles className="h-4 w-4 mr-2 text-primary" />
                Excel Copilot
              </h3>
              <Button variant="ghost" size="icon" onClick={() => setAiPanelCollapsed(true)}>
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>

            <ScrollArea className="flex-1 p-3">
              {messages.length === 0 ? (
                <div className="text-center py-10 text-muted-foreground">
                  <Sparkles className="h-8 w-8 mx-auto mb-3 text-primary/60" />
                  <h3 className="font-medium mb-1">Excel Assistente</h3>
                  <p className="text-sm max-w-xs mx-auto">
                    Utilize linguagem natural para manipular sua planilha. Digite comandos como:
                  </p>
                  <div className="mt-4 space-y-2 text-xs">{renderQuickCommands}</div>
                </div>
              ) : (
                <div className="space-y-4">
                  {messages.map((msg: { role: string; content: string }, index: number) => (
                    <div
                      key={index}
                      className={`
                        p-3 rounded-lg text-sm
                        ${
                          msg.role === 'user'
                            ? 'bg-primary/10 border border-primary/20'
                            : 'bg-muted'
                        }
                      `}
                    >
                      <div className="text-xs font-medium mb-1 text-muted-foreground">
                        {msg.role === 'user' ? 'Você' : 'Excel Copilot'}
                      </div>
                      <div className="whitespace-pre-wrap">{msg.content}</div>
                    </div>
                  ))}
                </div>
              )}
            </ScrollArea>

            <div className="p-3 border-t">
              <ChatInput
                onSendMessage={sendMessage}
                isLoading={isExcelProcessing}
                placeholder="Digite um comando..."
                onChange={handleChatInputChange}
                showExamples={false}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Botão fixo para acesso rápido ao assistente IA (visível quando painel colapsado) */}
      {aiPanelCollapsed && !isMobileView && (
        <OptimizedButton
          variant="default"
          size="sm"
          className="fixed right-4 bottom-4 shadow-lg rounded-full h-10 w-10 p-0"
          onClick={() => setAiPanelCollapsed(false)}
        >
          <Sparkles className="h-4 w-4" />
        </OptimizedButton>
      )}

      {/* Interface mobile */}
      {isMobileView && renderMobileChat()}

      {/* Modal de Upgrade */}
      <AlertDialog open={showUpgradeModal} onOpenChange={setShowUpgradeModal}>
        <AlertDialogContent className="max-w-md">
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-amber-500" />
              Você está chegando ao limite
            </AlertDialogTitle>
            <AlertDialogDescription className="text-base">
              Você já utilizou {apiUsageInfo?.used || 0} de {apiUsageInfo?.limit || 50} comandos
              disponíveis no seu plano. Para continuar utilizando todos os recursos do Excel
              Copilot, escolha uma opção abaixo:
            </AlertDialogDescription>
          </AlertDialogHeader>

          <div className="grid grid-cols-1 gap-4 my-4">
            <div
              className="border rounded-lg p-4 hover:border-primary cursor-pointer"
              onClick={() => handleTrialClick()}
            >
              <h3 className="font-semibold flex items-center">
                <Sparkles className="h-4 w-4 mr-2 text-primary" />
                Experimente o Plano Pro Grátis por 7 dias
              </h3>
              <p className="text-sm text-muted-foreground mt-1">
                Acesso total a todos os recursos sem limitações durante 7 dias. Será necessário
                informar um cartão, mas você pode cancelar a qualquer momento.
              </p>
            </div>

            <div
              className="border rounded-lg p-4 hover:border-primary cursor-pointer"
              onClick={() => handleUpgradeProClick()}
            >
              <h3 className="font-semibold flex items-center">
                <ArrowRight className="h-4 w-4 mr-2 text-primary" />
                Fazer Upgrade para o Plano Pro
              </h3>
              <p className="text-sm text-muted-foreground mt-1">
                R$20/mês ou R$200/ano. Acesso ilimitado a todos os recursos premium.
              </p>
            </div>
          </div>

          <AlertDialogFooter>
            <AlertDialogCancel>Continuar no Plano Free</AlertDialogCancel>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Alerta de limite de uso (quando estiver entre 80-99%) */}
      {apiUsageInfo &&
        apiUsageInfo.used / apiUsageInfo.limit >= 0.8 &&
        apiUsageInfo.used / apiUsageInfo.limit < 1 && (
          <div className="bg-amber-50 dark:bg-amber-950/30 border-amber-200 dark:border-amber-800/50 border px-4 py-2 flex items-center justify-between">
            <div className="flex items-center gap-2">
              <AlertCircle className="h-4 w-4 text-amber-500" />
              <span className="text-sm text-amber-800 dark:text-amber-300">
                Você utilizou {Math.round((apiUsageInfo.used / apiUsageInfo.limit) * 100)}% do seu
                limite mensal de comandos.
              </span>
            </div>
            <Button variant="ghost" size="sm" onClick={() => setShowUpgradeModal(true)}>
              Fazer Upgrade
            </Button>
          </div>
        )}

      {/* Alerta de limite atingido (quando chegar a 100%) */}
      {apiUsageInfo && apiUsageInfo.used >= apiUsageInfo.limit && (
        <div className="bg-destructive/10 border-destructive/30 border px-4 py-2 flex items-center justify-between">
          <div className="flex items-center gap-2">
            <AlertCircle className="h-4 w-4 text-destructive" />
            <span className="text-sm text-destructive">
              Você atingiu 100% do seu limite mensal de comandos.
            </span>
          </div>
          <Button variant="outline" size="sm" onClick={() => setShowUpgradeModal(true)}>
            Fazer Upgrade Agora
          </Button>
        </div>
      )}

      {/* Área de IA e controles */}
      <div className="flex flex-col lg:flex-row gap-4 p-4 border-t dark:border-gray-800">
        <div className="flex-1">
          {/* Pré-visualização de comando */}
          {commandInterpretation && (
            <CommandPreview
              command={pendingCommand?.command || ''}
              interpretation={commandInterpretation}
              isLoading={isExcelProcessing}
              onExecute={handleExecuteCommand}
              onCancel={handleCancelCommand}
            />
          )}

          {/* Feedback sobre último comando */}
          {showFeedback && lastExecutedCommand && (
            <CommandFeedback
              commandId={lastExecutedCommand.id}
              command={lastExecutedCommand.command}
              onDismiss={handleDismissFeedback}
              onFeedbackSubmit={handleFeedbackSubmit}
            />
          )}

          {/* Input de chat */}
          <ChatInput
            onSendMessage={sendMessage}
            isLoading={isExcelProcessing}
            disabled={isExcelProcessing || readOnly}
            autoFocus={true}
            onChange={handleChatInputChange}
          />
        </div>

        {/* Painel de sugestões */}
        <div className="w-full lg:w-64 space-y-2">{/* ... existing suggestions panel ... */}</div>
      </div>
    </div>
  );
}
