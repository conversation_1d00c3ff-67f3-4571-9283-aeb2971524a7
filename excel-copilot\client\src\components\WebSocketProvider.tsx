import React, { createContext, useContext, useEffect, useRef, useState } from 'react';
import { v4 as uuidv4 } from 'uuid';

import { WebSocketStatus, WebSocketMessage, OperationResult } from '../types';

// Definir tipo ExcelOperation localmente para evitar dependências circulares
interface ExcelOperation {
  type: string;
  data?: Record<string, unknown>;
  id?: string;
}

// Define context types
interface WebSocketContextType {
  status: WebSocketStatus;
  connect: (url: string) => void;
  disconnect: () => void;
  executeOperation: (operation: ExcelOperation) => Promise<OperationResult>;
}

// Create the context with default values
const WebSocketContext = createContext<WebSocketContextType | null>(null);

// Message response handler type (currently unused)
// type MessageHandler = (result: OperationResult) => void;

export const WebSocketProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [status, setStatus] = useState<WebSocketStatus>(WebSocketStatus.Disconnected);
  const socketRef = useRef<WebSocket | null>(null);
  const operationsRef = useRef<
    Map<
      string,
      {
        resolve: (value: OperationResult) => void;
        reject: (reason?: unknown) => void;
        timeoutId: NodeJS.Timeout;
      }
    >
  >(new Map());

  // Connect to WebSocket
  const connect = (url: string) => {
    if (socketRef.current?.readyState === WebSocket.OPEN) {
      return;
    }

    try {
      setStatus(WebSocketStatus.Connecting);
      socketRef.current = new WebSocket(url);

      socketRef.current.onopen = () => {
        setStatus(WebSocketStatus.Connected);
        // WebSocket connected - using logger would be better
      };

      socketRef.current.onmessage = event => {
        try {
          const message = JSON.parse(event.data) as WebSocketMessage;

          if (message.id && operationsRef.current.has(message.id)) {
            const { resolve, timeoutId } = operationsRef.current.get(message.id)!;
            clearTimeout(timeoutId);
            operationsRef.current.delete(message.id);

            if (message.error) {
              resolve({
                success: false,
                error: message.error,
              });
            } else {
              resolve({
                success: true,
                data: message.data,
              });
            }
          }

          // Handle other message types if needed
        } catch {
          // Error processing WebSocket message - using logger would be better
        }
      };

      socketRef.current.onerror = error => {
        console.error('WebSocket error:', error);
        setStatus(WebSocketStatus.Error);
      };

      socketRef.current.onclose = () => {
        setStatus(WebSocketStatus.Disconnected);
        // WebSocket disconnected - using logger would be better

        // Reject all pending operations
        operationsRef.current.forEach(({ reject, timeoutId }) => {
          clearTimeout(timeoutId);
          reject(new Error('WebSocket disconnected'));
        });
        operationsRef.current.clear();
      };
    } catch (error) {
      console.error('Error connecting to WebSocket:', error);
      setStatus(WebSocketStatus.Failed);
    }
  };

  // Disconnect from WebSocket
  const disconnect = () => {
    if (socketRef.current) {
      socketRef.current.close();
      socketRef.current = null;
    }
  };

  // Execute an operation via WebSocket
  const executeOperation = (operation: ExcelOperation): Promise<OperationResult> => {
    return new Promise((resolve, reject) => {
      if (status !== WebSocketStatus.Connected) {
        return reject(new Error('WebSocket is not connected'));
      }

      const id = uuidv4();
      const message: WebSocketMessage = {
        id,
        type: 'operation',
        operation: operation.type,
        data: operation.data,
      };

      const timeoutId = setTimeout(() => {
        if (operationsRef.current.has(id)) {
          operationsRef.current.delete(id);
          reject(new Error('Operation timed out'));
        }
      }, 30000); // 30 second timeout

      operationsRef.current.set(id, {
        resolve,
        reject,
        timeoutId,
      });

      try {
        if (socketRef.current) {
          socketRef.current.send(JSON.stringify(message));
        } else {
          throw new Error('WebSocket is null');
        }
      } catch (error) {
        clearTimeout(timeoutId);
        operationsRef.current.delete(id);
        reject(error);
      }
    });
  };

  useEffect(() => {
    return () => {
      disconnect();
    };
  }, []);

  // Context value
  const contextValue: WebSocketContextType = {
    status,
    connect,
    disconnect,
    executeOperation,
  };

  return <WebSocketContext.Provider value={contextValue}>{children}</WebSocketContext.Provider>;
};

// Custom hook to use the WebSocket context
export const useWebSocket = () => {
  const context = useContext(WebSocketContext);
  if (!context) {
    throw new Error('useWebSocket must be used within a WebSocketProvider');
  }
  return context;
};

export default WebSocketProvider;
