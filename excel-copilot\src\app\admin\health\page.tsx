/**
 * 🏥 HEALTH DASHBOARD - EXCEL COPILOT
 *
 * Dashboard administrativo para monitorar a saúde do sistema
 *
 * <AUTHOR> Copilot Team
 * @version 1.0.0
 */

'use client';

import {
  Activity,
  Database,
  Shield,
  Bot,
  CreditCard,
  Plug,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
} from 'lucide-react';
import { useState, useEffect } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';

interface HealthCheckResult {
  service: string;
  status: 'healthy' | 'degraded' | 'unhealthy' | 'unknown';
  responseTime: number;
  timestamp: string;
  details?: any;
}

interface HealthReport {
  overall: 'healthy' | 'degraded' | 'unhealthy' | 'unknown';
  timestamp: string;
  responseTime: number;
  services: HealthCheckResult[];
  summary: {
    healthy: number;
    unhealthy: number;
    degraded: number;
    total: number;
  };
}

const SERVICE_ICONS = {
  database: Database,
  auth: Shield,
  ai: Bot,
  stripe: CreditCard,
  mcp: Plug,
} as const;

const STATUS_VARIANTS = {
  healthy: 'default' as const,
  degraded: 'secondary' as const,
  unhealthy: 'destructive' as const,
  unknown: 'outline' as const,
} as const;

export default function HealthDashboard() {
  const [healthData, setHealthData] = useState<HealthReport | null>(null);
  const [loading, setLoading] = useState(true);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);
  const [autoRefresh, setAutoRefresh] = useState(true);

  const fetchHealthData = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/health');
      const data = await response.json();
      setHealthData(data);
      setLastUpdate(new Date());
    } catch (error) {
      console.error('Failed to fetch health data:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchHealthData();
  }, []);

  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(fetchHealthData, 30000); // 30 segundos
    return () => clearInterval(interval);
  }, [autoRefresh]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'degraded':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case 'unhealthy':
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getServiceIcon = (service: string) => {
    const IconComponent = SERVICE_ICONS[service as keyof typeof SERVICE_ICONS] || Activity;
    return <IconComponent className="h-4 w-4" />;
  };

  const formatResponseTime = (time: number) => {
    if (time < 1000) return `${time}ms`;
    return `${(time / 1000).toFixed(2)}s`;
  };

  const getPerformanceLevel = (time: number) => {
    if (time < 100) return 'Excelente';
    if (time < 500) return 'Bom';
    if (time < 1000) return 'Regular';
    if (time < 3000) return 'Lento';
    return 'Crítico';
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">🏥 Health Dashboard</h1>
          <p className="text-muted-foreground">Monitoramento em tempo real da saúde do sistema</p>
        </div>

        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={() => setAutoRefresh(!autoRefresh)}>
            {autoRefresh ? 'Pausar' : 'Retomar'} Auto-refresh
          </Button>

          <Button variant="outline" size="sm" onClick={fetchHealthData} disabled={loading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Atualizar
          </Button>
        </div>
      </div>

      {/* Status Geral */}
      {healthData && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {getStatusIcon(healthData.overall)}
              Status Geral do Sistema
            </CardTitle>
            <CardDescription>
              Última atualização: {lastUpdate?.toLocaleString() || 'Nunca'}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {healthData.summary.healthy}
                </div>
                <div className="text-sm text-muted-foreground">Saudáveis</div>
              </div>

              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-600">
                  {healthData.summary.degraded}
                </div>
                <div className="text-sm text-muted-foreground">Degradados</div>
              </div>

              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">
                  {healthData.summary.unhealthy}
                </div>
                <div className="text-sm text-muted-foreground">Não Saudáveis</div>
              </div>

              <div className="text-center">
                <div className="text-2xl font-bold">
                  {formatResponseTime(healthData.responseTime)}
                </div>
                <div className="text-sm text-muted-foreground">Tempo Total</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Serviços Individuais */}
      {healthData && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {healthData.services.map(service => (
            <Card key={service.service}>
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    {getServiceIcon(service.service)}
                    <span className="capitalize">{service.service}</span>
                  </div>
                  {getStatusIcon(service.status)}
                </CardTitle>
              </CardHeader>

              <CardContent className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Status:</span>
                  <Badge variant={STATUS_VARIANTS[service.status]}>{service.status}</Badge>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Tempo de Resposta:</span>
                  <span className="text-sm font-mono">
                    {formatResponseTime(service.responseTime)}
                  </span>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Performance:</span>
                  <span className="text-sm">{getPerformanceLevel(service.responseTime)}</span>
                </div>

                {service.details && (
                  <>
                    <Separator />
                    <div className="space-y-1">
                      <div className="text-xs text-muted-foreground">Detalhes:</div>
                      {service.details.message && (
                        <div className="text-xs">{service.details.message}</div>
                      )}
                      {service.details.mode && (
                        <div className="text-xs">Modo: {service.details.mode}</div>
                      )}
                      {service.details.providers && (
                        <div className="text-xs">
                          Providers: {service.details.providers.join(', ')}
                        </div>
                      )}
                    </div>
                  </>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Loading State */}
      {loading && !healthData && (
        <div className="flex items-center justify-center py-12">
          <RefreshCw className="h-8 w-8 animate-spin" />
          <span className="ml-2">Carregando dados de saúde...</span>
        </div>
      )}

      {/* Error State */}
      {!loading && !healthData && (
        <Card>
          <CardContent className="flex items-center justify-center py-12">
            <div className="text-center">
              <XCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Erro ao carregar dados de saúde</h3>
              <p className="text-muted-foreground mb-4">
                Não foi possível conectar com os serviços de health check.
              </p>
              <Button onClick={fetchHealthData}>Tentar Novamente</Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Footer */}
      <div className="text-center text-sm text-muted-foreground">
        <p>Dashboard de Health Checks • Excel Copilot v1.0.0</p>
        <p>Atualização automática a cada 30 segundos</p>
      </div>
    </div>
  );
}
