# ============================================================================
# 🔧 CONFIGURAÇÃO DE AMBIENTE - EXCEL COPILOT v2.0
# ============================================================================
#
# Este arquivo contém TODAS as variáveis de ambiente suportadas pela aplicação.
# Copie este arquivo para .env.local (desenvolvimento) ou .env.production (produção)
# e configure os valores apropriados.
#
# HIERARQUIA DE PRECEDÊNCIA:
# 1. Variáveis do sistema operacional
# 2. .env.local (desenvolvimento)
# 3. .env.production (produção)
# 4. .env.example (este arquivo - apenas documentação)
#
# CONVENÇÕES DE NOMENCLATURA PADRONIZADA:
# - AUTH_* : Configurações de autenticação
# - DB_* : Configurações de banco de dados
# - AI_* : Configurações de inteligência artificial
# - MCP_* : Configurações de Model Context Protocol
# - STRIPE_* : Configurações de pagamentos
# - SECURITY_* : Configurações de segurança
# - DEV_* : Configurações de desenvolvimento
#
# @version 2.0.0
# <AUTHOR> Copilot Team
# ============================================================================

# ============================================================================
# CONFIGURAÇÕES BÁSICAS (OBRIGATÓRIAS)
# ============================================================================

# Ambiente de execução
# Valores: development | production | test
NODE_ENV=development

# Nome da aplicação
APP_NAME="Excel Copilot"

# Versão da aplicação
APP_VERSION="1.0.0"

# URL base da aplicação
# Desenvolvimento: http://localhost:3000
# Produção: https://excel-copilot-eight.vercel.app
APP_URL=http://localhost:3000
NEXT_PUBLIC_APP_URL=http://localhost:3000

# ============================================================================
# AUTENTICAÇÃO (OBRIGATÓRIAS EM PRODUÇÃO)
# ============================================================================

# NextAuth.js - Chave secreta para JWT (OBRIGATÓRIA)
# Gerar com: openssl rand -base64 32
# DEVE ter pelo menos 32 caracteres em produção
AUTH_NEXTAUTH_SECRET=excel-copilot-secret-key-development

# NextAuth.js - URL base para callbacks OAuth (OBRIGATÓRIA)
# Deve corresponder ao domínio real da aplicação
AUTH_NEXTAUTH_URL=http://localhost:3000

# Google OAuth (OPCIONAL - mas recomendado)
# Obter em: https://console.cloud.google.com/apis/credentials
# GOOGLE_CLIENT_ID=217111050148-1gocm6a0sa9jcrk8s08dubqn8n2001lv.apps.googleusercontent.com
# GOOGLE_CLIENT_SECRET=GOCSPX-...

# GitHub OAuth (OPCIONAL)
# Obter em: https://github.com/settings/applications/new
# GITHUB_CLIENT_ID=Ov23li...
# GITHUB_CLIENT_SECRET=...

# Pular providers OAuth (apenas desenvolvimento)
# SKIP_AUTH_PROVIDERS=false

# ============================================================================
# BANCO DE DADOS (OBRIGATÓRIAS)
# ============================================================================

# URL de conexão com PostgreSQL (OBRIGATÓRIA)
# Formato: postgresql://user:password@host:port/database
# Supabase: postgresql://postgres:[password]@db.[project].supabase.co:6543/postgres
DB_DATABASE_URL=postgresql://postgres:password@localhost:5432/excel_copilot

# URL direta (sem pooling) - para migrações
# Supabase: postgresql://postgres:[password]@db.[project].supabase.co:5432/postgres
# DIRECT_URL=postgresql://postgres:password@localhost:5432/excel_copilot

# Provider do banco de dados
DB_PROVIDER=postgresql

# ============================================================================
# INTELIGÊNCIA ARTIFICIAL (CONFIGURAÇÃO UNIFICADA)
# ============================================================================

# Habilitar serviços de IA (padrão: true em produção, false em desenvolvimento)
# AI_ENABLED=true

# Usar mocks em vez de APIs reais (padrão: true em desenvolvimento)
# AI_USE_MOCK=false

# Google Vertex AI - ID do projeto
# VERTEX_AI_PROJECT_ID=excel-copilot

# Google Vertex AI - Região
# VERTEX_AI_LOCATION=us-central1

# Google Vertex AI - Modelo
# VERTEX_AI_MODEL_NAME=gemini-2.0-flash-001

# Google Vertex AI - Credenciais (JSON base64 ou caminho do arquivo)
# VERTEX_AI_CREDENTIALS=

# ============================================================================
# SUPABASE (SE USANDO COMO BACKEND)
# ============================================================================

# URL do projeto Supabase
# SUPABASE_URL=https://[project-id].supabase.co
# NEXT_PUBLIC_SUPABASE_URL=https://[project-id].supabase.co

# Chave anônima do Supabase
# SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
# NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# Chave de service role (apenas servidor)
# SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# ============================================================================
# CONFIGURAÇÕES DE CACHE E FILAS (REDIS)
# ============================================================================

# Configurações do Redis (para cache, sessões e filas)
REDIS_URL="redis://localhost:6379"
REDIS_HOST="localhost"
REDIS_PORT="6379"
REDIS_PASSWORD=""

# Configurações de Filas (Bull/BullMQ)
QUEUE_REDIS_DB="1"  # Database 1 para filas (0 para cache)
QUEUE_CONCURRENCY="3"  # Máximo de jobs simultâneos
QUEUE_MAX_ATTEMPTS="3"  # Tentativas máximas por job

# ============================================================================
# STRIPE (PAGAMENTOS)
# ============================================================================

# Habilitar Stripe (padrão: true)
# STRIPE_ENABLED=true

# Chave secreta do Stripe
# Teste: sk_test_...
# Produção: sk_live_...
# STRIPE_SECRET_KEY=sk_test_...

# Chave pública do Stripe
# Teste: pk_test_...
# Produção: pk_live_...
# NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_...

# Webhook secret do Stripe
# STRIPE_WEBHOOK_SECRET=whsec_...

# ============================================================================
# MODEL CONTEXT PROTOCOL (MCPs)
# ============================================================================

# Vercel MCP
# MCP_VERCEL_TOKEN=************************
# MCP_VERCEL_PROJECT_ID=prj_IQemY1bXbDdiiQmHDfRAYLUqMZIg
# MCP_VERCEL_TEAM_ID=team_BLCIn3CF09teqBeBn8u0fLqp

# Linear MCP
# MCP_LINEAR_API_KEY=lin_api_...

# GitHub MCP
# MCP_GITHUB_TOKEN=ghp_...

# ============================================================================
# CONFIGURAÇÕES DE DESENVOLVIMENTO
# ============================================================================

# Desabilitar validação de ambiente (NÃO usar em produção)
# DEV_DISABLE_VALIDATION=false

# Forçar modo de produção
# DEV_FORCE_PRODUCTION=false

# Nível de log
# Valores: debug | info | warn | error
# DEV_LOG_LEVEL=info

# ============================================================================
# CONFIGURAÇÕES DE SEGURANÇA
# ============================================================================

# Chave secreta para CSRF
# SECURITY_CSRF_SECRET=

# Habilitar rate limiting (padrão: true)
# SECURITY_RATE_LIMIT_ENABLED=true

# Origens permitidas para CORS (separadas por vírgula)
# SECURITY_CORS_ORIGINS=https://excel-copilot-eight.vercel.app,http://localhost:3000

# ============================================================================
# CONFIGURAÇÕES LEGADAS (COMPATIBILIDADE)
# ============================================================================
#
# As variáveis abaixo são mantidas para compatibilidade com o sistema antigo.
# Elas são automaticamente mapeadas para o novo sistema unificado.
# RECOMENDAÇÃO: Use as novas variáveis com prefixos padronizados.

# Legado: Controle de IA (REMOVIDO - usar AI_ENABLED e AI_USE_MOCK)
# ❌ USE_MOCK_AI → AI_USE_MOCK
# ❌ VERTEX_AI_ENABLED → AI_ENABLED
# ❌ FORCE_GOOGLE_MOCKS → AI_USE_MOCK
# ❌ NEXT_PUBLIC_DISABLE_VERTEX_AI → AI_ENABLED

# Legado: Variáveis desnecessárias (REMOVIDAS)
# ❌ APP_DESCRIPTION → Removida (não utilizada)
# ❌ SUPABASE_JWT_SECRET → Removida (não necessária)
# ❌ CSRF_SECRET → Removida (não utilizada)
# ❌ NEXT_PUBLIC_DISABLE_CSRF → Removida (não utilizada)
# ❌ DISABLE_ENV_VALIDATION → Removida (não utilizada)
# ❌ VERCEL_BUILD_DATABASE_MIGRATION → Removida (não utilizada)
# ❌ VERCEL_OIDC_TOKEN → Removida (não utilizada no código)

# Legado: Variáveis antigas de integração (PADRONIZADAS)
# ❌ VERCEL_API_TOKEN → MCP_VERCEL_TOKEN
# ❌ VERCEL_PROJECT_ID → MCP_VERCEL_PROJECT_ID
# ❌ VERCEL_TEAM_ID → MCP_VERCEL_TEAM_ID
# ❌ LINEAR_API_KEY → MCP_LINEAR_API_KEY
# ❌ GITHUB_TOKEN → MCP_GITHUB_TOKEN

# ============================================================================
# INSTRUÇÕES DE CONFIGURAÇÃO POR AMBIENTE
# ============================================================================

# DESENVOLVIMENTO LOCAL:
# 1. Copie este arquivo para .env.local
# 2. Configure DATABASE_URL para seu banco local
# 3. Configure NEXTAUTH_SECRET (gere com: openssl rand -base64 32)
# 4. Configure NEXTAUTH_URL=http://localhost:3000
# 5. Deixe AI_USE_MOCK=true para desenvolvimento
# 6. Configure OAuth providers se necessário

# PRODUÇÃO (VERCEL):
# 1. Configure todas as variáveis obrigatórias no painel do Vercel
# 2. Use URLs de produção (https://excel-copilot-eight.vercel.app)
# 3. Configure credenciais reais do Vertex AI
# 4. Use chaves de produção do Stripe
# 5. Configure OAuth com domínios de produção
# 6. Defina SECURITY_CORS_ORIGINS adequadamente

# TESTE:
# 1. Use banco de dados separado para testes
# 2. Configure AI_USE_MOCK=true
# 3. Use credenciais de teste para todos os serviços
# 4. Configure NEXTAUTH_URL para ambiente de teste

# ============================================================================
# VALIDAÇÃO E DIAGNÓSTICO
# ============================================================================

# Para validar sua configuração, execute:
# node scripts/diagnose-environment.js

# Para aplicar correções automáticas:
# node scripts/diagnose-environment.js --fix

# Para gerar relatório detalhado:
# node scripts/diagnose-environment.js --report

# ============================================================================
# SUPORTE E DOCUMENTAÇÃO
# ============================================================================

# Documentação completa: README.md
# Configuração de OAuth: docs/oauth-setup.md
# Configuração do Vertex AI: docs/vertex-ai-setup.md
# Troubleshooting: docs/troubleshooting.md

# Para suporte, abra uma issue em:
# https://github.com/seu-usuario/excel-copilot/issues

# ============================================================================
# CHANGELOG v2.0
# ============================================================================

# MUDANÇAS PRINCIPAIS:
# ✅ Nomenclatura padronizada com prefixos consistentes
# ✅ Lógica unificada para configuração de IA (elimina conflitos)
# ✅ Validação rigorosa com mensagens específicas
# ✅ Hierarquia clara de precedência entre variáveis
# ✅ Compatibilidade com sistema antigo mantida
# ✅ Documentação completa de todas as variáveis
# ✅ Utilitário de diagnóstico automático
# ✅ Templates específicos por ambiente

# VARIÁVEIS REMOVIDAS (OTIMIZAÇÃO PRIORIDADE ALTA):
# ❌ USE_MOCK_AI → AI_USE_MOCK (padronizado)
# ❌ VERTEX_AI_ENABLED → AI_ENABLED (padronizado)
# ❌ FORCE_GOOGLE_MOCKS → AI_USE_MOCK (consolidado)
# ❌ NEXT_PUBLIC_DISABLE_VERTEX_AI → AI_ENABLED (consolidado)
# ❌ APP_DESCRIPTION → Removida (não utilizada no código)
# ❌ SUPABASE_JWT_SECRET → Removida (não necessária)
# ❌ CSRF_SECRET → Removida (não utilizada no código)
# ❌ NEXT_PUBLIC_DISABLE_CSRF → Removida (não utilizada no código)
# ❌ DISABLE_ENV_VALIDATION → Removida (não utilizada no código)
# ❌ VERCEL_BUILD_DATABASE_MIGRATION → Removida (não utilizada no código)
# ❌ VERCEL_OIDC_TOKEN → Removida (não utilizada no código)

# VARIÁVEIS PADRONIZADAS (IMPLEMENTADAS):
# ✅ VERCEL_API_TOKEN → MCP_VERCEL_TOKEN (implementado)
# ✅ VERCEL_PROJECT_ID → MCP_VERCEL_PROJECT_ID (implementado)
# ✅ VERCEL_TEAM_ID → MCP_VERCEL_TEAM_ID (implementado)
# ✅ LINEAR_API_KEY → MCP_LINEAR_API_KEY (implementado)
# ✅ GITHUB_TOKEN → MCP_GITHUB_TOKEN (implementado)
