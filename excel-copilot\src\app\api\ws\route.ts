import { NextRequest } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { Server, Socket } from 'socket.io';

import { logger } from '@/lib/logger';
import { socketMessageSchema } from '@/schemas/socket';
import { ServerWebSocket } from '@/server/websocket';
import { CellUpdateMessage, UserActivityMessage } from '@/types/socket';
import { ApiResponse } from '@/utils/api-response';

/**
 * WebSocket para colaboração em tempo real
 * Esta rota fornece uma conexão de WebSockets para atualizações em tempo real
 */

// Configurar rota como dinâmica para permitir uso de headers
export const dynamic = 'force-dynamic';

// Manter singleton do servidor Socket.io entre requests
let socketIoServer: Server;
let isInitialized = false;

export async function GET(req: NextRequest) {
  try {
    // Verificar se a requisição está tentando iniciar uma conexão WebSocket
    const upgrade = req.headers.get('upgrade');
    if (upgrade !== 'websocket') {
      return ApiResponse.badRequest('Requisição não é uma conexão WebSocket');
    }

    // Verificar autenticação
    const session = await getServerSession();
    if (!session || !session.user) {
      return ApiResponse.unauthorized('Usuário não autenticado');
    }

    // Inicializar servidor Socket.io se ainda não foi feito
    if (!isInitialized) {
      socketIoServer = ServerWebSocket.init();
      isInitialized = true;

      // Configurar manipuladores de eventos
      socketIoServer.on('connection', (socket: Socket) => {
        const userId = (session.user as { id?: string })?.id;
        const userName = session.user?.name || 'Usuário anônimo';

        logger.debug('Nova conexão WebSocket', {
          socketId: socket.id,
          userId,
        });

        // Associar usuário ao socket
        socket.data.userId = userId;
        socket.data.userName = userName;

        // Manipular mensagens
        socket.on('message', async (data: unknown) => {
          try {
            // Validar mensagem com schema Zod
            const result = socketMessageSchema.safeParse(data);
            if (!result.success) {
              socket.emit('error', {
                type: 'error',
                message: 'Formato de mensagem inválido',
                details: result.error.format(),
              });
              return;
            }

            const message = result.data;

            // Manipular diferentes tipos de mensagens
            switch (message.type) {
              case 'join_room':
                await handleJoinRoom(socket, message.workbookId);
                break;

              case 'leave_room':
                handleLeaveRoom(socket, message.workbookId);
                break;

              case 'cell_update':
                handleCellUpdate(socket, message as CellUpdateMessage);
                break;

              case 'user_activity':
                handleUserActivity(socket, message as UserActivityMessage);
                break;

              case 'heartbeat':
                // Responder heartbeat para manter conexão ativa
                socket.emit('pong', { timestamp: Date.now() });
                break;

              default:
                socket.emit('error', {
                  type: 'error',
                  message: 'Tipo de mensagem desconhecido',
                });
            }
          } catch (error) {
            logger.error('Erro ao processar mensagem WebSocket', {
              socketId: socket.id,
              userId: socket.data.userId,
              error,
            });

            socket.emit('error', {
              type: 'error',
              message: 'Erro ao processar mensagem',
            });
          }
        });

        // Manipular desconexão
        socket.on('disconnect', () => {
          handleDisconnect(socket);
        });
      });
    }

    // Retornar uma resposta para iniciar o handshake
    return new Response(null, {
      status: 101,
      headers: {
        Upgrade: 'websocket',
        Connection: 'Upgrade',
      },
    });
  } catch (error) {
    logger.error('Erro ao configurar WebSocket', error);
    return ApiResponse.error('Erro ao configurar WebSocket');
  }
}

// Manipulador para entrar em uma sala
async function handleJoinRoom(socket: Socket, workbookId: string) {
  try {
    // Verificar se o usuário tem acesso ao workbook
    // Isso pode ser feito com uma verificação no banco de dados
    const hasAccess = await checkWorkbookAccess(workbookId, socket.data.userId);
    if (!hasAccess) {
      socket.emit('error', {
        type: 'error',
        message: 'Sem permissão para acessar este workbook',
      });
      return;
    }

    // Entrar na sala do workbook
    socket.join(`workbook:${workbookId}`);

    // Notificar outros usuários
    socket.to(`workbook:${workbookId}`).emit('user_presence', {
      type: 'user_presence',
      workbookId,
      users: [
        {
          id: socket.data.userId,
          name: socket.data.userName,
          status: 'connected',
          activity: 'idle',
        },
      ],
    });

    // Enviar lista de usuários conectados ao novo usuário
    const connectedSockets = await socketIoServer.in(`workbook:${workbookId}`).fetchSockets();
    const connectedUsers = connectedSockets.map(s => ({
      id: s.data.userId,
      name: s.data.userName,
      status: 'connected',
      activity: s.data.activity || 'idle',
      position: s.data.position,
    }));

    socket.emit('user_presence', {
      type: 'user_presence',
      workbookId,
      users: connectedUsers,
    });

    logger.debug('Usuário entrou na sala', {
      workbookId,
      userId: socket.data.userId,
      socketId: socket.id,
    });
  } catch (error) {
    logger.error('Erro ao entrar na sala', {
      workbookId,
      userId: socket.data.userId,
      error,
    });

    socket.emit('error', {
      type: 'error',
      message: 'Erro ao entrar na sala',
    });
  }
}

// Verificar permissão de acesso ao workbook
async function checkWorkbookAccess(workbookId: string, userId: string): Promise<boolean> {
  try {
    // Implementar verificação baseada no banco de dados
    // Exemplo: um usuário tem acesso se for o dono ou o workbook for público
    // Para propósitos do exemplo, retornamos true
    return true;
  } catch (error) {
    logger.error('Erro ao verificar acesso ao workbook', { workbookId, userId, error });
    return false;
  }
}

// Manipulador para sair de uma sala
function handleLeaveRoom(socket: Socket, workbookId: string) {
  socket.leave(`workbook:${workbookId}`);

  // Notificar outros usuários
  socket.to(`workbook:${workbookId}`).emit('user_presence', {
    type: 'user_presence',
    workbookId,
    users: [
      {
        id: socket.data.userId,
        name: socket.data.userName,
        status: 'disconnected',
      },
    ],
  });

  logger.debug('Usuário saiu da sala', {
    workbookId,
    userId: socket.data.userId,
  });
}

// Manipulador para atualização de célula
function handleCellUpdate(socket: Socket, message: CellUpdateMessage) {
  const { workbookId, sheetId, cell } = message;

  // Transmitir para todos os outros clientes na sala
  socket.to(`workbook:${workbookId}`).emit('cell_updated', {
    type: 'cell_updated',
    workbookId,
    sheetId,
    cell,
    userId: socket.data.userId,
    userName: socket.data.userName,
    timestamp: Date.now(),
  });

  logger.debug('Célula atualizada', {
    workbookId,
    sheetId,
    cell: `${cell.row},${cell.col}`,
    userId: socket.data.userId,
  });
}

// Manipulador para atividade do usuário
function handleUserActivity(socket: Socket, message: UserActivityMessage) {
  const { workbookId, sheetId, activity, position } = message;

  // Armazenar posição atual do usuário
  socket.data.activity = activity;
  if (position) {
    socket.data.position = {
      sheetId,
      ...position,
    };
  }

  // Transmitir para todos os outros clientes na sala
  socket.to(`workbook:${workbookId}`).emit('user_presence', {
    type: 'user_presence',
    workbookId,
    users: [
      {
        id: socket.data.userId,
        name: socket.data.userName,
        status: 'connected',
        activity,
        position: socket.data.position,
      },
    ],
  });
}

// Manipulador para desconexão
function handleDisconnect(socket: Socket) {
  // Obter salas das quais o socket faz parte
  const rooms = Array.from(socket.rooms as Set<string>)
    .filter(room => room.startsWith('workbook:'))
    .map(room => room.replace('workbook:', ''));

  // Notificar todos os workbooks que o usuário estava editando
  rooms.forEach(workbookId => {
    socket.to(`workbook:${workbookId}`).emit('user_presence', {
      type: 'user_presence',
      workbookId,
      users: [
        {
          id: socket.data.userId,
          name: socket.data.userName,
          status: 'disconnected',
        },
      ],
    });
  });

  logger.debug('Usuário desconectado', {
    userId: socket.data.userId,
    socketId: socket.id,
    rooms,
  });
}
