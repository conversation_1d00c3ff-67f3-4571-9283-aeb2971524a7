/**
 * Definições de tipos para processamento de IA
 */

import { ExcelOperation } from './global';

// Contexto para processamento de IA
export interface AIProcessingContext {
  workbookId?: string;
  workbookName?: string;
  userId?: string;
  sheets?: SheetContext[];
  activeSheet?: string;
  cellSelection?: CellSelection;
  language?: string;
  previousMessages?: AIMessage[];
  systemContext?: string;
  preferredInsightTypes?: string[];
}

// Resultado do processamento de IA
export interface AIProcessingResult {
  success: boolean;
  operations: ExcelOperation[];
  explanation?: string;
  error?: string;
  message?: string;
}

// Resposta da API de IA
export interface AIModelResponse {
  content?: string;
  operations?: ExcelOperation[];
  explanation?: string;
  error?: string;
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
}

// Configuração para processamento de IA
export interface AIProcessorConfig {
  model?: string;
  maxTokens?: number;
  temperature?: number;
  systemPrompt?: string;
  operationTypes?: string[];
}

// Tipos de operação permitidos
export type AIOperationType = 'FORMULA' | 'FILTER' | 'SORT' | 'FORMAT' | 'CHART' | 'ANALYSIS';

/**
 * Interface para resposta de análise IA
 */
export interface AIAnalysisResponse {
  summary: string;
  insights: string[];
  suggestedOperations?: string[];
}

/**
 * Interface para resposta de fórmula IA
 */
export interface FormulaResponse {
  formula: string;
  explanation: string;
}

/**
 * Interface para seleção de células
 */
export interface CellSelection {
  startRow: number;
  startCol: number;
  endRow: number;
  endCol: number;
}

/**
 * Interface para contexto de planilha
 */
export interface SheetContext {
  id: string;
  name: string;
  data: string | any;
}

/**
 * Interface para mensagem de IA
 */
export interface AIMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp?: number;
}

/**
 * Interface para opções de geração de conteúdo IA
 */
export interface GenerationOptions {
  temperature?: number;
  maxTokens?: number;
  model?: string;
  includeContext?: boolean;
}

/**
 * Interface para resultados de formatação
 */
export interface FormattingResult {
  cells: CellFormatting[];
  summary: string;
}

/**
 * Interface para formatação de célula
 */
export interface CellFormatting {
  row: number;
  col: number;
  format: string;
}

/**
 * Interface para a classe AIProvider
 */
export interface AIProvider {
  processPrompt(prompt: string, options?: any): Promise<string>;
  analyzeExcelData(data: any, options?: any): Promise<any>;
  generateExcelFormula(description: string, options?: any): Promise<any>;
  healthCheck(): Promise<boolean>;
}
