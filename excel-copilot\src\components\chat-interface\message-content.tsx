'use client';

import { motion } from 'framer-motion';
import { Check, Loader2 } from 'lucide-react';

import { SIZES, ANIMATIONS, TYPOGRAPHY, LAYOUTS, COLORS, SPACING } from '@/lib/design-tokens';
import sanitizeHtml from '@/lib/security/sanitize-html';
import { cn } from '@/lib/utils';

interface MessageContentProps {
  content: string;
  loading?: boolean;
}

export function MessageContent({ content, loading }: MessageContentProps) {
  if (loading) {
    return (
      <motion.div
        initial={{ opacity: 0.5 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.8, repeat: Infinity, repeatType: 'reverse' }}
        className={cn('flex items-center', SPACING.gap.xs)}
      >
        <Loader2 className={cn(SIZES.icon.sm, ANIMATIONS.spin)} />
        <span>Processando...</span>
      </motion.div>
    );
  }

  // Detectar e processar seção de ações executadas
  const actionsSectionMatch = content.match(/\*\*Ações executadas:\*\*\n([\s\S]*)/);

  if (actionsSectionMatch && actionsSectionMatch[1]) {
    const [fullMatch, actionsList] = actionsSectionMatch;
    const mainContent = content.replace(fullMatch, '');

    // Sanitizar o conteúdo antes de aplicar formatação Markdown
    const sanitizedContent = sanitizeHtml(mainContent);
    const formattedContent = sanitizedContent
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\n/g, '<br />');

    return (
      <>
        <div dangerouslySetInnerHTML={{ __html: formattedContent }} />

        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
          className={cn('mt-3 pt-3 border-t', COLORS.border)}
        >
          <strong>Ações executadas:</strong>
          <ul
            className={cn('mt-1', SPACING.gap.xs, TYPOGRAPHY.size.sm)}
            role="list"
            aria-label="Lista de ações executadas"
          >
            {actionsList
              .split('\n')
              .filter(line => line.trim().startsWith('-'))
              .map((line, i) => (
                <motion.li
                  key={i}
                  initial={{ opacity: 0, x: -5 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.2, delay: i * 0.05 }}
                  className={cn(LAYOUTS.start)}
                >
                  <Check
                    className={cn(SIZES.icon.sm, 'mr-1', COLORS.intent.success.base, 'mt-0.5')}
                    aria-hidden="true"
                  />
                  <span>{sanitizeHtml(line.replace(/^- /, ''))}</span>
                </motion.li>
              ))}
          </ul>
        </motion.div>
      </>
    );
  }

  // Para mensagens sem ações, aplicar formatação básica
  const sanitizedContent = sanitizeHtml(content);
  return (
    <div
      dangerouslySetInnerHTML={{
        __html: sanitizedContent
          .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
          .replace(/\n/g, '<br />'),
      }}
    />
  );
}
