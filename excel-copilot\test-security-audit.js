#!/usr/bin/env node

/**
 * AUDITORIA DE SEGURANÇA - SISTEMA DE MONETIZAÇÃO
 * Script para testar vulnerabilidades e verificar implementação de limites
 */

import fs from 'fs';
import path from 'path';

console.log('🔍 INICIANDO AUDITORIA DE SEGURANÇA - SISTEMA DE MONETIZAÇÃO\n');

// ============================================================================
// FASE 1: ANÁLISE ESTÁTICA DE CÓDIGO
// ============================================================================

console.log('📊 FASE 1: ANÁLISE ESTÁTICA DE CÓDIGO');
console.log('=====================================\n');

const vulnerabilities = [];
const securityIssues = [];
const recommendations = [];

// Verificar arquivos críticos
const criticalFiles = [
  'src/lib/subscription-limits.ts',
  'src/app/api/workbooks/route.ts',
  'src/app/api/chat/route.ts',
  'src/app/api/workbook/save/route.ts',
  'src/server/services/workbook-service.ts',
  'src/lib/middleware/plan-based-rate-limiter.ts',
];

console.log('🔍 Verificando arquivos críticos...\n');

criticalFiles.forEach(file => {
  const filePath = path.join(__dirname, file);
  if (fs.existsSync(filePath)) {
    console.log(`✅ ${file} - Encontrado`);

    const content = fs.readFileSync(filePath, 'utf8');

    // Verificar vulnerabilidades específicas
    checkFileVulnerabilities(file, content);
  } else {
    console.log(`❌ ${file} - Não encontrado`);
    vulnerabilities.push({
      file,
      type: 'MISSING_FILE',
      severity: 'HIGH',
      description: 'Arquivo crítico não encontrado',
    });
  }
});

function checkFileVulnerabilities(file, content) {
  // 1. Verificar se há validação server-side
  if (file.includes('api/') && !content.includes('getServerSession')) {
    if (!content.includes('authMiddleware') && !content.includes('session?.user')) {
      vulnerabilities.push({
        file,
        type: 'MISSING_AUTH',
        severity: 'CRITICAL',
        description: 'Endpoint de API sem verificação de autenticação',
      });
    }
  }

  // 2. Verificar bypass de limites
  if (content.includes('userId = ') && content.includes('guest')) {
    vulnerabilities.push({
      file,
      type: 'GUEST_ACCESS',
      severity: 'HIGH',
      description: 'Permite acesso como usuário guest, possível bypass de limites',
    });
  }

  // 3. Verificar validação de entrada
  if (file.includes('api/') && content.includes('req.json()')) {
    if (!content.includes('.safeParse') && !content.includes('zod')) {
      securityIssues.push({
        file,
        type: 'MISSING_VALIDATION',
        severity: 'MEDIUM',
        description: 'Falta validação de entrada com schema',
      });
    }
  }

  // 4. Verificar rate limiting
  if (file.includes('api/') && !content.includes('rateLimiter') && !content.includes('rateLimit')) {
    securityIssues.push({
      file,
      type: 'MISSING_RATE_LIMIT',
      severity: 'MEDIUM',
      description: 'Endpoint sem rate limiting',
    });
  }

  // 5. Verificar logs de segurança
  if (content.includes('subscription') && !content.includes('logUserAction')) {
    recommendations.push({
      file,
      type: 'MISSING_AUDIT_LOG',
      severity: 'LOW',
      description: 'Falta log de auditoria para ações de assinatura',
    });
  }

  // 6. Verificar fallback seguro
  if (content.includes('catch') && content.includes('allowed: true')) {
    vulnerabilities.push({
      file,
      type: 'UNSAFE_FALLBACK',
      severity: 'HIGH',
      description: 'Fallback inseguro permite acesso em caso de erro',
    });
  }

  // 7. Verificar cache de bypass
  if (content.includes('cache') && content.includes('userPlan')) {
    if (!content.includes('PLAN_VERIFICATION_INTERVAL')) {
      securityIssues.push({
        file,
        type: 'CACHE_BYPASS_RISK',
        severity: 'MEDIUM',
        description: 'Cache de planos sem expiração adequada',
      });
    }
  }
}

// ============================================================================
// FASE 2: VERIFICAÇÃO DE CONFIGURAÇÃO
// ============================================================================

console.log('\n📋 FASE 2: VERIFICAÇÃO DE CONFIGURAÇÃO');
console.log('======================================\n');

// Verificar se as constantes de limite estão definidas corretamente
const limitsFile = path.join(__dirname, 'src/lib/subscription-limits.ts');
if (fs.existsSync(limitsFile)) {
  const limitsContent = fs.readFileSync(limitsFile, 'utf8');

  console.log('🔍 Verificando definição de limites...');

  const requiredLimits = [
    'MAX_WORKBOOKS',
    'MAX_CELLS',
    'MAX_CHARTS',
    'ADVANCED_AI_COMMANDS',
    'RATE_LIMITS',
  ];

  requiredLimits.forEach(limit => {
    if (limitsContent.includes(limit)) {
      console.log(`✅ ${limit} - Definido`);
    } else {
      console.log(`❌ ${limit} - Não encontrado`);
      vulnerabilities.push({
        file: 'subscription-limits.ts',
        type: 'MISSING_LIMIT_DEFINITION',
        severity: 'HIGH',
        description: `Limite ${limit} não está definido`,
      });
    }
  });

  // Verificar se os planos estão definidos
  const requiredPlans = ['FREE', 'PRO_MONTHLY', 'PRO_ANNUAL'];
  requiredPlans.forEach(plan => {
    if (limitsContent.includes(plan)) {
      console.log(`✅ Plano ${plan} - Definido`);
    } else {
      console.log(`❌ Plano ${plan} - Não encontrado`);
    }
  });
}

// ============================================================================
// FASE 3: RELATÓRIO DE VULNERABILIDADES
// ============================================================================

console.log('\n🚨 FASE 3: RELATÓRIO DE VULNERABILIDADES');
console.log('=========================================\n');

console.log(`📊 RESUMO DA AUDITORIA:`);
console.log(
  `- Vulnerabilidades CRÍTICAS: ${vulnerabilities.filter(v => v.severity === 'CRITICAL').length}`
);
console.log(
  `- Vulnerabilidades ALTAS: ${vulnerabilities.filter(v => v.severity === 'HIGH').length}`
);
console.log(`- Problemas MÉDIOS: ${securityIssues.filter(v => v.severity === 'MEDIUM').length}`);
console.log(`- Recomendações: ${recommendations.length}\n`);

if (vulnerabilities.length > 0) {
  console.log('🚨 VULNERABILIDADES ENCONTRADAS:');
  vulnerabilities.forEach((vuln, index) => {
    console.log(`\n${index + 1}. [${vuln.severity}] ${vuln.type}`);
    console.log(`   Arquivo: ${vuln.file}`);
    console.log(`   Descrição: ${vuln.description}`);
  });
}

if (securityIssues.length > 0) {
  console.log('\n⚠️  PROBLEMAS DE SEGURANÇA:');
  securityIssues.forEach((issue, index) => {
    console.log(`\n${index + 1}. [${issue.severity}] ${issue.type}`);
    console.log(`   Arquivo: ${issue.file}`);
    console.log(`   Descrição: ${issue.description}`);
  });
}

if (recommendations.length > 0) {
  console.log('\n💡 RECOMENDAÇÕES:');
  recommendations.forEach((rec, index) => {
    console.log(`\n${index + 1}. ${rec.type}`);
    console.log(`   Arquivo: ${rec.file}`);
    console.log(`   Descrição: ${rec.description}`);
  });
}

// ============================================================================
// FASE 4: CLASSIFICAÇÃO DE RISCO
// ============================================================================

console.log('\n📈 FASE 4: CLASSIFICAÇÃO DE RISCO');
console.log('=================================\n');

const criticalCount = vulnerabilities.filter(v => v.severity === 'CRITICAL').length;
const highCount = vulnerabilities.filter(v => v.severity === 'HIGH').length;
const mediumCount = securityIssues.filter(v => v.severity === 'MEDIUM').length;

let overallRisk = 'BAIXO';
if (criticalCount > 0) {
  overallRisk = 'CRÍTICO';
} else if (highCount > 2) {
  overallRisk = 'ALTO';
} else if (highCount > 0 || mediumCount > 3) {
  overallRisk = 'MÉDIO';
}

console.log(`🎯 RISCO GERAL DO SISTEMA: ${overallRisk}`);

// Salvar relatório
const report = {
  timestamp: new Date().toISOString(),
  overallRisk,
  summary: {
    critical: criticalCount,
    high: highCount,
    medium: mediumCount,
    recommendations: recommendations.length,
  },
  vulnerabilities,
  securityIssues,
  recommendations,
};

fs.writeFileSync('security-audit-report.json', JSON.stringify(report, null, 2));
console.log('\n📄 Relatório salvo em: security-audit-report.json');

console.log('\n✅ AUDITORIA CONCLUÍDA');
