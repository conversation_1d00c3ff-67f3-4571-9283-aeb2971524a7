import { logSuspiciousActivity } from '@/lib/auth-audit-logger';
import { toNullableString } from '@/server/db/utils';

/**
 * Sistema de monitoramento de anomalias de segurança
 * Detecta padrões suspeitos e atividades maliciosas
 */

interface SecurityMetrics {
  ipAddress: string;
  userAgent?: string | null;
  userId?: string | null;
  endpoint: string;
  timestamp: Date;
  success: boolean;
}

interface AnomalyPattern {
  type:
    | 'RAPID_REQUESTS'
    | 'MULTIPLE_FAILURES'
    | 'UNUSUAL_LOCATION'
    | 'SUSPICIOUS_USER_AGENT'
    | 'BRUTE_FORCE';
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  description: string;
  metadata?: Record<string, string | number | boolean>;
}

// Cache para métricas de segurança (em produção, usar Redis)
const securityMetricsCache = new Map<string, SecurityMetrics[]>();

// Configurações de detecção de anomalias
const ANOMALY_CONFIG = {
  // Máximo de requisições por IP por minuto
  maxRequestsPerMinute: 60,
  // Máximo de falhas de login por IP por hora
  maxLoginFailuresPerHour: 10,
  // Janela de tempo para análise (em milissegundos)
  analysisWindowMs: 60 * 60 * 1000, // 1 hora
  // User agents suspeitos (bots, scanners)
  suspiciousUserAgents: [
    'curl',
    'wget',
    'python-requests',
    'bot',
    'crawler',
    'scanner',
    'sqlmap',
    'nikto',
  ],
};

/**
 * Registra uma métrica de segurança
 */
export function recordSecurityMetric(metric: SecurityMetrics): void {
  const key = `${metric.ipAddress}-${metric.endpoint}`;

  if (!securityMetricsCache.has(key)) {
    securityMetricsCache.set(key, []);
  }

  const metrics = securityMetricsCache.get(key)!;
  metrics.push(metric);

  // Manter apenas métricas da última hora
  const cutoff = new Date(Date.now() - ANOMALY_CONFIG.analysisWindowMs);
  const filteredMetrics = metrics.filter(m => m.timestamp > cutoff);
  securityMetricsCache.set(key, filteredMetrics);

  // Analisar anomalias
  analyzeAnomalies(metric, filteredMetrics);
}

/**
 * Analisa métricas em busca de anomalias
 */
async function analyzeAnomalies(
  currentMetric: SecurityMetrics,
  recentMetrics: SecurityMetrics[]
): Promise<void> {
  const anomalies: AnomalyPattern[] = [];

  // 1. Detectar requisições muito rápidas
  const lastMinuteMetrics = recentMetrics.filter(
    m => m.timestamp > new Date(Date.now() - 60 * 1000)
  );

  if (lastMinuteMetrics.length > ANOMALY_CONFIG.maxRequestsPerMinute) {
    anomalies.push({
      type: 'RAPID_REQUESTS',
      severity: 'HIGH',
      description: `${lastMinuteMetrics.length} requisições em 1 minuto do IP ${currentMetric.ipAddress}`,
      metadata: {
        requestCount: lastMinuteMetrics.length,
        timeframe: '1 minute',
      },
    });
  }

  // 2. Detectar múltiplas falhas de login
  const loginFailures = recentMetrics.filter(m => m.endpoint.includes('/auth/') && !m.success);

  if (loginFailures.length > ANOMALY_CONFIG.maxLoginFailuresPerHour) {
    anomalies.push({
      type: 'MULTIPLE_FAILURES',
      severity: 'CRITICAL',
      description: `${loginFailures.length} falhas de login em 1 hora do IP ${currentMetric.ipAddress}`,
      metadata: {
        failureCount: loginFailures.length,
        timeframe: '1 hour',
      },
    });
  }

  // 3. Detectar User-Agent suspeito
  if (currentMetric.userAgent) {
    const isSuspiciousUA = ANOMALY_CONFIG.suspiciousUserAgents.some(suspicious =>
      currentMetric.userAgent!.toLowerCase().includes(suspicious.toLowerCase())
    );

    if (isSuspiciousUA) {
      anomalies.push({
        type: 'SUSPICIOUS_USER_AGENT',
        severity: 'MEDIUM',
        description: `User-Agent suspeito detectado: ${currentMetric.userAgent}`,
        metadata: {
          userAgent: currentMetric.userAgent,
        },
      });
    }
  }

  // 4. Detectar padrão de força bruta
  const authEndpointMetrics = recentMetrics.filter(m => m.endpoint.includes('/auth/'));
  const successRate =
    authEndpointMetrics.length > 0
      ? authEndpointMetrics.filter(m => m.success).length / authEndpointMetrics.length
      : 1;

  if (authEndpointMetrics.length > 20 && successRate < 0.1) {
    anomalies.push({
      type: 'BRUTE_FORCE',
      severity: 'CRITICAL',
      description: `Possível ataque de força bruta: ${authEndpointMetrics.length} tentativas com ${(successRate * 100).toFixed(1)}% de sucesso`,
      metadata: {
        attempts: authEndpointMetrics.length,
        successRate: successRate,
      },
    });
  }

  // Processar anomalias detectadas
  for (const anomaly of anomalies) {
    await handleAnomaly(currentMetric, anomaly);
  }
}

/**
 * Processa uma anomalia detectada
 */
async function handleAnomaly(metric: SecurityMetrics, anomaly: AnomalyPattern): Promise<void> {
  try {
    // Log da anomalia
    console.warn(`🚨 ANOMALIA DE SEGURANÇA [${anomaly.severity}]: ${anomaly.description}`, {
      type: anomaly.type,
      ip: metric.ipAddress,
      userAgent: metric.userAgent,
      userId: metric.userId,
      metadata: anomaly.metadata,
    });

    // Registrar no sistema de auditoria
    await logSuspiciousActivity(
      metric.userId || undefined,
      undefined,
      `${anomaly.type}: ${anomaly.description}`,
      metric.ipAddress,
      metric.userAgent || undefined,
      {
        anomalyType: anomaly.type,
        severity: anomaly.severity,
        ...anomaly.metadata,
      }
    );

    // Ações automáticas baseadas na severidade
    if (anomaly.severity === 'CRITICAL') {
      await handleCriticalAnomaly(metric, anomaly);
    } else if (anomaly.severity === 'HIGH') {
      await handleHighSeverityAnomaly(metric, anomaly);
    }
  } catch (error) {
    console.error('Erro ao processar anomalia de segurança:', error);
  }
}

/**
 * Processa anomalias críticas
 */
async function handleCriticalAnomaly(
  metric: SecurityMetrics,
  anomaly: AnomalyPattern
): Promise<void> {
  // Para anomalias críticas, podemos implementar bloqueio automático
  console.error(`🔴 ANOMALIA CRÍTICA: ${anomaly.description}`);

  // Se temos um userId, marcar como suspeito
  if (metric.userId) {
    try {
      // Usar importação dinâmica para evitar problemas no Edge Runtime
      const { prisma } = await import('@/server/db/client');
      await prisma.user.update({
        where: { id: metric.userId },
        data: {
          isSuspicious: true,
        },
      });
    } catch (error) {
      console.error('Erro ao marcar usuário como suspeito:', error);
    }
  }

  // TODO: Implementar bloqueio automático de IP em casos extremos
  // TODO: Enviar alerta para administradores
}

/**
 * Processa anomalias de alta severidade
 */
async function handleHighSeverityAnomaly(
  metric: SecurityMetrics,
  anomaly: AnomalyPattern
): Promise<void> {
  console.warn(`🟡 ANOMALIA ALTA SEVERIDADE: ${anomaly.description}`);

  // TODO: Implementar throttling adicional para o IP
  // TODO: Enviar notificação para equipe de segurança
}

/**
 * Obtém estatísticas de segurança
 */
export function getSecurityStats(): {
  totalMetrics: number;
  uniqueIPs: number;
  suspiciousIPs: number;
  recentAnomalies: number;
} {
  const allMetrics: SecurityMetrics[] = [];
  const uniqueIPs = new Set<string>();
  const suspiciousIPs = new Set<string>();

  for (const metrics of securityMetricsCache.values()) {
    allMetrics.push(...metrics);
  }

  for (const metric of allMetrics) {
    uniqueIPs.add(metric.ipAddress);

    // Considerar IP suspeito se tem muitas falhas
    const ipMetrics = allMetrics.filter(m => m.ipAddress === metric.ipAddress);
    const failures = ipMetrics.filter(m => !m.success);

    if (failures.length > 5) {
      suspiciousIPs.add(metric.ipAddress);
    }
  }

  return {
    totalMetrics: allMetrics.length,
    uniqueIPs: uniqueIPs.size,
    suspiciousIPs: suspiciousIPs.size,
    recentAnomalies: 0, // TODO: Implementar contador de anomalias
  };
}

/**
 * Limpa métricas antigas
 */
export function cleanupOldMetrics(): void {
  const cutoff = new Date(Date.now() - ANOMALY_CONFIG.analysisWindowMs);

  for (const [key, metrics] of securityMetricsCache.entries()) {
    const filteredMetrics = metrics.filter(m => m.timestamp > cutoff);

    if (filteredMetrics.length === 0) {
      securityMetricsCache.delete(key);
    } else {
      securityMetricsCache.set(key, filteredMetrics);
    }
  }
}

/**
 * Middleware para registrar métricas de segurança
 */
export function createSecurityMetric(
  ipAddress: string,
  endpoint: string,
  success: boolean,
  userAgent?: string,
  userId?: string
): void {
  recordSecurityMetric({
    ipAddress,
    userAgent: toNullableString(userAgent),
    userId: toNullableString(userId),
    endpoint,
    timestamp: new Date(),
    success,
  });
}
