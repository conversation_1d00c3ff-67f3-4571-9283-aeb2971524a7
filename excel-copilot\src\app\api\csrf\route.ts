export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

import { NextRequest, NextResponse } from 'next/server';

import { logger } from '@/lib/logger';
import { generateCSRFToken } from '@/lib/security/csrf-protection';

/**
 * Gera um novo token CSRF e retorna para o cliente
 */
export async function GET(_req: NextRequest) {
  try {
    // Gerar um novo token CSRF com os parâmetros necessários
    const secret = process.env.SECURITY_CSRF_SECRET || 'default-csrf-secret';
    const sessionData = Date.now().toString(); // Usar timestamp como dado de sessão
    const token = await generateCSRFToken(secret, sessionData);

    // Criar resposta
    const response = NextResponse.json({
      csrfToken: token,
      expires: Date.now() + 60 * 60 * 1000, // 1 hora
    });

    // Definir cookie na resposta
    response.cookies.set({
      name: 'csrf_token',
      value: token,
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      path: '/',
      maxAge: 60 * 60, // 1 hora em segundos
    });

    return response;
  } catch (error) {
    logger.error('Erro ao gerar token CSRF:', error);
    return NextResponse.json({ error: 'Erro interno ao gerar token CSRF' }, { status: 500 });
  }
}

/**
 * Para verificar a validade de um token CSRF existente
 */
export async function POST(_req: NextRequest) {
  try {
    // Este endpoint existe apenas para validar que o cookie está sendo definido corretamente
    // O middleware já implementa a validação real de CSRF
    return NextResponse.json({
      valid: true,
      message: 'O token CSRF foi validado com sucesso',
    });
  } catch (error) {
    logger.error('Erro ao validar token CSRF:', error);
    return NextResponse.json({ error: 'Erro interno ao validar token CSRF' }, { status: 500 });
  }
}
