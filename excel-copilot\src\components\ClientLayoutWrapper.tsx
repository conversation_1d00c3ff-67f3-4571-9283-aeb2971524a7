'use client';

import { useEffect } from 'react';

interface ClientLayoutWrapperProps {
  children: React.ReactNode;
  className?: string;
  interClassName?: string;
}

/**
 * Componente cliente para comportamentos específicos de cliente do layout
 * Permite executar código que depende do navegador
 */
export default function ClientLayoutWrapper({
  children,
  className: _className, // Renomeado para indicar que não é usado
  interClassName: _interClassName, // Renomeado para indicar que não é usado
}: ClientLayoutWrapperProps) {
  // Scripts e comportamentos específicos do cliente
  useEffect(() => {
    // Precarregar recursos críticos ao início
    const imagesToPreload = ['/images/excel-copilot-icon.svg', '/favicon.ico'];

    imagesToPreload.forEach(imgSrc => {
      const img = new Image();
      img.src = imgSrc;
    });

    // Prefetch rotas principais para navegação rápida
    const routesToPrefetch = ['/dashboard'];

    if ('connection' in navigator && (navigator as any).connection.saveData === false) {
      try {
        routesToPrefetch.forEach(route => {
          const link = document.createElement('link');
          link.rel = 'prefetch';
          link.href = route;
          document.head.appendChild(link);
        });
      } catch {
        // Erro de prefetch não é crítico, então apenas ignoramos silenciosamente
      }
    }

    // Resolver problemas com Web Workers e THREE.js
    if (window.Worker) {
      try {
        // Helper para substituir URLs de recursos externos
        (window as any).__resolveExternalResource = function (url: string) {
          // Cache local para evitar requisições repetidas
          if (!(window as any).__resourceCache) (window as any).__resourceCache = {};
          if ((window as any).__resourceCache[url]) return (window as any).__resourceCache[url];

          // Lista de domínios que precisam de proxy
          const externalDomains = ['raw.githack.com', 'fonts.gstatic.com'];

          // Verificar se o URL é para um domínio que precisa de proxy
          const needsProxy = externalDomains.some(domain => url.includes(domain));

          if (needsProxy) {
            // Usar cache local ou versão do site para recursos bloqueados por CSP
            const filename = url.split('/').pop();
            const localPath = '/assets/' + filename;
            // Redirecionamento de recurso externo
            (window as any).__resourceCache[url] = localPath;
            return localPath;
          }

          (window as any).__resourceCache[url] = url;
          return url;
        };
      } catch {
        // Erro ao configurar proxy de recursos, não é crítico para a aplicação principal
      }
    }
  }, []);

  return (
    <>
      {children}
      <div id="portal-root" aria-hidden="true"></div>
    </>
  );
}
