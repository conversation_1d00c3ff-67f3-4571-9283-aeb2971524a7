import React from 'react';

interface CommandExamplesProps {
  onSelect?: (command: string) => void;
}

// Mock para o componente CommandExamples
const CommandExamplesMock: React.FC<CommandExamplesProps> = ({ onSelect }) => {
  // Dados de exemplo para o componente
  const categories = [
    {
      id: 'matematica',
      name: '<PERSON><PERSON><PERSON><PERSON> Matemá<PERSON>',
      icon: 'Calculator',
      commands: [
        'Some os valores da coluna B',
        'Calcule a média da coluna A',
        'Encontre o valor máximo na linha 5',
      ],
    },
    {
      id: 'formatacao',
      name: 'Formata<PERSON>',
      icon: 'Palette',
      commands: [
        'Aplique negrito na linha de cabeçalho',
        'Alinhe o texto ao centro na coluna C',
        'Formate a coluna D como moeda',
      ],
    },
    {
      id: 'graficos',
      name: 'Gráfic<PERSON>',
      icon: 'PieChart',
      commands: [
        'Crie um gráfico de barras com os dados da tabela',
        'Adicione um gráfico de linha para as colunas A e B',
        'Faça um gráfico de pizza com a distribuição por região',
      ],
    },
  ];

  // Estado para categoria selecionada
  const [selectedCategory, setSelectedCategory] = React.useState(categories[0]?.id || 'matematica');

  // Função para selecionar um comando
  const handleCommandSelect = (command: string) => {
    if (onSelect) {
      onSelect(command);
    }
  };

  return (
    <div className="space-y-4 w-full">
      <div className="rounded-xl p-6 border-border border shadow-sm bg-card">
        <div className="mb-4 flex flex-col space-y-1.5 pb-2">
          <h3 className="font-semibold tracking-tight text-lg flex items-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="h-6 w-6 mr-2 text-primary"
            >
              <path d="m12 3-1.9 5.8a2 2 0 0 1-1.287 1.288L3 12l5.8 1.9a2 2 0 0 1 1.288 1.287L12 21l1.9-5.8a2 2 0 0 1 1.287-1.288L21 12l-5.8-1.9a2 2 0 0 1-1.288-1.287Z" />
            </svg>
            Exemplos de Comandos
          </h3>
        </div>

        <div className="">
          {/* Tabs para categorias */}
          <div className="flex mb-4 space-x-2">
            {categories.map(category => (
              <button
                key={category.id}
                className={`px-3 py-1 text-sm rounded-full transition-colors ${
                  selectedCategory === category.id
                    ? 'bg-primary text-primary-foreground'
                    : 'bg-muted text-muted-foreground hover:bg-muted/80'
                }`}
                onClick={() => setSelectedCategory(category.id)}
                data-testid={`category-${category.id}`}
              >
                {category.name}
              </button>
            ))}
          </div>

          {/* Exemplos da categoria selecionada */}
          <div className="mb-3">
            <div className="flex items-center space-x-2 mb-1">
              <div className="p-1 rounded-md bg-primary/10 text-primary">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="h-5 w-5"
                >
                  <rect x="4" y="2" width="16" height="20" rx="2" />
                  <line x1="8" y1="6" x2="16" y2="6" />
                  <line x1="16" y1="14" x2="16" y2="18" />
                  <path d="M16 10h.01" />
                  <path d="M12 10h.01" />
                  <path d="M8 10h.01" />
                  <path d="M12 14h.01" />
                  <path d="M8 14h.01" />
                  <path d="M12 18h.01" />
                  <path d="M8 18h.01" />
                </svg>
              </div>
              <span className="text-xs font-medium text-muted-foreground">
                {categories.find(c => c.id === selectedCategory)?.name}
              </span>
            </div>

            {/* Lista de comandos */}
            {categories
              .find(c => c.id === selectedCategory)
              ?.commands.map((command, idx) => (
                <button
                  key={idx}
                  className="w-full justify-start text-left font-normal h-auto py-3 border-primary/20 bg-primary/5 hover:bg-primary/10"
                  onClick={() => handleCommandSelect(command)}
                  data-testid={`command-${idx}`}
                >
                  <span className="line-clamp-2">{command}</span>
                </button>
              ))}
          </div>

          <button className="w-full">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="h-5 w-5 mr-1"
            >
              <path d="m6 9 6 6 6-6" />
            </svg>
            Ver mais exemplos
          </button>
        </div>
      </div>
    </div>
  );
};

export default CommandExamplesMock;
