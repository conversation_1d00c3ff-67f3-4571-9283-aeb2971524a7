/**
 * Definições de tipos para o sistema de colaboração em tempo real
 */

// Colaborador ativo na planilha
export interface ActiveCollaborator {
  id: string;
  name: string;
  email: string;
  color: string;
  position?: {
    sheetId: string;
    row?: number;
    col?: number;
    cell?: string;
    range?: string;
  };
  lastActivity: number;
  status: 'active' | 'idle' | 'offline';
}

// Estado de colaboração
export interface CollabState {
  collaborators: ActiveCollaborator[];
  activeUsers: number;
  isConnected: boolean;
  isTyping: Record<string, boolean>;
  currentUser: string | null;
  currentUserColor: string;
  workbookId: string | null;
  sheetId: string | null;
  socketId: string | null;
  _submitCellEdit?: (cellId: string, value: any) => void;
  _userName?: string;
  _userId?: string;
}

// Evento de célula
export interface CellEvent {
  id: string;
  type: 'update' | 'format' | 'comment' | 'selection';
  cell: string;
  value?: any;
  formula?: string;
  userId: string;
  userName: string;
  timestamp: number;
}

// Evento de planilha
export interface SheetEvent {
  id: string;
  type: 'rename' | 'add' | 'delete' | 'reorder' | 'resize';
  sheetId: string;
  userId: string;
  userName: string;
  timestamp: number;
  data?: any;
}

// Eventos de presença
export interface PresenceEvent {
  type: 'join' | 'leave' | 'activity' | 'position';
  userId: string;
  userName: string;
  timestamp: number;
  data?: any;
}

// Tipos de mensagens de colaboração
export type CollaborationEventType =
  | 'cell:update'
  | 'cell:select'
  | 'sheet:change'
  | 'user:join'
  | 'user:leave'
  | 'cursor:move';

// Mensagem de colaboração
export interface CollaborationMessage {
  type: CollaborationEventType;
  workbookId: string;
  sheetId?: string;
  userId: string;
  userName: string;
  timestamp: number;
  data: any;
}
