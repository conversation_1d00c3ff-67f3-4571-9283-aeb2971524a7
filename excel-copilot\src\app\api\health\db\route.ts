import { NextResponse } from 'next/server';

import { logger } from '@/lib/logger';
import { prisma, getDatabaseMetrics } from '@/server/db/client';
import { getQueryCacheStats } from '@/server/db/query-cache';

// Verificar se estamos no processo de build estático ou exportação
const isBuildTime =
  process.env.NEXT_PHASE === 'phase-production-build' || process.env.NEXT_STATIC_EXPORT === 'true';

/**
 * Endpoint para verificação de saúde do banco de dados
 * GET /api/health/db
 */
export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

export async function GET() {
  // Pular a verificação durante o build estático
  if (isBuildTime) {
    logger.info('Pulando verificação de DB durante build estático');
    return NextResponse.json({
      status: 'skipped',
      message: 'Verificação de saúde do banco ignorada durante build estático',
    });
  }

  try {
    // Teste simples de conexão
    const startTime = Date.now();
    await prisma.$queryRaw`SELECT 1`;
    const responseTime = Date.now() - startTime;

    // Obter métricas
    const metrics = getDatabaseMetrics();
    const cacheStats = getQueryCacheStats();

    return NextResponse.json({
      status: 'healthy',
      responseTime: `${responseTime}ms`,
      metrics: {
        activeConnections: metrics.activeConnections,
        totalQueries: metrics.totalQueries,
        failedQueries: metrics.failedQueries,
        averageQueryTime: `${metrics.averageQueryTime.toFixed(2)}ms`,
        connectionFailures: metrics.connectionFailures,
        lastConnectionFailure: metrics.lastConnectionFailure,
        cacheSize: cacheStats.size,
      },
    });
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
    logger.error('Falha na verificação de saúde do banco de dados:', error);

    return NextResponse.json(
      {
        status: 'unhealthy',
        error: errorMessage,
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
