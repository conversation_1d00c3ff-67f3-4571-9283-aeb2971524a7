import { NextRequest, NextResponse } from 'next/server';

import { ENV } from '@/config/unified-environment';
import { logger } from '@/lib/logger';

// Cache em memória para armazenar tentativas (em produção, idealmente usar Redis)
const paymentAttemptCache = new Map<
  string,
  { count: number; resetTime: number; blocked?: boolean; blockUntil?: number }
>();

// Lista de IPs marcados como suspeitos por comportamento anômalo
const suspiciousPaymentIPs = new Set<string>();

// Configurações de limites para endpoints de pagamento
const PAYMENT_LIMITS = {
  // Limites mais restritivos para pagamentos
  maxRequests: ENV.IS_PRODUCTION ? 10 : 30,
  windowSizeInSeconds: 60,
  blockDurationSeconds: 300, // 5 minutos de bloqueio
  maxConsecutiveFailures: 3, // Máximo de falhas consecutivas permitidas
  suspiciousPatterns: [
    /multiple.cards.in.rapid.succession/i,
    /card.testing.pattern/i,
    /multiple.declined.transactions/i,
  ],
};

/**
 * Middleware específico para endpoints de pagamento
 * Implementa proteções adicionais contra fraudes e abusos
 */
export async function paymentLimiter(req: NextRequest): Promise<NextResponse | null> {
  // Se estamos em modo de teste, pular rate limiting
  if (ENV.IS_TEST) {
    return null;
  }

  const path = req.nextUrl.pathname;

  // Verificar se é um endpoint de pagamento
  const isPaymentEndpoint =
    path.startsWith('/api/payment') ||
    path.startsWith('/api/stripe') ||
    path.startsWith('/api/webhooks/stripe') ||
    path.includes('/checkout');

  if (!isPaymentEndpoint) {
    return null; // Não é um endpoint de pagamento
  }

  // Obter identificadores da requisição
  const ip = req.headers.get('x-forwarded-for') || 'unknown';
  const userAgent = req.headers.get('user-agent') || 'unknown';
  const sessionToken = req.cookies.get('next-auth.session-token')?.value;

  // Identificador: priorizar sessão, caso contrário IP
  const identifier = sessionToken ? `session:${sessionToken.substring(0, 16)}` : `ip:${ip}`;

  // Verificar se o IP está na lista de suspeitos
  if (suspiciousPaymentIPs.has(ip)) {
    logger.warn(`Tentativa de pagamento bloqueada de IP suspeito: ${ip} para ${path}`);
    return new NextResponse(
      JSON.stringify({
        error: 'Access Denied',
        message: 'Seu acesso a pagamentos foi temporariamente restrito por motivos de segurança',
        code: 'PAYMENT_IP_BLOCKED',
      }),
      { status: 403, headers: { 'Content-Type': 'application/json' } }
    );
  }

  const now = Math.floor(Date.now() / 1000);
  const cacheKey = `payment-limit:${identifier}`;

  // Obter o registro atual de contagem
  let record = paymentAttemptCache.get(cacheKey);

  // Verificar se o usuário está bloqueado
  if (record?.blocked && record.blockUntil && now < record.blockUntil) {
    const remainingBlock = record.blockUntil - now;
    logger.warn(`Usuário bloqueado tentando acessar pagamento: ${identifier} em ${path}`);

    return new NextResponse(
      JSON.stringify({
        error: 'Too Many Payment Attempts',
        message: 'Muitas tentativas de pagamento detectadas. Tente novamente mais tarde.',
        retryAfter: remainingBlock,
        code: 'PAYMENT_RATE_LIMITED',
      }),
      {
        status: 429,
        headers: {
          'Retry-After': remainingBlock.toString(),
          'Content-Type': 'application/json',
        },
      }
    );
  }

  // Se não existir ou já expirou, criar novo registro
  if (!record || now > record.resetTime) {
    record = {
      count: 0,
      resetTime: now + PAYMENT_LIMITS.windowSizeInSeconds,
      blocked: false,
    };
  }

  // Incrementar contador
  record.count++;
  paymentAttemptCache.set(cacheKey, record);

  // Calcular cabeçalhos de rate limit
  const remaining = Math.max(0, PAYMENT_LIMITS.maxRequests - record.count);
  const reset = record.resetTime - now;

  // Configurar headers para informar cliente sobre limite
  const headers = new Headers();
  headers.set('X-RateLimit-Limit', PAYMENT_LIMITS.maxRequests.toString());
  headers.set('X-RateLimit-Remaining', remaining.toString());
  headers.set('X-RateLimit-Reset', reset.toString());

  // Verificar padrões suspeitos no corpo da requisição para POST requests
  let suspiciousPatternDetected = false;
  if (req.method === 'POST') {
    try {
      // Clone da requisição para leitura do corpo
      const clonedReq = req.clone();
      const body = await clonedReq.json();

      // Verificar por padrões suspeitos de fraude nos dados
      const bodyString = JSON.stringify(body).toLowerCase();

      suspiciousPatternDetected = PAYMENT_LIMITS.suspiciousPatterns.some(pattern =>
        pattern.test(bodyString)
      );

      if (suspiciousPatternDetected) {
        logger.warn(`Padrão suspeito detectado em pagamento: ${identifier}, IP: ${ip}`);

        // Marcar IP como suspeito para monitoramento adicional
        if (ip !== 'unknown') {
          suspiciousPaymentIPs.add(ip);
        }
      }
    } catch (error) {
      // Erro ao ler corpo, continuar com verificações normais
      logger.error(`Erro ao analisar corpo da requisição de pagamento: ${error}`);
    }
  }

  // Se excedeu o limite, retornar 429
  if (record.count > PAYMENT_LIMITS.maxRequests || suspiciousPatternDetected) {
    // Log detalhado para análise de segurança
    logger.warn(
      `Limite de pagamento excedido: ${identifier} em ${path}. ` +
        `IP: ${ip}, UserAgent: ${userAgent}, Requests: ${record.count}, ` +
        `Suspicious: ${suspiciousPatternDetected}`
    );

    // Bloquear temporariamente se excedeu significativamente ou é suspeito
    if (record.count > PAYMENT_LIMITS.maxRequests * 1.5 || suspiciousPatternDetected) {
      record.blocked = true;
      record.blockUntil = now + PAYMENT_LIMITS.blockDurationSeconds;
      paymentAttemptCache.set(cacheKey, record);

      logger.warn(
        `Usuário ${identifier} bloqueado por ${PAYMENT_LIMITS.blockDurationSeconds}s após exceder limite de pagamentos`
      );
    }

    return new NextResponse(
      JSON.stringify({
        error: 'Too Many Payment Attempts',
        message:
          'Detectamos um volume incomum de tentativas de pagamento. Por favor, tente novamente mais tarde.',
        retryAfter: reset,
        code: 'PAYMENT_RATE_LIMITED',
      }),
      {
        status: 429,
        headers: {
          ...Object.fromEntries(headers.entries()),
          'Retry-After': reset.toString(),
          'Content-Type': 'application/json',
        },
      }
    );
  }

  // Se o usuário está chegando perto do limite, fazer log para monitoramento
  if (record.count > PAYMENT_LIMITS.maxRequests * 0.75) {
    logger.info(
      `Alto volume de requisições de pagamento: ${identifier} em ${path} (${record.count}/${PAYMENT_LIMITS.maxRequests})`
    );
  }

  // Requisição permitida, continua
  return null;
}

/**
 * Função para adicionar manualmente um IP à lista de suspeitos
 * Útil para integração com sistemas de detecção de fraude externos
 */
export function markIPAsSuspicious(ip: string): void {
  if (!suspiciousPaymentIPs.has(ip)) {
    suspiciousPaymentIPs.add(ip);
    logger.warn(`IP ${ip} marcado manualmente como suspeito para pagamentos`);
  }
}

/**
 * Função para remover um IP da lista de suspeitos
 * Útil após verificação manual de que não é um atacante
 */
export function clearSuspiciousIP(ip: string): boolean {
  const removed = suspiciousPaymentIPs.delete(ip);
  if (removed) {
    logger.info(`IP ${ip} removido da lista de suspeitos para pagamentos`);
  }
  return removed;
}

/**
 * Função para limpar o cache de tentativas de um usuário específico
 * Útil para casos em que um bloqueio foi acidental
 */
export function clearPaymentRateLimitForUser(identifier: string): boolean {
  const cacheKey = `payment-limit:${identifier}`;
  const removed = paymentAttemptCache.delete(cacheKey);
  if (removed) {
    logger.info(`Limite de pagamento limpo para usuário: ${identifier}`);
  }
  return removed;
}
