import { safeConsoleLog } from '@/lib/logger';
import { toNullableString, toPrismaUpdateInput } from '@/server/db/utils';

/**
 * Sistema de auditoria para eventos de autenticação
 * Registra tentativas de login, sucessos, falhas e eventos suspeitos
 */

export interface AuthAuditEvent {
  userId?: string | null;
  email?: string | null;
  provider?: string | null;
  eventType:
    | 'LOGIN_SUCCESS'
    | 'LOGIN_FAILURE'
    | 'LOGOUT'
    | 'SESSION_CREATED'
    | 'SESSION_EXPIRED'
    | 'SUSPICIOUS_ACTIVITY'
    | 'RATE_LIMIT_EXCEEDED'
    | 'OAUTH_ERROR';
  ipAddress?: string | null;
  userAgent?: string | null;
  details?: string;
  metadata?: Record<string, unknown> | undefined;
}

/**
 * Registra um evento de auditoria de autenticação
 */
export async function logAuthEvent(event: AuthAuditEvent): Promise<void> {
  try {
    // Log no console para desenvolvimento e monitoramento imediato
    const logMessage = `🔐 AUTH EVENT: ${event.eventType} - User: ${event.userId || event.email || 'unknown'} - IP: ${event.ipAddress || 'unknown'}`;

    if (
      event.eventType === 'LOGIN_FAILURE' ||
      event.eventType === 'SUSPICIOUS_ACTIVITY' ||
      event.eventType === 'RATE_LIMIT_EXCEEDED'
    ) {
      console.warn(logMessage, event.details);
    } else {
      safeConsoleLog(logMessage);
    }

    // Registrar no banco de dados se tivermos um userId
    if (event.userId) {
      try {
        const { prisma } = await import('@/server/db/client');
        await prisma.securityLog.create({
          data: {
            userId: event.userId,
            eventType: event.eventType,
            details: JSON.stringify({
              provider: event.provider,
              ipAddress: event.ipAddress,
              userAgent: event.userAgent,
              details: event.details,
              metadata: event.metadata,
              timestamp: new Date().toISOString(),
            }),
          },
        });
      } catch (error) {
        console.error('Erro ao registrar no securityLog:', error);
      }
    }

    // Para eventos críticos, também registrar no UserActionLog
    if (
      ['LOGIN_SUCCESS', 'LOGIN_FAILURE', 'SUSPICIOUS_ACTIVITY'].includes(event.eventType) &&
      event.userId
    ) {
      try {
        const { prisma } = await import('@/server/db/client');
        await prisma.userActionLog.create({
          data: {
            userId: event.userId,
            action: event.eventType,
            details: JSON.stringify({
              provider: event.provider,
              ipAddress: event.ipAddress,
              success: event.eventType === 'LOGIN_SUCCESS',
              details: event.details,
            }),
          },
        });
      } catch (error) {
        console.error('Erro ao registrar no userActionLog:', error);
      }
    }
  } catch (error) {
    // Não falhar a autenticação por causa de erro de log
    console.error('Erro ao registrar evento de auditoria:', error);
  }
}

/**
 * Registra tentativa de login bem-sucedida
 */
export async function logSuccessfulLogin(
  userId: string,
  email: string,
  provider: string,
  ipAddress?: string,
  userAgent?: string
): Promise<void> {
  await logAuthEvent({
    userId,
    email,
    provider,
    eventType: 'LOGIN_SUCCESS',
    ipAddress: toNullableString(ipAddress),
    userAgent: toNullableString(userAgent),
    details: `Login successful via ${provider}`,
  });

  // Atualizar estatísticas do usuário
  try {
    const { prisma } = await import('@/server/db/client');
    await prisma.user.update({
      where: { id: userId },
      data: {
        lastLoginAt: new Date(),
        loginCount: { increment: 1 },
        lastIpAddress: toPrismaUpdateInput(ipAddress),
        userAgent: toPrismaUpdateInput(userAgent),
      },
    });
  } catch (error) {
    console.error('Erro ao atualizar estatísticas de login:', error);
  }
}

/**
 * Registra tentativa de login falhada
 */
export async function logFailedLogin(
  email?: string,
  provider?: string,
  reason?: string,
  ipAddress?: string,
  userAgent?: string
): Promise<void> {
  await logAuthEvent({
    email: toNullableString(email),
    provider: toNullableString(provider),
    eventType: 'LOGIN_FAILURE',
    ipAddress: toNullableString(ipAddress),
    userAgent: toNullableString(userAgent),
    details: `Login failed: ${reason || 'Unknown reason'}`,
  });
}

/**
 * Registra logout do usuário
 */
export async function logUserLogout(
  userId: string,
  ipAddress?: string,
  userAgent?: string
): Promise<void> {
  await logAuthEvent({
    userId,
    eventType: 'LOGOUT',
    ipAddress: toNullableString(ipAddress),
    userAgent: toNullableString(userAgent),
    details: 'User logged out',
  });
}

/**
 * Registra criação de nova sessão
 */
export async function logSessionCreated(
  userId: string,
  sessionToken: string,
  ipAddress?: string,
  userAgent?: string
): Promise<void> {
  await logAuthEvent({
    userId,
    eventType: 'SESSION_CREATED',
    ipAddress: toNullableString(ipAddress),
    userAgent: toNullableString(userAgent),
    details: 'New session created',
    metadata: {
      sessionToken: sessionToken.substring(0, 8) + '...', // Apenas primeiros 8 caracteres por segurança
    },
  });
}

/**
 * Registra atividade suspeita
 */
export async function logSuspiciousActivity(
  userId?: string,
  email?: string,
  reason?: string,
  ipAddress?: string,
  userAgent?: string,
  metadata?: Record<string, unknown>
): Promise<void> {
  await logAuthEvent({
    userId: toNullableString(userId),
    email: toNullableString(email),
    eventType: 'SUSPICIOUS_ACTIVITY',
    ipAddress: toNullableString(ipAddress),
    userAgent: toNullableString(userAgent),
    details: `Suspicious activity detected: ${reason}`,
    metadata,
  });

  // Se temos um userId, marcar como suspeito para revisão
  if (userId) {
    try {
      const { prisma } = await import('@/server/db/client');
      await prisma.user.update({
        where: { id: userId },
        data: {
          isSuspicious: true,
        },
      });
    } catch (error) {
      console.error('Erro ao marcar usuário como suspeito:', error);
    }
  }
}

/**
 * Registra erro OAuth
 */
export async function logOAuthError(
  provider: string,
  error: string,
  email?: string,
  ipAddress?: string,
  userAgent?: string
): Promise<void> {
  await logAuthEvent({
    email: toNullableString(email),
    provider,
    eventType: 'OAUTH_ERROR',
    ipAddress: toNullableString(ipAddress),
    userAgent: toNullableString(userAgent),
    details: `OAuth error with ${provider}: ${error}`,
  });
}

/**
 * Registra excesso de rate limit
 */
export async function logRateLimitExceeded(
  endpoint: string,
  ipAddress?: string,
  userAgent?: string
): Promise<void> {
  await logAuthEvent({
    eventType: 'RATE_LIMIT_EXCEEDED',
    ipAddress: toNullableString(ipAddress),
    userAgent: toNullableString(userAgent),
    details: `Rate limit exceeded for endpoint: ${endpoint}`,
  });
}

/**
 * Obtém estatísticas de eventos de autenticação
 */
export async function getAuthAuditStats(
  userId?: string,
  timeframe: 'hour' | 'day' | 'week' | 'month' = 'day'
): Promise<{
  totalEvents: number;
  successfulLogins: number;
  failedLogins: number;
  suspiciousActivities: number;
  rateLimitExceeded: number;
}> {
  const timeframeDates = {
    hour: new Date(Date.now() - 60 * 60 * 1000),
    day: new Date(Date.now() - 24 * 60 * 60 * 1000),
    week: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
    month: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
  };

  const since = timeframeDates[timeframe];

  try {
    const { prisma } = await import('@/server/db/client');
    const whereClause = {
      timestamp: { gte: since },
      ...(userId && { userId }),
    };

    const [totalEvents, successfulLogins, failedLogins, suspiciousActivities, rateLimitExceeded] =
      await Promise.all([
        prisma.securityLog.count({ where: whereClause }),
        prisma.securityLog.count({ where: { ...whereClause, eventType: 'LOGIN_SUCCESS' } }),
        prisma.securityLog.count({ where: { ...whereClause, eventType: 'LOGIN_FAILURE' } }),
        prisma.securityLog.count({ where: { ...whereClause, eventType: 'SUSPICIOUS_ACTIVITY' } }),
        prisma.securityLog.count({ where: { ...whereClause, eventType: 'RATE_LIMIT_EXCEEDED' } }),
      ]);

    return {
      totalEvents,
      successfulLogins,
      failedLogins,
      suspiciousActivities,
      rateLimitExceeded,
    };
  } catch (error) {
    console.error('Erro ao obter estatísticas de auditoria:', error);
    return {
      totalEvents: 0,
      successfulLogins: 0,
      failedLogins: 0,
      suspiciousActivities: 0,
      rateLimitExceeded: 0,
    };
  }
}
