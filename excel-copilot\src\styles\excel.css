@tailwind components;

/* Estilos específicos do Excel Copilot */
.excel-grid {
  @apply border-collapse w-full;
}

.excel-cell {
  @apply border border-border p-2 whitespace-nowrap overflow-hidden text-ellipsis;
  min-width: 80px;
}

.excel-header-cell {
  @apply bg-muted font-medium text-sm;
  background-color: hsl(125, 55%, 95%);
}

.excel-data-cell {
  @apply text-sm;
}

.excel-numeric {
  @apply text-right;
}

.excel-date {
  @apply text-center;
}

.excel-formula {
  @apply italic;
  color: hsl(125, 55%, 43%);
}

/* Melhorias nos cards para parecer mais Office */
.card-excel {
  @apply rounded-md border border-border bg-card p-4 shadow-sm transition-all duration-200;
}

.card-excel:hover {
  @apply shadow-md;
  border-color: hsl(125, 55%, 43%);
}

/* Tour highlight ajustado para cor verde Excel */
.tour-highlight {
  position: relative;
  z-index: 55 !important;
  box-shadow: 0 0 0 4px hsl(var(--primary)) !important;
  border-radius: 0.25rem;
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%,
  100% {
    box-shadow: 0 0 0 4px hsl(var(--primary) / 0.6) !important;
  }
  50% {
    box-shadow: 0 0 0 4px hsl(var(--primary) / 1) !important;
  }
}

/* Estilos específicos para parecer com Excel */
.excel-header {
  background-color: hsl(125, 55%, 43%);
  color: white;
  font-weight: 600;
}

.excel-ribbon {
  background-color: hsl(125, 55%, 43%);
  border-bottom: 1px solid hsl(125, 55%, 38%);
  padding: 0.5rem;
  display: flex;
  gap: 1rem;
}

.excel-ribbon-button {
  padding: 0.5rem;
  border-radius: 0.25rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  color: white;
  font-size: 0.75rem;
}

.excel-ribbon-button:hover {
  background-color: hsl(125, 55%, 38%);
}

.excel-ribbon-divider {
  width: 1px;
  background-color: hsl(125, 55%, 38%);
}

/* Melhorias na visualização de gráficos */
.bar-chart-container {
  height: 180px;
  width: 100%;
  display: flex;
  align-items: flex-end;
  justify-content: space-around;
  padding: 0 1rem;
}

.bar-chart-bar {
  width: 2rem;
  background-color: hsl(var(--primary) / 0.8);
  border-top-left-radius: 0.25rem;
  border-top-right-radius: 0.25rem;
  transition: height 0.5s ease;
}

@media (prefers-reduced-motion: reduce) {
  .bar-chart-bar {
    transition: none;
  }
}

.bar-chart-label {
  font-size: 0.75rem;
  color: hsl(var(--muted-foreground));
  margin-top: 0.25rem;
  text-align: center;
}

/* Melhorias para acessibilidade */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

/* Melhorias no foco e interação */
.focus-visible-ring {
  @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 focus-visible:ring-offset-background;
}

/* Melhorias na responsividade */
.fluid-container {
  width: 100%;
  max-width: 72rem; /* 1152px */
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .fluid-container {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .fluid-container {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

/* High priority UI elements - optimization for LCP */
.lcp-target {
  background-color: var(--lcp-elem-bg);
  transition: background-color var(--duration-medium) ease;
}
