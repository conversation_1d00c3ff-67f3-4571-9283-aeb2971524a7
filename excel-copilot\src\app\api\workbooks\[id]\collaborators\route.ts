import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';

import { CollaborationStore } from '@/lib/collaboration/store';
import { prisma } from '@/server/db/client';

// Interface para tipagem do usuário da sessão
interface SessionUser {
  id: string;
  name?: string;
  email?: string;
}

// Define a interface DbCollaborator com avatar opcional que aceite undefined
interface DbCollaborator {
  id: string;
  name: string;
  email: string;
  avatar: string | undefined;
  role: string;
  permission: string;
  lastActive: Date;
}

/**
 * API para obter colaboradores de uma planilha
 */
export async function GET(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    // Verificar autenticação
    const session = await getServerSession();
    if (!session?.user) {
      return NextResponse.json({ error: 'Você precisa estar autenticado' }, { status: 401 });
    }

    const workbookId = params.id;
    if (!workbookId) {
      return NextResponse.json({ error: 'ID da planilha é obrigatório' }, { status: 400 });
    }

    // Verificar se o usuário tem acesso à planilha
    const userId = (session.user as SessionUser).id;

    // Verificar se a planilha existe e se o usuário tem acesso
    const workbook = await prisma.workbook.findUnique({
      where: { id: workbookId },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
          },
        },
      },
    });

    if (!workbook) {
      return NextResponse.json({ error: 'Planilha não encontrada' }, { status: 404 });
    }

    // Verificar se o usuário tem acesso à planilha
    if (workbook.userId !== userId && !workbook.isPublic) {
      // Verificar compartilhamento
      const share = await prisma.workbookShare.findFirst({
        where: {
          workbookId,
          sharedWithUserId: userId,
        },
      });

      if (!share) {
        return NextResponse.json({ error: 'Acesso negado' }, { status: 403 });
      }
    }

    // Buscar todos os compartilhamentos desta planilha
    const workbookShares = await prisma.workbookShare.findMany({
      where: { workbookId },
      include: {
        sharedWithUser: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
          },
        },
      },
    });

    // Montar a lista de colaboradores baseada no dono e compartilhamentos
    const dbCollaborators: DbCollaborator[] = [];

    // Adicionar o dono como colaborador
    dbCollaborators.push({
      id: workbook.user.id,
      name: workbook.user.name || 'Usuário',
      email: workbook.user.email || '',
      avatar: workbook.user.image || undefined,
      role: 'owner',
      permission: 'edit',
      lastActive: new Date(),
    });

    // Adicionar colaboradores com base nos compartilhamentos
    if (workbookShares.length > 0) {
      dbCollaborators.push(
        ...workbookShares.map(share => ({
          id: share.sharedWithUser.id,
          name: share.sharedWithUser.name || 'Usuário',
          email: share.sharedWithUser.email || '',
          avatar: share.sharedWithUser.image || undefined,
          role: 'collaborator',
          permission: share.permissionLevel,
          lastActive: share.updatedAt || new Date(),
        }))
      );
    }

    // Obter colaboradores ativos da store em memória
    const store = CollaborationStore.getInstance();
    const activeCollaborators = store.getCollaborators(workbookId);

    // Mesclar dados do banco com os ativos
    const mergedCollaborators = dbCollaborators.map(dbCollab => {
      const active = activeCollaborators.find(ac => ac.id === dbCollab.id);
      return {
        ...dbCollab,
        status: active ? active.status : 'offline',
        position: active ? active.position : undefined,
        lastActive: active ? active.lastActive : dbCollab.lastActive,
      };
    });

    // Adicionar colaboradores ativos que não estão no banco
    activeCollaborators.forEach(active => {
      if (!dbCollaborators.some(db => db.id === active.id)) {
        mergedCollaborators.push({
          id: active.id,
          name: active.name || 'Convidado',
          email: active.email || '',
          avatar: active.avatar,
          role: 'guest',
          permission: 'view',
          status: active.status,
          position: active.position || { row: 0, col: 0 }, // Garantir que position sempre existe
          lastActive: active.lastActive,
        });
      }
    });

    return NextResponse.json({ collaborators: mergedCollaborators });
  } catch (error) {
    console.error('Erro ao obter colaboradores:', error);
    return NextResponse.json({ error: 'Erro interno do servidor' }, { status: 500 });
  }
}
