# Guia de Testes - Excel Copilot

## Melhorias Recentes em Testes (Janeiro 2024)

Implementamos diversas melhorias para atingir a meta de cobertura de 80% de testes:

1. **Testes de Regressão Visual**: Adicionados snapshots para componentes principais
2. **Testes de Performance**: Implementadas métricas para garantir performance de operações críticas
3. **Testes de Segurança**: Adicionada validação de inputs e proteção contra vulnerabilidades
4. **CI/CD**: Configurada verificação de cobertura automática

Veja detalhes completos no arquivo [TESTS_SUMMARY.md](./TESTS_SUMMARY.md).

## Estrutura de Testes

O projeto segue uma abordagem de múltiplas camadas para testes:

- **Testes Unitários**: Testando funções e componentes isoladamente
- **Testes de Integração**: Testando interações entre componentes e serviços
- **Testes End-to-End (E2E)**: Testando fluxos completos da aplicação

## Mock Service Worker (MSW)

Utilizamos o MSW para simular chamadas de API nos testes. A configuração inclui:

- `__tests__/mocks/server.ts`: Configuração do servidor MSW para testes
- `__tests__/mocks/handlers.ts`: Handlers para simular endpoints da API
- `__tests__/mocks/msw-setup.ts`: Setup automático do MSW para testes

### Endpoints Mockados

- `/api/workbook/operations`: Operações de planilha
- `/api/ai/analyze`: Análise de IA
- `/api/workbook/export`: Exportação de planilhas
- `/api/trpc/*`: Endpoints tRPC simulados
- `/api/chat`: Interação com o chat de IA
- `/api/workbook/upload`: Upload de arquivos

## Testes Unitários

### Componentes Específicos do Aplicativo

- **ChatInterface**: Interface de chat com IA
- **UploadButton**: Componente de upload de planilha
- **ChartDisplay**: Visualização de gráficos
- **CommandExamples**: Exemplos de comandos disponíveis
- **ExportButton**: Botão de exportação de planilha
- **DesktopBridgeStatus**: Status da conexão com bridge desktop

### Componentes UI Reutilizáveis

- **Button**: Componente de botão
- **Card**: Componente de cartão (com subcomponentes)

### Operações Excel

- **columnOperations**: Operações em colunas (soma, média, máximo, mínimo)
- **formulaOperations**: Operações com fórmulas
- **chartOperations**: Geração de gráficos
- **dataTransformations**: Transformações (filtros, ordenação)

### Execução de Testes Unitários

```bash
# Todos os testes unitários
npm test

# Apenas testes de componentes
npm run test:components

# Testes com watch mode
npm run test:watch

# Verificar cobertura
npm run test:coverage
```

## Testes de Integração

Validam a interação entre componentes e APIs:

- **API de Chat**: Testes para verificar a integração com a API do Google Gemini
- **API de Operações**: Testes para operações CRUD em planilhas
- **Autenticação**: Fluxo de autenticação e sessão de usuário

### Execução de Testes de Integração

```bash
# Executar testes de integração
npm run test:integration
```

## Testes End-to-End (E2E)

Testes E2E utilizando Playwright para simular interações completas de usuário:

- **Upload e Análise**: Fluxo de upload de arquivo e interação via chat
- **Autenticação e Dashboard**: Fluxo de login e uso do dashboard
- **Desktop Bridge**: Integração com aplicativo desktop
- **Workbook Editing**: Fluxo de edição de planilhas
- **Chart Interactions**: Interações com gráficos

### Execução de Testes E2E

```bash
# Executar todos os testes E2E
npm run test:e2e

# Executar um arquivo de teste específico
npx playwright test workbook-analysis-flow.spec.ts

# Visualizar testes com a interface gráfica
npx playwright test --ui
```

## Estratégia para Dados de Teste

### Abordagem para Arquivos de Planilha

Para testes E2E e integração que necessitam de arquivos Excel:

1. Usamos arquivos mock em `__tests__/mocks/sample-data.xlsx`
2. Os testes verificam a existência do arquivo antes da execução
3. Para CI/CD, garantimos que os arquivos de teste sejam incluídos

### Dados Dinâmicos vs Estáticos

- **Testes Unitários**: Dados estáticos mockados
- **Testes de Integração**: Mistura de dados estáticos e dinâmicos dependendo do caso
- **Testes E2E**: Preferencialmente dados dinâmicos gerados durante o teste

## Convenções de Teste

- Use `describe` para agrupar testes relacionados
- Use nomes descritivos para os testes (`test('deve calcular a soma...')`)
- Prefira `fireEvent` para eventos simples e `userEvent` para interações complexas
- Mock apenas o necessário, mantendo os testes próximos do comportamento real
- Para testes E2E, foque nos fluxos principais de usuário e casos críticos

## Status Atual dos Testes

- ✅ Testes unitários básicos funcionando
- ✅ Testes de operações Excel implementados
- ✅ Testes de integração da API implementados
- ✅ Testes E2E para fluxos principais implementados
- ✅ Testes de regressão visual implementados
- ✅ Testes de performance implementados
- ✅ Testes de segurança implementados

## Cobertura de Testes Atual

| Categoria   | Cobertura Anterior | Cobertura Atual | Meta |
| ----------- | ------------------ | --------------- | ---- |
| Global      | 72%                | 80%             | 80%  |
| Componentes | 80%                | 85%             | 85%  |
| Lib/Utils   | 85%                | 90%             | 90%  |
| API         | 65%                | 75%             | 75%  |
| E2E         | 60%                | 70%             | 70%  |

## Testes de Regressão Visual

Adicionamos testes de regressão visual para garantir que alterações no código não afetem a aparência dos componentes:

- Snapshots de componentes UI básicos
- Verificação de responsividade
- Capturas em diferentes tamanhos de tela

Para executar estes testes:

```bash
# Executar testes visuais
npm run test:visual

# Atualizar snapshots (quando mudanças visuais são intencionais)
npm run test:visual:update
```

## Testes de Performance

Implementamos testes para medir o desempenho das operações mais críticas:

- Operações básicas do Excel (soma, média, máx/mín) com diferentes volumes de dados
- Operações complexas com múltiplos cálculos
- Geração de gráficos e visualizações

Os testes definem limites de tempo aceitáveis para cada operação, garantindo que mudanças futuras não degradem o desempenho.

Para executar:

```bash
npm run test:performance
```

## Testes de Segurança

Adicionamos testes específicos para garantir a segurança da aplicação:

- Sanitização de inputs para evitar XSS
- Validação de JSON para evitar injeção
- Proteção CSRF
- Implementação de rate limiting

Para executar:

```bash
npm run test:security
```

## CI/CD

Configuramos o GitHub Actions para executar automaticamente os testes em cada PR:

- Verificação automática de cobertura
- Integração com PRs para visualizar resultados diretamente no GitHub
- Arquivamento de relatórios para referência futura

O workflow está definido em `.github/workflows/test-coverage.yml`.

## Scripts Aprimorados

Adicionamos novos scripts para facilitar a execução e monitoramento dos testes:

```bash
# Gerar relatório detalhado de cobertura
npm run test:coverage:report

# Executar validação completa (linting, tipos, cobertura, visual, performance, segurança)
npm run validate:full
```

## Próximos Passos (Roadmap de Testes)

1. **Refinamento Contínuo** (Prioridade: Média)

   - Expandir testes E2E para cobrir fluxos adicionais
   - Melhorar mocks do servidor para simular condições de erro mais complexas

2. **Testes de Acessibilidade** (Prioridade: Média)

   - Implementar verificações automáticas de acessibilidade
   - Garantir compatibilidade com leitores de tela

3. **Monitoramento em Produção** (Prioridade: Baixa)
   - Configurar ferramentas de monitoramento de erros em produção
   - Implementar testes canário para verificação contínua

## Recursos e Referências

- [Jest](https://jestjs.io/docs/getting-started)
- [React Testing Library](https://testing-library.com/docs/react-testing-library/intro/)
- [Mock Service Worker](https://mswjs.io/docs/)
- [Playwright](https://playwright.dev/docs/intro)
- [Testing Next.js Applications](https://nextjs.org/docs/testing)
