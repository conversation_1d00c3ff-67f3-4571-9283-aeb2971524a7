/**
 * Mock Database para Edge Runtime
 * Substitui temporariamente o Prisma até resolvermos a configuração
 */

// Tipos básicos para compatibilidade
export interface MockUser {
  id: string;
  email: string;
  name: string;
  image: string | undefined;
  createdAt: Date;
  updatedAt: Date;
}

export interface MockWorkbook {
  id: string;
  title: string;
  userId: string;
  createdAt: Date;
  updatedAt: Date;
}

// Mock data em memória
const mockUsers: MockUser[] = [];
const mockWorkbooks: MockWorkbook[] = [];

// Mock Prisma Client para Edge Runtime
export const mockPrisma = {
  user: {
    findUnique: async ({ where }: { where: { id?: string; email?: string } }) => {
      if (where.id) {
        return mockUsers.find(u => u.id === where.id) || null;
      }
      if (where.email) {
        return mockUsers.find(u => u.email === where.email) || null;
      }
      return null;
    },

    create: async ({ data }: { data: Partial<MockUser> }) => {
      const user: MockUser = {
        id: data.id || Math.random().toString(36),
        email: data.email || '',
        name: data.name || '',
        image: data.image || undefined,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      mockUsers.push(user);
      return user;
    },

    update: async ({ where, data }: { where: { id: string }; data: Partial<MockUser> }) => {
      const index = mockUsers.findIndex(u => u.id === where.id);
      if (index === -1) throw new Error('User not found');

      const existingUser = mockUsers[index];
      if (!existingUser) throw new Error('User not found');

      const updatedUser: MockUser = {
        ...existingUser,
        ...data,
        updatedAt: new Date(),
        id: existingUser.id,
        email: data.email ?? existingUser.email,
        name: data.name ?? existingUser.name,
        image: data.image ?? existingUser.image,
        createdAt: existingUser.createdAt,
      };
      mockUsers[index] = updatedUser;
      return updatedUser;
    },

    count: async () => mockUsers.length,
  },

  workbook: {
    findMany: async ({ where }: { where?: { userId?: string } } = {}) => {
      if (where?.userId) {
        return mockWorkbooks.filter(w => w.userId === where.userId);
      }
      return mockWorkbooks;
    },

    findUnique: async ({ where }: { where: { id: string } }) => {
      return mockWorkbooks.find(w => w.id === where.id) || null;
    },

    create: async ({ data }: { data: Partial<MockWorkbook> }) => {
      const workbook: MockWorkbook = {
        id: data.id || Math.random().toString(36),
        title: data.title || 'Untitled',
        userId: data.userId || '',
        createdAt: new Date(),
        updatedAt: new Date(),
      };
      mockWorkbooks.push(workbook);
      return workbook;
    },

    count: async () => mockWorkbooks.length,
  },

  securityLog: {
    create: async ({ data }: { data: Record<string, unknown> }) => {
      // Mock - apenas log no console
      // eslint-disable-next-line no-console
      console.log('Mock SecurityLog:', data);
      return { id: Math.random().toString(36), ...data };
    },

    count: async () => 0,
    findMany: async () => [],
    deleteMany: async () => ({ count: 0 }),
  },

  // Métodos de conexão mock
  $connect: async () => {
    // eslint-disable-next-line no-console
    console.log('Mock Prisma: Connected');
  },

  $disconnect: async () => {
    // eslint-disable-next-line no-console
    console.log('Mock Prisma: Disconnected');
  },

  $queryRaw: async (query: unknown) => {
    // eslint-disable-next-line no-console
    console.log('Mock Prisma Query:', query);
    return [{ result: 1 }];
  },

  $executeRaw: async (query: unknown) => {
    // eslint-disable-next-line no-console
    console.log('Mock Prisma Execute:', query);
    return 1;
  },
};

// Função para detectar se devemos usar mock
export function shouldUseMockDb(): boolean {
  // Usar mock no Edge Runtime ou quando DATABASE_URL não estiver disponível
  const isEdgeRuntime =
    typeof (globalThis as Record<string, unknown>).EdgeRuntime !== 'undefined' ||
    process.env.VERCEL_REGION !== undefined ||
    (globalThis as Record<string, unknown>).EdgeRuntime !== undefined;

  const noDatabaseUrl = !process.env.DB_DATABASE_URL;

  return isEdgeRuntime || noDatabaseUrl;
}

// Export principal que decide qual cliente usar
export function getDbClient() {
  if (shouldUseMockDb()) {
    // eslint-disable-next-line no-console
    console.log('Using Mock Database for Edge Runtime');
    return mockPrisma;
  }

  // Em Node.js Runtime, usar Prisma real
  try {
    // Importação dinâmica para evitar problemas no Edge Runtime
    // eslint-disable-next-line @typescript-eslint/no-require-imports
    const { prisma } = require('@/server/db/client');
    return prisma;
  } catch (error) {
    // eslint-disable-next-line no-console
    console.warn('Fallback to Mock Database due to Prisma error:', error);
    return mockPrisma;
  }
}

// Export para compatibilidade
export const prisma = getDbClient();
export default prisma;
