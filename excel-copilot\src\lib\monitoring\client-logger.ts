/**
 * Sistema de logging para frontend/cliente
 * Registra estados de autenticação, interações do usuário, performance e erros JavaScript
 */

import { sentryLogger } from './sentry-logger';

// Tipos para eventos do cliente
export interface ClientEvent {
  type: ClientEventType;
  userId?: string;
  sessionId: string;
  timestamp: string;
  metadata?: Record<string, any>;
}

export enum ClientEventType {
  PAGE_LOADED = 'page_loaded',
  COMPONENT_RENDERED = 'component_rendered',
  USER_INTERACTION = 'user_interaction',
  AUTH_STATE_CHANGED = 'auth_state_changed',
  API_REQUEST = 'api_request',
  API_RESPONSE = 'api_response',
  ERROR_OCCURRED = 'error_occurred',
  PERFORMANCE_METRIC = 'performance_metric',
  NAVIGATION = 'navigation',
  FEATURE_USED = 'feature_used',
  CACHE_EVENT = 'cache_event',
}

export interface PerformanceMetric {
  name: string;
  value: number;
  unit: 'ms' | 'bytes' | 'count' | 'percentage';
  category: 'load' | 'render' | 'interaction' | 'memory' | 'network';
}

export interface UserInteraction {
  element: string;
  action: 'click' | 'hover' | 'focus' | 'scroll' | 'keypress' | 'drag';
  target?: string;
  value?: any;
}

/**
 * Logger principal para eventos do cliente
 */
export class ClientLogger {
  private static sessionId: string = '';
  private static userId: string | undefined;
  private static isInitialized = false;
  private static performanceObserver?: PerformanceObserver;

  /**
   * Inicializa o logger do cliente
   */
  static initialize(userId?: string): void {
    if (typeof window === 'undefined') return; // Só funciona no browser

    this.sessionId = this.generateSessionId();
    this.userId = userId;
    this.isInitialized = true;

    // Configurar captura de erros globais
    this.setupErrorHandling();

    // Configurar observador de performance
    this.setupPerformanceObserver();

    // Log de inicialização
    this.logEvent(ClientEventType.PAGE_LOADED, {
      url: window.location.href,
      userAgent: navigator.userAgent,
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight,
      },
      timestamp: performance.now(),
    });

    console.log('🔧 Client logger initialized', { sessionId: this.sessionId, userId });
  }

  /**
   * Atualiza o ID do usuário
   */
  static setUserId(userId: string): void {
    const oldUserId = this.userId;
    this.userId = userId;

    this.logEvent(ClientEventType.AUTH_STATE_CHANGED, {
      oldUserId,
      newUserId: userId,
      action: 'login',
    });
  }

  /**
   * Remove o ID do usuário (logout)
   */
  static clearUserId(): void {
    const oldUserId = this.userId;
    this.userId = undefined;

    this.logEvent(ClientEventType.AUTH_STATE_CHANGED, {
      oldUserId,
      newUserId: null,
      action: 'logout',
    });
  }

  /**
   * Registra carregamento de página
   */
  static logPageLoaded(metadata: {
    page: string;
    loadTime: number;
    resourceCount?: number;
    cacheHits?: number;
  }): void {
    this.logEvent(ClientEventType.PAGE_LOADED, metadata);

    // Métricas de performance para Sentry
    sentryLogger.performance('page_load', metadata.loadTime, {
      page: metadata.page,
      resourceCount: metadata.resourceCount,
      cacheHits: metadata.cacheHits,
    });
  }

  /**
   * Registra renderização de componente
   */
  static logComponentRendered(metadata: {
    component: string;
    renderTime: number;
    props?: Record<string, any>;
    rerender?: boolean;
  }): void {
    this.logEvent(ClientEventType.COMPONENT_RENDERED, metadata);

    // Log apenas componentes lentos para evitar spam
    if (metadata.renderTime > 100) {
      console.warn('🐌 Slow component render', metadata);
      sentryLogger.performance('slow_component_render', metadata.renderTime, {
        component: metadata.component,
        rerender: metadata.rerender,
      });
    }
  }

  /**
   * Registra interação do usuário
   */
  static logUserInteraction(interaction: UserInteraction, metadata?: Record<string, any>): void {
    this.logEvent(ClientEventType.USER_INTERACTION, {
      ...interaction,
      ...metadata,
    });

    // Enviar para Sentry como user action
    if (this.userId) {
      sentryLogger.userAction(this.userId, `${interaction.action}_${interaction.element}`, {
        target: interaction.target,
        value: interaction.value,
        ...metadata,
      });
    }
  }

  /**
   * Registra requisição de API
   */
  static logApiRequest(metadata: {
    url: string;
    method: string;
    startTime: number;
    headers?: Record<string, string>;
    body?: any;
  }): string {
    const requestId = this.generateRequestId();

    this.logEvent(ClientEventType.API_REQUEST, {
      requestId,
      ...metadata,
    });

    return requestId;
  }

  /**
   * Registra resposta de API
   */
  static logApiResponse(
    requestId: string,
    metadata: {
      status: number;
      responseTime: number;
      size?: number;
      cached?: boolean;
      error?: string;
    }
  ): void {
    this.logEvent(ClientEventType.API_RESPONSE, {
      requestId,
      ...metadata,
    });

    // Métricas de performance para APIs
    sentryLogger.performance('api_call', metadata.responseTime, {
      requestId,
      status: metadata.status,
      size: metadata.size,
      cached: metadata.cached,
      error: metadata.error,
    });

    // Log de APIs lentas
    if (metadata.responseTime > 3000) {
      console.warn('🐌 Slow API response', { requestId, ...metadata });
    }
  }

  /**
   * Registra erro JavaScript
   */
  static logError(
    error: Error,
    metadata?: {
      component?: string;
      action?: string;
      recoverable?: boolean;
      userImpact?: 'none' | 'minor' | 'major' | 'critical';
    }
  ): void {
    this.logEvent(ClientEventType.ERROR_OCCURRED, {
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack,
      },
      ...metadata,
    });

    // Enviar para Sentry
    sentryLogger.error('Client-side error occurred', error, {
      ...(this.userId ? { userId: this.userId } : {}),
      component: metadata?.component || 'unknown',
      operation: metadata?.action || 'unknown',
      metadata: {
        recoverable: metadata?.recoverable ?? false,
        userImpact: metadata?.userImpact ?? 'none',
        sessionId: this.sessionId,
      },
    });
  }

  /**
   * Registra métrica de performance
   */
  static logPerformanceMetric(metric: PerformanceMetric, metadata?: Record<string, any>): void {
    this.logEvent(ClientEventType.PERFORMANCE_METRIC, {
      metric,
      ...metadata,
    });

    // Enviar métricas críticas para Sentry
    if (metric.category === 'memory' && metric.value > 100 * 1024 * 1024) {
      // > 100MB
      sentryLogger.warn('High memory usage detected', {
        metric,
        sessionId: this.sessionId,
      });
    }
  }

  /**
   * Registra navegação
   */
  static logNavigation(metadata: {
    from: string;
    to: string;
    method: 'push' | 'replace' | 'back' | 'forward';
    loadTime?: number;
  }): void {
    this.logEvent(ClientEventType.NAVIGATION, metadata);

    if (this.userId) {
      sentryLogger.userAction(this.userId, 'navigation', metadata);
    }
  }

  /**
   * Registra uso de feature
   */
  static logFeatureUsed(metadata: {
    feature: string;
    category: 'spreadsheet' | 'ai' | 'collaboration' | 'export' | 'import' | 'chart';
    success: boolean;
    duration?: number;
    context?: Record<string, any>;
  }): void {
    this.logEvent(ClientEventType.FEATURE_USED, metadata);

    if (this.userId) {
      sentryLogger.userAction(this.userId, `feature_${metadata.feature}`, {
        category: metadata.category,
        success: metadata.success,
        duration: metadata.duration,
        ...metadata.context,
      });
    }
  }

  /**
   * Registra evento de cache
   */
  static logCacheEvent(metadata: {
    operation: 'hit' | 'miss' | 'set' | 'clear';
    key: string;
    size?: number;
    ttl?: number;
  }): void {
    this.logEvent(ClientEventType.CACHE_EVENT, metadata);
  }

  /**
   * Método interno para registrar eventos
   */
  private static logEvent(type: ClientEventType, metadata?: Record<string, any>): void {
    if (!this.isInitialized) return;

    const event: ClientEvent = {
      type,
      ...(this.userId ? { userId: this.userId } : {}),
      sessionId: this.sessionId,
      timestamp: new Date().toISOString(),
      ...(metadata ? { metadata } : {}),
    };

    // Log no console apenas em desenvolvimento
    if (process.env.NODE_ENV === 'development') {
      console.log(`[CLIENT] ${type}`, event);
    }

    // Enviar para sistema de analytics (implementar conforme necessário)
    this.sendToAnalytics(event);
  }

  /**
   * Configura captura de erros globais
   */
  private static setupErrorHandling(): void {
    // Capturar erros JavaScript
    window.addEventListener('error', event => {
      this.logError(new Error(event.message), {
        component: 'global',
        action: 'script_error',
        recoverable: false,
        userImpact: 'minor',
      });
    });

    // Capturar promises rejeitadas
    window.addEventListener('unhandledrejection', event => {
      this.logError(new Error(event.reason), {
        component: 'global',
        action: 'unhandled_promise',
        recoverable: false,
        userImpact: 'minor',
      });
    });
  }

  /**
   * Configura observador de performance
   */
  private static setupPerformanceObserver(): void {
    if (!('PerformanceObserver' in window)) return;

    try {
      this.performanceObserver = new PerformanceObserver(list => {
        for (const entry of list.getEntries()) {
          if (entry.entryType === 'navigation') {
            const navEntry = entry as PerformanceNavigationTiming;
            this.logPerformanceMetric({
              name: 'page_load_time',
              value: navEntry.loadEventEnd - navEntry.fetchStart,
              unit: 'ms',
              category: 'load',
            });
          }

          if (entry.entryType === 'paint') {
            this.logPerformanceMetric({
              name: entry.name,
              value: entry.startTime,
              unit: 'ms',
              category: 'render',
            });
          }
        }
      });

      this.performanceObserver.observe({ entryTypes: ['navigation', 'paint'] });
    } catch (error) {
      console.warn('Failed to setup performance observer:', error);
    }
  }

  /**
   * Gera ID de sessão único
   */
  private static generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Gera ID de requisição único
   */
  private static generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`;
  }

  /**
   * Envia evento para sistema de analytics
   */
  private static sendToAnalytics(event: ClientEvent): void {
    // Implementar integração com Google Analytics, Mixpanel, etc.
    // Por enquanto, apenas armazenar localmente para debug
    if (typeof window !== 'undefined' && window.localStorage) {
      try {
        const events = JSON.parse(localStorage.getItem('client_events') || '[]');
        events.push(event);

        // Manter apenas os últimos 100 eventos
        if (events.length > 100) {
          events.splice(0, events.length - 100);
        }

        localStorage.setItem('client_events', JSON.stringify(events));
      } catch (error) {
        console.warn('Failed to store client event:', error);
      }
    }
  }

  /**
   * Obtém eventos armazenados localmente
   */
  static getStoredEvents(): ClientEvent[] {
    if (typeof window === 'undefined') return [];

    try {
      return JSON.parse(localStorage.getItem('client_events') || '[]');
    } catch {
      return [];
    }
  }

  /**
   * Limpa eventos armazenados
   */
  static clearStoredEvents(): void {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('client_events');
    }
  }

  /**
   * Obtém métricas de sessão atual
   */
  static getSessionMetrics(): {
    sessionId: string;
    userId?: string;
    duration: number;
    eventsCount: number;
    errorsCount: number;
    interactionsCount: number;
  } {
    const events = this.getStoredEvents();
    const sessionEvents = events.filter(e => e.sessionId === this.sessionId);

    return {
      sessionId: this.sessionId,
      ...(this.userId ? { userId: this.userId } : {}),
      duration: Date.now() - parseInt(this.sessionId.split('_')[1] || '0'),
      eventsCount: sessionEvents.length,
      errorsCount: sessionEvents.filter(e => e.type === ClientEventType.ERROR_OCCURRED).length,
      interactionsCount: sessionEvents.filter(e => e.type === ClientEventType.USER_INTERACTION)
        .length,
    };
  }
}

export default ClientLogger;
