#!/usr/bin/env node

/**
 * 🔧 VALIDADOR DE TEMPLATES DE AMBIENTE - EXCEL COPILOT
 *
 * Script para validar se os templates de ambiente estão corretos e consistentes.
 * Parte da Fase 2 - Reestruturação da auditoria de segurança.
 *
 * <AUTHOR> Copilot Team
 * @version 2.0.0
 */

const fs = require('fs');
const path = require('path');

// Cores para output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m',
};

// Configuração dos templates
const templates = {
  development: '.env.local.template',
  production: '.env.production.template',
  test: '.env.test.template',
  example: '.env.example',
};

// Variáveis obrigatórias por ambiente
const requiredVars = {
  development: [
    'NODE_ENV',
    'AUTH_NEXTAUTH_SECRET',
    'AUTH_NEXTAUTH_URL',
    'DB_DATABASE_URL',
    'AI_USE_MOCK',
  ],
  production: [
    'NODE_ENV',
    'AUTH_NEXTAUTH_SECRET',
    'AUTH_NEXTAUTH_URL',
    'DB_DATABASE_URL',
    'AUTH_GOOGLE_CLIENT_ID',
    'AUTH_GOOGLE_CLIENT_SECRET',
  ],
  test: ['NODE_ENV', 'AUTH_NEXTAUTH_SECRET', 'AUTH_NEXTAUTH_URL', 'DB_DATABASE_URL', 'AI_USE_MOCK'],
};

// Variáveis com nomenclatura padronizada (MCP_*)
const standardizedVars = [
  'MCP_VERCEL_TOKEN',
  'MCP_VERCEL_PROJECT_ID',
  'MCP_VERCEL_TEAM_ID',
  'MCP_LINEAR_API_KEY',
  'MCP_GITHUB_TOKEN',
  'MCP_GITHUB_OWNER',
  'MCP_GITHUB_REPO',
];

// Variáveis legadas que não devem aparecer
const legacyVars = [
  'MCP_VERCEL_TOKEN',
  'MCP_VERCEL_PROJECT_ID',
  'MCP_VERCEL_TEAM_ID',
  'MCP_LINEAR_API_KEY',
  'MCP_GITHUB_TOKEN',
  'AI_USE_MOCK',
  'AI_ENABLED',
  'AI_USE_MOCK',
];

/**
 * Lê e parseia um arquivo .env
 */
function parseEnvFile(filePath) {
  if (!fs.existsSync(filePath)) {
    return null;
  }

  const content = fs.readFileSync(filePath, 'utf8');
  const vars = {};
  const comments = [];

  content.split('\n').forEach((line, index) => {
    const trimmed = line.trim();

    if (trimmed.startsWith('#')) {
      comments.push({ line: index + 1, content: trimmed });
    } else if (trimmed && trimmed.includes('=')) {
      const [key, ...valueParts] = trimmed.split('=');
      if (key) {
        vars[key] = valueParts.join('=').replace(/^["']|["']$/g, '');
      }
    }
  });

  return { vars, comments, content };
}

/**
 * Valida um template específico
 */
function validateTemplate(templateName, filePath, environment) {
  console.log(`\n${colors.blue}📋 Validando: ${templateName}${colors.reset}`);
  console.log(`   Arquivo: ${filePath}`);

  const parsed = parseEnvFile(filePath);
  if (!parsed) {
    console.log(`   ${colors.red}❌ Arquivo não encontrado${colors.reset}`);
    return { valid: false, errors: ['Arquivo não encontrado'] };
  }

  const { vars, comments } = parsed;
  const errors = [];
  const warnings = [];
  const success = [];

  // Verificar variáveis obrigatórias
  if (requiredVars[environment]) {
    requiredVars[environment].forEach(varName => {
      if (vars[varName] || parsed.content.includes(varName)) {
        success.push(`✅ ${varName} presente`);
      } else {
        errors.push(`❌ ${varName} ausente`);
      }
    });
  }

  // Verificar nomenclatura padronizada
  standardizedVars.forEach(varName => {
    if (parsed.content.includes(varName)) {
      success.push(`✅ ${varName} usa nomenclatura padronizada`);
    }
  });

  // Verificar variáveis legadas (não devem aparecer)
  legacyVars.forEach(varName => {
    if (parsed.content.includes(varName) && !parsed.content.includes(`# ❌ ${varName}`)) {
      warnings.push(`⚠️ ${varName} usa nomenclatura legada`);
    }
  });

  // Verificar NODE_ENV correto
  if (environment !== 'example') {
    const expectedNodeEnv = environment;
    if (vars.NODE_ENV === expectedNodeEnv) {
      success.push(`✅ NODE_ENV="${expectedNodeEnv}" correto`);
    } else if (vars.NODE_ENV) {
      errors.push(`❌ NODE_ENV="${vars.NODE_ENV}" incorreto, esperado "${expectedNodeEnv}"`);
    }
  }

  // Verificar estrutura de comentários
  const hasInstructions = comments.some(c => c.content.includes('INSTRUÇÕES'));
  if (hasInstructions) {
    success.push('✅ Instruções de configuração presentes');
  } else {
    warnings.push('⚠️ Instruções de configuração ausentes');
  }

  // Exibir resultados
  if (success.length > 0) {
    console.log(`   ${colors.green}Sucessos:${colors.reset}`);
    success.forEach(msg => console.log(`     ${msg}`));
  }

  if (warnings.length > 0) {
    console.log(`   ${colors.yellow}Avisos:${colors.reset}`);
    warnings.forEach(msg => console.log(`     ${msg}`));
  }

  if (errors.length > 0) {
    console.log(`   ${colors.red}Erros:${colors.reset}`);
    errors.forEach(msg => console.log(`     ${msg}`));
  }

  const valid = errors.length === 0;
  console.log(
    `   ${colors.bold}Status: ${valid ? colors.green + '✅ VÁLIDO' : colors.red + '❌ INVÁLIDO'}${colors.reset}`
  );

  return { valid, errors, warnings, success };
}

/**
 * Valida consistência entre templates
 */
function validateConsistency() {
  console.log(`\n${colors.blue}🔄 Validando Consistência Entre Templates${colors.reset}`);

  const devTemplate = parseEnvFile(templates.development);
  const prodTemplate = parseEnvFile(templates.production);

  if (!devTemplate || !prodTemplate) {
    console.log(
      `   ${colors.red}❌ Não foi possível carregar templates para comparação${colors.reset}`
    );
    return false;
  }

  // Verificar se variáveis padronizadas estão em ambos
  let consistent = true;
  standardizedVars.forEach(varName => {
    const inDev = devTemplate.content.includes(varName);
    const inProd = prodTemplate.content.includes(varName);

    if (inDev && inProd) {
      console.log(`   ${colors.green}✅ ${varName} presente em ambos templates${colors.reset}`);
    } else if (!inDev && !inProd) {
      console.log(`   ${colors.yellow}⚠️ ${varName} ausente em ambos templates${colors.reset}`);
    } else {
      console.log(`   ${colors.red}❌ ${varName} inconsistente entre templates${colors.reset}`);
      consistent = false;
    }
  });

  return consistent;
}

/**
 * Função principal
 */
function main() {
  console.log(
    `${colors.bold}${colors.blue}🔧 VALIDADOR DE TEMPLATES DE AMBIENTE - EXCEL COPILOT${colors.reset}`
  );
  console.log(`${colors.blue}Fase 2 - Reestruturação | Tarefa 2.1${colors.reset}`);
  console.log('='.repeat(60));

  let allValid = true;
  const results = {};

  // Validar cada template
  Object.entries(templates).forEach(([env, file]) => {
    const filePath = path.join(process.cwd(), file);
    const result = validateTemplate(file, filePath, env);
    results[env] = result;
    if (!result.valid) {
      allValid = false;
    }
  });

  // Validar consistência
  const consistent = validateConsistency();
  if (!consistent) {
    allValid = false;
  }

  // Resumo final
  console.log(`\n${colors.bold}📊 RESUMO FINAL${colors.reset}`);
  console.log('='.repeat(30));

  Object.entries(results).forEach(([env, result]) => {
    const status = result.valid ? `${colors.green}✅ VÁLIDO` : `${colors.red}❌ INVÁLIDO`;
    console.log(`${env.padEnd(12)}: ${status}${colors.reset}`);
  });

  console.log(
    `Consistência: ${consistent ? `${colors.green}✅ CONSISTENTE` : `${colors.red}❌ INCONSISTENTE`}${colors.reset}`
  );

  const finalStatus = allValid && consistent;
  console.log(
    `\n${colors.bold}Status Geral: ${finalStatus ? `${colors.green}✅ TODOS OS TEMPLATES VÁLIDOS` : `${colors.red}❌ CORREÇÕES NECESSÁRIAS`}${colors.reset}`
  );

  // Próximos passos
  if (finalStatus) {
    console.log(`\n${colors.green}🎉 Tarefa 2.1 CONCLUÍDA com sucesso!${colors.reset}`);
    console.log(
      `${colors.blue}📋 Próximo: Tarefa 2.2 - Consolidação de Configurações MCP${colors.reset}`
    );
  } else {
    console.log(
      `\n${colors.yellow}⚠️ Corrija os problemas identificados antes de prosseguir${colors.reset}`
    );
  }

  process.exit(finalStatus ? 0 : 1);
}

// Executar se chamado diretamente
if (require.main === module) {
  main();
}

module.exports = { validateTemplate, parseEnvFile, validateConsistency };
