/**
 * 🔐 AUTHENTICATION HEALTH CHECK - EXCEL COPILOT
 *
 * Verifica a configuração e funcionamento do sistema de autenticação
 * NextAuth.js e providers OAuth (Google, GitHub)
 *
 * <AUTHOR> Copilot Team
 * @version 1.0.0
 */

import { <PERSON>HealthCheck, HealthStatus, healthLogger } from '../health-checks';
// import { unifiedEnv } from '@/config/unified-environment';

// ============================================================================
// TIPOS E INTERFACES
// ============================================================================

interface AuthConfig {
  enabled: boolean;
  status: string;
  credentials: {
    nextAuthSecret?: string;
    nextAuthUrl?: string;
    googleClientId?: string;
    googleClientSecret?: string;
    githubClientId?: string;
    githubClientSecret?: string;
  };
}

interface AuthIssue {
  type: string;
  message: string;
  severity: string;
}

interface AuthValidationResult {
  valid: boolean;
  issues: AuthIssue[];
}

interface OAuthProvidersResult {
  availableProviders: string[];
  issues: AuthIssue[];
}

interface UrlValidationResult {
  urls: Record<string, string>;
  issues: AuthIssue[];
}

// ============================================================================
// AUTHENTICATION HEALTH CHECK
// ============================================================================

export class AuthHealthCheck extends BaseHealthCheck {
  constructor() {
    super('auth');
  }

  protected async check(): Promise<{
    status: HealthStatus;
    details?: Record<string, string | number | boolean | undefined>;
  }> {
    try {
      // Verificar variáveis de ambiente diretamente
      const nextAuthSecret = process.env.AUTH_NEXTAUTH_SECRET;
      const nextAuthUrl = process.env.AUTH_NEXTAUTH_URL;
      const googleClientId = process.env.AUTH_GOOGLE_CLIENT_ID;
      const googleClientSecret = process.env.AUTH_GOOGLE_CLIENT_SECRET;
      const githubClientId = process.env.AUTH_GITHUB_CLIENT_ID;
      const githubClientSecret = process.env.AUTH_GITHUB_CLIENT_SECRET;

      const issues: AuthIssue[] = [];

      // Verificar configurações básicas
      if (!nextAuthSecret) {
        issues.push({
          type: 'missing_secret',
          message: 'NEXTAUTH_SECRET not configured',
          severity: 'critical',
        });
      }

      if (!nextAuthUrl) {
        issues.push({
          type: 'missing_url',
          message: 'NEXTAUTH_URL not configured',
          severity: 'critical',
        });
      }

      // Verificar providers
      const availableProviders: string[] = [];

      if (googleClientId && googleClientSecret) {
        availableProviders.push('google');
      }

      if (githubClientId && githubClientSecret) {
        availableProviders.push('github');
      }

      // Determinar status
      let status: HealthStatus = 'healthy';

      if (issues.some(issue => issue.severity === 'critical')) {
        status = 'unhealthy';
      } else if (issues.length > 0) {
        status = 'degraded';
      }

      healthLogger.info('Authentication health check completed', {
        status,
        providersAvailable: availableProviders,
        issuesCount: issues.length,
      });

      return {
        status,
        details: {
          message: 'Authentication system checked',
          providersCount: availableProviders.length,
          issuesCount: issues.length,
          hasSecret: !!nextAuthSecret,
          hasUrl: !!nextAuthUrl,
          hasIssues: issues.length > 0,
        },
      };
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      const errorStack = error instanceof Error ? error.stack : undefined;

      healthLogger.error('Authentication health check failed', {
        error: errorMessage,
        stack: errorStack,
      });

      return {
        status: 'unhealthy',
        details: {
          message: 'Authentication health check failed',
          providersCount: 0,
          hasSecret: false,
          hasUrl: false,
          error: errorMessage,
        },
      };
    }
  }

  /**
   * Valida a configuração básica de autenticação
   */
  private validateAuthConfig(authConfig: AuthConfig): AuthValidationResult {
    const issues: AuthIssue[] = [];

    // Verificar NEXTAUTH_SECRET
    const secret = authConfig.credentials.nextAuthSecret;
    if (!secret) {
      issues.push({
        type: 'missing_secret',
        message: 'NEXTAUTH_SECRET not configured',
        severity: 'critical',
      });
    } else if (secret.length < 32) {
      issues.push({
        type: 'weak_secret',
        message: 'NEXTAUTH_SECRET should be at least 32 characters',
        severity: 'high',
      });
    } else if (secret === 'excel-copilot-secret-key-development') {
      // TODO: Implementar quando unifiedEnv estiver disponível
      // const env = unifiedEnv.getConfig().NODE_ENV;
      const env = process.env.NODE_ENV;
      if (env === 'production') {
        issues.push({
          type: 'default_secret',
          message: 'Using default development secret in production',
          severity: 'critical',
        });
      }
    }

    // Verificar NEXTAUTH_URL
    const url = authConfig.credentials.nextAuthUrl;
    if (!url) {
      issues.push({
        type: 'missing_url',
        message: 'NEXTAUTH_URL not configured',
        severity: 'critical',
      });
    } else {
      try {
        const parsedUrl = new URL(url);
        // TODO: Implementar quando unifiedEnv estiver disponível
        // const env = unifiedEnv.getConfig().NODE_ENV;
        const env = process.env.NODE_ENV;

        if (env === 'production' && parsedUrl.hostname === 'localhost') {
          issues.push({
            type: 'localhost_in_production',
            message: 'NEXTAUTH_URL cannot be localhost in production',
            severity: 'critical',
          });
        }
      } catch {
        issues.push({
          type: 'invalid_url',
          message: 'NEXTAUTH_URL is not a valid URL',
          severity: 'critical',
        });
      }
    }

    return {
      valid: issues.filter(i => i.severity === 'critical').length === 0,
      issues,
    };
  }

  /**
   * Verifica os providers OAuth disponíveis
   */
  private async checkOAuthProviders(authConfig: AuthConfig): Promise<OAuthProvidersResult> {
    const issues: AuthIssue[] = [];
    const availableProviders: string[] = [];

    // Verificar Google OAuth
    const googleClientId = authConfig.credentials.googleClientId;
    const googleClientSecret = authConfig.credentials.googleClientSecret;

    if (googleClientId && googleClientSecret) {
      availableProviders.push('google');

      // Verificar se as credenciais parecem válidas
      if (!googleClientId.includes('.apps.googleusercontent.com')) {
        issues.push({
          type: 'invalid_google_client_id',
          message: 'Google Client ID format appears invalid',
          severity: 'high',
        });
      }
    }

    // Verificar GitHub OAuth
    const githubClientId = authConfig.credentials.githubClientId;
    const githubClientSecret = authConfig.credentials.githubClientSecret;

    if (githubClientId && githubClientSecret) {
      availableProviders.push('github');
    }

    // Verificar se há pelo menos um provider em produção
    // TODO: Implementar quando unifiedEnv estiver disponível
    // const env = unifiedEnv.getConfig().NODE_ENV;
    const env = process.env.NODE_ENV;
    if (env === 'production' && availableProviders.length === 0) {
      issues.push({
        type: 'no_providers',
        message: 'No OAuth providers configured for production',
        severity: 'critical',
      });
    }

    return {
      availableProviders,
      issues,
    };
  }

  /**
   * Valida URLs e callbacks
   */
  private validateUrls(authConfig: AuthConfig): UrlValidationResult {
    const issues: AuthIssue[] = [];
    const urls: Record<string, string> = {};

    const nextAuthUrl = authConfig.credentials.nextAuthUrl;
    if (nextAuthUrl) {
      urls.nextAuthUrl = nextAuthUrl;
      urls.googleCallback = `${nextAuthUrl}/api/auth/callback/google`;
      urls.githubCallback = `${nextAuthUrl}/api/auth/callback/github`;
    }

    return { urls, issues };
  }

  /**
   * Avalia o nível de segurança da configuração
   */
  private getSecurityLevel(authConfig: AuthConfig): string {
    let score = 0;

    // NEXTAUTH_SECRET strength
    const secret = authConfig.credentials.nextAuthSecret;
    if (secret && secret.length >= 32) score += 2;
    else if (secret && secret.length >= 16) score += 1;

    // Multiple providers
    const hasGoogle =
      authConfig.credentials.googleClientId && authConfig.credentials.googleClientSecret;
    const hasGitHub =
      authConfig.credentials.githubClientId && authConfig.credentials.githubClientSecret;

    if (hasGoogle && hasGitHub) score += 2;
    else if (hasGoogle || hasGitHub) score += 1;

    // Production URL
    // TODO: Implementar quando unifiedEnv estiver disponível
    // const env = unifiedEnv.getConfig().NODE_ENV;
    const env = process.env.NODE_ENV;
    const url = authConfig.credentials.nextAuthUrl;
    if (env === 'production' && url && !url.includes('localhost')) score += 1;

    if (score >= 5) return 'excellent';
    if (score >= 4) return 'good';
    if (score >= 2) return 'fair';
    return 'poor';
  }
}

// ============================================================================
// FACTORY FUNCTION
// ============================================================================

/**
 * Cria uma instância do Auth Health Check
 */
export function createAuthHealthCheck(): AuthHealthCheck {
  return new AuthHealthCheck();
}

// ============================================================================
// UTILITÁRIOS ESPECÍFICOS
// ============================================================================

/**
 * Verifica se a autenticação está configurada corretamente
 */
export function isAuthConfigured(): boolean {
  // TODO: Implementar quando unifiedEnv estiver disponível
  // const authConfig = unifiedEnv.getAuthConfig();
  // return (
  //   authConfig.enabled &&
  //   !!authConfig.credentials.nextAuthSecret &&
  //   !!authConfig.credentials.nextAuthUrl
  // );

  // Implementação temporária baseada em variáveis de ambiente
  const nextAuthSecret = process.env.AUTH_NEXTAUTH_SECRET;
  const nextAuthUrl = process.env.AUTH_NEXTAUTH_URL;

  return !!(nextAuthSecret && nextAuthUrl);
}

/**
 * Obtém informações sobre a configuração de autenticação
 */
export function getAuthInfo() {
  // TODO: Implementar quando unifiedEnv estiver disponível
  // const authConfig = unifiedEnv.getAuthConfig();

  // Implementação temporária baseada em variáveis de ambiente
  const nextAuthSecret = process.env.AUTH_NEXTAUTH_SECRET;
  const nextAuthUrl = process.env.AUTH_NEXTAUTH_URL;
  const googleClientId = process.env.AUTH_GOOGLE_CLIENT_ID;
  const googleClientSecret = process.env.AUTH_GOOGLE_CLIENT_SECRET;
  const githubClientId = process.env.AUTH_GITHUB_CLIENT_ID;
  const githubClientSecret = process.env.AUTH_GITHUB_CLIENT_SECRET;

  return {
    enabled: !!(nextAuthSecret && nextAuthUrl),
    status: 'configured',
    hasSecret: !!nextAuthSecret,
    hasUrl: !!nextAuthUrl,
    providers: {
      google: !!(googleClientId && googleClientSecret),
      github: !!(githubClientId && githubClientSecret),
    },
  };
}
