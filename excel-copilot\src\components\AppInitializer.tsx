'use client';

import { useEffect, useState, useRef } from 'react';

import { initializeApplication, initializationState } from '@/lib/app-initializer';
import { setupGlobalErrorHandlers } from '@/lib/error-handler';

interface AppInitializerProps {
  children: React.ReactNode;
}

// Fases de inicialização
type InitPhase =
  | 'not-started'
  | 'critical-services'
  | 'high-priority'
  | 'medium-priority'
  | 'low-priority'
  | 'health-checks'
  | 'complete'
  | 'error';

export function AppInitializer({ children }: AppInitializerProps) {
  const [isInitialized, setIsInitialized] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [initPhase, setInitPhase] = useState<InitPhase>('not-started');
  const [progress, setProgress] = useState(0);
  const initStartTime = useRef<number>(0);
  const progressUpdateInterval = useRef<number | null>(null);

  // Monitorar o progresso de inicialização
  useEffect(() => {
    // ID para o requestIdleCallback
    let idleCallbackId: number;

    // Função para verificar o estado da inicialização
    const checkInitializationStatus = () => {
      // Se o estado estiver disponível
      if (initializationState) {
        // Se já terminou a inicialização
        if (initializationState.initialized) {
          setIsInitialized(true);
          setInitPhase('complete');
          setProgress(100);
          setIsLoading(false);
          return true;
        }

        // Se estiver em progresso, atualizar a fase
        if (initializationState.initializationResult) {
          const details = initializationState.initializationResult.details || {};

          // Determinar fase atual com base nos detalhes
          if (
            details &&
            typeof details === 'object' &&
            'criticalServicesReady' in details &&
            details.criticalServicesReady
          ) {
            const services =
              'initializedServices' in details
                ? (details.initializedServices as string[]) || []
                : [];

            if ('healthChecksComplete' in details && details.healthChecksComplete) {
              setInitPhase('health-checks');
              setProgress(95);
            } else if (services.includes('ai')) {
              setInitPhase('low-priority');
              setProgress(80);
            } else if (services.includes('telemetry')) {
              setInitPhase('medium-priority');
              setProgress(60);
            } else if (services.includes('rateLimiters')) {
              setInitPhase('high-priority');
              setProgress(40);
            } else {
              setInitPhase('critical-services');
              setProgress(20);
            }
          }
        }
      }

      return false;
    };

    // Função para agendar a próxima verificação
    const scheduleNextCheck = () => {
      if (typeof window !== 'undefined' && 'requestIdleCallback' in window) {
        idleCallbackId = window.requestIdleCallback(() => {
          if (!checkInitializationStatus()) {
            scheduleNextCheck();
          }
        });
      } else {
        // Fallback para navegadores que não suportam requestIdleCallback
        setTimeout(() => {
          if (!checkInitializationStatus()) {
            scheduleNextCheck();
          }
        }, 200);
      }
    };

    // Iniciar o monitoramento
    scheduleNextCheck();

    // Cleanup
    return () => {
      if (typeof window !== 'undefined' && 'cancelIdleCallback' in window && idleCallbackId) {
        window.cancelIdleCallback(idleCallbackId);
      }
    };
  }, []);

  // Inicialização da aplicação
  useEffect(() => {
    // Configurar tratamento de erros globais
    setupGlobalErrorHandlers();

    // Registrar o tempo de início
    initStartTime.current = performance.now();
    setInitPhase('not-started');

    // Simulação de progresso durante inicialização
    const simulateProgress = () => {
      setProgress(prev => {
        // Limitar o progresso máximo baseado na fase
        const maxProgress =
          initPhase === 'critical-services'
            ? 25
            : initPhase === 'high-priority'
              ? 50
              : initPhase === 'medium-priority'
                ? 75
                : initPhase === 'low-priority'
                  ? 90
                  : initPhase === 'health-checks'
                    ? 95
                    : initPhase === 'complete'
                      ? 100
                      : 10;

        // Avançar o progresso gradualmente, mas não ultrapassar o máximo da fase
        return Math.min(prev + 0.5, maxProgress);
      });
    };

    // Iniciar simulação de progresso
    if (isLoading) {
      progressUpdateInterval.current = window.setInterval(simulateProgress, 150);
    }

    // Inicialização assíncrona da aplicação
    const initialize = async () => {
      try {
        setInitPhase('critical-services');

        // No cliente, pular a inicialização completa e apenas configurar o básico
        if (typeof window !== 'undefined') {
          // Log apenas em desenvolvimento
          if (process.env.NODE_ENV === 'development') {
            // eslint-disable-next-line no-console
            console.log(
              'AppInitializer: Detectado ambiente cliente, pulando inicialização de serviços servidor'
            );
          }
          setIsInitialized(true);
          setInitPhase('complete');
          setProgress(100);
          return;
        }

        // APENAS NO SERVIDOR - inicializar aplicação
        const result = await initializeApplication();

        if (result.success) {
          setIsInitialized(true);
          setInitPhase('complete');

          // Calcular tempo total de inicialização
          const _initTime = performance.now() - initStartTime.current;
          if (process.env.NODE_ENV === 'development') {
            // Aplicação inicializada em ${_initTime.toFixed(2)}ms
          }
        } else {
          setError(result.error || 'Falha ao inicializar a aplicação');
          setInitPhase('error');
          console.error('Falha na inicialização:', result.error);
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Erro desconhecido na inicialização');
        setInitPhase('error');
        console.error('Erro na inicialização:', err);
      } finally {
        setIsLoading(false);

        // Limpar o intervalo de simulação de progresso
        if (progressUpdateInterval.current !== null) {
          clearInterval(progressUpdateInterval.current);
          progressUpdateInterval.current = null;
        }

        // Garantir que o progresso chegue a 100% quando finalizar
        if (initPhase !== 'error') {
          setProgress(100);
        }
      }
    };

    initialize();

    // Cleanup
    return () => {
      if (progressUpdateInterval.current !== null) {
        clearInterval(progressUpdateInterval.current);
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Mapear fases para mensagens amigáveis
  const getPhaseMessage = (phase: InitPhase): string => {
    switch (phase) {
      case 'not-started':
        return 'Preparando ambiente...';
      case 'critical-services':
        return 'Inicializando serviços essenciais...';
      case 'high-priority':
        return 'Preparando interface de usuário...';
      case 'medium-priority':
        return 'Carregando funcionalidades avançadas...';
      case 'low-priority':
        return 'Inicializando recursos de IA...';
      case 'health-checks':
        return 'Verificando conexões externas...';
      case 'complete':
        return 'Inicialização concluída!';
      case 'error':
        return 'Erro de inicialização';
      default:
        return 'Carregando...';
    }
  };

  // Enquanto carrega, mostra um indicador de progresso
  if (isLoading) {
    return (
      <div className="flex h-screen w-full items-center justify-center bg-background">
        <div className="flex flex-col items-center space-y-6 max-w-md p-8">
          <div className="relative h-10 w-10">
            <div className="absolute h-10 w-10 animate-ping rounded-full bg-primary opacity-75"></div>
            <div className="relative flex h-10 w-10 items-center justify-center rounded-full bg-primary">
              <svg
                className="h-5 w-5 text-white"
                fill="none"
                height="24"
                stroke="currentColor"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                viewBox="0 0 24 24"
                width="24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path d="M21 8V5a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v3" />
                <path d="M21 16v3a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-3" />
                <path d="M4 12H2" />
                <path d="M10 12H8" />
                <path d="M16 12h-2" />
                <path d="M22 12h-2" />
              </svg>
            </div>
          </div>
          <div className="space-y-4 text-center w-full">
            <h3 className="text-xl font-medium">Inicializando Excel Copilot</h3>
            <p className="text-sm text-muted-foreground">{getPhaseMessage(initPhase)}</p>

            {/* Barra de progresso */}
            <div className="w-full bg-secondary rounded-full h-2.5 overflow-hidden">
              <div
                className="bg-primary h-2.5 rounded-full transition-all duration-300 ease-out"
                style={{ width: `${progress}%` }}
              ></div>
            </div>

            {/* Fases de inicialização */}
            <div className="flex justify-between text-xs text-muted-foreground pt-1">
              <span className={initPhase !== 'not-started' ? 'text-primary font-medium' : ''}>
                Essenciais
              </span>
              <span
                className={
                  initPhase === 'high-priority' ||
                  initPhase === 'medium-priority' ||
                  initPhase === 'low-priority' ||
                  initPhase === 'health-checks' ||
                  initPhase === 'complete'
                    ? 'text-primary font-medium'
                    : ''
                }
              >
                Interface
              </span>
              <span
                className={
                  initPhase === 'medium-priority' ||
                  initPhase === 'low-priority' ||
                  initPhase === 'health-checks' ||
                  initPhase === 'complete'
                    ? 'text-primary font-medium'
                    : ''
                }
              >
                Avançados
              </span>
              <span
                className={
                  initPhase === 'low-priority' ||
                  initPhase === 'health-checks' ||
                  initPhase === 'complete'
                    ? 'text-primary font-medium'
                    : ''
                }
              >
                IA
              </span>
              <span
                className={
                  initPhase === 'health-checks' || initPhase === 'complete'
                    ? 'text-primary font-medium'
                    : ''
                }
              >
                Verificação
              </span>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Se houver erro na inicialização
  if (error) {
    return (
      <div className="flex h-screen w-full items-center justify-center bg-background">
        <div className="flex flex-col items-center space-y-4 max-w-md p-6">
          <div className="h-10 w-10 rounded-full bg-destructive flex items-center justify-center">
            <svg
              className="h-5 w-5 text-white"
              fill="none"
              height="24"
              stroke="currentColor"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              viewBox="0 0 24 24"
              width="24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path d="M18 6 6 18" />
              <path d="m6 6 12 12" />
            </svg>
          </div>
          <div className="space-y-2 text-center">
            <h3 className="text-xl font-medium">Erro de inicialização</h3>
            <p className="text-sm text-muted-foreground mb-4">{error}</p>
            <button
              className="inline-flex h-9 items-center justify-center rounded-md bg-primary px-4 py-2 text-sm font-medium text-primary-foreground shadow hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-primary"
              onClick={() => window.location.reload()}
            >
              Tentar novamente
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Quando inicializado com sucesso, renderizar filhos
  if (isInitialized) {
    return <>{children}</>;
  }

  // Fallback: caso esteja em estado inconsistente
  return (
    <div className="flex h-screen w-full items-center justify-center bg-background">
      <div className="text-center">
        <p>Estado de inicialização inconsistente. Atualize a página.</p>
        <button
          className="mt-4 inline-flex h-9 items-center justify-center rounded-md bg-primary px-4 py-2 text-sm font-medium text-primary-foreground shadow hover:bg-primary/90"
          onClick={() => window.location.reload()}
        >
          Atualizar
        </button>
      </div>
    </div>
  );
}
