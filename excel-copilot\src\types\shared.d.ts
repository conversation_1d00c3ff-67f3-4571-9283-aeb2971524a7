/**
 * Tipos compartilhados para Excel Copilot
 * Define interfaces reutilizáveis para resolver problemas comuns de tipagem
 */

import { Session } from 'next-auth';
import { ReactNode } from 'react';

/**
 * Estende Session.user para garantir que sempre tenha um id
 * Resolve problemas com (session.user as unknown).id
 */
export interface SessionWithId extends Session {
  user: {
    id: string;
    name?: string | null;
    email?: string | null;
    image?: string | null;
  };
}

/**
 * Estende EventTarget para permitir tipagem de eventos
 */
export interface FormInputEvent {
  target: {
    value: string;
  };
}

/**
 * Tipo de valor para tooltips e formatters de gráficos
 */
export type ValueType = string | number;

/**
 * Interface para resultado de operações
 */
export interface OperationResult<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  status?: number;
}

/**
 * Interfaces para tipos de mensagens usadas no sistema
 */
export interface ErrorInfo {
  type: 'error';
  message: string;
  details?: any;
}

/**
 * Interface para propriedades de componentes com prefixo _
 */
export interface ComponentWithUnderscoreProps {
  _className?: string;
  _variant?: string;
  _wrapperClassName?: string;
  _iconClassName?: string;
  _icon?: ReactNode;
  _locale?: string;
  _theme?: string;
  _interClassName?: string;
  [key: `_${string}`]: any;
}

/**
 * Interface para tipos do Custom Event
 */
export interface CustomEventMap {
  cursor_position: CustomEvent<{ row: number; col: number }>;
  cell_changed: CustomEvent<any>;
}

/**
 * Estende o request do NextJS para rotas dinâmicas
 */
export interface PageProps {
  params: { [key: string]: string };
  searchParams: { [key: string]: string | string[] | undefined } | null;
}

/**
 * Para dados formatados como objetos
 */
export interface DataObject<T = any> {
  [key: string]: T;
}

/**
 * Mapamento para tipos de hooks comuns
 */
export interface HookResultMap {
  [key: string]: any;
}
