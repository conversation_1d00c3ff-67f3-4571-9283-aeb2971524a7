import { useState, useEffect, useCallback, useRef } from 'react';

import { ChunkManager } from '@/lib/chunk-manager';
import { logger } from '@/lib/logger';

/**
 * Opções para o hook useChunkedData
 */
interface UseChunkedDataOptions<T> {
  // Tamanho inicial de chunks a serem carregados
  chunkSize?: number;
  // Número total de itens, se conhecido
  totalItems?: number;
  // Função para carregar um chunk específico
  loadChunk: (chunkIndex: number) => Promise<T[]>;
  // Dados iniciais para pré-carregar o primeiro chunk
  initialData?: T[];
  // Indicador se deve manter chunks não utilizados em memória
  keepUnusedChunks?: boolean;
  // Número de chunks de buffer para manter ao redor dos que estão sendo usados
  bufferSize?: number;
  // Callback para quando o estado de carregamento mudar
  onLoadingChange?: (isLoading: boolean) => void;
  // Callback quando ocorrer um erro
  onError?: (error: Error) => void;
}

/**
 * Hook para gerenciar e acessar dados em chunks para grandes conjuntos de dados
 */
export function useChunkedData<T = unknown>(options: UseChunkedDataOptions<T>) {
  const {
    chunkSize = 1000,
    totalItems = 0,
    loadChunk,
    initialData = [],
    keepUnusedChunks = false,
    bufferSize = 2,
    onLoadingChange,
    onError,
  } = options;

  // Refs para manter estado entre renderizações
  const chunkManagerRef = useRef<ChunkManager<T> | null>(null);
  const activeChunksRef = useRef<Set<number>>(new Set());

  // Estado para controlar carregamento e erros
  const [isLoading, setIsLoading] = useState(false);
  const [loadingChunks, setLoadingChunks] = useState<number[]>([]);
  const [error, setError] = useState<Error | null>(null);
  const [itemCount, setItemCount] = useState(totalItems || initialData.length);

  // Inicializar o ChunkManager
  useEffect(() => {
    chunkManagerRef.current = new ChunkManager<T>(initialData, totalItems, chunkSize);

    // Se tivermos um total conhecido, defini-lo
    if (totalItems > 0) {
      chunkManagerRef.current.setTotalItems(totalItems);
      setItemCount(totalItems);
    }

    return () => {
      // Limpeza ao desmontar
      chunkManagerRef.current = null;
    };
  }, [initialData, totalItems, chunkSize]);

  /**
   * Função para carregar um chunk específico através do loadChunk fornecido
   */
  const fetchChunk = useCallback(
    async (chunkIndex: number): Promise<T[]> => {
      if (!loadChunk) {
        throw new Error('Função loadChunk não fornecida');
      }

      try {
        setIsLoading(true);
        setLoadingChunks(prev => [...prev, chunkIndex]);
        onLoadingChange?.(true);

        // Calcular o offset e limite para o chunk
        const offset = chunkIndex * chunkSize;

        // Carregar o chunk
        const chunkData = await loadChunk(chunkIndex);

        // Atualizar o total de itens se necessário (apenas para o primeiro chunk)
        if (chunkIndex === 0 && !totalItems && chunkManagerRef.current) {
          const estimatedTotal = Math.max(offset + chunkData.length, itemCount);
          chunkManagerRef.current.setTotalItems(estimatedTotal);
          setItemCount(estimatedTotal);
        }

        // Registrar o chunk como ativo
        activeChunksRef.current.add(chunkIndex);

        return chunkData;
      } catch (err) {
        const error = err instanceof Error ? err : new Error(String(err));
        logger.error(`Erro ao carregar chunk ${chunkIndex}:`, error);
        setError(error);
        onError?.(error);
        throw error;
      } finally {
        setLoadingChunks(prev => prev.filter(idx => idx !== chunkIndex));
        if (loadingChunks.length <= 1) {
          setIsLoading(false);
          onLoadingChange?.(false);
        }
      }
    },
    [loadChunk, chunkSize, totalItems, itemCount, loadingChunks, onLoadingChange, onError]
  );

  /**
   * Obter um item específico pelo índice
   */
  const getItem = useCallback(
    async (index: number): Promise<T | undefined> => {
      if (!chunkManagerRef.current) return undefined;

      try {
        return await chunkManagerRef.current.getItem(index, fetchChunk);
      } catch (err) {
        const error = err instanceof Error ? err : new Error(String(err));
        logger.error(`Erro ao obter item ${index}:`, error);
        setError(error);
        onError?.(error);
        return undefined;
      }
    },
    [fetchChunk, onError]
  );

  /**
   * Obter um intervalo de itens
   */
  const getRange = useCallback(
    async (startIndex: number, endIndex: number): Promise<T[]> => {
      if (!chunkManagerRef.current) return [];

      try {
        const result = await chunkManagerRef.current.getRange(startIndex, endIndex, fetchChunk);

        // Gerenciar chunks ativos e limpar não utilizados
        if (!keepUnusedChunks) {
          const startChunk = chunkManagerRef.current.getChunkIndexForItem(startIndex);
          const endChunk = chunkManagerRef.current.getChunkIndexForItem(endIndex);

          // Manter apenas os chunks ativos
          const activeChunks = [];
          for (let i = startChunk; i <= endChunk; i++) {
            activeChunks.push(i);
          }

          // Limpar chunks não utilizados
          chunkManagerRef.current.clearUnusedChunks(activeChunks, bufferSize);
        }

        return result;
      } catch (err) {
        const error = err instanceof Error ? err : new Error(String(err));
        logger.error(`Erro ao obter intervalo ${startIndex}-${endIndex}:`, error);
        setError(error);
        onError?.(error);
        return [];
      }
    },
    [fetchChunk, onError, keepUnusedChunks, bufferSize]
  );

  /**
   * Atualizar um item específico
   */
  const updateItem = useCallback(
    (index: number, value: T): void => {
      if (!chunkManagerRef.current) return;

      try {
        chunkManagerRef.current.setItem(index, value);
      } catch (err) {
        const error = err instanceof Error ? err : new Error(String(err));
        logger.error(`Erro ao atualizar item ${index}:`, error);
        setError(error);
        onError?.(error);
      }
    },
    [onError]
  );

  /**
   * Definir o total de itens
   */
  const setTotal = useCallback((newTotal: number): void => {
    if (!chunkManagerRef.current) return;

    chunkManagerRef.current.setTotalItems(newTotal);
    setItemCount(newTotal);
  }, []);

  return {
    getItem,
    getRange,
    updateItem,
    isLoading,
    loadingChunks,
    error,
    totalItems: itemCount,
    chunkSize,
    activeChunks: Array.from(activeChunksRef.current),
    setTotal,
  };
}
