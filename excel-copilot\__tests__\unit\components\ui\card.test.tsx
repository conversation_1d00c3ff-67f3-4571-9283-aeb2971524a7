import React from 'react';
import { render, screen } from '@testing-library/react';
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  CardFooter,
} from '@/components/ui/card';

describe('Card Component', () => {
  test('renderiza um Card com todos os subcomponentes', () => {
    render(
      <Card>
        <CardHeader>
          <CardTitle>Título do Card</CardTitle>
          <CardDescription>Descrição do card</CardDescription>
        </CardHeader>
        <CardContent>
          <p>Conteúdo do card</p>
        </CardContent>
        <CardFooter>
          <p>Rodapé do card</p>
        </CardFooter>
      </Card>
    );

    expect(screen.getByText('Título do Card')).toBeDefined();
    expect(screen.getByText('Descrição do card')).toBeDefined();
    expect(screen.getByText('Conteúdo do card')).toBeDefined();
    expect(screen.getByText('Rodapé do card')).toBeDefined();
  });

  test('aplica classes de estilo corretas', () => {
    render(
      <Card className="test-card">
        <CardHeader className="test-header">
          <CardTitle className="test-title">Título</CardTitle>
          <CardDescription className="test-desc">Descrição</CardDescription>
        </CardHeader>
        <CardContent className="test-content">Conteúdo</CardContent>
        <CardFooter className="test-footer">Rodapé</CardFooter>
      </Card>
    );

    expect(screen.getByText('Título')).toHaveClass('test-title');
    expect(screen.getByText('Descrição')).toHaveClass('test-desc');
    expect(screen.getByText('Conteúdo')).toBeInTheDocument();
    expect(screen.getByText('Rodapé')).toBeInTheDocument();
  });
});
