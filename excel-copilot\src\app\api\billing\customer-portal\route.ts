export const dynamic = 'force-dynamic';

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';

import { logger } from '@/lib/logger';
import { stripe } from '@/lib/stripe';
import { prisma } from '@/server/db/client';

export async function POST(req: NextRequest) {
  try {
    // Verificar se o Stripe está configurado
    if (!stripe) {
      return NextResponse.json({ error: 'Stripe não está configurado.' }, { status: 503 });
    }

    // Verificar autenticação
    const session = await getServerSession();
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Não autorizado. Faça login para continuar.' },
        { status: 401 }
      );
    }

    // Obter dados do corpo da requisição
    const { returnUrl } = await req.json();

    const userId = session.user.id;

    // Obter o usuário e suas assinaturas ativas
    const user = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      return NextResponse.json({ error: 'Usuário não encontrado.' }, { status: 404 });
    }

    // Buscar assinaturas ativas do usuário
    const subscriptions = await prisma.subscription.findMany({
      where: {
        userId,
        OR: [{ status: 'active' }, { status: 'trialing' }, { status: 'past_due' }],
      },
      orderBy: { createdAt: 'desc' },
      take: 1,
    });

    const subscription = subscriptions[0];

    if (!subscription?.stripeCustomerId) {
      return NextResponse.json({ error: 'Nenhuma assinatura ativa encontrada.' }, { status: 400 });
    }

    // Criar sessão do portal de clientes do Stripe
    const portalSession = await stripe.billingPortal.sessions.create({
      customer: subscription.stripeCustomerId,
      return_url: returnUrl || `${req.headers.get('origin')}/dashboard`,
    });

    // Retornar a URL da sessão
    return NextResponse.json({ url: portalSession.url });
  } catch (error) {
    logger.error('[CUSTOMER_PORTAL_ERROR]', error);
    return NextResponse.json(
      {
        error: 'Erro ao acessar o portal do cliente. Por favor, tente novamente.',
        details: process.env.NODE_ENV === 'development' ? String(error) : undefined,
      },
      { status: 500 }
    );
  }
}
