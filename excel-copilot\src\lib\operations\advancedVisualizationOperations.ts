import { ExcelOperationType } from '../../types';
import { ExcelOperation } from '../excel/types';
import { columnLetterToIndex } from '../utils';

/**
 * Interface para configuração de visualizações avançadas
 */
export interface AdvancedVisualizationConfig {
  // Configuração básica da visualização
  type: VisualizationType;
  sourceRange: string;
  destinationRange?: string;
  title?: string;

  // Configurações de cores e estilo
  colors?: string[];
  theme?: 'default' | 'monochrome' | 'gradient' | 'pastel' | 'bold' | 'vintage';

  // Configurações específicas por tipo
  dimensions?: string[];
  measures?: string[];

  // Opções avançadas
  interactive?: boolean;
  animation?: boolean;
  viewMode?: '2d' | '3d';

  // Metadados
  id?: string;
}

/**
 * Tipos disponíveis de visualizações avançadas
 */
export type VisualizationType =
  | '3d-bar'
  | '3d-scatter'
  | '3d-surface'
  | 'network-graph'
  | 'force-directed'
  | 'geo-map'
  | 'heat-map'
  | 'tree-map'
  | 'voronoi'
  | 'parallel-coordinates'
  | 'parallel-sets'
  | 'correlation-matrix'
  | 'dendrogram'
  | 'horizon-chart'
  | 'motion-chart'
  | 'node-link';

/**
 * Extrai operações de visualizações avançadas a partir do texto de comando
 */
export function extractAdvancedVisualizationOperations(text: string): ExcelOperation[] {
  const operations: ExcelOperation[] = [];

  // Padrões para diferentes tipos de visualizações
  const patterns = [
    // Padrão para visualização 3D
    {
      regex:
        /(?:crie|adicione|gere|insira)\s+(?:uma)?\s+visualização\s+(?:em\s+)?3[dD](?:\s+d[eo])?\s+(?:tipo\s+)?(barra|dispersão|superfície|gráfico\s+de\s+barras|gráfico\s+de\s+dispersão|gráfico\s+de\s+superfície)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,
      handler: (matches: RegExpMatchArray): ExcelOperation | null => {
        let type: VisualizationType = '3d-bar';
        const vizTypeText = matches[1]?.toLowerCase() || '';

        if (vizTypeText.includes('disp') || vizTypeText.includes('scatt')) {
          type = '3d-scatter';
        } else if (vizTypeText.includes('super') || vizTypeText.includes('surf')) {
          type = '3d-surface';
        }

        const sourceRange = matches[2]?.trim();
        const title = matches[3] || matches[4] || matches[5];

        if (!sourceRange) {
          return null;
        }

        return {
          type: ExcelOperationType.ADVANCED_VISUALIZATION,
          data: {
            type,
            sourceRange,
            title: title || `Visualização 3D de ${vizTypeText}`,
            viewMode: '3d',
            animation: true,
            interactive: true,
          },
        };
      },
    },

    // Padrão para mapa de calor
    {
      regex:
        /(?:crie|adicione|gere|insira)\s+(?:um)?\s+(?:mapa\s+de\s+calor|heatmap)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,
      handler: (matches: RegExpMatchArray): ExcelOperation | null => {
        const sourceRange = matches[1]?.trim();
        const title = matches[2] || matches[3] || matches[4];

        if (!sourceRange) {
          return null;
        }

        return {
          type: ExcelOperationType.ADVANCED_VISUALIZATION,
          data: {
            type: 'heat-map',
            sourceRange,
            title: title || 'Mapa de Calor',
            colors: ['#0033CC', '#00CCFF', '#FFFF00', '#FF6600', '#CC0000'],
            interactive: true,
          },
        };
      },
    },

    // Padrão para treemap
    {
      regex:
        /(?:crie|adicione|gere|insira)\s+(?:um)?\s+(?:treemap|mapa\s+de\s+árvore|mapa\s+de\s+arvore)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,
      handler: (matches: RegExpMatchArray): ExcelOperation | null => {
        const sourceRange = matches[1]?.trim();
        const title = matches[2] || matches[3] || matches[4];

        if (!sourceRange) {
          return null;
        }

        return {
          type: ExcelOperationType.ADVANCED_VISUALIZATION,
          data: {
            type: 'tree-map',
            sourceRange,
            title: title || 'Treemap',
            theme: 'gradient',
          },
        };
      },
    },

    // Padrão para gráfico de rede
    {
      regex:
        /(?:crie|adicione|gere|insira)\s+(?:um)?\s+(?:gráfico\s+de\s+rede|network\s+graph|grafo\s+de\s+rede)(?:\s+para|com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[oe])?(?:\s+intervalo|range)?\s+([A-Z0-9:]+|\[.+?\])(?:\s+com\s+título\s+(?:"(.+?)"|'(.+?)'|(\S+.+\S+)))?/i,
      handler: (matches: RegExpMatchArray): ExcelOperation | null => {
        const sourceRange = matches[1]?.trim();
        const title = matches[2] || matches[3] || matches[4];

        if (!sourceRange) {
          return null;
        }

        return {
          type: ExcelOperationType.ADVANCED_VISUALIZATION,
          data: {
            type: 'network-graph',
            sourceRange,
            title: title || 'Grafo de Rede',
            interactive: true,
            animation: true,
          },
        };
      },
    },
  ];

  // Processa cada padrão contra o texto
  for (const pattern of patterns) {
    const matches = text.match(pattern.regex);
    if (matches) {
      const operation = pattern.handler(matches);
      if (operation) {
        operations.push(operation);
      }
    }
  }

  return operations;
}

/**
 * Executa operação de visualização avançada
 */
export async function executeAdvancedVisualizationOperation(
  sheetData: any,
  operation: ExcelOperation
): Promise<{ updatedData: any; resultSummary: string }> {
  try {
    if (operation.type !== 'ADVANCED_VISUALIZATION' || !operation.data) {
      throw new Error('Operação de visualização avançada inválida');
    }

    const config = operation.data as AdvancedVisualizationConfig;
    const sourceRange = config.sourceRange;

    // Extrai os dados do intervalo
    const data = await extractDataFromSource(sheetData, sourceRange);

    // Determina o destino da visualização
    const destinationRange =
      config.destinationRange || findSuitableVisualizationDestination(sheetData);

    // Gera um ID único para a visualização
    const vizId = config.id || generateVisualizationId();

    // Cria metadados da visualização para o cliente renderizar
    if (!sheetData._visualizations) {
      sheetData._visualizations = {};
    }

    sheetData._visualizations[vizId] = {
      type: config.type,
      title: config.title,
      data,
      config,
      position: destinationRange,
    };

    return {
      updatedData: sheetData,
      resultSummary: `Visualização avançada "${config.title || config.type}" criada com sucesso em ${destinationRange}`,
    };
  } catch (error) {
    console.error('Erro ao executar operação de visualização avançada:', error);
    return {
      updatedData: sheetData,
      resultSummary: `Erro ao criar visualização avançada: ${(error as Error).message}`,
    };
  }
}

/**
 * Extrai dados do intervalo de origem
 */
async function extractDataFromSource(sheetData: any, sourceRange: string): Promise<any[]> {
  try {
    // Caso simples: dados em formato de array de objetos
    if (Array.isArray(sheetData) && sheetData.length > 0) {
      return sheetData;
    }

    // Formato de células {A1: valor, B2: valor}
    if (typeof sheetData === 'object' && !Array.isArray(sheetData)) {
      const result: any[] = [];

      // Analisar o intervalo (ex: A1:C10)
      const rangeParts = sourceRange.split(':');
      const start = rangeParts[0];
      // Garantir que end tem valor
      const end = rangeParts.length > 1 ? rangeParts[1] : start;

      if (!start) {
        return [];
      }

      // Extrair as letras das colunas e números das linhas com segurança
      const startColMatch = start.match(/[A-Z]+/);
      const startRowMatch = start.match(/\d+/);
      // Se end existe, extrair os valores
      const endColMatch = end ? end.match(/[A-Z]+/) : null;
      const endRowMatch = end ? end.match(/\d+/) : null;

      const startCol = startColMatch ? startColMatch[0] : 'A';
      const startRow = startRowMatch ? parseInt(startRowMatch[0], 10) : 1;
      const endCol = endColMatch && endColMatch[0] ? endColMatch[0] : startCol;
      const endRow = endRowMatch && endRowMatch[0] ? parseInt(endRowMatch[0], 10) : startRow;

      if (startRow <= 0 || endRow <= 0) {
        return [];
      }

      // Converter letras de coluna para índices
      const startColIndex = columnLetterToIndex(startCol);
      const endColIndex = columnLetterToIndex(endCol);

      // Extrair valores de cabeçalho (primeira linha)
      const headers: string[] = [];
      for (let colIndex = startColIndex; colIndex <= endColIndex; colIndex++) {
        const colLetter = String.fromCharCode(65 + colIndex);
        const cellRef = `${colLetter}${startRow}`;
        // Usar valor seguro para o header (fallback para nome de coluna genérico)
        headers.push(sheetData[cellRef] ? String(sheetData[cellRef]) : `Column${colIndex + 1}`);
      }

      // Extrair dados das linhas
      for (let rowIndex = startRow + 1; rowIndex <= endRow; rowIndex++) {
        const rowData: Record<string, any> = {};

        for (let colIndex = startColIndex; colIndex <= endColIndex; colIndex++) {
          const colLetter = String.fromCharCode(65 + colIndex);
          const cellRef = `${colLetter}${rowIndex}`;
          // Usar acesso seguro para o header
          const headerIndex = colIndex - startColIndex;
          const header =
            headerIndex >= 0 && headerIndex < headers.length
              ? headers[headerIndex]
              : `Column${colIndex + 1}`;

          if (sheetData[cellRef] !== undefined && header) {
            // Verificar se header é válido como chave
            rowData[header as keyof typeof rowData] = sheetData[cellRef];
          }
        }

        result.push(rowData);
      }

      return result;
    }

    return [];
  } catch (error) {
    console.error('Erro ao extrair dados do intervalo:', error);
    return [];
  }
}

/**
 * Encontra um destino adequado para a visualização
 */
function findSuitableVisualizationDestination(sheetData: any): string {
  // Lógica para encontrar um espaço livre na planilha
  // Esta é uma implementação simplificada

  const visualizationsCount = Object.keys(sheetData._visualizations || {}).length;

  // Para cada nova visualização, colocamos em uma posição diferente
  // Normalmente seria algo mais sofisticado baseado em espaço livre
  const column = String.fromCharCode(65 + (visualizationsCount % 3) * 8);
  const row = Math.floor(visualizationsCount / 3) * 15 + 1;

  return `${column}${row}`;
}

/**
 * Gera um ID único para a visualização
 */
function generateVisualizationId(): string {
  return 'viz_' + Math.random().toString(36).substring(2, 9);
}
