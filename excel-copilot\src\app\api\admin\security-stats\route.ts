import { NextRequest, NextResponse } from 'next/server';
import { getToken } from 'next-auth/jwt';

import { getAuthAuditStats } from '@/lib/auth-audit-logger';
import { getOAuthRateLimitStats } from '@/lib/middleware/oauth-limiter';
import { getSecurityStats } from '@/lib/security-monitor';
import { prisma } from '@/server/db/client';

export const dynamic = 'force-dynamic';

/**
 * Endpoint para obter estatísticas de segurança
 * Apenas para administradores
 */
export async function GET(request: NextRequest) {
  try {
    // Verificar autenticação
    const token = await getToken({
      req: request,
      secret: process.env.AUTH_NEXTAUTH_SECRET || '',
    });

    if (!token) {
      return NextResponse.json({ error: 'Não autenticado' }, { status: 401 });
    }

    // TODO: Verificar se o usuário é administrador
    // Por enquanto, permitir apenas em desenvolvimento
    if (process.env.NODE_ENV === 'production') {
      return NextResponse.json(
        { error: 'Endpoint disponível apenas em desenvolvimento' },
        { status: 403 }
      );
    }

    // Obter estatísticas de diferentes sistemas
    const [
      securityStats,
      authAuditStats24h,
      authAuditStats1h,
      oauthRateLimitStats,
      suspiciousUsers,
      recentSecurityLogs,
    ] = await Promise.all([
      getSecurityStats(),
      getAuthAuditStats(undefined, 'day'),
      getAuthAuditStats(undefined, 'hour'),
      getOAuthRateLimitStats(),
      // Usuários marcados como suspeitos
      prisma.user.count({
        where: {
          isSuspicious: true,
        },
      }),
      // Logs de segurança recentes
      prisma.securityLog.findMany({
        where: {
          timestamp: {
            gte: new Date(Date.now() - 24 * 60 * 60 * 1000), // Últimas 24 horas
          },
        },
        orderBy: {
          timestamp: 'desc',
        },
        take: 50,
        include: {
          user: {
            select: {
              id: true,
              email: true,
              name: true,
            },
          },
        },
      }),
    ]);

    // Compilar estatísticas consolidadas
    const consolidatedStats = {
      timestamp: new Date().toISOString(),

      // Estatísticas gerais de segurança
      security: {
        totalMetrics: securityStats.totalMetrics,
        uniqueIPs: securityStats.uniqueIPs,
        suspiciousIPs: securityStats.suspiciousIPs,
        recentAnomalies: securityStats.recentAnomalies,
      },

      // Estatísticas de autenticação
      authentication: {
        last24Hours: {
          totalEvents: authAuditStats24h.totalEvents,
          successfulLogins: authAuditStats24h.successfulLogins,
          failedLogins: authAuditStats24h.failedLogins,
          suspiciousActivities: authAuditStats24h.suspiciousActivities,
          rateLimitExceeded: authAuditStats24h.rateLimitExceeded,
        },
        lastHour: {
          totalEvents: authAuditStats1h.totalEvents,
          successfulLogins: authAuditStats1h.successfulLogins,
          failedLogins: authAuditStats1h.failedLogins,
          suspiciousActivities: authAuditStats1h.suspiciousActivities,
          rateLimitExceeded: authAuditStats1h.rateLimitExceeded,
        },
      },

      // Estatísticas de rate limiting OAuth
      oauthRateLimit: {
        totalEntries: oauthRateLimitStats.totalEntries,
        blockedIPs: oauthRateLimitStats.blockedIPs,
        activeEntries: oauthRateLimitStats.activeEntries,
      },

      // Usuários suspeitos
      users: {
        suspiciousCount: suspiciousUsers,
      },

      // Logs recentes
      recentLogs: recentSecurityLogs.map(log => ({
        id: log.id,
        eventType: log.eventType,
        timestamp: log.timestamp,
        user: log.user
          ? {
              id: log.user.id,
              email: log.user.email,
              name: log.user.name,
            }
          : null,
        details: log.details ? JSON.parse(log.details) : null,
      })),

      // Alertas baseados nas estatísticas
      alerts: generateSecurityAlerts({
        authAuditStats24h,
        authAuditStats1h,
        oauthRateLimitStats,
        suspiciousUsers,
        securityStats,
      }),
    };

    return NextResponse.json(consolidatedStats, {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
      },
    });
  } catch (error) {
    console.error('Erro ao obter estatísticas de segurança:', error);

    return NextResponse.json(
      {
        error: 'Erro interno do servidor',
        message: error instanceof Error ? error.message : 'Erro desconhecido',
      },
      { status: 500 }
    );
  }
}

// Interfaces para tipagem das estatísticas
interface AuthAuditStats {
  failedLogins: number;
  suspiciousActivities: number;
}

interface OAuthRateLimitStats {
  blockedIPs: number;
}

interface SecurityStats {
  suspiciousIPs: number;
}

/**
 * Gera alertas baseados nas estatísticas de segurança
 */
function generateSecurityAlerts(stats: {
  authAuditStats24h: AuthAuditStats;
  authAuditStats1h: AuthAuditStats;
  oauthRateLimitStats: OAuthRateLimitStats;
  suspiciousUsers: number;
  securityStats: SecurityStats;
}): Array<{
  type: 'WARNING' | 'CRITICAL' | 'INFO';
  message: string;
  metric: string;
  value: number;
}> {
  const alerts = [];

  // Alerta para muitas falhas de login
  if (stats.authAuditStats1h.failedLogins > 20) {
    alerts.push({
      type: 'CRITICAL' as const,
      message: 'Muitas falhas de login na última hora',
      metric: 'failedLogins1h',
      value: stats.authAuditStats1h.failedLogins,
    });
  }

  // Alerta para atividades suspeitas
  if (stats.authAuditStats24h.suspiciousActivities > 5) {
    alerts.push({
      type: 'WARNING' as const,
      message: 'Atividades suspeitas detectadas nas últimas 24 horas',
      metric: 'suspiciousActivities24h',
      value: stats.authAuditStats24h.suspiciousActivities,
    });
  }

  // Alerta para IPs bloqueados
  if (stats.oauthRateLimitStats.blockedIPs > 10) {
    alerts.push({
      type: 'WARNING' as const,
      message: 'Muitos IPs bloqueados por rate limiting',
      metric: 'blockedIPs',
      value: stats.oauthRateLimitStats.blockedIPs,
    });
  }

  // Alerta para usuários suspeitos
  if (stats.suspiciousUsers > 0) {
    alerts.push({
      type: 'INFO' as const,
      message: 'Usuários marcados como suspeitos requerem revisão',
      metric: 'suspiciousUsers',
      value: stats.suspiciousUsers,
    });
  }

  // Alerta para IPs suspeitos
  if (stats.securityStats.suspiciousIPs > 5) {
    alerts.push({
      type: 'WARNING' as const,
      message: 'Muitos IPs suspeitos detectados',
      metric: 'suspiciousIPs',
      value: stats.securityStats.suspiciousIPs,
    });
  }

  return alerts;
}

/**
 * Endpoint para limpar dados de segurança (apenas desenvolvimento)
 */
export async function DELETE(request: NextRequest) {
  try {
    // Verificar autenticação
    const token = await getToken({
      req: request,
      secret: process.env.AUTH_NEXTAUTH_SECRET || '',
    });

    if (!token) {
      return NextResponse.json({ error: 'Não autenticado' }, { status: 401 });
    }

    // Apenas em desenvolvimento
    if (process.env.NODE_ENV !== 'development') {
      return NextResponse.json(
        { error: 'Operação disponível apenas em desenvolvimento' },
        { status: 403 }
      );
    }

    // Limpar logs de segurança antigos (mais de 7 dias)
    const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);

    const deletedLogs = await prisma.securityLog.deleteMany({
      where: {
        timestamp: {
          lt: sevenDaysAgo,
        },
      },
    });

    // Resetar flags de usuários suspeitos
    const resetUsers = await prisma.user.updateMany({
      where: {
        isSuspicious: true,
      },
      data: {
        isSuspicious: false,
      },
    });

    return NextResponse.json({
      message: 'Dados de segurança limpos com sucesso',
      deletedLogs: deletedLogs.count,
      resetUsers: resetUsers.count,
    });
  } catch (error) {
    console.error('Erro ao limpar dados de segurança:', error);

    return NextResponse.json(
      {
        error: 'Erro interno do servidor',
        message: error instanceof Error ? error.message : 'Erro desconhecido',
      },
      { status: 500 }
    );
  }
}
