import { useSession } from 'next-auth/react';
import { useState, useEffect, useCallback, useRef } from 'react';
import { io, Socket } from 'socket.io-client';
import { toast } from 'sonner';

import { logger } from '@/lib/logger';

// Interface para a sessão estendida do NextAuth
interface UserWithId {
  id: string;
  name?: string | null;
  email?: string | null;
  image?: string | null;
}

// Interface para as funções de retorno do hook
interface UseSocketReturn {
  socket: Socket | null;
  isConnected: boolean;
  connect: () => void;
  disconnect: () => void;
  emit: (event: string, data: unknown) => void;
}

/**
 * Hook para gerenciar conexões de WebSocket para colaboração em tempo real
 * @param workbookId ID da planilha para sincronização
 * @returns Objeto com socket, status e métodos de controle
 */
export function useSocket(workbookId: string): UseSocketReturn {
  const { data: session } = useSession();
  const [socket, setSocket] = useState<Socket | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const reconnectAttempts = useRef(0);
  const maxReconnectAttempts = 5;

  // Função para iniciar conexão
  const connect = useCallback(() => {
    if (socket) return; // Evitar conexões duplicadas

    if (!workbookId || !session?.user) {
      logger.warn('Tentativa de conexão socket sem workbookId ou usuário autenticado');
      return;
    }

    try {
      // Obter URL base do servidor
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      const host = window.location.host;
      const baseUrl = `${protocol}//${host}`;

      const user = session.user as UserWithId;

      // Criar instância do socket
      const socketInstance = io(baseUrl, {
        path: '/api/socket',
        autoConnect: false,
        reconnection: true,
        reconnectionAttempts: maxReconnectAttempts,
        reconnectionDelay: 1000,
        reconnectionDelayMax: 5000,
        timeout: 20000,
        auth: {
          workbookId,
          userId: user.id,
          userName: user.name || 'Usuário',
          userEmail: user.email,
        },
      });

      // Configurar handlers de evento
      socketInstance.on('connect', () => {
        setIsConnected(true);
        reconnectAttempts.current = 0;
        logger.info('Conexão socket estabelecida');
      });

      socketInstance.on('disconnect', reason => {
        setIsConnected(false);
        logger.info(`Desconectado: ${reason}`);

        if (reason === 'io server disconnect') {
          // Desconexão iniciada pelo servidor, é necessário reconectar manualmente
          setTimeout(() => {
            socketInstance.connect();
          }, 1000);
        }
      });

      socketInstance.on('connect_error', error => {
        logger.error('Erro de conexão socket:', error);
        reconnectAttempts.current += 1;

        if (reconnectAttempts.current >= maxReconnectAttempts) {
          toast.error('Não foi possível estabelecer conexão em tempo real', {
            description: 'Tente recarregar a página ou verifique sua conexão',
            duration: 5000,
          });
        }
      });

      // Eventos específicos da aplicação
      socketInstance.on('error', message => {
        toast.error('Erro na sincronização', {
          description: message,
          duration: 3000,
        });
      });

      // Iniciar conexão
      socketInstance.connect();
      setSocket(socketInstance);
    } catch (error) {
      logger.error('Erro ao inicializar socket:', error);
      toast.error('Não foi possível iniciar a sincronização em tempo real');
    }
  }, [workbookId, session, socket]);

  // Função para encerrar conexão
  const disconnect = useCallback(() => {
    if (!socket) return;

    socket.disconnect();
    setSocket(null);
    setIsConnected(false);
    logger.info('Conexão socket encerrada');
  }, [socket]);

  // Função para emitir eventos
  const emit = useCallback(
    (event: string, data: unknown) => {
      if (!socket || !isConnected) {
        logger.warn(`Tentativa de emissão de evento '${event}' sem conexão ativa`);
        return;
      }

      socket.emit(event, data);
    },
    [socket, isConnected]
  );

  // Limpar socket ao desmontar componente
  useEffect(() => {
    return () => {
      if (socket) {
        socket.disconnect();
      }
    };
  }, [socket]);

  return {
    socket,
    isConnected,
    connect,
    disconnect,
    emit,
  };
}
