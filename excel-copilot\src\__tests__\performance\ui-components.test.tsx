/**
 * Testes de performance para componentes UI otimizados
 * Valida melhorias de re-renderização e tempo de resposta
 */

import { render, fireEvent, screen } from '@testing-library/react';
import React, { useState } from 'react';

import { Button } from '@/components/ui/button';
import { OptimizedButton, ActionButton } from '@/components/ui/optimized-button';
import { performanceMonitor } from '@/utils/performance-monitor';

// Mock do performance monitor para testes
jest.mock('@/utils/performance-monitor', () => ({
  performanceMonitor: {
    recordRender: jest.fn(),
    getAllMetrics: jest.fn(() => []),
    getPerformanceComparison: jest.fn(() => ({
      optimized: [],
      nonOptimized: [],
      improvement: {
        averageRenderTimeReduction: 0,
        renderCountReduction: 0,
      },
    })),
    clear: jest.fn(),
  },
}));

// Componente de teste para simular re-renderizações
function TestContainer({ useOptimized = false }: { useOptimized?: boolean }) {
  const [count, setCount] = useState(0);
  const [items, setItems] = useState(Array.from({ length: 10 }, (_, i) => i));

  const ButtonComponent = useOptimized ? OptimizedButton : Button;
  const ActionButtonComponent = useOptimized ? ActionButton : Button;

  return (
    <div>
      <button onClick={() => setCount(c => c + 1)} data-testid="trigger-rerender">
        Re-render ({count})
      </button>

      <div data-testid="button-list">
        {items.map(item => (
          <ButtonComponent
            key={item}
            onClick={() => setItems(prev => prev.filter(i => i !== item))}
            data-testid={`button-${item}`}
          >
            Item {item}
          </ButtonComponent>
        ))}
      </div>

      <div data-testid="action-button-list">
        {items.map(item => (
          <ActionButtonComponent
            key={item}
            actionId={item}
            onAction={() => setItems(prev => prev.filter(i => i !== item))}
            data-testid={`action-button-${item}`}
          >
            Action {item}
          </ActionButtonComponent>
        ))}
      </div>
    </div>
  );
}

describe('UI Components Performance Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    performanceMonitor.clear();
  });

  describe('Button vs OptimizedButton', () => {
    it('should render both components without errors', () => {
      render(<TestContainer useOptimized={false} />);
      render(<TestContainer useOptimized={true} />);

      expect(screen.getAllByText(/Re-render/)).toHaveLength(2);
    });

    it('should handle click events correctly in both versions', () => {
      const { rerender } = render(<TestContainer useOptimized={false} />);

      // Testar botão normal
      fireEvent.click(screen.getByTestId('button-0'));
      expect(screen.queryByTestId('button-0')).not.toBeInTheDocument();

      // Testar versão otimizada
      rerender(<TestContainer useOptimized={true} />);
      fireEvent.click(screen.getByTestId('button-1'));
      expect(screen.queryByTestId('button-1')).not.toBeInTheDocument();
    });

    it('should maintain functionality after multiple re-renders', () => {
      render(<TestContainer useOptimized={true} />);

      // Forçar múltiplas re-renderizações
      const triggerButton = screen.getByTestId('trigger-rerender');
      for (let i = 0; i < 5; i++) {
        fireEvent.click(triggerButton);
      }

      // Verificar que os botões ainda funcionam
      fireEvent.click(screen.getByTestId('button-2'));
      expect(screen.queryByTestId('button-2')).not.toBeInTheDocument();
    });
  });

  describe('ActionButton Performance', () => {
    it('should handle action callbacks correctly', () => {
      render(<TestContainer useOptimized={true} />);

      // Testar ActionButton
      fireEvent.click(screen.getByTestId('action-button-3'));
      expect(screen.queryByTestId('action-button-3')).not.toBeInTheDocument();
    });

    it('should maintain stable references with actionId', () => {
      const { rerender } = render(<TestContainer useOptimized={true} />);

      const initialButton = screen.getByTestId('action-button-4');

      // Re-renderizar múltiplas vezes
      const triggerButton = screen.getByTestId('trigger-rerender');
      fireEvent.click(triggerButton);
      fireEvent.click(triggerButton);

      const afterRerendersButton = screen.getByTestId('action-button-4');

      // O botão deve ainda estar funcional
      fireEvent.click(afterRerendersButton);
      expect(screen.queryByTestId('action-button-4')).not.toBeInTheDocument();
    });
  });

  describe('Performance Monitoring Integration', () => {
    it('should record performance metrics for optimized components', () => {
      render(<TestContainer useOptimized={true} />);

      // Verificar se o performance monitor foi chamado
      // (Em um teste real, você verificaria as métricas específicas)
      expect(performanceMonitor.recordRender).toHaveBeenCalled();
    });

    it('should handle performance monitoring gracefully when disabled', () => {
      // Simular ambiente de produção usando Object.defineProperty
      const originalEnv = process.env.NODE_ENV;
      Object.defineProperty(process.env, 'NODE_ENV', {
        value: 'production',
        writable: true,
        configurable: true,
      });

      render(<TestContainer useOptimized={true} />);

      // Componentes devem funcionar normalmente mesmo com monitoring desabilitado
      fireEvent.click(screen.getByTestId('button-5'));
      expect(screen.queryByTestId('button-5')).not.toBeInTheDocument();

      Object.defineProperty(process.env, 'NODE_ENV', {
        value: originalEnv,
        writable: true,
        configurable: true,
      });
    });
  });

  describe('Memory Leak Prevention', () => {
    it('should not accumulate event listeners on re-renders', () => {
      const { rerender, unmount } = render(<TestContainer useOptimized={true} />);

      // Múltiplas re-renderizações
      for (let i = 0; i < 10; i++) {
        rerender(<TestContainer useOptimized={true} />);
      }

      // Cleanup deve funcionar corretamente
      expect(() => unmount()).not.toThrow();
    });

    it('should cleanup performance monitoring on unmount', () => {
      const { unmount } = render(<TestContainer useOptimized={true} />);

      unmount();

      // Verificar que não há vazamentos de memória
      // (Em um teste real, você verificaria se os listeners foram removidos)
      expect(true).toBe(true); // Placeholder para teste real
    });
  });

  describe('Accessibility Preservation', () => {
    it('should maintain accessibility attributes in optimized components', () => {
      render(
        <div>
          <OptimizedButton aria-label="Optimized button">Optimized</OptimizedButton>
          <ActionButton actionId="test" onAction={() => {}} aria-label="Action button">
            Action
          </ActionButton>
        </div>
      );

      expect(screen.getByLabelText('Optimized button')).toBeInTheDocument();
      expect(screen.getByLabelText('Action button')).toBeInTheDocument();
    });

    it('should preserve keyboard navigation', () => {
      render(<TestContainer useOptimized={true} />);

      const firstButton = screen.getByTestId('button-0');
      firstButton.focus();

      expect(document.activeElement).toBe(firstButton);
    });
  });

  describe('Edge Cases', () => {
    it('should handle rapid successive clicks', () => {
      render(<TestContainer useOptimized={true} />);

      const button = screen.getByTestId('button-6');

      // Cliques rápidos sucessivos
      fireEvent.click(button);
      fireEvent.click(button);
      fireEvent.click(button);

      // Deve ter sido removido apenas uma vez
      expect(screen.queryByTestId('button-6')).not.toBeInTheDocument();
    });

    it('should handle undefined/null props gracefully', () => {
      expect(() => {
        render(
          <div>
            <OptimizedButton onClick={undefined}>Undefined onClick</OptimizedButton>
            <ActionButton actionId="null-test" onAction={() => {}}>
              Null actionId
            </ActionButton>
          </div>
        );
      }).not.toThrow();
    });

    it('should handle dynamic prop changes', () => {
      function DynamicPropsTest() {
        const [variant, setVariant] = useState<'default' | 'destructive'>('default');

        return (
          <div>
            <OptimizedButton
              variant={variant}
              onClick={() => setVariant(v => (v === 'default' ? 'destructive' : 'default'))}
              data-testid="dynamic-button"
            >
              Dynamic Button
            </OptimizedButton>
          </div>
        );
      }

      render(<DynamicPropsTest />);

      const button = screen.getByTestId('dynamic-button');
      fireEvent.click(button);

      // Deve continuar funcionando após mudança de props
      expect(button).toBeInTheDocument();
    });
  });
});
