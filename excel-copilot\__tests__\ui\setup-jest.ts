/**
 * Configuração e extensões para Jest nos testes de UI
 * Este arquivo resolve problemas de tipagem e adiciona extensões do jest-dom
 */

import '@testing-library/jest-dom';

// Extensões de matcher para o Jest
declare global {
  namespace jest {
    interface Matchers<R> {
      toBeInTheDocument(): R;
      toHaveClass(className: string): R;
      toHaveStyle(css: Record<string, any>): R;
      toHaveAttribute(attr: string, value?: string): R;
      toHaveValue(value: any): R;
      toBeDisabled(): R;
      toBeEnabled(): R;
      toBeVisible(): R;
      toBeChecked(): R;
      toHaveFocus(): R;
    }
  }
}

// Setup do documento para jsdom
if (typeof document === 'undefined' && typeof window !== 'undefined') {
  // Caso estejamos em um ambiente onde window existe mas document não
  Object.defineProperty(window, 'document', {
    writable: true,
    value: {
      documentElement: {
        classList: {
          add: jest.fn(),
          remove: jest.fn(),
          contains: jest.fn().mockReturnValue(false),
        },
      },
      createElement: jest.fn(),
      getElementsByTagName: jest.fn().mockReturnValue([]),
      querySelector: jest.fn(),
      querySelectorAll: jest.fn().mockReturnValue([]),
    },
  });
}

// Mock para elementos do DOM necessários nos testes
if (typeof window !== 'undefined') {
  // Mock para funções de mídia que podem não estar disponíveis no ambiente de teste
  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: jest.fn().mockImplementation(query => ({
      matches: false,
      media: query,
      onchange: null,
      addListener: jest.fn(), // deprecated
      removeListener: jest.fn(), // deprecated
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
      dispatchEvent: jest.fn(),
    })),
  });

  // Mock para o IntersectionObserver que pode não estar disponível no ambiente de teste
  class MockIntersectionObserver {
    readonly root: Element | null = null;
    readonly rootMargin: string = '';
    readonly thresholds: ReadonlyArray<number> = [];

    constructor(
      private callback: IntersectionObserverCallback,
      private options?: IntersectionObserverInit
    ) {}

    observe = jest.fn();
    unobserve = jest.fn();
    disconnect = jest.fn();
    takeRecords = jest.fn();
  }

  Object.defineProperty(window, 'IntersectionObserver', {
    writable: true,
    configurable: true,
    value: MockIntersectionObserver,
  });

  // Mock para ResizeObserver
  class MockResizeObserver {
    constructor(private callback: ResizeObserverCallback) {}

    observe = jest.fn();
    unobserve = jest.fn();
    disconnect = jest.fn();
  }

  Object.defineProperty(window, 'ResizeObserver', {
    writable: true,
    configurable: true,
    value: MockResizeObserver,
  });

  // Mock para funções de animação
  Object.defineProperty(window, 'requestAnimationFrame', {
    writable: true,
    value: (callback: FrameRequestCallback) => {
      return setTimeout(() => callback(Date.now()), 0);
    },
  });

  Object.defineProperty(window, 'cancelAnimationFrame', {
    writable: true,
    value: (id: number) => {
      clearTimeout(id);
    },
  });

  // Mock para localStorage e sessionStorage quando não disponíveis
  // Somente definir se ainda não estiverem definidos
  const mockStorageIfNeeded = (propertyName: 'localStorage' | 'sessionStorage') => {
    try {
      // Tentar acessar para ver se já existe
      const existingStorage = (window as any)[propertyName];
      if (!existingStorage) {
        const storageMock = (() => {
          let store: Record<string, string> = {};
          return {
            getItem: (key: string) => store[key] || null,
            setItem: (key: string, value: string) => {
              store[key] = value.toString();
            },
            removeItem: (key: string) => {
              delete store[key];
            },
            clear: () => {
              store = {};
            },
          };
        })();

        Object.defineProperty(window, propertyName, {
          value: storageMock,
          configurable: true,
          writable: true,
        });
      }
    } catch (error) {
      // Se ocorrer um erro ao acessar, provavelmente precisa ser mockado
      console.warn(`Failed to mock ${propertyName}, it may already be defined elsewhere`);
    }
  };

  mockStorageIfNeeded('localStorage');
  mockStorageIfNeeded('sessionStorage');
}

// Configurações globais para todos os testes de UI
beforeAll(() => {
  // Configurar elemento do documento para testes com next-themes
  if (typeof document !== 'undefined' && document.documentElement) {
    document.documentElement.classList.add('light');
  }

  // Suprime logs de console nos testes para evitar poluição da saída
  jest.spyOn(console, 'error').mockImplementation(() => {});
  jest.spyOn(console, 'warn').mockImplementation(() => {});
});

afterAll(() => {
  // Limpar mocks
  jest.restoreAllMocks();
});

// Exportar uma instância vazia para poder importar este arquivo
export {};
