import { extractGroup } from '@/utils/regex-utils';

import { ExcelOperationType } from '../../types/index';
import { ExcelOperation } from '../excel/types';
import { columnLetterToIndex } from '../utils';

/**
 * Interface para configuração de tabela dinâmica
 */
export interface PivotTableConfig {
  // Configurações básicas
  sourceRange: string;
  destinationRange?: string;

  // Campos para análise
  rowFields: string[];
  columnFields: string[];
  dataFields: string[];
  filterFields?: string[];

  // Cálculos para cada campo de dados
  calculations?: {
    field: string;
    function:
      | 'sum'
      | 'count'
      | 'average'
      | 'max'
      | 'min'
      | 'product'
      | 'countNums'
      | 'stdDev'
      | 'stdDevp'
      | 'var'
      | 'varp';
    showAs?:
      | 'normal'
      | 'percentOfTotal'
      | 'percentOfRow'
      | 'percentOfColumn'
      | 'difference'
      | 'percentDifference'
      | 'runningTotal';
  }[];

  // Formatação
  showHeaders?: boolean;
  compactLayout?: boolean;
  showRowTotals?: boolean;
  showColumnTotals?: boolean;

  // Agrupamentos
  dateGrouping?: {
    field: string;
    by: 'years' | 'quarters' | 'months' | 'days';
  }[];

  numberGrouping?: {
    field: string;
    start: number;
    end: number;
    interval: number;
  }[];
}

/**
 * Extrai operações de tabela dinâmica a partir do texto de comando
 */
export function extractPivotTableOperations(text: string): ExcelOperation[] {
  const operations: ExcelOperation[] = [];

  // Padrões de comandos para tabelas dinâmicas
  const patterns = [
    // Padrão para criar tabela dinâmica básica
    {
      regex:
        /crie\s+uma\s+tabela\s+dinâmica\s+(?:com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[aoe])?\s+(?:intervalo|range|coluna[s]?|dados)?\s*(?:entre)?\s*([A-Z0-9:]+|\[.+?\])(?:\s+com)?(?:\s+([^,]+)(?:\s+nas)?(?:\s+linhas))?(?:\s+e)?(?:\s+([^,]+)(?:\s+nas)?(?:\s+colunas))?(?:\s+e)?(?:\s+(?:valores|medidas|dados|campos)\s+de\s+([^,]+))?/i,
      handler: (matches: RegExpMatchArray): ExcelOperation | null => {
        const sourceRange = matches[1]?.trim();
        const rowFields = matches[2]?.split(/\s*,\s*/) || [];
        const columnFields = matches[3]?.split(/\s*,\s*/) || [];
        const dataFields = matches[4]?.split(/\s*,\s*/) || [];

        if (sourceRange) {
          return {
            type: ExcelOperationType.PIVOT_TABLE,
            data: {
              sourceRange,
              rowFields: rowFields.filter(f => f),
              columnFields: columnFields.filter(f => f),
              dataFields: dataFields.filter(f => f),
              calculations: dataFields.map(field => ({
                field,
                function: 'sum',
              })),
            },
          };
        }
        return null;
      },
    },

    // Padrão para tabela dinâmica com cálculos específicos
    {
      regex:
        /crie\s+uma\s+tabela\s+dinâmica\s+(?:com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[aoe])?\s+(?:intervalo|range|coluna[s]?|dados)?\s*(?:entre)?\s*([A-Z0-9:]+|\[.+?\])(?:\s+com)?(?:\s+([^,]+)(?:\s+nas)?(?:\s+linhas))?(?:\s+e)?(?:\s+([^,]+)(?:\s+nas)?(?:\s+colunas))?(?:\s+calculando|mostrando|usando)(?:\s+a)?\s+(soma|média|contagem|máximo|mínimo)(?:\s+d[aoe])?\s+([^,]+)/i,
      handler: (matches: RegExpMatchArray): ExcelOperation | null => {
        const sourceRange = matches[1]?.trim();
        const rowFields = matches[2]?.split(/\s*,\s*/) || [];
        const columnFields = matches[3]?.split(/\s*,\s*/) || [];
        const calculationType = matches[4]?.toLowerCase();
        const dataFields = matches[5]?.split(/\s*,\s*/) || [];

        // Mapear tipos de cálculo em português para inglês
        const calculationMap: Record<string, string> = {
          soma: 'sum',
          média: 'average',
          contagem: 'count',
          máximo: 'max',
          mínimo: 'min',
        };

        if (sourceRange && calculationType) {
          return {
            type: ExcelOperationType.PIVOT_TABLE,
            data: {
              sourceRange,
              rowFields: rowFields.filter(f => f),
              columnFields: columnFields.filter(f => f),
              dataFields: dataFields.filter(f => f),
              calculations: dataFields.map(field => ({
                field,
                function: calculationMap[calculationType] || 'sum',
              })),
            },
          };
        }
        return null;
      },
    },

    // Padrão para tabela dinâmica com filtros
    {
      regex:
        /crie\s+uma\s+tabela\s+dinâmica\s+(?:com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[aoe])?\s+(?:intervalo|range|coluna[s]?|dados)?\s*(?:entre)?\s*([A-Z0-9:]+|\[.+?\])(?:\s+com)?(?:\s+([^,]+)(?:\s+nas)?(?:\s+linhas))?(?:\s+e)?(?:\s+([^,]+)(?:\s+nas)?(?:\s+colunas))?(?:\s+e)?(?:\s+(?:valores|medidas|dados|campos)\s+de\s+([^,]+))?(?:\s+filtrando\s+por\s+([^,]+))?/i,
      handler: (matches: RegExpMatchArray): ExcelOperation | null => {
        const sourceRange = matches[1]?.trim();
        const rowFields = matches[2]?.split(/\s*,\s*/) || [];
        const columnFields = matches[3]?.split(/\s*,\s*/) || [];
        const dataFields = matches[4]?.split(/\s*,\s*/) || [];
        const filterFields = matches[5]?.split(/\s*,\s*/) || [];

        if (sourceRange) {
          return {
            type: ExcelOperationType.PIVOT_TABLE,
            data: {
              sourceRange,
              rowFields: rowFields.filter(f => f),
              columnFields: columnFields.filter(f => f),
              dataFields: dataFields.filter(f => f),
              filterFields: filterFields.filter(f => f),
              calculations: dataFields.map(field => ({
                field,
                function: 'sum',
              })),
            },
          };
        }
        return null;
      },
    },

    // Padrão para tabela dinâmica com agrupamento de data
    {
      regex:
        /crie\s+uma\s+tabela\s+dinâmica\s+(?:com|usando)(?:\s+os)?(?:\s+dados)?(?:\s+d[aoe])?\s+(?:intervalo|range|coluna[s]?|dados)?\s*(?:entre)?\s*([A-Z0-9:]+|\[.+?\])(?:\s+com)?(?:\s+([^,]+)(?:\s+nas)?(?:\s+linhas))?(?:\s+e)?(?:\s+([^,]+)(?:\s+nas)?(?:\s+colunas))?(?:\s+agrupando\s+([^,]+)\s+por\s+(anos|trimestres|meses|dias|semanas))/i,
      handler: (matches: RegExpMatchArray): ExcelOperation | null => {
        const sourceRange = matches[1]?.trim();
        const rowFields = matches[2]?.split(/\s*,\s*/) || [];
        const columnFields = matches[3]?.split(/\s*,\s*/) || [];
        const dateField = matches[4]?.trim();
        const groupingType = matches[5]?.toLowerCase();

        // Mapear tipos de agrupamento em português para inglês
        const groupingMap: Record<string, string> = {
          anos: 'years',
          trimestres: 'quarters',
          meses: 'months',
          dias: 'days',
          semanas: 'weeks',
        };

        if (sourceRange && dateField && groupingType) {
          return {
            type: ExcelOperationType.PIVOT_TABLE,
            data: {
              sourceRange,
              rowFields: rowFields.filter(f => f),
              columnFields: columnFields.filter(f => f),
              dataFields: ['Contagem'], // Valor padrão se não especificado
              dateGrouping: [
                {
                  field: dateField,
                  by: groupingMap[groupingType] as any,
                },
              ],
            },
          };
        }
        return null;
      },
    },
  ];

  // Testar cada padrão
  for (const { regex, handler } of patterns) {
    const matches = text.match(regex);
    if (matches) {
      const operation = handler(matches);
      if (operation) {
        operations.push(operation);
      }
    }
  }

  return operations;
}

/**
 * Executa operação de tabela dinâmica
 */
export async function executePivotTableOperation(
  sheetData: any,
  operation: ExcelOperation
): Promise<{ updatedData: any; resultSummary: string }> {
  try {
    const {
      sourceRange,
      rowFields,
      columnFields,
      dataFields,
      filterFields,
      calculations,
      dateGrouping,
    } = operation.data;

    // Validar dados de entrada
    if (!sourceRange) {
      return {
        updatedData: sheetData,
        resultSummary: 'Erro: Intervalo de origem não especificado para a tabela dinâmica.',
      };
    }

    // Extrair dados da origem (pode ser range ou nome de tabela)
    let sourceData: any[] = [];
    try {
      sourceData = extractDataFromSource(sheetData, sourceRange);
    } catch (error) {
      return {
        updatedData: sheetData,
        resultSummary: `Erro ao extrair dados de origem: ${error instanceof Error ? error.message : String(error)}`,
      };
    }

    if (sourceData.length === 0) {
      return {
        updatedData: sheetData,
        resultSummary: 'Erro: Não foram encontrados dados no intervalo especificado.',
      };
    }

    // Criar tabela dinâmica
    const pivotResult = createPivotTable(
      sourceData,
      rowFields || [],
      columnFields || [],
      dataFields || [],
      filterFields || [],
      calculations,
      dateGrouping
    );

    // Destino para a tabela dinâmica (por padrão, nova planilha)
    const destinationSheet = 'Tabela Dinâmica';

    // Atualizar dados da planilha (neste exemplo, adicionamos uma nova sheet)
    const updatedData = {
      ...sheetData,
      pivotTables: {
        ...(sheetData.pivotTables || {}),
        [destinationSheet]: pivotResult,
      },
    };

    return {
      updatedData,
      resultSummary: `Tabela dinâmica criada com sucesso usando ${rowFields.length} campo(s) de linha, ${columnFields.length} campo(s) de coluna e ${dataFields.length} campo(s) de dados.`,
    };
  } catch (error) {
    return {
      updatedData: sheetData,
      resultSummary: `Erro ao criar tabela dinâmica: ${error instanceof Error ? error.message : String(error)}`,
    };
  }
}

/**
 * Extrai dados de uma área específica da planilha
 */
function extractDataFromSource(sheetData: any, sourceRange: string): any[] {
  // Analisar formato de intervalo (ex: "A1:D10")
  const rangeMatch = sourceRange.match(/([A-Z]+)(\d+):([A-Z]+)(\d+)/);
  if (rangeMatch) {
    // Usar extractGroup para acesso seguro aos grupos de captura
    const startCol = extractGroup(rangeMatch, 1, 'A');
    const startRow = extractGroup(rangeMatch, 2, '1');
    const endCol = extractGroup(rangeMatch, 3, 'A');
    const endRow = extractGroup(rangeMatch, 4, '1');

    // Converter letras de coluna e números de linha para índices
    const startColIndex = columnLetterToIndex(startCol);
    const endColIndex = columnLetterToIndex(endCol);
    // Garantir que os valores numéricos sejam válidos
    const startRowIndex = Math.max(0, parseInt(startRow, 10) - 1);
    const endRowIndex = Math.max(0, parseInt(endRow, 10) - 1);

    // Extrair cabeçalhos e dados
    const headers: string[] = [];
    for (let col = startColIndex; col <= endColIndex; col++) {
      // Usar acesso seguro aos objetos aninhados
      const cellValue = sheetData.rows?.[startRowIndex]?.cells?.[col]?.value;
      const header = cellValue !== undefined ? String(cellValue) : `Coluna${col + 1}`;
      headers.push(header);
    }

    // Extrair dados em formato de array de objetos
    const data = [];
    for (let row = startRowIndex + 1; row <= endRowIndex; row++) {
      const rowData: Record<string, any> = {};
      for (let col = startColIndex; col <= endColIndex; col++) {
        const headerIndex = col - startColIndex;
        // Garantir que o índice do header existe
        if (headerIndex >= 0 && headerIndex < headers.length) {
          const header = headers[headerIndex];
          // Valor seguro do header
          const cellValue = sheetData.rows?.[row]?.cells?.[col]?.value;
          if (header && cellValue !== undefined) {
            rowData[header] = cellValue;
          }
        }
      }
      data.push(rowData);
    }

    return data;
  }

  throw new Error(`Formato de intervalo '${sourceRange}' não reconhecido`);
}

/**
 * Cria uma tabela dinâmica a partir dos dados de origem
 */
function createPivotTable(
  sourceData: any[],
  rowFields: string[],
  columnFields: string[],
  dataFields: string[],
  filterFields: string[] = [],
  calculations: any[] = [],
  dateGrouping: any[] = []
): any {
  // Aplicar filtros (se especificados)
  let filteredData = sourceData;
  if (filterFields.length > 0 && filterFields[0]) {
    // Implementação simplificada de filtro
    // Em uma implementação real, precisaríamos processar critérios de filtro
  }

  // Aplicar agrupamento de datas (se especificado)
  if (dateGrouping && dateGrouping.length > 0) {
    filteredData = applyDateGrouping(filteredData, dateGrouping);
  }

  // Criar estrutura para armazenar valores agregados
  const pivotValues: Record<string, Record<string, Record<string, any[]>>> = {};

  // Determinar funções de cálculo para cada campo de dados
  const calculationFunctions: Record<string, (values: any[]) => number> = {};
  if (calculations && calculations.length > 0) {
    for (const calc of calculations) {
      const field = calc.field;
      switch (calc.function) {
        case 'sum':
          calculationFunctions[field] = values =>
            values.reduce((sum, v) => sum + (Number(v) || 0), 0);
          break;
        case 'average':
          calculationFunctions[field] = values => {
            const nums = values.filter(v => !isNaN(Number(v)));
            return nums.length > 0 ? nums.reduce((sum, v) => sum + Number(v), 0) / nums.length : 0;
          };
          break;
        case 'count':
          calculationFunctions[field] = values => values.length;
          break;
        case 'max':
          calculationFunctions[field] = values => {
            const nums = values.filter(v => !isNaN(Number(v)));
            return nums.length > 0 ? Math.max(...nums.map(v => Number(v))) : 0;
          };
          break;
        case 'min':
          calculationFunctions[field] = values => {
            const nums = values.filter(v => !isNaN(Number(v)));
            return nums.length > 0 ? Math.min(...nums.map(v => Number(v))) : 0;
          };
          break;
        default:
          calculationFunctions[field] = values =>
            values.reduce((sum, v) => sum + (Number(v) || 0), 0);
      }
    }
  } else {
    // Configuração padrão: soma para todos os campos
    dataFields.forEach(field => {
      calculationFunctions[field] = values => values.reduce((sum, v) => sum + (Number(v) || 0), 0);
    });
  }

  // Processar dados e criar tabela dinâmica
  for (const row of filteredData) {
    // Obter valores dos campos de linha e coluna
    const rowKey = rowFields.map(field => row[field] || 'Vazio').join('|');
    const colKey = columnFields.map(field => row[field] || 'Vazio').join('|');

    // Inicializar estrutura se necessário
    if (!pivotValues[rowKey]) {
      pivotValues[rowKey] = {};
    }
    if (!pivotValues[rowKey][colKey]) {
      pivotValues[rowKey][colKey] = {};
    }

    // Acumular valores para cada campo de dados
    for (const field of dataFields) {
      if (!pivotValues[rowKey][colKey][field]) {
        pivotValues[rowKey][colKey][field] = [] as any[];
      }
      pivotValues[rowKey][colKey][field].push(row[field]);
    }
  }

  // Calcular resultados finais
  const pivotResult: Record<string, Record<string, Record<string, number>>> = {};
  for (const rowKey in pivotValues) {
    pivotResult[rowKey] = {};
    for (const colKey in pivotValues[rowKey]) {
      pivotResult[rowKey][colKey] = {};
      for (const field of dataFields) {
        // Usar acesso seguro aos valores
        const pivotValuesForField = pivotValues[rowKey]?.[colKey]?.[field];
        const values: any[] = pivotValuesForField || [];

        // Verificar se a função de cálculo existe antes de invocá-la
        const calcFunction = calculationFunctions[field];
        if (calcFunction) {
          pivotResult[rowKey][colKey][field] = calcFunction(values);
        } else {
          // Fallback para soma se a função não estiver definida
          pivotResult[rowKey][colKey][field] = values.reduce((sum, v) => sum + (Number(v) || 0), 0);
        }
      }
    }
  }

  // Estrutura completa da tabela dinâmica
  return {
    config: {
      rowFields,
      columnFields,
      dataFields,
      filterFields,
      calculations,
    },
    data: pivotResult,
    rowKeys: Object.keys(pivotResult),
    columnKeys: Object.keys(Object.values(pivotResult)[0] || {}),
  };
}

/**
 * Aplica agrupamento de datas
 */
function applyDateGrouping(data: any[], dateGrouping: any[]): any[] {
  const result = [...data];

  // Processar cada configuração de agrupamento
  for (const grouping of dateGrouping) {
    const { field, by } = grouping;

    // Criar novas propriedades para as datas agrupadas
    const groupFieldName = `${field}_${by}`;

    for (const row of result) {
      // Verificar se o valor é uma data válida
      const dateValue = row[field];
      if (!dateValue) continue;

      let date: Date;
      try {
        date = new Date(dateValue);
        if (isNaN(date.getTime())) continue;
      } catch {
        continue;
      }

      // Aplicar o agrupamento apropriado
      let firstDay;
      let day;
      switch (by) {
        case 'years':
          row[groupFieldName] = date.getFullYear();
          break;
        case 'quarters':
          row[groupFieldName] = `Q${Math.floor(date.getMonth() / 3) + 1} ${date.getFullYear()}`;
          break;
        case 'months':
          row[groupFieldName] =
            `${date.toLocaleString('default', { month: 'long' })} ${date.getFullYear()}`;
          break;
        case 'weeks':
          // Obter o primeiro dia da semana (domingo)
          firstDay = new Date(date);
          day = date.getDay();
          firstDay.setDate(date.getDate() - day);
          row[groupFieldName] = `Semana de ${firstDay.toLocaleDateString()}`;
          break;
        case 'days':
          row[groupFieldName] = date.toLocaleDateString();
          break;
      }
    }
  }

  return result;
}
