# Testes de Integração com Supabase no Excel Copilot

Este documento descreve como verificar e testar a integração do Excel Copilot com o Supabase.

## Visão Geral da Integração

O Excel Copilot utiliza o Supabase de duas formas principais:

1. **PostgreSQL como banco de dados principal** (acessado via Prisma ORM)
2. **Supabase para Storage e funcionalidades adicionais** (acessado via cliente Supabase)

A implementação é estruturada da seguinte forma:

- O **Prisma** gerencia a conexão com o banco de dados PostgreSQL do Supabase
- O **cliente Supabase** é usado para recursos específicos como Storage e Auth quando necessário

## Testes Disponíveis

Desenvolvemos três conjuntos de testes para garantir que a integração com o Supabase funcione corretamente:

### 1. Teste de Variáveis de Ambiente (Integration Test)

Arquivo: `__tests__/integration/supabase-postgres.integration.test.ts`

Este teste verifica se todas as variáveis de ambiente necessárias para a conexão com o Supabase estão configuradas corretamente. Usa mocks para evitar conexões reais com o banco de dados.

**O que é testado:**

- Estrutura e formato das variáveis de ambiente
- Configuração correta das URLs do PostgreSQL e do Supabase
- Existência das chaves de autenticação

### 2. Teste de Conexão Real (Real Test)

Arquivo: `__tests__/integration/supabase-postgres.real.test.ts`

Este teste estabelece conexões reais com o banco de dados PostgreSQL e o Supabase, verificando se a comunicação está funcionando corretamente.

**O que é testado:**

- Conexão real com o banco de dados PostgreSQL via Prisma
- Autenticação com o serviço Supabase Auth
- Acesso a buckets de Storage do Supabase
- Consultas básicas ao banco de dados

### 3. Teste Abrangente do Cliente Supabase (Comprehensive Test)

Arquivo: `__tests__/integration/supabase-client.test.ts`

Este é o teste mais completo, que verifica todas as funcionalidades do cliente Supabase que são utilizadas pelo Excel Copilot.

**O que é testado:**

- Serviço de autenticação
- Configuração dos provedores OAuth
- Consultas ao banco de dados via cliente Supabase
- Funcionalidades de armazenamento (Storage)
- Consistência entre configurações do Prisma e Supabase
- Verificação de políticas RLS (Row Level Security)

## Como Executar os Testes

### Pré-requisitos

Antes de executar os testes, certifique-se de que:

1. O arquivo `.env.local` está configurado corretamente com as credenciais do Supabase
2. As seguintes variáveis de ambiente são necessárias:
   - `SUPABASE_URL`
   - `SUPABASE_ANON_KEY`
   - `DATABASE_URL`
   - `DIRECT_URL`

### Executar Testes Individuais

Para executar um teste específico:

```bash
# Teste básico de variáveis de ambiente
npm run test -- --testPathPattern=__tests__/integration/supabase-postgres.integration.test.ts

# Teste de conexões reais
npm run test -- --testPathPattern=__tests__/integration/supabase-postgres.real.test.ts

# Teste abrangente do cliente Supabase
npm run test:supabase
```

### Executar Todos os Testes do Supabase

Para executar todos os testes relacionados ao Supabase:

```bash
npm run test:supabase:all
```

## Resolução de Problemas

### Teste Falha na Verificação de Conexão

Se os testes falharem na verificação de conexão:

1. Verifique se as variáveis de ambiente estão corretas
2. Confirme que o IP de onde você está executando os testes está na lista de permissões do Supabase
3. Verifique se o projeto Supabase está ativo e não em modo de manutenção

### Erro "Authentication failed against database server"

Este erro indica problemas com as credenciais:

1. Verifique se a senha no `DATABASE_URL` está correta
2. Certifique-se de que caracteres especiais estão corretamente codificados com `encodeURIComponent()`

### Erro "Can't reach database server"

Este erro indica problemas de conectividade:

1. Verifique sua conexão com a internet
2. Confirme que o host do banco de dados está correto
3. Verifique as configurações de firewall

## Manutenção

Para manter os testes atualizados:

1. Sempre adicione testes para novas funcionalidades do Supabase que forem utilizadas
2. Atualize os testes existentes quando houver mudanças na estrutura do banco de dados
3. Execute os testes regularmente (especialmente após atualizações do Supabase)
