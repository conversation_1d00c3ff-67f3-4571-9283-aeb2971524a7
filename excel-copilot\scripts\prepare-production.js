#!/usr/bin/env node

/**
 * Script para preparar o ambiente de produção do Excel Copilot
 * Este script verifica variáveis de ambiente necessárias e ajuda a configurar o ambiente de produção
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');
const { execSync } = require('child_process');
const chalk = require('chalk');

// Caminho para o arquivo .env.production
const envPath = path.join(process.cwd(), '.env.production');
// Caminho para o arquivo de exemplo
const exampleEnvPath = path.join(process.cwd(), 'env.example');

// Função para criar o readline interface
const createInterface = () => {
  return readline.createInterface({
    input: process.stdin,
    output: process.stdout,
  });
};

// Função para ler input do usuário
const question = query =>
  new Promise(resolve => {
    const rl = createInterface();
    rl.question(query, answer => {
      rl.close();
      resolve(answer);
    });
  });

// Função para verificar se um arquivo existe
const fileExists = filePath => {
  try {
    return fs.existsSync(filePath);
  } catch (err) {
    return false;
  }
};

// Função para ler o conteúdo de um arquivo
const readFile = filePath => {
  try {
    return fs.readFileSync(filePath, 'utf8');
  } catch (err) {
    console.error(`Erro ao ler o arquivo ${filePath}:`, err);
    return null;
  }
};

// Função para escrever em um arquivo
const writeFile = (filePath, content) => {
  try {
    fs.writeFileSync(filePath, content, 'utf8');
    return true;
  } catch (err) {
    console.error(`Erro ao escrever no arquivo ${filePath}:`, err);
    return false;
  }
};

// Função para extrair variáveis de ambiente do arquivo de exemplo
const extractVariables = content => {
  const variables = [];
  const lines = content.split('\n');

  for (const line of lines) {
    if (line.trim() && !line.startsWith('#')) {
      const match = line.match(/^([A-Za-z0-9_]+)=/);
      if (match && match[1]) {
        variables.push(match[1]);
      }
    }
  }

  return variables;
};

// Função para gerar um segredo aleatório
const generateSecret = (length = 32) => {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  const charactersLength = characters.length;

  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
  }

  return result;
};

// Função principal
const main = async () => {
  console.log(chalk.blue.bold('==================================================='));
  console.log(chalk.blue.bold('🚀 Preparando Ambiente de Produção do Excel Copilot'));
  console.log(chalk.blue.bold('==================================================='));

  // Verificar se o arquivo .env.production já existe
  if (fileExists(envPath)) {
    const overwrite = await question(
      chalk.yellow('O arquivo .env.production já existe. Deseja sobrescrevê-lo? (s/N): ')
    );
    if (overwrite.toLowerCase() !== 's') {
      console.log(chalk.green('Cancelado. O arquivo .env.production existente será mantido.'));
      process.exit(0);
    }
  }

  // Ler o arquivo de exemplo
  const exampleContent = readFile(exampleEnvPath);
  if (!exampleContent) {
    console.error(
      chalk.red('❌ Não foi possível ler o arquivo env.example. Verifique se ele existe.')
    );
    process.exit(1);
  }

  // Extrair variáveis necessárias
  const variables = extractVariables(exampleContent);
  console.log(
    chalk.green(`✅ Encontradas ${variables.length} variáveis de ambiente no arquivo de exemplo.`)
  );

  // Criar conteúdo do novo arquivo .env.production
  let newContent = '';
  let line = '';

  const lines = exampleContent.split('\n');
  for (let i = 0; i < lines.length; i++) {
    line = lines[i];

    if (line.startsWith('NEXTAUTH_SECRET=')) {
      // Gerar um segredo aleatório para NEXTAUTH_SECRET
      const secret = generateSecret(48);
      line = `NEXTAUTH_SECRET="${secret}"`;
      console.log(chalk.green('✅ Gerado um novo segredo aleatório para NEXTAUTH_SECRET.'));
    } else if (line.startsWith('USE_MOCK_AI=')) {
      // Garantir que os mocks estejam desativados em produção
      line = 'USE_MOCK_AI="false"';
    } else if (line.startsWith('SKIP_AUTH_PROVIDERS=')) {
      // Garantir que os provedores de autenticação estejam habilitados em produção
      line = 'SKIP_AUTH_PROVIDERS="false"';
    } else if (line.startsWith('DATABASE_PROVIDER=')) {
      // Garantir que o provedor de banco de dados seja MySQL em produção
      line = 'DATABASE_PROVIDER="mysql"';
    }

    newContent += line + '\n';
  }

  // Escrever o novo arquivo .env.production
  if (writeFile(envPath, newContent)) {
    console.log(chalk.green(`✅ Arquivo .env.production criado com sucesso!`));
  } else {
    console.error(chalk.red('❌ Falha ao criar o arquivo .env.production.'));
    process.exit(1);
  }

  // Informar sobre variáveis que precisam ser preenchidas
  console.log(
    chalk.yellow(
      '\n⚠️ Importante: Você precisa preencher as seguintes variáveis no arquivo .env.production:'
    )
  );
  console.log(chalk.yellow('- DATABASE_URL (URL de conexão com o banco de dados MySQL)'));
  console.log(
    chalk.yellow('- VERTEX_AI_PROJECT_ID (ID do projeto no Google Cloud para Vertex AI)')
  );
  console.log(chalk.yellow('- VERTEX_AI_LOCATION (Localização do Vertex AI, ex: us-central1)'));
  console.log(
    chalk.yellow('- VERTEX_AI_MODEL_NAME (Nome do modelo Gemini a usar, ex: gemini-1.5-pro)')
  );
  console.log(
    chalk.yellow('- GOOGLE_CLIENT_ID e GOOGLE_CLIENT_SECRET (Credenciais OAuth do Google)')
  );
  console.log(
    chalk.yellow('- GITHUB_CLIENT_ID e GITHUB_CLIENT_SECRET (Credenciais OAuth do GitHub)')
  );
  console.log(chalk.yellow('- Credenciais do Stripe (STRIPE_SECRET_KEY, etc.)'));
  console.log(chalk.yellow('- Ou forneça o arquivo vertex-credentials.json na raiz do projeto'));

  // Perguntar se o usuário quer executar o comando de migração do banco de dados
  const runMigration = await question(
    chalk.blue('\nDeseja executar a migração do banco de dados agora? (s/N): ')
  );
  if (runMigration.toLowerCase() === 's') {
    try {
      console.log(chalk.blue('Executando migração do banco de dados...'));
      execSync('npx prisma migrate deploy', { stdio: 'inherit' });
      console.log(chalk.green('✅ Migração do banco de dados concluída com sucesso!'));
    } catch (error) {
      console.error(chalk.red('❌ Erro ao executar a migração do banco de dados.'));
      console.error(
        'Verifique se você configurou corretamente a variável DATABASE_URL no arquivo .env.production.'
      );
    }
  }

  // Perguntar se o usuário quer iniciar a aplicação em modo de produção
  const startApp = await question(
    chalk.blue('\nDeseja iniciar a aplicação em modo de produção? (s/N): ')
  );
  if (startApp.toLowerCase() === 's') {
    try {
      console.log(chalk.blue('Construindo a aplicação em modo de produção...'));
      execSync('npm run build', { stdio: 'inherit' });
      console.log(chalk.green('✅ Build concluído com sucesso!'));

      console.log(chalk.blue('Iniciando a aplicação em modo de produção...'));
      execSync('npm run start', { stdio: 'inherit' });
    } catch (error) {
      console.error(chalk.red('❌ Erro ao construir ou iniciar a aplicação.'));
    }
  }

  console.log(chalk.blue.bold('\n==================================================='));
  console.log(chalk.green.bold('✅ Configuração de produção concluída!'));
  console.log(chalk.blue.bold('==================================================='));
  console.log(chalk.blue('Para mais informações, consulte o arquivo MIGRACAO_PRODUCAO.md'));
  console.log(
    chalk.blue('Não esqueça de executar o checklist de segurança antes de lançar em produção!')
  );
};

// Executar a função principal
main().catch(err => {
  console.error(chalk.red('Erro inesperado:'), err);
  process.exit(1);
});
