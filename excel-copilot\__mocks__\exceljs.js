// Mock da biblioteca ExcelJS
const ExcelJS = {
  Workbook: class Workbook {
    constructor() {
      this.creator = 'Jest Test';
      this.lastModifiedBy = 'Jest Test';
      this.created = new Date();
      this.modified = new Date();
      this.lastPrinted = new Date();
      this.worksheets = [];
    }

    addWorksheet(name) {
      const worksheet = new ExcelJS.Worksheet(name);
      this.worksheets.push(worksheet);
      return worksheet;
    }

    getWorksheet(nameOrIndex) {
      if (typeof nameOrIndex === 'number') {
        return this.worksheets[nameOrIndex];
      }
      return this.worksheets.find(ws => ws.name === nameOrIndex);
    }

    xlsx = {
      writeBuffer: jest.fn().mockResolvedValue(Buffer.from('Mock Excel Binary Data')),
    };

    csv = {
      writeBuffer: jest.fn().mockResolvedValue(Buffer.from('Mock CSV Data')),
    };
  },

  Worksheet: class Worksheet {
    constructor(name) {
      this.name = name;
      this.columns = [];
      this.rows = [];
      this.getColumn = jest.fn().mockReturnValue({
        values: [],
        eachCell: jest.fn(),
      });
      this.getRow = jest.fn().mockReturnValue({
        values: [],
        eachCell: jest.fn(),
      });
      this.getCell = jest.fn().mockReturnValue({
        value: '',
        formula: '',
        style: {},
      });
      this.addRow = jest.fn(row => {
        this.rows.push(row);
        return row;
      });
    }
  },

  ValueType: {
    String: 'string',
    Number: 'number',
    Boolean: 'boolean',
    Date: 'date',
    Formula: 'formula',
  },

  CellValue: {
    // Tipos de células simulados
  },
};

module.exports = ExcelJS;
