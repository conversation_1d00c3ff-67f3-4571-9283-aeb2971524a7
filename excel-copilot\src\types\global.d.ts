/**
 * Global type definitions for Excel Copilot
 * This file contains all the type definitions that are missing from the codebase
 */

// Define ExcelOperationType enum directly to avoid circular dependencies
export enum ExcelOperationType {
  FORMULA = 'FORMULA',
  FILTER = 'FILTER',
  SORT = 'SORT',
  FORMAT = 'FORMAT',
  CHART = 'CHART',
  CELL_UPDATE = 'CELL_UPDATE',
  COLUMN_OPERATION = 'COLUMN_OPERATION',
  ROW_OPERATION = 'ROW_OPERATION',
  TABLE = 'TABLE',
  DATA_TRANSFORMATION = 'DATA_TRANSFORMATION',
  PIVOT_TABLE = 'PIVOT_TABLE',
  CONDITIONAL_FORMAT = 'CONDITIONAL_FORMAT',
  ADVANCED_CHART = 'ADVANCED_CHART',
  ADVANCED_VISUALIZATION = 'ADVANCED_VISUALIZATION',
  OPEN_FILE = 'OPEN_FILE',
  CHECK_EXCEL_INSTALLED = 'CHECK_EXCEL_INSTALLED',
  CHECK_EXCEL = 'CHECK_EXCEL',
  OPEN_EXCEL_FILE = 'OPEN_EXCEL_FILE',
  GET_ACTIVE_FILE = 'GET_ACTIVE_FILE',
  TABLE_OPERATION = 'TABLE_OPERATION',
  COLUMN = 'COLUMN',
  CELL = 'CELL',
  CONDITIONAL_FORMATTING = 'CONDITIONAL_FORMATTING',
  GENERIC = 'GENERIC',
  ANALYSIS = 'ANALYSIS',
  GET_WORKBOOK_INFO = 'GET_WORKBOOK_INFO',
  GET_WORKSHEET_DATA = 'GET_WORKSHEET_DATA',
}

// Google Analytics global declaration
declare global {
  interface Window {
    gtag: (
      command: 'event',
      eventName: string,
      eventParams?: {
        [key: string]: any;
      }
    ) => void;
  }
}

// Command Parser Types
export interface CommandParserOptions {
  strictMode?: boolean;
  allowUnknownParams?: boolean;
  translateOperation?: boolean;
}

export interface CommandParserResult<_T = any> {
  params: any;
  operations: ExcelOperation[];
  error?: string;
  message?: string;
  success: boolean;
}

// Main Excel Operation interface that was causing problems
export interface ExcelOperation {
  type: ExcelOperationType | string;
  params?: any; // For backward compatibility
  data?: any; // For new-style operations (client compatibility)
  id?: string; // Support client-side ID for operation tracking
  description?: string; // Description of the operation for logs and UI
}

// WebSocket message handler types
export interface WebSocketMessageHandler {
  handle(message: any): Promise<any>;
  canHandle(message: any): boolean;
}

// Rate limiter types
export interface RateLimitContext {
  ip: string;
  path: string;
  method: string;
  sessionId?: string;
  userId?: string;
}

// Health checker types
export interface HealthCheckOptions {
  timeout?: number;
  includeDetails?: boolean;
}

export interface HealthStatus {
  status: 'healthy' | 'unhealthy' | 'degraded';
  services: {
    [key: string]: {
      status: 'healthy' | 'unhealthy' | 'degraded';
      responseTime?: number;
      error?: string;
      details?: any;
    };
  };
  uptime: number;
  timestamp: number;
}

// Excel types
export interface ExcelClient {
  connect(): Promise<boolean>;
  disconnect(): Promise<boolean>;
  executeOperation(operation: ExcelOperation): Promise<any>;
  getActiveWorkbook(): Promise<any>;
  getWorksheets(): Promise<any>;
  getStatus(): Promise<any>;
}

// Context for AI operations
export interface AIContext {
  history?: any[];
  workbookData?: any;
  userPreferences?: any;
  sessionId?: string;
  timestamp?: number;
}

// Telemetry types
export interface TelemetryService {
  recordMetric(name: string, value: number, type?: string, tags?: Record<string, string>): void;
  recordError(error: Error | string, context?: Record<string, any>, severity?: string): void;
  recordHttpMetric(
    path: string,
    statusCode: number,
    duration: number,
    method?: string,
    hasError?: boolean
  ): void;
}

// Security types
export interface SecurityContext {
  userId?: string;
  sessionId?: string;
  roles?: string[];
  permissions?: string[];
  ip?: string;
  userAgent?: string;
}

// Logger configurations
export interface LoggerConfig {
  level?: string;
  format?: string;
  destination?: string;
  prefix?: string;
  timestamp?: boolean;
  colorize?: boolean;
}

// Service types
export interface Service {
  init(): Promise<void>;
  shutdown(): Promise<void>;
  isInitialized(): boolean;
  getName(): string;
}

// Validation types
export interface ValidationOptions {
  abortEarly?: boolean;
  convert?: boolean;
  allowUnknown?: boolean;
  stripUnknown?: boolean;
}

// Middleware types
export interface MiddlewareContext {
  req: any;
  res: any;
  nextMiddleware: () => Promise<void>;
}

export interface MiddlewareHandler {
  handle(context: MiddlewareContext): Promise<void>;
}

// Database service types
export interface DatabaseConfig {
  url: string;
  maxConnections?: number;
  ssl?: boolean;
  timeout?: number;
}

export interface QueryOptions {
  timeout?: number;
  traceparent?: string;
}

// Cache service types
export interface CacheOptions {
  ttl?: number;
  namespace?: string;
}

// HTTP client types
export interface HttpClientOptions {
  timeout?: number;
  headers?: Record<string, string>;
  retries?: number;
  baseUrl?: string;
}

// Job queue types
export interface Job {
  id: string;
  type: string;
  data: any;
  options?: JobOptions;
}

export interface JobOptions {
  priority?: number;
  delay?: number;
  attempts?: number;
  backoff?: {
    type: 'fixed' | 'exponential';
    delay: number;
  };
  timeout?: number;
}

// Event bus types
export interface Event {
  type: string;
  data: any;
  timestamp: number;
  source?: string;
}

export interface EventHandler {
  handle(event: Event): Promise<void>;
  canHandle(event: Event): boolean;
}

// Excel-specific types that appear in errors
export interface FormulaResult {
  value: any;
  formula: string;
  error?: string;
}

export interface CellFormatting {
  bold?: boolean;
  italic?: boolean;
  underline?: boolean;
  color?: string;
  backgroundColor?: string;
  numberFormat?: string;
}

export interface TableDefinition {
  name: string;
  range: string;
  hasHeaders: boolean;
  style?: string;
}

// Feature flag types
export interface FeatureFlag {
  name: string;
  enabled: boolean;
  rules?: FeatureFlagRule[];
}

export interface FeatureFlagRule {
  type: 'user' | 'percentage' | 'environment' | 'date';
  value: any;
}

// AI/ML types
export interface AIModelConfig {
  provider: string;
  model: string;
  apiKey?: string;
  temperature?: number;
  maxTokens?: number;
}

export interface AIPrompt {
  text: string;
  context?: any;
  options?: AIModelConfig;
}

export interface AIResponse {
  text: string;
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  metadata?: any;
}

// Web socket status and messages
export enum WebSocketStatus {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  ERROR = 'error',
}

export type WebSocketMessageType =
  | 'CONNECT'
  | 'DISCONNECT'
  | 'OPERATION'
  | 'RESPONSE'
  | 'ERROR'
  | 'EVENT'
  | 'STATUS';

export interface WebSocketMessage {
  id?: string;
  type?: string | WebSocketMessageType;
  event?: string;
  method?: string;
  params?: any;
  payload?: any;
  error?: string;
}

// Operation result interface
export interface OperationResult {
  success: boolean;
  message?: string;
  data?: any;
  error?: string;
}

// Definições para MockReadableStream usado nos testes
declare global {
  namespace NodeJS {
    interface Global {
      ReadableStream: typeof ReadableStream;
    }
  }

  interface MockReadableStreamController<T> {
    enqueue: (chunk: T) => void;
    close: () => void;
    error: (e?: any) => void;
  }

  interface MockReadable<T> {
    read: () => Promise<{ value: T; done: boolean }>;
    cancel: () => Promise<void>;
  }

  interface MockReadableStreamSource<T> {
    start?: (controller: MockReadableStreamController<T>) => void | Promise<void>;
    pull?: (controller: MockReadableStreamController<T>) => void | Promise<void>;
    cancel?: (reason?: any) => void | Promise<void>;
  }

  class MockReadableStream<T = Uint8Array> {
    constructor(source?: MockReadableStreamSource<T>);
    getReader(): MockReadable<T>;
    tee(): [MockReadableStream<T>, MockReadableStream<T>];
    pipeTo(destination: WritableStream): Promise<void>;
    pipeThrough<R>(transform: {
      writable: WritableStream;
      readable: ReadableStream<R>;
    }): ReadableStream<R>;
    locked: boolean;
    cancel(reason?: any): Promise<void>;
  }
}
