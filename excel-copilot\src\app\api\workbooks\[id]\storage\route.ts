import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';

import { authOptions } from '@/lib/auth';
import { storageService } from '@/lib/supabase/storage';
import { prisma } from '@/server/db/client';

/**
 * GET /api/workbooks/[id]/storage
 * Lista arquivos do workbook no Supabase Storage
 */
export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const session = (await getServerSession(authOptions as any)) as any;
    if (!session?.user) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    const workbookId = params.id;

    // Verificar se o usuário tem acesso ao workbook
    const workbook = await prisma.workbook.findFirst({
      where: {
        id: workbookId,
        OR: [
          { userId: session.user.id },
          {
            shares: {
              some: {
                sharedWithUserId: session.user.id,
              },
            },
          },
        ],
      },
    });

    if (!workbook) {
      return NextResponse.json({ error: 'Workbook não encontrado' }, { status: 404 });
    }

    // Listar arquivos do usuário para este workbook
    const files = await storageService.listUserFiles(
      session.user.id || session.user.email || 'unknown',
      'excel-files',
      `users/${session.user.id || session.user.email || 'unknown'}/workbooks/${workbookId}`
    );

    return NextResponse.json({
      success: true,
      files,
      workbookId,
    });
  } catch (error) {
    console.error('Erro ao listar arquivos:', error);
    return NextResponse.json({ error: 'Erro interno do servidor' }, { status: 500 });
  }
}

/**
 * POST /api/workbooks/[id]/storage
 * Upload de arquivo para o Supabase Storage
 */
export async function POST(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const session = (await getServerSession(authOptions as any)) as any;
    if (!session?.user) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    const workbookId = params.id;

    // Verificar se o usuário tem acesso ao workbook
    const workbook = await prisma.workbook.findFirst({
      where: {
        id: workbookId,
        OR: [
          { userId: session.user.id },
          {
            shares: {
              some: {
                sharedWithUserId: session.user.id,
                permissionLevel: {
                  in: ['EDIT', 'ADMIN'],
                },
              },
            },
          },
        ],
      },
    });

    if (!workbook) {
      return NextResponse.json(
        { error: 'Workbook não encontrado ou sem permissão' },
        { status: 404 }
      );
    }

    // Obter arquivo do FormData
    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json({ error: 'Nenhum arquivo enviado' }, { status: 400 });
    }

    // Validar tipo de arquivo
    const allowedTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-excel',
      'text/csv',
    ];

    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json({ error: 'Tipo de arquivo não suportado' }, { status: 400 });
    }

    // Validar tamanho (50MB máximo)
    const maxSize = 50 * 1024 * 1024; // 50MB
    if (file.size > maxSize) {
      return NextResponse.json({ error: 'Arquivo muito grande (máximo 50MB)' }, { status: 400 });
    }

    // Upload para Supabase Storage
    const uploadResult = await storageService.uploadExcelFile(
      file,
      session.user.id || session.user.email || 'unknown',
      workbookId,
      {
        fileName: file.name,
        upsert: true,
      }
    );

    // Atualizar workbook com informações do arquivo
    await prisma.workbook.update({
      where: { id: workbookId },
      data: {
        updatedAt: new Date(),
      },
    });

    return NextResponse.json({
      success: true,
      file: {
        path: uploadResult.path,
        size: uploadResult.size,
        name: file.name,
        type: file.type,
        uploadedAt: new Date().toISOString(),
      },
      workbookId,
    });
  } catch (error) {
    console.error('Erro no upload:', error);
    return NextResponse.json(
      {
        error: 'Erro no upload do arquivo',
        details: error instanceof Error ? error.message : 'Erro desconhecido',
      },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/workbooks/[id]/storage
 * Remove arquivo do Supabase Storage
 */
export async function DELETE(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const session = (await getServerSession(authOptions as any)) as any;
    if (!session?.user) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    const workbookId = params.id;
    const { searchParams } = new URL(request.url);
    const filePath = searchParams.get('path');

    if (!filePath) {
      return NextResponse.json({ error: 'Caminho do arquivo não fornecido' }, { status: 400 });
    }

    // Verificar se o usuário tem acesso ao workbook
    const workbook = await prisma.workbook.findFirst({
      where: {
        id: workbookId,
        userId: session.user.id, // Apenas o dono pode deletar arquivos
      },
    });

    if (!workbook) {
      return NextResponse.json(
        { error: 'Workbook não encontrado ou sem permissão' },
        { status: 404 }
      );
    }

    // Verificar se o arquivo pertence ao usuário
    const userId = session.user.id || session.user.email || 'unknown';
    if (!filePath.includes(`users/${userId}/workbooks/${workbookId}`)) {
      return NextResponse.json(
        { error: 'Sem permissão para deletar este arquivo' },
        { status: 403 }
      );
    }

    // Deletar arquivo do Supabase Storage
    await storageService.deleteFile(filePath, 'excel-files');

    return NextResponse.json({
      success: true,
      message: 'Arquivo deletado com sucesso',
      workbookId,
    });
  } catch (error) {
    console.error('Erro ao deletar arquivo:', error);
    return NextResponse.json(
      {
        error: 'Erro ao deletar arquivo',
        details: error instanceof Error ? error.message : 'Erro desconhecido',
      },
      { status: 500 }
    );
  }
}
