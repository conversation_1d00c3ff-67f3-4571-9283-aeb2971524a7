import { Session } from 'next-auth';
import { getServerSession } from 'next-auth/next';

// Interface extendida para representar o usuário com ID
interface UserWithId {
  id: string;
  name?: string | null;
  email?: string | null;
  image?: string | null;
}

// Interface para sessão tipada com usuário
interface TypedSession {
  user: UserWithId | null;
  expires: string;
}

// Type guard para verificar se um objeto é um usuário com ID
export function isUserWithId(user: unknown): user is UserWithId {
  return (
    typeof user === 'object' &&
    user !== null &&
    'id' in user &&
    typeof (user as UserWithId).id === 'string'
  );
}

// Função auxiliar que retorna a sessão com o usuário tipado corretamente
export async function getTypedSession(): Promise<TypedSession | null> {
  // Chamada do getServerSession sem passar argumentos
  // Ele vai usar a configuração padrão de authOptions do next-auth.config
  const session = await getServerSession();

  if (!session) return null;

  // Criar uma versão tipada da sessão
  const userWithId = session.user
    ? {
        ...session.user,
        id: isUserWithId(session.user) ? session.user.id : 'unknown',
      }
    : null;

  return {
    ...session,
    user: userWithId,
    expires:
      typeof session.expires === 'string'
        ? session.expires
        : new Date(session.expires).toISOString(),
  };
}

// Função auxiliar para extrair o ID do usuário de forma segura
export function getUserId(session: Session | null | undefined): string | undefined {
  if (!session?.user) return undefined;

  // Verificar se o usuário tem um campo ID
  const user = session.user as import('@/types/global-types').UserWithId;
  return user && typeof user.id === 'string' ? user.id : undefined;
}
