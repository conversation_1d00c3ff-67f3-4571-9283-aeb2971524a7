import { MetadataRoute } from 'next';

// Domínio base da aplicação
const DOMAIN = process.env.NEXT_PUBLIC_APP_URL || 'https://excel-copilot.vercel.app';

export default function robots(): MetadataRoute.Robots {
  return {
    rules: {
      userAgent: '*',
      allow: '/',
      disallow: [
        '/api/',
        '/admin/',
        '/user-profile/',
        '/_next/',
        '/private/',
        '/checkout/',
        '/auth/',
      ],
    },
    sitemap: `${DOMAIN}/sitemap.xml`,
    host: DOMAIN,
  };
}
