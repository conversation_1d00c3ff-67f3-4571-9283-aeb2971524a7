/**
 * Utilitários para trabalhar com RegExp
 * Este arquivo resolve problemas comuns ao trabalhar com matches de regex
 */

// Tipos básicos para expressões regulares
export type RegexPattern = string | RegExp;

/**
 * Representa o tipo de retorno de um match de regex bem-sucedido
 * com os grupos de captura acessíveis de forma segura
 */
export type RegExpMatchWithGroups = RegExpMatchArray & {
  /**
   * Acessa um grupo de captura com verificação de tipo
   * @param index O índice do grupo a ser acessado
   */
  safeGroup(index: number): string;

  /**
   * Verifica se o match tem um determinado grupo
   * @param index O índice do grupo a verificar
   */
  hasGroup(index: number): boolean;
};

/**
 * Acesso seguro a um elemento de array por índice
 * @param arr O array para acessar
 * @param index O índice a acessar
 * @returns O elemento no índice ou undefined se o índice for inválido
 */
export function safeArrayAccess<T>(arr: T[] | undefined | null, index: number): T | undefined {
  if (!arr || !Array.isArray(arr) || index < 0 || index >= arr.length) {
    return undefined;
  }
  return arr[index];
}

/**
 * Função para correspondência segura de regex que retorna null em caso de falha
 * @param text O texto a ser pesquisado
 * @param pattern O padrão de regex a ser usado
 * @returns Array de resultados de correspondência ou null se não houver correspondência
 */
export function safeRegexMatch(
  text: string | undefined | null,
  pattern: RegexPattern
): RegExpMatchArray | null {
  if (!text) return null;

  try {
    const regex = typeof pattern === 'string' ? new RegExp(pattern) : pattern;
    const match = text.match(regex);

    if (!match) return null;

    return {
      ...match,
      safeGroup: (index: number): string => {
        if (index < 0 || index >= match.length) {
          return '';
        }
        return match[index] || '';
      },
      hasGroup: (index: number): boolean => {
        return index >= 0 && index < match.length && match[index] !== undefined;
      },
    } as RegExpMatchWithGroups;
  } catch (error) {
    console.warn('Erro ao executar regex match:', error);
    return null;
  }
}

/**
 * Extrai um grupo capturado de uma correspondência de regex com segurança
 * @param match O resultado da correspondência de regex
 * @param groupIndex O índice do grupo a ser extraído
 * @param defaultValue Valor padrão a ser retornado se o grupo não existir
 * @returns O valor do grupo ou o valor padrão
 */
export function extractGroup(
  match: RegExpMatchArray | null,
  groupIndex: number,
  defaultValue: string = ''
): string {
  if (!match || !Array.isArray(match) || groupIndex < 0 || groupIndex >= match.length) {
    return defaultValue;
  }

  const groupValue = match[groupIndex];
  return groupValue !== undefined ? groupValue : defaultValue;
}

/**
 * Divide uma string usando um padrão regex de forma segura
 * @param text O texto a ser dividido
 * @param separator O padrão regex para divisão
 * @returns Array de substrings ou array vazio em caso de falha
 */
export function safeSplit(text: string | undefined | null, separator: RegexPattern): string[] {
  if (!text) return [];

  try {
    const regex = typeof separator === 'string' ? new RegExp(separator) : separator;
    return text.split(regex);
  } catch (error) {
    console.warn('Erro ao executar split com regex:', error);
    return [text];
  }
}

/**
 * Substitui ocorrências de um padrão em uma string de forma segura
 * @param text O texto original
 * @param pattern O padrão a ser substituído
 * @param replacement A string de substituição
 * @returns A string com substituições ou a string original em caso de falha
 */
export function safeReplace(
  text: string | undefined | null,
  pattern: RegexPattern,
  replacement: string
): string {
  if (!text) return '';

  try {
    const regex =
      typeof pattern === 'string'
        ? new RegExp(pattern, 'g')
        : pattern.flags.includes('g')
          ? pattern
          : new RegExp(pattern.source, pattern.flags + 'g');
    return text.replace(regex, replacement);
  } catch (error) {
    console.warn('Erro ao executar replace com regex:', error);
    return text;
  }
}

/**
 * Testa de forma segura se um padrão corresponde a uma string
 * @param text O texto a ser testado
 * @param pattern O padrão regex para testar
 * @returns true se houver correspondência, false caso contrário
 */
export function safeTest(text: string | undefined | null, pattern: RegexPattern): boolean {
  if (!text) return false;

  try {
    const regex = typeof pattern === 'string' ? new RegExp(pattern) : pattern;
    return regex.test(text);
  } catch (error) {
    console.warn('Erro ao testar regex:', error);
    return false;
  }
}

/**
 * Verifica se um padrão regex corresponde a uma string
 * @param pattern O padrão regex a verificar
 * @param text O texto a verificar
 * @returns True se o padrão corresponder, false caso contrário
 */
export function matchesPattern(pattern: RegExp, text: string): boolean {
  if (!text) return false;
  return pattern.test(text);
}

/**
 * Extrai o primeiro grupo de captura de uma string
 * @param pattern O padrão regex a usar
 * @param text O texto a verificar
 * @param defaultValue Valor padrão a retornar se não houver correspondência
 * @returns O primeiro grupo de captura ou o valor padrão
 */
export function extractFirstGroup(
  pattern: RegExp,
  text: string,
  defaultValue: string = ''
): string {
  if (!text) return defaultValue;

  const match = text.match(pattern);
  return extractGroup(match, 1, defaultValue);
}
