import { test, expect } from '@playwright/test';

/**
 * Testes de Performance - Garantir tempos de carregamento < 3 segundos
 */
test.describe('Testes de Performance', () => {
  test('dashboard deve carregar em menos de 3 segundos', async ({ page }) => {
    console.log('⚡ Testando performance do dashboard...');
    
    const startTime = Date.now();
    
    // Navegar para o dashboard
    await page.goto('/dashboard');
    
    // Aguardar carregamento completo
    await page.waitForLoadState('networkidle');
    
    // Aguardar elementos críticos
    await page.waitForSelector('[data-testid="dashboard-metrics"]');
    await page.waitForSelector('[data-testid="workbook-list"]');
    
    const loadTime = Date.now() - startTime;
    console.log(`📊 Dashboard carregou em ${loadTime}ms`);
    
    // Verificar se carregou em menos de 3 segundos
    expect(loadTime).toBeLessThan(3000);
    
    // Verificar Core Web Vitals
    const vitals = await page.evaluate(() => {
      return new Promise((resolve) => {
        new PerformanceObserver((list) => {
          const entries = list.getEntries();
          const vitals: any = {};
          
          entries.forEach((entry: any) => {
            if (entry.name === 'first-contentful-paint') {
              vitals.fcp = entry.startTime;
            }
            if (entry.name === 'largest-contentful-paint') {
              vitals.lcp = entry.startTime;
            }
          });
          
          resolve(vitals);
        }).observe({ entryTypes: ['paint', 'largest-contentful-paint'] });
        
        // Timeout após 5 segundos
        setTimeout(() => resolve({}), 5000);
      });
    });
    
    console.log('📈 Core Web Vitals:', vitals);
    
    // FCP deve ser < 1.8s, LCP deve ser < 2.5s
    if (vitals.fcp) {
      expect(vitals.fcp).toBeLessThan(1800);
    }
    if (vitals.lcp) {
      expect(vitals.lcp).toBeLessThan(2500);
    }
  });

  test('criação de planilha deve ser responsiva', async ({ page }) => {
    console.log('⚡ Testando performance de criação de planilha...');
    
    await page.goto('/dashboard');
    await page.waitForLoadState('networkidle');
    
    const startTime = Date.now();
    
    // Clicar em criar planilha
    await page.locator('[data-testid="create-workbook-button"]').click();
    
    // Aguardar modal aparecer
    await page.waitForSelector('[data-testid="create-workbook-form"]');
    
    const modalTime = Date.now() - startTime;
    console.log(`📝 Modal de criação apareceu em ${modalTime}ms`);
    
    // Modal deve aparecer em menos de 500ms
    expect(modalTime).toBeLessThan(500);
    
    // Preencher formulário rapidamente
    const formStartTime = Date.now();
    
    await page.fill('[data-testid="workbook-name"]', 'Performance Test');
    await page.fill('[data-testid="ai-command"]', 'Criar planilha simples');
    
    const formTime = Date.now() - formStartTime;
    console.log(`⌨️ Formulário preenchido em ${formTime}ms`);
    
    // Submeter
    const submitStartTime = Date.now();
    await page.locator('[data-testid="create-workbook-submit"]').click();
    
    // Aguardar redirecionamento
    await page.waitForURL(/\/workbook\/[a-zA-Z0-9]+/);
    
    const submitTime = Date.now() - submitStartTime;
    console.log(`🚀 Redirecionamento em ${submitTime}ms`);
    
    // Redirecionamento deve ser < 2 segundos
    expect(submitTime).toBeLessThan(2000);
  });

  test('editor de planilha deve carregar rapidamente', async ({ page }) => {
    console.log('⚡ Testando performance do editor...');
    
    // Primeiro criar uma planilha
    await page.goto('/dashboard');
    await page.locator('[data-testid="create-workbook-button"]').click();
    await page.fill('[data-testid="workbook-name"]', 'Editor Performance Test');
    await page.locator('[data-testid="create-workbook-submit"]').click();
    
    // Aguardar redirecionamento
    await page.waitForURL(/\/workbook\/[a-zA-Z0-9]+/);
    
    const editorStartTime = Date.now();
    
    // Aguardar editor carregar
    await page.waitForSelector('[data-testid="spreadsheet-editor"]');
    await page.waitForSelector('[data-testid="spreadsheet-grid"]');
    
    const editorTime = Date.now() - editorStartTime;
    console.log(`📊 Editor carregou em ${editorTime}ms`);
    
    // Editor deve carregar em menos de 2 segundos
    expect(editorTime).toBeLessThan(2000);
    
    // Testar responsividade de edição
    const editStartTime = Date.now();
    
    await page.locator('[data-testid="cell-A1"]').click();
    await page.locator('[data-testid="cell-A1"]').fill('Test');
    await page.keyboard.press('Enter');
    
    const editTime = Date.now() - editStartTime;
    console.log(`✏️ Edição de célula em ${editTime}ms`);
    
    // Edição deve ser instantânea (< 100ms)
    expect(editTime).toBeLessThan(100);
  });

  test('busca de planilhas deve ser rápida', async ({ page }) => {
    console.log('⚡ Testando performance de busca...');
    
    await page.goto('/dashboard');
    await page.waitForLoadState('networkidle');
    
    const searchInput = page.locator('[data-testid="search-workbooks"]');
    if (await searchInput.isVisible()) {
      const searchStartTime = Date.now();
      
      // Digitar termo de busca
      await searchInput.fill('test');
      
      // Aguardar resultados
      await page.waitForTimeout(300); // Debounce típico
      await page.waitForSelector('[data-testid="search-results"]');
      
      const searchTime = Date.now() - searchStartTime;
      console.log(`🔍 Busca completada em ${searchTime}ms`);
      
      // Busca deve ser < 1 segundo
      expect(searchTime).toBeLessThan(1000);
    }
  });

  test('navegação entre páginas deve ser fluida', async ({ page }) => {
    console.log('⚡ Testando performance de navegação...');
    
    await page.goto('/dashboard');
    await page.waitForLoadState('networkidle');
    
    // Testar navegação para diferentes seções
    const navigationTests = [
      { selector: '[data-testid="nav-templates"]', expectedUrl: '/templates' },
      { selector: '[data-testid="nav-settings"]', expectedUrl: '/settings' },
      { selector: '[data-testid="nav-dashboard"]', expectedUrl: '/dashboard' },
    ];
    
    for (const nav of navigationTests) {
      const navElement = page.locator(nav.selector);
      
      if (await navElement.isVisible()) {
        const navStartTime = Date.now();
        
        await navElement.click();
        await page.waitForURL(nav.expectedUrl);
        await page.waitForLoadState('networkidle');
        
        const navTime = Date.now() - navStartTime;
        console.log(`🧭 Navegação para ${nav.expectedUrl} em ${navTime}ms`);
        
        // Navegação deve ser < 1.5 segundos
        expect(navTime).toBeLessThan(1500);
      }
    }
  });

  test('deve manter performance com muitos dados', async ({ page }) => {
    console.log('⚡ Testando performance com dados grandes...');
    
    // Criar planilha com comando que gera muitos dados
    await page.goto('/dashboard');
    await page.locator('[data-testid="create-workbook-button"]').click();
    
    await page.fill('[data-testid="workbook-name"]', 'Large Data Test');
    await page.fill('[data-testid="ai-command"]', 'Criar planilha com 100 linhas de dados financeiros');
    
    const largeDataStartTime = Date.now();
    
    await page.locator('[data-testid="create-workbook-submit"]').click();
    await page.waitForURL(/\/workbook\/[a-zA-Z0-9]+/);
    
    // Aguardar processamento de IA (pode demorar mais)
    await page.waitForSelector('[data-testid="spreadsheet-editor"]', { timeout: 30000 });
    
    // Aguardar dados carregarem
    await page.waitForFunction(() => {
      const cells = document.querySelectorAll('[data-testid^="cell-"]');
      return cells.length > 50; // Aguardar pelo menos 50 células
    }, { timeout: 60000 });
    
    const largeDataTime = Date.now() - largeDataStartTime;
    console.log(`📊 Planilha grande carregada em ${largeDataTime}ms`);
    
    // Com dados grandes, permitir até 10 segundos
    expect(largeDataTime).toBeLessThan(10000);
    
    // Testar scroll performance
    const scrollStartTime = Date.now();
    
    await page.mouse.wheel(0, 1000); // Scroll down
    await page.waitForTimeout(100);
    
    const scrollTime = Date.now() - scrollStartTime;
    console.log(`📜 Scroll performance: ${scrollTime}ms`);
    
    // Scroll deve ser fluido (< 50ms)
    expect(scrollTime).toBeLessThan(50);
  });

  test('deve monitorar uso de memória', async ({ page }) => {
    console.log('⚡ Monitorando uso de memória...');
    
    await page.goto('/dashboard');
    await page.waitForLoadState('networkidle');
    
    // Obter métricas de memória inicial
    const initialMemory = await page.evaluate(() => {
      return (performance as any).memory ? {
        usedJSHeapSize: (performance as any).memory.usedJSHeapSize,
        totalJSHeapSize: (performance as any).memory.totalJSHeapSize,
        jsHeapSizeLimit: (performance as any).memory.jsHeapSizeLimit,
      } : null;
    });
    
    if (initialMemory) {
      console.log('💾 Memória inicial:', {
        used: Math.round(initialMemory.usedJSHeapSize / 1024 / 1024) + 'MB',
        total: Math.round(initialMemory.totalJSHeapSize / 1024 / 1024) + 'MB',
      });
      
      // Criar várias planilhas para testar vazamentos
      for (let i = 0; i < 3; i++) {
        await page.locator('[data-testid="create-workbook-button"]').click();
        await page.fill('[data-testid="workbook-name"]', `Memory Test ${i}`);
        await page.locator('[data-testid="create-workbook-submit"]').click();
        await page.waitForURL(/\/workbook\/[a-zA-Z0-9]+/);
        await page.goBack();
        await page.waitForURL('/dashboard');
      }
      
      // Verificar memória final
      const finalMemory = await page.evaluate(() => {
        return (performance as any).memory ? {
          usedJSHeapSize: (performance as any).memory.usedJSHeapSize,
          totalJSHeapSize: (performance as any).memory.totalJSHeapSize,
        } : null;
      });
      
      if (finalMemory) {
        const memoryIncrease = finalMemory.usedJSHeapSize - initialMemory.usedJSHeapSize;
        const increaseInMB = Math.round(memoryIncrease / 1024 / 1024);
        
        console.log('💾 Aumento de memória:', increaseInMB + 'MB');
        
        // Não deve aumentar mais que 50MB
        expect(increaseInMB).toBeLessThan(50);
      }
    }
  });
});
