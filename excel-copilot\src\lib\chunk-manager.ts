/**
 * Gerenciador de chunks para manipulação eficiente de grandes planilhas
 */
export class ChunkManager<T = unknown> {
  private chunks: T[][];
  private chunkSize: number;
  private totalItems: number;
  private loadedChunks: Set<number>;
  private pendingChunks: Map<number, Promise<void>>;

  /**
   * Construtor do ChunkManager
   * @param initialData Dados iniciais (primeiro chunk)
   * @param totalItems Número total de itens (se conhecido)
   * @param chunkSize Tamanho de cada chunk (padrão: 1000 linhas)
   */
  constructor(initialData: T[] = [], totalItems = 0, chunkSize = 1000) {
    this.chunkSize = chunkSize;
    this.totalItems = totalItems || initialData.length;
    this.chunks = [];
    this.loadedChunks = new Set();
    this.pendingChunks = new Map();

    // Armazenar dados iniciais como primeiro chunk
    if (initialData.length > 0) {
      this.chunks[0] = initialData.slice(0, chunkSize);
      this.loadedChunks.add(0);
    }
  }

  /**
   * Retorna o número total de itens
   */
  getTotalItems(): number {
    return this.totalItems;
  }

  /**
   * Define o número total de itens (útil quando é descoberto após a inicialização)
   */
  setTotalItems(count: number): void {
    this.totalItems = count;
  }

  /**
   * Calcula o número total de chunks
   */
  getTotalChunks(): number {
    return Math.ceil(this.totalItems / this.chunkSize);
  }

  /**
   * Verifica se um chunk específico está carregado
   */
  isChunkLoaded(chunkIndex: number): boolean {
    return this.loadedChunks.has(chunkIndex);
  }

  /**
   * Obtém o índice do chunk para um determinado item
   */
  getChunkIndexForItem(itemIndex: number): number {
    return Math.floor(itemIndex / this.chunkSize);
  }

  /**
   * Obtém um item específico, carregando o chunk se necessário
   * @param index Índice do item
   * @param loadChunkFn Função para carregar o chunk se não estiver disponível
   */
  async getItem(
    index: number,
    loadChunkFn?: (chunkIndex: number) => Promise<T[]>
  ): Promise<T | undefined> {
    if (index < 0 || index >= this.totalItems) {
      return undefined;
    }

    const chunkIndex = this.getChunkIndexForItem(index);
    const indexInChunk = index % this.chunkSize;

    // Carregar o chunk se necessário
    await this.ensureChunkLoaded(chunkIndex, loadChunkFn);

    // Retornar o item
    return this.chunks[chunkIndex]?.[indexInChunk];
  }

  /**
   * Obtém vários itens de um intervalo, carregando chunks se necessário
   * @param startIndex Índice inicial
   * @param endIndex Índice final (inclusive)
   * @param loadChunkFn Função para carregar chunks não disponíveis
   */
  async getRange(
    startIndex: number,
    endIndex: number,
    loadChunkFn?: (chunkIndex: number) => Promise<T[]>
  ): Promise<T[]> {
    // Validar índices
    startIndex = Math.max(0, startIndex);
    endIndex = Math.min(this.totalItems - 1, endIndex);

    if (startIndex > endIndex) {
      return [];
    }

    // Determinar chunks necessários
    const startChunk = this.getChunkIndexForItem(startIndex);
    const endChunk = this.getChunkIndexForItem(endIndex);

    // Carregar todos os chunks necessários em paralelo
    const loadPromises: Promise<void>[] = [];
    for (let i = startChunk; i <= endChunk; i++) {
      loadPromises.push(this.ensureChunkLoaded(i, loadChunkFn));
    }
    await Promise.all(loadPromises);

    // Extrair e retornar os itens do intervalo
    const result: T[] = [];
    for (let i = startIndex; i <= endIndex; i++) {
      const chunkIndex = this.getChunkIndexForItem(i);
      const indexInChunk = i % this.chunkSize;

      if (this.chunks[chunkIndex] && indexInChunk < this.chunks[chunkIndex].length) {
        const item = this.chunks[chunkIndex][indexInChunk];
        if (item !== undefined) {
          result.push(item);
        }
      }
    }

    return result;
  }

  /**
   * Atualiza um item específico em um chunk
   */
  setItem(index: number, value: T): void {
    if (index < 0 || index >= this.totalItems) {
      return;
    }

    const chunkIndex = this.getChunkIndexForItem(index);
    const indexInChunk = index % this.chunkSize;

    // Verificar se o chunk está carregado
    if (!this.isChunkLoaded(chunkIndex)) {
      throw new Error(`Tentativa de atualizar item em chunk não carregado: ${chunkIndex}`);
    }

    // Garantir que o chunk existe
    if (!this.chunks[chunkIndex]) {
      this.chunks[chunkIndex] = [];
    }

    // Atualizar o item
    this.chunks[chunkIndex][indexInChunk] = value;
  }

  /**
   * Adiciona um chunk completo de dados
   */
  addChunk(chunkIndex: number, data: T[]): void {
    this.chunks[chunkIndex] = data;
    this.loadedChunks.add(chunkIndex);
  }

  /**
   * Garante que um chunk específico esteja carregado
   */
  private async ensureChunkLoaded(
    chunkIndex: number,
    loadChunkFn?: (chunkIndex: number) => Promise<T[]>
  ): Promise<void> {
    // Se o chunk já está carregado, retorna imediatamente
    if (this.isChunkLoaded(chunkIndex)) {
      return;
    }

    // Se o chunk já está sendo carregado, aguarda a conclusão
    if (this.pendingChunks.has(chunkIndex)) {
      return this.pendingChunks.get(chunkIndex);
    }

    // Se não temos função para carregar, lança erro
    if (!loadChunkFn) {
      throw new Error(
        `Chunk ${chunkIndex} não está carregado e nenhuma função de carregamento foi fornecida`
      );
    }

    // Inicia o carregamento e armazena a promessa
    const loadPromise = (async () => {
      try {
        const data = await loadChunkFn(chunkIndex);
        this.addChunk(chunkIndex, data);
      } finally {
        // Remover da lista de pendentes após conclusão (bem ou mal sucedida)
        this.pendingChunks.delete(chunkIndex);
      }
    })();

    this.pendingChunks.set(chunkIndex, loadPromise);
    return loadPromise;
  }

  /**
   * Limpa chunks não utilizados recentemente para economizar memória
   * @param keepChunkIndices Índices de chunks a manter
   * @param bufferSize Quantos chunks extras manter além dos especificados
   */
  clearUnusedChunks(keepChunkIndices: number[], bufferSize = 2): void {
    // Criar um conjunto de chunks a manter
    const keepSet = new Set(keepChunkIndices);

    // Adicionar buffer (chunks adjacentes)
    for (const idx of keepChunkIndices) {
      for (let i = 1; i <= bufferSize; i++) {
        if (idx - i >= 0) keepSet.add(idx - i);
        if (idx + i < this.getTotalChunks()) keepSet.add(idx + i);
      }
    }

    // Remover chunks que não estão no conjunto
    for (const loadedChunk of this.loadedChunks) {
      if (!keepSet.has(loadedChunk)) {
        delete this.chunks[loadedChunk];
        this.loadedChunks.delete(loadedChunk);
      }
    }
  }
}
