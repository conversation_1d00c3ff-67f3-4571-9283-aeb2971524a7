'use client';

import { ThemeProvider as NextThemesProvider } from 'next-themes';
import { ReactNode } from 'react';

/**
 * Props para o ThemeProvider
 */
interface ThemeProviderProps {
  /** Componentes filhos a serem envolvidos pelo provedor de tema */
  children: ReactNode;
  /** Te<PERSON> padr<PERSON> (light, dark, system) */
  defaultTheme?: string;
  /** Ativar detecção do tema do sistema operacional */
  enableSystem?: boolean;
  /** Desativar transições ao mudar de tema */
  disableTransitionOnChange?: boolean;
}

/**
 * Provedor de tema para a aplicação
 *
 * Encapsula o ThemeProvider do next-themes com configurações padrão para nosso projeto.
 * Isso fornece um ponto único de configuração para o theming em toda a aplicação.
 */
export function ThemeProvider({
  children,
  defaultTheme = 'system',
  enableSystem = true,
  disableTransitionOnChange = false,
}: ThemeProviderProps) {
  return (
    <NextThemesProvider
      attribute="class"
      defaultTheme={defaultTheme}
      enableSystem={enableSystem}
      disableTransitionOnChange={disableTransitionOnChange}
    >
      {children}
    </NextThemesProvider>
  );
}
