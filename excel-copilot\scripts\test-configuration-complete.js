#!/usr/bin/env node

/**
 * 🧪 TESTES COMPLETOS DE CONFIGURAÇÃO - EXCEL COPILOT
 *
 * Suite completa de testes para validar todas as configurações
 * do sistema após a reestruturação da Fase 2.
 *
 * <AUTHOR> Copilot Team
 * @version 2.1.0
 */

const fs = require('fs');
const path = require('path');

// Cores para output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m',
};

/**
 * Suite de testes de configuração
 */
class ConfigurationTestSuite {
  constructor() {
    this.results = {
      passed: 0,
      failed: 0,
      warnings: 0,
      total: 0,
      tests: [],
    };
  }

  /**
   * Executa um teste individual
   */
  runTest(name, testFn, category = 'General') {
    this.results.total++;

    try {
      const startTime = Date.now();
      const result = testFn();
      const duration = Date.now() - startTime;

      if (result.passed) {
        this.results.passed++;
        console.log(`   ${colors.green}✅ ${name}${colors.reset} (${duration}ms)`);
      } else if (result.warning) {
        this.results.warnings++;
        console.log(
          `   ${colors.yellow}⚠️ ${name}${colors.reset}: ${result.message} (${duration}ms)`
        );
      } else {
        this.results.failed++;
        console.log(`   ${colors.red}❌ ${name}${colors.reset}: ${result.message} (${duration}ms)`);
      }

      this.results.tests.push({
        name,
        category,
        passed: result.passed,
        warning: result.warning,
        message: result.message,
        duration,
      });

      return result;
    } catch (error) {
      this.results.failed++;
      console.log(`   ${colors.red}❌ ${name}${colors.reset}: ${error.message}`);

      this.results.tests.push({
        name,
        category,
        passed: false,
        warning: false,
        message: error.message,
        duration: 0,
      });

      return { passed: false, message: error.message };
    }
  }

  /**
   * Testa estrutura de arquivos .env
   */
  testEnvFileStructure() {
    console.log(`\n${colors.blue}📁 Testando Estrutura de Arquivos .env...${colors.reset}`);

    const requiredFiles = [
      '.env.example',
      '.env.local.template',
      '.env.production.template',
      '.env.test.template',
    ];

    requiredFiles.forEach(file => {
      this.runTest(
        `Arquivo ${file} existe`,
        () => {
          const exists = fs.existsSync(file);
          return {
            passed: exists,
            message: exists ? 'Arquivo encontrado' : 'Arquivo não encontrado',
          };
        },
        'Environment Files'
      );
    });

    // Testar conteúdo dos templates
    this.runTest(
      'Templates têm nomenclatura padronizada',
      () => {
        const exampleContent = fs.readFileSync('.env.example', 'utf8');
        const hasStandardVars = [
          'AUTH_NEXTAUTH_SECRET',
          'DB_DATABASE_URL',
          'AI_USE_MOCK',
          'MCP_VERCEL_TOKEN',
        ].every(varName => exampleContent.includes(varName));

        return {
          passed: hasStandardVars,
          message: hasStandardVars
            ? 'Nomenclatura padronizada encontrada'
            : 'Nomenclatura antiga ainda presente',
        };
      },
      'Environment Files'
    );
  }

  /**
   * Testa sistema de validação
   */
  testValidationSystem() {
    console.log(`\n${colors.blue}🔍 Testando Sistema de Validação...${colors.reset}`);

    this.runTest(
      'Arquivo validation-system.ts existe',
      () => {
        const exists = fs.existsSync('src/config/validation-system.ts');
        return {
          passed: exists,
          message: exists
            ? 'Sistema de validação encontrado'
            : 'Sistema de validação não encontrado',
        };
      },
      'Validation System'
    );

    this.runTest(
      'Script de teste de validação funciona',
      () => {
        const exists = fs.existsSync('scripts/test-validation-system.js');
        return {
          passed: exists,
          message: exists ? 'Script de teste encontrado' : 'Script de teste não encontrado',
        };
      },
      'Validation System'
    );
  }

  /**
   * Testa sistema de diagnóstico
   */
  testDiagnosticSystem() {
    console.log(`\n${colors.blue}🔬 Testando Sistema de Diagnóstico...${colors.reset}`);

    this.runTest(
      'Arquivo diagnostic-system.ts existe',
      () => {
        const exists = fs.existsSync('src/config/diagnostic-system.ts');
        return {
          passed: exists,
          message: exists
            ? 'Sistema de diagnóstico encontrado'
            : 'Sistema de diagnóstico não encontrado',
        };
      },
      'Diagnostic System'
    );

    this.runTest(
      'Script de teste de diagnóstico funciona',
      () => {
        const exists = fs.existsSync('scripts/test-diagnostic-system.js');
        return {
          passed: exists,
          message: exists ? 'Script de teste encontrado' : 'Script de teste não encontrado',
        };
      },
      'Diagnostic System'
    );
  }

  /**
   * Testa configuração MCP
   */
  testMCPConfiguration() {
    console.log(`\n${colors.blue}🔌 Testando Configuração MCP...${colors.reset}`);

    this.runTest(
      'Arquivo mcp-config.ts existe',
      () => {
        const exists = fs.existsSync('src/config/mcp-config.ts');
        return {
          passed: exists,
          message: exists ? 'Configuração MCP encontrada' : 'Configuração MCP não encontrada',
        };
      },
      'MCP Configuration'
    );

    this.runTest(
      'Script de teste MCP funciona',
      () => {
        const exists = fs.existsSync('scripts/test-mcp-config.js');
        return {
          passed: exists,
          message: exists ? 'Script de teste MCP encontrado' : 'Script de teste MCP não encontrado',
        };
      },
      'MCP Configuration'
    );

    // Testar nomenclatura padronizada em arquivos MCP
    this.runTest(
      'Arquivos MCP usam nomenclatura padronizada',
      () => {
        const mcpConfigContent = fs.readFileSync('src/config/mcp-config.ts', 'utf8');
        const hasStandardVars = [
          'MCP_VERCEL_TOKEN',
          'MCP_LINEAR_API_KEY',
          'MCP_GITHUB_TOKEN',
        ].every(varName => mcpConfigContent.includes(varName));

        return {
          passed: hasStandardVars,
          message: hasStandardVars
            ? 'Nomenclatura MCP padronizada'
            : 'Nomenclatura MCP não padronizada',
        };
      },
      'MCP Configuration'
    );
  }

  /**
   * Testa migração de variáveis
   */
  testVariableMigration() {
    console.log(`\n${colors.blue}🔄 Testando Migração de Variáveis...${colors.reset}`);

    this.runTest(
      'Script de migração existe',
      () => {
        const exists = fs.existsSync('scripts/migrate-env-variables.js');
        return {
          passed: exists,
          message: exists ? 'Script de migração encontrado' : 'Script de migração não encontrado',
        };
      },
      'Variable Migration'
    );

    this.runTest(
      'Script de finalização existe',
      () => {
        const exists = fs.existsSync('scripts/finalize-migration.js');
        return {
          passed: exists,
          message: exists
            ? 'Script de finalização encontrado'
            : 'Script de finalização não encontrado',
        };
      },
      'Variable Migration'
    );

    // Verificar se arquivos críticos foram migrados
    const criticalFiles = [
      'src/lib/health-checker.ts',
      'src/lib/env-validator.ts',
      'src/config/unified-environment.ts',
    ];

    criticalFiles.forEach(file => {
      this.runTest(
        `${file} usa nomenclatura padronizada`,
        () => {
          if (!fs.existsSync(file)) {
            return { passed: false, message: 'Arquivo não encontrado' };
          }

          const content = fs.readFileSync(file, 'utf8');
          const hasOldVars = ['NEXTAUTH_SECRET', 'DATABASE_URL', 'USE_MOCK_AI'].some(
            oldVar =>
              content.includes(`process.env.${oldVar}`) && !content.includes(`AUTH_${oldVar}`)
          );

          return {
            passed: !hasOldVars,
            message: hasOldVars ? 'Ainda contém variáveis antigas' : 'Migração completa',
          };
        },
        'Variable Migration'
      );
    });
  }

  /**
   * Testa configuração de ambiente unificada
   */
  testUnifiedEnvironment() {
    console.log(`\n${colors.blue}⚙️ Testando Configuração Unificada...${colors.reset}`);

    this.runTest(
      'Arquivo unified-environment.ts existe',
      () => {
        const exists = fs.existsSync('src/config/unified-environment.ts');
        return {
          passed: exists,
          message: exists
            ? 'Configuração unificada encontrada'
            : 'Configuração unificada não encontrada',
        };
      },
      'Unified Environment'
    );

    this.runTest(
      'Configuração unificada tem estrutura correta',
      () => {
        const content = fs.readFileSync('src/config/unified-environment.ts', 'utf8');
        const hasRequiredSections = [
          'UnifiedEnvironment',
          'ValidationResult',
          'ServiceConfig',
        ].every(section => content.includes(section));

        return {
          passed: hasRequiredSections,
          message: hasRequiredSections ? 'Estrutura correta' : 'Estrutura incompleta',
        };
      },
      'Unified Environment'
    );
  }

  /**
   * Testa scripts de utilitários
   */
  testUtilityScripts() {
    console.log(`\n${colors.blue}🛠️ Testando Scripts de Utilitários...${colors.reset}`);

    const utilityScripts = [
      'scripts/validate-env-templates.js',
      'scripts/test-validation-system.js',
      'scripts/test-diagnostic-system.js',
      'scripts/test-mcp-config.js',
      'scripts/finalize-migration.js',
    ];

    utilityScripts.forEach(script => {
      this.runTest(
        `Script ${path.basename(script)} existe`,
        () => {
          const exists = fs.existsSync(script);
          return {
            passed: exists,
            message: exists ? 'Script encontrado' : 'Script não encontrado',
          };
        },
        'Utility Scripts'
      );
    });
  }

  /**
   * Testa compatibilidade com sistema antigo
   */
  testBackwardCompatibility() {
    console.log(`\n${colors.blue}🔄 Testando Compatibilidade com Sistema Antigo...${colors.reset}`);

    this.runTest(
      'Arquivo environment.ts ainda existe',
      () => {
        const exists = fs.existsSync('src/config/environment.ts');
        return {
          passed: exists,
          warning: !exists,
          message: exists ? 'Compatibilidade mantida' : 'Arquivo legado removido',
        };
      },
      'Backward Compatibility'
    );

    this.runTest(
      'Configuração unificada é priorizada',
      () => {
        const unifiedExists = fs.existsSync('src/config/unified-environment.ts');
        const legacyExists = fs.existsSync('src/config/environment.ts');

        if (!unifiedExists) {
          return { passed: false, message: 'Configuração unificada não encontrada' };
        }

        return {
          passed: true,
          warning: legacyExists,
          message: legacyExists
            ? 'Ambos arquivos existem - verificar priorização'
            : 'Apenas configuração unificada',
        };
      },
      'Backward Compatibility'
    );
  }

  /**
   * Executa todos os testes
   */
  runAllTests() {
    console.log(
      `${colors.bold}${colors.blue}🧪 TESTES COMPLETOS DE CONFIGURAÇÃO - EXCEL COPILOT${colors.reset}`
    );
    console.log(`${colors.blue}Fase 2 - Reestruturação | Tarefa 2.7${colors.reset}`);
    console.log('='.repeat(60));

    // Executar todas as suites de teste
    this.testEnvFileStructure();
    this.testValidationSystem();
    this.testDiagnosticSystem();
    this.testMCPConfiguration();
    this.testVariableMigration();
    this.testUnifiedEnvironment();
    this.testUtilityScripts();
    this.testBackwardCompatibility();

    // Gerar relatório final
    this.generateFinalReport();
  }

  /**
   * Gera relatório final
   */
  generateFinalReport() {
    console.log(`\n${colors.bold}📊 RELATÓRIO FINAL DE TESTES${colors.reset}`);
    console.log('='.repeat(50));

    // Estatísticas gerais
    console.log(`\n${colors.blue}📈 ESTATÍSTICAS:${colors.reset}`);
    console.log(`   Total de testes: ${this.results.total}`);
    console.log(`   Passou: ${colors.green}${this.results.passed}${colors.reset}`);
    console.log(`   Falhou: ${colors.red}${this.results.failed}${colors.reset}`);
    console.log(`   Avisos: ${colors.yellow}${this.results.warnings}${colors.reset}`);

    // Taxa de sucesso
    const successRate = (
      ((this.results.passed + this.results.warnings) / this.results.total) *
      100
    ).toFixed(1);
    const successColor =
      successRate >= 90 ? colors.green : successRate >= 70 ? colors.yellow : colors.red;
    console.log(`   Taxa de sucesso: ${successColor}${successRate}%${colors.reset}`);

    // Resumo por categoria
    console.log(`\n${colors.blue}📋 RESUMO POR CATEGORIA:${colors.reset}`);
    const categories = {};
    this.results.tests.forEach(test => {
      if (!categories[test.category]) {
        categories[test.category] = { passed: 0, failed: 0, warnings: 0, total: 0 };
      }
      categories[test.category].total++;
      if (test.passed) {
        categories[test.category].passed++;
      } else if (test.warning) {
        categories[test.category].warnings++;
      } else {
        categories[test.category].failed++;
      }
    });

    Object.entries(categories).forEach(([category, stats]) => {
      const categorySuccessRate = (((stats.passed + stats.warnings) / stats.total) * 100).toFixed(
        1
      );
      const categoryColor =
        categorySuccessRate >= 90
          ? colors.green
          : categorySuccessRate >= 70
            ? colors.yellow
            : colors.red;
      console.log(
        `   ${category}: ${categoryColor}${categorySuccessRate}%${colors.reset} (${stats.passed}/${stats.total})`
      );
    });

    // Status final
    console.log(`\n${colors.bold}🎯 STATUS FINAL:${colors.reset}`);
    const overallSuccess = this.results.failed === 0;
    const statusColor = overallSuccess ? colors.green : colors.red;
    const statusText = overallSuccess ? '✅ TODOS OS TESTES PASSARAM' : '❌ ALGUNS TESTES FALHARAM';
    console.log(`   ${statusColor}${statusText}${colors.reset}`);

    if (overallSuccess) {
      console.log(
        `\n${colors.green}🎉 Fase 2 - Reestruturação CONCLUÍDA COM SUCESSO!${colors.reset}`
      );
      console.log(`${colors.blue}📋 Tarefa 2.7 CONCLUÍDA${colors.reset}`);
      console.log(`${colors.blue}📋 Próximo: Tarefa 2.8 - Documentação Técnica${colors.reset}`);
    } else {
      console.log(
        `\n${colors.yellow}⚠️ Corrija os testes que falharam antes de prosseguir${colors.reset}`
      );
    }

    return overallSuccess;
  }
}

/**
 * Função principal
 */
function main() {
  const testSuite = new ConfigurationTestSuite();
  const success = testSuite.runAllTests();
  process.exit(success ? 0 : 1);
}

// Executar se chamado diretamente
if (require.main === module) {
  main();
}

module.exports = { ConfigurationTestSuite };
