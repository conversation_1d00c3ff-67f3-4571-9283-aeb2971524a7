import { Server, ServerOptions } from 'socket.io';

import { logger } from '@/lib/logger';

/**
 * Implementação do servidor WebSocket para colaboração em tempo real
 */
export class ServerWebSocket {
  private static instance: Server;

  /**
   * Inicializa o servidor Socket.io
   * @returns Instância do servidor Socket.io
   */
  public static init(): Server {
    if (this.instance) {
      return this.instance;
    }

    try {
      const options: Partial<ServerOptions> = {
        path: '/api/ws',
        transports: ['websocket', 'polling'],
        cors: {
          origin: process.env.NEXT_PUBLIC_BASE_URL || '*',
          methods: ['GET', 'POST'],
          credentials: true,
        },
        connectTimeout: 30000,
        pingTimeout: 20000,
        pingInterval: 25000,
        maxHttpBufferSize: 1e6, // 1 MB
      };

      this.instance = new Server(options);

      logger.info('Servidor WebSocket inicializado');

      // Monitorar conexões
      this.instance.on('connection', socket => {
        socket.setMaxListeners(20); // Aumentar limite de listeners
        logger.debug(`Socket conectado: ${socket.id}`);
      });

      return this.instance;
    } catch (error) {
      logger.error('Erro ao inicializar servidor WebSocket', error);
      throw error;
    }
  }

  /**
   * Obtém a instância atual do servidor Socket.io
   * @returns Instância do servidor ou null se não inicializado
   */
  public static getInstance(): Server | null {
    return this.instance || null;
  }

  /**
   * Encerra o servidor WebSocket
   */
  public static shutdown(): void {
    if (this.instance) {
      this.instance.close();
      this.instance = null as unknown as Server;
      logger.info('Servidor WebSocket encerrado');
    }
  }

  /**
   * Envia uma mensagem para todos os clientes em uma sala
   * @param room Sala para enviar a mensagem
   * @param event Nome do evento
   * @param data Dados a serem enviados
   */
  public static broadcast(room: string, event: string, data: unknown): void {
    if (!this.instance) {
      logger.warn('Tentativa de broadcast sem servidor WebSocket inicializado');
      return;
    }

    this.instance.to(room).emit(event, data);
  }

  /**
   * Obtém o número de clientes conectados em uma sala
   * @param room Sala para verificar
   * @returns Número de clientes na sala
   */
  public static async getClientsInRoom(room: string): Promise<number> {
    if (!this.instance) {
      return 0;
    }

    const sockets = await this.instance.in(room).fetchSockets();
    return sockets.length;
  }

  /**
   * Obtém informações sobre as salas ativas
   */
  public static async getRoomsInfo(): Promise<Record<string, number>> {
    if (!this.instance) {
      return {};
    }

    const rooms: Record<string, number> = {};
    const sockets = await this.instance.fetchSockets();

    for (const socket of sockets) {
      for (const room of socket.rooms) {
        // Ignorar a sala com ID do próprio socket
        if (room !== socket.id && room.startsWith('workbook:')) {
          rooms[room] = (rooms[room] || 0) + 1;
        }
      }
    }

    return rooms;
  }
}
