# Linear MCP Integration - Excel Copilot

## 📊 **Visão Geral**

A integração Linear MCP permite ao Excel Copilot monitorar e gerenciar issues, projetos e workflow de desenvolvimento através da API do Linear. Esta integração oferece visibilidade completa do progresso do projeto e facilita a gestão de bugs, features e melhorias.

## 🎯 **Funcionalidades Implementadas**

### ✅ **Monitoramento de Issues**

- **Listagem de Issues**: Visualização de todas as issues com filtros por team, assignee e estado
- **Issues do Excel Copilot**: Filtro específico para issues relacionadas ao projeto
- **Detalhes Completos**: Acesso a título, descrição, estado, assignee, labels e prioridade
- **Atividade Recente**: Tracking de issues atualizadas nas últimas 24 horas

### ✅ **Gestão de Workflow**

- **Estados de Workflow**: Monitoramento de estados (Todo, In Progress, Done, etc.)
- **Transições de Estado**: Acompanhamento do progresso das issues
- **Métricas de Desenvolvimento**: Velocity, tempo médio de conclusão, issues completadas

### ✅ **Criação e Atualização**

- **Criação de Issues**: Criação automática de bugs, features e melhorias
- **Atualização de Issues**: Modificação de título, descrição, estado e assignee
- **Categorização Automática**: Issues criadas são automaticamente marcadas como [Excel Copilot]

### ✅ **Teams e Projetos**

- **Listagem de Teams**: Acesso a todos os teams do workspace
- **Workflow States**: Estados disponíveis para cada team
- **Projetos**: Monitoramento de projetos e seu progresso

### ✅ **Health Monitoring**

- **Status da Conexão**: Verificação contínua da saúde da integração
- **Validação de API Key**: Confirmação de credenciais válidas
- **Métricas de Performance**: Tempo de resposta e disponibilidade

## 🔧 **Configuração**

### **1. Obter API Key do Linear**

1. Acesse [Linear Settings > API](https://linear.app/settings/api)
2. Clique em "Create new API key"
3. Dê um nome descritivo: "Excel Copilot MCP Integration"
4. Selecione as permissões necessárias:
   - ✅ **Read**: Issues, Teams, Projects, Workflow States
   - ✅ **Write**: Issues (para criação e atualização)
5. Copie a API key gerada (formato: `lin_api_...`)

### **2. Configurar Variáveis de Ambiente**

Adicione ao seu arquivo `.env.local`:

```bash
# Linear MCP Integration
LINEAR_API_KEY="lin_api_sua-chave-aqui"
```

### **3. Verificar Configuração**

Execute o health check para confirmar que a integração está funcionando:

```bash
curl http://localhost:3000/api/health
```

Procure pela seção `linear` no response:

```json
{
  "service": "linear",
  "status": "healthy",
  "responseTime": 245,
  "details": {
    "configured": true,
    "apiKeyValid": true,
    "workspaceAccessible": true,
    "issueCount": 15,
    "teamCount": 1,
    "recentActivity": 3
  }
}
```

## 🚀 **Endpoints da API**

### **Status e Métricas**

```http
GET /api/linear/status
GET /api/linear/status?metrics=true&issues=true
POST /api/linear/status
```

### **Gestão de Issues**

```http
GET /api/linear/issues
GET /api/linear/issues?excelCopilotOnly=true
GET /api/linear/issues?teamId=TEAM_ID&state=In Progress
POST /api/linear/issues
PATCH /api/linear/issues
```

### **Teams e Workflow**

```http
GET /api/linear/teams
GET /api/linear/teams?includeStates=true&includeProjects=true
```

## 📋 **Exemplos de Uso**

### **Listar Issues do Excel Copilot**

```bash
curl "http://localhost:3000/api/linear/issues?excelCopilotOnly=true"
```

### **Criar Issue de Bug**

```bash
curl -X POST "http://localhost:3000/api/linear/issues" \
  -H "Content-Type: application/json" \
  -d '{
    "type": "bug",
    "title": "Erro ao processar planilhas grandes",
    "description": "Planilhas com mais de 1000 linhas causam timeout",
    "priority": 1
  }'
```

### **Obter Métricas de Desenvolvimento**

```bash
curl "http://localhost:3000/api/linear/status?metrics=true"
```

### **Atualizar Estado de Issue**

```bash
curl -X PATCH "http://localhost:3000/api/linear/issues" \
  -H "Content-Type: application/json" \
  -d '{
    "issueId": "ISSUE_ID",
    "stateId": "STATE_ID"
  }'
```

## 📊 **Workspace Atual**

**Workspace:** `ngbprojectlinear`  
**Team Principal:** `Ngbprojectlinear (NGB)`  
**Issues Ativas:** 15 issues identificadas  
**Estados Disponíveis:** Todo, In Progress, In Review, Done, Backlog, Canceled, Duplicate

### **Issues Relacionadas ao Excel Copilot Identificadas:**

- Issues sobre MCP integrations
- Features de colaboração em tempo real
- Melhorias de performance
- Correções de bugs

## 🔍 **Monitoramento e Alertas**

### **Health Checks Automáticos**

- ✅ Verificação a cada 5 minutos
- ✅ Alertas para API key inválida
- ✅ Monitoramento de tempo de resposta
- ✅ Tracking de atividade recente

### **Métricas Disponíveis**

- **Total de Issues**: Contagem geral de issues
- **Issues por Estado**: Distribuição por workflow state
- **Velocity**: Issues completadas por período
- **Tempo Médio de Conclusão**: Métrica de performance do team
- **Atividade Recente**: Issues atualizadas nas últimas 24h

## 🛠️ **Arquitetura Técnica**

### **Componentes Principais**

- **`LinearClient`**: Cliente base para GraphQL API
- **`LinearMonitoringService`**: Serviços de alto nível para monitoramento
- **Health Checker**: Integração com sistema de health checks
- **API Endpoints**: Exposição via REST API

### **Tecnologias Utilizadas**

- **GraphQL**: Comunicação com Linear API
- **TypeScript**: Tipagem forte para todos os dados
- **Next.js API Routes**: Endpoints REST para frontend
- **Health Monitoring**: Integração com sistema existente

## 🔐 **Segurança**

### **Proteção de API Key**

- ✅ API key armazenada em variáveis de ambiente
- ✅ Nunca exposta no frontend
- ✅ Validação de permissões na inicialização
- ✅ Logs de segurança para tentativas de acesso

### **Rate Limiting**

- ✅ Respeita limites da Linear API
- ✅ Retry automático com backoff exponencial
- ✅ Cache para reduzir chamadas desnecessárias

## 📈 **Próximos Passos**

### **Melhorias Planejadas**

1. **Webhooks**: Receber notificações em tempo real do Linear
2. **Dashboard**: Interface visual para métricas e issues
3. **Automações**: Criação automática de issues baseada em erros
4. **Integração com IA**: Análise automática de issues para priorização
5. **Relatórios**: Geração de relatórios de progresso e performance

### **Integrações Futuras**

- **Slack**: Notificações de issues críticas
- **GitHub**: Sincronização com PRs e commits

## 🆘 **Troubleshooting**

### **Problemas Comuns**

**❌ "Linear API key não configurada"**

- Verifique se `LINEAR_API_KEY` está no `.env.local`
- Confirme que a variável não tem espaços extras

**❌ "Linear API key inválida ou sem permissões"**

- Verifique se a API key está correta
- Confirme as permissões no Linear Settings

**❌ "Workspace Linear não acessível"**

- Verifique sua conexão com internet
- Confirme que o workspace não foi suspenso

**❌ "Timeout nas requisições"**

- Verifique a latência da rede
- Considere aumentar o timeout nas configurações

## 📞 **Suporte**

Para problemas com a integração Linear MCP:

1. **Verifique os logs**: `console.log` mostra detalhes dos erros
2. **Execute health check**: `/api/health` para diagnóstico
3. **Teste a API key**: Acesse Linear diretamente para confirmar permissões
4. **Consulte documentação**: [Linear API Docs](https://developers.linear.app/docs)

---

**Status da Integração:** ✅ **IMPLEMENTADA E FUNCIONAL**  
**Última Atualização:** Dezembro 2024  
**Versão:** 1.0.0
