#!/usr/bin/env node

/**
 * Script para verificar erros de <PERSON>Script apenas nos arquivos de produção
 */

const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');
const { glob } = require('glob');

// Diretório raiz do projeto
const rootDir = path.resolve(__dirname, '..');

async function main() {
  try {
    console.log('🔍 Encontrando arquivos TypeScript em src/...');

    // Lista todos os arquivos TS/TSX no diretório src exceto testes
    const sourceFiles = await glob(
      [
        'src/**/*.ts',
        'src/**/*.tsx',
        '!src/**/*.test.ts',
        '!src/**/*.test.tsx',
        '!src/**/*.spec.ts',
        '!src/**/*.spec.tsx',
        '!src/**/__tests__/**',
      ],
      { cwd: rootDir, absolute: true }
    );

    if (sourceFiles.length === 0) {
      console.log('❌ Nenhum arquivo TypeScript encontrado!');
      process.exit(1);
    }

    console.log(`📝 Encontrados ${sourceFiles.length} arquivos TypeScript para verificar`);

    // Criar arquivo tsconfig temporário
    const tempConfigPath = path.join(rootDir, 'tsconfig.typecheck.json');

    const typeCheckConfig = {
      extends: './tsconfig.json',
      include: ['src/**/*.ts', 'src/**/*.tsx'],
      exclude: [
        '**/node_modules/**',
        '**/__tests__/**',
        '**/*.test.ts',
        '**/*.test.tsx',
        '**/*.spec.ts',
        '**/*.spec.tsx',
      ],
    };

    // Escrever configuração temporária
    fs.writeFileSync(tempConfigPath, JSON.stringify(typeCheckConfig, null, 2));

    console.log('🔍 Verificando erros de tipos...');

    // Executar verificação de tipos
    execSync(`npx tsc --noEmit --skipLibCheck --project ${tempConfigPath}`, {
      stdio: 'inherit',
      cwd: rootDir,
    });

    console.log('✅ Verificação de tipos concluída com sucesso!');
  } catch (error) {
    // Erro já reportado pelo TypeScript
    console.error('❌ Erro durante a verificação de tipos');
    process.exit(1);
  } finally {
    // Limpar arquivo temporário
    const tempConfigPath = path.join(rootDir, 'tsconfig.typecheck.json');
    if (fs.existsSync(tempConfigPath)) {
      fs.unlinkSync(tempConfigPath);
    }
  }
}

main();
