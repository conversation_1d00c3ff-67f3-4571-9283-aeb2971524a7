#!/usr/bin/env node

/**
 * Script para verificar se todos os serviços necessários estão disponíveis
 * antes de iniciar o Excel Copilot.
 */

import fs from 'fs';
import path from 'path';
import { PrismaClient } from '@prisma/client';
import chalk from 'chalk';
import * as dotenv from 'dotenv';
import axios from 'axios';

// Carregar variáveis de ambiente
const envFile = process.env.NODE_ENV === 'production' ? '.env' : '.env.local';

try {
  dotenv.config({ path: path.resolve(process.cwd(), envFile) });
} catch (error) {
  console.error(chalk.red(`Erro ao carregar ${envFile}: ${error}`));
  process.exit(1);
}

// Função principal para verificar serviços
async function checkServices() {
  console.log(chalk.blue('🔍 Verificando serviços necessários para o Excel Copilot...'));

  // Lista para armazenar problemas encontrados
  const issues: string[] = [];

  // Verificar existência de arquivos essenciais
  console.log(chalk.gray('Verificando arquivos essenciais...'));
  const requiredFiles = ['.env.local', 'next.config.js', 'package.json', 'prisma/schema.prisma'];

  for (const file of requiredFiles) {
    if (!fs.existsSync(path.resolve(process.cwd(), file))) {
      issues.push(
        `Arquivo ${file} não encontrado. Este arquivo é necessário para o funcionamento do sistema.`
      );
    }
  }

  // Verificar variáveis de ambiente essenciais
  console.log(chalk.gray('Verificando variáveis de ambiente...'));
  const requiredEnvVars = ['DATABASE_URL', 'NEXTAUTH_SECRET', 'NEXTAUTH_URL'];

  for (const envVar of requiredEnvVars) {
    if (!process.env[envVar]) {
      issues.push(
        `Variável de ambiente ${envVar} não definida. Configure-a no arquivo .env.local.`
      );
    }
  }

  // Verificar conexão com o banco de dados
  console.log(chalk.gray('Verificando conexão com o banco de dados...'));
  const prisma = new PrismaClient();
  try {
    await prisma.$connect();
    console.log(chalk.green('✅ Conexão com o banco de dados estabelecida.'));

    // Verificar tabelas essenciais
    try {
      await prisma.user.findFirst();
      await prisma.workbook.findFirst();
      console.log(chalk.green('✅ Tabelas essenciais verificadas.'));
    } catch (error) {
      issues.push(
        `Erro ao acessar tabelas essenciais do banco de dados. Execute as migrações com 'npx prisma migrate dev'.`
      );
    }
  } catch (error) {
    issues.push(`Não foi possível conectar ao banco de dados. Erro: ${error}`);
  } finally {
    await prisma.$disconnect();
  }

  // Verificar chave da API de IA se não estiver em modo mock
  if (process.env.AI_USE_MOCK !== 'true') {
    console.log(chalk.gray('Verificando configuração do Vertex AI...'));
    const vertexEnabled = process.env.AI_ENABLED === 'true';
    const hasProjectId = !!process.env.AI_VERTEX_PROJECT_ID;
    const credentialsFile = path.resolve(process.cwd(), 'vertex-credentials.json');
    const hasCredentialsFile = fs.existsSync(credentialsFile);

    if (!vertexEnabled) {
      console.log(
        chalk.yellow('⚠️ Serviço Vertex AI desabilitado. Ative com VERTEX_AI_ENABLED=true.')
      );
    } else if (!hasProjectId && !hasCredentialsFile) {
      issues.push(
        'Vertex AI configurado incorretamente. Configure VERTEX_AI_PROJECT_ID ou disponibilize o arquivo vertex-credentials.json na raiz do projeto.'
      );
    } else {
      if (hasCredentialsFile) {
        console.log(chalk.green('✅ Arquivo de credenciais do Vertex AI encontrado.'));
      }
      if (hasProjectId) {
        console.log(
          chalk.green(`✅ Project ID do Vertex AI configurado: ${process.env.AI_VERTEX_PROJECT_ID}`)
        );
      }
    }
  } else {
    console.log(
      chalk.yellow('⚠️ Modo mock da API de IA ativo. A IA terá funcionalidade limitada.')
    );
  }

  // Verificar acesso à internet
  console.log(chalk.gray('Verificando conexão com a internet...'));
  try {
    await axios.get('https://www.google.com', { timeout: 5000 });
    console.log(chalk.green('✅ Conexão com a internet estabelecida.'));
  } catch (error) {
    issues.push(
      'Não foi possível estabelecer conexão com a internet. Verifique sua conexão de rede.'
    );
  }

  // Verificar espaço em disco
  console.log(chalk.gray('Verificando espaço em disco...'));
  try {
    const tempFile = path.join(process.cwd(), 'temp-check.txt');
    fs.writeFileSync(tempFile, 'test', 'utf8');
    fs.unlinkSync(tempFile);
    console.log(chalk.green('✅ Sistema de arquivos funcional.'));
  } catch (error) {
    issues.push(`Problema com o sistema de arquivos. Erro: ${error}`);
  }

  // Exibir resultado da verificação
  if (issues.length === 0) {
    console.log(
      chalk.green('\n✅ Todos os serviços estão disponíveis e configurados corretamente!')
    );
    console.log(chalk.blue('\nO Excel Copilot está pronto para ser iniciado.'));
    process.exit(0);
  } else {
    console.log(
      chalk.red(`\n❌ Foram encontrados ${issues.length} problemas que precisam ser resolvidos:`)
    );
    issues.forEach((issue, index) => {
      console.log(chalk.yellow(`${index + 1}. ${issue}`));
    });
    console.log(chalk.blue('\nResolva os problemas acima antes de iniciar o Excel Copilot.'));
    process.exit(1);
  }
}

// Executar a verificação
checkServices().catch(error => {
  console.error(chalk.red(`Erro inesperado: ${error}`));
  process.exit(1);
});
