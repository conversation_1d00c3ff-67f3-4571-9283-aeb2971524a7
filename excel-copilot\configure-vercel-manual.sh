#!/bin/bash
# Script manual para configurar variáveis no Vercel
# Execute este script se o automático falhar

echo "🔧 Configurando variáveis críticas no Vercel..."
echo "⚠️ Certifique-se de estar logado: vercel login"
echo ""

echo "Configurando USE_MOCK_AI..."
echo "false" | vercel env add USE_MOCK_AI production --force

echo "Configurando NEXT_PUBLIC_USE_MOCK_AI..."
echo "false" | vercel env add NEXT_PUBLIC_USE_MOCK_AI production --force

echo "Configurando FORCE_GOOGLE_MOCKS..."
echo "false" | vercel env add FORCE_GOOGLE_MOCKS production --force

echo "Configurando NEXT_PUBLIC_DISABLE_VERTEX_AI..."
echo "false" | vercel env add NEXT_PUBLIC_DISABLE_VERTEX_AI production --force

echo "Configurando VERTEX_AI_ENABLED..."
echo "true" | vercel env add VERTEX_AI_ENABLED production --force

echo "Configurando NODE_ENV..."
echo "production" | vercel env add NODE_ENV production --force

echo "Configurando NEXT_PUBLIC_FORCE_PRODUCTION..."
echo "true" | vercel env add NEXT_PUBLIC_FORCE_PRODUCTION production --force

echo "Configurando NEXTAUTH_SECRET..."
echo "dW5jL4x7Q2tPaDZkVzFqc3pVTEhuMDdYZ0tLbldnRkxRV3hNeUJTRHJSWQ" | vercel env add NEXTAUTH_SECRET production --force

echo "Configurando NEXTAUTH_URL..."
echo "https://excel-copilot-eight.vercel.app" | vercel env add NEXTAUTH_URL production --force

echo "Configurando GOOGLE_CLIENT_ID..."
echo "217111050148-1gocm6a0sa9jcrk8s08dubqn8n2001lv.apps.googleusercontent.com" | vercel env add GOOGLE_CLIENT_ID production --force

echo "Configurando GOOGLE_CLIENT_SECRET..."
echo "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" | vercel env add GOOGLE_CLIENT_SECRET production --force

echo "Configurando APP_URL..."
echo "https://excel-copilot-eight.vercel.app" | vercel env add APP_URL production --force

echo "Configurando NEXT_PUBLIC_APP_URL..."
echo "https://excel-copilot-eight.vercel.app" | vercel env add NEXT_PUBLIC_APP_URL production --force

echo "Configurando SKIP_AUTH_PROVIDERS..."
echo "false" | vercel env add SKIP_AUTH_PROVIDERS production --force

echo "Configurando NEXT_PUBLIC_DISABLE_CSRF..."
echo "false" | vercel env add NEXT_PUBLIC_DISABLE_CSRF production --force

echo "Configurando DISABLE_ENV_VALIDATION..."
echo "false" | vercel env add DISABLE_ENV_VALIDATION production --force

echo "✅ Configuração manual concluída!"
echo "🚀 Execute: vercel --prod"
