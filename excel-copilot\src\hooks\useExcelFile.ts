import { useState } from 'react';
import { toast } from 'sonner';

// Definições de tipos para as funções de Excel
interface ExcelModule {
  createExcelFile: (sheets: { name: string; data: unknown }[], fileName: string) => Promise<Blob>;
  downloadExcelFile: (blob: Blob, fileName: string) => void;
  parseExcelFile: (file: File) => Promise<{ name: string; data: unknown }[]>;
  isValidExcelFile: (file: File) => boolean;
  exportToCSV: (sheets: { name: string; data: unknown }[], fileName: string) => void;
}

// Simulação de importação
const { createExcelFile, downloadExcelFile, parseExcelFile, isValidExcelFile, exportToCSV } =
  {} as ExcelModule;

interface ExcelSheet {
  name: string;
  data: unknown;
}

type ExportFormat = 'xlsx' | 'csv';

/**
 * Hook para facilitar operações com arquivos Excel
 */
export function useExcelFile() {
  const [isLoading, setIsLoading] = useState(false);

  /**
   * Importa um arquivo Excel
   * @param file Arquivo a ser importado
   * @param options Opções de importação
   * @returns Promise com os dados das planilhas ou null em caso de erro
   */
  const importExcel = async (
    file: File,
    options: {
      onSuccess?: (data: { fileName: string; sheets: ExcelSheet[] }) => void;
      maxSize?: number; // em bytes
      trackAnalytics?: boolean;
    } = {}
  ): Promise<{ fileName: string; sheets: ExcelSheet[] } | null> => {
    if (!file) return null;

    const maxSize = options.maxSize || 10 * 1024 * 1024; // 10MB default

    setIsLoading(true);
    const toastId = toast.loading(`Processando arquivo ${file.name}...`);

    try {
      // Verificações básicas
      if (!isValidExcelFile(file)) {
        throw new Error('Formato inválido: envie um arquivo Excel (.xlsx ou .xls)');
      }

      if (file.size > maxSize) {
        throw new Error(
          `Arquivo muito grande: o tamanho máximo é ${(maxSize / (1024 * 1024)).toFixed(0)}MB`
        );
      }

      // Processar o arquivo Excel
      const sheets = await parseExcelFile(file);

      // Verificar se obteve dados
      if (!sheets || sheets.length === 0) {
        throw new Error('O arquivo não contém dados válidos');
      }

      // Sucesso
      toast.success(`${file.name} carregado com sucesso!`, {
        id: toastId,
        duration: 3000,
      });

      // Registrar evento de importação (analytics)
      if (options.trackAnalytics && typeof window !== 'undefined' && 'gtag' in window) {
        const windowWithGtag = window as unknown as import('@/types/global-types').WindowWithGtag;
        const gtag = windowWithGtag.gtag;
        if (typeof gtag === 'function') {
          gtag('event', 'import_excel', {
            file_size: file.size,
            file_type: file.type,
            sheet_count: sheets.length,
          });
        }
      }

      const result = {
        fileName: file.name,
        sheets: sheets,
      };

      // Callback de sucesso se fornecido
      if (options.onSuccess) {
        options.onSuccess(result);
      }

      return result;
    } catch (error) {
      console.error('Erro ao processar arquivo Excel:', error);
      toast.error('Erro ao importar arquivo', {
        id: toastId,
        description:
          error instanceof Error
            ? error.message
            : 'Não foi possível processar o arquivo. Tente novamente.',
        duration: 4000,
      });
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Exporta dados para um arquivo Excel ou CSV
   * @param sheets Array de planilhas com dados
   * @param fileName Nome do arquivo sem extensão
   * @param format Formato de exportação (xlsx ou csv)
   * @param options Opções adicionais
   * @returns Promise<boolean> indicando sucesso ou falha
   */
  const exportExcel = async (
    sheets: ExcelSheet[],
    fileName: string,
    format: ExportFormat = 'xlsx',
    options: {
      trackAnalytics?: boolean;
      workbookId?: string;
    } = {}
  ): Promise<boolean> => {
    setIsLoading(true);

    // Remover caracteres especiais e espaços do nome do arquivo
    const safeFileName = fileName.replace(/[^a-z0-9]/gi, '_').toLowerCase();
    const timestamp = Date.now();
    const fullFileName = `${safeFileName}_${timestamp}`;

    const toastId = toast.loading(`Preparando exportação ${format.toUpperCase()}...`);

    try {
      // Verificar se temos pelo menos uma planilha com dados
      if (!sheets || sheets.length === 0) {
        throw new Error('Não há dados para exportar');
      }

      // Exportar com base no formato
      if (format === 'xlsx') {
        // Gerar o arquivo Excel
        const blob = await createExcelFile(sheets, fileName);

        // Fazer download do arquivo
        downloadExcelFile(blob, `${fullFileName}.xlsx`);

        // Registrar evento de exportação (analytics)
        if (options.trackAnalytics && typeof window !== 'undefined' && 'gtag' in window) {
          const windowWithGtag = window as unknown as import('@/types/global-types').WindowWithGtag;
          const gtag = windowWithGtag.gtag;
          if (typeof gtag === 'function') {
            gtag('event', 'export_excel', {
              workbook_id: options.workbookId,
              sheet_count: sheets.length,
              format: 'xlsx',
            });
          }
        }
      } else if (format === 'csv') {
        // Exportar como CSV
        exportToCSV(sheets, fullFileName);

        // Registrar evento de exportação (analytics)
        if (options.trackAnalytics && typeof window !== 'undefined' && 'gtag' in window) {
          const windowWithGtag = window as unknown as import('@/types/global-types').WindowWithGtag;
          const gtag = windowWithGtag.gtag;
          if (typeof gtag === 'function') {
            gtag('event', 'export_csv', {
              workbook_id: options.workbookId,
              sheet_count: sheets.length,
              format: 'csv',
            });
          }
        }
      }

      // Mostrar mensagem de sucesso
      toast.success(`Exportação ${format.toUpperCase()} concluída`, {
        id: toastId,
        description: `Arquivo "${fullFileName}.${format}" baixado com sucesso!`,
        duration: 3000,
      });

      return true;
    } catch (error) {
      console.error(`Erro ao exportar ${format}:`, error);
      toast.error(`Erro na exportação ${format.toUpperCase()}`, {
        id: toastId,
        description:
          error instanceof Error
            ? error.message
            : `Não foi possível exportar para ${format.toUpperCase()}. Tente novamente.`,
        duration: 4000,
      });
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  return {
    isLoading,
    importExcel,
    exportExcel,
  };
}
