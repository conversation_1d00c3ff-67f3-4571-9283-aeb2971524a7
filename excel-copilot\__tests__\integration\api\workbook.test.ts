import { server } from '../../mocks/server';
import { setupServer } from 'msw/node';
import { http, HttpResponse } from 'msw';

// Criar uma instância separada do servidor de mock para esses testes
const mockServer = setupServer();

/**
 * Teste de integração para as operações de workbook
 * Usando MSW para simular as respostas da API
 */
describe('Workbook API Integration', () => {
  // Configurar e limpar o servidor mock
  beforeAll(() => mockServer.listen());
  afterEach(() => mockServer.resetHandlers());
  afterAll(() => mockServer.close());

  beforeEach(() => {
    // Limpar mocks do fetch
    jest.clearAllMocks();
  });

  test('deve processar operações em colunas corretamente', async () => {
    // Sobrescrever handler para este teste específico
    server.use(
      http.post('/api/workbook/operations', () => {
        return HttpResponse.json({
          success: true,
          resultSummary: 'Soma da coluna Valor: 600',
          updatedData: {
            headers: ['Nome', 'Valor'],
            rows: [
              ['Item 1', 100],
              ['Item 2', 200],
              ['Item 3', 300],
            ],
          },
        });
      })
    );

    // Fazer a requisição para a API
    const response = await fetch('/api/workbook/operations', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        operation: {
          type: 'COLUMN_OPERATION',
          data: {
            column: 'Valor',
            operation: 'SUM',
          },
        },
        sheetData: {
          headers: ['Nome', 'Valor'],
          rows: [
            ['Item 1', 100],
            ['Item 2', 200],
            ['Item 3', 300],
          ],
        },
      }),
    });

    // Verificar a resposta
    expect(response.status).toBe(200);
    const data = await response.json();
    expect(data.success).toBe(true);
    expect(data.resultSummary).toBe('Soma da coluna Valor: 600');
    expect(data.updatedData).toBeDefined();
    expect(data.updatedData.headers).toHaveLength(2);
    expect(data.updatedData.rows).toHaveLength(3);
  });

  test('deve lidar com erros na API corretamente', async () => {
    // Sobrescrever handler para simular erro
    server.use(
      http.post('/api/workbook/operations', () => {
        return HttpResponse.json(
          { success: false, error: 'Coluna não encontrada' },
          { status: 400 }
        );
      })
    );

    // Fazer a requisição para a API
    const response = await fetch('/api/workbook/operations', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        operation: {
          type: 'COLUMN_OPERATION',
          data: {
            column: 'ColunaInexistente',
            operation: 'SUM',
          },
        },
        sheetData: {
          headers: ['Nome', 'Valor'],
          rows: [
            ['Item 1', 100],
            ['Item 2', 200],
            ['Item 3', 300],
          ],
        },
      }),
    });

    // Verificar a resposta de erro
    expect(response.status).toBe(400);
    const data = await response.json();
    expect(data.success).toBe(false);
    expect(data.error).toBe('Coluna não encontrada');
  });
});
