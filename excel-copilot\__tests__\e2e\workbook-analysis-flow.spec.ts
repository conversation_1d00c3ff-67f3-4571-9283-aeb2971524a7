import { test, expect } from '@playwright/test';
import { login } from './helpers/auth';
import path from 'path';
import fs from 'fs';

// Arquivo de teste para o fluxo completo de análise de planilha
test.describe('Fluxo completo de análise de planilha', () => {
  // Definir arquivo de teste a ser usado
  const testFilePath = path.join(__dirname, '../mocks/sample-data.xlsx');

  // Verificar se o arquivo existe antes de executar os testes
  test.beforeAll(async () => {
    // Criar diretório de mocks se não existir
    if (!fs.existsSync(path.join(__dirname, '../mocks'))) {
      fs.mkdirSync(path.join(__dirname, '../mocks'), { recursive: true });
    }

    // Se o arquivo de teste não existir, criar um arquivo Excel básico para testes
    if (!fs.existsSync(testFilePath)) {
      // Podemos usar um pacote para criar um arquivo Excel, como exceljs
      console.log(
        'Arquivo de teste não encontrado. Por favor, crie um arquivo Excel de teste em __tests__/mocks/sample-data.xlsx'
      );
      throw new Error('Arquivo de teste não encontrado');
    }
  });

  test('deve fazer upload de planilha, visualizar dados e executar comando de análise', async ({
    page,
  }) => {
    // Login no sistema
    await login(page);

    // Navegar para a página inicial
    await page.goto('/');

    // Verificar se a página carregou corretamente
    await expect(page.locator('h1')).toContainText('Excel Copilot');

    // Mudar para a aba de upload
    await page.getByRole('tab', { name: 'Upload de Planilha' }).click();

    // Esperar pelo componente de upload
    const fileInput = page.locator('input[type="file"]');
    await expect(fileInput).toBeVisible();

    // Fazer upload do arquivo de teste
    await fileInput.setInputFiles(testFilePath);

    // Esperar pelo formulário de detalhes da planilha e preencher
    await page.getByLabel('Nome da Planilha').fill('Planilha de Teste E2E');
    await page.getByLabel('Descrição (opcional)').fill('Planilha para testes automatizados');

    // Enviar o formulário
    await page.getByRole('button', { name: 'Continuar' }).click();

    // Esperar redirecionamento para o dashboard
    await expect(page).toHaveURL(/dashboard/);

    // Verificar se a planilha foi adicionada
    await expect(page.getByText('Planilha de Teste E2E')).toBeVisible();

    // Clicar na planilha para abrir
    await page.getByText('Planilha de Teste E2E').click();

    // Esperar pela página de visualização da planilha
    await expect(page).toHaveURL(/workbook\/[a-zA-Z0-9-]+/);

    // Verificar se os dados da planilha foram carregados
    await expect(page.locator('table')).toBeVisible();

    // Mudar para a aba de chat
    await page.getByRole('tab', { name: 'Chat' }).click();

    // Verificar se o componente de chat está visível
    const chatInput = page.getByPlaceholder('Digite um comando ou faça uma pergunta...');
    await expect(chatInput).toBeVisible();

    // Enviar um comando para análise
    await chatInput.fill('Some os valores da primeira coluna numérica');
    await page.getByRole('button', { name: 'Enviar' }).click();

    // Esperar pela resposta da IA (pode demorar um pouco)
    await expect(page.locator('.chat-message-assistant')).toBeVisible({ timeout: 15000 });

    // Verificar se a resposta contém informações sobre a soma
    const assistantMessage = page.locator('.chat-message-assistant').first();
    await expect(assistantMessage).toContainText(/soma|total|resultado/i);

    // Verificar se há indicação de que a operação foi executada
    await expect(page.locator('.operation-executed-indicator')).toBeVisible();

    // Mudar para a aba de dados para verificar o resultado
    await page.getByRole('tab', { name: 'Dados' }).click();

    // Verificar se a tabela ainda está visível após a operação
    await expect(page.locator('table')).toBeVisible();

    // Testar exportação da planilha
    await page.getByRole('button', { name: 'Baixar XLSX' }).click();

    // Verificar se o download foi iniciado
    const download = await page.waitForEvent('download');

    // Verificar se o nome do arquivo está correto
    expect(download.suggestedFilename()).toContain('.xlsx');

    // Fechar o download
    await download.delete();

    // Tentar outro comando de análise
    await page.getByRole('tab', { name: 'Chat' }).click();

    // Enviar comando para criar um gráfico
    await chatInput.fill('Crie um gráfico de barras com os dados da planilha');
    await page.getByRole('button', { name: 'Enviar' }).click();

    // Esperar pela resposta da IA (pode demorar um pouco)
    await expect(page.locator('.chat-message-assistant')).toBeVisible({ timeout: 15000 });

    // Verificar se a resposta contém informações sobre o gráfico
    const chartResponse = page.locator('.chat-message-assistant').first();
    await expect(chartResponse).toContainText(/gráfico|chart|visualização/i);

    // Mudar para a aba de análise para verificar o gráfico
    await page.getByRole('tab', { name: 'Análise' }).click();

    // Verificar se há elementos de gráfico visíveis
    await expect(page.locator('canvas')).toBeVisible({ timeout: 10000 });
  });

  test('deve tratar erros em comandos inválidos', async ({ page }) => {
    // Login no sistema
    await login(page);

    // Navegar para a página inicial
    await page.goto('/dashboard');

    // Verificar se a página carregou corretamente
    await expect(page.getByText('Minhas Planilhas')).toBeVisible();

    // Clicar na planilha criada no teste anterior
    await page.getByText('Planilha de Teste E2E').first().click();

    // Esperar pela página de visualização da planilha
    await expect(page).toHaveURL(/workbook\/[a-zA-Z0-9-]+/);

    // Mudar para a aba de chat
    await page.getByRole('tab', { name: 'Chat' }).click();

    // Verificar se o componente de chat está visível
    const chatInput = page.getByPlaceholder('Digite um comando ou faça uma pergunta...');
    await expect(chatInput).toBeVisible();

    // Enviar um comando inválido
    await chatInput.fill('abcdefg comando totalmente inválido 12345');
    await page.getByRole('button', { name: 'Enviar' }).click();

    // Esperar pela resposta da IA (pode demorar um pouco)
    await expect(page.locator('.chat-message-assistant')).toBeVisible({ timeout: 15000 });

    // Verificar se a resposta contém uma mensagem de erro ou esclarecimento
    const errorResponse = page.locator('.chat-message-assistant').first();
    await expect(errorResponse).toContainText(/não entendi|inválido|específico|ajudar/i);

    // Não deve haver indicador de operação executada
    await expect(page.locator('.operation-executed-indicator')).not.toBeVisible();
  });

  test('deve permitir a visualização do histórico de chat', async ({ page }) => {
    // Login no sistema
    await login(page);

    // Navegar para a página inicial
    await page.goto('/dashboard');

    // Verificar se a página carregou corretamente
    await expect(page.getByText('Minhas Planilhas')).toBeVisible();

    // Clicar na planilha criada no teste anterior
    await page.getByText('Planilha de Teste E2E').first().click();

    // Esperar pela página de visualização da planilha
    await expect(page).toHaveURL(/workbook\/[a-zA-Z0-9-]+/);

    // Mudar para a aba de chat
    await page.getByRole('tab', { name: 'Chat' }).click();

    // Verificar se o histórico de chat anterior está visível
    await expect(page.locator('.chat-message-user')).toBeVisible();

    // Deve haver pelo menos duas mensagens do usuário (dos testes anteriores)
    const userMessages = page.locator('.chat-message-user');
    expect(await userMessages.count()).toBeGreaterThanOrEqual(2);

    // Deve haver pelo menos duas respostas do assistente
    const assistantMessages = page.locator('.chat-message-assistant');
    expect(await assistantMessages.count()).toBeGreaterThanOrEqual(2);
  });
});

// Teste para verificar o fluxo de usuários sem planilhas
test('deve mostrar estado vazio para usuários sem planilhas', async ({ page }) => {
  // Login no sistema
  await login(page, { email: '<EMAIL>' }); // Usuário diferente sem planilhas

  // Navegar para a página inicial
  await page.goto('/dashboard');

  // Verificar se a mensagem de estado vazio está visível
  await expect(
    page.getByText(
      /não possui nenhuma planilha|comece criando uma planilha|nenhuma planilha encontrada/i
    )
  ).toBeVisible();

  // Deve mostrar botão para criar nova planilha
  await expect(page.getByRole('button', { name: /criar|nova planilha|começar/i })).toBeVisible();
});

// Criar um helper de autenticação em __tests__/e2e/helpers/auth.ts
/**
 * Este arquivo será criado separadamente como parte da implementação
 * Exemplo do conteúdo:
 *
 * export async function login(page, { email = '<EMAIL>' } = {}) {
 *   await page.goto('/auth/signin');
 *   await page.fill('input[name="email"]', email);
 *   await page.click('button[type="submit"]');
 *   // Verificar redirecionamento bem-sucedido
 *   await page.waitForURL('/dashboard');
 * }
 */
