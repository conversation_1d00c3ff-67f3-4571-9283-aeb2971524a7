/**
 * Utilitários para resolver problemas de tipagem TypeScript
 * Especialmente útil para problemas com exactOptionalPropertyTypes
 */

import { toNullableInput } from '@/server/db/utils';
import { WithOptionalFields } from '@/types/optional-types';

/**
 * Converte um objeto para ser compatível com exactOptionalPropertyTypes
 * Remove propriedades undefined e as converte para null quando necessário
 */
export function makeExactOptional<T extends Record<string, unknown>>(
  obj: T
): WithOptionalFields<T> {
  const result = {} as WithOptionalFields<T>;

  for (const [key, value] of Object.entries(obj)) {
    if (value !== undefined) {
      (result as any)[key] = value;
    }
  }

  return result;
}

/**
 * Converte um objeto para ser compatível com Prisma
 * Converte undefined para null onde necessário
 */
export function makePrismaCompatible<T extends Record<string, unknown>>(
  obj: T
): Record<string, unknown> {
  return toNullableInput(obj);
}

/**
 * Tipo guard para verificar se um valor não é null nem undefined
 */
export function isDefined<T>(value: T | null | undefined): value is T {
  return value !== null && value !== undefined;
}

/**
 * Tipo guard para verificar se um valor é uma string não vazia
 */
export function isNonEmptyString(value: unknown): value is string {
  return typeof value === 'string' && value.length > 0;
}

/**
 * Tipo guard para verificar se um valor é um número válido
 */
export function isValidNumber(value: unknown): value is number {
  return typeof value === 'number' && !isNaN(value) && isFinite(value);
}

/**
 * Tipo guard para verificar se um valor é um array não vazio
 */
export function isNonEmptyArray<T>(value: unknown): value is T[] {
  return Array.isArray(value) && value.length > 0;
}

/**
 * Utilitário para criar objetos com propriedades opcionais de forma segura
 */
export function createOptionalObject<T extends Record<string, unknown>>(
  required: Partial<T>,
  optional: Partial<T> = {}
): WithOptionalFields<T> {
  const result = { ...required } as WithOptionalFields<T>;

  // Adicionar apenas propriedades opcionais que não são undefined
  for (const [key, value] of Object.entries(optional)) {
    if (value !== undefined) {
      (result as any)[key] = value;
    }
  }

  return result;
}

/**
 * Utilitário para lidar com propriedades de eventos de forma segura
 */
export function safeEventHandler<T extends Event>(handler: (event: T) => void): (event: T) => void {
  return (event: T) => {
    try {
      handler(event);
    } catch (error) {
      console.error('Erro no manipulador de evento:', error);
    }
  };
}

/**
 * Utilitário para criar props de componente de forma segura
 */
export function createComponentProps<T extends Record<string, unknown>>(
  props: T
): WithOptionalFields<T> {
  return makeExactOptional(props);
}

/**
 * Utilitário para validar e limpar dados de formulário
 */
export function sanitizeFormData<T extends Record<string, unknown>>(
  data: T
): WithOptionalFields<T> {
  const sanitized = {} as WithOptionalFields<T>;

  for (const [key, value] of Object.entries(data)) {
    if (value !== undefined && value !== null) {
      // Limpar strings
      if (typeof value === 'string') {
        const trimmed = value.trim();
        if (trimmed.length > 0) {
          (sanitized as any)[key] = trimmed;
        }
      } else {
        (sanitized as any)[key] = value;
      }
    }
  }

  return sanitized;
}

/**
 * Utilitário para converter dados de API para uso em componentes
 */
export function normalizeApiData<T extends Record<string, unknown>>(
  data: T
): WithOptionalFields<T> {
  const normalized = {} as WithOptionalFields<T>;

  for (const [key, value] of Object.entries(data)) {
    // Converter null para undefined para compatibilidade com React
    if (value === null) {
      // Não adicionar a propriedade se for null
      continue;
    } else if (value !== undefined) {
      (normalized as any)[key] = value;
    }
  }

  return normalized;
}

/**
 * Tipo para manipuladores de eventos seguros
 */
export type SafeEventHandler<T extends Event> = (event: T) => void;

/**
 * Tipo para props de componente com propriedades opcionais seguras
 */
export type SafeComponentProps<T> = WithOptionalFields<T>;

/**
 * Utilitário para criar callbacks seguros que não falham
 */
export function safeCallback<TArgs extends unknown[], TReturn>(
  callback: (...args: TArgs) => TReturn,
  fallback?: TReturn
): (...args: TArgs) => TReturn | undefined {
  return (...args: TArgs) => {
    try {
      return callback(...args);
    } catch (error) {
      console.error('Erro no callback:', error);
      return fallback;
    }
  };
}
