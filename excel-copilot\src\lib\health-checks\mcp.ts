/**
 * 🔌 MCP HEALTH CHECK - EXCEL COPILOT
 *
 * Verifica a conectividade e funcionamento das integrações MCP
 * (Model Context Protocol): Vercel, Linear, GitHub
 *
 * <AUTHOR> Copilot Team
 * @version 1.0.0
 */

import { <PERSON>HealthCheck, HealthStatus, healthLogger } from '../health-checks';
// import { unifiedEnv } from '@/config/unified-environment';

// ============================================================================
// TIPOS E INTERFACES
// ============================================================================

interface MCPServiceResult {
  service: string;
  success: boolean;
  responseTime: number;
  details?: {
    projectId?: string;
    teamId?: string;
    apiKey?: string;
    token?: string;
    [key: string]: string | undefined;
  };
  error?: string;
}

// ============================================================================
// MCP HEALTH CHECK
// ============================================================================

export class MCPHealthCheck extends BaseHealthCheck {
  constructor() {
    super('mcp');
  }

  protected async check(): Promise<{
    status: HealthStatus;
    details?: Record<string, string | number | boolean | undefined>;
  }> {
    try {
      // Verificar variáveis de ambiente diretamente
      const vercelToken = process.env.MCP_VERCEL_TOKEN || process.env.MCP_VERCEL_TOKEN;
      const vercelProjectId =
        process.env.MCP_VERCEL_PROJECT_ID || process.env.MCP_VERCEL_PROJECT_ID;
      const vercelTeamId = process.env.MCP_VERCEL_TEAM_ID || process.env.MCP_VERCEL_TEAM_ID;

      const linearApiKey = process.env.MCP_LINEAR_API_KEY || process.env.MCP_LINEAR_API_KEY;
      const githubToken = process.env.MCP_GITHUB_TOKEN || process.env.MCP_GITHUB_TOKEN;

      // Testar cada integração MCP
      const results = await Promise.allSettled([
        this.testVercelMCP({
          ...(vercelToken && { token: vercelToken }),
          ...(vercelProjectId && { projectId: vercelProjectId }),
          ...(vercelTeamId && { teamId: vercelTeamId }),
        }),
        this.testLinearMCP({
          ...(linearApiKey && { apiKey: linearApiKey }),
        }),
        this.testGitHubMCP({
          ...(githubToken && { token: githubToken }),
        }),
      ]);

      const mcpResults = {
        vercel: this.extractResult(results[0]),
        linear: this.extractResult(results[1]),
        github: this.extractResult(results[2]),
      };

      // Determinar status geral
      const enabledServices = [
        vercelToken && vercelProjectId && vercelTeamId ? 'vercel' : null,
        linearApiKey ? 'linear' : null,
        githubToken ? 'github' : null,
      ].filter(Boolean);

      const healthyServices = Object.values(mcpResults).filter(result => result.success);

      let status: HealthStatus = 'healthy';

      if (enabledServices.length === 0) {
        status = 'degraded'; // Nenhuma integração habilitada
      } else if (healthyServices.length === 0) {
        status = 'unhealthy'; // Todas as integrações falharam
      } else if (healthyServices.length < enabledServices.length) {
        status = 'degraded'; // Algumas integrações falharam
      }

      healthLogger.info('MCP health check completed', {
        status,
        enabledServices: enabledServices.length,
        healthyServices: healthyServices.length,
      });

      return {
        status,
        details: {
          message: 'MCP integrations checked',
          enabled: enabledServices.length,
          healthy: healthyServices.length,
          degraded: enabledServices.length - healthyServices.length,
          vercel_success: mcpResults.vercel.success,
          linear_success: mcpResults.linear.success,
          github_success: mcpResults.github.success,
        },
      };
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      const errorStack = error instanceof Error ? error.stack : undefined;

      healthLogger.error('MCP health check failed', {
        error: errorMessage,
        stack: errorStack,
      });

      return {
        status: 'unhealthy',
        details: {
          message: 'MCP health check failed',
          enabled: 0,
          healthy: 0,
          degraded: 0,
          vercel_success: false,
          linear_success: false,
          github_success: false,
          error: errorMessage,
        },
      };
    }
  }

  /**
   * Testa a integração Vercel MCP
   */
  private async testVercelMCP(config: {
    token?: string;
    projectId?: string;
    teamId?: string;
  }): Promise<MCPServiceResult> {
    const start = Date.now();

    try {
      const { token, projectId, teamId } = config;

      if (!token || !projectId || !teamId) {
        return {
          service: 'vercel',
          success: false,
          responseTime: Date.now() - start,
          error: 'Missing Vercel MCP credentials',
        };
      }

      // Simular teste de conectividade
      await this.simulateAPICall('vercel', 200);

      return {
        service: 'vercel',
        success: true,
        responseTime: Date.now() - start,
        details: {
          projectId: projectId.substring(0, 10) + '...',
          teamId: teamId.substring(0, 10) + '...',
        },
      };
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      return {
        service: 'vercel',
        success: false,
        responseTime: Date.now() - start,
        error: errorMessage,
      };
    }
  }

  /**
   * Testa a integração Linear MCP
   */
  private async testLinearMCP(config: { apiKey?: string }): Promise<MCPServiceResult> {
    const start = Date.now();

    try {
      const { apiKey } = config;

      if (!apiKey) {
        return {
          service: 'linear',
          success: false,
          responseTime: Date.now() - start,
          error: 'Missing Linear MCP API key',
        };
      }

      // Simular teste de conectividade
      await this.simulateAPICall('linear', 300);

      return {
        service: 'linear',
        success: true,
        responseTime: Date.now() - start,
        details: {
          apiKey: apiKey.substring(0, 8) + '...',
        },
      };
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      return {
        service: 'linear',
        success: false,
        responseTime: Date.now() - start,
        error: errorMessage,
      };
    }
  }

  /**
   * Testa a integração GitHub MCP
   */
  private async testGitHubMCP(config: { token?: string }): Promise<MCPServiceResult> {
    const start = Date.now();

    try {
      const { token } = config;

      if (!token) {
        return {
          service: 'github',
          success: false,
          responseTime: Date.now() - start,
          error: 'Missing GitHub MCP token',
        };
      }

      // Simular teste de conectividade
      await this.simulateAPICall('github', 250);

      return {
        service: 'github',
        success: true,
        responseTime: Date.now() - start,
        details: {
          token: token.substring(0, 8) + '...',
        },
      };
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      return {
        service: 'github',
        success: false,
        responseTime: Date.now() - start,
        error: errorMessage,
      };
    }
  }

  /**
   * Simula uma chamada de API para teste
   */
  private async simulateAPICall(service: string, baseLatency: number): Promise<void> {
    // Simular latência variável
    const latency = baseLatency + Math.random() * 200;
    await new Promise(resolve => setTimeout(resolve, latency));

    // Simular falha ocasional (5% de chance)
    if (Math.random() < 0.05) {
      throw new Error(`Simulated ${service} API error`);
    }
  }

  /**
   * Extrai resultado de Promise.allSettled
   */
  private extractResult(result: PromiseSettledResult<MCPServiceResult>): MCPServiceResult {
    if (result.status === 'fulfilled') {
      return result.value;
    } else {
      const errorMessage = result.reason instanceof Error ? result.reason.message : 'Unknown error';
      return {
        service: 'unknown',
        success: false,
        responseTime: 0,
        error: errorMessage,
      };
    }
  }
}

// ============================================================================
// FACTORY FUNCTION
// ============================================================================

/**
 * Cria uma instância do MCP Health Check
 */
export function createMCPHealthCheck(): MCPHealthCheck {
  return new MCPHealthCheck();
}

// ============================================================================
// UTILITÁRIOS ESPECÍFICOS
// ============================================================================

/**
 * Verifica se alguma integração MCP está configurada
 */
export function isMCPConfigured(): boolean {
  // TODO: Implementar quando unifiedEnv estiver disponível
  // const mcpConfigs = unifiedEnv.getMCPConfig();
  // return Object.values(mcpConfigs).some(config => config.enabled);

  // Verificação temporária baseada em variáveis de ambiente
  const hasVercel = !!(process.env.MCP_VERCEL_TOKEN || process.env.MCP_VERCEL_TOKEN);
  const hasLinear = !!(process.env.MCP_LINEAR_API_KEY || process.env.MCP_LINEAR_API_KEY);
  const hasGithub = !!(process.env.MCP_GITHUB_TOKEN || process.env.MCP_GITHUB_TOKEN);

  return hasVercel || hasLinear || hasGithub;
}

/**
 * Obtém informações sobre as configurações MCP
 */
export function getMCPInfo() {
  // TODO: Implementar quando unifiedEnv estiver disponível
  // const mcpConfigs = unifiedEnv.getMCPConfig();

  // Implementação temporária baseada em variáveis de ambiente
  return {
    vercel: {
      enabled: !!(process.env.MCP_VERCEL_TOKEN || process.env.MCP_VERCEL_TOKEN),
      status: 'unknown',
      hasToken: !!(process.env.MCP_VERCEL_TOKEN || process.env.MCP_VERCEL_TOKEN),
      hasProjectId: !!(process.env.MCP_VERCEL_PROJECT_ID || process.env.MCP_VERCEL_PROJECT_ID),
      hasTeamId: !!(process.env.MCP_VERCEL_TEAM_ID || process.env.MCP_VERCEL_TEAM_ID),
    },
    linear: {
      enabled: !!(process.env.MCP_LINEAR_API_KEY || process.env.MCP_LINEAR_API_KEY),
      status: 'unknown',
      hasApiKey: !!(process.env.MCP_LINEAR_API_KEY || process.env.MCP_LINEAR_API_KEY),
    },
    github: {
      enabled: !!(process.env.MCP_GITHUB_TOKEN || process.env.MCP_GITHUB_TOKEN),
      status: 'unknown',
      hasToken: !!(process.env.MCP_GITHUB_TOKEN || process.env.MCP_GITHUB_TOKEN),
    },
  };
}

/**
 * Conta quantas integrações MCP estão habilitadas
 */
export function getEnabledMCPCount(): number {
  // TODO: Implementar quando unifiedEnv estiver disponível
  // const mcpConfigs = unifiedEnv.getMCPConfig();
  // return Object.values(mcpConfigs).filter(config => config.enabled).length;

  // Implementação temporária baseada em variáveis de ambiente
  const mcpInfo = getMCPInfo();
  return Object.values(mcpInfo).filter(config => config.enabled).length;
}
