# Configurações de Segurança para Produção - Excel Copilot

Este documento descreve as configurações de segurança implementadas no Excel Copilot para ambientes de produção.

## Sumário

- [Proteções de API](#proteções-de-api)
- [Segurança de Dados](#segurança-de-dados)
- [Backup e Recuperação](#backup-e-recuperação)
- [Monitoramento e Logging](#monitoramento-e-logging)
- [Checklist para Produção](#checklist-para-produção)

## Proteções de API

### Rate Limiting

O Excel Copilot implementa rate limiting em múltiplas camadas para proteger contra abusos:

#### Rate Limiting Geral

Implementado em `src/lib/middleware/rate-limiter.ts`, protege todas as APIs com limites específicos:

- APIs gerais: 200 requisições/minuto
- APIs de Excel: 100 requisições/minuto
- APIs de Chat: 50 requisições/minuto

#### Rate Limiting de Pagamentos

Proteção adicional para endpoints sensíveis em `src/lib/middleware/payment-limiter.ts`:

- APIs de Pagamento: 10 requisições/minuto em produção
- Bloqueio temporário após exceder limites (5 minutos)
- Detecção de padrões suspeitos de fraude
- Bloqueio de IPs com comportamento anômalo

### Autenticação Segura

- Tokens JWT com expiração curta (1 hora)
- Refresh tokens com rotação
- PKCE para fluxos OAuth
- WebAuthn/Passkeys suportado para autenticação sem senha
- Implementação de CSP (Content Security Policy) para prevenir XSS

### Prevenção de Ataques Comuns

- Proteção contra CSRF através de tokens específicos
- Validação de entrada usando Zod com schemas tipados
- Sanitização de queries SQL/NoSQL
- Prevenção de SSRF (Server-Side Request Forgery)
- Proteção contra ataques de timing através de comparações seguras

## Segurança de Dados

### Criptografia

- Dados sensíveis criptografados em repouso
- Conexões de banco de dados com TLS/SSL
- HTTPS obrigatório para todas as comunicações
- Certificados auto-renovados via Let's Encrypt

### Segurança de Banco de Dados

- Conexões com número limitado
- Acesso restrito por IP
- Credenciais com privilégios mínimos
- Queries parametrizadas para prevenir injeção

### PII (Informações Pessoalmente Identificáveis)

- Pseudonimização de dados quando possível
- Mascaramento de dados sensíveis em logs
- Consentimento explícito para coleta
- Mecanismos para remoção de dados (direito ao esquecimento)

## Backup e Recuperação

### Estratégia de Backup

O Excel Copilot implementa backups automatizados:

- **Backups Diários**: Retenção de 7 dias
- **Backups Semanais**: Retenção de 30 dias
- **Rotação Inteligente**: Um backup por mês para períodos mais antigos

Scripts implementados:

- `scripts/backup-database.js`: Realiza backup do banco de dados
- `scripts/clean-backups.js`: Gerencia a rotação de backups

Comandos disponíveis:

```bash
npm run db:backup        # Backup manual
npm run db:backup:daily  # Backup otimizado para execução diária
npm run db:backup:weekly # Backup semanal com retenção estendida
npm run db:backup:clean  # Limpa backups antigos conforme política
```

### Recuperação de Desastres

- RTO (Recovery Time Objective): 4 horas
- RPO (Recovery Point Objective): 24 horas
- Procedimento documentado de restauração
- Testes periódicos de restauração

## Monitoramento e Logging

### Logging Centralizado

- Logs estruturados em formato JSON
- Níveis de log configuráveis (produção: INFO+)
- Rotação de logs para evitar crescimento excessivo
- Mascaramento automático de dados sensíveis

### Alertas

- Alertas configurados para:
  - Falhas de backup
  - Tentativas excessivas de login
  - Padrões suspeitos de uso
  - Uso anormal de API
  - Erros críticos de sistema

### Métricas de Segurança

- Monitoramento de tentativas de login fracassadas
- Taxa de uso de API por usuário
- Detecção de anomalias de comportamento
- Tentativas de acesso a rotas protegidas

## Checklist para Produção

Antes de lançar em produção, verifique se:

### Configuração de Ambiente

- [ ] Variáveis de ambiente sensíveis configuradas corretamente
- [ ] Modo de produção ativado (NODE_ENV=production)
- [ ] Logs configurados para capturar eventos relevantes
- [ ] Limites de rate configurados apropriadamente

### Segurança de API

- [ ] Rate limiting testado e funcionando
- [ ] Validação de entrada implementada em todos os endpoints
- [ ] Autenticação e autorização verificadas
- [ ] Headers de segurança configurados (CORS, CSP, etc.)

### Banco de Dados

- [ ] Credenciais usando contas com privilégios mínimos
- [ ] Backups automatizados configurados
- [ ] Conexões SSL/TLS ativadas
- [ ] Índices criados para consultas críticas

### Backup e Recuperação

- [ ] Estratégia de backup implementada e testada
- [ ] Procedimento de recuperação documentado
- [ ] Armazenamento externo para backups configurado
- [ ] Processo de restauração validado

### Monitoramento

- [ ] Sistema de logs centralizado
- [ ] Alertas configurados para eventos críticos
- [ ] Métricas de desempenho e segurança visíveis
- [ ] Healthchecks implementados

## Referências Adicionais

- [Checklist de Segurança OWASP](https://owasp.org/www-project-application-security-verification-standard/)
- [Documentação do Next.js sobre Segurança](https://nextjs.org/docs/advanced-features/security-headers)
- [Guia de Segurança para Node.js](https://nodejs.org/en/docs/guides/security/)
