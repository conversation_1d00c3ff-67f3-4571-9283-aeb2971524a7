{"name": "Excel Copilot - Automatize suas planilhas com IA", "short_name": "Excel Copilot", "description": "Excel Copilot transforma comandos em linguagem natural em planilhas poderosas e análises complexas.", "theme_color": "#2563eb", "background_color": "#ffffff", "display": "standalone", "orientation": "portrait", "scope": "/", "id": "/", "start_url": "/", "lang": "pt-BR", "categories": ["productivity", "business", "utilities"], "dir": "ltr", "prefer_related_applications": false, "screenshots": [{"src": "/screenshots/dashboard.jpg", "sizes": "1280x720", "type": "image/jpeg", "platform": "web", "label": "Dashboard do Excel Copilot"}, {"src": "/screenshots/mobile-home.jpg", "sizes": "750x1334", "type": "image/jpeg", "platform": "web", "label": "Tela inicial em dispositivos móveis"}, {"src": "/images/chart-example.png", "sizes": "1280x720", "type": "image/png", "platform": "wide", "label": "Exemplo de gráfico no Excel Copilot"}], "icons": [{"src": "/icon.png", "sizes": "192x192", "type": "image/png", "purpose": "any maskable"}, {"src": "/icon-512.png", "sizes": "512x512", "type": "image/png", "purpose": "any maskable"}, {"src": "/maskable-icon-192.png", "sizes": "192x192", "type": "image/png", "purpose": "maskable"}, {"src": "/maskable-icon-512.png", "sizes": "512x512", "type": "image/png", "purpose": "maskable"}, {"src": "/favicon.ico", "sizes": "64x64 32x32 24x24 16x16", "type": "image/x-icon", "purpose": "any"}, {"src": "/apple-touch-icon.svg", "sizes": "180x180", "type": "image/svg+xml", "purpose": "any"}], "shortcuts": [{"name": "Dashboard", "short_name": "Dashboard", "description": "Acesse o dashboard principal", "url": "/dashboard", "icons": [{"src": "/shortcuts/dashboard.png", "sizes": "192x192"}]}, {"name": "Nova Planilha", "short_name": "Nova", "description": "Crie uma nova planilha", "url": "/dashboard/new", "icons": [{"src": "/shortcuts/new.png", "sizes": "192x192"}]}], "related_applications": [], "edge_side_panel": {"preferred_width": 480}, "share_target": {"action": "/upload", "method": "POST", "enctype": "multipart/form-data", "params": {"files": [{"name": "excelFile", "accept": [".xlsx", ".xls", ".csv"]}]}}, "handle_links": "preferred"}