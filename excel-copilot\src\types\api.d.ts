/**
 * Definições de tipos para dados de API
 */

// Tipos para dados do Excel e manipulação de planilhas
export interface WorkbookData {
  id?: string;
  name: string;
  description?: string | null;
  sheets: SheetData[];
}

export interface SheetData {
  name: string;
  data: string; // JSON serializado
  rows: RowData[];
}

export interface RowData {
  cells: CellData[];
}

export interface CellData {
  value?: any;
  formula?: string;
  style?: Record<string, any>;
}

// Tipos para Stripe
export interface StripeSubscription {
  id: string;
  current_period_start: number;
  current_period_end: number;
  status: string;
}

export interface StripeInvoice {
  id: string;
  subscription: string;
  payment_intent: string;
}

// Sessão do usuário estendida
export interface ExtendedUser {
  id: string;
  name?: string | null;
  email?: string | null;
  image?: string | null;
}

// Tipo para MediaQuery
export interface MediaQueryExtended {
  matches: boolean;
  addListener: (listener: (event: MediaQueryListEvent) => void) => void;
  removeListener: (listener: (event: MediaQueryListEvent) => void) => void;
}

export interface ApiResult<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  statusCode?: number;
  message?: string;
}
