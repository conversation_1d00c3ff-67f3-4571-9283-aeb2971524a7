'use client';

import { <PERSON>evronLeft, ChevronRight, MessageSquare, X, Sparkles, AlertCircle } from 'lucide-react';
import { memo } from 'react';

import { ChatInput } from '@/components/chat-interface/ChatInput';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import type { CommandInterpretation } from '@/hooks/useAIChat';

import { QuickCommands } from './QuickCommands';

interface AIPanelProps {
  // Panel state
  isCollapsed: boolean;
  onToggleCollapse: () => void;
  isMobileView: boolean;
  showMobileChat: boolean;
  onToggleMobileChat: () => void;

  // AI Chat state
  isProcessing: boolean;
  error: Error | null;
  pendingInterpretation: CommandInterpretation | null;

  // Actions
  onSendCommand: (command: string) => Promise<void>;
  onConfirmCommand: () => void;
  onCancelCommand: () => void;

  // Quick commands
  onQuickCommandClick: (command: string) => void;

  // Disabled state
  disabled?: boolean;
}

/**
 * Painel de IA otimizado para o SpreadsheetEditor
 * Memoizado para evitar re-renders desnecessários
 */
export const AIPanel = memo<AIPanelProps>(
  ({
    isCollapsed,
    onToggleCollapse,
    isMobileView,
    showMobileChat,
    onToggleMobileChat,
    isProcessing,
    error,
    pendingInterpretation,
    onSendCommand,
    onConfirmCommand,
    onCancelCommand,
    onQuickCommandClick,
    disabled = false,
  }) => {
    // Mobile view
    if (isMobileView) {
      return (
        <>
          {/* Mobile Chat Toggle Button */}
          <Button
            onClick={onToggleMobileChat}
            className="fixed bottom-4 right-4 z-50 rounded-full shadow-lg"
            size="lg"
          >
            <MessageSquare className="h-5 w-5" />
          </Button>

          {/* Mobile Chat Overlay */}
          {showMobileChat && (
            <div className="fixed inset-0 z-40 bg-background/80 backdrop-blur-sm">
              <div className="fixed bottom-0 left-0 right-0 bg-background border-t rounded-t-lg max-h-[80vh] flex flex-col">
                {/* Header */}
                <div className="flex items-center justify-between p-4 border-b">
                  <div className="flex items-center gap-2">
                    <Sparkles className="h-5 w-5 text-primary" />
                    <span className="font-medium">Assistente IA</span>
                  </div>
                  <Button variant="ghost" size="sm" onClick={onToggleMobileChat}>
                    <X className="h-4 w-4" />
                  </Button>
                </div>

                {/* Content */}
                <div className="flex-1 overflow-hidden">
                  <ScrollArea className="h-full p-4">
                    <QuickCommands
                      onCommandClick={onQuickCommandClick}
                      disabled={disabled || isProcessing}
                    />
                  </ScrollArea>
                </div>

                {/* Input */}
                <div className="p-4 border-t">
                  <ChatInput
                    onSendMessage={onSendCommand}
                    disabled={disabled || isProcessing}
                    placeholder="Digite um comando..."
                  />
                </div>
              </div>
            </div>
          )}
        </>
      );
    }

    // Desktop view
    return (
      <div
        className={`bg-background border-l transition-all duration-300 ${
          isCollapsed ? 'w-12' : 'w-80'
        }`}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-3 border-b">
          {!isCollapsed && (
            <div className="flex items-center gap-2">
              <Sparkles className="h-5 w-5 text-primary" />
              <span className="font-medium">Assistente IA</span>
            </div>
          )}
          <Button
            variant="ghost"
            size="sm"
            onClick={onToggleCollapse}
            title={isCollapsed ? 'Expandir painel' : 'Recolher painel'}
          >
            {isCollapsed ? (
              <ChevronLeft className="h-4 w-4" />
            ) : (
              <ChevronRight className="h-4 w-4" />
            )}
          </Button>
        </div>

        {/* Content */}
        {!isCollapsed && (
          <div className="flex flex-col h-full">
            {/* Error Display */}
            {error && (
              <div className="p-3 bg-destructive/10 border-b">
                <div className="flex items-start gap-2">
                  <AlertCircle className="h-4 w-4 text-destructive mt-0.5" />
                  <div className="text-sm text-destructive">{error.message}</div>
                </div>
              </div>
            )}

            {/* Pending Command */}
            {pendingInterpretation && (
              <div className="p-3 bg-amber-50 border-b">
                <div className="text-sm font-medium mb-2">Comando interpretado:</div>
                <div className="text-sm text-muted-foreground mb-3">
                  {pendingInterpretation.interpretation}
                </div>
                <div className="flex gap-2">
                  <Button size="sm" onClick={onConfirmCommand} disabled={isProcessing}>
                    Confirmar
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={onCancelCommand}
                    disabled={isProcessing}
                  >
                    Cancelar
                  </Button>
                </div>
              </div>
            )}

            {/* Quick Commands */}
            <div className="flex-1 overflow-hidden">
              <ScrollArea className="h-full p-3">
                <QuickCommands
                  onCommandClick={onQuickCommandClick}
                  disabled={disabled || isProcessing}
                />
              </ScrollArea>
            </div>

            {/* Chat Input */}
            <div className="p-3 border-t">
              <ChatInput
                onSendMessage={onSendCommand}
                disabled={disabled || isProcessing}
                placeholder="Digite um comando..."
              />
            </div>
          </div>
        )}
      </div>
    );
  }
);

AIPanel.displayName = 'AIPanel';
