'use client';

import { motion } from 'framer-motion';
import { ReactNode } from 'react';

import { cn } from '@/lib/utils';

export interface TabButtonProps {
  active: boolean;
  onClick: () => void;
  icon?: ReactNode;
  label: string;
  animated?: boolean;
}

export function TabButton({ active, onClick, icon, label, animated = true }: TabButtonProps) {
  const baseStyles = cn(
    'flex items-center justify-center px-6 py-4 border-0 border-b-2 transition-all duration-200 flex-1 outline-none',
    active
      ? 'border-primary text-primary font-semibold'
      : 'border-transparent text-muted-foreground font-medium hover:text-foreground hover:bg-accent/50'
  );

  if (animated) {
    return (
      <motion.button
        onClick={() => onClick()}
        className={baseStyles}
        whileTap={{ scale: 0.98 }}
        transition={{ duration: 0.1 }}
      >
        {icon && <span className="mr-2">{icon}</span>}
        <span>{label}</span>
        {active && (
          <motion.div
            className="absolute bottom-0 left-0 right-0 h-0.5 bg-primary"
            layoutId="activeTab"
            transition={{ type: 'spring', stiffness: 300, damping: 30 }}
          />
        )}
      </motion.button>
    );
  }

  return (
    <button onClick={() => onClick()} className={baseStyles}>
      {icon && <span className="mr-2">{icon}</span>}
      <span>{label}</span>
    </button>
  );
}
