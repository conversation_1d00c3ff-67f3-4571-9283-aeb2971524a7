import { EventEmitter } from 'events';

import { type Socket, io } from 'socket.io-client';
import { v4 as uuidv4 } from 'uuid';
import { create } from 'zustand';

import { safeConsoleLog } from '@/lib/logger';

// Define tipos para colaboradores e presença
export interface Collaborator {
  id: string;
  name: string;
  color: string;
  cursor?: {
    x: number;
    y: number;
    sheetId: string;
  };
  lastActivity: Date;
}

export interface CellEdit {
  id: string;
  sheetId: string;
  cellId: string;
  value: unknown;
  timestamp: Date;
  userId: string;
  userName: string;
}

export interface CollabState {
  // Estado
  connected: boolean;
  collaborators: Record<string, Collaborator>;
  pendingEdits: CellEdit[];
  socket: Socket | null;
  workbookId: string | null;
  userId: string;
  userName: string;
  userColor: string;

  // Ações
  connect: (workbookId: string, userId: string, userName: string) => void;
  disconnect: () => void;
  updateCursor: (x: number, y: number, sheetId: string) => void;
  submitCellEdit: (sheetId: string, cellId: string, value: unknown) => void;
  acknowledgeEdit: (editId: string) => void;
}

// Paleta de cores para usuários
const USER_COLORS = [
  '#EF4444',
  '#F59E0B',
  '#10B981',
  '#3B82F6',
  '#8B5CF6',
  '#EC4899',
  '#14B8A6',
  '#F97316',
  '#6366F1',
  '#D946EF',
];

// Gera uma cor aleatória para um novo usuário
const getRandomColor = (): string => {
  if (!USER_COLORS || USER_COLORS.length === 0) {
    return '#3B82F6'; // Cor padrão caso não haja cores disponíveis
  }
  const colorIndex = Math.min(
    Math.floor(Math.random() * USER_COLORS.length),
    USER_COLORS.length - 1
  );
  return USER_COLORS[colorIndex] || '#3B82F6'; // Fallback caso o índice resulte em undefined
};

// Store Zustand para gerenciar o estado de colaboração
export const useCollabStore = create<CollabState>((set, get) => ({
  connected: false,
  collaborators: {},
  pendingEdits: [],
  socket: null,
  workbookId: null,
  userId: '',
  userName: '',
  userColor: getRandomColor(),

  connect: (workbookId, userId, userName) => {
    const socket = io(process.env.NEXT_PUBLIC_SOCKET_URL || 'http://localhost:3001', {
      query: {
        workbookId,
        userId,
        userName,
        userColor: get().userColor,
      },
    });

    socket.on('connect', () => {
      set({
        connected: true,
        workbookId,
        userId,
        userName,
      });
      safeConsoleLog('Conectado à sessão colaborativa');
    });

    socket.on('disconnect', () => {
      set({ connected: false });
      safeConsoleLog('Desconectado da sessão colaborativa');
    });

    socket.on('presence_update', (collaborators: Record<string, Collaborator>) => {
      set({ collaborators });
    });

    socket.on('cell_edit', (edit: CellEdit) => {
      set(state => ({
        pendingEdits: [...state.pendingEdits, edit],
      }));
    });

    socket.on('edit_acknowledged', (editId: string) => {
      get().acknowledgeEdit(editId);
    });

    set({ socket });
  },

  disconnect: () => {
    const { socket } = get();
    if (socket) {
      socket.disconnect();
      set({
        socket: null,
        connected: false,
        collaborators: {},
        pendingEdits: [],
      });
    }
  },

  updateCursor: (x, y, sheetId) => {
    const { socket, userId } = get();
    if (socket && socket.connected) {
      socket.emit('cursor_update', { x, y, sheetId, userId });
    }
  },

  submitCellEdit: (sheetId, cellId, value) => {
    const { socket, userId, userName } = get();
    const editId = uuidv4();

    if (socket && socket.connected) {
      const edit: CellEdit = {
        id: editId,
        sheetId,
        cellId,
        value,
        timestamp: new Date(),
        userId,
        userName,
      };

      socket.emit('cell_edit', edit);

      // Adicionar à lista de edições pendentes
      set(state => ({
        pendingEdits: [...state.pendingEdits, edit],
      }));
    }
  },

  acknowledgeEdit: (editId: string) => {
    set(state => ({
      pendingEdits: state.pendingEdits.filter(edit => edit.id !== editId),
    }));
  },
}));

export class CollaborativeSyncManager {
  private eventEmitter: EventEmitter;
  private connected: boolean = false;
  private documentId: string | null = null;
  private userId: string | null = null;
  private socket: WebSocket | null = null;
  private reconnectAttempts: number = 0;
  private maxReconnectAttempts: number = 5;
  private reconnectTimeout: number = 1000;

  constructor() {
    this.eventEmitter = new EventEmitter();
  }

  // ... resto do código existente
}
