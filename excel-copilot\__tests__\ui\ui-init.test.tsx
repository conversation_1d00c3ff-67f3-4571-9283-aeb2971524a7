/**
 * @jest-environment jsdom
 */

import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { ThemeProvider } from 'next-themes';
import { SessionProvider } from 'next-auth/react';
import * as nextRouter from 'next/router';

// Componentes principais para testar renderização
import HomePage from '@/app/page';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Input, ThemeToggle } from '@/components/ui';

// Mock para trpc
jest.mock('@/server/trpc/react', () => ({
  TRPCReactProvider: ({ children }: { children: React.ReactNode }) => <>{children}</>,
}));

// Mock para o router do Next.js
jest.mock('next/router', () => ({
  useRouter: jest.fn(),
}));

// Mock para o ScrollArea do Radix UI
jest.mock('@radix-ui/react-scroll-area', () => ({
  ScrollArea: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  ScrollBar: () => <div data-testid="scrollbar" />,
}));

// Setup para ambiente Next.js
beforeAll(() => {
  // Mock useRouter
  (nextRouter.useRouter as jest.Mock).mockImplementation(() => ({
    pathname: '/',
    query: {},
    push: jest.fn(),
    events: {
      on: jest.fn(),
      off: jest.fn(),
    },
    asPath: '/',
  }));

  // Mock IntersectionObserver
  const mockIntersectionObserver = jest.fn();
  mockIntersectionObserver.mockReturnValue({
    observe: jest.fn(),
    unobserve: jest.fn(),
    disconnect: jest.fn(),
  });
  window.IntersectionObserver = mockIntersectionObserver;
});

// Wrapper para testes de componentes
const AllProviders = ({ children }: { children: React.ReactNode }) => {
  return (
    <SessionProvider session={null}>
      <ThemeProvider defaultTheme="light" enableSystem={true}>
        {children}
      </ThemeProvider>
    </SessionProvider>
  );
};

describe('Inicialização da Interface do Excel Copilot', () => {
  test('Componentes de UI básicos são renderizados corretamente', () => {
    render(
      <AllProviders>
        <div>
          <Button data-testid="test-button">Botão de Teste</Button>
          <Card data-testid="test-card">
            <div>Conteúdo do Card</div>
          </Card>
          <Input data-testid="test-input" placeholder="Input de teste" />
        </div>
      </AllProviders>
    );

    // Verificar se os componentes foram renderizados
    expect(screen.getByTestId('test-button')).toBeInTheDocument();
    expect(screen.getByTestId('test-card')).toBeInTheDocument();
    expect(screen.getByTestId('test-input')).toBeInTheDocument();

    // Verificar texto e atributos
    expect(screen.getByText('Botão de Teste')).toBeInTheDocument();
    expect(screen.getByText('Conteúdo do Card')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Input de teste')).toBeInTheDocument();
  });

  test('ThemeProvider inicializa corretamente', async () => {
    render(
      <AllProviders>
        <ThemeToggle />
      </AllProviders>
    );

    // Verificar se o botão de toggle de tema está presente
    const themeButton = screen.getByRole('button');
    expect(themeButton).toBeInTheDocument();

    // O tema padrão deve ser 'light'
    expect(document.documentElement.classList.contains('light')).toBeTruthy();
  });

  test('CSS global é carregado corretamente', () => {
    // Verificar se o estilo global está sendo aplicado verificando
    // propriedades CSS que sabemos que estão definidas no Tailwind

    // Criar um container e renderizar
    render(
      <AllProviders>
        <div data-testid="css-test" className="bg-background text-foreground p-4 rounded-md">
          Teste de CSS
        </div>
      </AllProviders>
    );

    const element = screen.getByTestId('css-test');

    // Em um ambiente de teste, as classes do Tailwind são aplicadas mas não processadas
    // Então verificamos se as classes foram aplicadas corretamente
    expect(element.classList.contains('bg-background')).toBeTruthy();
    expect(element.classList.contains('text-foreground')).toBeTruthy();
    expect(element.classList.contains('p-4')).toBeTruthy();
    expect(element.classList.contains('rounded-md')).toBeTruthy();
  });

  test('Componentes Radix UI são renderizados adequadamente', () => {
    render(
      <AllProviders>
        <div data-testid="radix-container">
          {/* O ScrollArea é mockado acima */}
          <div data-testid="scrollbar" />
        </div>
      </AllProviders>
    );

    expect(screen.getByTestId('radix-container')).toBeInTheDocument();
    expect(screen.getByTestId('scrollbar')).toBeInTheDocument();
  });
});

// Teste específico para a página inicial
describe('Inicialização da HomePage', () => {
  // Mock específico para a HomePage
  beforeEach(() => {
    // Mock para o hook trpc usado na HomePage
    jest.mock('@/lib/trpc', () => ({
      trpc: {
        getWorkbooks: {
          useQuery: () => ({
            data: [],
            isLoading: false,
            error: null,
          }),
        },
      },
    }));
  });

  test('HomePage renderiza sem erros', async () => {
    // Tentar renderizar a HomePage
    // Devido a complexidade da HomePage, pode ser necessário mais mocks
    try {
      render(
        <AllProviders>
          <HomePage />
        </AllProviders>
      );

      // Testes básicos que não dependem de implementação específica
      await waitFor(() => {
        expect(document.body).toBeInTheDocument();
      });

      // Se chegamos aqui sem erros, a renderização foi bem-sucedida
    } catch (error) {
      // Se houver erro, o teste falha, mas capturamos para diagnóstico
      console.error('Erro ao renderizar HomePage:', error);
      throw error;
    }
  });
});
