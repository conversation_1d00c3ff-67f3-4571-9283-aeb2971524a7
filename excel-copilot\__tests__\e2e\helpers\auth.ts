import { Page } from '@playwright/test';

/**
 * Helper para fazer login nos testes E2E
 *
 * @param page Instância da página do Playwright
 * @param options Opções de login (email e outras configurações)
 */
export async function login(page: Page, options = { email: '<EMAIL>' }) {
  // Navegar para a página de login
  await page.goto('/auth/signin');

  // Verificar qual método de login está sendo usado no ambiente de teste
  const googleLoginButton = page.getByRole('button', { name: /Continuar com Google/i });
  const githubLoginButton = page.getByRole('button', { name: /Continuar com GitHub/i });
  const guestLoginButton = page.getByRole('button', { name: /Continuar como convidado/i });

  // Em ambiente de teste, usar login como convidado que deve estar disponível
  if (await guestLoginButton.isVisible()) {
    await guestLoginButton.click();
    // Aguardar redirecionamento após login
    await page.waitForURL('/dashboard');
    return;
  }

  // Se login como convidado não estiver disponível, tentar os outros métodos
  if (await googleLoginButton.isVisible()) {
    console.log(
      'Login com Google detectado. Em ambiente de teste, recomenda-se habilitar o login como convidado.'
    );
    await googleLoginButton.click();
    // Aqui teríamos que simular o fluxo de autenticação do Google, o que é mais complexo
    // Por simplicidade, vamos assumir que o teste está configurado para simular este fluxo
    await page.waitForURL('/dashboard', { timeout: 10000 });
    return;
  }

  if (await githubLoginButton.isVisible()) {
    console.log(
      'Login com GitHub detectado. Em ambiente de teste, recomenda-se habilitar o login como convidado.'
    );
    await githubLoginButton.click();
    // Similarmente, a simulação completa do fluxo GitHub exigiria mais configuração
    await page.waitForURL('/dashboard', { timeout: 10000 });
    return;
  }

  // Se nenhum método for detectado, vamos reportar erro
  throw new Error(
    'Nenhum método de login foi detectado na página. Verifique a configuração de autenticação para testes.'
  );
}
