import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { UploadButton } from '@/components/upload-button';

// Mock para o fetch
global.fetch = jest.fn().mockImplementation(() =>
  Promise.resolve({
    ok: true,
    json: () =>
      Promise.resolve({
        success: true,
        workbookId: 'test-id',
        message: 'Arquivo importado com sucesso',
      }),
  })
) as jest.Mock;

// Mock para a função onUpload
const mockOnUpload = jest.fn();

// Mock para o hook useRouter
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
  }),
}));

// Mock para o hook useExcelFile
jest.mock('@/hooks/useExcelFile', () => ({
  useExcelFile: () => ({
    importExcel: jest.fn(),
    isLoading: false,
  }),
}));

// Criar um arquivo mock
const createMockFile = (name: string, type: string, size: number) => {
  const file = new File(['test content'], name, { type });
  Object.defineProperty(file, 'size', {
    get() {
      return size;
    },
  });
  return file;
};

describe('UploadButton Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renderiza o botão de upload', () => {
    render(<UploadButton onUpload={mockOnUpload} />);
    const uploadButton = screen.getByText(/Importar Excel/i);
    expect(uploadButton).toBeInTheDocument();
  });

  test('exibe ícone de upload', () => {
    render(<UploadButton onUpload={mockOnUpload} />);
    const uploadIcon = screen.getByText(/Importar Excel/i);
    expect(uploadIcon).toBeInTheDocument();
  });

  test('exibe informações sobre formatos suportados', () => {
    render(<UploadButton onUpload={mockOnUpload} />);

    expect(screen.getByText(/Suporte a formatos:/i)).toBeInTheDocument();
    expect(screen.getByText(/\.xlsx - Excel moderno/i)).toBeInTheDocument();
    expect(screen.getByText(/\.xls - Excel legado/i)).toBeInTheDocument();
    expect(screen.getByText(/\.csv - Valores separados por vírgula/i)).toBeInTheDocument();
  });
});
