const { exec } = require('child_process');
const fs = require('fs');
const path = require('path');

// Expressão regular para encontrar problemas com 'any'
const ANY_TYPE_WARNING = /Unexpected any\. Specify a different type\./;

// Processa o resultado do ESLint para encontrar arquivos com uso de 'any'
function processLintOutput(output) {
  const fileIssues = {};

  // Extrair informações do output
  const lines = output.split('\n');
  let currentFile = null;

  for (const line of lines) {
    // Verifica se é uma linha de arquivo
    if (line.startsWith('./')) {
      currentFile = line.split(':')[0].substring(2); // Remove './'
    } else if (currentFile && line.includes('Warning:') && line.includes('Unexpected any')) {
      // Extrai o número da linha e coluna
      const match = line.match(/(\d+):(\d+)\s+Warning:/);
      if (match) {
        const [_, lineNum, colNum] = match;

        if (!fileIssues[currentFile]) {
          fileIssues[currentFile] = [];
        }

        fileIssues[currentFile].push({
          line: parseInt(lineNum),
          column: parseInt(colNum),
        });
      }
    }
  }

  return fileIssues;
}

// Corrige os tipos 'any' substituindo por 'unknown'
function fixAnyTypes(fileIssues) {
  for (const [filePath, issues] of Object.entries(fileIssues)) {
    try {
      const fullPath = path.join(process.cwd(), filePath);
      const content = fs.readFileSync(fullPath, 'utf8');
      const lines = content.split('\n');

      // Ordenar problemas por linha em ordem decrescente
      issues.sort((a, b) => b.line - a.line);

      let modified = false;

      for (const issue of issues) {
        const line = lines[issue.line - 1];

        // Substituir 'any' por 'unknown'
        // Cuidado com substituições que podem quebrar a funcionalidade
        // Esta é uma substituição simples que funciona em muitos casos
        const newLine = line.replace(/\bany\b/g, 'unknown');

        if (newLine !== line) {
          lines[issue.line - 1] = newLine;
          modified = true;
        }
      }

      if (modified) {
        fs.writeFileSync(fullPath, lines.join('\n'), 'utf8');
        console.log(`✓ Corrigido: ${filePath}`);
      }
    } catch (error) {
      console.error(`Erro ao processar ${filePath}:`, error.message);
    }
  }
}

// Executa o lint e processa os resultados
exec('npm run lint', (error, stdout, stderr) => {
  if (error) {
    console.error(`Erro ao executar lint: ${error.message}`);
    return;
  }

  console.log('Processando resultados do lint para substituir any por unknown...');
  const fileIssues = processLintOutput(stdout);

  console.log(`Encontrados 'any' em ${Object.keys(fileIssues).length} arquivos.`);
  fixAnyTypes(fileIssues);

  console.log('Concluído! Execute npm run lint novamente para verificar as correções.');
});
