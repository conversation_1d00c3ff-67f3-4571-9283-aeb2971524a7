import { NextRequest } from 'next/server';

import { authMiddleware } from '@/middleware/auth';
import { withMiddleware } from '@/middleware/core';
import { getApiMetrics } from '@/middleware/metrics';
import { getDatabaseMetrics } from '@/server/db/client';
import { getQueryCacheStats } from '@/server/db/query-cache';
import { ApiResponse } from '@/utils/api-response';

/**
 * GET /api/metrics - Obtém métricas de API e banco de dados
 * Endpoint protegido, apenas para administradores
 */

// Configurar rota como dinâmica para permitir uso de headers em runtime
export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

export const GET = withMiddleware([authMiddleware], async (req: NextRequest, ctx) => {
  try {
    // Verificar se o usuário é administrador
    // Em um ambiente real, isso deveria verificar se o usuário tem a role 'admin'
    // Por ora, consideramos administrador se o email estiver na lista de admins
    const adminEmails = process.env.ADMIN_EMAILS?.split(',') || [];
    const isAdmin = adminEmails.includes(ctx.user?.email || '');

    if (!isAdmin) {
      return ApiResponse.forbidden('Apenas administradores podem acessar métricas');
    }

    // Obter métricas
    const apiMetrics = getApiMetrics();
    const dbMetrics = getDatabaseMetrics();
    const cacheMetrics = getQueryCacheStats();

    return ApiResponse.success({
      api: apiMetrics,
      database: dbMetrics,
      cache: cacheMetrics,
      server: {
        memory: process.memoryUsage(),
        uptime: process.uptime(),
        timestamp: new Date().toISOString(),
      },
    });
  } catch (error) {
    if (error instanceof Error) {
      return ApiResponse.error(error.message);
    }
    return ApiResponse.error('Erro ao buscar métricas');
  }
});
