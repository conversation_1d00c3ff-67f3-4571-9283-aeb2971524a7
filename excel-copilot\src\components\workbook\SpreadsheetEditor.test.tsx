import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi, describe, it, expect, beforeEach } from 'vitest';

import { SpreadsheetEditorRefactored } from './SpreadsheetEditorRefactored';
import { SpreadsheetData } from './SpreadsheetContext';

// Mock dos hooks externos
vi.mock('@/hooks/useAIChat', () => ({
  useAIChat: () => ({
    sendMessage: vi.fn(),
    isProcessing: false,
    messages: [],
    error: null,
  }),
}));

vi.mock('@/hooks/useDesktopBridge', () => ({
  useDesktopBridge: () => ({}),
}));

vi.mock('@/hooks/useExcelOperations', () => ({
  useExcelOperations: () => ({
    processExcelCommand: vi.fn(),
    isProcessing: false,
    lastModifiedCells: [],
  }),
}));

vi.mock('@/hooks/useWorkbookRealtime', () => ({
  useWorkbookRealtime: () => ({
    isConnected: false,
    updateCursor: vi.fn(),
    broadcastCellChange: vi.fn(),
  }),
}));

// Mock do router
vi.mock('next/navigation', () => ({
  useRouter: () => ({
    push: vi.fn(),
  }),
}));

// Mock do sonner
vi.mock('sonner', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
    info: vi.fn(),
  },
}));

describe('SpreadsheetEditorRefactored', () => {
  const mockProps = {
    workbookId: 'test-workbook-123',
    readOnly: false,
  };

  const mockInitialData: SpreadsheetData = {
    headers: ['Nome', 'Idade', 'Cidade'],
    rows: [
      ['João', '25', 'São Paulo'],
      ['Maria', '30', 'Rio de Janeiro'],
      ['Pedro', '35', 'Belo Horizonte'],
    ],
    charts: [],
    name: 'Planilha de Teste',
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('deve renderizar o componente corretamente', () => {
    render(<SpreadsheetEditorRefactored {...mockProps} />);
    
    // Verificar se a toolbar está presente
    expect(screen.getByText('Dashboard')).toBeInTheDocument();
    expect(screen.getByText('Nova Planilha')).toBeInTheDocument();
  });

  it('deve renderizar com dados iniciais', () => {
    render(
      <SpreadsheetEditorRefactored 
        {...mockProps} 
        initialData={mockInitialData} 
      />
    );
    
    // Verificar se o nome da planilha está correto
    expect(screen.getByText('Planilha de Teste')).toBeInTheDocument();
  });

  it('deve mostrar botões de undo/redo desabilitados inicialmente', () => {
    render(<SpreadsheetEditorRefactored {...mockProps} />);
    
    // Botões de undo/redo devem estar desabilitados no início
    const undoButton = screen.getByRole('button', { name: /desfazer/i });
    const redoButton = screen.getByRole('button', { name: /refazer/i });
    
    expect(undoButton).toBeDisabled();
    expect(redoButton).toBeDisabled();
  });

  it('deve mostrar painel de IA por padrão em desktop', () => {
    // Mock para simular desktop
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 1024,
    });

    render(<SpreadsheetEditorRefactored {...mockProps} />);
    
    // Verificar se o painel de IA está presente
    expect(screen.getByText('Assistente IA')).toBeInTheDocument();
  });

  it('deve alternar painel de IA quando clicado', async () => {
    render(<SpreadsheetEditorRefactored {...mockProps} />);
    
    // Encontrar e clicar no botão de alternar IA
    const toggleButton = screen.getByText(/ocultar ia|mostrar ia/i);
    fireEvent.click(toggleButton);
    
    // Aguardar mudança de estado
    await waitFor(() => {
      expect(toggleButton.textContent).toMatch(/mostrar ia|ocultar ia/i);
    });
  });

  it('deve funcionar em modo somente leitura', () => {
    render(
      <SpreadsheetEditorRefactored 
        {...mockProps} 
        readOnly={true}
        initialData={mockInitialData}
      />
    );
    
    // Verificar se o botão de salvar está desabilitado
    const saveButton = screen.getByRole('button', { name: /salvar/i });
    expect(saveButton).toBeDisabled();
  });

  it('deve mostrar interface mobile em telas pequenas', () => {
    // Mock para simular mobile
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 600,
    });

    render(<SpreadsheetEditorRefactored {...mockProps} />);
    
    // Disparar evento de resize
    fireEvent(window, new Event('resize'));
    
    // Verificar se o botão flutuante de chat está presente
    const chatButton = screen.getByRole('button');
    expect(chatButton).toBeInTheDocument();
  });

  it('deve chamar onSave quando fornecido', async () => {
    const mockOnSave = vi.fn().mockResolvedValue(undefined);
    
    render(
      <SpreadsheetEditorRefactored 
        {...mockProps} 
        onSave={mockOnSave}
        initialData={mockInitialData}
      />
    );
    
    // Clicar no botão de salvar
    const saveButton = screen.getByRole('button', { name: /salvar/i });
    fireEvent.click(saveButton);
    
    // Verificar se onSave foi chamado
    await waitFor(() => {
      expect(mockOnSave).toHaveBeenCalledWith(mockInitialData);
    });
  });

  it('deve processar comando inicial se fornecido', () => {
    const initialCommand = 'Adicione uma coluna Total';
    
    render(
      <SpreadsheetEditorRefactored 
        {...mockProps} 
        initialCommand={initialCommand}
        initialData={mockInitialData}
      />
    );
    
    // O comando deve ser processado automaticamente
    // (verificação seria feita através de mocks dos hooks)
    expect(screen.getByText('Planilha de Teste')).toBeInTheDocument();
  });

  it('deve abrir modal de atalhos quando solicitado', async () => {
    render(<SpreadsheetEditorRefactored {...mockProps} />);
    
    // Simular tecla ? para abrir atalhos
    fireEvent.keyDown(document, { key: '?', shiftKey: true });
    
    // Verificar se o modal de atalhos abre
    await waitFor(() => {
      expect(screen.getByText('Atalhos de Teclado')).toBeInTheDocument();
    });
  });

  it('deve navegar para dashboard quando clicado', () => {
    render(<SpreadsheetEditorRefactored {...mockProps} />);
    
    // Clicar no botão de dashboard
    const dashboardButton = screen.getByText('Dashboard');
    fireEvent.click(dashboardButton);
    
    // Verificação seria feita através de mock do router
    expect(dashboardButton).toBeInTheDocument();
  });
});

// Testes dos hooks individuais
describe('SpreadsheetContext', () => {
  it('deve fornecer estado inicial correto', () => {
    // Teste seria implementado com renderHook do testing-library
    expect(true).toBe(true); // Placeholder
  });

  it('deve atualizar estado quando ações são chamadas', () => {
    // Teste seria implementado com renderHook do testing-library
    expect(true).toBe(true); // Placeholder
  });
});

describe('useSpreadsheetData', () => {
  it('deve gerenciar dados da planilha corretamente', () => {
    // Teste seria implementado com renderHook do testing-library
    expect(true).toBe(true); // Placeholder
  });

  it('deve integrar com hooks externos', () => {
    // Teste seria implementado com renderHook do testing-library
    expect(true).toBe(true); // Placeholder
  });
});

describe('useSpreadsheetUI', () => {
  it('deve gerenciar estados de UI corretamente', () => {
    // Teste seria implementado com renderHook do testing-library
    expect(true).toBe(true); // Placeholder
  });

  it('deve detectar mobile/desktop corretamente', () => {
    // Teste seria implementado com renderHook do testing-library
    expect(true).toBe(true); // Placeholder
  });
});

describe('useSpreadsheetKeyboard', () => {
  it('deve registrar atalhos de teclado corretamente', () => {
    // Teste seria implementado com renderHook do testing-library
    expect(true).toBe(true); // Placeholder
  });

  it('deve executar ações quando atalhos são pressionados', () => {
    // Teste seria implementado com renderHook do testing-library
    expect(true).toBe(true); // Placeholder
  });
});
