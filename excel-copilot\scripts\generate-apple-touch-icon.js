#!/usr/bin/env node

/**
 * Script para gerar apple-touch-icon.png a partir do SVG
 * Resolve o erro de manifest.json em produção
 */

const fs = require('fs');
const path = require('path');

// Cores para output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m',
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

async function generateAppleTouchIcon() {
  log('\n🍎 Gerando apple-touch-icon.png...', 'blue');

  const publicDir = path.resolve(__dirname, '..', 'public');
  const svgPath = path.join(publicDir, 'apple-touch-icon.svg');
  const pngPath = path.join(publicDir, 'apple-touch-icon.png');

  // Verificar se o SVG existe
  if (!fs.existsSync(svgPath)) {
    log('❌ Arquivo apple-touch-icon.svg não encontrado!', 'red');
    return false;
  }

  // Ler o conteúdo do SVG
  const svgContent = fs.readFileSync(svgPath, 'utf8');

  // Verificar se é um SVG válido
  if (!svgContent.includes('<svg')) {
    log('❌ Arquivo apple-touch-icon.svg não é um SVG válido!', 'red');
    return false;
  }

  log('✅ SVG válido encontrado', 'green');

  // Como não podemos gerar PNG diretamente, vamos criar instruções
  log('\n📋 Para gerar o PNG, execute um dos seguintes comandos:', 'yellow');
  log('');
  log('Opção 1 - Usando ImageMagick:', 'blue');
  log('convert public/apple-touch-icon.svg public/apple-touch-icon.png', 'yellow');
  log('');
  log('Opção 2 - Usando Inkscape:', 'blue');
  log(
    'inkscape --export-png=public/apple-touch-icon.png --export-width=180 --export-height=180 public/apple-touch-icon.svg',
    'yellow'
  );
  log('');
  log('Opção 3 - Usando Node.js com sharp (recomendado):', 'blue');
  log('npm install sharp', 'yellow');
  log(
    "node -e \"const sharp = require('sharp'); sharp('public/apple-touch-icon.svg').png().resize(180, 180).toFile('public/apple-touch-icon.png').then(() => console.log('PNG gerado com sucesso!'));\"",
    'yellow'
  );

  // Criar um PNG placeholder temporário usando base64
  const pngPlaceholder = createPngPlaceholder();
  fs.writeFileSync(pngPath, pngPlaceholder, 'base64');

  log('\n✅ PNG placeholder criado temporariamente', 'green');
  log('⚠️  Substitua por um PNG real usando um dos comandos acima', 'yellow');

  return true;
}

function createPngPlaceholder() {
  // PNG mínimo válido de 1x1 pixel transparente em base64
  return 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77zgAAAABJRU5ErkJggg==';
}

function validateManifestIcons() {
  log('\n🔍 Validando ícones do manifest.json...', 'blue');

  const manifestPath = path.resolve(__dirname, '..', 'public', 'manifest.json');
  const publicDir = path.resolve(__dirname, '..', 'public');

  if (!fs.existsSync(manifestPath)) {
    log('❌ manifest.json não encontrado!', 'red');
    return false;
  }

  const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'));
  let allIconsExist = true;

  if (manifest.icons) {
    for (const icon of manifest.icons) {
      const iconPath = path.join(publicDir, icon.src);
      if (!fs.existsSync(iconPath)) {
        log(`❌ Ícone não encontrado: ${icon.src}`, 'red');
        allIconsExist = false;
      } else {
        log(`✅ Ícone encontrado: ${icon.src}`, 'green');
      }
    }
  }

  return allIconsExist;
}

async function main() {
  log(`${colors.bold}🔧 Correção do Erro de Manifest - Apple Touch Icon${colors.reset}`, 'blue');

  try {
    await generateAppleTouchIcon();
    validateManifestIcons();

    log('\n✅ Processo concluído!', 'green');
    log('📝 Próximos passos:', 'blue');
    log('1. Execute um dos comandos para gerar o PNG real', 'yellow');
    log('2. Teste o manifest.json em produção', 'yellow');
    log('3. Verifique se o erro do console foi resolvido', 'yellow');
  } catch (error) {
    log(`❌ Erro durante o processo: ${error.message}`, 'red');
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { generateAppleTouchIcon, validateManifestIcons };
