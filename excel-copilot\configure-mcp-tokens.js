#!/usr/bin/env node

/**
 * Script para configurar tokens das integrações MCP
 * Automatiza a configuração do Linear e GitHub MCP
 */

// eslint-disable-next-line @typescript-eslint/no-require-imports
const fs = require('fs');
// eslint-disable-next-line @typescript-eslint/no-require-imports
const path = require('path');
// eslint-disable-next-line @typescript-eslint/no-require-imports
const readline = require('readline');

// Configurações
const ENV_FILE = path.join(__dirname, '.env.local');
const MCP_FILE = path.join(__dirname, 'mcp.json');

// Cores para output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m',
};

// Interface para input do usuário
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
});

function question(prompt) {
  return new Promise(resolve => {
    rl.question(prompt, resolve);
  });
}

// eslint-disable-next-line no-console
console.log(`${colors.bold}🔧 CONFIGURADOR DE TOKENS MCP${colors.reset}\n`);

// Verificar se os arquivos existem
function checkFiles() {
  if (!fs.existsSync(ENV_FILE)) {
    // eslint-disable-next-line no-console
    console.log(`${colors.red}❌ Arquivo .env.local não encontrado${colors.reset}`);
    return false;
  }

  if (!fs.existsSync(MCP_FILE)) {
    // eslint-disable-next-line no-console
    console.log(`${colors.red}❌ Arquivo mcp.json não encontrado${colors.reset}`);
    return false;
  }

  return true;
}

// Atualizar .env.local
function updateEnvFile(linearToken, githubToken) {
  let envContent = fs.readFileSync(ENV_FILE, 'utf8');

  if (linearToken) {
    // Atualizar LINEAR_API_KEY
    const linearRegex = /LINEAR_API_KEY="[^"]*"/;
    if (envContent.match(linearRegex)) {
      envContent = envContent.replace(linearRegex, `LINEAR_API_KEY="${linearToken}"`);
    } else {
      envContent += `\nLINEAR_API_KEY="${linearToken}"`;
    }
    // eslint-disable-next-line no-console
    console.log(`${colors.green}✅ Token Linear configurado no .env.local${colors.reset}`);
  }

  if (githubToken) {
    // Atualizar GITHUB_TOKEN
    const githubRegex = /GITHUB_TOKEN="[^"]*"/;
    if (envContent.match(githubRegex)) {
      envContent = envContent.replace(githubRegex, `GITHUB_TOKEN="${githubToken}"`);
    } else {
      envContent += `\nGITHUB_TOKEN="${githubToken}"`;
    }
    // eslint-disable-next-line no-console
    console.log(`${colors.green}✅ Token GitHub configurado no .env.local${colors.reset}`);
  }

  fs.writeFileSync(ENV_FILE, envContent);
}

// Atualizar mcp.json
function updateMcpFile(linearToken, githubToken) {
  const mcpContent = JSON.parse(fs.readFileSync(MCP_FILE, 'utf8'));

  if (linearToken) {
    mcpContent.mcpServers.linear.env.LINEAR_API_KEY = linearToken;
    mcpContent.mcpServers.linear.status = 'active';
    mcpContent.configuration.integrations.active.push('linear');
    mcpContent.configuration.integrations.pending_tokens =
      mcpContent.configuration.integrations.pending_tokens.filter(item => item !== 'linear');
    // eslint-disable-next-line no-console
    console.log(`${colors.green}✅ Linear MCP ativado no mcp.json${colors.reset}`);
  }

  if (githubToken) {
    mcpContent.mcpServers.github.env.GITHUB_TOKEN = githubToken;
    mcpContent.mcpServers.github.status = 'active';
    mcpContent.configuration.integrations.active.push('github');
    mcpContent.configuration.integrations.pending_tokens =
      mcpContent.configuration.integrations.pending_tokens.filter(item => item !== 'github');
    // eslint-disable-next-line no-console
    console.log(`${colors.green}✅ GitHub MCP ativado no mcp.json${colors.reset}`);
  }

  fs.writeFileSync(MCP_FILE, JSON.stringify(mcpContent, null, 2));
}

// Validar tokens
function validateTokens(linearToken, githubToken) {
  const errors = [];

  if (linearToken && !linearToken.startsWith('lin_api_')) {
    errors.push('Token Linear deve começar com "lin_api_"');
  }

  if (githubToken && !githubToken.startsWith('ghp_')) {
    errors.push('Token GitHub deve começar com "ghp_"');
  }

  return errors;
}

// Testar integrações
async function testIntegrations() {
  // eslint-disable-next-line no-console
  console.log(`\n${colors.blue}🔍 Testando integrações configuradas...${colors.reset}`);

  try {
    // eslint-disable-next-line @typescript-eslint/no-require-imports
    const testScript = require('./test-mcp-production.js');
    const mcpResults = await testScript.testMCPEndpoints();

    const activeIntegrations = mcpResults.filter(r => r.status === 'OK').length;
    const totalIntegrations = mcpResults.length;

    // eslint-disable-next-line no-console
    console.log(
      `\n${colors.green}📊 Resultado: ${activeIntegrations}/${totalIntegrations} integrações ativas${colors.reset}`
    );

    if (activeIntegrations === totalIntegrations) {
      // eslint-disable-next-line no-console
      console.log(`${colors.green}🎉 Todas as integrações MCP estão funcionando!${colors.reset}`);
    }
  } catch (error) {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const _error = error;
    // eslint-disable-next-line no-console
    console.log(
      `${colors.yellow}⚠️ Não foi possível testar automaticamente. Execute: node test-mcp-production.js${colors.reset}`
    );
  }
}

// Função principal
async function main() {
  try {
    if (!checkFiles()) {
      process.exit(1);
    }

    // eslint-disable-next-line no-console
    console.log(`${colors.blue}📋 Configuração de Tokens MCP${colors.reset}`);
    // eslint-disable-next-line no-console
    console.log('Deixe em branco para pular a configuração de um token específico.\n');

    // Solicitar token Linear
    // eslint-disable-next-line no-console
    console.log(`${colors.yellow}🔗 Linear MCP Integration${colors.reset}`);
    // eslint-disable-next-line no-console
    console.log('1. Acesse: https://linear.app/settings/api');
    // eslint-disable-next-line no-console
    console.log('2. Clique em "Create API Key"');
    // eslint-disable-next-line no-console
    console.log('3. Configure nome: "Excel Copilot MCP Integration"');
    // eslint-disable-next-line no-console
    console.log('4. Selecione todas as permissões necessárias');
    // eslint-disable-next-line no-console
    console.log('5. Copie o token (formato: lin_api_...)');

    const linearToken = await question('\n📋 Cole o token Linear (ou Enter para pular): ');

    // Solicitar token GitHub
    // eslint-disable-next-line no-console
    console.log(`\n${colors.yellow}🐙 GitHub MCP Integration${colors.reset}`);
    // eslint-disable-next-line no-console
    console.log('1. Acesse: https://github.com/settings/tokens');
    // eslint-disable-next-line no-console
    console.log('2. Clique em "Generate new token (classic)"');
    // eslint-disable-next-line no-console
    console.log('3. Configure nome: "Excel Copilot MCP Integration"');
    // eslint-disable-next-line no-console
    console.log('4. Selecione scopes: repo, read:user, read:org');
    // eslint-disable-next-line no-console
    console.log('5. Copie o token (formato: ghp_...)');

    const githubToken = await question('\n🐙 Cole o token GitHub (ou Enter para pular): ');

    // Validar tokens
    const errors = validateTokens(linearToken.trim(), githubToken.trim());
    if (errors.length > 0) {
      // eslint-disable-next-line no-console
      console.log(`${colors.red}❌ Erros de validação:${colors.reset}`);
      // eslint-disable-next-line no-console
      errors.forEach(error => console.log(`   - ${error}`));
      process.exit(1);
    }

    // Configurar tokens
    if (linearToken.trim() || githubToken.trim()) {
      // eslint-disable-next-line no-console
      console.log(`\n${colors.blue}⚙️ Configurando tokens...${colors.reset}`);

      updateEnvFile(linearToken.trim() || null, githubToken.trim() || null);
      updateMcpFile(linearToken.trim() || null, githubToken.trim() || null);

      // eslint-disable-next-line no-console
      console.log(`${colors.green}✅ Configuração concluída!${colors.reset}`);

      // Testar integrações
      await testIntegrations();
    } else {
      // eslint-disable-next-line no-console
      console.log(
        `${colors.yellow}⚠️ Nenhum token fornecido. Configuração não alterada.${colors.reset}`
      );
    }

    // eslint-disable-next-line no-console
    console.log(`\n${colors.blue}📖 Próximos passos:${colors.reset}`);
    // eslint-disable-next-line no-console
    console.log('1. Execute: node test-mcp-production.js');
    // eslint-disable-next-line no-console
    console.log('2. Verifique se todas as integrações estão funcionando');
    // eslint-disable-next-line no-console
    console.log('3. Acesse os endpoints MCP para testar funcionalidades');
  } catch (error) {
    console.error(`${colors.red}Erro: ${error.message}${colors.reset}`);
    process.exit(1);
  } finally {
    rl.close();
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  main();
}

module.exports = { updateEnvFile, updateMcpFile, validateTokens };
