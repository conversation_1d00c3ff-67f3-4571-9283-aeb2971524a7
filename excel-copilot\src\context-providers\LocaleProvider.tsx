'use client';

import { createContext, useContext, useState, ReactNode } from 'react';

import useTranslation from '@/hooks/useTranslation';

/**
 * Tipos para o contexto de localização
 */
type LocaleContextType = {
  locale: string;
  setLocale: (locale: string) => void;
  t: (key: string, params?: Record<string, string | number | object>) => string;
  tPlural: (key: string, count: number) => string;
  formatNumber: (value: number, options?: Intl.NumberFormatOptions) => string;
  formatDate: (value: Date | string | number, options?: Intl.DateTimeFormatOptions) => string;
};

// Criar o contexto com valores padrão
const LocaleContext = createContext<LocaleContextType>({
  locale: 'pt-BR',
  setLocale: () => {},
  t: key => key,
  tPlural: key => key,
  formatNumber: value => String(value),
  formatDate: value => new Date(value).toISOString(),
});

/**
 * Hook para acessar o contexto de localização
 */
export const useLocale = () => useContext(LocaleContext);

/**
 * Provedor que fornece o contexto de idioma para a aplicação
 */
export function LocaleProvider({ children }: { children: ReactNode }) {
  // Sempre usar pt-BR como idioma padrão
  const [locale] = useState<string>('pt-BR');
  const { t, tPlural, formatNumber, formatDate } = useTranslation('pt-BR');

  // Função para alterar o idioma (agora apenas mantém pt-BR)
  const setLocale = (_newLocale: string) => {
    // Ignorar qualquer tentativa de mudar o idioma
    // Manter sempre como pt-BR
    if (typeof window !== 'undefined') {
      localStorage.setItem('locale', 'pt-BR');
    }
  };

  return (
    <LocaleContext.Provider value={{ locale, setLocale, t, tPlural, formatNumber, formatDate }}>
      {children}
    </LocaleContext.Provider>
  );
}

export default LocaleContext;
