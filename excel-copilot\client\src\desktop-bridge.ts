/**
 * Desktop Bridge - Interface para comunicação entre o aplicativo web e o aplicativo desktop Electron
 */

import { logger } from './lib/logger';
import { WebSocketStatus } from './types';

// Interface para as mensagens WebSocket
export interface WebSocketMessage {
  type: string;
  action?: string;
  payload?: unknown;
  id: string; // ID único para cada mensagem
}

// Interface para os resultados de operações
export interface OperationResult {
  success: boolean;
  message: string;
  data?: unknown;
}

// Interface para informações do workbook
export interface WorkbookInfo {
  filePath: string | null;
  worksheets: {
    id: string | number;
    name: string;
    rowCount: number;
    columnCount: number;
    tabColor?: string;
  }[];
  properties?: unknown;
  isConnected: boolean;
}

/**
 * Classe principal para comunicação com o aplicativo desktop
 */
export class DesktopBridge {
  private ws: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectTimeout: NodeJS.Timeout | null = null;
  private baseReconnectDelay = 1000; // 1 segundo
  private handlers: Map<string, (data: unknown) => void> = new Map();
  private statusHandler: ((status: WebSocketStatus) => void) | null = null;
  private operationId = 0;

  // Estado de conexão
  private _status: WebSocketStatus = WebSocketStatus.Disconnected;

  constructor(private serverUrl: string = 'ws://localhost:3030') {
    this.connect();
  }

  /**
   * Obtém o status atual da conexão WebSocket
   */
  public get status(): WebSocketStatus {
    return this._status;
  }

  /**
   * Define um manipulador para mudanças de status
   */
  public onStatusChange(handler: (status: WebSocketStatus) => void): void {
    this.statusHandler = handler;
  }

  /**
   * Atualiza o status da conexão
   */
  private setStatus(status: WebSocketStatus): void {
    if (this._status !== status) {
      this._status = status;
      if (this.statusHandler) {
        this.statusHandler(status);
      }
    }
  }

  /**
   * Conecta ao servidor WebSocket
   */
  public connect(): void {
    if (this.ws) {
      this.ws.close();
    }

    try {
      this.setStatus(WebSocketStatus.Connecting);
      this.ws = new WebSocket(this.serverUrl);

      this.ws.onopen = this.handleOpen.bind(this);
      this.ws.onmessage = this.handleMessage.bind(this);
      this.ws.onerror = this.handleError.bind(this);
      this.ws.onclose = this.handleClose.bind(this);

      logger.debug('[DesktopBridge] Tentando conectar ao WebSocket server:', this.serverUrl);
    } catch (error) {
      logger.error('[DesktopBridge] Erro ao criar conexão WebSocket:', error);
      this.setStatus(WebSocketStatus.Error);
      this.scheduleReconnect();
    }
  }

  /**
   * Manipula a abertura da conexão
   */
  private handleOpen(): void {
    logger.info('[DesktopBridge] Conexão WebSocket estabelecida');
    this.setStatus(WebSocketStatus.Connected);
    this.reconnectAttempts = 0;
  }

  /**
   * Manipula mensagens recebidas
   */
  private handleMessage(event: MessageEvent): void {
    try {
      const data = JSON.parse(event.data);
      logger.debug('[DesktopBridge] Mensagem recebida:', data);

      // Verifica se temos um handler registrado para o tipo de mensagem
      if (data.type && this.handlers.has(data.type)) {
        const handler = this.handlers.get(data.type);
        if (handler) {
          handler(data);
        }
      }
    } catch (error) {
      logger.error('[DesktopBridge] Erro ao processar mensagem:', error);
    }
  }

  /**
   * Manipula erros na conexão
   */
  private handleError(error: Event): void {
    logger.error('[DesktopBridge] Erro na conexão WebSocket:', error);
    this.setStatus(WebSocketStatus.Error);
  }

  /**
   * Manipula o fechamento da conexão
   */
  private handleClose(event: CloseEvent): void {
    logger.warn('[DesktopBridge] Conexão WebSocket fechada:', event.code, event.reason);
    this.setStatus(WebSocketStatus.Disconnected);
    this.ws = null;

    // Tentar reconectar automaticamente
    this.scheduleReconnect();
  }

  /**
   * Agenda uma tentativa de reconexão
   */
  private scheduleReconnect(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      logger.error('[DesktopBridge] Número máximo de tentativas de reconexão atingido');
      this.setStatus(WebSocketStatus.Failed);
      return;
    }

    // Limpar timeout anterior se existir
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
    }

    // Cálculo de backoff exponencial
    const delay = this.baseReconnectDelay * Math.pow(2, this.reconnectAttempts);
    this.reconnectAttempts++;

    logger.info(
      `[DesktopBridge] Tentando reconectar em ${delay}ms (tentativa ${this.reconnectAttempts}/${this.maxReconnectAttempts})`
    );

    this.reconnectTimeout = setTimeout(() => {
      this.connect();
    }, delay);
  }

  /**
   * Envia uma mensagem para o servidor WebSocket
   */
  private sendMessage(message: WebSocketMessage): boolean {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      logger.error('[DesktopBridge] Impossível enviar mensagem: WebSocket não está conectado');
      return false;
    }

    try {
      const messageString = JSON.stringify(message);
      this.ws.send(messageString);
      logger.debug('[DesktopBridge] Mensagem enviada:', message);
      return true;
    } catch (error) {
      logger.error('[DesktopBridge] Erro ao enviar mensagem:', error);
      return false;
    }
  }

  /**
   * Registra um manipulador para um tipo específico de mensagem
   */
  public registerHandler(type: string, handler: (data: unknown) => void): void {
    this.handlers.set(type, handler);
  }

  /**
   * Remove um manipulador
   */
  public removeHandler(type: string): void {
    this.handlers.delete(type);
  }

  /**
   * Normaliza uma operação para garantir consistência no formato
   * @param operation Operação para normalizar
   * @returns Operação normalizada
   */
  private normalizeOperation(operation: unknown): { type: string; data: unknown } {
    const op = operation as { type?: string; data?: unknown; params?: unknown };

    // Garantir que a propriedade 'type' existe
    if (!op.type) {
      throw new Error('Operação sem tipo não é válida');
    }

    // Converter tipos minúsculos para o formato enum correto
    let type = op.type;
    if (typeof type === 'string') {
      type = type.toUpperCase();
    }

    // Usar 'data' como propriedade padrão, mas aceitar 'params' para compatibilidade
    return {
      type,
      data: op.data || op.params || {},
    };
  }

  /**
   * Executa uma operação no Excel
   */
  public async executeOperation(operation: {
    type: string;
    data?: unknown;
    params?: unknown;
  }): Promise<OperationResult> {
    // Normalizar a operação para garantir formato consistente
    const normalizedOperation = this.normalizeOperation(operation);

    return new Promise(resolve => {
      const id = `op-${Date.now()}`;

      // Registrar handler para resposta
      this.registerHandler('operation_result', data => {
        const response = data as { id?: string; payload?: OperationResult };
        if (response.id === id) {
          this.removeHandler('operation_result');
          resolve(response.payload as OperationResult);
        }
      });

      // Enviar mensagem
      const success = this.sendMessage({
        type: 'operation',
        action: normalizedOperation.type,
        payload: normalizedOperation.data,
        id: id,
      });

      // Se falhar em enviar, resolver com erro
      if (!success) {
        this.removeHandler('operation_result');
        resolve({
          success: false,
          message: 'Falha ao enviar mensagem: WebSocket não está conectado',
        });
      }
    });
  }

  /**
   * Obtém informações do workbook atual
   */
  public async getWorkbookInfo(): Promise<WorkbookInfo | null> {
    return new Promise(resolve => {
      const id = `info-${Date.now()}`;

      // Registrar handler para resposta
      this.registerHandler('workbook_info', data => {
        const response = data as { id?: string; payload?: WorkbookInfo };
        if (response.id === id) {
          this.removeHandler('workbook_info');
          resolve(response.payload as WorkbookInfo);
        }
      });

      // Enviar mensagem
      const success = this.sendMessage({
        type: 'get_workbook_info',
        id: id,
      });

      // Se falhar em enviar, resolver com null
      if (!success) {
        this.removeHandler('workbook_info');
        resolve(null);
      }
    });
  }

  /**
   * Abre um arquivo Excel
   */
  public async openFile(filePath: string): Promise<OperationResult> {
    return new Promise(resolve => {
      const id = `open-${Date.now()}`;

      // Registrar handler para resposta
      this.registerHandler('file_opened', data => {
        const response = data as { id?: string; payload?: OperationResult };
        if (response.id === id) {
          this.removeHandler('file_opened');
          resolve(response.payload as OperationResult);
        }
      });

      // Enviar mensagem
      const success = this.sendMessage({
        type: 'open_file',
        payload: { filePath },
        id: id,
      });

      // Se falhar em enviar, resolver com erro
      if (!success) {
        this.removeHandler('file_opened');
        resolve({
          success: false,
          message: 'Falha ao enviar mensagem: WebSocket não está conectado',
        });
      }
    });
  }

  /**
   * Salva um arquivo Excel
   */
  public async saveFile(filePath?: string): Promise<OperationResult> {
    return new Promise(resolve => {
      const id = `save-${Date.now()}`;

      // Registrar handler para resposta
      this.registerHandler('file_saved', data => {
        const response = data as { id?: string; payload?: OperationResult };
        if (response.id === id) {
          this.removeHandler('file_saved');
          resolve(response.payload as OperationResult);
        }
      });

      // Enviar mensagem
      const success = this.sendMessage({
        type: 'save_file',
        payload: filePath ? { filePath } : undefined,
        id: id,
      });

      // Se falhar em enviar, resolver com erro
      if (!success) {
        this.removeHandler('file_saved');
        resolve({
          success: false,
          message: 'Falha ao enviar mensagem: WebSocket não está conectado',
        });
      }
    });
  }

  /**
   * Obtém dados da planilha ativa
   */
  public async getWorksheetData(): Promise<unknown> {
    return new Promise(resolve => {
      const id = `data-${Date.now()}`;

      // Registrar handler para resposta
      this.registerHandler('worksheet_data', data => {
        const response = data as { id?: string; payload?: unknown };
        if (response.id === id) {
          this.removeHandler('worksheet_data');
          resolve(response.payload);
        }
      });

      // Enviar mensagem
      const success = this.sendMessage({
        type: 'get_worksheet_data',
        id: id,
      });

      // Se falhar em enviar, resolver com null
      if (!success) {
        this.removeHandler('worksheet_data');
        resolve(null);
      }
    });
  }
}

// Instância singleton
export const desktopBridge = new DesktopBridge();
