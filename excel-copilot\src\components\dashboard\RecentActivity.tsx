'use client';

import { 
  FileSpreadsheet, 
  Edit3, 
  <PERSON>, 
  <PERSON>rk<PERSON>, 
  Clock,
  ExternalLink,
  MoreHorizontal
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import Link from 'next/link';
import React from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Skeleton } from '@/components/ui/skeleton';
import { cn } from '@/lib/utils';

interface ActivityItem {
  id: string;
  type: 'workbook_created' | 'workbook_edited' | 'ai_command' | 'collaboration';
  title: string;
  description: string;
  timestamp: Date;
  metadata?: Record<string, any>;
}

interface RecentActivityProps {
  activities: ActivityItem[];
  isLoading: boolean;
  className?: string;
  maxItems?: number;
}

/**
 * Mapeia tipos de atividade para ícones e cores
 */
const activityConfig = {
  workbook_created: {
    icon: FileSpreadsheet,
    color: 'text-green-600',
    bgColor: 'bg-green-50',
    label: 'Criação'
  },
  workbook_edited: {
    icon: Edit3,
    color: 'text-blue-600',
    bgColor: 'bg-blue-50',
    label: 'Edição'
  },
  ai_command: {
    icon: Sparkles,
    color: 'text-purple-600',
    bgColor: 'bg-purple-50',
    label: 'IA'
  },
  collaboration: {
    icon: Users,
    color: 'text-orange-600',
    bgColor: 'bg-orange-50',
    label: 'Colaboração'
  }
};

/**
 * Componente individual de item de atividade
 */
function ActivityItem({ activity }: { activity: ActivityItem }) {
  const config = activityConfig[activity.type];
  const Icon = config.icon;
  
  // Formatar timestamp relativo
  const timeAgo = formatDistanceToNow(new Date(activity.timestamp), {
    addSuffix: true,
    locale: ptBR
  });

  // Determinar se tem link para workbook
  const workbookId = activity.metadata?.workbookId;
  const hasAI = activity.metadata?.hasAI;

  return (
    <div className="flex items-start space-x-3 p-3 rounded-lg hover:bg-muted/50 transition-colors group">
      {/* Ícone da atividade */}
      <div className={cn(
        "flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center",
        config.bgColor
      )}>
        <Icon className={cn("h-4 w-4", config.color)} />
      </div>

      {/* Conteúdo da atividade */}
      <div className="flex-1 min-w-0">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <p className="text-sm font-medium text-foreground">
              {activity.title}
            </p>
            <Badge variant="outline" className="text-xs">
              {config.label}
            </Badge>
            {hasAI && (
              <Badge variant="secondary" className="text-xs bg-purple-100 text-purple-700">
                IA
              </Badge>
            )}
          </div>
          
          {/* Menu de ações */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button 
                variant="ghost" 
                size="sm" 
                className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
              >
                <MoreHorizontal className="h-3 w-3" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {workbookId && (
                <DropdownMenuItem asChild>
                  <Link href={`/workbook/${workbookId}`}>
                    <ExternalLink className="h-3 w-3 mr-2" />
                    Abrir Planilha
                  </Link>
                </DropdownMenuItem>
              )}
              <DropdownMenuItem>
                Ver Detalhes
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <p className="text-sm text-muted-foreground mt-1 truncate">
          {activity.description}
        </p>

        {/* Metadados adicionais */}
        {activity.metadata && (
          <div className="flex items-center space-x-4 mt-2 text-xs text-muted-foreground">
            <div className="flex items-center space-x-1">
              <Clock className="h-3 w-3" />
              <span>{timeAgo}</span>
            </div>
            
            {activity.metadata.sharedBy && (
              <span>por {activity.metadata.sharedBy}</span>
            )}
            
            {activity.metadata.sharedWith && (
              <span>com {activity.metadata.sharedWith}</span>
            )}
          </div>
        )}
      </div>
    </div>
  );
}

/**
 * Skeleton para loading de atividades
 */
function ActivitySkeleton() {
  return (
    <div className="flex items-start space-x-3 p-3">
      <Skeleton className="w-8 h-8 rounded-full" />
      <div className="flex-1 space-y-2">
        <div className="flex items-center space-x-2">
          <Skeleton className="h-4 w-32" />
          <Skeleton className="h-4 w-12" />
        </div>
        <Skeleton className="h-3 w-48" />
        <Skeleton className="h-3 w-24" />
      </div>
    </div>
  );
}

/**
 * Componente principal de atividade recente
 */
export function RecentActivity({ 
  activities, 
  isLoading, 
  className, 
  maxItems = 10 
}: RecentActivityProps) {
  const displayActivities = activities.slice(0, maxItems);

  return (
    <Card className={cn("", className)}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg">Atividade Recente</CardTitle>
            <CardDescription>
              Suas últimas ações no Excel Copilot
            </CardDescription>
          </div>
          {!isLoading && activities.length > maxItems && (
            <Button variant="outline" size="sm">
              Ver Todas ({activities.length})
            </Button>
          )}
        </div>
      </CardHeader>
      
      <CardContent className="p-0">
        {isLoading ? (
          <div className="space-y-1">
            {Array.from({ length: 5 }).map((_, index) => (
              <ActivitySkeleton key={index} />
            ))}
          </div>
        ) : displayActivities.length > 0 ? (
          <div className="space-y-1">
            {displayActivities.map((activity) => (
              <ActivityItem key={activity.id} activity={activity} />
            ))}
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center py-8 text-center">
            <Clock className="h-8 w-8 text-muted-foreground mb-2" />
            <p className="text-sm font-medium text-muted-foreground">
              Nenhuma atividade recente
            </p>
            <p className="text-xs text-muted-foreground mt-1">
              Suas ações aparecerão aqui conforme você usa o Excel Copilot
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

/**
 * Versão compacta para sidebar ou widgets menores
 */
export function RecentActivityCompact({ 
  activities, 
  isLoading, 
  className, 
  maxItems = 5 
}: RecentActivityProps) {
  const displayActivities = activities.slice(0, maxItems);

  if (isLoading) {
    return (
      <div className={cn("space-y-2", className)}>
        {Array.from({ length: 3 }).map((_, index) => (
          <div key={index} className="flex items-center space-x-2">
            <Skeleton className="w-6 h-6 rounded-full" />
            <div className="flex-1 space-y-1">
              <Skeleton className="h-3 w-24" />
              <Skeleton className="h-2 w-16" />
            </div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className={cn("space-y-2", className)}>
      {displayActivities.map((activity) => {
        const config = activityConfig[activity.type];
        const Icon = config.icon;
        const timeAgo = formatDistanceToNow(new Date(activity.timestamp), {
          addSuffix: true,
          locale: ptBR
        });

        return (
          <div key={activity.id} className="flex items-center space-x-2 text-sm">
            <div className={cn(
              "flex-shrink-0 w-6 h-6 rounded-full flex items-center justify-center",
              config.bgColor
            )}>
              <Icon className={cn("h-3 w-3", config.color)} />
            </div>
            <div className="flex-1 min-w-0">
              <p className="font-medium truncate">{activity.title}</p>
              <p className="text-xs text-muted-foreground">{timeAgo}</p>
            </div>
          </div>
        );
      })}
    </div>
  );
}
