import { ExcelOperation, ExcelOperationType } from '../../types/index';

// Interface temporária para compatibilidade
interface LocalExcelOperation extends ExcelOperation {
  // Este campo garante que a interface não fique vazia
  _localId?: string;
}

// Interface removida pois não é utilizada
// interface ExcelOperationBase {
//   /** Identificador único da operação */
//   id?: string;
// }

/**
 * Normaliza uma operação para o formato interno padrão
 * @param operation Operação para normalizar
 * @returns Operação normalizada
 */
export function normalizeOperation(operation: ExcelOperation): ExcelOperation {
  return {
    ...operation,
    // Garantir que sempre temos um ID
    id: operation.id || `op_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
  };
}

/**
 * Adapta operações do formato de API/público para formato interno da lib
 * @param operation Operação do tipo público/API
 * @returns Operação no formato interno da lib
 */
export function adaptToLibOperation(operation: ExcelOperation): LocalExcelOperation {
  // Garantir que operation.data seja definido
  const baseOperation = normalizeOperation(operation);

  // Converter para o formato local com type tipado como ExcelOperationType
  return convertToLocalOperation({
    ...baseOperation,
    type: mapPublicTypeToInternalType(baseOperation.type),
  });
}

/**
 * Converte uma operação para o formato local
 */
function convertToLocalOperation(operation: ExcelOperation): LocalExcelOperation {
  return operation as LocalExcelOperation;
}

/**
 * Mapeia tipos de operação do formato público para o formato interno
 */
function mapPublicTypeToInternalType(type: string): ExcelOperationType {
  // Mapeamento entre os tipos públicos e internos usando strings
  const typeMap: Record<string, ExcelOperationType> = {
    FORMULA: ExcelOperationType.FORMULA,
    CHART: ExcelOperationType.CHART,
    FILTER: ExcelOperationType.FILTER,
    SORT: ExcelOperationType.SORT,
    TABLE: ExcelOperationType.TABLE,
    FORMAT: ExcelOperationType.FORMAT,
    PIVOT_TABLE: ExcelOperationType.PIVOT_TABLE,
    CONDITIONAL_FORMAT: ExcelOperationType.CONDITIONAL_FORMAT,
    ADVANCED_VISUALIZATION: ExcelOperationType.ADVANCED_VISUALIZATION,
    COLUMN_OPERATION: ExcelOperationType.COLUMN_OPERATION,
    CELL_UPDATE: ExcelOperationType.CELL_UPDATE,
    ROW_OPERATION: ExcelOperationType.ROW_OPERATION,
    GENERIC: ExcelOperationType.GENERIC,
    DATA_TRANSFORMATION: ExcelOperationType.DATA_TRANSFORMATION,
  };

  // Se tivermos um match exato, usar
  if (type in typeMap) {
    const mappedType = typeMap[type];
    if (mappedType !== undefined) {
      return mappedType;
    }
  }

  // Fallback para operação genérica
  return ExcelOperationType.FORMULA;
}

/**
 * Normaliza o resultado de uma operação
 * @param result Resultado da operação a ser normalizado
 * @returns Resultado normalizado
 */
export function normalizeOperationResult(result: any): any {
  if (!result) return null;

  // Normalizar formato do resultado
  if (result.resultSummary && Array.isArray(result.resultSummary)) {
    return {
      ...result,
      resultSummary: result.resultSummary.join('; '),
    };
  }

  return result;
}

/**
 * Converte uma operação interna para o formato de operação base usado na aplicação
 */
export function convertToBaseOperation(libOperation: any): ExcelOperation {
  // Verificar se a operação já está no formato esperado
  if (libOperation.id && libOperation.type) {
    return libOperation as ExcelOperation;
  }

  // Caso contrário, converter para o formato padrão
  const type = libOperation.type || 'UNKNOWN';

  return createExcelOperation(type, {
    ...libOperation.data,
    ...libOperation.parameters,
  });
}

/**
 * Cria uma operação Excel com identificador único
 */
export function createExcelOperation(type: string, data: any = {}): ExcelOperation {
  return {
    id: generateOperationId(),
    type,
    data,
  };
}

/**
 * Gera um identificador único para uma operação
 */
function generateOperationId(): string {
  return Date.now().toString(36) + Math.random().toString(36).substring(2, 15);
}

/**
 * Adapta uma operação de qualquer biblioteca para o formato padrão de ExcelOperation
 */
export function adaptOperation(libOperation: any): ExcelOperation {
  if (!libOperation) {
    throw new Error('Operação inválida ou nula');
  }

  return convertToBaseOperation(libOperation);
}

/**
 * Mapeamento de aliases para tipos de operações
 */
export const operationTypeAliases: Record<string, string> = {
  COLUMN_OPERATION: ExcelOperationType.COLUMN_OPERATION,
  CELL_UPDATE: ExcelOperationType.CELL_UPDATE,
  ROW_OPERATION: ExcelOperationType.ROW_OPERATION,
  DATA_TRANSFORMATION: ExcelOperationType.DATA_TRANSFORMATION,
};
