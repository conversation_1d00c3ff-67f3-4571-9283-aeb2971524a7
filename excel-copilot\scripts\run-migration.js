#!/usr/bin/env node

/**
 * Script para executar a migração de usuários para plano Free
 * Uso: npm run migrate:free-plan
 */

const { execSync } = require('child_process');
const path = require('path');

console.log('🚀 Iniciando migração de usuários para plano Free...\n');

try {
  // Verificar se o tsx está instalado
  try {
    execSync('npx tsx --version', { stdio: 'ignore' });
  } catch (error) {
    console.log('📦 Instalando tsx...');
    execSync('npm install -g tsx', { stdio: 'inherit' });
  }

  // Executar o script de migração
  const scriptPath = path.join(__dirname, 'migrate-users-to-free-plan.ts');

  console.log('⚡ Executando script de migração...\n');
  execSync(`npx tsx "${scriptPath}"`, {
    stdio: 'inherit',
    cwd: path.dirname(__dirname), // Diretório raiz do projeto
  });

  console.log('\n✅ Migração concluída com sucesso!');
} catch (error) {
  console.error('\n❌ Erro durante a migração:', error.message);
  process.exit(1);
}
