/**
 * Integração entre o processador de IA e o Excel Desktop conectado
 *
 * Este módulo facilita a comunicação entre o processador de comandos IA
 * e o Excel Desktop, permitindo a execução de operações diretamente no Excel instalado.
 */

import { ExcelOperationType, ExcelOperation } from '@/types';

import { getDesktopBridgeCore } from '../desktop-bridge-core';
import { logger } from '../logger';

/**
 * Resultado de uma operação Excel
 */
export interface ExcelOperationResult {
  success: boolean;
  message: string;
  data?: any;
  error: string | undefined;
}

/**
 * Classe que conecta o processador de IA ao Excel Desktop
 */
export class ExcelDesktopConnector {
  private static instance: ExcelDesktopConnector;

  private constructor() {}

  /**
   * Obtém a instância singleton
   */
  public static getInstance(): ExcelDesktopConnector {
    if (!ExcelDesktopConnector.instance) {
      ExcelDesktopConnector.instance = new ExcelDesktopConnector();
    }
    return ExcelDesktopConnector.instance;
  }

  /**
   * Verifica se o Excel Desktop está conectado
   */
  public isExcelConnected(): boolean {
    const { state } = getDesktopBridgeCore();
    return state.isExcelConnected;
  }

  /**
   * Executa uma operação no Excel Desktop
   * @param operation Operação a ser executada
   */
  public async executeOperation(operation: ExcelOperation): Promise<ExcelOperationResult> {
    const { state, actions } = getDesktopBridgeCore();

    // Verificar se o Excel está conectado
    if (!state.isExcelConnected) {
      return {
        success: false,
        message: 'Excel não está conectado',
        error: 'EXCEL_NOT_CONNECTED',
      };
    }

    // Tentar executar a operação
    try {
      logger.info(`Executando operação no Excel Desktop: ${operation.type}`, operation);
      const result = await actions.executeOperation(operation);

      // Garantir que o resultado tenha a propriedade message como string
      return {
        success: result.success,
        message:
          result.message ||
          (result.success ? 'Operação executada com sucesso' : 'Falha na operação'),
        data: result.data,
        error: result.error,
      };
    } catch (error) {
      logger.error('Erro ao executar operação no Excel Desktop:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Erro desconhecido',
        error: 'OPERATION_EXECUTION_ERROR',
      };
    }
  }

  /**
   * Executa múltiplas operações em sequência
   * @param operations Lista de operações a serem executadas
   */
  public async executeOperations(operations: ExcelOperation[]): Promise<ExcelOperationResult[]> {
    const results: ExcelOperationResult[] = [];

    for (const operation of operations) {
      const result = await this.executeOperation(operation);
      results.push(result);

      // Se alguma operação falhar, interromper a sequência
      if (!result.success) {
        break;
      }
    }

    return results;
  }

  /**
   * Abre um arquivo Excel
   */
  public async openExcelFile(): Promise<ExcelOperationResult> {
    const { actions } = getDesktopBridgeCore();

    try {
      const result = await actions.openFile();
      return {
        success: result.success,
        message:
          result.message ||
          (result.success ? 'Arquivo aberto com sucesso' : 'Falha ao abrir arquivo'),
        data: result.data,
        error: result.error,
      };
    } catch (error) {
      logger.error('Erro ao abrir arquivo Excel:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Erro desconhecido',
        error: 'FILE_OPEN_ERROR',
      };
    }
  }

  /**
   * Obtém informações sobre o arquivo Excel atual
   */
  public async getWorkbookInfo(): Promise<ExcelOperationResult> {
    return await this.executeOperation({
      type: 'GET_WORKBOOK_INFO' as ExcelOperationType,
    });
  }

  /**
   * Obtém dados da planilha ativa
   */
  public async getActiveWorksheetData(): Promise<ExcelOperationResult> {
    return await this.executeOperation({
      type: 'GET_WORKSHEET_DATA' as ExcelOperationType,
    });
  }
}
