/**
 * Interceptor específico para webpack para prevenir carregamento de módulos Google AI no cliente
 */

import { isBlockedAIModule, AI_BLOCK_CLASSES, BLOCKED_AI_MODULES } from './constants';

// Função para criar módulo de bloqueio
function createBlockModule() {
  return {
    GoogleGenerativeAI: AI_BLOCK_CLASSES.GoogleGenerativeAI,
    VertexAI: AI_BLOCK_CLASSES.VertexAI,
    GenerativeModel: AI_BLOCK_CLASSES.GenerativeModel,
    default: {
      GoogleGenerativeAI: AI_BLOCK_CLASSES.GoogleGenerativeAI,
      VertexAI: AI_BLOCK_CLASSES.VertexAI,
      GenerativeModel: AI_BLOCK_CLASSES.GenerativeModel,
    },
  };
}

// Verificar se estamos no cliente
if (typeof window !== 'undefined') {
  console.log('[Webpack Interceptor] Inicializando interceptação de módulos webpack');

  // Interceptar __webpack_require__ se disponível
  const originalWebpackRequire = (window as any).__webpack_require__;
  if (originalWebpackRequire) {
    (window as any).__webpack_require__ = function (moduleId: string | number, ...args: any[]) {
      const moduleIdStr = String(moduleId);

      // Verificar se o módulo deve ser bloqueado usando função centralizada
      const shouldBlock =
        isBlockedAIModule(moduleIdStr) ||
        (typeof moduleId === 'string' && isBlockedAIModule(moduleId));

      if (shouldBlock) {
        console.warn(
          `[Webpack Interceptor] Bloqueado carregamento do módulo ${moduleIdStr} no cliente`
        );

        // Retornar módulo de bloqueio
        return createBlockModule();
      }

      // Se não for um módulo bloqueado, usar o require original
      return originalWebpackRequire.call(this, moduleId, ...args);
    };

    // Copiar propriedades do webpack require original
    Object.keys(originalWebpackRequire).forEach(key => {
      if (key !== 'apply' && key !== 'call') {
        try {
          (window as any).__webpack_require__[key] = originalWebpackRequire[key];
        } catch {
          // Ignorar erros ao copiar propriedades
        }
      }
    });
  }

  // Interceptar __webpack_modules__ se disponível
  const originalWebpackModules = (window as any).__webpack_modules__;
  if (originalWebpackModules && typeof originalWebpackModules === 'object') {
    // Criar proxy para interceptar acesso aos módulos
    (window as any).__webpack_modules__ = new Proxy(originalWebpackModules, {
      get(target, prop) {
        const propStr = String(prop);

        // Verificar se o módulo deve ser bloqueado usando função centralizada
        const shouldBlock = isBlockedAIModule(propStr);

        if (shouldBlock) {
          console.warn(
            `[Webpack Interceptor] Bloqueado acesso ao módulo webpack ${propStr} no cliente`
          );

          // Retornar função que retorna módulos de bloqueio
          return function (module: any, exports: any, _require: any) {
            const blockModule = createBlockModule();
            Object.assign(exports, blockModule);
          };
        }

        return target[prop];
      },
    });
  }

  // Interceptar tentativas de definir módulos Google diretamente
  BLOCKED_AI_MODULES.forEach(moduleName => {
    try {
      Object.defineProperty(window, moduleName, {
        get: function () {
          console.warn(
            `[Webpack Interceptor] Tentativa de acessar ${moduleName} no cliente foi bloqueada`
          );
          return createBlockModule();
        },
        set: function (_value) {
          console.warn(
            `[Webpack Interceptor] Tentativa de definir ${moduleName} no cliente foi bloqueada`
          );
        },
        configurable: true,
        enumerable: false,
      });
    } catch {
      // Ignorar erros ao definir propriedades
    }
  });

  console.log('[Webpack Interceptor] Interceptação de módulos webpack configurada com sucesso');
}

export {};
