const { spawn, execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const readline = require('readline');

const envPath = path.join(__dirname, '..', '.env.local');
const prismaPath = path.join(__dirname, '..', 'prisma');
const dbPath = path.join(prismaPath, 'dev.db');

// Cores para output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  green: '\x1b[32m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  yellow: '\x1b[33m',
  red: '\x1b[31m',
};

// Criar interface de readline
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
});

// Função para perguntar
function ask(question) {
  return new Promise(resolve => {
    rl.question(question, answer => {
      resolve(answer);
    });
  });
}

// Função para executar comando com output colorido
function runCommand(command, args, color) {
  return new Promise((resolve, reject) => {
    console.log(`${colors.bright}${color}> ${command} ${args.join(' ')}${colors.reset}`);

    const proc = spawn(command, args, { stdio: 'inherit', shell: true });

    proc.on('close', code => {
      if (code === 0) {
        resolve();
      } else {
        reject(new Error(`Comando falhou com código ${code}`));
      }
    });
  });
}

// Adicionar verificação completa das variáveis de ambiente
console.log('\n🔍 Executando verificação completa das variáveis de ambiente...');
try {
  const checkEnvResult = require('child_process').spawnSync('node', ['scripts/check-env.js'], {
    stdio: 'inherit',
    shell: true,
  });

  if (checkEnvResult.status !== 0) {
    console.error(
      '\n❌ Verificação de variáveis de ambiente falhou. Por favor, corrija os problemas acima antes de continuar.'
    );
    process.exit(1);
  }

  console.log('\n✅ Verificação de variáveis de ambiente concluída com sucesso!');
} catch (error) {
  console.error(`\n❌ Erro ao verificar variáveis de ambiente: ${error.message}`);
  process.exit(1);
}

// Verificar também serviços externos
console.log('\n🔌 Verificando conexão com serviços externos...');
try {
  const checkServicesResult = require('child_process').spawnSync(
    'node',
    ['scripts/verify-external-services.js'],
    {
      stdio: 'inherit',
      shell: true,
    }
  );

  if (checkServicesResult.status !== 0) {
    console.warn('\n⚠️ Alguns serviços externos podem não estar configurados corretamente.');
    console.warn(
      '   O servidor será iniciado, mas algumas funcionalidades podem estar indisponíveis.'
    );
  } else {
    console.log('\n✅ Todos os serviços externos estão configurados corretamente!');
  }
} catch (error) {
  console.warn(`\n⚠️ Erro ao verificar serviços externos: ${error.message}`);
  console.warn(
    '   O servidor será iniciado, mas algumas funcionalidades podem estar indisponíveis.'
  );
}

// Verificar ambiente e iniciar aplicação
async function startDevelopment() {
  console.log(
    `\n${colors.bright}${colors.blue}===== Excel Copilot - Configuração de Desenvolvimento =====${colors.reset}\n`
  );

  try {
    // 1. Verificar se .env.local existe, criar se não
    if (!fs.existsSync(envPath)) {
      console.log(`${colors.yellow}Arquivo .env.local não encontrado, criando...${colors.reset}`);

      // Executar script de configuração de ambiente
      require('./setup-environment');
      console.log(
        `${colors.green}✓ Arquivo .env.local criado com configurações padrão${colors.reset}`
      );
    } else {
      console.log(`${colors.green}✓ Arquivo .env.local encontrado${colors.reset}`);
    }

    // 2. Verificar se o banco de dados existe, criar se não
    if (!fs.existsSync(dbPath)) {
      console.log(
        `${colors.yellow}Banco de dados SQLite não encontrado, iniciando configuração...${colors.reset}`
      );

      // Perguntar se deve aplicar migrações
      const shouldMigrate = await ask(
        `${colors.cyan}Aplicar migrações Prisma para criar o banco de dados? (S/n): ${colors.reset}`
      );

      if (shouldMigrate.toLowerCase() !== 'n') {
        console.log(`${colors.blue}Executando migrações do Prisma...${colors.reset}`);
        await runCommand('npx', ['prisma', 'migrate', 'dev'], colors.blue);
        console.log(`${colors.green}✓ Banco de dados criado e migrado com sucesso${colors.reset}`);
      } else {
        console.log(
          `${colors.yellow}Migrações ignoradas. O banco de dados não será inicializado.${colors.reset}`
        );
      }
    } else {
      console.log(`${colors.green}✓ Banco de dados SQLite encontrado${colors.reset}`);
    }

    // 3. Verificar dependências
    console.log(`${colors.blue}Verificando dependências...${colors.reset}`);
    try {
      execSync('npx prisma --version', { stdio: 'ignore' });
      console.log(`${colors.green}✓ Prisma CLI instalado${colors.reset}`);
    } catch (error) {
      console.log(
        `${colors.red}✗ Prisma CLI não encontrado, instalando dependências...${colors.reset}`
      );
      await runCommand('npm', ['install'], colors.blue);
    }

    // 4. Gerar cliente Prisma
    console.log(`${colors.blue}Gerando cliente Prisma...${colors.reset}`);
    await runCommand('npx', ['prisma', 'generate'], colors.blue);

    // 5. Iniciar servidor de desenvolvimento
    console.log(
      `\n${colors.bright}${colors.green}===== Iniciando Excel Copilot =====${colors.reset}\n`
    );
    await runCommand('npm', ['run', 'dev'], colors.cyan);
  } catch (error) {
    console.error(`\n${colors.red}Erro durante a configuração:${colors.reset}`, error);
    process.exit(1);
  } finally {
    rl.close();
  }
}

// Executar função principal
startDevelopment();
