import { NextRequest, NextResponse } from 'next/server';

import { getLegacyApiUsageStats } from '@/lib/api-tracker';

/**
 * <PERSON>ler para obter estatísticas de uso de APIs legadas
 * Esta rota é protegida e só pode ser acessada por administradores
 */
export async function GET(_request: NextRequest) {
  try {
    // Verificar se temos o recurso habilitado pelo ambiente
    const skipAuthProviders = process.env.AUTH_SKIP_PROVIDERS === 'true';

    // Se estamos pulando autenticação no ambiente, retornamos dados simulados
    if (skipAuthProviders) {
      return NextResponse.json({
        message: 'Mock statistics mode enabled',
        totalDeprecatedCalls: 0,
        uniqueEndpoints: 0,
        usageData: [],
        generateAt: new Date().toISOString(),
      });
    }

    // Em modo real, obter estatísticas
    const stats = getLegacyApiUsageStats();

    // Preparar dados para resposta
    const usageData = Object.entries(stats).map(([path, count]) => ({
      path,
      count,
      lastAccessed: new Date().toISOString(), // Idealmente, rastreie a última vez que foi acessada
    }));

    // Devolver relatório completo
    return NextResponse.json({
      totalDeprecatedCalls: Object.values(stats).reduce((a, b) => a + b, 0),
      uniqueEndpoints: Object.keys(stats).length,
      usageData: usageData,
      generateAt: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Erro ao obter estatísticas de uso de APIs legadas:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}

export async function HEAD() {
  return new Response(null, {
    status: 200,
    headers: {
      'x-api-version': '2.0',
      'x-deprecated': 'true',
    },
  });
}
