#!/usr/bin/env node

/**
 * Teste Final da Auditoria de Segurança
 * Verifica se todas as correções foram aplicadas corretamente
 */

import fs from 'fs';
import path from 'path';

console.log('🔍 TESTE FINAL DA AUDITORIA DE SEGURANÇA');
console.log('=========================================\n');

const results = {
  passed: 0,
  failed: 0,
  warnings: 0,
  tests: [],
};

function addTest(name, status, message) {
  results.tests.push({ name, status, message });
  if (status === 'PASS') results.passed++;
  else if (status === 'FAIL') results.failed++;
  else if (status === 'WARN') results.warnings++;
}

// ============================================================================
// 1. VERIFICAR CORREÇÕES DE SEGURANÇA
// ============================================================================

console.log('🛡️  1. VERIFICANDO CORREÇÕES DE SEGURANÇA');
console.log('==========================================\n');

// Verificar se o bypass foi corrigido
const sharedRouteFile = 'src/app/api/workbooks/shared/route.ts';
if (fs.existsSync(sharedRouteFile)) {
  const content = fs.readFileSync(sharedRouteFile, 'utf8');

  if (content.includes('localhost') && content.includes('127.0.0.1')) {
    addTest('Bypass de desenvolvimento', 'PASS', 'Restrito apenas para localhost');
    console.log('✅ Bypass de desenvolvimento corrigido');
  } else if (content.includes('ENV.FEATURES?.SKIP_AUTH_PROVIDERS')) {
    addTest('Bypass de desenvolvimento', 'WARN', 'Ainda permite bypass sem restrição de host');
    console.log('⚠️  Bypass ainda presente mas sem restrição adequada');
  } else {
    addTest('Bypass de desenvolvimento', 'PASS', 'Bypass removido completamente');
    console.log('✅ Bypass removido completamente');
  }
} else {
  addTest('Bypass de desenvolvimento', 'FAIL', 'Arquivo não encontrado');
  console.log('❌ Arquivo shared route não encontrado');
}

// Verificar middleware principal
const middlewareFile = 'middleware.ts';
if (fs.existsSync(middlewareFile)) {
  const content = fs.readFileSync(middlewareFile, 'utf8');

  if (content.includes('rate limiting') && content.includes('X-Content-Type-Options')) {
    addTest('Middleware principal', 'PASS', 'Middleware com rate limiting e headers de segurança');
    console.log('✅ Middleware principal implementado corretamente');
  } else {
    addTest('Middleware principal', 'WARN', 'Middleware básico implementado');
    console.log('⚠️  Middleware implementado mas pode ser melhorado');
  }
} else {
  addTest('Middleware principal', 'FAIL', 'Middleware não encontrado');
  console.log('❌ Middleware principal não encontrado');
}

// Verificar funções de invalidação de cache
const subscriptionLimitsFile = 'src/lib/subscription-limits.ts';
if (fs.existsSync(subscriptionLimitsFile)) {
  const content = fs.readFileSync(subscriptionLimitsFile, 'utf8');

  if (content.includes('invalidateUserPlanCache') && content.includes('clearAllPlanCache')) {
    addTest('Invalidação de cache', 'PASS', 'Funções de invalidação implementadas');
    console.log('✅ Funções de invalidação de cache implementadas');
  } else {
    addTest('Invalidação de cache', 'FAIL', 'Funções de invalidação não encontradas');
    console.log('❌ Funções de invalidação não encontradas');
  }
} else {
  addTest('Invalidação de cache', 'FAIL', 'Arquivo subscription-limits não encontrado');
  console.log('❌ Arquivo subscription-limits não encontrado');
}

console.log('');

// ============================================================================
// 2. VERIFICAR SISTEMA DE LIMITAÇÃO
// ============================================================================

console.log('📊 2. VERIFICANDO SISTEMA DE LIMITAÇÃO');
console.log('======================================\n');

if (fs.existsSync(subscriptionLimitsFile)) {
  const content = fs.readFileSync(subscriptionLimitsFile, 'utf8');

  // Verificar constantes de limites
  if (content.includes('PLAN_LIMITS') && content.includes('MAX_WORKBOOKS')) {
    addTest('Constantes de limites', 'PASS', 'PLAN_LIMITS definidas corretamente');
    console.log('✅ Constantes PLAN_LIMITS definidas');
  } else {
    addTest('Constantes de limites', 'FAIL', 'PLAN_LIMITS não encontradas');
    console.log('❌ Constantes PLAN_LIMITS não encontradas');
  }

  // Verificar funções de verificação
  const functions = ['canCreateWorkbook', 'canAddCells', 'canAddChart', 'canUseAdvancedAI'];
  let functionsFound = 0;

  functions.forEach(func => {
    if (content.includes(`export async function ${func}`)) {
      functionsFound++;
      console.log(`  ✅ ${func} implementada`);
    } else {
      console.log(`  ❌ ${func} não encontrada`);
    }
  });

  if (functionsFound === functions.length) {
    addTest('Funções de verificação', 'PASS', 'Todas as funções implementadas');
  } else if (functionsFound > 0) {
    addTest(
      'Funções de verificação',
      'WARN',
      `${functionsFound}/${functions.length} funções implementadas`
    );
  } else {
    addTest('Funções de verificação', 'FAIL', 'Nenhuma função encontrada');
  }
}

console.log('');

// ============================================================================
// 3. VERIFICAR APIS COM VERIFICAÇÃO
// ============================================================================

console.log('🔌 3. VERIFICANDO APIS COM VERIFICAÇÃO');
console.log('=====================================\n');

const apiFiles = [
  'src/app/api/workbooks/route.ts',
  'src/app/api/chat/route.ts',
  'src/app/api/workbook/save/route.ts',
];

apiFiles.forEach(file => {
  if (fs.existsSync(file)) {
    const content = fs.readFileSync(file, 'utf8');
    const fileName = path.basename(file);

    // Verificar middleware
    if (content.includes('authMiddleware') || content.includes('withMiddleware')) {
      console.log(`  ✅ ${fileName}: Middleware aplicado`);
      addTest(`API ${fileName}`, 'PASS', 'Middleware de autenticação aplicado');
    } else {
      console.log(`  ⚠️  ${fileName}: Middleware não detectado`);
      addTest(`API ${fileName}`, 'WARN', 'Middleware não detectado');
    }

    // Verificar validação
    if (content.includes('safeParse') || content.includes('zod')) {
      console.log(`    ✅ Validação com schema presente`);
    } else {
      console.log(`    ⚠️  Validação básica ou ausente`);
    }
  } else {
    console.log(`  ❌ ${path.basename(file)}: Arquivo não encontrado`);
    addTest(`API ${path.basename(file)}`, 'FAIL', 'Arquivo não encontrado');
  }
});

console.log('');

// ============================================================================
// 4. VERIFICAR BUILD E TYPESCRIPT
// ============================================================================

console.log('🔧 4. VERIFICANDO BUILD E TYPESCRIPT');
console.log('====================================\n');

// Verificar se build foi bem-sucedido
if (fs.existsSync('.next')) {
  addTest('Build de produção', 'PASS', 'Build concluído com sucesso');
  console.log('✅ Build de produção concluído');

  // Verificar se há arquivos gerados
  const buildFiles = fs.readdirSync('.next');
  if (buildFiles.includes('server') && buildFiles.includes('static')) {
    console.log('  ✅ Arquivos de servidor e estáticos gerados');
  }
} else {
  addTest('Build de produção', 'FAIL', 'Build não encontrado');
  console.log('❌ Build de produção não encontrado');
}

// Verificar arquivos TypeScript
if (fs.existsSync('tsconfig.json')) {
  addTest('Configuração TypeScript', 'PASS', 'tsconfig.json presente');
  console.log('✅ Configuração TypeScript presente');
} else {
  addTest('Configuração TypeScript', 'FAIL', 'tsconfig.json não encontrado');
  console.log('❌ Configuração TypeScript não encontrada');
}

console.log('');

// ============================================================================
// 5. VERIFICAR DOCUMENTAÇÃO
// ============================================================================

console.log('📚 5. VERIFICANDO DOCUMENTAÇÃO');
console.log('==============================\n');

const docs = [
  'AUDITORIA_TECNICA_SISTEMA_PRIVILEGIOS.md',
  'RELATORIO_FINAL_AUDITORIA_SEGURANCA.md',
  'security-fixes-log.md',
];

docs.forEach(doc => {
  if (fs.existsSync(doc)) {
    addTest(`Documentação ${doc}`, 'PASS', 'Documento gerado');
    console.log(`✅ ${doc} gerado`);
  } else {
    addTest(`Documentação ${doc}`, 'WARN', 'Documento não encontrado');
    console.log(`⚠️  ${doc} não encontrado`);
  }
});

console.log('');

// ============================================================================
// RELATÓRIO FINAL
// ============================================================================

console.log('📊 RELATÓRIO FINAL DO TESTE');
console.log('===========================\n');

const total = results.passed + results.failed + results.warnings;
const successRate = Math.round((results.passed / total) * 100);

console.log(`✅ Testes Aprovados: ${results.passed}`);
console.log(`❌ Testes Falharam: ${results.failed}`);
console.log(`⚠️  Avisos: ${results.warnings}`);
console.log(`📊 Taxa de Sucesso: ${successRate}%\n`);

// Classificação final
let classification;
let emoji;

if (successRate >= 90) {
  classification = 'EXCELENTE';
  emoji = '🟢';
} else if (successRate >= 80) {
  classification = 'MUITO BOM';
  emoji = '🟡';
} else if (successRate >= 70) {
  classification = 'BOM';
  emoji = '🟠';
} else {
  classification = 'PRECISA MELHORAR';
  emoji = '🔴';
}

console.log(`${emoji} CLASSIFICAÇÃO FINAL: ${classification}`);
console.log(
  `🛡️  NÍVEL DE SEGURANÇA: ${successRate >= 85 ? 'ALTO' : successRate >= 70 ? 'MÉDIO' : 'BAIXO'}`
);

if (results.failed === 0) {
  console.log('\n🎉 SISTEMA APROVADO PARA PRODUÇÃO!');
  console.log('✅ Todas as correções de segurança foram aplicadas com sucesso');
} else {
  console.log('\n⚠️  ATENÇÃO: Alguns problemas precisam ser corrigidos antes da produção');
}

console.log('\n📋 DETALHES DOS TESTES:');
console.log('======================');

results.tests.forEach((test, index) => {
  const statusEmoji = test.status === 'PASS' ? '✅' : test.status === 'FAIL' ? '❌' : '⚠️ ';
  console.log(`${index + 1}. ${statusEmoji} ${test.name}: ${test.message}`);
});

console.log('\n🔍 AUDITORIA DE SEGURANÇA CONCLUÍDA');
console.log('===================================');
