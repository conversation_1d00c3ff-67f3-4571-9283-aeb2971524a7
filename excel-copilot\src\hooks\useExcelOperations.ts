import { useState, useCallback, useRef, useEffect } from 'react';
import { toast } from 'sonner';

import { executeExcelOperations, parseAICommandToExcelOperations } from '../lib/excel';
import { logger } from '../lib/logger';
import { logError, logWarning } from '../lib/utils/error-utils';

import { useExcelWorker } from './useExcelWorker';

export interface SpreadsheetData {
  headers: string[];
  rows: unknown[][];
  charts?: unknown[];
  name: string;
}

interface UseExcelOperationsProps {
  onDataChange?: (newData: SpreadsheetData) => void;
  onAddHistory?: (data: SpreadsheetData) => void;
}

interface ExcelOperationResult {
  updatedData: SpreadsheetData;
  resultSummary: string[];
  modifiedCells: Array<{ row: number; col: number }> | undefined;
}

/**
 * Hook para encapsular operações de manipulação de planilhas
 */
export function useExcelOperations({ onDataChange, onAddHistory }: UseExcelOperationsProps = {}) {
  const [isProcessing, setIsProcessing] = useState(false);
  const [lastModifiedCells, setLastModifiedCells] = useState<Array<{ row: number; col: number }>>(
    []
  );
  const [lastOperationSummary, setLastOperationSummary] = useState<string[]>([]);

  // Usar uma referência para os timers para limpeza adequada
  const resetTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Inicializar o worker de Excel
  const {
    executeOperation: executeWorkerOperation,
    isProcessing: isWorkerProcessing,
    cancelAllOperations,
  } = useExcelWorker({
    onSuccess: result => {
      // Quando uma operação for concluída com sucesso via worker
      if (result.modifiedCells) {
        setLastModifiedCells(result.modifiedCells);
        resetModifiedCells();
      }
    },
    onError: error => {
      logger.error('Erro no worker Excel:', error);
    },
  });

  // Limpar timers na desmontagem do componente
  useEffect(() => {
    return () => {
      if (resetTimerRef.current) {
        clearTimeout(resetTimerRef.current);
      }
      // Também cancelar todas as operações pendentes no worker
      cancelAllOperations();
    };
  }, [cancelAllOperations]);

  // Função para lidar com o reset das células modificadas
  const resetModifiedCells = useCallback(() => {
    if (resetTimerRef.current) {
      clearTimeout(resetTimerRef.current);
    }

    resetTimerRef.current = setTimeout(() => {
      setLastModifiedCells([]);
      resetTimerRef.current = null;
    }, 3000);
  }, []);

  // Função para determinar se uma operação é complexa o suficiente para usar worker
  const isComplexOperation = useCallback((operations: unknown[]): boolean => {
    if (operations.length > 2) return true;

    // Verificar tipos de operações que são computacionalmente intensivas
    return operations.some(op => {
      const opObject = op as Record<string, unknown>;
      const type = opObject.type ? String(opObject.type).toUpperCase() : undefined;
      return (
        type === 'FILTER' ||
        type === 'SORT' ||
        type === 'PIVOT_TABLE' ||
        type === 'CHART' ||
        type === 'ADVANCED_VISUALIZATION'
      );
    });
  }, []);

  /**
   * Processa um comando de linguagem natural e executa operações Excel
   */
  const processExcelCommand = useCallback(
    async (command: string, currentData: SpreadsheetData): Promise<SpreadsheetData | null> => {
      if (!command || !currentData) return null;

      // Evitar múltiplas execuções simultâneas
      if (isProcessing || isWorkerProcessing) {
        toast.info('Aguarde a conclusão da operação anterior', {
          duration: 2000,
        });
        return null;
      }

      setIsProcessing(true);

      try {
        // Salvar estado atual para histórico
        if (onAddHistory) {
          onAddHistory(structuredClone(currentData));
        }

        // Tentar processamento por novo processor (apenas no servidor)
        try {
          // CORREÇÃO: Não importar IA no cliente
          if (typeof window !== 'undefined') {
            throw new Error('IA não disponível no cliente');
          }

          // Importar o processador de IA apenas no servidor
          const { createExcelAIProcessor } = await import('../lib/ai');

          // Criar instância do processador com contexto da planilha atual
          const _aiProcessor = createExcelAIProcessor();

          // Preparar os dados de contexto para o processador
          const contextData = {
            activeSheet: currentData.name,
            headers: currentData.headers,
            selection: `A1:${String.fromCharCode(65 + currentData.headers.length - 1)}${currentData.rows.length}`,
            recentOperations: [],
          };

          // Processador atual não tem updateContext, então criamos uma nova instância com o contexto completo
          const contextualizedProcessor = new (await import('../lib/ai')).ExcelAIProcessor(
            contextData
          );

          // Processar o comando usando o AI Processor
          const parserResult = await contextualizedProcessor.processQuery(command);

          // Verificar se temos operações a executar
          if (
            parserResult.operations &&
            parserResult.operations.length > 0 &&
            !parserResult.error
          ) {
            // Verificar se podemos processar via worker (planilhas grandes)
            const isLargeDataset =
              currentData.rows.length > 500 ||
              (currentData.rows.length > 0 &&
                currentData.rows[0] &&
                currentData.rows[0].length > 50);

            // Se for um conjunto de dados grande e operação complexa, usar worker
            if (isLargeDataset && isComplexOperation(parserResult.operations)) {
              try {
                // Processamento em paralelo com o worker para operações individuais
                const results = await Promise.all(
                  parserResult.operations.map(operation =>
                    executeWorkerOperation(operation, currentData)
                  )
                );

                // Combinar resultados
                let finalResult = structuredClone(currentData) as SpreadsheetData;
                const allSummaries: string[] = [];
                const allModifiedCells: Array<{ row: number; col: number }> = [];

                for (const result of results) {
                  finalResult = result.updatedData as SpreadsheetData;
                  // Ignorar inferência de tipo TypeScript com conversão explícita
                  allSummaries.push(String(result.resultSummary));

                  if (result.modifiedCells) {
                    allModifiedCells.push(...result.modifiedCells);
                  }
                }

                // Atualizar metadados da operação
                setLastOperationSummary(allSummaries);
                if (allModifiedCells.length > 0) {
                  setLastModifiedCells(allModifiedCells);
                  resetModifiedCells();
                }

                // Notificar mudança de dados
                if (onDataChange) {
                  onDataChange(finalResult);
                }

                // Feedback visual
                toast.success('Operações executadas', {
                  description: allSummaries.join('; '),
                  duration: 3000,
                });

                return finalResult;
              } catch (workerError) {
                logWarning('Erro no worker, retornando ao método padrão:', workerError);
                // Fallback para o método padrão
              }
            }

            // Método padrão (sem worker)
            const excelOpResult = await executeExcelOperations(
              currentData as unknown as Record<string, unknown>,
              parserResult.operations
            );
            const result: ExcelOperationResult = {
              updatedData: excelOpResult.updatedData as unknown as SpreadsheetData,
              resultSummary: Array.isArray(excelOpResult.resultSummary)
                ? excelOpResult.resultSummary
                : [String(excelOpResult.resultSummary)],
              modifiedCells: excelOpResult.modifiedCells,
            };

            // Atualizar metadados da operação
            setLastOperationSummary(result.resultSummary);
            if (result.modifiedCells) {
              setLastModifiedCells(result.modifiedCells);
              resetModifiedCells();
            }

            // Notificar mudança de dados
            if (onDataChange) {
              onDataChange(result.updatedData);
            }

            // Feedback visual
            toast.success('Operações executadas', {
              description: result.resultSummary.join('; '),
              duration: 3000,
            });

            return result.updatedData;
          }
        } catch (err) {
          logWarning('Erro no novo processor, tentando fallback:', err);
        }

        // Fallback para o método antigo se o novo falhar
        try {
          const legacyParserResult = await parseAICommandToExcelOperations(command);

          if (legacyParserResult.success && legacyParserResult.operations.length > 0) {
            // Executar operações extraídas
            const excelOpResult = await executeExcelOperations(
              currentData as unknown as Record<string, unknown>,
              legacyParserResult.operations
            );
            const result: ExcelOperationResult = {
              updatedData: excelOpResult.updatedData as unknown as SpreadsheetData,
              resultSummary: Array.isArray(excelOpResult.resultSummary)
                ? excelOpResult.resultSummary
                : [String(excelOpResult.resultSummary)],
              modifiedCells: excelOpResult.modifiedCells,
            };

            // Atualizar metadados da operação
            setLastOperationSummary(result.resultSummary);
            if (result.modifiedCells) {
              setLastModifiedCells(result.modifiedCells);
              resetModifiedCells();
            }

            // Notificar mudança de dados
            if (onDataChange) {
              onDataChange(result.updatedData);
            }

            // Feedback visual
            toast.success('Operações executadas', {
              description: result.resultSummary.join('; '),
              duration: 3000,
            });

            return result.updatedData;
          } else {
            // Resposta sem operações reconhecidas
            toast.info('Nenhuma operação Excel', {
              description:
                legacyParserResult.message ||
                'Não foi possível extrair operações Excel deste comando.',
              duration: 4000,
            });

            return null;
          }
        } catch (error) {
          logError('Erro no parser de comandos:', error);
          // Continuar para o próximo fallback ou mostrar erro
        }

        // Retorno padrão caso nenhum processor consiga processar o comando
        toast.error('Não foi possível executar o comando', {
          description: 'Tente reformular seu comando ou use um exemplo da lista de sugestões.',
          duration: 4000,
        });
        return null;
      } catch (error) {
        logError('Erro ao processar comando Excel:', error);
        toast.error('Erro ao processar comando', {
          description: error instanceof Error ? error.message : 'Ocorreu um erro desconhecido.',
          duration: 4000,
        });
        return null;
      } finally {
        setIsProcessing(false);
      }
    },
    [
      isProcessing,
      isWorkerProcessing,
      onDataChange,
      onAddHistory,
      resetModifiedCells,
      executeWorkerOperation,
      isComplexOperation,
    ]
  );

  return {
    processExcelCommand,
    isProcessing: isProcessing || isWorkerProcessing,
    lastModifiedCells,
    lastOperationSummary,
  };
}
