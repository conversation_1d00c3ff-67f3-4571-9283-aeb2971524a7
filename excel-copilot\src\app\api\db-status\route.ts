import { PrismaClient } from '@prisma/client';
import { NextRequest, NextResponse } from 'next/server';

import { logger } from '@/lib/logger';

// Verificar se estamos no processo de build estático ou exportação
const isBuildTime =
  process.env.NEXT_PHASE === 'phase-production-build' || process.env.NEXT_STATIC_EXPORT === 'true';

// Criar o cliente Prisma apenas quando não estiver em build estático
const prisma = isBuildTime ? null : new PrismaClient();

type TableRecord = {
  table_name: string;
  [key: string]: unknown;
};

export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

export async function GET(_req: NextRequest) {
  // Pular a verificação durante o build estático para evitar erros
  if (isBuildTime) {
    logger.info('Pulando verificação de DB durante build estático');
    return NextResponse.json(
      {
        status: 'skipped',
        message: 'Verificação de banco de dados ignorada durante build estático',
      },
      { status: 200 }
    );
  }

  // O prisma não será null neste ponto, pois já verificamos NEXT_PHASE acima
  const dbClient = prisma as PrismaClient;

  try {
    // Verificar se podemos nos conectar ao banco de dados
    const tables = await dbClient.$queryRaw`
      SELECT table_name
      FROM information_schema.tables
      WHERE table_schema = 'public'
    `;

    // Verificar se as tabelas esperadas existem
    const tableNames = (tables as TableRecord[]).map((table: TableRecord) => table.table_name);

    const requiredTables = [
      'account',
      'session',
      'user',
      'verificationtoken',
      'subscription',
      'workbook',
    ];

    const missingTables = requiredTables.filter(table => !tableNames.includes(table.toLowerCase()));

    if (missingTables.length > 0) {
      return NextResponse.json(
        {
          status: 'warning',
          message: `Banco de dados conectado, mas faltam tabelas: ${missingTables.join(', ')}`,
          tables: tableNames,
          missingTables,
        },
        { status: 200 }
      );
    }

    return NextResponse.json(
      {
        status: 'ok',
        message: 'Banco de dados conectado e tabelas verificadas',
        tables: tableNames,
      },
      { status: 200 }
    );
  } catch (error: unknown) {
    console.error('Erro ao verificar status do banco de dados:', error);

    return NextResponse.json(
      {
        status: 'error',
        message: 'Falha ao conectar ao banco de dados',
        error: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  } finally {
    if (dbClient) {
      await dbClient.$disconnect();
    }
  }
}
