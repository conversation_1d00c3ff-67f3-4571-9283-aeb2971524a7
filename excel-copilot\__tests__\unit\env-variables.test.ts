/**
 * @jest-environment node
 */

import { ENV } from '@/config/unified-environment';
import * as fs from 'fs';
import * as path from 'path';

// Tipos para validação de variáveis de ambiente
type EnvVarValidator = {
  format?: RegExp;
  minLength?: number;
  isUrl?: boolean;
  isEmail?: boolean;
  isBoolean?: boolean;
  isPredefinedValue?: string[];
  isBase64?: boolean;
  isExternalUrl?: boolean;
};

// Configuração de validadores para cada variável
const ENV_VALIDATORS: Record<string, EnvVarValidator> = {
  // Banco de dados
  DATABASE_URL: {
    format: /^(mysql|postgresql):\/\/.+/,
    minLength: 20,
  },
  DATABASE_PROVIDER: {
    isPredefinedValue: ['mysql', 'postgresql', 'sqlite'],
  },

  // NextAuth
  NEXTAUTH_SECRET: {
    minLength: 16,
    isBase64: true,
  },
  NEXTAUTH_URL: {
    isUrl: true,
  },

  // Vertex AI
  VERTEX_AI_ENABLED: {
    isBoolean: true,
  },
  VERTEX_AI_PROJECT_ID: {
    format: /^[a-z][-a-z0-9]{4,28}[a-z0-9]$/,
  },
  VERTEX_AI_LOCATION: {
    isPredefinedValue: [
      'us-central1',
      'us-east1',
      'us-west1',
      'europe-west1',
      'europe-west2',
      'europe-west4',
      'asia-east1',
      'asia-northeast1',
      'asia-southeast1',
    ],
  },
  VERTEX_AI_MODEL_NAME: {
    format: /^gemini-[0-9].[0-9]-(pro|flash|ultra)(-vision)?(-001)?$/,
  },

  // OAuth
  GOOGLE_CLIENT_ID: {
    format: /^[0-9]+-[a-z0-9]+\.apps\.googleusercontent\.com$/,
  },
  GOOGLE_CLIENT_SECRET: {
    minLength: 16,
  },
  GITHUB_CLIENT_ID: {
    format: /^[a-f0-9]{20}$/,
  },
  GITHUB_CLIENT_SECRET: {
    format: /^[a-f0-9]{40}$/,
  },

  // Stripe
  STRIPE_SECRET_KEY: {
    format: /^(sk_test_|sk_live_)/,
  },
  NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY: {
    format: /^(pk_test_|pk_live_)/,
  },
  STRIPE_WEBHOOK_SECRET: {
    format: /^whsec_/,
  },

  // Feature flags
  USE_MOCK_AI: {
    isBoolean: true,
  },
  SKIP_AUTH_PROVIDERS: {
    isBoolean: true,
  },
  ENABLE_REALTIME_COLLABORATION: {
    isBoolean: true,
  },
  ENABLE_DESKTOP_INTEGRATION: {
    isBoolean: true,
  },

  // Limites e timeouts
  API_TIMEOUT: {
    format: /^\d+$/,
  },
  AI_TIMEOUT: {
    format: /^\d+$/,
  },
  MAX_FILE_SIZE: {
    format: /^\d+$/,
  },

  // Cache
  CACHE_DEFAULT_TTL: {
    format: /^\d+$/,
  },
};

// Funções auxiliares de validação
const isValidUrl = (value: string): boolean => {
  try {
    new URL(value);
    return true;
  } catch {
    return false;
  }
};

const isValidBase64 = (value: string): boolean => {
  return /^[A-Za-z0-9+/=]+$/.test(value);
};

const isValidEmail = (value: string): boolean => {
  return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value);
};

const isBoolean = (value: string): boolean => {
  return ['true', 'false'].includes(value.toLowerCase());
};

// Função para validar uma variável de ambiente
const validateEnvVar = (
  key: string,
  value: string | undefined,
  validator: EnvVarValidator
): string[] => {
  const errors: string[] = [];

  if (!value) {
    return [`${key} is not defined`];
  }

  if (validator.format && !validator.format.test(value)) {
    errors.push(`${key} has invalid format`);
  }

  if (validator.minLength && value.length < validator.minLength) {
    errors.push(`${key} is too short (min ${validator.minLength} chars)`);
  }

  if (validator.isUrl && !isValidUrl(value)) {
    errors.push(`${key} is not a valid URL`);
  }

  if (validator.isEmail && !isValidEmail(value)) {
    errors.push(`${key} is not a valid email`);
  }

  if (validator.isBoolean && !isBoolean(value)) {
    errors.push(`${key} is not a valid boolean (should be 'true' or 'false')`);
  }

  if (validator.isPredefinedValue && !validator.isPredefinedValue.includes(value)) {
    errors.push(
      `${key} is not one of the allowed values: ${validator.isPredefinedValue.join(', ')}`
    );
  }

  if (validator.isBase64 && !isValidBase64(value)) {
    errors.push(`${key} is not valid Base64 encoding`);
  }

  if (validator.isExternalUrl && (!isValidUrl(value) || value.startsWith('http://localhost'))) {
    errors.push(`${key} should be an external URL (not localhost)`);
  }

  return errors;
};

describe('Environment Variables', () => {
  // Verificar credenciais do Vertex AI antes de executar os testes
  const hasVertexCredentials = fs.existsSync(path.join(process.cwd(), 'vertex-credentials.json'));

  // Lista para armazenar erros para relatório final
  const allValidationErrors: string[] = [];

  // Verificar cada variável individualmente
  for (const [key, validator] of Object.entries(ENV_VALIDATORS)) {
    it(`verifica formato e valor de ${key}`, () => {
      // Ignorar testes do Vertex AI se estiver em mock mode
      if (key.startsWith('VERTEX_AI_') && (ENV.FEATURES.USE_MOCK_AI || !ENV.VERTEX_AI.ENABLED)) {
        return;
      }

      // Verificações especiais para credenciais
      if (
        key === 'VERTEX_AI_PROJECT_ID' &&
        !hasVertexCredentials &&
        (!process.env[key] || process.env[key] === '')
      ) {
        return; // Ignorar se usa arquivo de credenciais
      }

      // Se for o ambiente de teste, algumas variáveis podem estar faltando
      if (process.env.NODE_ENV === 'test' && !process.env[key]) {
        return;
      }

      // Validar a variável
      const errors = validateEnvVar(key, process.env[key], validator);

      if (errors.length > 0) {
        allValidationErrors.push(...errors);
      }

      expect(errors).toHaveLength(0);
    });
  }

  // Teste específico para dependências entre variáveis
  it('valida dependências entre variáveis de ambiente', () => {
    // Verificar Vertex AI
    if (ENV.VERTEX_AI.ENABLED && !ENV.FEATURES.USE_MOCK_AI) {
      if (!hasVertexCredentials) {
        expect(process.env.VERTEX_AI_PROJECT_ID).toBeDefined();
        expect(process.env.VERTEX_AI_LOCATION).toBeDefined();
      }
    }

    // Verificar providers de autenticação
    if (process.env.SKIP_AUTH_PROVIDERS !== 'true') {
      // Se não estiver pulando autenticação, verificar Google OAuth
      if (!process.env.GOOGLE_CLIENT_ID || !process.env.GOOGLE_CLIENT_SECRET) {
        allValidationErrors.push(
          'Google OAuth credentials missing when SKIP_AUTH_PROVIDERS is false'
        );
      }
      expect(process.env.GOOGLE_CLIENT_ID).toBeDefined();
      expect(process.env.GOOGLE_CLIENT_SECRET).toBeDefined();
    }

    // Stripe em produção
    if (process.env.NODE_ENV === 'production') {
      expect(process.env.STRIPE_SECRET_KEY).toBeDefined();
      expect(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY).toBeDefined();
      expect(process.env.STRIPE_WEBHOOK_SECRET).toBeDefined();
    }
  });

  // Teste para consistência de variáveis
  it('verifica consistência das variáveis em produção', () => {
    // Em produção, mock AI deve ser desativado
    if (process.env.NODE_ENV === 'production') {
      expect(ENV.FEATURES.USE_MOCK_AI).toBe(false);
    }

    // URLs de produção não devem ser localhost
    if (process.env.NODE_ENV === 'production' && process.env.NEXTAUTH_URL) {
      expect(process.env.NEXTAUTH_URL.includes('localhost')).toBe(false);
    }
  });

  // Relatório final de todos os erros para facilitar debugging
  afterAll(() => {
    if (allValidationErrors.length > 0) {
      console.error('Erros de validação de variáveis de ambiente:');
      allValidationErrors.forEach(err => console.error(`- ${err}`));
    }
  });
});
