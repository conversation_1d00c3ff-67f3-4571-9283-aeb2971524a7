// Mock do módulo vitest para compatibilidade com Jest

// Exportar funcionalidades do jest como vitest
module.exports = {
  describe: global.describe,
  it: global.it,
  test: global.test,
  expect: global.expect,
  beforeAll: global.beforeAll,
  afterAll: global.afterAll,
  beforeEach: global.beforeEach,
  afterEach: global.afterEach,
  vi: {
    fn: jest.fn,
    mock: jest.mock,
    spyOn: jest.spyOn,
    clearAllMocks: jest.clearAllMocks,
    resetAllMocks: jest.resetAllMocks,
    restoreAllMocks: jest.restoreAllMocks,
  },
};
