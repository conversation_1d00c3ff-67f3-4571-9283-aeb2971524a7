const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

async function setupSupabaseBuckets() {
  console.log('🪣 Configurando buckets do Supabase...\n');

  // Verificar variáveis de ambiente
  if (!process.env.SUPABASE_URL || !process.env.SUPABASE_SERVICE_ROLE_KEY) {
    console.error('❌ Variáveis SUPABASE_URL e SUPABASE_SERVICE_ROLE_KEY são necessárias');
    process.exit(1);
  }

  const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_SERVICE_ROLE_KEY, {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  });

  // Buckets necessários para o Excel Copilot
  const requiredBuckets = [
    {
      name: 'excel-files',
      public: false,
      allowedMimeTypes: [
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-excel',
      ],
      fileSizeLimit: 50 * 1024 * 1024, // 50MB
    },
    {
      name: 'exports',
      public: false,
      allowedMimeTypes: [
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-excel',
        'text/csv',
        'application/pdf',
      ],
      fileSizeLimit: 100 * 1024 * 1024, // 100MB
    },
    {
      name: 'templates',
      public: true,
      allowedMimeTypes: [
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-excel',
      ],
      fileSizeLimit: 25 * 1024 * 1024, // 25MB
    },
    {
      name: 'backups',
      public: false,
      allowedMimeTypes: [
        'application/json',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      ],
      fileSizeLimit: 200 * 1024 * 1024, // 200MB
    },
  ];

  try {
    // Listar buckets existentes
    console.log('📋 Verificando buckets existentes...');
    const { data: existingBuckets, error: listError } = await supabase.storage.listBuckets();

    if (listError) {
      throw listError;
    }

    console.log(`✅ Encontrados ${existingBuckets.length} buckets existentes:`);
    existingBuckets.forEach(bucket => {
      console.log(`   - ${bucket.name} (${bucket.public ? 'público' : 'privado'})`);
    });
    console.log();

    // Criar buckets necessários
    for (const bucketConfig of requiredBuckets) {
      const bucketExists = existingBuckets.some(bucket => bucket.name === bucketConfig.name);

      if (bucketExists) {
        console.log(`⏭️  Bucket '${bucketConfig.name}' já existe`);
        continue;
      }

      console.log(`🔨 Criando bucket '${bucketConfig.name}'...`);

      const { data, error } = await supabase.storage.createBucket(bucketConfig.name, {
        public: bucketConfig.public,
        allowedMimeTypes: bucketConfig.allowedMimeTypes,
        fileSizeLimit: bucketConfig.fileSizeLimit,
      });

      if (error) {
        console.error(`❌ Erro ao criar bucket '${bucketConfig.name}':`, error.message);
        continue;
      }

      console.log(`✅ Bucket '${bucketConfig.name}' criado com sucesso`);
      console.log(`   - Público: ${bucketConfig.public ? 'Sim' : 'Não'}`);
      console.log(`   - Tamanho máximo: ${Math.round(bucketConfig.fileSizeLimit / 1024 / 1024)}MB`);
      console.log(`   - Tipos permitidos: ${bucketConfig.allowedMimeTypes.length} tipos`);
    }

    // Verificar buckets finais
    console.log('\n📊 Verificação final dos buckets...');
    const { data: finalBuckets, error: finalError } = await supabase.storage.listBuckets();

    if (finalError) {
      throw finalError;
    }

    console.log(`✅ Total de buckets configurados: ${finalBuckets.length}`);
    finalBuckets.forEach(bucket => {
      const isRequired = requiredBuckets.some(req => req.name === bucket.name);
      const status = isRequired ? '✅' : '📦';
      console.log(`   ${status} ${bucket.name} (${bucket.public ? 'público' : 'privado'})`);
    });

    // Testar upload em um bucket
    console.log('\n🧪 Testando upload de arquivo...');
    const testData = Buffer.from('Test file for Excel Copilot', 'utf-8');
    const testPath = `test/test_${Date.now()}.txt`;

    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('excel-files')
      .upload(testPath, testData, {
        contentType: 'text/plain',
      });

    if (uploadError) {
      console.error('❌ Erro no teste de upload:', uploadError.message);
    } else {
      console.log('✅ Teste de upload realizado com sucesso');
      console.log(`   Arquivo: ${uploadData.path}`);

      // Limpar arquivo de teste
      await supabase.storage.from('excel-files').remove([testPath]);
      console.log('🧹 Arquivo de teste removido');
    }

    console.log('\n🎉 Configuração de buckets concluída com sucesso!');
    console.log('\n📝 Próximos passos:');
    console.log('   1. Configurar políticas RLS para os buckets');
    console.log('   2. Testar upload/download de arquivos Excel');
    console.log('   3. Implementar limpeza automática de arquivos antigos');
  } catch (error) {
    console.error('\n💥 Erro na configuração dos buckets:', error.message);
    process.exit(1);
  }
}

// Executar configuração
setupSupabaseBuckets();
