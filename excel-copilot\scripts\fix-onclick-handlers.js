#!/usr/bin/env node

/**
 * Script para corrigir automaticamente problemas de onClick em componentes
 * Este script transforma onClick={handler} em onClick={() => handler()}
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

// Cores para mensagens de terminal
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
};

// Lista de extensões de arquivo a serem processadas
const extensions = ['tsx', 'jsx'];

// Função principal
async function main() {
  console.log(
    `${colors.cyan}Excel Copilot - Correção Automática de Handlers onClick${colors.reset}`
  );
  console.log(
    `${colors.yellow}Este script irá transformar onClick={handler} em onClick={() => handler()} em componentes React${colors.reset}\n`
  );

  const rootDir = path.resolve(__dirname, '..');
  const srcDir = path.join(rootDir, 'src');

  // Verifica se o diretório src existe
  if (!fs.existsSync(srcDir)) {
    console.log(
      `${colors.red}Diretório src não encontrado. Verifique se você está executando este script na pasta raiz do projeto.${colors.reset}`
    );
    process.exit(1);
  }

  try {
    // Encontra todos os arquivos .tsx e .jsx
    const files = glob.sync(`**/*.{${extensions.join(',')}}`, { cwd: srcDir });

    if (files.length === 0) {
      console.log(
        `${colors.yellow}Nenhum arquivo ${extensions.join('/')} encontrado no diretório src.${colors.reset}`
      );
      process.exit(0);
    }

    console.log(
      `${colors.blue}Encontrados ${files.length} arquivos para processamento.${colors.reset}\n`
    );

    let filesChanged = 0;
    let handlersFixed = 0;

    // Processa cada arquivo
    for (const file of files) {
      const filePath = path.join(srcDir, file);
      const content = fs.readFileSync(filePath, 'utf8');

      // Verifica se tem onClick com handler simples
      // Padrão: onClick={handlerFunction} sem parênteses
      const pattern = /onClick={(\w+)(?!\s*\(.*\))}/g;

      if (pattern.test(content)) {
        // Substitui onClick={handlerFunction} por onClick={() => handlerFunction()}
        const modifiedContent = content.replace(pattern, 'onClick={() => $1()}');

        // Conta quantas substituições foram feitas
        const matches = content.match(pattern);
        const fixedCount = matches ? matches.length : 0;

        // Salva as alterações no arquivo
        fs.writeFileSync(filePath, modifiedContent);

        console.log(`${colors.green}Arquivo: ${file}${colors.reset}`);
        console.log(`  ${colors.yellow}Corrigidos ${fixedCount} handlers onClick${colors.reset}`);

        filesChanged++;
        handlersFixed += fixedCount;
      }
    }

    console.log(`\n${colors.blue}Processo concluído!${colors.reset}`);
    console.log(`${colors.cyan}Resumo:${colors.reset}`);
    console.log(`  ${colors.green}Arquivos processados: ${files.length}${colors.reset}`);
    console.log(`  ${colors.green}Arquivos modificados: ${filesChanged}${colors.reset}`);
    console.log(`  ${colors.green}Handlers onClick corrigidos: ${handlersFixed}${colors.reset}`);

    if (handlersFixed === 0) {
      console.log(
        `\n${colors.yellow}Nenhum problema de onClick encontrado nos arquivos.${colors.reset}`
      );
    } else {
      console.log(`\n${colors.green}Correções aplicadas com sucesso!${colors.reset}`);
      console.log(`${colors.yellow}Próximos passos:${colors.reset}`);
      console.log(
        `  1. Execute o comando 'npm run build' para verificar se os problemas foram resolvidos`
      );
      console.log(
        `  2. Execute o script 'fix-client-components.js' novamente para verificar se todos os avisos foram eliminados`
      );
    }
  } catch (error) {
    console.error(`${colors.red}Erro ao processar os arquivos:${colors.reset}`, error);
    process.exit(1);
  }
}

// Executa a função principal
main().catch(error => {
  console.error(`${colors.red}Erro ao executar o script:${colors.reset}`, error);
  process.exit(1);
});
