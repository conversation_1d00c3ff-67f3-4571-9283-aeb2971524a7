/**
 * Validação de configurações de autenticação
 * Garante que todas as variáveis críticas estejam presentes e válidas
 */

import { logger } from '@/lib/logger';

interface AuthEnvironmentConfig {
  AUTH_NEXTAUTH_SECRET: string;
  AUTH_NEXTAUTH_URL: string;
  AUTH_GOOGLE_CLIENT_ID: string;
  AUTH_GOOGLE_CLIENT_SECRET: string;
  AUTH_GITHUB_CLIENT_ID: string;
  AUTH_GITHUB_CLIENT_SECRET: string;
}

interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

/**
 * Valida se todas as variáveis de ambiente necessárias para autenticação estão presentes
 */
export function validateAuthEnvironment(): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Validar variáveis obrigatórias
  const requiredVars: (keyof AuthEnvironmentConfig)[] = [
    'AUTH_NEXTAUTH_SECRET',
    'AUTH_NEXTAUTH_URL',
    'AUTH_GOOGLE_CLIENT_ID',
    'AUTH_GOOGLE_CLIENT_SECRET',
    'AUTH_GITHUB_CLIENT_ID',
    'AUTH_GITHUB_CLIENT_SECRET',
  ];

  for (const varName of requiredVars) {
    const value = process.env[varName];

    if (!value) {
      errors.push(`Variável de ambiente obrigatória ausente: ${varName}`);
      continue;
    }

    // Validações específicas por tipo
    switch (varName) {
      case 'AUTH_NEXTAUTH_SECRET':
        if (value.length < 32) {
          warnings.push('NEXTAUTH_SECRET deve ter pelo menos 32 caracteres para máxima segurança');
        }
        break;

      case 'AUTH_NEXTAUTH_URL':
        try {
          const url = new URL(value);
          if (!url.protocol.startsWith('http')) {
            errors.push('NEXTAUTH_URL deve ser uma URL válida (http/https)');
          }
          if (process.env.NODE_ENV === 'production' && url.protocol !== 'https:') {
            warnings.push('NEXTAUTH_URL deve usar HTTPS em produção');
          }
        } catch {
          errors.push('NEXTAUTH_URL deve ser uma URL válida');
        }
        break;

      case 'AUTH_GOOGLE_CLIENT_ID':
        if (!value.endsWith('.apps.googleusercontent.com')) {
          warnings.push('GOOGLE_CLIENT_ID deve terminar com .apps.googleusercontent.com');
        }
        break;

      case 'AUTH_GITHUB_CLIENT_ID':
        if (value.length < 16) {
          warnings.push('GITHUB_CLIENT_ID parece ter formato inválido');
        }
        break;
    }
  }

  const isValid = errors.length === 0;

  // Log dos resultados
  if (isValid) {
    logger.info('✅ Validação de ambiente de autenticação passou', {
      warnings: warnings.length,
      environment: process.env.NODE_ENV,
    });
  } else {
    logger.error('❌ Validação de ambiente de autenticação falhou', {
      errors: errors.length,
      warnings: warnings.length,
      environment: process.env.NODE_ENV,
    });
  }

  // Log warnings se houver
  if (warnings.length > 0) {
    warnings.forEach(warning => logger.warn(`⚠️ ${warning}`));
  }

  // Log errors se houver
  if (errors.length > 0) {
    errors.forEach(error => logger.error(`🚨 ${error}`));
  }

  return {
    isValid,
    errors,
    warnings,
  };
}

/**
 * Valida configuração específica do Google OAuth
 */
export function validateGoogleOAuthConfig(): boolean {
  const clientId = process.env.AUTH_GOOGLE_CLIENT_ID;
  const clientSecret = process.env.AUTH_GOOGLE_CLIENT_SECRET;

  if (!clientId || !clientSecret) {
    logger.error('Google OAuth: Credenciais ausentes');
    return false;
  }

  if (!clientId.includes('.apps.googleusercontent.com')) {
    logger.error('Google OAuth: Client ID inválido');
    return false;
  }

  if (clientSecret.length < 20) {
    logger.warn('Google OAuth: Client Secret parece muito curto');
  }

  return true;
}

/**
 * Valida configuração específica do GitHub OAuth
 */
export function validateGitHubOAuthConfig(): boolean {
  const clientId = process.env.AUTH_GITHUB_CLIENT_ID;
  const clientSecret = process.env.AUTH_GITHUB_CLIENT_SECRET;

  if (!clientId || !clientSecret) {
    logger.error('GitHub OAuth: Credenciais ausentes');
    return false;
  }

  if (clientId.length < 16) {
    logger.error('GitHub OAuth: Client ID inválido');
    return false;
  }

  if (clientSecret.length < 30) {
    logger.warn('GitHub OAuth: Client Secret parece muito curto');
  }

  return true;
}

/**
 * Executa validação completa e falha rapidamente se configuração crítica estiver ausente
 */
export function validateOrFail(): void {
  const result = validateAuthEnvironment();

  if (!result.isValid) {
    const errorMessage = `Configuração de autenticação inválida:\n${result.errors.join('\n')}`;
    logger.error('🚨 FALHA CRÍTICA: Configuração de autenticação inválida', {
      errors: result.errors,
      warnings: result.warnings,
    });

    if (process.env.NODE_ENV === 'production') {
      throw new Error(errorMessage);
    } else {
      logger.warn('⚠️ Continuando em desenvolvimento apesar dos erros de configuração');
    }
  }
}

/**
 * Gera relatório de saúde da configuração de autenticação
 */
export function getAuthHealthReport() {
  const validation = validateAuthEnvironment();
  const googleValid = validateGoogleOAuthConfig();
  const githubValid = validateGitHubOAuthConfig();

  return {
    overall: validation.isValid && googleValid && githubValid,
    environment: validation,
    providers: {
      google: googleValid,
      github: githubValid,
    },
    timestamp: new Date().toISOString(),
  };
}
