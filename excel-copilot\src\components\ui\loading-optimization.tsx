'use client';

import { useEffect } from 'react';

import { safeConsoleLog } from '@/lib/logger';

/**
 * Componente para otimizar o carregamento da aplicação
 * Utiliza técnicas avançadas para melhorar FCP e LCP
 */
export function LoadingOptimization(): React.ReactNode {
  useEffect(() => {
    const optimizeLoading = () => {
      // 1. Preload imagens críticas
      const criticalImages = ['/images/excel-copilot-icon.svg', '/favicon.ico'];

      criticalImages.forEach(src => {
        const img = new Image();
        img.src = src;
      });

      // 2. Prefetch rotas críticas
      const criticalRoutes = ['/dashboard'];

      criticalRoutes.forEach(route => {
        const link = document.createElement('link');
        link.rel = 'prefetch';
        link.href = route;
        document.head.appendChild(link);
      });

      // 3. Preconnect com APIs e serviços
      const services = [
        'https://fonts.googleapis.com',
        'https://us-central1-aiplatform.googleapis.com', // Vertex AI endpoint
      ];

      services.forEach(service => {
        const link = document.createElement('link');
        link.rel = 'preconnect';
        link.href = service;
        link.crossOrigin = 'anonymous';
        document.head.appendChild(link);
      });

      // 4. Lazy load recursos não-críticos após LCP
      setTimeout(() => {
        // Carregar imagens secundárias, scripts não-críticos, etc.
        const secondaryResources = document.querySelectorAll('[data-loading="lazy"]');
        secondaryResources.forEach((el: Element) => {
          if (el instanceof HTMLImageElement && el.dataset.src) {
            el.src = el.dataset.src;
          }
        });
      }, 2000); // 2 segundos após carregamento
    };

    // Executar otimização após o componente montar
    optimizeLoading();

    // Monitorar métricas de performance
    if (typeof window !== 'undefined' && 'PerformanceObserver' in window) {
      try {
        const observer = new PerformanceObserver(list => {
          const entries = list.getEntries();
          entries.forEach(entry => {
            // Reportar LCP para análise
            if (entry.entryType === 'largest-contentful-paint') {
              safeConsoleLog('LCP:', Math.round(entry.startTime));
            }
          });
        });

        observer.observe({ type: 'largest-contentful-paint', buffered: true });
      } catch (e) {
        console.error('Erro ao monitorar performance:', e);
      }
    }

    return () => {
      // Cleanup se necessário
    };
  }, []);

  // Componente não renderiza nada visualmente
  return null;
}

export default LoadingOptimization;
