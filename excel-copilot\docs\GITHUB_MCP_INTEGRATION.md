# 🐙 GitHub MCP Integration - Excel Copilot

Integração completa com GitHub API para monitoramento de repositórios, issues, pull requests e CI/CD workflows através do protocolo MCP (Model Context Protocol).

## 📋 Índice

- [Visão Geral](#-visão-geral)
- [Configuração](#-configuração)
- [Funcionalidades](#-funcionalidades)
- [Endpoints da API](#-endpoints-da-api)
- [Exemplos de Uso](#-exemplos-de-uso)
- [Monitoramento](#-monitoramento)
- [Desenvolvimento](#-desenvolvimento)
- [Troubleshooting](#-troubleshooting)

## 🎯 Visão Geral

A integração GitHub MCP permite:

- **Monitoramento de Repositórios**: Acompanhar status, issues e pull requests
- **CI/CD Analytics**: Métricas de workflow runs, taxa de sucesso e falhas
- **Gestão de Issues**: Listar e filtrar issues por estado, labels e assignees
- **Pull Request Tracking**: Monitorar PRs, reviews e merge status
- **Rate Limit Management**: Controle automático de rate limiting da API GitHub
- **Health Monitoring**: Verificação contínua da conectividade e permissões

## ⚙️ Configuração

### 1. Variáveis de Ambiente

Adicione as seguintes variáveis ao seu arquivo `.env.local`:

```bash
# GitHub MCP Integration
GITHUB_TOKEN="ghp_seu-token-github-aqui"
GITHUB_OWNER="seu-usuario-ou-organizacao"
GITHUB_REPO="nome-do-repositorio"
```

### 2. Obter Token GitHub

1. Acesse [GitHub Settings > Personal Access Tokens](https://github.com/settings/tokens)
2. Clique em "Generate new token (classic)"
3. Selecione as seguintes permissões:
   - `repo` - Acesso completo a repositórios privados
   - `read:org` - Ler informações da organização
   - `read:user` - Ler perfil do usuário
   - `read:project` - Ler projetos (se usar GitHub Projects)

### 3. Configurar Owner e Repo (Opcional)

- `GITHUB_OWNER`: Seu username ou nome da organização
- `GITHUB_REPO`: Nome do repositório principal para monitoramento

> **Nota**: Se não configurar owner/repo, a integração funcionará para listar repositórios do usuário autenticado.

## 🚀 Funcionalidades

### Monitoramento de Repositórios

- Lista repositórios do usuário/organização
- Detalhes completos de repositórios específicos
- Estatísticas de stars, forks e issues

### Gestão de Issues e PRs

- Listagem com filtros avançados
- Estados: open, closed, all
- Filtros por labels, assignees e milestones
- Ordenação por data de criação/atualização

### CI/CD Analytics

- Workflow runs com status e conclusão
- Métricas de taxa de sucesso
- Duração média de builds
- Trends dos últimos 30 dias
- Identificação de falhas recentes

### Health Monitoring

- Verificação de conectividade com GitHub API
- Validação de token e permissões
- Monitoramento de rate limits
- Status de repositórios configurados

## 🔌 Endpoints da API

### Status da Integração

#### `GET /api/github/status`

Obtém status geral da integração GitHub.

**Resposta:**

```json
{
  "data": {
    "status": "healthy",
    "configured": {
      "token": true,
      "owner": true,
      "repo": true
    },
    "repository": {
      "name": "usuario/repositorio",
      "description": "Descrição do repositório",
      "language": "TypeScript",
      "stars": 42,
      "forks": 7,
      "openIssues": 3,
      "openPullRequests": 1,
      "healthStatus": "healthy",
      "lastUpdate": "2024-01-15T10:30:00Z"
    },
    "cicd": {
      "totalRuns": 156,
      "successRate": 94.2,
      "averageDuration": 4.5,
      "recentFailures": 2
    },
    "timestamp": "2024-01-15T12:00:00Z"
  }
}
```

#### `POST /api/github/status`

Força verificação de status (útil para debugging).

### Repositórios

#### `GET /api/github/repositories`

Lista repositórios do usuário/organização.

**Parâmetros de Query:**

- `type`: `all`, `owner`, `public`, `private`, `member` (padrão: `owner`)
- `sort`: `created`, `updated`, `pushed`, `full_name` (padrão: `updated`)
- `direction`: `asc`, `desc` (padrão: `desc`)
- `per_page`: 1-100 (padrão: 30)
- `page`: número da página (padrão: 1)

#### `POST /api/github/repositories`

Obtém detalhes de um repositório específico.

**Body:**

```json
{
  "owner": "usuario",
  "repo": "repositorio"
}
```

### Issues e Pull Requests

#### `GET /api/github/issues`

Lista issues e pull requests.

**Parâmetros de Query:**

- `owner`: proprietário do repositório
- `repo`: nome do repositório
- `type`: `issues`, `pulls`, `all` (padrão: `all`)
- `state`: `open`, `closed`, `all` (padrão: `open`)
- `labels`: filtrar por labels (separadas por vírgula)
- `sort`: `created`, `updated`, `comments` (padrão: `updated`)
- `direction`: `asc`, `desc` (padrão: `desc`)
- `per_page`: 1-100 (padrão: 30)
- `page`: número da página (padrão: 1)

### Workflows e CI/CD

#### `GET /api/github/workflows`

Lista workflow runs e métricas de CI/CD.

**Parâmetros de Query:**

- `owner`: proprietário do repositório
- `repo`: nome do repositório
- `workflow_id`: ID do workflow específico
- `actor`: filtrar por usuário
- `branch`: filtrar por branch
- `event`: filtrar por evento (push, pull_request, etc.)
- `status`: `queued`, `in_progress`, `completed`
- `include_metrics`: `true` para incluir métricas detalhadas
- `per_page`: 1-100 (padrão: 30)
- `page`: número da página (padrão: 1)

#### `POST /api/github/workflows`

Obtém métricas detalhadas de CI/CD.

**Body:**

```json
{
  "owner": "usuario",
  "repo": "repositorio",
  "days": 30
}
```

## 💡 Exemplos de Uso

### Verificar Status da Integração

```bash
curl -X GET "http://localhost:3000/api/github/status"
```

### Listar Repositórios Recentes

```bash
curl -X GET "http://localhost:3000/api/github/repositories?sort=updated&per_page=10"
```

### Obter Issues Abertas

```bash
curl -X GET "http://localhost:3000/api/github/issues?type=issues&state=open&owner=usuario&repo=repositorio"
```

### Métricas de CI/CD

```bash
curl -X GET "http://localhost:3000/api/github/workflows?include_metrics=true&owner=usuario&repo=repositorio"
```

## 📊 Monitoramento

### Health Check Integrado

A integração GitHub é automaticamente incluída no health check geral da aplicação:

```bash
curl -X GET "http://localhost:3000/api/health"
```

### Métricas Disponíveis

- **Rate Limit**: Requisições restantes e reset time
- **Repository Access**: Conectividade com repositórios configurados
- **Token Validity**: Validação de permissões do token
- **Recent Activity**: Atividade nas últimas 24 horas
- **CI/CD Health**: Status dos workflows recentes

### Alertas Automáticos

O sistema monitora automaticamente:

- Rate limit próximo do limite (< 100 requisições)
- Falhas consecutivas em workflows (> 2)
- Token expirado ou com permissões insuficientes
- Repositórios inacessíveis

## 🛠️ Desenvolvimento

### Estrutura de Arquivos

```
src/
├── lib/
│   └── github-integration.ts        # Cliente e serviços GitHub
├── app/api/github/
│   ├── status/route.ts              # Status e métricas
│   ├── repositories/route.ts        # Repositórios
│   ├── issues/route.ts              # Issues e PRs
│   └── workflows/route.ts           # CI/CD workflows
└── lib/health-checker.ts            # Health check integrado
```

### Classes Principais

- **`GitHubClient`**: Cliente base para GitHub API REST e GraphQL
- **`GitHubMonitoringService`**: Serviços de monitoramento de alto nível
- **`checkGitHubHealth()`**: Função de health check

### Rate Limiting

A integração implementa controle automático de rate limiting:

```typescript
// Verificar rate limit antes de fazer requisições
const rateLimit = await githubClient.getRateLimit();
if (rateLimit.remaining < 10) {
  // Aguardar reset ou usar cache
  await waitForRateLimit(rateLimit.reset);
}
```

### Retry Logic

Implementa retry automático para:

- Erros temporários de rede (5xx)
- Rate limit exceeded (403)
- Timeouts de conexão

## 🚨 Troubleshooting

### Erro: "GITHUB_TOKEN não configurado"

- Verifique se a variável está definida no `.env.local`
- Confirme que o token é válido no GitHub
- Teste o token manualmente: `curl -H "Authorization: Bearer SEU_TOKEN" https://api.github.com/user`

### Erro: "GitHub token inválido ou sem permissões"

- Verifique se o token não expirou
- Confirme que as permissões necessárias estão habilitadas
- Regenere o token se necessário

### Erro: "Repositório GitHub não acessível"

- Verifique se `GITHUB_OWNER` e `GITHUB_REPO` estão corretos
- Confirme que o token tem acesso ao repositório
- Para repositórios privados, verifique permissões de `repo`

### Rate Limit Exceeded

- A integração aguarda automaticamente o reset
- Considere usar cache para reduzir requisições
- Monitore o uso através do endpoint `/api/github/status`

### Workflows não aparecem

- Verifique se o repositório tem GitHub Actions habilitado
- Confirme que existem workflows na pasta `.github/workflows/`
- Verifique permissões de `actions:read`

### Performance Lenta

- Use parâmetros `per_page` menores para reduzir payload
- Implemente cache local para dados que mudam pouco
- Use GraphQL para queries complexas (implementação futura)

## 📈 Roadmap

### Funcionalidades Planejadas

- **GraphQL Integration**: Queries mais eficientes
- **Webhook Support**: Notificações em tempo real
- **Advanced Analytics**: Métricas de produtividade
- **Team Insights**: Análise de colaboração
- **Security Scanning**: Integração com GitHub Security

### Melhorias Técnicas

- **Caching Strategy**: Redis para cache distribuído
- **Background Jobs**: Processamento assíncrono
- **Real-time Updates**: WebSocket para atualizações live
- **Bulk Operations**: Operações em lote para múltiplos repositórios

## 🔗 Links Úteis

- [GitHub API Documentation](https://docs.github.com/en/rest)
- [GitHub Personal Access Tokens](https://github.com/settings/tokens)
- [GitHub Webhooks](https://docs.github.com/en/developers/webhooks-and-events/webhooks)
- [GitHub Actions API](https://docs.github.com/en/rest/actions)

## 📝 Changelog

### v1.0.0 (2024-01-15)

- ✅ Implementação inicial da integração GitHub MCP
- ✅ Endpoints para repositórios, issues e workflows
- ✅ Health check integrado
- ✅ Rate limiting automático
- ✅ Documentação completa

---

**Desenvolvido para Excel Copilot** | [Documentação Principal](./README.md)
