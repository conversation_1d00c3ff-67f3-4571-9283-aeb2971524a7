# Descrição

Por favor, inclua um resumo das mudanças e o problema relacionado. Inclua também a motivação e o contexto relevantes. Liste quaisquer dependências necessárias para esta mudança.

## Tipo de mudança

Por favor, selecione as opções relevantes e exclua as que não se aplicam:

- [ ] Correção de bug (mudança que corrige um problema sem quebrar compatibilidade)
- [ ] Nova funcionalidade (mudança que adiciona funcionalidade sem quebrar compatibilidade)
- [ ] Mudança com quebra de compatibilidade (correção ou funcionalidade que causaria incompatibilidade)
- [ ] Melhoria de performance
- [ ] Refatoração de código (sem mudança funcional)
- [ ] Documentação
- [ ] Atualização de dependências
- [ ] CI/CD e configurações de infraestrutura

## Como foi testado?

Descreva os testes que você executou para verificar suas mudanças. Forneça instruções para reproduzir os testes e liste quaisquer dependências relevantes para este teste.

- [ ] Testes unitários
- [ ] Testes de integração
- [ ] Testes E2E

## Checklist:

- [ ] Meu código segue as diretrizes de estilo deste projeto
- [ ] Realizei uma auto-revisão do meu próprio código
- [ ] Comentei meu código, principalmente em áreas difíceis de entender
- [ ] Fiz as alterações correspondentes na documentação
- [ ] Minhas mudanças não geram novos warnings ou erros de linter
- [ ] Adicionei testes que provam que minha correção é eficaz ou que minha feature funciona
- [ ] Testes unitários novos e existentes passam localmente com minhas mudanças
- [ ] Qualquer mudança pendente está incluída nesta PR
