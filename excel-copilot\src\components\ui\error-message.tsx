'use client';

import { AlertTriangle, XCircle, Info } from 'lucide-react';
import * as React from 'react';

import { cn } from '@/lib/utils';

// Tipos de erro
export type ErrorType = 'error' | 'warning' | 'info' | 'success';

// Props do componente
export interface ErrorMessageProps {
  type?: ErrorType;
  message: string;
  description?: string;
  className?: string;
  iconClassName?: string;
  _iconClassName?: string;
  _icon?: React.ReactNode;
}

// Props para estilização
interface ErrorStyleProps {
  className: string;
  iconClassName: string;
  icon: React.ReactNode;
}

// Mapeamento de tipos de erro para configurações visuais
const ERROR_STYLES: Record<ErrorType, ErrorStyleProps> = {
  error: {
    className: 'bg-destructive/15 text-destructive',
    iconClassName: 'text-destructive',
    icon: <XCircle className="h-5 w-5" />,
  },
  warning: {
    className: 'bg-warning/15 text-warning',
    iconClassName: 'text-warning',
    icon: <AlertTriangle className="h-5 w-5" />,
  },
  info: {
    className: 'bg-info/15 text-info',
    iconClassName: 'text-info',
    icon: <Info className="h-5 w-5" />,
  },
  success: {
    className: 'bg-success/15 text-success',
    iconClassName: 'text-success',
    icon: <Info className="h-5 w-5" />,
  },
};

/**
 * Componente ErrorMessage
 * Exibe mensagens de erro/aviso/info com estilos consistentes
 */
const ErrorMessage = React.forwardRef<HTMLDivElement, ErrorMessageProps>(
  (
    { type = 'error', message, description, className, iconClassName, _iconClassName, _icon },
    ref
  ) => {
    const styles = ERROR_STYLES[type];
    const finalIconClassName = iconClassName || _iconClassName;
    const icon = _icon || styles.icon;

    return (
      <div
        ref={ref}
        className={cn('flex items-start gap-3 rounded-md border p-3', styles.className, className)}
        role="alert"
      >
        <div className={cn('mt-0.5 shrink-0', styles.iconClassName, finalIconClassName)}>
          {icon}
        </div>
        <div className="grid gap-1">
          <div className="font-medium leading-none tracking-tight">{message}</div>
          {description && <div className="text-sm opacity-80">{description}</div>}
        </div>
      </div>
    );
  }
);

ErrorMessage.displayName = 'ErrorMessage';

/**
 * Componente ErrorMessage com tipagem alternativa
 * Conveniência para componentes que preferem usar "children" em vez de "message"
 */
const ErrorMessageWithChildren = ({
  children,
  ...props
}: Omit<ErrorMessageProps, 'message'> & { children: React.ReactNode }) => {
  return <ErrorMessage message={children as string} {...props} />;
};

// Export both as named and default export
export { ErrorMessage, ErrorMessageWithChildren };
export default ErrorMessage;
