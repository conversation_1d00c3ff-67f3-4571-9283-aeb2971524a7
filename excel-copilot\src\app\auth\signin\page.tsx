'use client';

import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>2, User } from 'lucide-react';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { signIn } from 'next-auth/react';
import { useState, useEffect } from 'react';

import { ThemeToggle } from '@/components/theme-toggle';
import { Button } from '@/components/ui/button';
import { Card, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { ErrorMessage } from '@/components/ui/error-message';
import { ENV } from '@/config/unified-environment';

export default function SignIn() {
  const [isLoading, setIsLoading] = useState<{ [key: string]: boolean }>({});
  const [isMounted, setIsMounted] = useState(false);
  const searchParams = useSearchParams();
  const router = useRouter();

  // Adicionar verificação de null para searchParams
  const callbackUrl = searchParams?.get('callbackUrl') || '/dashboard';
  const error = searchParams?.get('error');
  const errorDescription = searchParams?.get('error_description');

  // Garantir que a página só seja renderizada no cliente
  useEffect(() => {
    setIsMounted(true);
  }, []);

  const handleSignIn = async (provider: string) => {
    setIsLoading(prev => ({ ...prev, [provider]: true }));

    try {
      // Decodificar o callback URL se ele já estiver codificado
      let cleanCallbackUrl = callbackUrl;
      try {
        // Se o callback URL contém %2F, significa que já está codificado
        if (callbackUrl.includes('%2F')) {
          cleanCallbackUrl = decodeURIComponent(callbackUrl);
        }
      } catch {
        // Se falhar ao decodificar, usar o original
        cleanCallbackUrl = callbackUrl;
      }

      // Usar signIn do NextAuth em vez de redirecionamento manual
      const result = await signIn(provider, {
        callbackUrl: cleanCallbackUrl,
        redirect: true,
      });

      // Se chegou até aqui e não houve redirecionamento, algo deu errado
      if (result?.error) {
        console.error('Erro no signin:', result.error);
        setIsLoading(prev => ({ ...prev, [provider]: false }));
      }
    } catch (error) {
      console.error('Erro ao fazer signin:', error);
      setIsLoading(prev => ({ ...prev, [provider]: false }));
    }
  };

  // Função para login de desenvolvimento (bypass)
  const handleDevLogin = () => {
    setIsLoading(prev => ({ ...prev, dev: true }));
    // Em modo de desenvolvimento, simplesmente redireciona para o dashboard
    setTimeout(() => {
      router.push('/dashboard');
    }, 500);
  };

  return (
    <main className="h-screen flex flex-col items-center justify-center p-4 bg-gradient-to-b from-blue-50 via-indigo-50/50 to-white dark:from-gray-900 dark:via-blue-950/20 dark:to-background">
      {/* Background decorations */}
      <div className="absolute inset-0 pointer-events-none overflow-hidden">
        <div className="absolute -top-[30%] -left-[10%] w-[60%] h-[60%] rounded-full bg-gradient-to-br from-blue-400/20 to-indigo-600/5 blur-3xl opacity-70"></div>
        <div className="absolute -bottom-[20%] right-[10%] w-[40%] h-[40%] rounded-full bg-gradient-to-bl from-indigo-400/20 to-blue-600/5 blur-3xl opacity-70"></div>
      </div>

      {/* Theme toggle button */}
      <div className="absolute top-4 right-4 z-10">
        <ThemeToggle />
      </div>

      <div className="relative z-10 w-full max-w-md flex flex-col justify-between">
        {/* Logo e título - versão compacta */}
        <div className="flex items-center justify-center gap-3 mb-5">
          <div className="flex items-center justify-center h-10 w-10 rounded-full bg-primary/10">
            <Sparkles className="h-5 w-5 text-primary" />
          </div>
          <h1 className="text-2xl font-bold">Excel Copilot</h1>
        </div>

        {error && (
          <ErrorMessage
            type="error"
            className="mb-4"
            message={
              error === 'CredentialsSignin'
                ? 'Credenciais inválidas'
                : error === 'OAuthCallback'
                  ? 'Erro durante o processo de autenticação OAuth. Verifique suas configurações.'
                  : error === 'AccessDenied'
                    ? 'Acesso negado. Você rejeitou as permissões ou houve um problema com a conta.'
                    : error === 'callback_error'
                      ? 'Erro no processo de callback. Tente novamente.'
                      : `Erro ao realizar login: ${error}.`
            }
            description={errorDescription || undefined}
          />
        )}

        <Card className="shadow-lg border-primary/10">
          <CardHeader className="pb-2">
            <CardTitle>Entrar</CardTitle>
            <CardDescription>Escolha uma das opções abaixo para acessar sua conta</CardDescription>
          </CardHeader>

          {/* Renderizar os botões somente no lado do cliente */}
          {isMounted ? (
            <>
              <div className="space-y-3 p-6 pt-0 pb-2">
                {/* Botão de acesso de desenvolvimento (aparece apenas com SKIP_AUTH_PROVIDERS=true) */}
                {ENV.FEATURES.SKIP_AUTH_PROVIDERS && (
                  <Button
                    variant="default"
                    className="w-full py-5 bg-gradient-to-r from-blue-600 to-indigo-600"
                    onClick={handleDevLogin}
                    disabled={isLoading['dev']}
                  >
                    {isLoading['dev'] ? (
                      <Loader2 className="h-5 w-5 animate-spin" />
                    ) : (
                      <>
                        <User className="mr-2 h-5 w-5" />
                        Acesso Direto (Desenvolvimento)
                      </>
                    )}
                  </Button>
                )}

                <Button
                  variant="outline"
                  className="w-full py-5 relative"
                  onClick={() => handleSignIn('google')}
                  disabled={isLoading['google']}
                >
                  {isLoading['google'] ? (
                    <Loader2 className="h-5 w-5 animate-spin" />
                  ) : (
                    <>
                      <svg className="mr-2 h-5 w-5" viewBox="0 0 24 24" aria-hidden="true">
                        <path
                          d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                          fill="#4285F4"
                        />
                        <path
                          d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                          fill="#34A853"
                        />
                        <path
                          d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                          fill="#FBBC05"
                        />
                        <path
                          d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                          fill="#EA4335"
                        />
                      </svg>
                      Continuar com Google
                    </>
                  )}
                </Button>

                <Button
                  variant="outline"
                  className="w-full py-5"
                  onClick={() => handleSignIn('github')}
                  disabled={isLoading['github']}
                >
                  {isLoading['github'] ? (
                    <Loader2 className="h-5 w-5 animate-spin" />
                  ) : (
                    <>
                      <Github className="mr-2 h-5 w-5" />
                      Continuar com GitHub
                    </>
                  )}
                </Button>
              </div>
            </>
          ) : (
            <div className="p-6 pt-0 pb-2">
              <div className="h-12 w-full rounded bg-gray-100 dark:bg-gray-800 animate-pulse mb-3"></div>
              <div className="h-12 w-full rounded bg-gray-100 dark:bg-gray-800 animate-pulse mb-3"></div>
            </div>
          )}

          <CardFooter className="pt-2">
            <div className="w-full text-xs text-center text-muted-foreground">
              <p className="mb-3">
                Ainda não tem uma conta?{' '}
                <Link href="/pricing" className="text-primary font-medium hover:underline">
                  Conheça nossos planos
                </Link>
              </p>
              <p>
                Ao continuar, você concorda com nossos{' '}
                <Link href="/terms" className="underline underline-offset-4 hover:text-primary">
                  Termos
                </Link>{' '}
                e{' '}
                <Link href="/privacy" className="underline underline-offset-4 hover:text-primary">
                  Privacidade
                </Link>
              </p>
            </div>
          </CardFooter>
        </Card>
      </div>
    </main>
  );
}
