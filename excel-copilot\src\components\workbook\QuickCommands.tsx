'use client';

import { FileSpreadsheet, <PERSON><PERSON><PERSON>, ArrowRight } from 'lucide-react';
import { memo } from 'react';

import { Button } from '@/components/ui/button';

// Exemplos de comandos comuns para sugestões rápidas
const QUICK_COMMAND_EXAMPLES = [
  { text: 'Crie uma tabela de controle de horas', icon: <FileSpreadsheet className="h-3 w-3" /> },
  { text: 'Adicione validação de dados na coluna B', icon: <ArrowRight className="h-3 w-3" /> },
  { text: 'Gere um gráfico de barras com os dados', icon: <BarChart className="h-3 w-3" /> },
  { text: 'Calcule a média da coluna C', icon: <ArrowRight className="h-3 w-3" /> },
  { text: 'Formate a tabela com cores alternadas', icon: <ArrowRight className="h-3 w-3" /> },
];

interface QuickCommandsProps {
  onCommandClick: (command: string) => void;
  disabled?: boolean;
}

// Memoizando comandos rápidos para evitar recriação desnecessária
const MemoizedQuickCommandButton = memo<{
  command: { text: string; icon: React.ReactNode };
  onClick: () => void;
  disabled?: boolean;
}>(({ command, onClick, disabled = false }) => (
  <Button
    variant="ghost"
    className="h-8 px-2 text-sm justify-start w-full hover:bg-accent disabled:opacity-50"
    onClick={onClick}
    disabled={disabled}
  >
    <span className="mr-2">{command.icon}</span>
    <span className="truncate">{command.text}</span>
  </Button>
));
MemoizedQuickCommandButton.displayName = 'MemoizedQuickCommandButton';

/**
 * Componente de comandos rápidos otimizado
 * Memoizado para evitar re-renders desnecessários
 */
export const QuickCommands = memo<QuickCommandsProps>(({ onCommandClick, disabled = false }) => {
  return (
    <div className="space-y-1">
      <h4 className="text-sm font-medium text-muted-foreground mb-2">Comandos Rápidos</h4>
      {QUICK_COMMAND_EXAMPLES.map((command, index) => (
        <MemoizedQuickCommandButton
          key={`${command.text}-${index}`}
          command={command}
          onClick={() => onCommandClick(command.text)}
          disabled={disabled}
        />
      ))}
    </div>
  );
});

QuickCommands.displayName = 'QuickCommands';
