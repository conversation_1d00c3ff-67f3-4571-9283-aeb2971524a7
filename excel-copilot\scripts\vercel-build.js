/**
 * Script personalizado para build na Vercel
 * Este script configura o ambiente corretamente antes do build
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Verificar se estamos na Vercel
const isVercel = process.env.VERCEL === '1';

console.log('🚀 Iniciando build customizado para Vercel...');

// Verificar se temos todas as variáveis necessárias para usar IA real
const hasVertexCredentials =
  process.env.AI_VERTEX_PROJECT_ID &&
  process.env.AI_VERTEX_LOCATION &&
  (process.env.VERTEX_AI_CREDENTIALS || process.env.VERTEX_AI_API_KEY);

// Definir modo automaticamente com base nas variáveis disponíveis
const useMockAI = !hasVertexCredentials;

if (useMockAI) {
  console.log('⚠️ Variáveis do Vertex AI incompletas. Usando modo MOCK para o build.');
  // Configurar variáveis de ambiente para o build com mocks
  process.env.AI_USE_MOCK = 'true';
  process.env.AI_ENABLED = 'true';
  process.env.AI_USE_MOCK = 'true';
  process.env.NEXT_PUBLIC_USE_MOCK_AI = 'true';
} else {
  console.log('✅ Variáveis do Vertex AI encontradas. Usando modo REAL para IA.');
  // Configurar variáveis de ambiente para o build com IA real
  process.env.AI_USE_MOCK = 'false';
  process.env.AI_ENABLED = 'false';
  process.env.AI_USE_MOCK = 'false';
  process.env.NEXT_PUBLIC_USE_MOCK_AI = 'false';
}

// Configurar variáveis padrão para autenticação
process.env.AUTH_SKIP_PROVIDERS = 'false';
process.env.NEXT_PUBLIC_SKIP_AUTH_PROVIDERS = 'false';

// Configurar variáveis de ambiente críticas com valores padrão se não existirem
if (!process.env.AUTH_NEXTAUTH_SECRET) {
  process.env.AUTH_NEXTAUTH_SECRET = 'default-secret-for-build-only';
  console.log('⚠️ NEXTAUTH_SECRET não configurado, usando valor padrão para build');
}

if (!process.env.AUTH_NEXTAUTH_URL) {
  process.env.AUTH_NEXTAUTH_URL = 'https://excel-copilot-eight.vercel.app';
  console.log('⚠️ NEXTAUTH_URL não configurado, usando valor padrão para build');
}

if (!process.env.DB_DATABASE_URL) {
  // Use uma URL de banco de dados em memória para o build, em vez de apontar para localhost
  process.env.DB_DATABASE_URL =
    '****************************************/fakedatabase?schema=public';
  // Alternativa: 'file:./dev.db'
  console.log('⚠️ DATABASE_URL não configurado, usando valor falso para build');
}

// Defina explicitamente que este é o ambiente de build Vercel
process.env.VERCEL_BUILD = 'true';
process.env.SKIP_DB_CHECK = 'true';
process.env.PRISMA_SKIP_DATABASE_CALLS = 'true';

// Verificar variáveis de autenticação OAuth
console.log('📋 Verificando configurações de autenticação...');
const missingOAuth = [];
if (!process.env.AUTH_GOOGLE_CLIENT_ID) missingOAuth.push('AUTH_GOOGLE_CLIENT_ID');
if (!process.env.AUTH_GOOGLE_CLIENT_SECRET) missingOAuth.push('AUTH_GOOGLE_CLIENT_SECRET');
if (!process.env.AUTH_GITHUB_CLIENT_ID) missingOAuth.push('AUTH_GITHUB_CLIENT_ID');
if (!process.env.AUTH_GITHUB_CLIENT_SECRET) missingOAuth.push('AUTH_GITHUB_CLIENT_SECRET');

if (missingOAuth.length > 0) {
  console.log(`⚠️ Variáveis OAuth ausentes: ${missingOAuth.join(', ')}`);

  // Valores padrão para build (mesmo que fictícios)
  if (!process.env.AUTH_GOOGLE_CLIENT_ID)
    process.env.AUTH_GOOGLE_CLIENT_ID = 'mock-client-id-for-build';
  if (!process.env.AUTH_GOOGLE_CLIENT_SECRET)
    process.env.AUTH_GOOGLE_CLIENT_SECRET = 'mock-client-secret-for-build';
  if (!process.env.AUTH_GITHUB_CLIENT_ID)
    process.env.AUTH_GITHUB_CLIENT_ID = 'mock-client-id-for-build';
  if (!process.env.AUTH_GITHUB_CLIENT_SECRET)
    process.env.AUTH_GITHUB_CLIENT_SECRET = 'mock-client-secret-for-build';

  // Forçar skip de auth providers se OAuth não configurado
  process.env.AUTH_SKIP_PROVIDERS = 'true';
  process.env.NEXT_PUBLIC_SKIP_AUTH_PROVIDERS = 'true';
}

// Verificar e configurar valores padrão para Vertex AI se estiver no modo mock
if (useMockAI) {
  if (!process.env.AI_VERTEX_PROJECT_ID)
    process.env.AI_VERTEX_PROJECT_ID = 'mock-vertex-project-id';
  if (!process.env.AI_VERTEX_LOCATION) process.env.AI_VERTEX_LOCATION = 'us-central1';

  // Criar diretório de mocks se necessário
  const mockDir = path.resolve(__dirname, '../src/lib/ai');
  if (!fs.existsSync(mockDir)) {
    console.log('📁 Criando diretório para mocks de AI...');
    fs.mkdirSync(mockDir, { recursive: true });
  }

  // Criar arquivo de mock
  const mockFilePath = path.resolve(mockDir, 'mock-modules.js');
  const mockContent = `
  // Mock módulo para substituir as APIs do Google
  module.exports = {
    VertexAI: class MockVertexAI {
      constructor() {}
      getGenerativeModel() { 
        return { 
          generateContent: async () => ({ response: { text: () => "Este é um texto gerado pelo mock." }}),
          streamGenerateContent: async () => ({ stream: [] })
        };
      }
    },
    GoogleGenAI: class MockGoogleGenAI {
      constructor() {}
      getGenerativeModel() { 
        return { 
          generateContent: async () => ({ response: { text: () => "Este é um texto gerado pelo mock." }}),
          streamGenerateContent: async () => ({ stream: [] })
        };
      }
    },
    HarmCategory: {},
    HarmBlockThreshold: {}
  };`;

  console.log('📝 Criando arquivo de mock para APIs do Google...');
  fs.writeFileSync(mockFilePath, mockContent);
}

// Garantir que o env-validator esteja exportando 'env'
const envValidatorPath = path.resolve(__dirname, '../src/lib/env-validator.ts');
if (fs.existsSync(envValidatorPath)) {
  let envValidatorContent = fs.readFileSync(envValidatorPath, 'utf8');

  // Verificar se já existe a exportação de 'env'
  if (!envValidatorContent.includes('export const env')) {
    console.log('🔧 Atualizando env-validator.ts para exportar env...');
    const envExport = `
// Exportar uma versão compatível da configuração para uso em ambientes Edge
export const env = {
  GOOGLE_CLIENT_ID: process.env.AUTH_GOOGLE_CLIENT_ID || '',
  GOOGLE_CLIENT_SECRET: process.env.AUTH_GOOGLE_CLIENT_SECRET || '',
  GITHUB_CLIENT_ID: process.env.AUTH_GITHUB_CLIENT_ID || '',
  GITHUB_CLIENT_SECRET: process.env.AUTH_GITHUB_CLIENT_SECRET || '',
  NEXTAUTH_SECRET: process.env.AUTH_NEXTAUTH_SECRET || '',
  NEXTAUTH_URL: process.env.AUTH_NEXTAUTH_URL || '',
  DATABASE_URL: process.env.DB_DATABASE_URL || '',
  VERTEX_AI_PROJECT_ID: process.env.AI_VERTEX_PROJECT_ID || '',
  VERTEX_AI_LOCATION: process.env.AI_VERTEX_LOCATION || '',
};
`;

    // Inserir após as importações
    const importEndIdx = envValidatorContent.lastIndexOf('import');
    const lineAfterImports = envValidatorContent.indexOf('\n', importEndIdx);

    if (lineAfterImports !== -1) {
      envValidatorContent =
        envValidatorContent.slice(0, lineAfterImports + 1) +
        envExport +
        envValidatorContent.slice(lineAfterImports + 1);

      fs.writeFileSync(envValidatorPath, envValidatorContent);
    }
  }
}

// Criar arquivo .env.local temporário
console.log('📝 Criando arquivo .env.local temporário para o build...');
const envPath = path.resolve(__dirname, '../.env.local');
const envContent = `
# Arquivo .env.local temporário gerado para build
DATABASE_URL=${process.env.DB_DATABASE_URL || ''}
DIRECT_URL=${process.env.DB_DATABASE_URL || ''}
PRISMA_SKIP_DATABASE_CALLS=true
NEXTAUTH_SECRET=${process.env.AUTH_NEXTAUTH_SECRET || ''}
NEXTAUTH_URL=${process.env.AUTH_NEXTAUTH_URL || ''}
GOOGLE_CLIENT_ID=${process.env.AUTH_GOOGLE_CLIENT_ID || ''}
GOOGLE_CLIENT_SECRET=${process.env.AUTH_GOOGLE_CLIENT_SECRET || ''}
GITHUB_CLIENT_ID=${process.env.AUTH_GITHUB_CLIENT_ID || ''}
GITHUB_CLIENT_SECRET=${process.env.AUTH_GITHUB_CLIENT_SECRET || ''}
VERTEX_AI_PROJECT_ID=${process.env.AI_VERTEX_PROJECT_ID || ''}
VERTEX_AI_LOCATION=${process.env.AI_VERTEX_LOCATION || 'us-central1'}
VERTEX_AI_ENABLED=${useMockAI ? 'false' : 'true'}
USE_MOCK_AI=${useMockAI ? 'true' : 'false'}
SKIP_AUTH_PROVIDERS=${process.env.AUTH_SKIP_PROVIDERS}
NEXT_PUBLIC_USE_MOCK_AI=${useMockAI ? 'true' : 'false'}
NEXT_PUBLIC_SKIP_AUTH_PROVIDERS=${process.env.NEXT_PUBLIC_SKIP_AUTH_PROVIDERS}
NODE_ENV=production
NEXT_PUBLIC_VERCEL_ENV=production
VERCEL_ENV=production
VERCEL=1
VERCEL_BUILD=true
SKIP_DB_CHECK=true
${useMockAI ? 'MOCK_ENABLED_BUILD=true' : ''}
IGNORE_TS_CONFIG_PATHS=true
DISABLE_EDGE_RUNTIME=true
`;

fs.writeFileSync(envPath, envContent);

// Executar o build do Next.js
console.log('🔨 Executando build do Next.js...');
try {
  execSync('next build', { stdio: 'inherit' });
  console.log('✅ Build concluído com sucesso!');
  process.exit(0);
} catch (error) {
  console.error('❌ Erro durante o build:', error);
  process.exit(1);
}
