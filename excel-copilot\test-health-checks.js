#!/usr/bin/env node

/**
 * Script para testar os health checks
 */

console.log('🏥 TESTE DOS HEALTH CHECKS - EXCEL COPILOT');
console.log('='.repeat(60));
console.log();

async function testHealthChecks() {
  try {
    // Testar importação
    console.log('📦 Testando importação...');

    // Simular o ambiente Next.js
    process.env.NODE_ENV = 'development';

    // Tentar importar o sistema de health checks
    const healthChecks = await import('./src/lib/health-checks/index.ts');

    console.log('✅ Importação bem-sucedida');
    console.log('📋 Funções disponíveis:', Object.keys(healthChecks));

    // Testar função checkAllServices
    if (typeof healthChecks.checkAllServices === 'function') {
      console.log('✅ checkAllServices está disponível');

      console.log('🔍 Executando health check completo...');
      const result = await healthChecks.checkAllServices();

      console.log('📊 Resultado:');
      console.log('  Status geral:', result.overall);
      console.log('  Serviços verificados:', result.services.length);
      console.log('  Tempo total:', result.responseTime + 'ms');

      result.services.forEach(service => {
        const icon =
          service.status === 'healthy' ? '✅' : service.status === 'degraded' ? '⚠️' : '❌';
        console.log(`  ${icon} ${service.service}: ${service.status} (${service.responseTime}ms)`);
      });
    } else {
      console.log('❌ checkAllServices não está disponível');
      console.log('Tipo:', typeof healthChecks.checkAllServices);
    }
  } catch (error) {
    console.error('❌ Erro durante teste:', error.message);
    console.error('Stack:', error.stack);
  }
}

testHealthChecks()
  .then(() => {
    console.log();
    console.log('✅ Teste concluído!');
  })
  .catch(error => {
    console.error('❌ Erro fatal:', error);
    process.exit(1);
  });
