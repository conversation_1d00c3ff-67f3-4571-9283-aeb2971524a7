import { NextRequest, NextResponse } from 'next/server';

import { authOptions } from '@/server/auth/options';

export const dynamic = 'force-dynamic';

interface ProviderWithCredentials {
  clientId?: string;
  clientSecret?: string;
}

export async function GET(_request: NextRequest) {
  try {
    // Test login logging

    // Verificar se os provedores estão configurados corretamente
    const providers = authOptions.providers;

    const testResults = {
      timestamp: new Date().toISOString(),
      providersCount: providers.length,
      providers: providers.map(provider => ({
        id: provider.id,
        name: provider.name,
        type: provider.type,
        // Não expor secrets, apenas verificar se existem
        hasClientId: !!(provider as ProviderWithCredentials).clientId,
        hasClientSecret: !!(provider as ProviderWithCredentials).clientSecret,
        clientIdLength: (provider as ProviderWithCredentials).clientId?.length || 0,
        clientSecretLength: (provider as ProviderWithCredentials).clientSecret?.length || 0,
      })),
      authOptions: {
        hasAdapter: !!authOptions.adapter,
        hasSecret: !!authOptions.secret,
        sessionStrategy: authOptions.session?.strategy,
        hasCallbacks: !!authOptions.callbacks,
        hasPages: !!authOptions.pages,
        debug: authOptions.debug,
      },
      issues: [] as string[],
      recommendations: [] as string[],
    };

    // Verificar cada provedor
    providers.forEach(provider => {
      const providerData = provider as ProviderWithCredentials;

      if (!providerData.clientId) {
        testResults.issues.push(`${provider.id}: Client ID não configurado`);
      } else if (providerData.clientId.length < 10) {
        testResults.issues.push(`${provider.id}: Client ID muito curto`);
      }

      if (!providerData.clientSecret) {
        testResults.issues.push(`${provider.id}: Client Secret não configurado`);
      } else if (providerData.clientSecret.length < 10) {
        testResults.issues.push(`${provider.id}: Client Secret muito curto`);
      }
    });

    // Verificar configurações gerais
    if (!authOptions.secret) {
      testResults.issues.push('NEXTAUTH_SECRET não configurado');
    }

    if (!authOptions.adapter) {
      testResults.issues.push('Adapter do banco de dados não configurado');
    }

    // Testar se conseguimos criar URLs de signin
    try {
      const baseUrl = process.env.AUTH_NEXTAUTH_URL || 'https://excel-copilot-eight.vercel.app';

      testResults.providers.forEach((_provider, index) => {
        const originalProvider = providers[index];
        if (originalProvider) {
          const signinUrl = `${baseUrl}/api/auth/signin/${originalProvider.id}`;
          (testResults.providers[index] as Record<string, unknown>).signinUrl = signinUrl;
        }
      });
    } catch (error) {
      testResults.issues.push(
        `Erro ao gerar URLs de signin: ${error instanceof Error ? error.message : error}`
      );
    }

    // Adicionar recomendações baseadas nos problemas
    if (testResults.issues.length > 0) {
      testResults.recommendations.push('Verificar variáveis de ambiente no Vercel');
      testResults.recommendations.push('Confirmar configurações OAuth nos provedores');
      testResults.recommendations.push('Verificar se as URLs de callback estão corretas');
    }

    // Test login completed

    return NextResponse.json(testResults, {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
      },
    });
  } catch (error) {
    // Error handling for test login

    return NextResponse.json(
      {
        error: 'Erro interno durante teste de login',
        message: error instanceof Error ? error.message : 'Erro desconhecido',
        stack: error instanceof Error ? error.stack : undefined,
        timestamp: new Date().toISOString(),
      },
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
}
