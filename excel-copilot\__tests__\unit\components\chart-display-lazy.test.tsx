import React from 'react';
import { render, screen } from '@testing-library/react';
import ChartDisplayLazy from '@/components/chart-display-lazy';

// Mockando o componente lazy-loaded
jest.mock('@/components/chart-display', () => ({
  ChartDisplay: ({ title }: { title: string }) => <div data-testid="mocked-chart">{title}</div>,
}));

describe('ChartDisplayLazy', () => {
  it('deve renderizar um loading state inicialmente', () => {
    render(
      <ChartDisplayLazy
        data={{
          labels: ['Test'],
          datasets: [{ label: 'Test', data: [1] }],
        }}
        title="Test Chart"
      />
    );

    // Deve mostrar o indicador de carregamento
    expect(screen.getByText('Carregando gráfico...')).toBeInTheDocument();
  });

  it('deve renderizar o componente ChartDisplay quando carregado', async () => {
    render(
      <ChartDisplayLazy
        data={{
          labels: ['Test'],
          datasets: [{ label: 'Test', data: [1] }],
        }}
        title="Test Chart"
      />
    );

    // Aguardar pelo componente real ser renderizado após o lazy loading
    const chartElement = await screen.findByTestId('mocked-chart');
    expect(chartElement).toBeInTheDocument();
    expect(chartElement).toHaveTextContent('Test Chart');
  });
});
