/**
 * 🔍 HEALTH CHECK DEBUG ENDPOINT
 *
 * GET /api/health/debug
 *
 * Endpoint para debugar problemas de importação dos health checks
 */

import { NextRequest, NextResponse } from 'next/server';

// Tipos para os resultados de importação
interface ImportResult {
  success: boolean;
  exports?: string[];
  hasCheckAllServices?: boolean;
  hasCheckCriticalServices?: boolean;
  hasHealthManager?: boolean;
  error?: string;
}

export async function GET(_request: NextRequest) {
  try {
    const timestamp = new Date().toISOString();

    // Tentar importar o sistema de health checks
    let importResult: ImportResult = {
      success: false,
    };

    try {
      const healthChecks = await import('@/lib/health-checks');
      importResult = {
        success: true,
        exports: Object.keys(healthChecks),
        hasCheckAllServices:
          typeof (healthChecks as Record<string, unknown>).checkAllServices === 'function',
        hasCheckCriticalServices:
          typeof (healthChecks as Record<string, unknown>).checkCriticalServices === 'function',
        hasHealthManager: !!(healthChecks as Record<string, unknown>).healthManager,
      };
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      importResult = {
        success: false,
        error: errorMessage,
      };
    }

    // Tentar importar diretamente do index
    let indexImportResult: ImportResult = {
      success: false,
    };
    try {
      const healthChecksIndex = await import('@/lib/health-checks/index');
      indexImportResult = {
        success: true,
        exports: Object.keys(healthChecksIndex),
        hasCheckAllServices:
          typeof (healthChecksIndex as Record<string, unknown>).checkAllServices === 'function',
        hasCheckCriticalServices:
          typeof (healthChecksIndex as Record<string, unknown>).checkCriticalServices ===
          'function',
      };
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      indexImportResult = {
        success: false,
        error: errorMessage,
      };
    }

    const debugResult = {
      status: 'debug',
      timestamp,
      environment: process.env.NODE_ENV,
      imports: {
        healthChecks: importResult,
        healthChecksIndex: indexImportResult,
      },
      availableEnvVars: {
        database: !!process.env.DB_DATABASE_URL,
        nextAuthSecret: !!process.env.AUTH_NEXTAUTH_SECRET,
        stripeSecret: !!process.env.STRIPE_SECRET_KEY,
        vertexAiProject: !!process.env.AI_VERTEX_PROJECT_ID,
        vercelToken: !!(process.env.MCP_VERCEL_TOKEN || process.env.MCP_VERCEL_TOKEN),
      },
    };

    return NextResponse.json(debugResult, {
      status: 200,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        Pragma: 'no-cache',
        Expires: '0',
      },
    });
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    const errorStack = error instanceof Error ? error.stack : undefined;

    return NextResponse.json(
      {
        status: 'error',
        timestamp: new Date().toISOString(),
        error: errorMessage,
        stack: errorStack,
      },
      {
        status: 500,
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
        },
      }
    );
  }
}

// Permitir apenas GET
export async function POST() {
  return NextResponse.json({ error: 'Method not allowed' }, { status: 405 });
}

export async function PUT() {
  return NextResponse.json({ error: 'Method not allowed' }, { status: 405 });
}

export async function DELETE() {
  return NextResponse.json({ error: 'Method not allowed' }, { status: 405 });
}
