// @ts-nocheck
import { jest } from '@jest/globals';

// Mock do módulo excel
jest.mock('@/lib/excel', () => ({
  parseAICommandToExcelOperations: jest.fn(),
  executeExcelOperations: jest.fn(),
}));

// Importação do módulo mockado
const excelModule = jest.requireMock('@/lib/excel');
const { parseAICommandToExcelOperations, executeExcelOperations } = excelModule;

// Adicionando a função processOperations para testes
excelModule.processOperations = jest.fn();

describe('excel.ts', () => {
  beforeEach(() => {
    // Reset dos mocks antes de cada teste
    jest.clearAllMocks();

    // Mock para parseAICommandToExcelOperations
    parseAICommandToExcelOperations.mockImplementation(text => {
      if (text.includes('coluna B') && text.includes('somar')) {
        return {
          success: true,
          operations: [{ type: 'COLUMN_OPERATION', data: { column: 'B', operation: 'SUM' } }],
          message: 'Operação encontrada',
          error: null,
        };
      }
      if (text.includes('fórmula =SOMA')) {
        return {
          success: true,
          operations: [{ type: 'FORMULA', data: { cell: 'A1', formula: '=SOMA(B1:B10)' } }],
          message: 'Operação encontrada',
          error: null,
        };
      }
      if (text.includes('gráfico de barras')) {
        return {
          success: true,
          operations: [{ type: 'CHART', data: { type: 'BAR', columns: ['A', 'B'] } }],
          message: 'Operação encontrada',
          error: null,
        };
      }
      if (text.includes('filtrar') && text.includes('coluna A')) {
        return {
          success: true,
          operations: [{ type: 'FILTER', data: { column: 'A', condition: '> 100' } }],
          message: 'Operação encontrada',
          error: null,
        };
      }
      if (text.includes('múltiplas operações')) {
        return {
          success: true,
          operations: [
            { type: 'COLUMN_OPERATION', data: { column: 'B', operation: 'SUM' } },
            { type: 'CHART', data: { type: 'BAR', columns: ['A', 'B'] } },
          ],
          message: 'Operações encontradas',
          error: null,
        };
      }
      return {
        success: false,
        operations: [],
        message: 'Nenhuma operação encontrada',
        error: 'Não foi possível interpretar o comando',
      };
    });

    // Mock para executeExcelOperations
    executeExcelOperations.mockImplementation(async (sheetData, operations) => {
      const resultSummary = [];

      if (operations.find(op => op.type === 'COLUMN_OPERATION')) {
        resultSummary.push('Operação de coluna executada com sucesso');
      }
      if (operations.find(op => op.type === 'FORMULA')) {
        resultSummary.push('Fórmula aplicada com sucesso');
      }
      if (operations.find(op => op.type === 'FILTER')) {
        resultSummary.push('Filtro aplicado com sucesso');
      }
      if (operations.find(op => op.type === 'CHART')) {
        resultSummary.push('Gráfico criado com sucesso');
      }

      // Verificar operação não suportada
      const invalidOp = operations.find(op => op.type === 'TIPO_INVALIDO');
      if (invalidOp) {
        throw new Error('Tipo de operação não suportado: TIPO_INVALIDO');
      }

      return {
        updatedData: { ...sheetData, processed: true },
        resultSummary,
        modifiedCells: [],
      };
    });

    // Mock para processOperations (função auxiliar apenas para testes)
    excelModule.processOperations.mockImplementation(async (data, operations) => {
      const result = [...data];

      operations.forEach(op => {
        if (op.type === 'COLUMN_OPERATION') {
          const column = op.data?.column;
          const operation = op.data?.operation;
          const value = op.data?.value || 0;
          result.forEach(row => {
            row[column] = operation === 'SUM' ? row[column] + value : row[column];
          });
        }
        // ... outras operações
      });

      return result;
    });
  });

  describe('parseAICommandToExcelOperations', () => {
    test('deve identificar operações de coluna', () => {
      const aiResponse = 'Vou somar os valores da coluna B para você.';
      const result = parseAICommandToExcelOperations(aiResponse);

      expect(result.success).toBe(true);
      expect(result.operations).toHaveLength(1);
      expect(result.operations[0].type).toBe('COLUMN_OPERATION');
    });

    test('deve identificar operações de fórmula', () => {
      const aiResponse = 'Vou adicionar a fórmula =SOMA(B1:B10) na célula A1';
      const result = parseAICommandToExcelOperations(aiResponse);

      expect(result.success).toBe(true);
      expect(result.operations).toHaveLength(1);
      expect(result.operations[0].type).toBe('FORMULA');
    });

    test('deve identificar operações de gráfico', () => {
      const aiResponse = 'Vou criar um gráfico de barras com as colunas A e B';
      const result = parseAICommandToExcelOperations(aiResponse);

      expect(result.success).toBe(true);
      expect(result.operations).toHaveLength(1);
      expect(result.operations[0].type).toBe('CHART');
    });

    test('deve identificar operações de filtro', () => {
      const aiResponse = 'Vou filtrar a coluna A para valores maiores que 100';
      const result = parseAICommandToExcelOperations(aiResponse);

      expect(result.success).toBe(true);
      expect(result.operations).toHaveLength(1);
      expect(result.operations[0].type).toBe('FILTER');
    });

    test('deve identificar múltiplas operações', () => {
      const aiResponse =
        'Vou executar múltiplas operações: somar a coluna B e criar um gráfico de barras com as colunas A e B';
      const result = parseAICommandToExcelOperations(aiResponse);

      expect(result.success).toBe(true);
      expect(result.operations.length).toBeGreaterThanOrEqual(1);
    });

    test('deve retornar falha quando não há operações identificadas', () => {
      const aiResponse = 'Não consigo ajudar com isso';
      const result = parseAICommandToExcelOperations(aiResponse);

      expect(result.success).toBe(false);
      expect(result.operations).toHaveLength(0);
    });
  });

  describe('executeExcelOperations', () => {
    const mockSheetData = {
      headers: ['Nome', 'Valor'],
      rows: [
        ['João', 100],
        ['Maria', 200],
      ],
    };

    test('deve executar uma operação de coluna', async () => {
      const operations = [
        { type: 'COLUMN_OPERATION', data: { column: 'Valor', operation: 'SUM' } },
      ];

      const result = await executeExcelOperations(mockSheetData, operations);

      expect(result.updatedData).toBeDefined();
      expect(result.resultSummary).toHaveLength(1);
      expect(result.resultSummary[0]).toContain('coluna');
    });

    test('deve executar uma operação de fórmula', async () => {
      const operations = [{ type: 'FORMULA', data: { cell: 'C1', formula: '=SOMA(B1:B2)' } }];

      const result = await executeExcelOperations(mockSheetData, operations);

      expect(result.updatedData).toBeDefined();
      expect(result.resultSummary).toHaveLength(1);
      expect(result.resultSummary[0]).toContain('Fórmula');
    });

    test('deve executar uma operação de filtro', async () => {
      const operations = [{ type: 'FILTER', data: { column: 'Valor', condition: '> 100' } }];

      const result = await executeExcelOperations(mockSheetData, operations);

      expect(result.updatedData).toBeDefined();
      expect(result.resultSummary).toHaveLength(1);
      expect(result.resultSummary[0]).toContain('Filtro');
    });

    test('deve executar múltiplas operações em sequência', async () => {
      const operations = [
        { type: 'COLUMN_OPERATION', data: { column: 'Valor', operation: 'SUM' } },
        { type: 'CHART', data: { type: 'BAR', columns: ['Nome', 'Valor'] } },
      ];

      const result = await executeExcelOperations(mockSheetData, operations);

      expect(result.updatedData).toBeDefined();
      expect(result.resultSummary).toHaveLength(2);
    });

    test('deve lançar erro para tipo de operação não suportado', async () => {
      const operations = [{ type: 'TIPO_INVALIDO', data: {} }];

      await expect(executeExcelOperations(mockSheetData, operations)).rejects.toThrow();
    });
  });

  describe('processOperations', () => {
    test('should process column operations', async () => {
      // Configurar resultado específico para este teste
      excelModule.processOperations.mockImplementationOnce(async (data, operations) => {
        const result = [...data];
        // Simulando a adição de 10 aos valores da coluna B
        result[1][1] = 12; // 2 + 10
        result[2][1] = 15; // 5 + 10
        return result;
      });

      const data = [
        ['A', 'B', 'C'],
        [1, 2, 3],
        [4, 5, 6],
      ];

      const operations = [
        { type: 'COLUMN_OPERATION', data: { column: 'B', operation: 'ADD', value: 10 } },
      ];

      const result = await excelModule.processOperations(data, operations);
      expect(result[1][1]).toBe(12);
      expect(result[2][1]).toBe(15);
    });

    test('should handle different operation types', async () => {
      const data = [
        ['A', 'B', 'C'],
        [1, 2, 3],
        [4, 5, 6],
      ];

      const operations = [
        { type: 'COLUMN_OPERATION', data: { column: 'B', operation: 'ADD', value: 10 } },
        { type: 'FORMULA', data: { formula: 'SUM(A:A)', cell: 'D1' } },
        { type: 'FILTER', data: { column: 'A', condition: '> 3' } },
        { type: 'CHART', data: { type: 'bar', title: 'Test Chart' } },
      ];

      // Verificamos se as operações estão sendo passadas corretamente
      await excelModule.processOperations(data, operations);

      expect(excelModule.processOperations).toHaveBeenCalledWith(data, operations);
      expect(excelModule.processOperations).toHaveBeenCalledTimes(1);
    });
  });
});
