/**
 * Definições de tipos especiais para testes
 * Compatibiliza os tipos de mock e MSW para evitar erros de TypeScript
 */

import { Response } from 'node-fetch';

// Tipos para Rest Handlers do MSW
export interface MockRequest {
  url: URL;
  params: Record<string, string>;
  cookies: Record<string, string>;
  headers: Headers;
  body: any;
}

export interface MockResponse<T = any> {
  status: number | ((code: number) => MockResponse<T>);
  json: (body: T) => Response;
  text: (body: string) => Response;
  headers: Headers;
}

export interface MockContext {
  status: (statusCode: number) => { json: (body: any) => Response };
  json: (body: any) => Response;
  text: (body: string) => Response;
  delay: (ms: number) => MockContext;
  set: (headerName: string, value: string) => MockContext;
}

// Tipos para funções de teste
export interface TestResponse extends Response {
  status: number;
  json: () => Promise<any>;
  text: () => Promise<string>;
}

// Ampliação de tipos para Jest
declare global {
  namespace jest {
    interface Matchers<R> {
      toBeValidWorkbook(): R;
      toContainSheet(sheetName: string): R;
      toHavePermission(userId: string): R;
    }
  }
}
