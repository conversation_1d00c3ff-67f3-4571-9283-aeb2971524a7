# ======================================
# CONFIGURAÇÃO DE AMBIENTE PARA TESTES
# ======================================
# Este arquivo é usado apenas durante a execução dos testes

# ======================================
# Ambiente
# ======================================
NODE_ENV="test"

# ======================================
# Banco de Dados de Teste
# ======================================
DB_DATABASE_URL="postgresql://test_user:test_password@localhost:5432/excel_copilot_test"
DB_PROVIDER="postgresql"

# ======================================
# NextAuth.js - Testes
# ======================================
AUTH_NEXTAUTH_SECRET="test-secret-key-for-testing-only"
AUTH_NEXTAUTH_URL="http://localhost:3000"

# ======================================
# OAuth Providers - Mock para Testes
# ======================================
AUTH_GOOGLE_CLIENT_ID="mock-google-client-id.apps.googleusercontent.com"
AUTH_GOOGLE_CLIENT_SECRET="mock-google-client-secret"
AUTH_GITHUB_CLIENT_ID="mock-github-client-id"
AUTH_GITHUB_CLIENT_SECRET="mock-github-client-secret"

# ======================================
# Supabase - Mock para Testes
# ======================================
NEXT_PUBLIC_SUPABASE_URL="https://mock-supabase-instance.supabase.co"
NEXT_PUBLIC_SUPABASE_ANON_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.mock"
SUPABASE_URL="https://mock-supabase-instance.supabase.co"
SUPABASE_ANON_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.mock"

# ======================================
# Vertex AI - Mock para Testes
# ======================================
AI_ENABLED="false"
AI_VERTEX_PROJECT_ID="mock-project"
AI_VERTEX_LOCATION="us-central1"
AI_VERTEX_MODEL="gemini-1.5-pro"

# ======================================
# Feature Flags - Testes (Mocks permitidos apenas em testes)
# ======================================
AI_USE_MOCK="true"
AI_USE_MOCK="false"
AUTH_SKIP_PROVIDERS="true"
DEV_FORCE_PRODUCTION="false"
AI_ENABLED="true"
DEV_DISABLE_VALIDATION="true"
AUTH_USE_DEMO_USER="false"

# ======================================
# Configurações de Rate Limiting - Testes
# ======================================
API_RATE_LIMIT="1000"
API_RATE_WINDOW="60"

# ======================================
# Logs - Desabilitados em Testes
# ======================================
DISABLE_LOGGING="true"
