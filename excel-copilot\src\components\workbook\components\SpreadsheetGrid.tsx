'use client';

import React, { useMemo, useRef } from 'react';
import { useVirtualizer } from '@tanstack/react-virtual';

import { OptimizedTableComponents } from '../OptimizedTableComponents';
import { SpreadsheetData } from '../SpreadsheetContext';

interface SpreadsheetGridProps {
  data: SpreadsheetData;
  lastModifiedCell: { row: number; col: number } | null;
  readOnly: boolean;
  onCellChange: (rowIndex: number, colIndex: number, value: string) => void;
  onAddColumn: () => void;
  onAddRow: () => void;
  onRemoveRow: (index: number) => void;
  onRemoveColumn: (index: number) => void;
}

export function SpreadsheetGrid({
  data,
  lastModifiedCell,
  readOnly,
  onCellChange,
  onAddColumn,
  onAddRow,
  onRemoveRow,
  onRemoveColumn,
}: SpreadsheetGridProps) {
  const parentRef = useRef<HTMLDivElement>(null);

  // Preparar dados para o virtualizador
  const { headers, rows } = useMemo(() => {
    const safeHeaders = Array.isArray(data.headers) ? data.headers : [];
    const safeRows = Array.isArray(data.rows) ? data.rows : [];

    // Garantir que todas as linhas tenham o mesmo número de colunas
    const normalizedRows = safeRows.map(row => {
      if (!Array.isArray(row)) {
        return Array(safeHeaders.length).fill('');
      }
      
      // Ajustar tamanho da linha para corresponder ao número de headers
      const normalizedRow = [...row];
      while (normalizedRow.length < safeHeaders.length) {
        normalizedRow.push('');
      }
      return normalizedRow.slice(0, safeHeaders.length);
    });

    return {
      headers: safeHeaders,
      rows: normalizedRows,
    };
  }, [data.headers, data.rows]);

  // Configurar virtualizador para linhas
  const rowVirtualizer = useVirtualizer({
    count: rows.length + 1, // +1 para linha de adicionar
    getScrollElement: () => parentRef.current,
    estimateSize: () => 40, // Altura estimada de cada linha
    overscan: 10, // Renderizar 10 linhas extras para suavidade
  });

  // Configurar virtualizador para colunas
  const columnVirtualizer = useVirtualizer({
    horizontal: true,
    count: headers.length + 1, // +1 para coluna de adicionar
    getScrollElement: () => parentRef.current,
    estimateSize: () => 120, // Largura estimada de cada coluna
    overscan: 5, // Renderizar 5 colunas extras
  });

  // Função para verificar se uma célula foi modificada recentemente
  const isCellModified = (rowIndex: number, colIndex: number) => {
    return (
      lastModifiedCell &&
      lastModifiedCell.row === rowIndex &&
      lastModifiedCell.col === colIndex
    );
  };

  return (
    <div className="flex-1 overflow-hidden">
      <div
        ref={parentRef}
        className="h-full w-full overflow-auto"
        style={{
          contain: 'strict',
        }}
      >
        <div
          style={{
            height: `${rowVirtualizer.getTotalSize()}px`,
            width: `${columnVirtualizer.getTotalSize()}px`,
            position: 'relative',
          }}
        >
          {/* Header das colunas */}
          <div
            className="sticky top-0 z-20 bg-muted border-b"
            style={{
              height: '40px',
              width: `${columnVirtualizer.getTotalSize()}px`,
            }}
          >
            {columnVirtualizer.getVirtualItems().map(virtualColumn => {
              const isAddColumn = virtualColumn.index === headers.length;
              
              return (
                <div
                  key={virtualColumn.key}
                  className={`absolute top-0 flex items-center justify-center border-r text-sm font-medium ${
                    isAddColumn ? 'bg-muted/50' : 'bg-muted'
                  }`}
                  style={{
                    left: `${virtualColumn.start}px`,
                    width: `${virtualColumn.size}px`,
                    height: '40px',
                  }}
                >
                  {isAddColumn ? (
                    <button
                      onClick={onAddColumn}
                      disabled={readOnly}
                      className="h-full w-full text-muted-foreground hover:text-foreground hover:bg-muted/80 disabled:opacity-50 disabled:cursor-not-allowed"
                      title="Adicionar coluna"
                    >
                      +
                    </button>
                  ) : (
                    <div className="flex items-center justify-between w-full px-2">
                      <span className="truncate">{headers[virtualColumn.index]}</span>
                      {!readOnly && headers.length > 1 && (
                        <button
                          onClick={() => onRemoveColumn(virtualColumn.index)}
                          className="ml-1 text-xs text-muted-foreground hover:text-destructive"
                          title="Remover coluna"
                        >
                          ×
                        </button>
                      )}
                    </div>
                  )}
                </div>
              );
            })}
          </div>

          {/* Linhas de dados */}
          {rowVirtualizer.getVirtualItems().map(virtualRow => {
            const isAddRow = virtualRow.index === rows.length;
            const rowData = rows[virtualRow.index] || [];

            return (
              <div
                key={virtualRow.key}
                className="absolute left-0"
                style={{
                  top: `${virtualRow.start + 40}px`, // +40 para o header
                  height: `${virtualRow.size}px`,
                  width: `${columnVirtualizer.getTotalSize()}px`,
                }}
              >
                {isAddRow ? (
                  // Linha para adicionar nova linha
                  <div className="h-full border-b bg-muted/30">
                    <button
                      onClick={onAddRow}
                      disabled={readOnly}
                      className="h-full w-32 text-muted-foreground hover:text-foreground hover:bg-muted/50 disabled:opacity-50 disabled:cursor-not-allowed border-r"
                      title="Adicionar linha"
                    >
                      + Nova linha
                    </button>
                  </div>
                ) : (
                  // Linha de dados normal
                  <div className="h-full border-b">
                    {/* Número da linha */}
                    <div className="absolute left-0 top-0 w-12 h-full bg-muted border-r flex items-center justify-center text-sm text-muted-foreground">
                      <div className="flex items-center">
                        <span>{virtualRow.index + 1}</span>
                        {!readOnly && rows.length > 1 && (
                          <button
                            onClick={() => onRemoveRow(virtualRow.index)}
                            className="ml-1 text-xs hover:text-destructive"
                            title="Remover linha"
                          >
                            ×
                          </button>
                        )}
                      </div>
                    </div>

                    {/* Células da linha */}
                    {columnVirtualizer.getVirtualItems().map(virtualColumn => {
                      if (virtualColumn.index >= headers.length) return null;

                      const cellValue = rowData[virtualColumn.index] || '';
                      const isModified = isCellModified(virtualRow.index, virtualColumn.index);

                      return (
                        <OptimizedTableComponents.Cell
                          key={`${virtualRow.index}-${virtualColumn.index}`}
                          value={cellValue}
                          rowIndex={virtualRow.index}
                          colIndex={virtualColumn.index}
                          readOnly={readOnly}
                          isModified={isModified}
                          style={{
                            position: 'absolute',
                            left: `${virtualColumn.start + 48}px`, // +48 para o número da linha
                            top: 0,
                            width: `${virtualColumn.size}px`,
                            height: `${virtualRow.size}px`,
                          }}
                          onChange={onCellChange}
                        />
                      );
                    })}
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}
