import { safeConsoleLog } from '@/lib/logger';
import { SendMessageOptions } from '@/server/ai/vertex-ai-service';

/**
 * Implementação mock do serviço Vertex AI para testes
 * Retorna respostas pré-definidas com base nos prompts
 */
export class MockVertexAIService {
  private static instance: MockVertexAIService;
  private isInitialized = true;
  // Inicializar stats com valores padrão para evitar undefined
  private stats: {
    totalRequests: number;
    successfulRequests: number;
    failedRequests: number;
  } = {
    totalRequests: 0,
    successfulRequests: 0,
    failedRequests: 0,
  };

  private constructor() {
    safeConsoleLog('Mock Vertex AI Service inicializado');
  }

  /**
   * Obtém a instância singleton do serviço mock
   */
  public static getInstance(): MockVertexAIService {
    if (!MockVertexAIService.instance) {
      MockVertexAIService.instance = new MockVertexAIService();
    }
    return MockVertexAIService.instance;
  }

  /**
   * Verifica se o serviço está disponível
   */
  public async isServiceAvailable(): Promise<boolean> {
    return true;
  }

  /**
   * Retorna estatísticas de uso
   */
  public getStats(): Record<string, number> {
    return { ...this.stats };
  }

  /**
   * Envia uma mensagem para o serviço mock
   * @param message Mensagem a ser enviada
   * @param options Opções de configuração
   * @returns Resposta simulada com base no prompt
   */
  public async sendMessage(message: string, _options: SendMessageOptions = {}): Promise<string> {
    this.stats.totalRequests++;
    this.stats.successfulRequests++;

    // Gerar resposta baseada no conteúdo da mensagem
    if (message.toLowerCase().includes('brasil') || message.toLowerCase().includes('capital')) {
      return 'A capital do Brasil é Brasília, localizada na região Centro-Oeste.';
    }

    if (message.toLowerCase().includes('excel') && message.toLowerCase().includes('função')) {
      return 'As três funções mais importantes do Excel são: PROCV, SOMASE e SE. Estas funções permitem buscar dados, somar com condições e criar lógica condicional.';
    }

    if (message.toLowerCase().includes('procv') || message.toLowerCase().includes('vlookup')) {
      return 'A fórmula PROCV (VLOOKUP) no Excel permite buscar dados em tabelas. A sintaxe básica é =PROCV(valor_procurado; tabela; num_coluna; [procura_exata]).';
    }

    if (message.toLowerCase().includes('gráfico') || message.toLowerCase().includes('dispersão')) {
      return 'Para criar um gráfico de dispersão no Excel, selecione os dados, vá em Inserir > Gráficos > Dispersão. Este tipo de gráfico é ideal para analisar correlações entre variáveis.';
    }

    if (
      message.toLowerCase().includes('visualização') ||
      message.toLowerCase().includes('dashboard')
    ) {
      return 'Para melhorar a visualização de dados financeiros, considere usar gráficos de colunas para valores, formatação condicional para destacar valores importantes e tabelas dinâmicas para resumir dados por categorias.';
    }

    // Resposta padrão para qualquer outra mensagem
    return 'Entendi sua pergunta sobre Excel. Para responder adequadamente, precisaria de mais contexto sobre sua planilha e objetivos específicos.';
  }
}
