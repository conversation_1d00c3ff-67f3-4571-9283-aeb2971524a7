import { RealtimeChannel, RealtimePostgresChangesPayload } from '@supabase/supabase-js';

import { supabaseClient } from './client';

/**
 * Tipos para eventos de Real-time
 */
export interface WorkbookChangePayload {
  eventType: 'INSERT' | 'UPDATE' | 'DELETE';
  new?: Record<string, unknown>;
  old?: Record<string, unknown>;
  table: string;
  schema: string;
}

export interface CellChangeEvent {
  workbookId: string;
  sheetId: string;
  cellAddress: string;
  value: unknown;
  userId: string;
  timestamp: string;
}

export interface UserPresenceEvent {
  userId: string;
  userName: string;
  workbookId: string;
  isOnline: boolean;
  lastSeen: string;
  cursor?: {
    sheetId: string;
    cellAddress: string;
  };
}

/**
 * Callbacks para eventos de Real-time
 */
export type WorkbookChangeCallback = (payload: WorkbookChangePayload) => void;
export type CellChangeCallback = (event: CellChangeEvent) => void;
export type UserPresenceCallback = (event: UserPresenceEvent) => void;

/**
 * Serviço de Real-time para colaboração em workbooks
 */
export class WorkbookRealtimeService {
  private channels: Map<string, RealtimeChannel> = new Map();
  private presenceChannels: Map<string, RealtimeChannel> = new Map();

  /**
   * Inscrever-se em mudanças de um workbook específico
   */
  subscribeToWorkbook(
    workbookId: string,
    callbacks: {
      onWorkbookChange?: WorkbookChangeCallback;
      onSheetChange?: WorkbookChangeCallback;
      onCellChange?: CellChangeCallback;
    }
  ): RealtimeChannel {
    const channelName = `workbook:${workbookId}`;

    // Se já existe uma inscrição, retornar o canal existente
    if (this.channels.has(channelName)) {
      return this.channels.get(channelName)!;
    }

    const channel = supabaseClient
      .channel(channelName)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'Workbook',
          filter: `id=eq.${workbookId}`,
        },
        (payload: RealtimePostgresChangesPayload<Record<string, unknown>>) => {
          if (callbacks.onWorkbookChange) {
            callbacks.onWorkbookChange({
              eventType: payload.eventType,
              new: payload.new,
              old: payload.old,
              table: payload.table,
              schema: payload.schema,
            });
          }
        }
      )
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'Sheet',
          filter: `workbookId=eq.${workbookId}`,
        },
        (payload: RealtimePostgresChangesPayload<Record<string, unknown>>) => {
          if (callbacks.onSheetChange) {
            callbacks.onSheetChange({
              eventType: payload.eventType,
              new: payload.new,
              old: payload.old,
              table: payload.table,
              schema: payload.schema,
            });
          }
        }
      )
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'Cell',
          filter: `sheetId=in.(${workbookId})`,
        },
        (payload: RealtimePostgresChangesPayload<Record<string, unknown>>) => {
          if (callbacks.onCellChange && payload.new) {
            const newData = payload.new as Record<string, unknown>;
            callbacks.onCellChange({
              workbookId,
              sheetId: newData.sheetId as string,
              cellAddress: newData.address as string,
              value: newData.value,
              userId: (newData.updatedBy as string) || 'unknown',
              timestamp: (newData.updatedAt as string) || new Date().toISOString(),
            });
          }
        }
      )
      .subscribe();

    this.channels.set(channelName, channel);
    return channel;
  }

  /**
   * Inscrever-se em presença de usuários em um workbook
   */
  subscribeToUserPresence(
    workbookId: string,
    currentUser: { id: string; name: string },
    onPresenceChange: UserPresenceCallback
  ): RealtimeChannel {
    const channelName = `presence:${workbookId}`;

    if (this.presenceChannels.has(channelName)) {
      return this.presenceChannels.get(channelName)!;
    }

    const channel = supabaseClient
      .channel(channelName, {
        config: {
          presence: {
            key: currentUser.id,
          },
        },
      })
      .on('presence', { event: 'sync' }, () => {
        const state = channel.presenceState();
        Object.entries(state).forEach(([userId, presences]) => {
          const presence = presences[0] as Record<string, unknown>;
          const cursor = presence.cursor as { sheetId: string; cellAddress: string } | undefined;
          onPresenceChange({
            userId,
            userName: presence.name as string,
            workbookId,
            isOnline: true,
            lastSeen: new Date().toISOString(),
            ...(cursor && { cursor }),
          });
        });
      })
      .on('presence', { event: 'join' }, ({ key, newPresences }) => {
        const presence = newPresences[0] as Record<string, unknown>;
        const cursor = presence.cursor as { sheetId: string; cellAddress: string } | undefined;
        onPresenceChange({
          userId: key,
          userName: presence.name as string,
          workbookId,
          isOnline: true,
          lastSeen: new Date().toISOString(),
          ...(cursor && { cursor }),
        });
      })
      .on('presence', { event: 'leave' }, ({ key, leftPresences }) => {
        const presence = leftPresences[0] as Record<string, unknown>;
        const cursor = presence.cursor as { sheetId: string; cellAddress: string } | undefined;
        onPresenceChange({
          userId: key,
          userName: presence.name as string,
          workbookId,
          isOnline: false,
          lastSeen: new Date().toISOString(),
          ...(cursor && { cursor }),
        });
      })
      .subscribe(async status => {
        if (status === 'SUBSCRIBED') {
          // Enviar presença inicial
          await channel.track({
            name: currentUser.name,
            joinedAt: new Date().toISOString(),
          });
        }
      });

    this.presenceChannels.set(channelName, channel);
    return channel;
  }

  /**
   * Atualizar cursor do usuário
   */
  async updateUserCursor(
    workbookId: string,
    cursor: { sheetId: string; cellAddress: string }
  ): Promise<void> {
    const channelName = `presence:${workbookId}`;
    const channel = this.presenceChannels.get(channelName);

    if (channel) {
      await channel.track({
        cursor,
        lastActivity: new Date().toISOString(),
      });
    }
  }

  /**
   * Enviar mudança de célula em tempo real
   */
  async broadcastCellChange(
    workbookId: string,
    cellChange: Omit<CellChangeEvent, 'timestamp'>
  ): Promise<void> {
    const channelName = `workbook:${workbookId}`;
    const channel = this.channels.get(channelName);

    if (channel) {
      await channel.send({
        type: 'broadcast',
        event: 'cell_change',
        payload: {
          ...cellChange,
          timestamp: new Date().toISOString(),
        },
      });
    }
  }

  /**
   * Cancelar inscrição de um workbook
   */
  unsubscribeFromWorkbook(workbookId: string): void {
    const channelName = `workbook:${workbookId}`;
    const channel = this.channels.get(channelName);

    if (channel) {
      supabaseClient.removeChannel(channel);
      this.channels.delete(channelName);
    }
  }

  /**
   * Cancelar inscrição de presença
   */
  unsubscribeFromPresence(workbookId: string): void {
    const channelName = `presence:${workbookId}`;
    const channel = this.presenceChannels.get(channelName);

    if (channel) {
      supabaseClient.removeChannel(channel);
      this.presenceChannels.delete(channelName);
    }
  }

  /**
   * Cancelar todas as inscrições
   */
  unsubscribeAll(): void {
    // Cancelar canais de workbook
    this.channels.forEach(channel => {
      supabaseClient.removeChannel(channel);
    });
    this.channels.clear();

    // Cancelar canais de presença
    this.presenceChannels.forEach(channel => {
      supabaseClient.removeChannel(channel);
    });
    this.presenceChannels.clear();
  }

  /**
   * Obter status de conexão
   */
  getConnectionStatus(): {
    connected: boolean;
    activeChannels: number;
    presenceChannels: number;
  } {
    return {
      connected: supabaseClient.realtime.isConnected(),
      activeChannels: this.channels.size,
      presenceChannels: this.presenceChannels.size,
    };
  }

  /**
   * Reconectar todos os canais
   */
  async reconnectAll(): Promise<void> {
    // Reconectar canais de workbook
    for (const [, channel] of this.channels) {
      channel.subscribe();
    }

    // Reconectar canais de presença
    for (const [, channel] of this.presenceChannels) {
      channel.subscribe();
    }
  }
}

/**
 * Instância singleton do serviço
 */
export const realtimeService = new WorkbookRealtimeService();
