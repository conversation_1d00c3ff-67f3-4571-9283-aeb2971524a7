/**
 * Script de configuração para ambiente de produção
 *
 * Este script configura o ambiente de produção do Excel Copilot:
 * 1. Configura o banco de dados MySQL
 * 2. Desativa os mocks
 * 3. Configura o Stripe com chaves reais
 * 4. Configura a autenticação com provedores OAuth
 *
 * Modo de usar:
 * node scripts/setup-production.js --db-host=localhost --db-user=root --db-pass=senha --stripe-key=sk_live_xxxx
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');
const { execSync } = require('child_process');

// Criar interface de readline
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
});

// Função para perguntar
function ask(question) {
  return new Promise(resolve => {
    rl.question(question, answer => {
      resolve(answer);
    });
  });
}

// Cores para output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  green: '\x1b[32m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  yellow: '\x1b[33m',
  red: '\x1b[31m',
};

// Caminho para o arquivo .env.production
const envPath = path.join(__dirname, '..', '.env.production');

async function setupProductionEnvironment() {
  console.log(
    `\n${colors.bright}${colors.blue}===== Excel Copilot - Configuração de Produção =====${colors.reset}\n`
  );

  try {
    // Verificar se o arquivo já existe
    const fileExists = fs.existsSync(envPath);

    if (fileExists) {
      const overwrite = await ask(
        `${colors.yellow}O arquivo .env.production já existe. Deseja sobrescrevê-lo? (s/N): ${colors.reset}`
      );
      if (overwrite.toLowerCase() !== 's') {
        console.log(`${colors.cyan}Configuração cancelada pelo usuário.${colors.reset}`);
        rl.close();
        return;
      }
    }

    console.log(
      `${colors.bright}${colors.cyan}Vamos configurar as variáveis de ambiente para produção.${colors.reset}\n`
    );

    // Banco de dados
    console.log(
      `\n${colors.bright}${colors.blue}===== Configuração do Banco de Dados =====${colors.reset}`
    );
    const databaseProvider = await ask(
      `${colors.cyan}Provedor de banco de dados (mysql/postgresql) [mysql]: ${colors.reset}`
    );
    const databaseUrl = await ask(
      `${colors.cyan}URL do banco de dados (ex: mysql://user:password@host:port/database): ${colors.reset}`
    );

    // Autenticação
    console.log(
      `\n${colors.bright}${colors.blue}===== Configuração de Autenticação =====${colors.reset}`
    );
    const nextAuthSecret = await ask(
      `${colors.cyan}NEXTAUTH_SECRET (chave secreta para autenticação): ${colors.reset}`
    );
    const nextAuthUrl = await ask(
      `${colors.cyan}NEXTAUTH_URL (URL completa da aplicação, ex: https://excel-copilot.com): ${colors.reset}`
    );

    // Provedores de autenticação
    const googleClientId = await ask(
      `${colors.cyan}GOOGLE_CLIENT_ID (opcional, deixe em branco para pular): ${colors.reset}`
    );
    const googleClientSecret = await ask(
      `${colors.cyan}GOOGLE_CLIENT_SECRET (opcional): ${colors.reset}`
    );
    const githubClientId = await ask(`${colors.cyan}GITHUB_CLIENT_ID (opcional): ${colors.reset}`);
    const githubClientSecret = await ask(
      `${colors.cyan}GITHUB_CLIENT_SECRET (opcional): ${colors.reset}`
    );

    // Configurações do Stripe
    console.log(
      `\n${colors.bright}${colors.blue}===== Configuração do Stripe =====${colors.reset}`
    );
    const stripeSecretKey = await ask(`${colors.cyan}STRIPE_SECRET_KEY: ${colors.reset}`);
    const stripePublishableKey = await ask(
      `${colors.cyan}NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY: ${colors.reset}`
    );
    const stripeWebhookSecret = await ask(`${colors.cyan}STRIPE_WEBHOOK_SECRET: ${colors.reset}`);
    const stripePriceMonthly = await ask(
      `${colors.cyan}NEXT_PUBLIC_STRIPE_PRICE_MONTHLY (ID do preço mensal): ${colors.reset}`
    );
    const stripePriceAnnual = await ask(
      `${colors.cyan}NEXT_PUBLIC_STRIPE_PRICE_ANNUAL (ID do preço anual): ${colors.reset}`
    );

    // Configuração da IA
    console.log(`\n${colors.bright}${colors.blue}===== Configuração da IA =====${colors.reset}`);
    console.log(`${colors.cyan}Configuração do Google Vertex AI:${colors.reset}`);
    const vertexEnabled = await ask(`${colors.cyan}Habilitar Vertex AI? (s/N): ${colors.reset}`);
    const useVertexAI = vertexEnabled.toLowerCase() === 's';

    let vertexProjectId = '';
    let vertexLocation = 'us-central1';
    let vertexModelName = 'gemini-1.5-pro';

    if (useVertexAI) {
      vertexProjectId = await ask(
        `${colors.cyan}VERTEX_AI_PROJECT_ID (ID do projeto no Google Cloud): ${colors.reset}`
      );
      const customLocation = await ask(
        `${colors.cyan}VERTEX_AI_LOCATION [us-central1]: ${colors.reset}`
      );
      if (customLocation.trim()) vertexLocation = customLocation;

      const customModel = await ask(
        `${colors.cyan}VERTEX_AI_MODEL_NAME [gemini-1.5-pro]: ${colors.reset}`
      );
      if (customModel.trim()) vertexModelName = customModel;

      console.log(
        `${colors.yellow}Importante: Para usar o Vertex AI, coloque o arquivo vertex-credentials.json na raiz do projeto.${colors.reset}`
      );
    }

    // Outras configurações
    console.log(`\n${colors.bright}${colors.blue}===== Outras Configurações =====${colors.reset}`);
    const appName =
      (await ask(`${colors.cyan}APP_NAME [Excel Copilot]: ${colors.reset}`)) || 'Excel Copilot';
    const appUrl = nextAuthUrl;
    const appDescription =
      (await ask(
        `${colors.cyan}APP_DESCRIPTION [Transforme suas planilhas com comandos em linguagem natural]: ${colors.reset}`
      )) || 'Transforme suas planilhas com comandos em linguagem natural';

    // Criar conteúdo do arquivo .env.production
    const envContent = `# Configuração de Banco de Dados
DATABASE_PROVIDER="${databaseProvider || 'mysql'}"
DATABASE_URL="${databaseUrl}"

# Autenticação
NEXTAUTH_SECRET="${nextAuthSecret}"
NEXTAUTH_URL="${nextAuthUrl}"
NODE_ENV="production"

# Desativar mocks em produção
USE_MOCK_AI="false"
SKIP_AUTH_PROVIDERS="${!googleClientId && !githubClientId ? 'true' : 'false'}"

# Configuração do Stripe
STRIPE_SECRET_KEY="${stripeSecretKey}"
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY="${stripePublishableKey}"
STRIPE_WEBHOOK_SECRET="${stripeWebhookSecret}"
NEXT_PUBLIC_STRIPE_PRICE_MONTHLY="${stripePriceMonthly}"
NEXT_PUBLIC_STRIPE_PRICE_ANNUAL="${stripePriceAnnual}"

# Provedores de autenticação
${googleClientId ? `GOOGLE_CLIENT_ID="${googleClientId}"` : '# GOOGLE_CLIENT_ID=""'}
${googleClientSecret ? `GOOGLE_CLIENT_SECRET="${googleClientSecret}"` : '# GOOGLE_CLIENT_SECRET=""'}
${githubClientId ? `GITHUB_CLIENT_ID="${githubClientId}"` : '# GITHUB_CLIENT_ID=""'}
${githubClientSecret ? `GITHUB_CLIENT_SECRET="${githubClientSecret}"` : '# GITHUB_CLIENT_SECRET=""'}

# Configuração da IA - Vertex AI
${useVertexAI ? `VERTEX_AI_PROJECT_ID="${vertexProjectId}"` : '# VERTEX_AI_PROJECT_ID=""'}
${useVertexAI ? `VERTEX_AI_LOCATION="${vertexLocation}"` : '# VERTEX_AI_LOCATION=""'}
${useVertexAI ? `VERTEX_AI_MODEL_NAME="${vertexModelName}"` : '# VERTEX_AI_MODEL_NAME=""'}

# Configurações da Aplicação
APP_NAME="${appName}"
APP_URL="${appUrl}"
# REMOVIDO: APP_DESCRIPTION="${appDescription}"`;

    // Escrever arquivo
    fs.writeFileSync(envPath, envContent);

    console.log(`\n${colors.green}✓ Arquivo .env.production criado com sucesso!${colors.reset}`);

    // Perguntar se deseja executar as migrações de produção
    const runMigrations = await ask(
      `${colors.cyan}Deseja executar as migrações de banco de dados para produção? (s/N): ${colors.reset}`
    );

    if (runMigrations.toLowerCase() === 's') {
      console.log(
        `${colors.blue}Executando migrações do Prisma em modo de produção...${colors.reset}`
      );
      try {
        // Copiar .env.production para .env temporariamente para executar as migrações
        fs.copyFileSync(envPath, path.join(__dirname, '..', '.env'));

        // Executar migrações
        execSync('npx prisma migrate deploy', { stdio: 'inherit' });

        console.log(`${colors.green}✓ Migrações aplicadas com sucesso!${colors.reset}`);

        // Remover arquivo .env temporário
        fs.unlinkSync(path.join(__dirname, '..', '.env'));
      } catch (error) {
        console.error(`${colors.red}Erro ao executar migrações:${colors.reset}`, error);
      }
    }

    console.log(
      `\n${colors.bright}${colors.green}===== Configuração de Produção Concluída =====${colors.reset}`
    );
    console.log(`\n${colors.blue}Para iniciar em modo produção:${colors.reset}`);
    console.log(
      `${colors.cyan}1. Copie .env.production para .env.local (cp .env.production .env.local)${colors.reset}`
    );
    console.log(`${colors.cyan}2. Execute: npm run build${colors.reset}`);
    console.log(`${colors.cyan}3. Execute: npm run start:safe${colors.reset}`);
  } catch (error) {
    console.error(`\n${colors.red}Erro durante a configuração:${colors.reset}`, error);
    process.exit(1);
  } finally {
    rl.close();
  }
}

// Executar função principal
setupProductionEnvironment();
