// Definição dos tipos de erro da aplicação
export enum ErrorType {
  // Erros de autenticação
  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR',
  AUTHORIZATION_ERROR = 'AUTHORIZATION_ERROR',
  UNAUTHORIZED = 'UNAUTHORIZED',
  FORBIDDEN = 'FORBIDDEN',

  // Erros de recurso
  NOT_FOUND = 'NOT_FOUND',
  ALREADY_EXISTS = 'ALREADY_EXISTS',

  // Erros de validação
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  INVALID_INPUT = 'INVALID_INPUT',
  INVALID_REQUEST = 'INVALID_REQUEST',

  // Erros de serviço
  SERVICE_UNAVAILABLE = 'SERVICE_UNAVAILABLE',
  EXTERNAL_API_ERROR = 'EXTERNAL_API_ERROR',
  EXTERNAL_SERVICE = 'EXTERNAL_SERVICE',
  DATABASE_ERROR = 'DATABASE_ERROR',

  // Erros de operação
  OPERATION_NOT_ALLOWED = 'OPERATION_NOT_ALLOWED',
  CONFLICT_ERROR = 'CONFLICT_ERROR',

  // Erros de limite
  RATE_LIMIT_ERROR = 'RATE_LIMIT_ERROR',
  QUOTA_EXCEEDED = 'QUOTA_EXCEEDED',

  // Erros de configuração
  CONFIGURATION_ERROR = 'CONFIGURATION_ERROR',

  // Erros específicos da aplicação
  EXCEL_OPERATION_ERROR = 'EXCEL_OPERATION_ERROR',
  EXCEL_PARSE_ERROR = 'EXCEL_PARSE_ERROR',
  AI_REQUEST_ERROR = 'AI_REQUEST_ERROR',
  AI_RESPONSE_ERROR = 'AI_RESPONSE_ERROR',
  NETWORK_ERROR = 'NETWORK_ERROR',

  // Erro genérico
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
  INTERNAL = 'INTERNAL',
  API_ERROR = 'API_ERROR',
}

// Códigos HTTP para cada tipo de erro
export const ErrorStatusCodes: Record<ErrorType, number> = {
  [ErrorType.UNKNOWN_ERROR]: 500,
  [ErrorType.EXTERNAL_API_ERROR]: 502,
  [ErrorType.EXTERNAL_SERVICE]: 503,
  [ErrorType.DATABASE_ERROR]: 500,
  [ErrorType.AUTHENTICATION_ERROR]: 401,
  [ErrorType.UNAUTHORIZED]: 401,
  [ErrorType.AUTHORIZATION_ERROR]: 403,
  [ErrorType.FORBIDDEN]: 403,
  [ErrorType.NOT_FOUND]: 404,
  [ErrorType.VALIDATION_ERROR]: 400,
  [ErrorType.SERVICE_UNAVAILABLE]: 503,
  [ErrorType.INVALID_INPUT]: 400,
  [ErrorType.INVALID_REQUEST]: 400,
  [ErrorType.OPERATION_NOT_ALLOWED]: 403,
  [ErrorType.CONFLICT_ERROR]: 409,
  [ErrorType.RATE_LIMIT_ERROR]: 429,
  [ErrorType.QUOTA_EXCEEDED]: 403,
  [ErrorType.CONFIGURATION_ERROR]: 500,
  [ErrorType.ALREADY_EXISTS]: 409,
  [ErrorType.API_ERROR]: 500,
  [ErrorType.INTERNAL]: 500,
  [ErrorType.EXCEL_OPERATION_ERROR]: 400,
  [ErrorType.EXCEL_PARSE_ERROR]: 400,
  [ErrorType.AI_REQUEST_ERROR]: 400,
  [ErrorType.AI_RESPONSE_ERROR]: 502,
  [ErrorType.NETWORK_ERROR]: 503,
};

// Mensagens amigáveis para o usuário para cada tipo de erro
export const UserFriendlyMessages: Record<ErrorType, string> = {
  [ErrorType.UNKNOWN_ERROR]: 'Ocorreu um erro inesperado. Por favor, tente novamente.',
  [ErrorType.EXTERNAL_API_ERROR]:
    'Ocorreu um erro ao comunicar com um serviço externo. Tente novamente mais tarde.',
  [ErrorType.EXTERNAL_SERVICE]: 'Um serviço externo está indisponível no momento.',
  [ErrorType.DATABASE_ERROR]: 'Erro ao acessar os dados. Tente novamente mais tarde.',
  [ErrorType.AUTHENTICATION_ERROR]: 'Erro de autenticação. Por favor, faça login novamente.',
  [ErrorType.UNAUTHORIZED]: 'Erro de autenticação. Por favor, faça login novamente.',
  [ErrorType.AUTHORIZATION_ERROR]: 'Você não tem permissão para acessar este recurso.',
  [ErrorType.FORBIDDEN]: 'Você não tem permissão para acessar este recurso.',
  [ErrorType.NOT_FOUND]: 'O recurso solicitado não foi encontrado.',
  [ErrorType.VALIDATION_ERROR]: 'Dados inválidos. Por favor, verifique as informações fornecidas.',
  [ErrorType.SERVICE_UNAVAILABLE]:
    'Serviço temporariamente indisponível. Por favor, tente novamente mais tarde.',
  [ErrorType.INVALID_INPUT]: 'Entrada inválida. Verifique os dados enviados.',
  [ErrorType.INVALID_REQUEST]: 'Requisição inválida. Verifique os parâmetros enviados.',
  [ErrorType.OPERATION_NOT_ALLOWED]:
    'Operação não permitida. Verifique as permissões e tente novamente.',
  [ErrorType.CONFLICT_ERROR]:
    'Conflito ao processar a solicitação. Verifique os dados e tente novamente.',
  [ErrorType.RATE_LIMIT_ERROR]: 'Muitas solicitações. Por favor, tente novamente mais tarde.',
  [ErrorType.QUOTA_EXCEEDED]:
    'Limite de requisições excedido. Por favor, tente novamente mais tarde.',
  [ErrorType.CONFIGURATION_ERROR]:
    'Erro de configuração do sistema. Entre em contato com o suporte.',
  [ErrorType.ALREADY_EXISTS]: 'Recurso já existe. Verifique os dados e tente novamente.',
  [ErrorType.API_ERROR]: 'Ocorreu um erro ao processar sua solicitação na API.',
  [ErrorType.INTERNAL]: 'Ocorreu um erro interno no servidor. Tente novamente mais tarde.',
  [ErrorType.EXCEL_OPERATION_ERROR]:
    'Erro ao executar operação no Excel. Verifique os dados e tente novamente.',
  [ErrorType.EXCEL_PARSE_ERROR]:
    'Erro ao processar o arquivo Excel. Verifique se o formato é válido.',
  [ErrorType.AI_REQUEST_ERROR]: 'Erro ao processar requisição para o serviço de IA.',
  [ErrorType.AI_RESPONSE_ERROR]:
    'Erro ao receber resposta do serviço de IA. Tente novamente mais tarde.',
  [ErrorType.NETWORK_ERROR]: 'Erro de conexão de rede. Verifique sua internet e tente novamente.',
};

// Tempos de expiração para retry de cada tipo de erro (em ms)
export const ErrorRetryTimes: Partial<Record<ErrorType, number>> = {
  [ErrorType.EXTERNAL_API_ERROR]: 2000,
  [ErrorType.EXTERNAL_SERVICE]: 3000,
  [ErrorType.DATABASE_ERROR]: 5000,
  [ErrorType.RATE_LIMIT_ERROR]: 30000,
  [ErrorType.NETWORK_ERROR]: 1500,
  [ErrorType.AI_RESPONSE_ERROR]: 3000,
  [ErrorType.SERVICE_UNAVAILABLE]: 5000,
};

// Interface para garantir tipagem correta nas propriedades técnicas
export interface TechnicalErrorDetails {
  requestId?: string;
  path?: string;
  timestamp?: string;
  originalMessage?: string;
  code?: string | number;
  [key: string]: any;
}

// Classe personalizada de erro para padronização do tratamento de erros
export class AppError extends Error {
  type: ErrorType;
  status: number;
  details?: any;
  id: string;
  timestamp: Date;
  originalError?: any;

  constructor(
    message: string,
    type: ErrorType = ErrorType.UNKNOWN_ERROR,
    status: number = 500,
    details?: any,
    originalError?: any
  ) {
    super(message);
    this.name = 'AppError';
    this.type = type;
    this.status = status || ErrorStatusCodes[type] || 500;
    this.details = details;
    this.id = generateErrorId();
    this.timestamp = new Date();
    this.originalError = originalError;

    // Necessário para que instanceof funcione corretamente com classes que estendem Error
    Object.setPrototypeOf(this, AppError.prototype);
  }

  // Método para obter mensagem amigável ao usuário com base no tipo de erro
  getUserFriendlyMessage(): string {
    return UserFriendlyMessages[this.type] || UserFriendlyMessages[ErrorType.UNKNOWN_ERROR];
  }

  // Método para obter status HTTP com base no tipo de erro
  getHttpStatus(): number {
    return ErrorStatusCodes[this.type] || 500;
  }

  // Verificar se o erro é recuperável (pode ser tentado novamente)
  public isRetryable(): boolean {
    return Object.keys(ErrorRetryTimes).includes(this.type);
  }

  // Tempo recomendado para aguardar antes de retry
  public getRetryTime(): number {
    return ErrorRetryTimes[this.type] || 0;
  }

  // Formata o erro para logging
  public toLogFormat(): Record<string, any> {
    return {
      id: this.id,
      type: this.type,
      message: this.message,
      userMessage: this.getUserFriendlyMessage(),
      statusCode: this.getHttpStatus(),
      stack: this.stack,
      timestamp: this.timestamp.toISOString(),
      technicalDetails: this.details,
      originalError: this.formatOriginalError(),
    };
  }

  // Formata o erro para API (remove informações sensíveis)
  public toApiResponse(includeDetails: boolean = false): Record<string, any> {
    const response: Record<string, any> = {
      error: this.getUserFriendlyMessage(),
      type: this.type,
      status: this.getHttpStatus(),
      id: this.id,
    };

    if (includeDetails && process.env.NODE_ENV !== 'production') {
      response.details = {
        message: this.message,
        stack: this.stack?.split('\n').map(line => line.trim()),
        timestamp: this.timestamp.toISOString(),
      };
    }

    return response;
  }

  private formatOriginalError(): any {
    if (!this.originalError) return null;

    if (this.originalError instanceof Error) {
      return {
        name: this.originalError.name,
        message: this.originalError.message,
        stack: this.originalError.stack,
      };
    }

    try {
      return JSON.stringify(this.originalError);
    } catch {
      return String(this.originalError);
    }
  }
}

// Gerar um ID único para cada erro para facilitar tracking
function generateErrorId(): string {
  const timestamp = Date.now().toString(36);
  const random = Math.random().toString(36).substring(2, 8);
  return `err_${timestamp}_${random}`;
}

// Função para normalizar erros de várias fontes para o formato padrão da aplicação
export function normalizeError(error: unknown, context?: string): AppError {
  // Se já for um AppError, retorna ele mesmo
  if (error instanceof AppError) {
    return error;
  }

  let message: string;
  let type: ErrorType = ErrorType.UNKNOWN_ERROR;
  let statusCode: number = 500;
  let details: any = {};

  // Adicionar contexto aos detalhes se fornecido
  if (context) {
    details.context = context;
  }

  // Processar diferentes tipos de erro
  if (error instanceof Error) {
    message = error.message;
    details.stack = error.stack;
    details.name = error.name;

    // Tentar detectar o tipo de erro com base na mensagem
    type = detectErrorTypeFromMessage(error.message);

    // Tratar erros específicos de API
    if ('status' in error && typeof (error as any).status === 'number') {
      statusCode = (error as any).status;
      type = getErrorTypeFromStatus(statusCode);
    }

    // Extrair detalhes adicionais se disponíveis
    if ('code' in error) details.code = (error as any).code;
    if ('response' in error) {
      try {
        details.response = tryStringify((error as any).response);
      } catch {
        details.response = 'Failed to serialize response';
      }
    }
  } else if (typeof error === 'string') {
    message = error;
    type = detectErrorTypeFromMessage(error);
  } else if (error && typeof error === 'object') {
    // Para objetos de erro genéricos
    if ('message' in error && typeof (error as any).message === 'string') {
      message = (error as any).message;
    } else {
      message = tryStringify(error);
    }

    // Extrair status se disponível
    if ('status' in error && typeof (error as any).status === 'number') {
      statusCode = (error as any).status;
      type = getErrorTypeFromStatus(statusCode);
    }

    // Adicionar todos os campos do objeto como detalhes
    details = { ...error };
  } else {
    message = String(error);
  }

  return new AppError(message, type, statusCode, details, error);
}

// Função auxiliar para tentar detectar o tipo de erro a partir da mensagem
function detectErrorTypeFromMessage(message: string): ErrorType {
  message = message.toLowerCase();

  if (
    message.includes('network') ||
    message.includes('fetch') ||
    message.includes('connection') ||
    message.includes('offline')
  ) {
    return ErrorType.NETWORK_ERROR;
  }

  if (
    message.includes('permission') ||
    message.includes('access denied') ||
    message.includes('not allowed')
  ) {
    return ErrorType.FORBIDDEN;
  }

  if (message.includes('not found') || message.includes('404')) {
    return ErrorType.NOT_FOUND;
  }

  if (
    message.includes('authentication') ||
    message.includes('login') ||
    message.includes('unauthorized') ||
    message.includes('401')
  ) {
    return ErrorType.UNAUTHORIZED;
  }

  return ErrorType.UNKNOWN_ERROR;
}

// Função para mapear códigos HTTP para tipos de erro
function getErrorTypeFromStatus(status: number): ErrorType {
  switch (status) {
    case 400:
      return ErrorType.INVALID_INPUT;
    case 401:
      return ErrorType.UNAUTHORIZED;
    case 403:
      return ErrorType.FORBIDDEN;
    case 404:
      return ErrorType.NOT_FOUND;
    case 409:
      return ErrorType.CONFLICT_ERROR;
    case 429:
      return ErrorType.RATE_LIMIT_ERROR;
    case 500:
      return ErrorType.INTERNAL;
    case 502:
      return ErrorType.EXTERNAL_API_ERROR;
    case 503:
      return ErrorType.SERVICE_UNAVAILABLE;
    default:
      return ErrorType.UNKNOWN_ERROR;
  }
}

// Função auxiliar para serializar objetos com segurança
function tryStringify(obj: any): string {
  try {
    return JSON.stringify(obj);
  } catch {
    return '[Object não serializável]';
  }
}

// Função para logar erros de forma padronizada
export function logError(error: Error | AppError, context?: string): void {
  const normalizedError = error instanceof AppError ? error : normalizeError(error, context);

  console.error('ERROR:', normalizedError.toLogFormat());
}

// Função para tratar erros em contexto de API
export function apiErrorHandler(error: unknown, context?: string) {
  const normalizedError = normalizeError(error, context);
  logError(normalizedError, context);

  return {
    status: normalizedError.getHttpStatus(),
    body: normalizedError.toApiResponse(process.env.NODE_ENV !== 'production'),
  };
}

// Função para envolver uma Promise com tratamento de erro padronizado
export function catchError<T>(
  promise: Promise<T>,
  errorType: ErrorType = ErrorType.UNKNOWN_ERROR,
  errorMessage?: string
): Promise<T> {
  return promise.catch(error => {
    const normalizedError = normalizeError(error);
    normalizedError.type = errorType;

    if (errorMessage) {
      normalizedError.message = errorMessage;
    }

    throw normalizedError;
  });
}

// Funções auxiliares para criar erros comuns
export const createValidationError = (message: string, context?: Record<string, any>): AppError => {
  return new AppError(message, ErrorType.VALIDATION_ERROR, 400, context);
};

export const createAuthenticationError = (
  message: string,
  context?: Record<string, any>
): AppError => {
  return new AppError(message, ErrorType.AUTHENTICATION_ERROR, 401, context);
};

export const createNotFoundError = (resource: string, id?: string): AppError => {
  const message = id ? `${resource} com ID ${id} não encontrado` : `${resource} não encontrado`;

  return new AppError(message, ErrorType.NOT_FOUND, 404, { resource, id });
};

export const createUnauthorizedError = (
  message: string = 'Não autorizado',
  context?: Record<string, any>
): AppError => {
  return new AppError(message, ErrorType.UNAUTHORIZED, 401, context);
};

export const createRateLimitError = (
  message: string = 'Limite de taxa excedido',
  context?: Record<string, any>
): AppError => {
  return new AppError(message, ErrorType.RATE_LIMIT_ERROR, 429, context);
};

export const createConfigurationError = (
  message: string,
  context?: Record<string, any>
): AppError => {
  return new AppError(message, ErrorType.CONFIGURATION_ERROR, 500, context);
};

// Erros específicos da aplicação
export const createExcelOperationError = (
  message: string,
  context?: Record<string, any>
): AppError => {
  return new AppError(message, ErrorType.EXCEL_OPERATION_ERROR, 400, context);
};

export const createExcelParseError = (message: string, context?: Record<string, any>): AppError => {
  return new AppError(message, ErrorType.EXCEL_PARSE_ERROR, 400, context);
};

export const createAIRequestError = (message: string, context?: Record<string, any>): AppError => {
  return new AppError(message, ErrorType.AI_REQUEST_ERROR, 400, context);
};

export const createAIResponseError = (message: string, context?: Record<string, any>): AppError => {
  return new AppError(message, ErrorType.AI_RESPONSE_ERROR, 502, context);
};

export const createNetworkError = (
  message: string = 'Erro de conexão de rede',
  context?: Record<string, any>
): AppError => {
  return new AppError(message, ErrorType.NETWORK_ERROR, 503, context);
};
