// Mock para ExcelJS para evitar problemas com ESM
// Este mock simula as funcionalidades básicas do ExcelJS utilizadas nos testes
import fs from 'fs';

class MockWorksheet {
  name: string;
  columns: Array<any> = [];
  rows: Array<any> = [];

  constructor(name: string) {
    this.name = name;
  }

  addRow(data: any[]) {
    const row = {
      values: [null, ...data],
      getCell: (index: number) => ({ value: data[index - 1] }),
    };
    this.rows.push(row);
    return row;
  }

  getRow(index: number) {
    return this.rows[index - 1] || { values: [], getCell: () => ({ value: null }) };
  }

  getColumn(index: number | string) {
    const colIndex =
      typeof index === 'string' ? this.columns.findIndex(col => col.key === index) : index - 1;

    return {
      values: this.rows.map(row => row.values[typeof index === 'string' ? colIndex + 1 : index]),
      eachCell: (callback: Function) => {
        this.rows.forEach((row, i) => {
          callback(row.getCell(typeof index === 'string' ? colIndex + 1 : index), i + 1);
        });
      },
    };
  }

  getCell(row: number, col: number | string) {
    const rowData = this.getRow(row);
    const colIndex =
      typeof col === 'string' ? this.columns.findIndex(column => column.key === col) + 1 : col;

    return rowData.getCell(colIndex);
  }
}

class MockWorkbook {
  creator = 'Excel Copilot Test';
  lastModifiedBy = 'Test User';
  created = new Date();
  modified = new Date();
  lastPrinted = new Date();
  properties = {
    date1904: false,
  };
  views: any[] = [];
  sheets: MockWorksheet[] = [];

  addWorksheet(name: string) {
    const worksheet = new MockWorksheet(name);
    this.sheets.push(worksheet);
    return worksheet;
  }

  getWorksheet(index: number | string) {
    if (typeof index === 'number') {
      return this.sheets[index - 1];
    }
    return this.sheets.find(s => s.name === index) || null;
  }

  xlsx = {
    writeFile: async (filename: string) => {
      // Criar um arquivo vazio para simular a escrita do Excel
      fs.writeFileSync(filename, 'Mock Excel file content');
      return Promise.resolve();
    },
    writeBuffer: async () => {
      // Simular a escrita para um buffer
      return Buffer.from('Mock Excel file content');
    },
    readFile: async (filename: string) => {
      // Verificar se o arquivo existe e simular sua leitura
      if (fs.existsSync(filename)) {
        // Adicionar dados de teste
        const worksheet = this.addWorksheet('Test');
        const cell = worksheet.getCell(1, 1); // A1
        cell.value = 'Teste';
        return Promise.resolve(this);
      }
      throw new Error(`File ${filename} does not exist`);
    },
  };

  csv = {
    writeFile: async (filename: string) => {
      // Criar um arquivo CSV simulado
      fs.writeFileSync(filename, 'col1,col2\nval1,val2');
      return Promise.resolve();
    },
    writeBuffer: async () => {
      // Simular a escrita para um buffer
      return Buffer.from('col1,col2\nval1,val2');
    },
  };

  // Para ajudar nos casos onde os testes esperam o readFile
  static async readFile(filename: string) {
    // Verificar se o arquivo existe
    if (fs.existsSync(filename)) {
      const workbook = new MockWorkbook();
      workbook.addWorksheet('Sheet1');
      return workbook;
    }
    throw new Error(`File ${filename} does not exist`);
  }
}

const ExcelJS = {
  Workbook: MockWorkbook,
};

export default ExcelJS;
