'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { useForm } from 'react-hook-form';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { useToast } from '@/components/ui/use-toast';
import { createWorkbookValidator, type CreateWorkbookInput } from '@/lib/validators/workbook';
import { createWorkbookAction } from '@/server/workbook.actions';

export function CreateWorkbookForm() {
  const { toast } = useToast();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);

  // Usar React Hook Form com validação Zod
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
  } = useForm<CreateWorkbookInput>({
    resolver: zodResolver(createWorkbookValidator),
    defaultValues: {
      name: '',
      description: '',
    },
  });

  // Obter os valores atuais dos campos
  const _nameValue = watch('name');
  const _descriptionValue = watch('description');

  // Manipulador de envio do formulário - agora usando server actions
  const onSubmit = async (data: CreateWorkbookInput) => {
    try {
      setIsLoading(true);

      // Criar FormData para server action
      const formData = new FormData();
      formData.append('name', data.name);
      formData.append('description', data.description || '');

      // Chamar server action
      const result = await createWorkbookAction(formData);

      if (result.success && result.workbook) {
        toast({
          title: 'Planilha criada com sucesso',
          description: 'Sua planilha foi criada e está pronta para uso.',
        });

        // Redirecionar para a nova planilha
        router.push(`/workbook/${result.workbook.id}`);
        reset();
      } else {
        throw new Error(result.error || 'Erro ao criar planilha');
      }
    } catch (error: any) {
      toast({
        title: 'Erro ao criar planilha',
        description: error.message || 'Ocorreu um erro ao criar a planilha. Tente novamente.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle>Criar Nova Planilha</CardTitle>
      </CardHeader>
      <form onSubmit={handleSubmit(onSubmit)}>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Nome</Label>
            <input
              id="name"
              className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              placeholder="Nome da planilha"
              {...register('name')}
              disabled={isLoading}
              required
            />
            {errors.name && <p className="text-sm text-destructive mt-1">{errors.name.message}</p>}
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Descrição (opcional)</Label>
            <textarea
              id="description"
              className="flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 resize-vertical"
              placeholder="Descrição (opcional)"
              {...register('description')}
              disabled={isLoading}
              rows={3}
            />
            {errors.description && (
              <p className="text-sm text-destructive mt-1">{errors.description.message}</p>
            )}
          </div>
        </CardContent>
        <CardFooter className="flex justify-end space-x-2">
          <Button type="button" variant="outline" onClick={() => reset()} disabled={isLoading}>
            Cancelar
          </Button>
          <Button type="submit" disabled={isLoading}>
            {isLoading ? (
              <>
                <span className="mr-2">Criando...</span>
                <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
              </>
            ) : (
              'Criar Planilha'
            )}
          </Button>
        </CardFooter>
      </form>
    </Card>
  );
}
