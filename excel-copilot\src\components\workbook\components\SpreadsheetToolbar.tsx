'use client';

import React from 'react';
import {
  ChevronRight,
  FileSpreadsheet,
  BarChart,
  Undo,
  Redo,
  Save,
  Loader2,
  KeyboardIcon,
  FullscreenIcon,
  ChevronLeft,
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { logger } from '@/lib/logger';

interface SpreadsheetToolbarProps {
  // Estados
  isSaving: boolean;
  canUndo: boolean;
  canRedo: boolean;
  isFullScreen: boolean;
  aiPanelCollapsed: boolean;
  readOnly: boolean;
  spreadsheetName: string;

  // Handlers
  onSave: () => void;
  onUndo: () => void;
  onRedo: () => void;
  onToggleFullScreen: () => void;
  onToggleAIPanel: () => void;
  onShowKeyboardShortcuts: () => void;
  onNavigate: (route: string) => void;
}

export function SpreadsheetToolbar({
  isSaving,
  canUndo,
  canRedo,
  isFullScreen,
  aiPanelCollapsed,
  readOnly,
  spreadsheetName,
  onSave,
  onUndo,
  onRedo,
  onToggleFullScreen,
  onToggleAIPanel,
  onShowKeyboardShortcuts,
  onNavigate,
}: SpreadsheetToolbarProps) {
  return (
    <TooltipProvider>
      <div className="flex items-center justify-between p-4 border-b bg-background">
        {/* Lado esquerdo - Navegação e título */}
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onNavigate('/dashboard')}
            className="text-muted-foreground hover:text-foreground"
          >
            <ChevronLeft className="h-4 w-4 mr-1" />
            Dashboard
          </Button>

          <Separator orientation="vertical" className="h-6" />

          <div className="flex items-center space-x-2">
            <FileSpreadsheet className="h-5 w-5 text-primary" />
            <span className="font-medium text-foreground">
              {spreadsheetName || 'Planilha sem nome'}
            </span>
          </div>
        </div>

        {/* Centro - Controles principais */}
        <div className="flex items-center space-x-2">
          {/* Undo/Redo */}
          <div className="flex items-center space-x-1">
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onUndo}
                  disabled={!canUndo || readOnly}
                  className="h-8 w-8 p-0"
                >
                  <Undo className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Desfazer (Ctrl+Z)</p>
              </TooltipContent>
            </Tooltip>

            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onRedo}
                  disabled={!canRedo || readOnly}
                  className="h-8 w-8 p-0"
                >
                  <Redo className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Refazer (Ctrl+Y)</p>
              </TooltipContent>
            </Tooltip>
          </div>

          <Separator orientation="vertical" className="h-6" />

          {/* Salvar */}
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                onClick={onSave}
                disabled={isSaving || readOnly}
                className="h-8 px-3"
              >
                {isSaving ? (
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                ) : (
                  <Save className="h-4 w-4 mr-2" />
                )}
                {isSaving ? 'Salvando...' : 'Salvar'}
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Salvar planilha (Ctrl+S)</p>
            </TooltipContent>
          </Tooltip>

          <Separator orientation="vertical" className="h-6" />

          {/* Gráficos */}
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  // TODO: Implementar funcionalidade de gráficos
                  logger.debug('Abrir painel de gráficos', { component: 'SpreadsheetToolbar', action: 'charts' });
                }}
                className="h-8 w-8 p-0"
              >
                <BarChart className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Gráficos e visualizações</p>
            </TooltipContent>
          </Tooltip>
        </div>

        {/* Lado direito - Controles de visualização */}
        <div className="flex items-center space-x-2">
          {/* Atalhos de teclado */}
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                onClick={onShowKeyboardShortcuts}
                className="h-8 w-8 p-0"
              >
                <KeyboardIcon className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Atalhos de teclado (?)</p>
            </TooltipContent>
          </Tooltip>

          {/* Tela cheia */}
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                onClick={onToggleFullScreen}
                className="h-8 w-8 p-0"
              >
                <FullscreenIcon className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>{isFullScreen ? 'Sair da tela cheia' : 'Tela cheia'} (F11)</p>
            </TooltipContent>
          </Tooltip>

          <Separator orientation="vertical" className="h-6" />

          {/* Toggle AI Panel */}
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                onClick={onToggleAIPanel}
                className="h-8 px-3"
              >
                {aiPanelCollapsed ? (
                  <ChevronLeft className="h-4 w-4 mr-2" />
                ) : (
                  <ChevronRight className="h-4 w-4 mr-2" />
                )}
                {aiPanelCollapsed ? 'Mostrar IA' : 'Ocultar IA'}
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Alternar painel de IA (Ctrl+Alt+I)</p>
            </TooltipContent>
          </Tooltip>
        </div>
      </div>
    </TooltipProvider>
  );
}
