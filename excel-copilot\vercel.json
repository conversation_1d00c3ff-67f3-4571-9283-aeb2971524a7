{"functions": {"src/app/api/**/*.ts": {"maxDuration": 30}}, "headers": [{"source": "/api/auth/(.*)", "headers": [{"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}, {"key": "X-Content-Type-Options", "value": "nosniff"}]}, {"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}]}], "rewrites": [{"source": "/api/:path*", "destination": "/api/:path*"}], "buildCommand": "node scripts/vercel-build-safe.js", "outputDirectory": ".next", "installCommand": "npm ci"}