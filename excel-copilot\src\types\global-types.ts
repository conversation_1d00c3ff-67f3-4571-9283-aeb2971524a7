// Tipos globais para substituir 'any' e melhorar type safety

// Tipos para Google Analytics
export interface GtagFunction {
  (command: 'event', eventName: string, parameters?: Record<string, unknown>): void;
  (command: 'config', targetId: string, config?: Record<string, unknown>): void;
  (command: string, ...args: unknown[]): void;
}

export interface WindowWithGtag {
  gtag?: GtagFunction;
}

// Tipos para Performance API com extensões do Chrome
export interface PerformanceMemory {
  jsHeapSizeLimit: number;
  totalJSHeapSize: number;
  usedJSHeapSize: number;
}

export interface ExtendedPerformance extends Performance {
  memory?: PerformanceMemory;
}

// Tipos para objetos globais
export interface GlobalWithRateLimiters {
  excelCopilotRateLimiters?: Map<string, unknown>;
}

// Tipos para serviços com métodos de cleanup
export interface ServiceWithShutdown {
  shutdown(): void | Promise<void>;
}

export interface ServiceWithDispose {
  dispose(): void | Promise<void>;
}

export interface ServiceWithClose {
  close(): void | Promise<void>;
}

export interface ServiceWithTerminate {
  terminate(): void | Promise<void>;
}

export interface ServiceWithDestroy {
  destroy(): void | Promise<void>;
}

export interface ServiceWithTimer {
  _timer: NodeJS.Timeout | number;
}

// Tipos para usuários e sessões
export interface UserWithId {
  id: string;
  email?: string;
  name?: string;
  image?: string;
  createdAt?: Date | string;
}

export interface SessionUserData {
  user?: UserWithId;
  [key: string]: unknown;
}

// Tipos para dados de uso e estatísticas
export interface UsageData {
  total: number;
  [key: string]: unknown;
}

export interface AccountData {
  createdAt: Date | string;
  email?: string;
  [key: string]: unknown;
}

// Tipos para eventos de banco de dados
export interface DatabaseEvent {
  duration?: number;
  query?: string;
  message?: string;
  [key: string]: unknown;
}

// Tipos para campos de data em queries
export interface DateRangeQuery {
  [field: string]: {
    gte?: Date;
    lte?: Date;
  };
}

// Tipo genérico para objetos com propriedades desconhecidas mas type-safe
export type SafeUnknownObject = Record<string, unknown>;

// Tipo para funções de callback genéricas
export type SafeCallback = (...args: unknown[]) => unknown;

// Tipo para componentes React que aceitam children
export interface ReactComponentWithChildren {
  children: React.ReactNode;
}

// Tipos para contextos com propriedades de compatibilidade
export interface LocaleContextType {
  locale: string;
  setLocale: (locale: string) => void;
  t: (key: string, params?: Record<string, unknown>) => string;
  _t: (key: string, params?: Record<string, unknown>) => string; // Alias para compatibilidade
  tPlural: (key: string, count: number, params?: Record<string, unknown>) => string;
  formatNumber: (value: number) => string;
  formatDate: (date: Date) => string;
}

// Tipos para hooks de tema
export interface UseThemeProps {
  theme?: string;
  setTheme: (theme: string) => void;
  resolvedTheme?: string;
  _theme: string; // Alias para compatibilidade
}

// Tipos para hooks de toast
export interface UseToastReturn {
  toast: (props: unknown) => { id: string; dismiss: () => void; update: (props: unknown) => void };
  dismiss: (toastId?: string) => void;
  toasts: unknown[];
  _toast: (props: unknown) => {
    id: string;
    dismiss: () => void;
    update: (props: unknown) => void;
  }; // Alias para compatibilidade
}

// Tipos para bridge state
export interface BridgeState {
  status: string;
  platform: string;
  [key: string]: unknown;
}

export interface BridgeActions {
  disconnect: () => void;
  [key: string]: unknown;
}

export interface ExtendedBridgeReturn extends BridgeState, BridgeActions {
  pendingOperations: number;
  lastOperation: { id: string; status: string } | null;
  executeCommand: (command: unknown) => Promise<unknown>;
  _disconnect: () => void; // Alias para compatibilidade
  _status: string; // Alias para compatibilidade
  _platform: string; // Alias para compatibilidade
}

// Tipos para props de componentes com variantes
export interface ComponentWithVariant {
  className?: string;
  variant?: 'default' | 'outline' | 'ghost';
  isMini?: boolean;
  _variant?: 'default' | 'outline' | 'ghost'; // Alias para compatibilidade
}
