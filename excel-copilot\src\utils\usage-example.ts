/**
 * Exemplos de uso dos utilitários tipados
 * Este arquivo demonstra como utilizar os utilitários para resolver problemas comuns de tipagem.
 */

import {
  // Utilitários de log tipados
  logError,

  // Utilitários de erro
  createDatabaseError,
  createExcelError,
  extractErrorInfo,

  // Utilitários de Excel
  parseCellReference,
  getCellValue,
  setCellValue,
} from '@/utils';

/**
 * Exemplo: Substituir logger.error com unknown para versão segura
 */
async function exampleLoggerUsage() {
  try {
    // Simular alguma operação que pode falhar
    const result = await fetchData();
    return result;
  } catch (error) {
    // ❌ Antes: Erro de tipagem com unknown
    // logger.error('Erro ao carregar dados', error);

    // ✅ Depois: Tipagem segura com o utilitário
    logError('Erro ao carregar dados', error);
    return null;
  }
}

/**
 * Exemplo: Criação e manipulação de erros tipados
 */
function exampleErrorHandling(userId: string) {
  try {
    // Alguma operação com banco de dados
    throw new Error('Falha na conexão');
  } catch (error) {
    // ❌ Antes: Erro de tipagem ao adicionar contexto
    // error.userId = userId; // Erro: propriedade userId não existe em Error

    // ✅ Depois: Criar um erro tipado com contexto
    const enhancedError = createDatabaseError('Falha ao consultar banco de dados', error, {
      userId,
      operation: 'SELECT',
    });

    // Logs tipados
    logError('Erro no banco de dados', enhancedError);

    // O erro já contém as informações de contexto
    return { success: false, error: enhancedError.message };
  }
}

/**
 * Exemplo: Manipulação segura de dados do Excel
 */
function exampleExcelOperations(sheetData: any[][], cellRef: string) {
  try {
    // ❌ Antes: Acesso potencialmente inseguro
    // const { row, col } = cellRef.match(/([A-Z]+)(\d+)/).groups;

    // ✅ Depois: Parsing seguro com tipagem correta
    const { row, col } = parseCellReference(cellRef);

    // ❌ Antes: Não verifica limites de array
    // const value = sheetData[row][col];

    // ✅ Depois: Acesso seguro com valor padrão
    const value = getCellValue(sheetData, row, col, '');

    // Atualizar valor de forma segura
    const updatedData = setCellValue(sheetData, row, col, value + ' (atualizado)');

    return { value, updatedData };
  } catch (error) {
    // Erro tipado específico para operações Excel
    const excelError = createExcelError('Erro ao manipular dados Excel', 'INVALID_OPERATION', {
      cellRef,
      errorDetails: extractErrorInfo(error),
    });

    logError('Falha na operação Excel', excelError);
    return { error: excelError.message };
  }
}

// Função fictícia para o exemplo
async function fetchData(): Promise<any> {
  // Simulação
  return Promise.reject(new Error('Serviço indisponível'));
}

// Exportar exemplos para possível uso em testes
export { exampleLoggerUsage, exampleErrorHandling, exampleExcelOperations };
