import { useState, useCallback } from 'react';
import { toast } from 'sonner';

import { ChatInput } from '@/components/chat-interface/ChatInput';
import { CommandFeedback, CommandFeedbackData } from '@/components/command-feedback';
import { CommandPreview } from '@/components/command-preview';
import { feedbackService } from '@/lib/ai/feedback-service';

// Interface local em vez de importar, baseada na que é necessária
interface CommandInterpretation {
  commandId?: string;
  _commandId?: string;
  interpretation: string;
}

// Interface para o hook useAIChat
interface UseAIChatResult {
  sendMessage: (message: string) => Promise<void>;
  isProcessing: boolean;
  confirmAndExecute: () => Promise<any>;
  cancelCommand: () => void;
  pendingInterpretation: CommandInterpretation | null;
  commandStatus: string;
  messages: any[];
  error: Error | null;
  clearMessages: () => void;
}

// Mock do hook useAIChat
const useAIChat = (options: any): UseAIChatResult => {
  const [isProcessing, _setIsProcessing] = useState(false);

  const sendMessage = async (_message: string) => {
    if (options.onMessageReceived) {
      options.onMessageReceived('Resposta simulada');
    }
  };

  const confirmAndExecute = async () => {
    return Promise.resolve(true);
  };

  const cancelCommand = () => {};

  return {
    sendMessage,
    isProcessing,
    confirmAndExecute,
    cancelCommand,
    pendingInterpretation: null,
    commandStatus: 'idle',
    messages: [],
    error: null,
    clearMessages: () => {},
  };
};

interface EnhancedChatInputProps {
  workbookId?: string;
  _workbookId?: string;
  onCommandExecuted?: (result: any) => void;
  disabled?: boolean;
  readOnly?: boolean;
}

/**
 * Componente de chat aprimorado com preview de interpretação e feedback
 */
export function EnhancedChatInput({
  _workbookId,
  workbookId = _workbookId,
  onCommandExecuted,
  disabled = false,
  readOnly = false,
}: EnhancedChatInputProps) {
  const [commandInterpretation, setCommandInterpretation] = useState<string | null>(null);
  const [pendingCommand, setPendingCommand] = useState<{ id: string; command: string } | null>(
    null
  );
  const [showFeedback, setShowFeedback] = useState(false);
  const [lastExecutedCommand, setLastExecutedCommand] = useState<{
    id: string;
    command: string;
  } | null>(null);
  const [_inputValue, setInputValue] = useState('');

  // Inicializar o chat AI
  const { sendMessage, isProcessing, confirmAndExecute, cancelCommand } = useAIChat({
    workbookId,
    onMessageReceived: (content: string) => {
      if (onCommandExecuted) {
        onCommandExecuted(content);
      }
    },
    onInterpretation: handleCommandInterpreted,
  });

  // Handler para comandos interpretados
  function handleCommandInterpreted(interpretation: CommandInterpretation) {
    setCommandInterpretation(interpretation.interpretation);
    setPendingCommand({
      id: interpretation.commandId || interpretation._commandId || '',
      command: interpretation.interpretation,
    });
  }

  // Handler para envio de mensagens do chat
  const handleSendMessage = useCallback(
    async (message: string) => {
      setInputValue('');
      return sendMessage(message);
    },
    [sendMessage]
  );

  // Handler para executar comando confirmado
  const handleExecuteCommand = useCallback(async () => {
    if (!pendingCommand) return;

    setCommandInterpretation(null);

    try {
      const result = await confirmAndExecute();

      if (result) {
        // Armazenar o último comando executado para feedback
        setLastExecutedCommand(pendingCommand);
        setShowFeedback(true);

        // Limpar após 5 segundos se não houver interação
        setTimeout(() => {
          setShowFeedback(prev => (prev ? false : prev));
        }, 5000);
      }
    } catch (error) {
      console.error('Erro ao executar comando:', error);
      toast.error('Erro ao executar o comando');
    } finally {
      setPendingCommand(null);
    }
  }, [pendingCommand, confirmAndExecute]);

  // Handler para cancelar comando pendente
  const handleCancelCommand = useCallback(() => {
    cancelCommand();
    setCommandInterpretation(null);
    setPendingCommand(null);
  }, [cancelCommand]);

  // Handler para envio de feedback
  const handleFeedbackSubmit = useCallback(async (feedback: CommandFeedbackData) => {
    try {
      await feedbackService.storeFeedback(feedback);
      setShowFeedback(false);
      setLastExecutedCommand(null);
    } catch (error) {
      console.error('Erro ao enviar feedback:', error);
      toast.error('Não foi possível enviar seu feedback');
    }
  }, []);

  // Handler para fechar o feedback sem enviar
  const handleDismissFeedback = useCallback(() => {
    setShowFeedback(false);
    setLastExecutedCommand(null);
  }, []);

  // Handler para mudanças no input
  const handleInputChange = useCallback((text: string) => {
    setInputValue(text);
  }, []);

  return (
    <div className="w-full space-y-3">
      {/* Pré-visualização de comando */}
      {commandInterpretation && (
        <CommandPreview
          command={pendingCommand?.command || ''}
          interpretation={commandInterpretation}
          isLoading={isProcessing}
          onExecute={handleExecuteCommand}
          onCancel={handleCancelCommand}
        />
      )}

      {/* Feedback sobre último comando */}
      {showFeedback && lastExecutedCommand && (
        <CommandFeedback
          commandId={lastExecutedCommand.id}
          command={lastExecutedCommand.command}
          onDismiss={handleDismissFeedback}
          onFeedbackSubmit={handleFeedbackSubmit}
        />
      )}

      {/* Input de chat */}
      <ChatInput
        onSendMessage={handleSendMessage}
        isLoading={isProcessing}
        disabled={disabled || readOnly || isProcessing}
        autoFocus={true}
        onChange={handleInputChange}
        placeholder="Digite um comando ou faça uma pergunta..."
      />
    </div>
  );
}
