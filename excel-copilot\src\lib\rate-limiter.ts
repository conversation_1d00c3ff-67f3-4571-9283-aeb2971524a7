/**
 * ⚡ SISTEMA DE RATE LIMITING AVANÇADO
 *
 * Implementa rate limiting rigoroso para endpoints de autenticação
 * e outras operações sensíveis
 */

import { Redis } from '@upstash/redis';

import { recordAuthEvent } from './auth-monitoring';
import { logger } from './logger';

/**
 * Configuração de rate limit
 */
export interface RateLimitConfig {
  windowMs: number; // Janela de tempo em ms
  maxRequests: number; // Máximo de requests na janela
  blockDurationMs: number; // Tempo de bloqueio após exceder limite
  skipSuccessfulRequests?: boolean; // <PERSON>ão contar requests bem-sucedidos
  skipFailedRequests?: boolean; // Não contar requests que falharam
  keyGenerator?: (identifier: string) => string; // Gerador de chave customizado
}

/**
 * Resultado do rate limiting
 */
export interface RateLimitResult {
  allowed: boolean;
  remaining: number;
  resetTime: number;
  totalHits: number;
  isBlocked: boolean;
  blockExpiresAt?: number;
  error?: string;
}

/**
 * Rate limiter baseado em Redis com fallback em memória
 */
class RateLimiter {
  private redis: Redis | null = null;
  private memoryStore = new Map<string, { count: number; resetTime: number; blocked?: number }>();
  private readonly CLEANUP_INTERVAL = 300000; // 5 minutos

  constructor() {
    // Inicializa Redis se disponível
    if (process.env.UPSTASH_REDIS_REST_URL && process.env.UPSTASH_REDIS_REST_TOKEN) {
      this.redis = new Redis({
        url: process.env.UPSTASH_REDIS_REST_URL,
        token: process.env.UPSTASH_REDIS_REST_TOKEN,
      });
    }

    // Limpeza periódica do store em memória
    setInterval(() => {
      this.cleanupMemoryStore();
    }, this.CLEANUP_INTERVAL);
  }

  /**
   * Verifica se uma requisição deve ser permitida
   */
  async checkLimit(identifier: string, config: RateLimitConfig): Promise<RateLimitResult> {
    const key = config.keyGenerator ? config.keyGenerator(identifier) : `rate_limit:${identifier}`;
    const now = Date.now();
    const windowStart = now - config.windowMs;

    try {
      if (this.redis) {
        return await this.checkLimitRedis(key, config, now, windowStart);
      } else {
        return this.checkLimitMemory(key, config, now, windowStart);
      }
    } catch (error) {
      logger.error('Rate limiter error, blocking request for security', { error, identifier });
      // CORREÇÃO DE SEGURANÇA: Em caso de erro, bloquear requisição (fail-closed)
      return {
        allowed: false,
        remaining: 0,
        resetTime: now + config.windowMs,
        totalHits: config.maxRequests,
        isBlocked: true,
        error: 'Rate limiter temporarily unavailable',
      };
    }
  }

  /**
   * Rate limiting usando Redis
   */
  private async checkLimitRedis(
    key: string,
    config: RateLimitConfig,
    now: number,
    windowStart: number
  ): Promise<RateLimitResult> {
    const blockKey = `${key}:blocked`;

    // Verifica se está bloqueado
    const blockExpiry = await this.redis!.get(blockKey);
    if (blockExpiry && parseInt(blockExpiry as string) > now) {
      return {
        allowed: false,
        remaining: 0,
        resetTime: parseInt(blockExpiry as string),
        totalHits: config.maxRequests,
        isBlocked: true,
        blockExpiresAt: parseInt(blockExpiry as string),
      };
    }

    // Pipeline para operações atômicas
    const pipeline = this.redis!.pipeline();

    // Remove entradas antigas da janela deslizante
    pipeline.zremrangebyscore(key, 0, windowStart);

    // Conta requests na janela atual
    pipeline.zcard(key);

    // Adiciona request atual
    pipeline.zadd(key, { score: now, member: `${now}-${Math.random()}` });

    // Define expiração da chave
    pipeline.expire(key, Math.ceil(config.windowMs / 1000));

    const results = await pipeline.exec();
    const countResult = results && results[1] && Array.isArray(results[1]) ? results[1][1] : 0;
    const currentCount = (typeof countResult === 'number' ? countResult : 0) + 1; // +1 pelo request atual

    const remaining = Math.max(0, config.maxRequests - currentCount);
    const resetTime = now + config.windowMs;

    // Se excedeu o limite, bloqueia
    if (currentCount > config.maxRequests) {
      const blockUntil = now + config.blockDurationMs;
      await this.redis!.setex(
        blockKey,
        Math.ceil(config.blockDurationMs / 1000),
        blockUntil.toString()
      );

      return {
        allowed: false,
        remaining: 0,
        resetTime: blockUntil,
        totalHits: currentCount,
        isBlocked: true,
        blockExpiresAt: blockUntil,
      };
    }

    return {
      allowed: true,
      remaining,
      resetTime,
      totalHits: currentCount,
      isBlocked: false,
    };
  }

  /**
   * Rate limiting usando memória (fallback)
   */
  private checkLimitMemory(
    key: string,
    config: RateLimitConfig,
    now: number,
    _windowStart: number
  ): RateLimitResult {
    const entry = this.memoryStore.get(key);

    // Verifica se está bloqueado
    if (entry?.blocked && entry.blocked > now) {
      return {
        allowed: false,
        remaining: 0,
        resetTime: entry.blocked,
        totalHits: config.maxRequests,
        isBlocked: true,
        blockExpiresAt: entry.blocked,
      };
    }

    // Reset se a janela expirou
    if (!entry || entry.resetTime <= now) {
      this.memoryStore.set(key, {
        count: 1,
        resetTime: now + config.windowMs,
      });

      return {
        allowed: true,
        remaining: config.maxRequests - 1,
        resetTime: now + config.windowMs,
        totalHits: 1,
        isBlocked: false,
      };
    }

    // Incrementa contador
    entry.count++;
    const remaining = Math.max(0, config.maxRequests - entry.count);

    // Se excedeu o limite, bloqueia
    if (entry.count > config.maxRequests) {
      const blockUntil = now + config.blockDurationMs;
      entry.blocked = blockUntil;

      return {
        allowed: false,
        remaining: 0,
        resetTime: blockUntil,
        totalHits: entry.count,
        isBlocked: true,
        blockExpiresAt: blockUntil,
      };
    }

    return {
      allowed: true,
      remaining,
      resetTime: entry.resetTime,
      totalHits: entry.count,
      isBlocked: false,
    };
  }

  /**
   * Limpa entradas expiradas do store em memória
   */
  private cleanupMemoryStore(): void {
    const now = Date.now();
    let cleaned = 0;

    for (const [key, entry] of this.memoryStore.entries()) {
      if (entry.resetTime <= now && (!entry.blocked || entry.blocked <= now)) {
        this.memoryStore.delete(key);
        cleaned++;
      }
    }

    if (cleaned > 0) {
      logger.debug(`Cleaned up ${cleaned} expired rate limit entries`);
    }
  }

  /**
   * Remove rate limit para um identificador específico
   */
  async resetLimit(identifier: string): Promise<void> {
    const key = `rate_limit:${identifier}`;

    try {
      if (this.redis) {
        await this.redis.del(key, `${key}:blocked`);
      } else {
        this.memoryStore.delete(key);
      }

      logger.info('Rate limit reset', { identifier });
    } catch (error) {
      logger.error('Failed to reset rate limit', { error, identifier });
    }
  }
}

// Instância global do rate limiter
const rateLimiter = new RateLimiter();

/**
 * Configurações predefinidas para diferentes tipos de endpoints
 */
export const RATE_LIMIT_CONFIGS = {
  // Autenticação - muito restritivo
  AUTH: {
    windowMs: 900000, // 15 minutos
    maxRequests: 5, // 5 tentativas por 15 min
    blockDurationMs: 1800000, // Bloqueia por 30 min
  } as RateLimitConfig,

  // Login - restritivo
  LOGIN: {
    windowMs: 300000, // 5 minutos
    maxRequests: 3, // 3 tentativas por 5 min
    blockDurationMs: 900000, // Bloqueia por 15 min
  } as RateLimitConfig,

  // API geral - moderado
  API: {
    windowMs: 60000, // 1 minuto
    maxRequests: 100, // 100 requests por minuto
    blockDurationMs: 300000, // Bloqueia por 5 min
  } as RateLimitConfig,

  // Operações sensíveis - muito restritivo
  SENSITIVE: {
    windowMs: 3600000, // 1 hora
    maxRequests: 10, // 10 operações por hora
    blockDurationMs: 3600000, // Bloqueia por 1 hora
  } as RateLimitConfig,

  // Upload de arquivos - moderado
  UPLOAD: {
    windowMs: 300000, // 5 minutos
    maxRequests: 20, // 20 uploads por 5 min
    blockDurationMs: 600000, // Bloqueia por 10 min
  } as RateLimitConfig,
};

/**
 * Middleware de rate limiting para Next.js
 */
export async function rateLimitMiddleware(
  identifier: string,
  config: RateLimitConfig,
  req?: { ip?: string; headers?: { 'user-agent'?: string } }
): Promise<RateLimitResult> {
  const result = await rateLimiter.checkLimit(identifier, config);

  // Log se foi bloqueado
  if (!result.allowed) {
    logger.warn('Rate limit exceeded', {
      identifier,
      totalHits: result.totalHits,
      maxRequests: config.maxRequests,
      isBlocked: result.isBlocked,
      blockExpiresAt: result.blockExpiresAt,
    });

    // Registra evento de rate limit se for relacionado a auth
    if (req && (identifier.includes('auth') || identifier.includes('login'))) {
      recordAuthEvent.rateLimitExceeded(
        req.ip || 'unknown',
        req.headers?.['user-agent'] || 'unknown'
      );
    }
  }

  return result;
}

/**
 * Helpers para rate limiting específico
 */
export const rateLimit = {
  /**
   * Rate limit para tentativas de login
   */
  login: async (ip: string, email?: string) => {
    const identifier = email ? `login:${email}:${ip}` : `login:${ip}`;
    return rateLimitMiddleware(identifier, RATE_LIMIT_CONFIGS.LOGIN);
  },

  /**
   * Rate limit para endpoints de autenticação
   */
  auth: async (ip: string) => {
    return rateLimitMiddleware(`auth:${ip}`, RATE_LIMIT_CONFIGS.AUTH);
  },

  /**
   * Rate limit para API geral
   */
  api: async (userId: string) => {
    return rateLimitMiddleware(`api:${userId}`, RATE_LIMIT_CONFIGS.API);
  },

  /**
   * Rate limit para operações sensíveis
   */
  sensitive: async (userId: string, operation: string) => {
    return rateLimitMiddleware(`sensitive:${operation}:${userId}`, RATE_LIMIT_CONFIGS.SENSITIVE);
  },

  /**
   * Rate limit para uploads
   */
  upload: async (userId: string) => {
    return rateLimitMiddleware(`upload:${userId}`, RATE_LIMIT_CONFIGS.UPLOAD);
  },

  /**
   * Rate limit customizado
   */
  custom: async (identifier: string, config: RateLimitConfig) => {
    return rateLimitMiddleware(identifier, config);
  },

  /**
   * Reset rate limit
   */
  reset: async (identifier: string) => {
    return rateLimiter.resetLimit(identifier);
  },
};

/**
 * Utilitário para extrair IP da requisição
 */
export function getClientIP(req: Record<string, unknown>): string {
  const headers = req.headers as Record<string, unknown> | undefined;
  const connection = req.connection as Record<string, unknown> | undefined;
  const socket = req.socket as Record<string, unknown> | undefined;

  const forwardedFor = headers?.['x-forwarded-for'] as string | undefined;
  const realIp = headers?.['x-real-ip'] as string | undefined;
  const connectionRemote = connection?.remoteAddress as string | undefined;
  const socketRemote = socket?.remoteAddress as string | undefined;

  return forwardedFor?.split(',')[0] || realIp || connectionRemote || socketRemote || 'unknown';
}

/**
 * Utilitário para extrair User-Agent
 */
export function getUserAgent(req: Record<string, unknown>): string {
  const headers = req.headers as Record<string, unknown> | undefined;
  return (headers?.['user-agent'] as string) || 'unknown';
}
