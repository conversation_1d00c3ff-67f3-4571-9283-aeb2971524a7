/**
 * @jest-environment node
 */

import { createClient } from '@supabase/supabase-js';
import * as dotenv from 'dotenv';
import * as path from 'path';
import { PrismaClient } from '@prisma/client';

// Carregar variáveis de ambiente
dotenv.config({ path: path.resolve(process.cwd(), '.env.local') });

// Verificar se temos as configurações necessárias
const hasConfig =
  typeof process.env.SUPABASE_URL === 'string' &&
  typeof process.env.SUPABASE_ANON_KEY === 'string' &&
  typeof process.env.DATABASE_URL === 'string';

// Só executar testes se as variáveis estiverem configuradas
const testOrSkip = hasConfig ? describe : describe.skip;

testOrSkip('Supabase Client Comprehensive Tests', () => {
  let supabase: ReturnType<typeof createClient>;
  let prisma: PrismaClient;

  beforeAll(() => {
    // Inicializar o cliente Supabase
    supabase = createClient(
      process.env.SUPABASE_URL as string,
      process.env.SUPABASE_ANON_KEY as string,
      {
        auth: {
          persistSession: false,
          autoRefreshToken: false,
        },
      }
    );

    // Inicializar o cliente Prisma
    prisma = new PrismaClient({
      datasources: {
        db: { url: process.env.DATABASE_URL || 'placeholder-url' },
      },
    });
  });

  afterAll(async () => {
    // Limpar recursos
    await prisma.$disconnect();
  });

  describe('Autenticação Supabase', () => {
    test('Serviço de autenticação está acessível', async () => {
      const { data, error } = await supabase.auth.getSession();

      expect(error).toBeNull();
      expect(data).toBeDefined();
    });

    test('Configuração de provedores OAuth está correta', async () => {
      // Verificar variáveis de ambiente para OAuth
      expect(process.env.GOOGLE_CLIENT_ID).toBeDefined();
      expect(process.env.GOOGLE_CLIENT_SECRET).toBeDefined();
      expect(process.env.GITHUB_CLIENT_ID).toBeDefined();
      expect(process.env.GITHUB_CLIENT_SECRET).toBeDefined();
    });
  });

  describe('Banco de Dados Supabase', () => {
    test('Conexão PostgreSQL está funcional', async () => {
      await expect(prisma.$connect()).resolves.not.toThrow();
    });

    test('Pode consultar metadados do banco', async () => {
      try {
        // Consultar versão do PostgreSQL
        const result = await prisma.$queryRaw`SELECT version()`;
        expect(result).toBeDefined();

        // Exibir versão para diagnóstico
        if (Array.isArray(result) && result.length > 0) {
          console.log(`Versão do PostgreSQL: ${JSON.stringify(result[0])}`);
        }
      } catch (error) {
        console.error('Erro ao consultar versão do PostgreSQL:', error);
        throw error;
      }
    });

    test('Pode consultar tabelas via Supabase', async () => {
      const { data, error } = await supabase
        .from('_prisma_migrations')
        .select('id, applied_steps_count')
        .limit(1);

      expect(error).toBeNull();
      expect(data).toBeDefined();
    });
  });

  describe('Funcionalidades Supabase Storage', () => {
    test('Serviço de storage está acessível', async () => {
      const { data, error } = await supabase.storage.listBuckets();

      expect(error).toBeNull();
      expect(Array.isArray(data)).toBe(true);
    });

    test('Pode obter bucket de avatares se existir', async () => {
      // Tenta acessar um bucket comum
      const { data: buckets } = await supabase.storage.listBuckets();

      if (buckets && buckets.some(b => b.name === 'avatars')) {
        const { data, error } = await supabase.storage.from('avatars').list();
        expect(error).toBeNull();
        expect(Array.isArray(data)).toBe(true);
      } else {
        console.log('Bucket "avatars" não encontrado, pulando teste');
      }
    });
  });

  describe('Monitoramento da Integridade', () => {
    test('Prisma e Supabase têm a mesma conexão subjacente', async () => {
      // Verificar se o host do banco no Prisma corresponde ao domínio do Supabase
      const supabaseDomain = new URL(process.env.SUPABASE_URL as string).hostname;
      const supabaseProjectId = supabaseDomain.split('.')[0];

      // Verificar se DATABASE_URL contém o mesmo ID de projeto
      // Existem dois formatos possíveis:
      // 1. Conexão pooling: aws-0-[region].pooler.supabase.com
      // 2. Conexão direta: db.[project-id].supabase.co
      const databaseUrl = process.env.DATABASE_URL as string;

      if (databaseUrl.includes('pooler.supabase.com')) {
        // OK, usando conexão pooling
        expect(databaseUrl).toContain('pooler.supabase.com');
      } else {
        // Se usar conexão direta, deve conter o mesmo ID de projeto
        expect(databaseUrl).toContain(`db.${supabaseProjectId}`);
      }
    });

    test('Configuração RLS está presente', async () => {
      // Verificar a existência de políticas RLS
      try {
        const { data, error } = await supabase.from('pg_policies').select('*').limit(10);

        // Alguns projetos podem não ter acesso à tabela pg_policies
        // Não falhar o teste, apenas registrar
        if (error) {
          console.log('Não foi possível verificar políticas RLS:', error.message);
        } else {
          console.log(`Encontradas ${data?.length || 0} políticas RLS`);
        }
      } catch (error) {
        console.log('Erro ao verificar políticas RLS:', error);
      }
    });
  });

  describe('Testes de Integração entre Prisma e Supabase', () => {
    test('Esquema Prisma corresponde ao banco Supabase', async () => {
      // Definir tipagem para as tabelas retornadas
      interface TableRow {
        table_name: string;
      }

      // Obter tabelas no banco
      const { data, error } = await supabase
        .from('information_schema.tables')
        .select('table_name')
        .eq('table_schema', 'public')
        .neq('table_name', '_prisma_migrations');

      if (error) {
        console.log('Não foi possível verificar tabelas:', error.message);
        return;
      }

      // Converter os dados para o tipo correto
      const tablesData = data as TableRow[];

      // Verificar existência de tabelas essenciais
      const tables = tablesData.map(t => t.table_name.toLowerCase());

      expect(tables).toEqual(
        expect.arrayContaining([expect.stringMatching(/user/i), expect.stringMatching(/session/i)])
      );

      console.log('Tabelas encontradas:', tables.join(', '));
    });
  });
});
