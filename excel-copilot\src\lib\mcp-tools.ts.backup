/**
 * Ferramentas MCP para integração com serviços externos
 * Este arquivo fornece wrappers para as integrações MCP disponíveis
 */

import { logger } from './logger';

/**
 * Interface para chamadas MCP Linear
 */
interface LinearMCPCall {
  summary: string;
  query: string;
  is_read_only: boolean;
}

/**
 * Interface para resposta MCP Linear
 */
interface LinearMCPResponse {
  viewer?: {
    id: string;
    name: string;
    email: string;
    organization: {
      id: string;
      name: string;
      urlKey: string;
    };
    teams: {
      nodes: Array<{
        id: string;
        name: string;
        key: string;
        description?: string;
      }>;
    };
    assignedIssues: {
      nodes: Array<{
        id: string;
        identifier: string;
        title: string;
        state: {
          name: string;
        };
        createdAt: string;
      }>;
    };
  };
  issues?: {
    nodes: Array<{
      id: string;
      identifier: string;
      title: string;
      description?: string;
      state: {
        id: string;
        name: string;
        type: string;
      };
      team: {
        id: string;
        name: string;
        key: string;
      };
      assignee?: {
        id: string;
        name: string;
        email: string;
      };
      labels?: {
        nodes: Array<{
          id: string;
          name: string;
          color: string;
        }>;
      };
      createdAt: string;
      updatedAt: string;
      priority?: number;
      estimate?: number;
    }>;
  };
  teams?: {
    nodes: Array<{
      id: string;
      name: string;
      key: string;
      description?: string;
      states?: {
        nodes: Array<{
          id: string;
          name: string;
          type: string;
          color: string;
        }>;
      };
    }>;
  };
  organization?: {
    id: string;
    name: string;
    urlKey: string;
    createdAt: string;
  };
  projects?: {
    nodes: Array<{
      id: string;
      name: string;
      description?: string;
      state: string;
      progress?: number;
      targetDate?: string;
      createdAt: string;
      updatedAt: string;
    }>;
  };
  workflowStates?: {
    nodes: Array<{
      id: string;
      name: string;
      type: string;
      color: string;
      team: {
        id: string;
        name: string;
      };
    }>;
  };
}

/**
 * Dados mockados para desenvolvimento e testes
 */
const MOCK_LINEAR_DATA: LinearMCPResponse = {
  viewer: {
    id: '0c54dadc-ca2c-4c4f-8c17-98841efc500d',
    name: 'Cauã Alves',
    email: '<EMAIL>',
    organization: {
      id: '0f3119e5-04aa-4d9c-b772-c51ad39f3931',
      name: 'ngbprojectlinear',
      urlKey: 'ngbprojectlinear',
    },
    teams: {
      nodes: [
        {
          id: 'c0752512-d166-40b3-a89d-7206e26223ac',
          name: 'Ngbprojectlinear',
          key: 'NGB',
          description: 'Team principal do Excel Copilot',
        },
      ],
    },
    assignedIssues: {
      nodes: [
        {
          id: '8aab86d3-30ed-41b8-924f-5f374d28ab14',
          identifier: 'NGB-15',
          title: 'Melhorar Pipeline CI/CD e Automação de Deploy',
          state: { name: 'Backlog' },
          createdAt: '2025-05-29T11:46:55.942Z',
        },
        {
          id: '965e88d8-1332-4a3a-9fb7-4ad897cff8e5',
          identifier: 'NGB-14',
          title: 'Otimizar Performance de Processamento Excel e APIs',
          state: { name: 'Backlog' },
          createdAt: '2025-05-29T11:46:20.760Z',
        },
        {
          id: '48797cb3-1fec-42db-913a-4ee7f49e4473',
          identifier: 'NGB-13',
          title: 'Melhorar Cobertura de Testes para Processamento Excel e IA',
          state: { name: 'Backlog' },
          createdAt: '2025-05-29T11:45:59.048Z',
        },
        {
          id: 'db16c0e7-1efb-4875-9994-eaccfa05a873',
          identifier: 'NGB-12',
          title: 'Otimizar Configuração e Debugging do Vertex AI',
          state: { name: 'Todo' },
          createdAt: '2025-05-29T11:45:39.832Z',
        },
        {
          id: '0b345496-cd93-487c-8b31-4a020c6f9b8d',
          identifier: 'NGB-11',
          title: 'Implementar Sentry MCP Integration para Error Tracking Avançado',
          state: { name: 'Todo' },
          createdAt: '2025-05-29T11:45:20.031Z',
        },
      ],
    },
  },
};

/**
 * Função para chamar a integração Linear MCP
 */
export async function linear(params: LinearMCPCall): Promise<LinearMCPResponse> {
  try {
    logger.info(`Executando consulta Linear MCP: ${params.summary}`);

    // Por enquanto, retorna dados mockados baseados nos dados reais do workspace
    // Em produção, isso seria substituído pela chamada real para a MCP

    if (params.query.includes('workspace') || params.query.includes('organization')) {
      return {
        organization: MOCK_LINEAR_DATA.viewer?.organization,
        teams: MOCK_LINEAR_DATA.viewer?.teams,
      } as LinearMCPResponse;
    }

    if (params.query.includes('issues')) {
      let filteredIssues = MOCK_LINEAR_DATA.viewer?.assignedIssues.nodes || [];

      logger.info(`Query recebida: ${params.query}`);

      // Aplica filtros baseados na query
      if (params.query.includes('state: { name: { eq:')) {
        const stateMatch = params.query.match(/state: { name: { eq: "([^"]+)" } }/);
        if (stateMatch) {
          const targetState = stateMatch[1];
          logger.info(`Filtrando por estado: ${targetState}`);
          filteredIssues = filteredIssues.filter(issue => issue.state.name === targetState);
          logger.info(`Issues após filtro: ${filteredIssues.length}`);
        }
      }

      if (params.query.includes('team: { id: { eq:')) {
        const teamMatch = params.query.match(/team: { id: { eq: "([^"]+)" } }/);
        if (teamMatch) {
          const targetTeamId = teamMatch[1];
          // Todos os issues mockados são do mesmo team, então não filtra
        }
      }

      if (params.query.includes('assignee: { id: { eq:')) {
        const assigneeMatch = params.query.match(/assignee: { id: { eq: "([^"]+)" } }/);
        if (assigneeMatch) {
          const targetAssigneeId = assigneeMatch[1];
          // Todos os issues mockados são do mesmo assignee, então não filtra
        }
      }

      return {
        issues: {
          nodes: filteredIssues.map(issue => ({
            id: issue.id,
            identifier: issue.identifier,
            title: issue.title,
            description: `Descrição da issue ${issue.identifier}`,
            state: {
              id: `state-${issue.state.name.toLowerCase()}`,
              name: issue.state.name,
              type: issue.state.name === 'Todo' ? 'unstarted' : 'backlog',
            },
            team: {
              id: MOCK_LINEAR_DATA.viewer?.teams.nodes[0]?.id || '',
              name: MOCK_LINEAR_DATA.viewer?.teams.nodes[0]?.name || '',
              key: MOCK_LINEAR_DATA.viewer?.teams.nodes[0]?.key || '',
            },
            assignee: {
              id: MOCK_LINEAR_DATA.viewer?.id || '',
              name: MOCK_LINEAR_DATA.viewer?.name || '',
              email: MOCK_LINEAR_DATA.viewer?.email || '',
            },
            labels: { nodes: [] },
            createdAt: issue.createdAt,
            updatedAt: issue.createdAt,
            priority: 2,
            estimate: undefined,
          })),
        },
      } as LinearMCPResponse;
    }

    if (params.query.includes('teams')) {
      return {
        teams: {
          nodes:
            MOCK_LINEAR_DATA.viewer?.teams.nodes.map(team => ({
              ...team,
              states: {
                nodes: [
                  { id: 'state-backlog', name: 'Backlog', type: 'backlog', color: '#95a2b3' },
                  { id: 'state-todo', name: 'Todo', type: 'unstarted', color: '#3b82f6' },
                  {
                    id: 'state-in-progress',
                    name: 'In Progress',
                    type: 'started',
                    color: '#f59e0b',
                  },
                  { id: 'state-done', name: 'Done', type: 'completed', color: '#10b981' },
                ],
              },
            })) || [],
        },
      } as LinearMCPResponse;
    }

    if (params.query.includes('projects')) {
      return {
        projects: {
          nodes: [
            {
              id: 'project-excel-copilot',
              name: 'Excel Copilot',
              description: 'SaaS de planilhas colaborativas com IA integrada',
              state: 'started',
              progress: 75,
              targetDate: '2025-12-31T00:00:00.000Z',
              createdAt: '2025-01-01T00:00:00.000Z',
              updatedAt: new Date().toISOString(),
            },
          ],
        },
      } as LinearMCPResponse;
    }

    // Suporte para mutations (criação e atualização de issues)
    if (
      params.query.includes('Create') ||
      params.query.includes('Update') ||
      !params.is_read_only
    ) {
      const newIssueId = `issue-${Date.now()}`;
      const newIssue = {
        id: newIssueId,
        identifier: `NGB-${Math.floor(Math.random() * 1000)}`,
        title: params.query.includes('Create')
          ? 'Nova Issue Criada via MCP'
          : 'Issue Atualizada via MCP',
        description: 'Issue processada através da integração MCP Linear',
        state: {
          id: 'state-todo',
          name: 'Todo',
          type: 'unstarted',
        },
        team: {
          id: MOCK_LINEAR_DATA.viewer?.teams.nodes[0]?.id || '',
          name: MOCK_LINEAR_DATA.viewer?.teams.nodes[0]?.name || '',
          key: MOCK_LINEAR_DATA.viewer?.teams.nodes[0]?.key || '',
        },
        assignee: {
          id: MOCK_LINEAR_DATA.viewer?.id || '',
          name: MOCK_LINEAR_DATA.viewer?.name || '',
          email: MOCK_LINEAR_DATA.viewer?.email || '',
        },
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      if (params.query.includes('Create')) {
        return {
          issueCreate: {
            success: true,
            issue: newIssue,
          },
        } as any;
      } else if (params.query.includes('Update')) {
        return {
          issueUpdate: {
            success: true,
            issue: newIssue,
          },
        } as any;
      }
    }

    // Retorna dados básicos do viewer por padrão
    return MOCK_LINEAR_DATA;
  } catch (error) {
    logger.error('Erro na integração Linear MCP:', error);
    throw new Error(
      `Falha na integração Linear MCP: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
    );
  }
}

/**
 * Função para verificar se a integração MCP está disponível
 */
export function isMCPAvailable(): boolean {
  // Por enquanto sempre retorna true, pois estamos usando dados mockados
  // Em produção, isso verificaria se a integração MCP está realmente disponível
  return true;
}
