/**
 * API endpoint para teams e workflow states do Linear
 * GET /api/linear/teams - Lista teams e seus estados
 */

import { NextRequest, NextResponse } from 'next/server';

import { LinearClient } from '@/lib/linear-integration';
import { logger } from '@/lib/logger';

// Forçar o modo dinâmico para essa rota
export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const includeStates = searchParams.get('includeStates') !== 'false'; // true por padrão
    const includeProjects = searchParams.get('includeProjects') === 'true';

    const linearClient = new LinearClient();

    // Busca teams
    const teams = await linearClient.getTeams();

    const response: {
      status: string;
      teams: unknown[];
      summary: {
        totalTeams: number;
        teamKeys: string[];
        totalProjects?: number;
      };
      timestamp: string;
      workflowStates?: unknown;
      statesByTeam?: Record<string, unknown[]>;
      projects?: unknown;
    } = {
      status: 'success',
      teams: teams.teams,
      summary: {
        totalTeams: teams.teams.length,
        teamKeys: teams.teams.map(team => team.key),
      },
      timestamp: new Date().toISOString(),
    };

    // Inclui workflow states se solicitado
    if (includeStates) {
      try {
        const workflowStates = await linearClient.getWorkflowStates();
        response.workflowStates = workflowStates.states;

        // Agrupa states por team
        const statesByTeam = workflowStates.states.reduce(
          (acc, state) => {
            const teamName = state.team.name;
            if (!acc[teamName]) {
              acc[teamName] = [];
            }
            acc[teamName].push(state);
            return acc;
          },
          {} as Record<string, unknown[]>
        );

        response.statesByTeam = statesByTeam;
      } catch (error) {
        logger.warn('Erro ao obter workflow states:', error);
        response.workflowStates = { error: 'Falha ao obter workflow states' };
      }
    }

    // Inclui projetos se solicitado
    if (includeProjects) {
      try {
        const projects = await linearClient.getProjects();
        response.projects = projects.projects;
        response.summary.totalProjects = projects.projects.length;
      } catch (error) {
        logger.warn('Erro ao obter projetos:', error);
        response.projects = { error: 'Falha ao obter projetos' };
      }
    }

    return NextResponse.json(response);
  } catch (error) {
    logger.error('Erro ao listar teams Linear:', error);

    return NextResponse.json(
      {
        status: 'error',
        message: error instanceof Error ? error.message : 'Erro interno do servidor',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
