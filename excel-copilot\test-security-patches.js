#!/usr/bin/env node

/**
 * 🔒 TESTE DAS CORREÇÕES DE SEGURANÇA IMPLEMENTADAS
 * Excel Copilot - Sistema de Preços e Privilégios
 *
 * Este script testa as correções críticas implementadas
 */

import { execSync } from 'child_process';
import fs from 'fs';

console.log('🔒 TESTE DAS CORREÇÕES DE SEGURANÇA');
console.log('='.repeat(50));

// Função para executar comando e capturar resultado
function runCommand(command, description) {
  console.log(`\n🔍 ${description}...`);
  try {
    const result = execSync(command, { encoding: 'utf8', stdio: 'pipe' });
    console.log(`✅ ${description}: SUCESSO`);
    return { success: true, output: result };
  } catch (error) {
    console.log(`❌ ${description}: FALHOU`);
    console.log(`Erro: ${error.message}`);
    return { success: false, error: error.message };
  }
}

// Função para verificar se arquivo existe e contém função específica
function checkFileContains(filePath, searchString, description) {
  console.log(`\n🔍 ${description}...`);

  if (!fs.existsSync(filePath)) {
    console.log(`❌ ${description}: Arquivo não encontrado - ${filePath}`);
    return false;
  }

  const content = fs.readFileSync(filePath, 'utf8');
  if (content.includes(searchString)) {
    console.log(`✅ ${description}: ENCONTRADO`);
    return true;
  } else {
    console.log(`❌ ${description}: NÃO ENCONTRADO`);
    return false;
  }
}

// TESTE 1: Verificar se as funções críticas foram implementadas
console.log('\n📋 TESTE 1: VERIFICAÇÃO DE IMPLEMENTAÇÃO');
console.log('-'.repeat(40));

const checks = [
  {
    file: 'src/lib/subscription-limits.ts',
    search: 'createWorkbookAtomic',
    desc: 'Função createWorkbookAtomic implementada',
  },
  {
    file: 'src/lib/subscription-limits.ts',
    search: "isolationLevel: 'Serializable'",
    desc: 'Transação atômica com isolamento Serializable',
  },
  {
    file: 'src/lib/middleware/plan-based-rate-limiter-secure.ts',
    search: 'generateCacheSignature',
    desc: 'Validação HMAC de cache implementada',
  },
  {
    file: 'src/lib/middleware/plan-based-rate-limiter-secure.ts',
    search: 'userHash',
    desc: 'Logs sanitizados implementados',
  },
  {
    file: 'src/app/api/workbook/save/route.ts',
    search: 'createWorkbookAtomic',
    desc: 'API usando função atômica',
  },
];

let implementationScore = 0;
checks.forEach(check => {
  if (checkFileContains(check.file, check.search, check.desc)) {
    implementationScore++;
  }
});

console.log(`\n📊 Score de Implementação: ${implementationScore}/${checks.length}`);

// TESTE 2: Verificação de TypeScript
console.log('\n📋 TESTE 2: VERIFICAÇÃO DE TYPESCRIPT');
console.log('-'.repeat(40));

const typeCheckResult = runCommand('npm run type-check', 'Verificação de tipos TypeScript');

// TESTE 3: Verificação de Linting
console.log('\n📋 TESTE 3: VERIFICAÇÃO DE LINTING');
console.log('-'.repeat(40));

const lintResult = runCommand('npm run lint', 'Verificação de linting');

// TESTE 4: Verificação de Variáveis de Ambiente
console.log('\n📋 TESTE 4: VERIFICAÇÃO DE VARIÁVEIS DE AMBIENTE');
console.log('-'.repeat(40));

const envFiles = ['.env.local', '.env.production', '.env'];
let envScore = 0;

envFiles.forEach(envFile => {
  if (fs.existsSync(envFile)) {
    const content = fs.readFileSync(envFile, 'utf8');
    if (content.includes('CACHE_SECRET')) {
      console.log(`✅ CACHE_SECRET encontrado em ${envFile}`);
      envScore++;
    } else {
      console.log(`⚠️ CACHE_SECRET não encontrado em ${envFile}`);
    }
  } else {
    console.log(`⚠️ Arquivo ${envFile} não encontrado`);
  }
});

// TESTE 5: Simulação de Race Condition (conceitual)
console.log('\n📋 TESTE 5: ANÁLISE DE PROTEÇÃO CONTRA RACE CONDITIONS');
console.log('-'.repeat(40));

const raceConditionProtection = checkFileContains(
  'src/lib/subscription-limits.ts',
  'prisma.$transaction',
  'Proteção contra race conditions com transações'
);

// TESTE 6: Verificação de Logs Sanitizados
console.log('\n📋 TESTE 6: VERIFICAÇÃO DE LOGS SANITIZADOS');
console.log('-'.repeat(40));

const logSanitization = [
  checkFileContains(
    'src/lib/middleware/plan-based-rate-limiter-secure.ts',
    'createHash',
    'Hash de IDs de usuários em logs'
  ),
  checkFileContains(
    'src/lib/middleware/plan-based-rate-limiter-secure.ts',
    'planTier',
    'Categorização de planos em logs'
  ),
];

const logScore = logSanitization.filter(Boolean).length;

// RELATÓRIO FINAL
console.log('\n🎯 RELATÓRIO FINAL DE SEGURANÇA');
console.log('='.repeat(50));

const totalTests = 6;
let passedTests = 0;

// Avaliar cada teste
if (implementationScore === checks.length) {
  console.log('✅ TESTE 1: Implementação - PASSOU');
  passedTests++;
} else {
  console.log('❌ TESTE 1: Implementação - FALHOU');
}

if (typeCheckResult.success) {
  console.log('✅ TESTE 2: TypeScript - PASSOU');
  passedTests++;
} else {
  console.log('❌ TESTE 2: TypeScript - FALHOU');
}

if (lintResult.success) {
  console.log('✅ TESTE 3: Linting - PASSOU');
  passedTests++;
} else {
  console.log('⚠️ TESTE 3: Linting - WARNINGS (aceitável)');
  passedTests += 0.5; // Meio ponto para warnings
}

if (envScore > 0) {
  console.log('✅ TESTE 4: Variáveis de Ambiente - PASSOU');
  passedTests++;
} else {
  console.log('❌ TESTE 4: Variáveis de Ambiente - FALHOU');
}

if (raceConditionProtection) {
  console.log('✅ TESTE 5: Proteção Race Conditions - PASSOU');
  passedTests++;
} else {
  console.log('❌ TESTE 5: Proteção Race Conditions - FALHOU');
}

if (logScore === logSanitization.length) {
  console.log('✅ TESTE 6: Logs Sanitizados - PASSOU');
  passedTests++;
} else {
  console.log('❌ TESTE 6: Logs Sanitizados - FALHOU');
}

// Pontuação final
const finalScore = Math.round((passedTests / totalTests) * 100);

console.log('\n📊 PONTUAÇÃO FINAL DE SEGURANÇA');
console.log('-'.repeat(30));
console.log(`Testes Passados: ${Math.floor(passedTests)}/${totalTests}`);
console.log(`Pontuação: ${finalScore}%`);

if (finalScore >= 85) {
  console.log('🎉 EXCELENTE: Patches de segurança implementados com sucesso!');
  console.log('✅ Sistema pronto para deploy em produção');
} else if (finalScore >= 70) {
  console.log('⚠️ BOM: Maioria dos patches implementados');
  console.log('🔧 Algumas correções ainda necessárias');
} else {
  console.log('❌ CRÍTICO: Patches de segurança incompletos');
  console.log('🚨 NÃO FAZER DEPLOY até corrigir problemas');
}

// Recomendações específicas
console.log('\n💡 RECOMENDAÇÕES:');
console.log('-'.repeat(20));

if (!typeCheckResult.success) {
  console.log('• Corrigir erros de TypeScript antes do deploy');
}

if (envScore === 0) {
  console.log('• Configurar variável CACHE_SECRET nos arquivos .env');
}

if (implementationScore < checks.length) {
  console.log('• Verificar se todas as funções de segurança foram implementadas');
}

if (!raceConditionProtection) {
  console.log('• Implementar proteção contra race conditions com transações');
}

console.log('\n🔒 PRÓXIMOS PASSOS:');
console.log('1. Corrigir problemas identificados');
console.log('2. Executar testes em ambiente de staging');
console.log('3. Monitorar logs de segurança');
console.log('4. Deploy gradual em produção');

console.log('\n✅ TESTE DE SEGURANÇA CONCLUÍDO');

// Retornar código de saída baseado na pontuação
process.exit(finalScore >= 85 ? 0 : 1);
