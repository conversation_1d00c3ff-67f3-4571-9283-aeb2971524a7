#!/usr/bin/env node

/**
 * Script para detectar e corrigir URLs malformadas com caracteres invisíveis
 * que estão causando o erro "Invalid URL" no build do Vercel
 */

const fs = require('fs');
const path = require('path');

const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m',
};

console.log(`${colors.blue}🔧 Detectando e corrigindo URLs malformadas...${colors.reset}`);

// Lista de arquivos para verificar
const filesToCheck = ['.env.local', '.env.production', '.env.do.vercel', '.env.verificacao'];

let totalFixed = 0;

function cleanUrl(url) {
  // Remove caracteres invisíveis comuns
  return url
    .replace(/[\r\n\t]/g, '') // Remove \r, \n, \t
    .replace(/\s+$/, '') // Remove espaços no final
    .replace(/^\s+/, '') // Remove espaços no início
    .replace(/["']/g, '') // Remove aspas extras
    .trim();
}

function detectInvisibleChars(text) {
  const invisibleChars = [];

  for (let i = 0; i < text.length; i++) {
    const char = text[i];
    const code = char.charCodeAt(0);

    // Detectar caracteres invisíveis comuns
    if (code === 13) invisibleChars.push({ char: '\\r', pos: i });
    if (code === 10) invisibleChars.push({ char: '\\n', pos: i });
    if (code === 9) invisibleChars.push({ char: '\\t', pos: i });
    if (code === 160) invisibleChars.push({ char: 'NBSP', pos: i });
    if (code === 8203) invisibleChars.push({ char: 'ZWSP', pos: i });
  }

  return invisibleChars;
}

for (const fileName of filesToCheck) {
  const filePath = path.join(__dirname, fileName);

  if (!fs.existsSync(filePath)) {
    console.log(`${colors.yellow}⚠️ Arquivo ${fileName} não encontrado${colors.reset}`);
    continue;
  }

  console.log(`\n${colors.blue}📋 Verificando ${fileName}...${colors.reset}`);

  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n');
    let hasChanges = false;
    const newLines = [];

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const lineNum = i + 1;

      // Verificar linhas que contêm URLs
      if (
        line.includes('https://excel-copilot-eight.vercel.app') ||
        line.includes('NEXT_PUBLIC_APP_URL') ||
        line.includes('APP_URL') ||
        line.includes('NEXTAUTH_URL')
      ) {
        const invisibleChars = detectInvisibleChars(line);

        if (invisibleChars.length > 0) {
          console.log(
            `${colors.red}❌ Linha ${lineNum}: Caracteres invisíveis detectados:${colors.reset}`
          );
          invisibleChars.forEach(({ char, pos }) => {
            console.log(`   Posição ${pos}: ${char}`);
          });

          // Limpar a linha
          const cleanedLine = line.replace(/[\r\n\t\u00A0\u200B]/g, '');
          newLines.push(cleanedLine);
          hasChanges = true;
          totalFixed++;

          console.log(`${colors.green}✅ Linha corrigida${colors.reset}`);
        } else {
          newLines.push(line);
        }

        // Verificar se a URL está bem formada
        const urlMatch = line.match(/=["']?(https?:\/\/[^"'\s]+)["']?/);
        if (urlMatch) {
          const url = urlMatch[1];
          const cleanedUrl = cleanUrl(url);

          if (url !== cleanedUrl) {
            console.log(`${colors.yellow}⚠️ URL pode estar malformada:${colors.reset}`);
            console.log(`   Original: "${url}"`);
            console.log(`   Limpa: "${cleanedUrl}"`);

            const newLine = line.replace(url, cleanedUrl);
            newLines[newLines.length - 1] = newLine;
            hasChanges = true;
            totalFixed++;
          }
        }
      } else {
        newLines.push(line);
      }
    }

    if (hasChanges) {
      const newContent = newLines.join('\n');
      fs.writeFileSync(filePath, newContent, 'utf8');
      console.log(`${colors.green}✅ Arquivo ${fileName} corrigido${colors.reset}`);
    } else {
      console.log(`${colors.green}✅ Arquivo ${fileName} está limpo${colors.reset}`);
    }
  } catch (error) {
    console.error(`${colors.red}❌ Erro ao processar ${fileName}:${colors.reset}`, error.message);
  }
}

console.log(`\n${colors.bold}📊 Resumo:${colors.reset}`);
console.log(`Total de correções aplicadas: ${totalFixed}`);

if (totalFixed > 0) {
  console.log(
    `\n${colors.green}✅ Correções aplicadas! Tente fazer o build novamente.${colors.reset}`
  );
  console.log(`${colors.blue}💡 Comando: npm run build${colors.reset}`);
} else {
  console.log(
    `\n${colors.yellow}⚠️ Nenhuma URL malformada detectada nos arquivos .env${colors.reset}`
  );
  console.log(`${colors.blue}💡 O erro pode estar em outro lugar. Verifique:${colors.reset}`);
  console.log(`   - Variáveis de ambiente na Vercel`);
  console.log(`   - Arquivos de configuração do Next.js`);
  console.log(`   - Componentes que usam URLs`);
}
