/**
 * Hook para gerenciar eventos de analytics com Google Tag Manager
 */
import { AnalyticsEventProperties, AnalyticsEventType } from '@/types/analytics';

/**
 * Hook para registrar eventos de analytics de forma padronizada
 */
export function useAnalytics() {
  /**
   * Registra um evento de analytics
   * @param eventName Nome do evento
   * @param params Parâmetros adicionais do evento
   */
  const trackEvent = (
    eventName: string | AnalyticsEventType,
    params: AnalyticsEventProperties = {}
  ) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', eventName, params);
    }
  };

  return { trackEvent };
}
