// Exportações principais
export { SpreadsheetEditor } from './SpreadsheetEditor';
export { SpreadsheetEditorRefactored } from './SpreadsheetEditorRefactored';

// Context e tipos
export { SpreadsheetProvider, useSpreadsheetContext } from './SpreadsheetContext';
export type { SpreadsheetData, UIState, SpreadsheetState } from './SpreadsheetContext';

// Hooks customizados
export { useSpreadsheetData } from './hooks/useSpreadsheetData';
export { useSpreadsheetUI } from './hooks/useSpreadsheetUI';
export { useSpreadsheetKeyboard } from './hooks/useSpreadsheetKeyboard';

// Componentes refatorados
export { SpreadsheetToolbar } from './components/SpreadsheetToolbar';
export { SpreadsheetGrid } from './components/SpreadsheetGrid';
export { AIAssistantPanel } from './components/AIAssistantPanel';
export { MobileChat } from './components/MobileChat';
export { SpreadsheetModals } from './components/SpreadsheetModals';

// Componentes otimizados (mantidos para compatibilidade)
export { OptimizedTableComponents } from './OptimizedTableComponents';
