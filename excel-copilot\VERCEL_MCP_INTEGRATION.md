# 🚀 Vercel MCP Integration - Excel Copilot

## 📋 Visão Geral

A integração Vercel MCP permite monitoramento em tempo real de deployments, logs e métricas de performance do Excel Copilot diretamente através do chat com IA.

## ✅ Status da Implementação

- ✅ **Cliente Vercel API** - Implementado em `src/lib/vercel-integration.ts`
- ✅ **Endpoints de API** - Criados em `src/app/api/vercel/`
- ✅ **Health Check Integration** - Integrado ao sistema de health checks
- ✅ **Documentação** - Completa com exemplos de uso

## 🔧 Configuração

### 1. Variáveis de Ambiente

Adicione as seguintes variáveis ao seu arquivo `.env.local`:

```bash
# Token de API do Vercel
VERCEL_API_TOKEN="seu-token-vercel-aqui"

# ID do projeto (opcional, mas recomendado)
VERCEL_PROJECT_ID="seu-project-id-vercel"

# ID do time (opcional, apenas se usar times)
VERCEL_TEAM_ID="seu-team-id-vercel"
```

### 2. Obter Token de API

1. Acesse [Vercel Account Tokens](https://vercel.com/account/tokens)
2. Clique em "Create Token"
3. Dê um nome descritivo (ex: "Excel Copilot MCP")
4. Selecione o escopo apropriado
5. Copie o token gerado

### 3. Encontrar Project ID

1. Acesse seu projeto no Vercel
2. Vá para Settings → General
3. Copie o "Project ID" da seção Project Information

## 🎯 Funcionalidades Implementadas

### 📊 Monitoramento de Status

- Status geral do projeto (healthy/degraded/down)
- Informações do último deployment
- Contagem de erros recentes
- Tempo de uptime

### 📈 Métricas de Performance

- Número de requisições (24h)
- Taxa de erro
- Tempo médio de resposta
- Uso de bandwidth
- Taxa de cache hit

### 📝 Logs Avançados

- Filtros por nível (info/warn/error)
- Filtros por fonte (build/static/lambda/edge)
- Busca por texto
- Logs de deployments específicos

### 🚀 Deployments

- Lista de deployments recentes
- Detalhes de deployments específicos
- Status de build
- Duração de deployments

## 🔌 Endpoints da API

### GET `/api/vercel/status`

Obtém status geral e métricas do projeto.

**Resposta:**

```json
{
  "success": true,
  "data": {
    "project": {
      "name": "excel-copilot",
      "environment": "production",
      "status": "healthy",
      "message": "Sistema funcionando normalmente",
      "uptime": 86400000,
      "lastDeployment": {
        "id": "dpl_xxx",
        "url": "https://excel-copilot.vercel.app",
        "state": "READY",
        "created": "2024-01-15T10:30:00Z",
        "target": "production"
      }
    },
    "metrics": {
      "requests24h": 1250,
      "errors24h": 3,
      "errorRate": 0.24,
      "averageResponseTime": 245,
      "bandwidth24h": 15728640,
      "cacheHitRate": 87.5,
      "recentErrors": 1
    }
  }
}
```

### GET `/api/vercel/logs`

Obtém logs filtrados.

**Parâmetros de Query:**

- `level`: info, warn, error
- `source`: build, static, lambda, edge
- `limit`: 1-200 (padrão: 50)
- `search`: termo de busca

**Exemplo:**

```bash
GET /api/vercel/logs?level=error&limit=10&search=vertex
```

### GET `/api/vercel/deployments`

Lista deployments recentes.

**Parâmetros de Query:**

- `limit`: 1-100 (padrão: 20)
- `state`: BUILDING, ERROR, READY, etc.

### POST `/api/vercel/deployments`

Obtém detalhes de um deployment específico.

**Body:**

```json
{
  "deploymentId": "dpl_xxx",
  "includeLogs": true
}
```

## 🤖 Uso com MCP

### Exemplos de Comandos

**Verificar Status:**

```
Como está o status do projeto no Vercel?
```

**Verificar Erros:**

```
Há erros recentes no Vercel? Mostre os últimos 5 erros.
```

**Analisar Performance:**

```
Qual é a performance atual do Excel Copilot? Mostre métricas das últimas 24h.
```

**Verificar Deployments:**

```
Mostre os últimos deployments e seus status.
```

**Buscar Logs:**

```
Busque logs de erro relacionados ao Vertex AI nos últimos deployments.
```

## 🔍 Health Check Integration

A integração Vercel foi adicionada ao sistema de health checks existente:

```typescript
// Verificação automática incluída em /api/health
{
  "service": "vercel",
  "status": "healthy",
  "responseTime": 245,
  "details": {
    "configured": true,
    "projectStatus": "healthy",
    "lastDeployment": {
      "id": "dpl_xxx",
      "state": "READY",
      "created": 1705312200000
    },
    "recentErrors": 0,
    "uptime": 86400000
  }
}
```

## 🛠️ Desenvolvimento

### Estrutura de Arquivos

```
src/
├── lib/
│   └── vercel-integration.ts     # Cliente e serviços Vercel
├── app/api/vercel/
│   ├── status/route.ts           # Status e métricas
│   ├── logs/route.ts             # Logs filtrados
│   └── deployments/route.ts      # Deployments
└── lib/health-checker.ts         # Health check integrado
```

### Classes Principais

- **`VercelClient`**: Cliente base para API do Vercel
- **`VercelMonitoringService`**: Serviços de monitoramento de alto nível
- **`checkVercelHealth()`**: Função de health check

## 🚨 Troubleshooting

### Erro: "VERCEL_API_TOKEN não configurado"

- Verifique se a variável está definida no `.env.local`
- Confirme que o token é válido no Vercel

### Erro: "Project ID é obrigatório"

- Defina `VERCEL_PROJECT_ID` no ambiente
- Ou passe o projectId diretamente nas chamadas

### Logs vazios ou incompletos

- Verifique se o token tem permissões adequadas
- Confirme que o projeto existe e está acessível

### Performance lenta

- Considere implementar cache para chamadas frequentes
- Ajuste os limites de requisições conforme necessário

## 📈 Próximos Passos

1. **Alertas Proativos**: Implementar notificações automáticas
2. **Dashboard Visual**: Criar interface gráfica para métricas
3. **Análise Histórica**: Armazenar dados para análise temporal
4. **Integração CI/CD**: Conectar com GitHub Actions

## 🔗 Links Úteis

- [Vercel API Documentation](https://vercel.com/docs/rest-api)
- [Vercel Account Tokens](https://vercel.com/account/tokens)
- [Excel Copilot Health Checks](./src/lib/health-checker.ts)

---

**Status**: ✅ Implementação Completa  
**Última Atualização**: Janeiro 2024  
**Responsável**: Integração MCP Excel Copilot
