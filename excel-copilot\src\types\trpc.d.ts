import '@trpc/react-query';
import { AnyRouter } from '@trpc/server';

// Para o @trpc/react-query
declare module '@trpc/react-query' {
  interface inferReactQueryProcedureOptions<TRouter extends AnyRouter, TPath extends string> {
    context?: unknown;
  }
}

// Para o @trpc/server
declare module '@trpc/server' {
  // Definição base para os roteadores
  interface AnyRouter {
    _def: {
      queries: Record<string, any>;
      mutations: Record<string, any>;
      subscriptions: Record<string, any>;
    };
  }

  // Tipo para opções de procedimento
  interface ProcedureCallOptions {
    ctx?: any;
    input?: any;
    path: string[];
    type: 'query' | 'mutation' | 'subscription';
  }

  // Tipo para opções de manipulador de requisição
  interface FetchHandlerRequestOptions<TRouter extends AnyRouter> {
    req: Request;
    router: TRouter;
    endpoint: string;
    path: string;
    createContext?: () => Promise<any>;
  }
}

// Tipos para contexto do trpc
export interface TRPCContext {
  req?: any;
  res?: any;
  session?: any;
  prisma?: any;
  user?: any;
  headers?: Record<string, string>;
}

// Opções para criar contexto
export interface CreateContextOptions {
  req?: any;
  res?: any;
}

// Tipos de procedure
export type ProcedureType = 'query' | 'mutation' | 'subscription';
