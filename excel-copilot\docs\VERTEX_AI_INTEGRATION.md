# Integração Excel Copilot com Vertex AI

Este documento explica como o Excel Copilot utiliza o Google Vertex AI para fornecer recursos de inteligência artificial sem a necessidade de chaves de API diretas.

## Visão Geral

O Excel Copilot se integra com modelos Gemini do Google através da plataforma Vertex AI, que proporciona:

- **Maior seguran<PERSON>** - Autenticação baseada em contas de serviço em vez de chaves de API expostas
- **Escalabilidade melhorada** - Acesso a modelos em escala empresarial
- **Controle de custos** - Monitoramento e controle de uso através do console do Google Cloud
- **Possibilidade de modelo customizado** - Suporte para modelos Gemini ajustados a necessidades específicas

## Requisitos

- Projeto no Google Cloud com faturamento ativado
- Vertex AI habilitado no projeto
- Arquivo de credenciais de conta de serviço

## Configuração

### 1. Criação da Conta de Serviço

1. Acesse o [Google Cloud Console](https://console.cloud.google.com/)
2. Selecione seu projeto ou crie um novo
3. Navegue até "IAM & Admin" > "Service Accounts"
4. Clique em "Create Service Account"
5. Forneça um nome e descrição para a conta de serviço
6. Atribua o papel `Vertex AI User` (`roles/aiplatform.user`) à conta de serviço
7. Clique em "Create Key" e selecione o formato JSON
8. Salve o arquivo de chave JSON com segurança

### 2. Configuração no Excel Copilot

Existem duas maneiras de configurar o Excel Copilot para usar o Vertex AI:

#### Opção 1: Arquivo de Credenciais na Raiz do Projeto (Recomendado)

Simplesmente coloque o arquivo de credenciais na raiz do projeto com o nome `vertex-credentials.json`.

O Excel Copilot detectará automaticamente este arquivo na inicialização e o utilizará para autenticação.

#### Opção 2: Variáveis de Ambiente

Configure as seguintes variáveis de ambiente:

```
VERTEX_AI_ENABLED=true
GOOGLE_APPLICATION_CREDENTIALS=/caminho/para/suas-credenciais.json
VERTEX_AI_PROJECT_ID=seu-projeto-id
VERTEX_AI_LOCATION=us-central1
VERTEX_AI_MODEL_NAME=gemini-2.0-flash-001
```

## Testes

Para verificar se a integração está funcionando corretamente:

```bash
# Executa teste manual básico para verificar a conexão com o Vertex AI
npm run test:vertex-ai

# Executa todos os testes relacionados ao Vertex AI
npm run test:ai:all
```

## Troubleshooting

### Problemas Comuns

1. **"Erro: Arquivo de credenciais não encontrado"**  
   Verifique se o arquivo `vertex-credentials.json` existe na raiz do projeto.

2. **"Serviço de IA não está disponível"**

   - Verifique se o arquivo de credenciais é válido
   - Confirme se a API do Vertex AI está habilitada no projeto
   - Verifique se a conta de serviço tem as permissões corretas

3. **"API quota exceeded"**  
   Verifique os limites de quota no Google Cloud Console e solicite um aumento se necessário.

### Logs

Para obter mais informações sobre erros, verifique os logs com:

```bash
grep -r "Vertex AI" logs/
```

## Comparação com API Key

| Aspecto            | Vertex AI (Conta de Serviço)     | API Key Direta              |
| ------------------ | -------------------------------- | --------------------------- |
| Segurança          | Alta - Baseada em identidade     | Média - Chave exposta       |
| Implementação      | Arquivo JSON na raiz do projeto  | Variável de ambiente        |
| Controle de Acesso | Granular através de IAM          | Limitado                    |
| Monitoramento      | Detalhado no GCP                 | Básico                      |
| Custos             | Transparentes no GCP             | Difíceis de rastrear        |
| Modelos            | Acesso a todos os modelos Gemini | Limitado a modelos públicos |

## Como Funciona

Quando o serviço `GeminiService` é inicializado:

1. Tenta encontrar o arquivo `vertex-credentials.json` na raiz do projeto
2. Se encontrado, configura o caminho das credenciais e inicializa o cliente Vertex AI
3. Obtém a referência para o modelo generativo especificado
4. Configura o processamento de requisições e streaming de respostas

A implementação oferece suporte completo às funcionalidades usadas no projeto, incluindo:

- Envio de mensagens simples
- Streaming de respostas para experiência em tempo real
- Processamento de comandos específicos para o Excel
