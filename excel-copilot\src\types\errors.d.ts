/**
 * Definições de tipos para erros no sistema
 */

// Tipos de erro possíveis no sistema
export type ErrorType =
  | 'AUTH_ERROR'
  | 'VALIDATION_ERROR'
  | 'NOT_FOUND'
  | 'PERMISSION_DENIED'
  | 'SERVER_ERROR'
  | 'NETWORK_ERROR'
  | 'DATABASE_ERROR'
  | 'AI_SERVICE_ERROR'
  | 'RATE_LIMIT_ERROR'
  | 'INVALID_REQUEST'
  | 'SUBSCRIPTION_ERROR'
  | 'EXCEL_PROCESSING_ERROR'
  | 'DESKTOP_BRIDGE_ERROR'
  | 'SOCKET_ERROR'
  | 'UNKNOWN_ERROR';

// Erro normalizado para usar no sistema
export interface NormalizedError {
  type: ErrorType;
  message: string;
  code?: string;
  status?: number;
  details?: any;
  stack?: string;
  cause?: Error;
}

// Mensagens amigáveis para os erros
export type UserFriendlyMessages = Record<ErrorType, string>;

// Tipo para o manipulador de erros
export interface ErrorHandlerOptions {
  showToast?: boolean;
  logToServer?: boolean;
  redirect?: string;
  fallback?: () => void;
}

// Contexto para manipulação de erros
export interface ErrorContext {
  userId?: string;
  path?: string;
  component?: string;
  action?: string;
  data?: any;
}

// Resultado de um erro processado
export interface ProcessedError {
  title: string;
  message: string;
  type: ErrorType;
  action?: string;
}
