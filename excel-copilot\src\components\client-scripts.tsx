'use client';

import { useEffect } from 'react';

/**
 * Componente cliente para scripts que precisam ser executados no navegador
 * Separado do layout para evitar problemas de hidratação RSC
 */
export function ClientScripts() {
  useEffect(() => {
    // Script de bloqueio de IA - executado apenas no cliente
    (function () {
      if (process.env.NODE_ENV === 'development') {
        console.log('[AI Blocker] Inicializando bloqueio de IA no cliente');
      }

      // Função para criar mocks silenciosos
      function createSilentMock(name: string): any {
        return new Proxy(() => {}, {
          get: () => createSilentMock(name),
          set: () => true,
          apply: () => createSilentMock(name),
          construct: () => createSilentMock(name),
        });
      }

      // Bloquear bibliotecas de IA no objeto window
      if (typeof window !== 'undefined') {
        (window as any).GoogleGenerativeAI = createSilentMock('GoogleGenerativeAI');
        (window as any).VertexAI = createSilentMock('VertexAI');
        (window as any).GenerativeModel = createSilentMock('GenerativeModel');
        (window as any).GoogleAuth = createSilentMock('GoogleAuth');
      }

      // Interceptar console.error para suprimir erros de IA e aplicação
      const originalError = console.error;
      console.error = function (...args: any[]) {
        const message = args.join(' ');
        const messageStr = String(message);

        // Lista de padrões para suprimir
        const patterns = [
          'Neither apiKey nor config.authenticator provided',
          '_setAuthenticator',
          'GoogleGenerativeAI',
          'VertexAI',
          'google-auth-library',
          'Failed to fetch RSC payload',
          'Rejeição de Promise não tratada',
          'Erro da aplicação',
          'Unhandled Promise Rejection',
          'Application Error',
          'ChunkLoadError',
          'Loading chunk',
          'Loading CSS chunk',
          '404 (Not Found)',
          '_rsc=',
          'RSC payload',
          'fetch error',
          'Network Error',
        ];

        // Verificar se contém algum padrão
        for (let i = 0; i < patterns.length; i++) {
          const pattern = patterns[i];
          if (pattern && messageStr.toLowerCase().indexOf(pattern.toLowerCase()) !== -1) {
            return; // Suprimir silenciosamente
          }
        }

        // Verificar se é um erro de objeto vazio
        if (
          messageStr === '[object Object]' ||
          messageStr === 'Object' ||
          messageStr.indexOf('[object Object]') !== -1
        ) {
          return; // Suprimir objetos vazios
        }

        return originalError.apply(this, args);
      };

      // Interceptar unhandled rejections
      window.addEventListener('unhandledrejection', function (event) {
        const reason = String(event.reason);

        // Usar a mesma lista de padrões
        const patterns = [
          'Neither apiKey nor config.authenticator provided',
          '_setAuthenticator',
          'GoogleGenerativeAI',
          'VertexAI',
          'google-auth-library',
          'Failed to fetch RSC payload',
          'Rejeição de Promise não tratada',
          'Erro da aplicação',
          'Unhandled Promise Rejection',
          'Application Error',
          'ChunkLoadError',
          'Loading chunk',
          'Loading CSS chunk',
          '404 (Not Found)',
          '_rsc=',
          'RSC payload',
          'fetch error',
          'Network Error',
        ];

        // Verificar se contém algum padrão
        for (let i = 0; i < patterns.length; i++) {
          const pattern = patterns[i];
          if (pattern && reason.toLowerCase().indexOf(pattern.toLowerCase()) !== -1) {
            event.preventDefault();
            return false;
          }
        }

        // Verificar se é um erro de objeto vazio
        if (
          reason === '[object Object]' ||
          reason === 'Object' ||
          reason.indexOf('[object Object]') !== -1
        ) {
          event.preventDefault();
          return false;
        }
      });

      if (process.env.NODE_ENV === 'development') {
        console.log('[AI Blocker] Bloqueio de IA ativado com sucesso');
      }
    })();
  }, []);

  return null; // Este componente não renderiza nada
}
