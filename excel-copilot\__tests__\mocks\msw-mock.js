/**
 * Mock para MSW (Mock Service Worker) usado nos testes
 * Fornece implementações simplificadas de http, setupServer e outras funcionalidades do MSW
 */

// Setup server - aceita os handlers e retorna uma interface compatível com MSW
const setupServer = (...handlers) => {
  return {
    listen: () => {},
    close: () => {},
    resetHandlers: () => {},
    use: (...newHandlers) => {},
    events: {
      on: () => {},
      removeListener: () => {},
    },
    handlers,
  };
};

// Implementação básica de http
const http = {
  get: (url, handler) => ({ url, method: 'get', handler }),
  post: (url, handler) => ({ url, method: 'post', handler }),
  put: (url, handler) => ({ url, method: 'put', handler }),
  delete: (url, handler) => ({ url, method: 'delete', handler }),
  patch: (url, handler) => ({ url, method: 'patch', handler }),
};

// Classe para simular respostas HTTP
class HttpResponse {
  static json(data, init = {}) {
    return { body: data, status: init.status || 200 };
  }

  static text(content, init = {}) {
    return { body: content, status: init.status || 200 };
  }

  static error(init = {}) {
    return { status: init.status || 500 };
  }

  constructor(body, init = {}) {
    return { body, status: init.status || 200 };
  }
}

// Exportar como CommonJS module
module.exports = {
  setupServer,
  http,
  HttpResponse,
};
