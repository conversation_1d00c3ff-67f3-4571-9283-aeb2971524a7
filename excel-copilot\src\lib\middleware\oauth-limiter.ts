import { NextRequest, NextResponse } from 'next/server';

/**
 * Rate limiter específico para tentativas de login OAuth
 * Protege contra ataques de força bruta em endpoints de autenticação
 */

interface RateLimitEntry {
  count: number;
  resetTime: number;
  lastAttempt: number;
}

// Cache em memória para rate limiting (em produção, usar Redis)
const rateLimitCache = new Map<string, RateLimitEntry>();

// Configurações de rate limiting
const OAUTH_RATE_LIMIT_CONFIG = {
  // Máximo de tentativas por IP por janela de tempo
  maxAttempts: process.env.NODE_ENV === 'development' ? 50 : 5,
  // Janela de tempo em milissegundos (15 minutos em prod, 5 minutos em dev)
  windowMs: process.env.NODE_ENV === 'development' ? 5 * 60 * 1000 : 15 * 60 * 1000,
  // Tempo de bloqueio após exceder limite (5 minutos em dev, 30 minutos em prod)
  blockDurationMs: process.env.NODE_ENV === 'development' ? 5 * 60 * 1000 : 30 * 60 * 1000,
  // Endpoints que devem ser limitados
  protectedPaths: ['/api/auth/signin', '/api/auth/callback', '/api/auth/session'],
};

/**
 * Obtém o identificador único para rate limiting
 * Usa IP + User-Agent para maior precisão
 */
function getRateLimitKey(request: NextRequest): string {
  const ip =
    request.ip ||
    request.headers.get('x-forwarded-for')?.split(',')[0] ||
    request.headers.get('x-real-ip') ||
    'unknown';

  const userAgent = request.headers.get('user-agent') || 'unknown';

  // Hash simples para reduzir tamanho da chave
  const hash = Buffer.from(`${ip}-${userAgent}`).toString('base64').slice(0, 16);

  return `oauth_limit:${hash}`;
}

/**
 * Verifica se a requisição deve ser limitada
 */
function shouldRateLimit(pathname: string): boolean {
  return OAUTH_RATE_LIMIT_CONFIG.protectedPaths.some(path => pathname.startsWith(path));
}

/**
 * Limpa entradas expiradas do cache
 */
function cleanupExpiredEntries(): void {
  const now = Date.now();

  for (const [key, entry] of rateLimitCache.entries()) {
    if (now > entry.resetTime) {
      rateLimitCache.delete(key);
    }
  }
}

/**
 * Registra tentativa de login para auditoria
 */
function logAuthAttempt(
  ip: string,
  pathname: string,
  blocked: boolean,
  remainingAttempts?: number
): void {
  const logData = {
    timestamp: new Date().toISOString(),
    ip,
    pathname,
    blocked,
    remainingAttempts,
    type: 'oauth_rate_limit',
  };

  if (blocked) {
    console.warn('🚨 OAuth Rate Limit Exceeded:', JSON.stringify(logData));
  } else {
    console.log('🔐 OAuth Attempt:', JSON.stringify(logData));
  }
}

/**
 * Middleware de rate limiting para OAuth
 */
export async function oauthRateLimiter(request: NextRequest): Promise<NextResponse | null> {
  const { pathname } = request.nextUrl;

  // Verificar se o endpoint deve ser limitado
  if (!shouldRateLimit(pathname)) {
    return null;
  }

  // Limpar entradas expiradas periodicamente
  if (Math.random() < 0.1) {
    // 10% de chance
    cleanupExpiredEntries();
  }

  const key = getRateLimitKey(request);
  const now = Date.now();
  const ip = request.ip || 'unknown';

  // Obter ou criar entrada de rate limit
  let entry = rateLimitCache.get(key);

  if (!entry) {
    // Primeira tentativa
    entry = {
      count: 1,
      resetTime: now + OAUTH_RATE_LIMIT_CONFIG.windowMs,
      lastAttempt: now,
    };
    rateLimitCache.set(key, entry);

    logAuthAttempt(ip, pathname, false, OAUTH_RATE_LIMIT_CONFIG.maxAttempts - 1);
    return null;
  }

  // Verificar se a janela de tempo expirou
  if (now > entry.resetTime) {
    // Reset da janela
    entry.count = 1;
    entry.resetTime = now + OAUTH_RATE_LIMIT_CONFIG.windowMs;
    entry.lastAttempt = now;

    logAuthAttempt(ip, pathname, false, OAUTH_RATE_LIMIT_CONFIG.maxAttempts - 1);
    return null;
  }

  // Incrementar contador
  entry.count++;
  entry.lastAttempt = now;

  // Verificar se excedeu o limite
  if (entry.count > OAUTH_RATE_LIMIT_CONFIG.maxAttempts) {
    // Estender o tempo de bloqueio
    entry.resetTime = now + OAUTH_RATE_LIMIT_CONFIG.blockDurationMs;

    logAuthAttempt(ip, pathname, true, 0);

    return NextResponse.json(
      {
        error: 'Muitas tentativas de login',
        message: 'Tente novamente em alguns minutos',
        retryAfter: Math.ceil(OAUTH_RATE_LIMIT_CONFIG.blockDurationMs / 1000),
      },
      {
        status: 429,
        headers: {
          'Retry-After': String(Math.ceil(OAUTH_RATE_LIMIT_CONFIG.blockDurationMs / 1000)),
          'X-RateLimit-Limit': String(OAUTH_RATE_LIMIT_CONFIG.maxAttempts),
          'X-RateLimit-Remaining': '0',
          'X-RateLimit-Reset': String(Math.ceil(entry.resetTime / 1000)),
        },
      }
    );
  }

  const remainingAttempts = OAUTH_RATE_LIMIT_CONFIG.maxAttempts - entry.count;
  logAuthAttempt(ip, pathname, false, remainingAttempts);

  // Adicionar headers informativos
  const response = NextResponse.next();
  response.headers.set('X-RateLimit-Limit', String(OAUTH_RATE_LIMIT_CONFIG.maxAttempts));
  response.headers.set('X-RateLimit-Remaining', String(remainingAttempts));
  response.headers.set('X-RateLimit-Reset', String(Math.ceil(entry.resetTime / 1000)));

  return null; // Permitir a requisição
}

/**
 * Função para limpar o rate limit de um IP específico (para uso administrativo)
 */
export function clearOAuthRateLimit(ip: string, userAgent?: string): boolean {
  const hash = Buffer.from(`${ip}-${userAgent || 'unknown'}`)
    .toString('base64')
    .slice(0, 16);
  const key = `oauth_limit:${hash}`;

  return rateLimitCache.delete(key);
}

/**
 * Função para obter estatísticas de rate limiting
 */
export function getOAuthRateLimitStats(): {
  totalEntries: number;
  blockedIPs: number;
  activeEntries: number;
} {
  const now = Date.now();
  let blockedIPs = 0;
  let activeEntries = 0;

  for (const entry of rateLimitCache.values()) {
    if (now <= entry.resetTime) {
      activeEntries++;
      if (entry.count > OAUTH_RATE_LIMIT_CONFIG.maxAttempts) {
        blockedIPs++;
      }
    }
  }

  return {
    totalEntries: rateLimitCache.size,
    blockedIPs,
    activeEntries,
  };
}
