/**
 * CSRF Protection para Edge Runtime
 * Versão simplificada compatível com Vercel Edge Functions
 */

// Função para gerar hash simples (substitui crypto.createHmac)
async function simpleHash(data: string, secret: string): Promise<string> {
  const encoder = new TextEncoder();
  const keyData = encoder.encode(secret);
  const messageData = encoder.encode(data);

  // Usar Web Crypto API disponível no Edge Runtime
  const key = await crypto.subtle.importKey(
    'raw',
    keyData,
    { name: 'HMAC', hash: 'SHA-256' },
    false,
    ['sign']
  );

  const signature = await crypto.subtle.sign('HMAC', key, messageData);
  const hashArray = Array.from(new Uint8Array(signature));
  const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');

  return hashHex;
}

// Função para gerar token aleatório
function generateRandomToken(): string {
  const array = new Uint8Array(32);
  crypto.getRandomValues(array);
  return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
}

/**
 * Gera um token CSRF usando Web Crypto API
 */
export async function generateCSRFToken(secret: string, sessionData: string): Promise<string> {
  try {
    const timestamp = Date.now().toString();
    const randomToken = generateRandomToken();
    const payload = `${sessionData}:${timestamp}:${randomToken}`;

    const signature = await simpleHash(payload, secret);
    const token = `${payload}:${signature}`;

    // Codificar em base64 para transporte seguro
    const encoder = new TextEncoder();
    const tokenBytes = encoder.encode(token);
    const base64Token = btoa(String.fromCharCode(...tokenBytes));

    return base64Token;
  } catch (error) {
    console.error('Erro ao gerar token CSRF:', error);
    throw new Error('Falha ao gerar token CSRF');
  }
}

/**
 * Valida um token CSRF
 */
export async function validateCSRFToken(
  token: string,
  secret: string,
  sessionData: string,
  maxAge: number = 3600000 // 1 hora em ms
): Promise<boolean> {
  try {
    // Decodificar base64
    const decodedBytes = Uint8Array.from(atob(token), c => c.charCodeAt(0));
    const decoder = new TextDecoder();
    const decodedToken = decoder.decode(decodedBytes);

    const parts = decodedToken.split(':');
    if (parts.length !== 4) {
      return false;
    }

    const [tokenSessionData, timestamp, randomToken, signature] = parts;

    // Verificar se os dados da sessão coincidem
    if (tokenSessionData !== sessionData) {
      return false;
    }

    // Verificar se o token não expirou
    const tokenTime = parseInt(timestamp || '0', 10);
    const now = Date.now();
    if (now - tokenTime > maxAge) {
      return false;
    }

    // Verificar a assinatura
    const payload = `${tokenSessionData}:${timestamp}:${randomToken}`;
    const expectedSignature = await simpleHash(payload, secret);

    return signature === expectedSignature;
  } catch (error) {
    console.error('Erro ao validar token CSRF:', error);
    return false;
  }
}

/**
 * Middleware para validação de CSRF em Edge Runtime
 */
export function createCSRFMiddleware(secret: string) {
  return async function validateCSRF(request: Request): Promise<boolean> {
    // Métodos seguros não precisam de validação CSRF
    if (['GET', 'HEAD', 'OPTIONS'].includes(request.method)) {
      return true;
    }

    // Obter token do header ou cookie
    const headerToken = request.headers.get('x-csrf-token');
    const cookieHeader = request.headers.get('cookie');

    let cookieToken: string | null = null;
    if (cookieHeader) {
      const cookies = cookieHeader.split(';').reduce(
        (acc, cookie) => {
          const [key, value] = cookie.trim().split('=');
          if (key && value) {
            acc[key] = value;
          }
          return acc;
        },
        {} as Record<string, string>
      );

      cookieToken = cookies['csrf_token'] || null;
    }

    const token = headerToken || cookieToken;
    if (!token) {
      return false;
    }

    // Usar timestamp como dados de sessão (simplificado para Edge Runtime)
    const sessionData = Date.now().toString();

    return await validateCSRFToken(token, secret, sessionData);
  };
}

// Export de utilitários
export const CSRF_HEADER_NAME = 'x-csrf-token';
export const CSRF_COOKIE_NAME = 'csrf_token';

// Configurações padrão
export const DEFAULT_CSRF_CONFIG = {
  secret: process.env.SECURITY_CSRF_SECRET || 'default-csrf-secret-change-in-production',
  maxAge: 60 * 60 * 1000, // 1 hora
  cookieOptions: {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax' as const,
    path: '/',
  },
};
