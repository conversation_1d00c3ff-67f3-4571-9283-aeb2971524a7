'use client';

import {
  <PERSON><PERSON>ronDown,
  Calculator,
  BarChart,
  FilterX,
  Table,
  Sparkles,
  Sigma,
  HelpCircle,
} from 'lucide-react';
import { useState, useEffect, useCallback } from 'react';

import { ActionButton, Button, ScrollArea } from '@/components/ui';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface CommandExampleProps {
  onSelect: (command: string) => void;
}

type CommandCategory = {
  name: string;
  icon: JSX.Element;
  examples: string[];
};

export function CommandExamples({ onSelect }: CommandExampleProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [_currentFocusIndex, setCurrentFocusIndex] = useState(-1);
  const [isMobile, setIsMobile] = useState(false);

  // Categorias de comandos com ícones e exemplos
  const commandCategories: CommandCategory[] = [
    {
      name: '<PERSON><PERSON><PERSON><PERSON>',
      icon: <Calculator className="h-5 w-5" />,
      examples: [
        'Some os valores da coluna B',
        'Calcule a média da coluna Vendas',
        'Qual é o maior valor na coluna de Receita?',
        'Encontre o desvio padrão dos dados na coluna E',
        'Multiplique os valores da coluna A por 2',
        'Calcule o percentual de crescimento mês a mês',
        'Faça uma análise estatística completa da coluna Valores',
      ],
    },
    {
      name: 'Visualização',
      icon: <BarChart className="h-5 w-5" />,
      examples: [
        'Crie um gráfico de barras com os dados das colunas A e B',
        'Faça um gráfico de linha de vendas por mês',
        'Gere um gráfico de pizza com os valores da tabela',
        'Mostre um gráfico de dispersão entre preço e quantidade',
        'Crie um histograma da coluna Idades',
        'Faça um dashboard com 3 gráficos: barras, linha e pizza',
        'Crie um mapa de calor com os dados de vendas por região',
      ],
    },
    {
      name: 'Filtros e Organização',
      icon: <FilterX className="h-5 w-5" />,
      examples: [
        'Filtre os dados onde Vendas > 1000',
        'Ordene a tabela por valor, do maior para o menor',
        "Mostre apenas registros onde a coluna Região é 'Sul'",
        'Filtre dados do mês de Janeiro',
        'Remova as linhas com valores nulos',
        'Filtre os 10 maiores clientes por volume de compras',
        'Agrupe por categoria e mostre apenas os grupos com mais de 5 itens',
      ],
    },
    {
      name: 'Formatação',
      icon: <Table className="h-5 w-5" />,
      examples: [
        'Converta a planilha atual para formato de tabela',
        'Aplique formatação condicional na coluna de valores',
        'Destaque células com valores negativos em vermelho',
        'Formate a coluna de datas no padrão DD/MM/AAAA',
        'Adicione uma linha de totais ao final da tabela',
        'Formate os cabeçalhos com fundo azul e texto branco em negrito',
        'Adicione bordas em todas as células da tabela',
      ],
    },
    {
      name: 'Tabelas Dinâmicas',
      icon: <Sigma className="h-5 w-5" />,
      examples: [
        'Crie uma tabela dinâmica agrupando vendas por região',
        'Faça uma tabela dinâmica com vendas por produto e vendedor',
        'Gere uma tabela dinâmica de receitas mensais por categoria',
        'Crie um resumo de vendas por trimestre e região',
        'Faça uma tabela dinâmica com subtotais por departamento',
      ],
    },
    {
      name: 'Análise Avançada',
      icon: <Sparkles className="h-5 w-5" />,
      examples: [
        'Faça uma análise de correlação entre as colunas preço e demanda',
        'Gere estatísticas descritivas de todas as colunas numéricas',
        'Crie segmentação de dados por faixa etária: 0-18, 19-35, 36-60, 60+',
        'Aplique regressão linear e preveja valores futuros',
        'Faça uma análise de sazonalidade nos dados mensais',
        'Identifique outliers na coluna de vendas e sugira tratamentos',
        'Calcule a previsão de vendas para os próximos 3 meses usando série temporal',
        'Agrupe clientes por comportamento de compra usando K-means',
        'Faça uma análise de cesta de compras para identificar produtos complementares',
        'Crie um modelo de pontuação para classificar leads por potencial',
      ],
    },
    {
      name: 'Perguntas em Linguagem Natural',
      icon: <HelpCircle className="h-5 w-5" />,
      examples: [
        'Quais foram os 3 melhores vendedores no último trimestre?',
        'Qual região teve a maior queda nas vendas?',
        'Como está o desempenho das vendas comparado com o mesmo período do ano passado?',
        'Quais produtos tiveram crescimento acima de 10% nos últimos 6 meses?',
        'Qual é a tendência de vendas para o produto X?',
      ],
    },
  ];

  // Exemplo em destaque - escolhe da primeira categoria
  const featuredCategory = commandCategories[0] || {
    name: 'Análise Matemática',
    icon: <Calculator className="h-5 w-5" />,
    examples: ['Some os valores da coluna B'],
  };
  const featuredExample = featuredCategory.examples[0] || '';

  // Detectar se é dispositivo móvel para otimizações
  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    // Verificar inicialmente
    checkIfMobile();

    // Adicionar listener para mudanças de tamanho
    window.addEventListener('resize', checkIfMobile);

    // Cleanup
    return () => window.removeEventListener('resize', checkIfMobile);
  }, []);

  // Manipulador de navegação por teclado
  const handleKeyNavigation = useCallback(
    (e: React.KeyboardEvent, categoryIndex: number, exampleIndex: number) => {
      const key = e.key;

      // Ativar comando com Enter/Space
      if (key === 'Enter' || key === ' ') {
        e.preventDefault();
        const category = commandCategories[categoryIndex];
        if (category && category.examples[exampleIndex]) {
          onSelect(category.examples[exampleIndex]);
        }
      }

      // Navegação com setas dentro de cada categoria
      if (key === 'ArrowDown' || key === 'ArrowUp') {
        e.preventDefault();
        const category = commandCategories[categoryIndex];
        if (!category) return;

        const maxIndex = category.examples.length - 1;

        if (key === 'ArrowDown' && exampleIndex < maxIndex) {
          setCurrentFocusIndex(exampleIndex + 1);
          document.getElementById(`example-${categoryIndex}-${exampleIndex + 1}`)?.focus();
        } else if (key === 'ArrowUp' && exampleIndex > 0) {
          setCurrentFocusIndex(exampleIndex - 1);
          document.getElementById(`example-${categoryIndex}-${exampleIndex - 1}`)?.focus();
        }
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [onSelect]
  );

  return (
    <div className="space-y-4 w-full">
      <Card className="border shadow-sm bg-card">
        <CardHeader className="pb-2">
          <CardTitle className="text-lg flex items-center">
            <Sparkles className="h-6 w-6 mr-2 text-primary" aria-hidden="true" />
            <span className="text-enhanced-contrast">Exemplos de Comandos</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="mb-3">
            <div className="flex items-center space-x-2 mb-1">
              <div className="p-1 rounded-md bg-primary/10 text-primary" aria-hidden="true">
                {featuredCategory.icon}
              </div>
              <span className="text-xs font-medium text-muted-foreground">
                {featuredCategory.name}
              </span>
            </div>
            <Button
              variant="outline"
              className="w-full justify-start text-left font-normal h-auto py-3 border-primary/20 bg-primary/5 hover:bg-primary/10 a11y-focus"
              onClick={() => onSelect(featuredExample)}
              aria-label={`Usar comando: ${featuredExample}`}
            >
              <span className="line-clamp-2 text-xs sm:text-sm">{featuredExample}</span>
            </Button>
          </div>

          {isExpanded ? (
            <>
              <ScrollArea
                className="h-[200px] sm:h-[250px] md:h-[300px] pr-4"
                role="list"
                aria-label="Lista de comandos por categoria"
              >
                <div className="space-y-3 sm:space-y-4">
                  {commandCategories.map((category, categoryIdx) => (
                    <div
                      key={categoryIdx}
                      className="space-y-1.5 sm:space-y-2"
                      role="group"
                      aria-labelledby={`category-${categoryIdx}`}
                    >
                      <div className="flex items-center space-x-2">
                        <div
                          className="p-1 sm:p-1.5 rounded-md bg-muted text-muted-foreground"
                          aria-hidden="true"
                        >
                          {category.icon}
                        </div>
                        <span
                          className="text-xs sm:text-sm font-medium"
                          id={`category-${categoryIdx}`}
                        >
                          {category.name}
                        </span>
                      </div>
                      <div className="grid grid-cols-1 gap-1 sm:gap-1.5 pl-5 sm:pl-7" role="list">
                        {/* Para dispositivos móveis, limitamos o número de exemplos para melhor desempenho */}
                        {category.examples.slice(0, isMobile ? 4 : undefined).map((example, i) => (
                          <ActionButton
                            key={i}
                            id={`example-${categoryIdx}-${i}`}
                            variant="ghost"
                            size="sm"
                            className="justify-start h-auto py-1 sm:py-1.5 px-1.5 sm:px-2 text-xs sm:text-sm font-normal text-muted-foreground hover:text-foreground mobile-enhanced-tap a11y-focus"
                            actionId={`${categoryIdx}-${i}`}
                            onAction={() => onSelect(example)}
                            onKeyDown={e => handleKeyNavigation(e, categoryIdx, i)}
                            aria-label={`Usar comando: ${example}`}
                          >
                            <span className="line-clamp-1">{example}</span>
                          </ActionButton>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
              <Button
                onClick={() => setIsExpanded(false)}
                variant="ghost"
                size="sm"
                className="w-full mt-2 text-sm text-muted-foreground hover:text-foreground flex items-center justify-center"
                aria-expanded="true"
                aria-label="Mostrar menos exemplos"
              >
                <span>Mostrar menos</span>
                <ChevronDown className="h-4 w-4 ml-1 transform rotate-180" />
              </Button>
            </>
          ) : (
            <Button
              onClick={() => setIsExpanded(true)}
              variant="ghost"
              size="sm"
              className="w-full text-sm text-muted-foreground hover:text-foreground flex items-center justify-center"
              aria-expanded="false"
              aria-label="Mostrar mais exemplos"
            >
              <span>Explorar mais sugestões</span>
              <ChevronDown className="h-4 w-4 ml-1" />
            </Button>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
