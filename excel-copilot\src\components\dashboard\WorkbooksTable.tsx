'use client';

import { formatDistanceToNow } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import {
  FileSpreadsheet,
  Plus,
  Settings,
  Trash2,
  Edit,
  Copy,
  Calendar,
  Clock,
  AlertCircle,
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { useState, useEffect, useCallback } from 'react';
import { toast } from 'sonner';

import { useFetchWithCSRF } from '@/components/providers/csrf-provider';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { EmptyState } from '@/components/ui/empty-state';
import { ActionButton, OptimizedButton } from '@/components/ui/optimized-button';
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { logger } from '@/lib/logger';

// Tipo para workbook
interface Workbook {
  id: string;
  name: string;
  createdAt: Date;
  updatedAt: Date;
  sheets: { id: string }[] | number;
}

interface WorkbooksTableProps {
  searchQuery?: string;
}

export function WorkbooksTable({ searchQuery = '' }: WorkbooksTableProps) {
  const router = useRouter();
  const { data: session } = useSession();
  const [workbooks, setWorkbooks] = useState<Workbook[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isDeleting, setIsDeleting] = useState<string | null>(null);
  const { fetchWithCSRF } = useFetchWithCSRF();
  const [loadError, setLoadError] = useState<boolean>(false);

  // Função para formatar data relativa em português
  const formatRelativeDate = (date: Date) => {
    return formatDistanceToNow(date, { addSuffix: true, locale: ptBR });
  };

  // Definir fetchWorkbooks com useCallback para evitar recriação desnecessária
  const fetchWorkbooks = useCallback(async () => {
    try {
      setIsLoading(true);

      // Verificar se o usuário está autenticado
      if (!session?.user) {
        // Usuário não autenticado
        setLoadError(true);
        setWorkbooks([]);
        setIsLoading(false);
        return;
      }

      // Implementar lógica de retry
      let attempts = 0;
      const maxAttempts = 3;
      let success = false;
      let responseData;

      while (attempts < maxAttempts && !success) {
        try {
          attempts++;
          const searchParams = new URLSearchParams({
            ...(searchQuery && { search: searchQuery }),
          });

          const response = await fetchWithCSRF(`/api/workbooks?${searchParams.toString()}`, {
            headers: {
              'Content-Type': 'application/json',
            },
          });

          // Se chegou aqui sem erro, processar resposta
          if (response.ok) {
            success = true;
            responseData = await response.json();
          } else {
            // Tentar extrair mais informações do erro
            let errorDetails = '';
            try {
              const errorResponse = await response.json();
              errorDetails = errorResponse.details || errorResponse.error || '';
            } catch {
              // Ignorar erros ao tentar ler o json da resposta
            }

            // Falha na tentativa: Aguardando retry

            // Se for erro de autenticação, não tentar novamente
            if (response.status === 401) {
              throw new Error(`Não autorizado: ${errorDetails}`);
            }

            if (attempts < maxAttempts) {
              await new Promise(resolve => setTimeout(resolve, 1000 * attempts));
            } else {
              throw new Error(`API retornou código ${response.status}: ${errorDetails}`);
            }
          }
        } catch (retryError) {
          // Erro na tentativa de conexão
          // Se for erro de autorização, não tentar novamente
          if (retryError instanceof Error && retryError.message?.includes('Não autorizado')) {
            toast.error('Você precisa estar autenticado para acessar suas planilhas');
            setLoadError(true);
            setWorkbooks([]);
            setIsLoading(false);
            return;
          }

          // Aguardar antes de tentar de novo (a menos que seja a última tentativa)
          if (attempts < maxAttempts) {
            await new Promise(resolve => setTimeout(resolve, 1000 * attempts));
          } else {
            throw retryError;
          }
        }
      }

      if (!success) {
        throw new Error('Máximo de tentativas atingido ao carregar planilhas');
      }

      // Transformar os dados para o formato esperado pelo componente
      logger.debug('WorkbooksTable: Processando workbooks', {
        responseDataKeys: Object.keys(responseData || {}),
        hasData: !!responseData?.data,
        hasWorkbooks: !!responseData?.data?.workbooks || !!responseData?.workbooks,
      });

      // ✅ A API retorna dados em responseData.data.workbooks (formato ApiResponse.success)
      const workbooksArray = responseData?.data?.workbooks || responseData?.workbooks || [];
      logger.debug('WorkbooksTable: Array de workbooks extraído', {
        count: workbooksArray.length,
        isArray: Array.isArray(workbooksArray),
      });

      const formattedWorkbooks = workbooksArray.map((wb: any) => ({
        ...wb,
        createdAt: new Date(wb.createdAt),
        updatedAt: new Date(wb.updatedAt),
        // Se sheets já for um array, use seu tamanho, senão use o número diretamente
        sheets: Array.isArray(wb.sheets) ? wb.sheets : wb.sheetsCount || 0,
      }));

      setWorkbooks(formattedWorkbooks);
      setLoadError(false);
    } catch (error) {
      // ✅ Tratamento de erro detalhado
      logger.error('WorkbooksTable: Erro ao carregar planilhas', error, {
        userId: session?.user?.id,
        searchQuery,
        attempts: 'max_attempts_reached',
      });

      if (error instanceof Error) {
        if (error.message.includes('401') || error.message.includes('unauthorized')) {
          toast.error('Sessão expirada. Faça login novamente.');
        } else if (error.message.includes('network') || error.message.includes('fetch')) {
          toast.error('Erro de conexão. Verificando conectividade...');
        } else if (error.message.includes('timeout')) {
          toast.error('Tempo limite excedido. Tente novamente.');
        } else {
          toast.error(`Erro ao carregar planilhas: ${error.message}`);
        }
      } else {
        toast.error('Erro desconhecido ao carregar planilhas. Tente novamente mais tarde.');
      }

      // Marcar o estado de erro
      setLoadError(true);
      setWorkbooks([]);
    } finally {
      logger.debug('WorkbooksTable: Finalizando carregamento', {
        workbooksCount: workbooks.length,
        hasError: loadError,
      });
      setIsLoading(false);
    }
  }, [fetchWithCSRF, session?.user?.id, searchQuery]); // ✅ Apenas ID do usuário

  // Função para tentar novamente a carga dos dados
  const handleRetry = () => {
    setLoadError(false);
    setIsLoading(true);
    fetchWorkbooks();
  };

  // Carregar workbooks ao montar componente
  useEffect(() => {
    if (session?.user?.id) {
      fetchWorkbooks();
    } else {
      // Se não houver usuário autenticado, limpar o estado de carregamento
      setIsLoading(false);
    }
  }, [session?.user?.id]); // ✅ Apenas ID do usuário para evitar loop

  // Reagir às mudanças na busca
  useEffect(() => {
    if (session?.user?.id && searchQuery !== undefined) {
      fetchWorkbooks();
    }
  }, [searchQuery, fetchWorkbooks, session?.user?.id]); // ✅ Dependências corretas incluindo fetchWorkbooks

  // Função para abrir uma planilha
  const handleOpenWorkbook = (workbookId: string) => {
    router.push(`/workbook/${workbookId}`);
  };

  // Função para duplicar uma planilha
  const handleDuplicateWorkbook = async (workbookId: string) => {
    try {
      const response = await fetchWithCSRF(`/api/workbooks/${workbookId}/duplicate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Erro ao duplicar planilha');
      }

      const data = await response.json();

      // Adicionar a nova planilha duplicada à lista
      const newWorkbook = {
        ...data.workbook,
        createdAt: new Date(data.workbook.createdAt),
        updatedAt: new Date(data.workbook.updatedAt),
        sheets: Array.isArray(data.workbook.sheets)
          ? data.workbook.sheets
          : data.workbook.sheetsCount || 0,
      };

      setWorkbooks([...workbooks, newWorkbook]);
      toast.success(`Planilha duplicada com sucesso!`);
    } catch (error) {
      logger.error('WorkbooksTable: Erro ao duplicar workbook', error, {
        workbookId,
        userId: session?.user?.id,
      });
      toast.error('Não foi possível duplicar a planilha');
    }
  };

  // Função para excluir uma planilha
  const handleDeleteWorkbook = async (workbookId: string) => {
    setIsDeleting(workbookId);

    try {
      const response = await fetchWithCSRF(`/api/workbooks/${workbookId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Erro ao excluir planilha');
      }

      setWorkbooks(workbooks.filter(wb => wb.id !== workbookId));
      toast.success('Planilha excluída com sucesso!');
    } catch (error) {
      logger.error('WorkbooksTable: Erro ao excluir workbook', error, {
        workbookId,
        userId: session?.user?.id,
      });
      toast.error('Não foi possível excluir a planilha');
    } finally {
      setIsDeleting(null);
    }
  };

  // Se estiver carregando, mostrar indicador de carregamento
  if (isLoading) {
    return (
      <div className="w-full py-10 flex justify-center">
        <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full"></div>
      </div>
    );
  }

  // Se ocorreu erro, mostrar estado de erro
  if (loadError) {
    return (
      <div className="flex flex-col items-center justify-center h-64 text-center">
        <EmptyState
          icon={<AlertCircle className="h-12 w-12 text-destructive" />}
          title="Erro ao carregar planilhas"
          description="Não foi possível carregar suas planilhas. Isso pode ser um problema temporário."
          action={<Button onClick={() => handleRetry()}>Tentar Novamente</Button>}
        />
      </div>
    );
  }

  // Se não tiver planilhas, mostrar estado vazio
  if (workbooks.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-64 text-center">
        <EmptyState
          icon={<FileSpreadsheet className="h-12 w-12" />}
          title={searchQuery ? 'Nenhuma planilha encontrada' : 'Nenhuma planilha encontrada'}
          description={
            searchQuery
              ? `Não encontramos planilhas com "${searchQuery}". Tente outro termo de busca.`
              : 'Você ainda não criou nenhuma planilha. Comece criando sua primeira planilha agora.'
          }
          action={
            !searchQuery ? (
              <OptimizedButton onClick={() => router.push('/dashboard?create=true')}>
                <Plus className="h-4 w-4 mr-2" />
                Criar Nova Planilha
              </OptimizedButton>
            ) : undefined
          }
        />
      </div>
    );
  }

  return (
    <div className="w-full">
      <Table>
        <TableCaption>Lista de suas planilhas Excel</TableCaption>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[50%]">Nome</TableHead>
            <TableHead>Folhas</TableHead>
            <TableHead>Criado</TableHead>
            <TableHead>Modificado</TableHead>
            <TableHead className="text-right">Ações</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {workbooks.map(workbook => (
            <TableRow key={workbook.id} className="cursor-pointer hover:bg-muted/50">
              <TableCell
                className="font-medium flex items-center gap-2"
                onClick={() => handleOpenWorkbook(workbook.id)}
              >
                <FileSpreadsheet className="h-4 w-4 text-primary" />
                {workbook.name}
              </TableCell>
              <TableCell onClick={() => handleOpenWorkbook(workbook.id)}>
                <Badge variant="outline" className="text-xs">
                  {typeof workbook.sheets === 'number' ? workbook.sheets : workbook.sheets.length}{' '}
                  {(typeof workbook.sheets === 'number'
                    ? workbook.sheets
                    : workbook.sheets.length) > 1
                    ? 'folhas'
                    : 'folha'}
                </Badge>
              </TableCell>
              <TableCell onClick={() => handleOpenWorkbook(workbook.id)}>
                <div className="flex items-center text-xs text-muted-foreground">
                  <Calendar className="h-3 w-3 mr-1" />
                  {formatRelativeDate(workbook.createdAt)}
                </div>
              </TableCell>
              <TableCell onClick={() => handleOpenWorkbook(workbook.id)}>
                <div className="flex items-center text-xs text-muted-foreground">
                  <Clock className="h-3 w-3 mr-1" />
                  {formatRelativeDate(workbook.updatedAt)}
                </div>
              </TableCell>
              <TableCell className="text-right">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <ActionButton
                      variant="ghost"
                      size="icon"
                      actionId={workbook.id}
                      onAction={() => {}} // Trigger é apenas visual, ações estão nos MenuItems
                    >
                      <Settings className="h-4 w-4" />
                    </ActionButton>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuLabel>Opções</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={() => handleOpenWorkbook(workbook.id)}>
                      <Edit className="h-4 w-4 mr-2" />
                      Editar
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleDuplicateWorkbook(workbook.id)}>
                      <Copy className="h-4 w-4 mr-2" />
                      Duplicar
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                      className="text-destructive focus:text-destructive"
                      onClick={() => handleDeleteWorkbook(workbook.id)}
                      disabled={isDeleting === workbook.id}
                    >
                      {isDeleting === workbook.id ? (
                        <>
                          <div className="h-4 w-4 mr-2 rounded-full border-2 border-destructive/20 border-t-destructive animate-spin"></div>
                          Excluindo...
                        </>
                      ) : (
                        <>
                          <Trash2 className="h-4 w-4 mr-2" />
                          Excluir
                        </>
                      )}
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
