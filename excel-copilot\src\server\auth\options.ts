import { PrismaAdapter } from '@auth/prisma-adapter';
import type { Session, User, Account, Profile } from 'next-auth';
import type { JWT } from 'next-auth/jwt';
import CredentialsProvider from 'next-auth/providers/credentials';
import GithubProvider from 'next-auth/providers/github';
import GoogleProvider from 'next-auth/providers/google';

import { logger } from '@/lib/logger';
import { SessionUser } from '@/types/next-auth';

import { ENV } from '../../config/unified-environment';
import { prisma } from '../db/client';

/**
 * Opções de configuração para o Next Auth
 * Configuradas para funcionar com a versão 4.24.11
 */
export const authOptions = {
  adapter: PrismaAdapter(prisma),
  session: {
    strategy: 'jwt' as const,
    maxAge: 30 * 24 * 60 * 60, // 30 dias
  },
  pages: {
    signIn: '/auth/signin',
    error: '/auth/signin?error=AuthError',
  },
  cookies: {
    sessionToken: {
      name: `next-auth.session-token`,
      options: {
        httpOnly: true,
        sameSite: 'lax' as const,
        path: '/',
        secure: ENV.IS_PRODUCTION,
      },
    },
    callbackUrl: {
      name: `next-auth.callback-url`,
      options: {
        sameSite: 'lax' as const,
        path: '/',
        secure: ENV.IS_PRODUCTION,
      },
    },
    csrfToken: {
      name: `next-auth.csrf-token`,
      options: {
        httpOnly: true,
        sameSite: 'lax' as const,
        path: '/',
        secure: ENV.IS_PRODUCTION,
      },
    },
  },
  debug: ENV.IS_DEVELOPMENT, // Debug apenas em desenvolvimento
  logger: {
    error(code: string, ...message: unknown[]) {
      logger.error('AUTH ERROR', { code, message });
    },
    warn(code: string, ...message: unknown[]) {
      logger.warn('AUTH WARNING', { code, message });
    },
    debug(code: string, ...message: unknown[]) {
      if (ENV.IS_DEVELOPMENT) {
        logger.debug('AUTH DEBUG', { code, message });
      }
    },
  },
  providers: ENV.IS_PRODUCTION
    ? [
        // Em produção, sempre usar provedores OAuth
        GoogleProvider({
          clientId: ENV.API_KEYS.GOOGLE_CLIENT_ID,
          clientSecret: ENV.API_KEYS.GOOGLE_CLIENT_SECRET,
          allowDangerousEmailAccountLinking: true,
          authorization: {
            params: {
              scope: 'openid email profile',
              prompt: 'consent',
              access_type: 'offline',
              response_type: 'code',
            },
          },
        }),
        GithubProvider({
          clientId: ENV.API_KEYS.GITHUB_CLIENT_ID,
          clientSecret: ENV.API_KEYS.GITHUB_CLIENT_SECRET,
          allowDangerousEmailAccountLinking: true,
        }),
      ]
    : ENV.FEATURES.SKIP_AUTH_PROVIDERS || process.env.AUTH_SKIP_PROVIDERS === 'true'
      ? [
          // Em desenvolvimento com SKIP_AUTH_PROVIDERS, usar provedor simples
          CredentialsProvider({
            name: 'Desenvolvimento',
            credentials: {
              email: { label: 'Email', type: 'text' },
              password: { label: 'Password', type: 'password' },
            },
            async authorize() {
              // Para fins de desenvolvimento, permitimos login sem credenciais
              return {
                id: 'dev-user',
                name: 'Usuário Desenvolvimento',
                email: '<EMAIL>',
              };
            },
          }),
        ]
      : [
          // Em desenvolvimento sem SKIP_AUTH_PROVIDERS, usar OAuth também
          GoogleProvider({
            clientId: ENV.API_KEYS.GOOGLE_CLIENT_ID,
            clientSecret: ENV.API_KEYS.GOOGLE_CLIENT_SECRET,
            allowDangerousEmailAccountLinking: true,
            authorization: {
              params: {
                scope: 'openid email profile',
                prompt: 'consent',
                access_type: 'offline',
                response_type: 'code',
              },
            },
          }),
          GithubProvider({
            clientId: ENV.API_KEYS.GITHUB_CLIENT_ID,
            clientSecret: ENV.API_KEYS.GITHUB_CLIENT_SECRET,
            allowDangerousEmailAccountLinking: true,
          }),
        ],
  callbacks: {
    /**
     * Callback de login - permite ou bloqueia o login
     */
    async signIn({
      user,
      account,
      profile: _profile,
    }: {
      user: User;
      account: Account | null;
      profile?: Profile;
    }) {
      try {
        // Log do processo de login para debug
        logger.info('🔐 Tentativa de login', {
          userId: user?.id,
          email: user?.email,
          provider: account?.provider,
          type: account?.type,
        });

        // Permitir login para todos os provedores OAuth configurados
        if (account?.type === 'oauth') {
          logger.info('✅ Login OAuth autorizado', {
            provider: account.provider,
            email: user?.email,
          });
          return true;
        }

        // Permitir login para credenciais (se configurado)
        if (account?.type === 'credentials') {
          logger.info('✅ Login por credenciais autorizado', {
            email: user?.email,
          });
          return true;
        }

        // Log de tentativa de login não autorizada
        logger.warn('❌ Tentativa de login não autorizada', {
          accountType: account?.type,
          provider: account?.provider,
          email: user?.email,
        });

        return true; // Permitir por padrão para evitar bloqueios
      } catch (error) {
        logger.error('💥 Erro no callback signIn', {
          error: error instanceof Error ? error.message : 'Erro desconhecido',
          userId: user?.id,
          email: user?.email,
          provider: account?.provider,
        });

        // Em caso de erro, permitir o login para evitar bloqueios
        return true;
      }
    },

    /**
     * Callback de sessão - adiciona ID do usuário à sessão
     */
    async session({ session, token }: { session: Session; token: JWT }) {
      if (session.user && token.sub) {
        const sessionUser = session.user as SessionUser;
        sessionUser.id = token.sub;
      }
      return session;
    },

    /**
     * Callback JWT - preserva ID do usuário no token
     */
    async jwt({ token, user }: { token: JWT; user?: User }) {
      if (user?.id) {
        token.sub = user.id;
      }
      return token;
    },

    /**
     * Callback de redirecionamento - garante URLs seguras
     */
    async redirect({ url, baseUrl }: { url: string; baseUrl: string }) {
      // URLs permitidas para redirecionamento
      const allowedPaths = ['/dashboard', '/workbook', '/account', '/pricing', '/'];

      try {
        // URLs relativas
        if (url.startsWith('/')) {
          const path = url.split('?')[0] || '/';
          if (allowedPaths.some(allowed => path.startsWith(allowed))) {
            return `${baseUrl}${url}`;
          }
          return `${baseUrl}/dashboard`;
        }

        // URLs absolutas do mesmo domínio
        if (url.startsWith(baseUrl)) {
          const relativePath = url.replace(baseUrl, '');
          const path = relativePath.split('?')[0] || '/';
          if (allowedPaths.some(allowed => path.startsWith(allowed))) {
            return url;
          }
        }

        // Fallback seguro
        return `${baseUrl}/dashboard`;
      } catch {
        return `${baseUrl}/dashboard`;
      }
    },
  },
  secret: ENV.NEXTAUTH_SECRET,
  // Configurações adicionais para produção
  trustHost: true, // Necessário para Vercel
  useSecureCookies: ENV.IS_PRODUCTION, // Usar cookies seguros em produção
};
