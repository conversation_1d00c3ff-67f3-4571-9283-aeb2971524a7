/**
 * 🔌 MCP HEALTH CHECK ENDPOINT
 *
 * GET /api/health/mcp
 *
 * Verifica a saúde das integrações MCP (Vercel, Linear, GitHub)
 */

import { NextRequest, NextResponse } from 'next/server';

import {
  checkService,
  healthStatusToHttpCode,
  formatHealthResponse,
} from '@/lib/health-checks/index';

export async function GET(request: NextRequest) {
  try {
    // Verificar se deve incluir detalhes
    const url = new URL(request.url);
    const includeDetails = url.searchParams.get('details') !== 'false';

    // Executar health check das integrações MCP
    const result = await checkService('mcp');

    // Determinar código de status HTTP
    const statusCode = healthStatusToHttpCode(result.status);

    // Formatar resposta
    const response = formatHealthResponse(result, includeDetails);

    return NextResponse.json(response, {
      status: statusCode,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        Pragma: 'no-cache',
        Expires: '0',
      },
    });
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json(
      {
        status: 'unhealthy',
        service: 'mcp',
        timestamp: new Date().toISOString(),
        responseTime: 0,
        error: errorMessage,
      },
      {
        status: 500,
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
        },
      }
    );
  }
}

// Permitir apenas GET
export async function POST() {
  return NextResponse.json({ error: 'Method not allowed' }, { status: 405 });
}

export async function PUT() {
  return NextResponse.json({ error: 'Method not allowed' }, { status: 405 });
}

export async function DELETE() {
  return NextResponse.json({ error: 'Method not allowed' }, { status: 405 });
}
