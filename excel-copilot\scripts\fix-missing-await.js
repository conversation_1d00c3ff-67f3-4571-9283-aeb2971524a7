/**
 * Script para corrigir problemas de "missing await" em testes
 * Este script encontra e corrige problemas onde uma Promise não é aguardada antes de acessar suas propriedades
 */
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Diretório base dos testes
const testDirs = [path.join(__dirname, '..', '__tests__')];

// Extensões de arquivo a considerar
const fileExtensions = ['.ts', '.tsx'];

// Expressões regulares para encontrar problemas
const problemPatterns = [
  // Padrão para expect(result.PROPERTY) onde result é uma Promise
  /(expect\()([a-zA-Z0-9_]+)(\.[\w.]+\))/g,

  // Padrão para verificação de propriedade de Promise (if (result.success))
  /(if\s*\()([a-zA-Z0-9_]+)(\.[\w.]+\))/g,

  // Padrão para atribuição de propriedade de Promise (const success = result.success)
  /(const|let|var)\s+([a-zA-Z0-9_]+)\s*=\s*([a-zA-Z0-9_]+)(\.[\w.]+)/g,
];

// Lista de variáveis que sabemos que são Promises
let knownPromiseVars = [];

/**
 * Verifica se um arquivo contém declarações de Promise
 * @param {string} content Conteúdo do arquivo
 * @returns {string[]} Lista de nomes de variáveis que são Promises
 */
function findPromiseVariables(content) {
  const promiseVars = [];

  // Procurar por declarações de Promise
  const promiseDeclarationPattern =
    /(?:const|let|var)\s+([a-zA-Z0-9_]+)\s*=\s*(?:await\s+)?.*Promise</g;
  let match;
  while ((match = promiseDeclarationPattern.exec(content)) !== null) {
    promiseVars.push(match[1]);
  }

  // Procurar por atribuições de funções que retornam Promise
  const asyncFunctionPattern =
    /(?:const|let|var)\s+([a-zA-Z0-9_]+)\s*=\s*(?:await\s+)?([a-zA-Z0-9_]+\.[a-zA-Z0-9_]+\(.*\)|[a-zA-Z0-9_]+\(.*\))/g;
  while ((match = asyncFunctionPattern.exec(content)) !== null) {
    // Verificar se a função é assíncrona verificando o conteúdo anterior
    const varName = match[1];
    const functionCall = match[2];

    // Verificar se a função tem indicação de ser assíncrona
    if (
      content.includes(`async function ${functionCall}`) ||
      content.includes(`${functionCall}: async`) ||
      content.includes(`${functionCall} = async`)
    ) {
      promiseVars.push(varName);
    }
  }

  return promiseVars;
}

/**
 * Corrige problemas de await em um arquivo
 * @param {string} filePath Caminho do arquivo
 */
function fixAwaitIssues(filePath) {
  console.log(`\nVerificando: ${filePath}`);

  let content = fs.readFileSync(filePath, 'utf8');
  const originalContent = content;

  // Encontrar variáveis que são Promises
  knownPromiseVars = findPromiseVariables(content);

  if (knownPromiseVars.length > 0) {
    console.log(`Encontradas variáveis Promise: ${knownPromiseVars.join(', ')}`);
  } else {
    // Se não encontramos variáveis Promise, não há nada para corrigir
    console.log('Nenhuma variável Promise encontrada, pulando.');
    return;
  }

  // Corrigir problemas para cada padrão
  let changes = false;
  let replacements = [];

  for (const pattern of problemPatterns) {
    let match;
    while ((match = pattern.exec(content)) !== null) {
      // Verificar se a variável é uma Promise
      const varName = match[2];

      if (knownPromiseVars.includes(varName)) {
        const fullMatch = match[0];
        let replacement;

        // Criar substituição apropriada baseada no padrão
        if (match[0].startsWith('expect(')) {
          // Para expressões expect
          replacement = `expect((await ${varName})${match[3]}`;
        } else if (match[0].startsWith('if(') || match[0].startsWith('if (')) {
          // Para expressões if
          replacement = `if((await ${varName})${match[3]}`;
        } else {
          // Para atribuições
          replacement = `${match[1]} ${match[2]} = (await ${match[3]})${match[4]}`;
        }

        replacements.push({ original: fullMatch, replacement });
        changes = true;
      }
    }
  }

  // Aplicar substituições
  if (replacements.length > 0) {
    let newContent = content;
    for (const { original, replacement } of replacements) {
      console.log(`Substituindo: ${original} -> ${replacement}`);
      newContent = newContent.replace(original, replacement);
    }

    // Salvar alterações
    fs.writeFileSync(filePath, newContent, 'utf8');
    console.log(`✅ Corrigido arquivo: ${filePath} (${replacements.length} alterações)`);
  } else {
    console.log(`✓ Nenhum problema encontrado em: ${filePath}`);
  }
}

/**
 * Processa diretório recursivamente
 * @param {string} dir Diretório a ser processado
 */
function processDirectory(dir) {
  const files = fs.readdirSync(dir);

  for (const file of files) {
    const fullPath = path.join(dir, file);
    const stat = fs.statSync(fullPath);

    if (stat.isDirectory()) {
      processDirectory(fullPath);
    } else if (fileExtensions.some(ext => file.endsWith(ext))) {
      fixAwaitIssues(fullPath);
    }
  }
}

/**
 * Função principal
 */
function main() {
  console.log('🔍 Iniciando correção de problemas de await em testes...');

  // Processar cada diretório de testes
  for (const dir of testDirs) {
    if (fs.existsSync(dir)) {
      console.log(`\nProcessando diretório: ${dir}`);
      processDirectory(dir);
    } else {
      console.log(`⚠️ Diretório não encontrado: ${dir}`);
    }
  }

  console.log('\n✅ Processo concluído!');
  console.log('Execute os testes para verificar se as correções resolveram os problemas:');
  console.log('npm run test:simple');
}

// Executar script
main();
