import { AuthOptions } from 'next-auth';
import NextAuth from 'next-auth/next';

import { authOptions } from '../../../../server/auth/options';

export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

// Debug logging only in development
if (process.env.NODE_ENV === 'development') {
  // Log adicional para depuração em desenvolvimento
}

// Modificar as opções para debug apenas em desenvolvimento
const enhancedOptions = {
  ...authOptions,
  debug: process.env.NODE_ENV === 'development', // CORREÇÃO: Debug apenas em desenvolvimento
  logger: {
    ...authOptions.logger,
    error(code: string, ...message: unknown[]) {
      if (process.env.NODE_ENV === 'development') {
        console.error('*AUTH ERROR*:', code, ...message);
      }
    },
    warn(code: string, ...message: unknown[]) {
      if (process.env.NODE_ENV === 'development') {
        console.warn('*AUTH WARNING*:', code, ...message);
      }
    },
    debug(code: string, ...message: unknown[]) {
      if (process.env.NODE_ENV === 'development') {
        console.debug('*AUTH DEBUG*:', code, ...message);
      }
    },
  },
} as AuthOptions;

// Usar a tipagem AuthOptions do next-auth
// e fazer um cast para evitar problemas de compatibilidade
const handler = NextAuth(enhancedOptions);

export { handler as GET, handler as POST };
