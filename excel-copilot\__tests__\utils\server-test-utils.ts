/**
 * Utilitários específicos para testar o mock server
 */

import { server, rest, HttpResponse } from '../mocks/server';
import { MockRequest, MockResponse, MockContext } from '../mocks/types';
import { createMockWorkbook, createMockSheetData, createApiResponse } from '../mocks/factories';

/**
 * Configura handlers comuns para testes relacionados ao Excel
 */
export const setupExcelMockHandlers = () => {
  // Mock para API de listagem de workbooks
  server.use(
    rest.get('/api/workbooks', () => {
      const workbooks = [createMockWorkbook({ id: 'wb-1' }), createMockWorkbook({ id: 'wb-2' })];

      return HttpResponse.json(createApiResponse(workbooks), { status: 200 });
    }),

    // Mock para API de detalhes de um workbook
    rest.get('/api/workbooks/:id', ({ params }) => {
      const { id } = params || {};

      if (id === 'invalid-id') {
        return HttpResponse.json(createApiResponse(null, false, 'Workbook não encontrado'), {
          status: 404,
        });
      }

      const workbook = createMockWorkbook({ id: id as string });
      return HttpResponse.json(createApiResponse(workbook), { status: 200 });
    }),

    // Mock para API de dados de uma planilha
    rest.get('/api/workbooks/:id/sheets/:sheetName', ({ params }) => {
      const { id, sheetName } = params || {};

      if (id === 'invalid-id' || sheetName === 'invalid-sheet') {
        return HttpResponse.json(createApiResponse(null, false, 'Planilha não encontrada'), {
          status: 404,
        });
      }

      const data = createMockSheetData(5, 3, true);
      return HttpResponse.json(createApiResponse(data), { status: 200 });
    }),

    // Mock para API de operações em planilhas
    rest.post('/api/workbooks/:id/operations', async ({ params, request }) => {
      const { id } = params || {};
      const body = await request.json();
      const { operations } = body || {};

      if (!operations || !Array.isArray(operations)) {
        return HttpResponse.json(createApiResponse(null, false, 'Formato de operações inválido'), {
          status: 400,
        });
      }

      return HttpResponse.json(
        createApiResponse({
          operationsExecuted: operations.length,
          updatedCells: ['A1', 'B1', 'C1'],
        }),
        { status: 200 }
      );
    })
  );
};

/**
 * Configura handlers comuns para testes relacionados à autenticação
 */
export const setupAuthMockHandlers = () => {
  server.use(
    // Mock para login
    rest.post('/api/auth/login', async ({ request }) => {
      const body = await request.json();
      const { email, password } = body || {};

      if (!email || !password) {
        return HttpResponse.json(createApiResponse(null, false, 'Email e senha são obrigatórios'), {
          status: 400,
        });
      }

      if (email === '<EMAIL>') {
        return HttpResponse.json(createApiResponse(null, false, 'Credenciais inválidas'), {
          status: 401,
        });
      }

      return HttpResponse.json(
        createApiResponse({
          token: 'mock-jwt-token',
          user: {
            id: 'user-1',
            name: 'Usuário Teste',
            email: email as string,
          },
        }),
        { status: 200 }
      );
    }),

    // Mock para logout
    rest.post('/api/auth/logout', () => {
      return HttpResponse.json(createApiResponse({ success: true }), { status: 200 });
    }),

    // Mock para verificação de sessão
    rest.get('/api/auth/session', () => {
      return HttpResponse.json(
        createApiResponse({
          isLoggedIn: true,
          user: {
            id: 'user-1',
            name: 'Usuário Teste',
            email: '<EMAIL>',
          },
        }),
        { status: 200 }
      );
    })
  );
};

/**
 * Configura handlers para testar erros e casos excepcionais
 */
export const setupErrorMockHandlers = () => {
  server.use(
    // Mock para simular erro de servidor
    rest.get('/api/error/server', () => {
      return HttpResponse.json(createApiResponse(null, false, 'Erro interno do servidor'), {
        status: 500,
      });
    }),

    // Mock para simular timeout
    rest.get('/api/error/timeout', () => {
      // Não retorna nada (simula timeout)
      return new Promise(() => {});
    }),

    // Mock para simular erro de rede
    rest.get('/api/error/network', () => {
      throw new Error('Erro de rede simulado');
    })
  );
};

/**
 * Configura handlers para testar a Desktop Bridge
 */
export const setupDesktopBridgeMockHandlers = () => {
  server.use(
    // Status da Desktop Bridge
    rest.get('/api/desktop-bridge/status', () => {
      return HttpResponse.json(
        {
          connected: true,
          version: '1.0.0',
          platform: 'win32',
        },
        { status: 200 }
      );
    }),

    // Conexão com a Desktop Bridge
    rest.post('/api/desktop-bridge/connect', () => {
      return HttpResponse.json(
        createApiResponse({
          connected: true,
          message: 'Conexão estabelecida com sucesso',
        }),
        { status: 200 }
      );
    })
  );
};

/**
 * Configura todos os handlers de teste de uma vez
 */
export const setupAllMockHandlers = () => {
  setupExcelMockHandlers();
  setupAuthMockHandlers();
  setupErrorMockHandlers();
  setupDesktopBridgeMockHandlers();
};
