/**
 * Testes de validação para tipos TypeScript da integração Vercel
 * Garante que os tipos estão corretos e consistentes
 */

import type {
  VercelDeployment,
  VercelLog,
  VercelMetrics,
  VercelProject,
} from '@/lib/vercel-integration';

describe('Tipos TypeScript Vercel', () => {
  describe('VercelDeployment', () => {
    it('deve aceitar deployment válido', () => {
      const deployment: VercelDeployment = {
        uid: 'dpl_test123',
        name: 'excel-copilot',
        url: 'https://excel-copilot.vercel.app',
        state: 'READY',
        type: 'LAMBDAS',
        created: 1640995200000,
        buildingAt: 1640995150000,
        ready: 1640995300000,
        target: 'production',
      };

      expect(deployment.uid).toBe('dpl_test123');
      expect(deployment.state).toBe('READY');
      expect(deployment.target).toBe('production');
      expect(typeof deployment.created).toBe('number');
      expect(typeof deployment.buildingAt).toBe('number');
    });

    it('deve aceitar deployment com propriedades opcionais', () => {
      const deployment: VercelDeployment = {
        uid: 'dpl_test456',
        name: 'excel-copilot',
        url: 'https://excel-copilot-preview.vercel.app',
        state: 'BUILDING',
        type: 'LAMBDAS',
        created: 1640995200000,
        buildingAt: 1640995200000,
        ready: null,
        target: 'preview',
        creator: {
          uid: 'user_123',
          username: 'developer',
          email: '<EMAIL>',
        },
        meta: {
          githubCommitSha: 'abc123def456',
          githubCommitMessage: 'feat: new feature',
          githubCommitAuthorName: 'Developer',
          githubCommitRef: 'main',
        },
      };

      expect(deployment.ready).toBeNull();
      expect(deployment.creator?.username).toBe('developer');
      expect(deployment.meta?.githubCommitSha).toBe('abc123def456');
    });

    it('deve validar estados de deployment válidos', () => {
      const validStates: VercelDeployment['state'][] = [
        'BUILDING',
        'ERROR',
        'INITIALIZING',
        'QUEUED',
        'READY',
        'CANCELED',
      ];

      validStates.forEach(state => {
        const deployment: VercelDeployment = {
          uid: 'dpl_test',
          name: 'test',
          url: 'https://test.vercel.app',
          state,
          type: 'LAMBDAS',
          created: Date.now(),
          ...(state === 'BUILDING' && { buildingAt: Date.now() }),
          ready: state === 'READY' ? Date.now() : null,
          target: 'production',
        };

        expect(deployment.state).toBe(state);
      });
    });

    it('deve validar targets válidos', () => {
      // Teste para production
      const productionDeployment: VercelDeployment = {
        uid: 'dpl_test_prod',
        name: 'test',
        url: 'https://test.vercel.app',
        state: 'READY',
        type: 'LAMBDAS',
        created: Date.now(),
        ready: Date.now(),
        target: 'production',
      };
      expect(productionDeployment.target).toBe('production');

      // Teste para preview
      const previewDeployment: VercelDeployment = {
        uid: 'dpl_test_preview',
        name: 'test',
        url: 'https://test-preview.vercel.app',
        state: 'READY',
        type: 'LAMBDAS',
        created: Date.now(),
        ready: Date.now(),
        target: 'preview',
      };
      expect(previewDeployment.target).toBe('preview');

      // Teste para development
      const developmentDeployment: VercelDeployment = {
        uid: 'dpl_test_dev',
        name: 'test',
        url: 'https://test-dev.vercel.app',
        state: 'READY',
        type: 'LAMBDAS',
        created: Date.now(),
        ready: Date.now(),
        target: 'development',
      };
      expect(developmentDeployment.target).toBe('development');
    });
  });

  describe('VercelLog', () => {
    it('deve aceitar log válido', () => {
      const log: VercelLog = {
        id: 'log_test123',
        timestamp: 1640995200000,
        message: 'Request processed successfully',
        level: 'info',
        source: 'lambda',
        requestId: 'req_abc123',
        deploymentId: 'dpl_test123',
      };

      expect(log.id).toBe('log_test123');
      expect(log.level).toBe('info');
      expect(log.source).toBe('lambda');
      expect(typeof log.timestamp).toBe('number');
    });

    it('deve validar níveis de log válidos', () => {
      const validLevels: VercelLog['level'][] = ['debug', 'info', 'warn', 'error'];

      validLevels.forEach(level => {
        const log: VercelLog = {
          id: 'log_test',
          timestamp: Date.now(),
          message: 'Test message',
          level,
          source: 'lambda',
          requestId: 'req_test',
          deploymentId: 'dpl_test',
        };

        expect(log.level).toBe(level);
      });
    });

    it('deve validar fontes válidas', () => {
      const validSources: VercelLog['source'][] = ['lambda', 'edge', 'build', 'static'];

      validSources.forEach(source => {
        const log: VercelLog = {
          id: 'log_test',
          timestamp: Date.now(),
          message: 'Test message',
          level: 'info',
          source,
          requestId: 'req_test',
          deploymentId: 'dpl_test',
        };

        expect(log.source).toBe(source);
      });
    });

    it('deve aceitar propriedades opcionais', () => {
      const log: VercelLog = {
        id: 'log_test',
        timestamp: Date.now(),
        message: 'Test message',
        level: 'info',
        source: 'lambda',
        requestId: 'req_test',
        deploymentId: 'dpl_test',
        region: 'iad1',
        statusCode: 200,
        duration: 150,
      };

      expect(log.region).toBe('iad1');
      expect(log.statusCode).toBe(200);
      expect(log.duration).toBe(150);
    });
  });

  describe('VercelMetrics', () => {
    it('deve aceitar métricas válidas', () => {
      const metrics: VercelMetrics = {
        requests24h: 1500,
        errors24h: 10,
        errorRate: 0.0067,
        avgResponseTime: 150,
      };

      expect(typeof metrics.requests24h).toBe('number');
      expect(typeof metrics.errors24h).toBe('number');
      expect(typeof metrics.errorRate).toBe('number');
      expect(typeof metrics.avgResponseTime).toBe('number');
      expect(metrics.errorRate).toBeGreaterThanOrEqual(0);
      expect(metrics.errorRate).toBeLessThanOrEqual(1);
    });

    it('deve aceitar métricas com propriedades opcionais', () => {
      const metrics: VercelMetrics = {
        requests24h: 1500,
        errors24h: 10,
        errorRate: 0.0067,
        avgResponseTime: 150,
        bandwidth24h: 1024 * 1024 * 100, // 100MB
        uniqueVisitors24h: 250,
        p95ResponseTime: 300,
        p99ResponseTime: 500,
      };

      expect(metrics.bandwidth24h).toBe(1024 * 1024 * 100);
      expect(metrics.uniqueVisitors24h).toBe(250);
      expect(metrics.p95ResponseTime).toBe(300);
      expect(metrics.p99ResponseTime).toBe(500);
    });
  });

  describe('VercelProject', () => {
    it('deve aceitar projeto válido', () => {
      const project: VercelProject = {
        id: 'prj_test123',
        name: 'excel-copilot',
        framework: 'nextjs',
        createdAt: 1640995200000,
        updatedAt: 1640995300000,
        env: [
          { key: 'NODE_ENV', value: 'production' },
          { key: 'DATABASE_URL', value: 'postgresql://...' },
        ],
        targets: {
          production: {
            id: 'dpl_prod123',
            url: 'https://excel-copilot.vercel.app',
            state: 'READY',
            createdAt: 1640995200000,
          },
        },
      };

      expect(project.id).toBe('prj_test123');
      expect(project.name).toBe('excel-copilot');
      expect(project.framework).toBe('nextjs');
      expect(Array.isArray(project.env)).toBe(true);
      expect(project.targets?.production?.state).toBe('READY');
    });

    it('deve aceitar projeto com propriedades opcionais', () => {
      const project: VercelProject = {
        id: 'prj_test456',
        name: 'test-project',
        framework: 'nextjs',
        createdAt: 1640995200000,
        updatedAt: 1640995300000,
        env: [],
        targets: {},
        description: 'Test project description',
        repository: {
          type: 'github',
          url: 'https://github.com/user/repo',
        },
        settings: {
          buildCommand: 'npm run build',
          outputDirectory: '.next',
          installCommand: 'npm install',
        },
      };

      expect(project.description).toBe('Test project description');
      expect(project.repository?.type).toBe('github');
      expect(project.settings?.buildCommand).toBe('npm run build');
    });
  });

  describe('Consistência entre tipos', () => {
    it('deve manter consistência entre VercelDeployment e VercelLog', () => {
      const deployment: VercelDeployment = {
        uid: 'dpl_consistent',
        name: 'excel-copilot',
        url: 'https://excel-copilot.vercel.app',
        state: 'READY',
        type: 'LAMBDAS',
        created: 1640995200000,
        ready: 1640995300000,
        target: 'production',
      };

      const log: VercelLog = {
        id: 'log_consistent',
        timestamp: 1640995250000,
        message: 'Deployment ready',
        level: 'info',
        source: 'lambda',
        requestId: 'req_consistent',
        deploymentId: deployment.uid, // Deve ser consistente
      };

      expect(log.deploymentId).toBe(deployment.uid);
    });

    it('deve permitir cálculo de métricas baseado em logs', () => {
      const logs: VercelLog[] = [
        {
          id: 'log_1',
          timestamp: Date.now(),
          message: 'Success',
          level: 'info',
          source: 'lambda',
          requestId: 'req_1',
          deploymentId: 'dpl_test',
          statusCode: 200,
        },
        {
          id: 'log_2',
          timestamp: Date.now(),
          message: 'Error',
          level: 'error',
          source: 'lambda',
          requestId: 'req_2',
          deploymentId: 'dpl_test',
          statusCode: 500,
        },
      ];

      const totalRequests = logs.length;
      const errors = logs.filter(log => log.level === 'error').length;
      const errorRate = errors / totalRequests;

      const metrics: VercelMetrics = {
        requests24h: totalRequests,
        errors24h: errors,
        errorRate,
        avgResponseTime: 150,
      };

      expect(metrics.errorRate).toBe(0.5);
      expect(metrics.errors24h).toBe(1);
    });
  });
});
