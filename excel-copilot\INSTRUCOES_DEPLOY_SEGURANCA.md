# 🔒 **INSTRUÇÕES PARA DEPLOY DOS PATCHES DE SEGURANÇA**

**Data:** 29 de Janeiro de 2025  
**Versão:** 1.0  
**Criticidade:** **ALTA** - Implementação Obrigatória

---

## 🚨 **RESUMO EXECUTIVO**

Os patches de segurança corrigem **3 vulnerabilidades críticas** identificadas na auditoria:

1. **Race Conditions** em contadores de workbooks
2. **Manipulação de Cache** de planos de usuário
3. **Exposição de Informações** em logs

**⚠️ ATENÇÃO:** Estes patches devem ser implementados **IMEDIATAMENTE** antes de qualquer deploy em produção.

---

## 📋 **PRÉ-REQUISITOS**

### **Ambiente de Desenvolvimento:**

- [ ] Node.js 18+ instalado
- [ ] npm ou yarn configurado
- [ ] Acesso ao banco de dados de desenvolvimento
- [ ] Variáveis de ambiente configuradas

### **Ambiente de Staging:**

- [ ] Ambiente de staging idêntico à produção
- [ ] Banco de dados de staging com dados de teste
- [ ] Monitoramento de logs configurado
- [ ] Acesso SSH/deploy configurado

### **Ambiente de Produção:**

- [ ] Backup completo do banco de dados
- [ ] Janela de manutenção agendada
- [ ] Plano de rollback preparado
- [ ] Equipe de suporte em standby

---

## 🛠️ **ETAPA 1: PREPARAÇÃO (30 minutos)**

### **1.1 Backup Completo**

```bash
# Backup do código atual
git checkout -b security-patches-backup
git add .
git commit -m "Backup antes dos patches de segurança"

# Backup do banco de dados
pg_dump $DATABASE_URL > backup_pre_security_patches_$(date +%Y%m%d_%H%M%S).sql
```

### **1.2 Verificação do Estado Atual**

```bash
# Verificar se não há erros TypeScript
npm run type-check

# Verificar linting
npm run lint

# Executar testes existentes
npm test
```

### **1.3 Configurar Variáveis de Ambiente**

```bash
# Adicionar ao .env.local (desenvolvimento)
echo "CACHE_SECRET=$(openssl rand -hex 32)" >> .env.local

# Adicionar ao .env.production (produção)
echo "CACHE_SECRET=$(openssl rand -hex 32)" >> .env.production
```

---

## 🔧 **ETAPA 2: IMPLEMENTAÇÃO EM DESENVOLVIMENTO (45 minutos)**

### **2.1 Aplicar Patches Automaticamente**

```bash
# Dar permissão de execução ao script
chmod +x deploy-security-patches.sh

# Executar script de implementação
./deploy-security-patches.sh
```

### **2.2 Verificação Manual dos Patches**

#### **A. Verificar Transações Atômicas:**

```bash
# Verificar se a função createWorkbookAtomic existe
grep -n "createWorkbookAtomic" src/lib/subscription-limits.ts
```

#### **B. Verificar Validação de Cache:**

```bash
# Verificar se a validação HMAC foi implementada
grep -n "generateCacheSignature" src/lib/middleware/plan-based-rate-limiter.ts
```

#### **C. Verificar Logs Sanitizados:**

```bash
# Verificar se os logs foram sanitizados
grep -n "userHash" src/lib/middleware/plan-based-rate-limiter.ts
```

### **2.3 Testes de Funcionalidade**

```bash
# Iniciar servidor de desenvolvimento
npm run dev

# Em outro terminal, testar criação de workbook
curl -X POST http://localhost:3000/api/workbook/save \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Teste Segurança",
    "sheets": [{"name": "Sheet1", "data": []}]
  }'
```

---

## 🧪 **ETAPA 3: TESTES DE SEGURANÇA (60 minutos)**

### **3.1 Teste de Race Conditions**

```javascript
// Criar arquivo test-race-conditions.js
const testRaceConditions = async () => {
  const promises = Array(10)
    .fill()
    .map(async (_, i) => {
      const response = await fetch('http://localhost:3000/api/workbook/save', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          name: `Test ${i}`,
          sheets: [{ name: 'Sheet1', data: [] }],
        }),
      });
      return { index: i, status: response.status };
    });

  const results = await Promise.all(promises);
  console.log('Resultados:', results);
};

testRaceConditions();
```

### **3.2 Teste de Validação de Cache**

```bash
# Verificar logs para validação de cache
tail -f logs/application.log | grep "CACHE_INTEGRITY"
```

### **3.3 Teste de Logs Sanitizados**

```bash
# Verificar se IDs de usuários não aparecem em logs
tail -f logs/application.log | grep -v "user_" | grep "RATE_LIMIT"
```

---

## 🚀 **ETAPA 4: DEPLOY EM STAGING (30 minutos)**

### **4.1 Preparar Branch de Deploy**

```bash
# Criar branch para staging
git checkout -b security-patches-staging
git add .
git commit -m "feat: implementar patches críticos de segurança

- Corrigir race conditions com transações atômicas
- Implementar validação HMAC para cache
- Sanitizar logs para proteção de dados
- Adicionar API atômica para criação de workbooks"

# Push para staging
git push origin security-patches-staging
```

### **4.2 Deploy em Staging**

```bash
# Deploy via Vercel (exemplo)
vercel --prod --env CACHE_SECRET=$(openssl rand -hex 32)

# Ou deploy manual
npm run build
npm run start
```

### **4.3 Testes em Staging**

```bash
# Testar endpoints críticos
curl -X POST https://staging.excel-copilot.com/api/workbook/save \
  -H "Content-Type: application/json" \
  -d '{"name": "Teste Staging", "sheets": [{"name": "Sheet1", "data": []}]}'

# Verificar logs de segurança
heroku logs --tail --app excel-copilot-staging | grep "SECURITY"
```

---

## 🏭 **ETAPA 5: DEPLOY EM PRODUÇÃO (45 minutos)**

### **5.1 Janela de Manutenção**

```bash
# Ativar modo de manutenção
echo "Sistema em manutenção para atualizações de segurança" > maintenance.html

# Notificar usuários (se aplicável)
# Enviar email/notificação sobre manutenção
```

### **5.2 Deploy Gradual**

```bash
# 1. Deploy para 10% do tráfego
vercel --prod --scale 10%

# 2. Monitorar por 15 minutos
watch -n 30 'curl -s https://excel-copilot.com/api/health'

# 3. Se estável, escalar para 50%
vercel --scale 50%

# 4. Monitorar por mais 15 minutos
# 5. Se estável, escalar para 100%
vercel --scale 100%
```

### **5.3 Configurar Variáveis de Produção**

```bash
# Via Vercel Dashboard ou CLI
vercel env add CACHE_SECRET production
# Inserir valor gerado: $(openssl rand -hex 32)

# Verificar configuração
vercel env ls
```

---

## 📊 **ETAPA 6: MONITORAMENTO PÓS-DEPLOY (24 horas)**

### **6.1 Métricas Críticas**

```bash
# Monitorar logs de segurança
tail -f /var/log/excel-copilot/security.log

# Verificar rate limiting
grep "RATE_LIMIT_EXCEEDED" /var/log/excel-copilot/application.log | wc -l

# Monitorar transações atômicas
grep "workbook_created_atomic" /var/log/excel-copilot/application.log | wc -l
```

### **6.2 Alertas Configurados**

- [ ] Alertas para tentativas de bypass
- [ ] Alertas para race conditions detectadas
- [ ] Alertas para violações de integridade de cache
- [ ] Alertas para erros de transação

### **6.3 Dashboard de Monitoramento**

```bash
# Criar dashboard simples
echo "
📊 MONITORAMENTO DE SEGURANÇA
============================
Workbooks criados hoje: $(grep 'workbook_created_atomic' logs/app.log | grep $(date +%Y-%m-%d) | wc -l)
Rate limits aplicados: $(grep 'RATE_LIMIT_EXCEEDED' logs/app.log | grep $(date +%Y-%m-%d) | wc -l)
Cache violations: $(grep 'CACHE_INTEGRITY_VIOLATION' logs/app.log | grep $(date +%Y-%m-%d) | wc -l)
" > security-dashboard.txt
```

---

## 🔄 **PLANO DE ROLLBACK**

### **Em Caso de Problemas:**

#### **Rollback Imediato (< 5 minutos):**

```bash
# 1. Reverter deploy
git checkout main
vercel --prod

# 2. Restaurar banco (se necessário)
psql $DATABASE_URL < backup_pre_security_patches_*.sql

# 3. Notificar equipe
echo "ROLLBACK EXECUTADO - Patches de segurança revertidos" | mail -s "ALERT" <EMAIL>
```

#### **Rollback Parcial:**

```bash
# Reverter apenas arquivos específicos
git checkout HEAD~1 -- src/lib/subscription-limits.ts
git commit -m "rollback: reverter correção de race conditions"
```

---

## ✅ **CHECKLIST DE VALIDAÇÃO FINAL**

### **Funcionalidade:**

- [ ] Criação de workbooks funcionando
- [ ] Limites de plano sendo respeitados
- [ ] Rate limiting operacional
- [ ] APIs respondendo normalmente

### **Segurança:**

- [ ] Race conditions corrigidas
- [ ] Cache validation funcionando
- [ ] Logs sanitizados
- [ ] Sem vazamento de informações

### **Performance:**

- [ ] Tempo de resposta < 2s
- [ ] Sem degradação de performance
- [ ] Transações completando em < 5s
- [ ] Cache hit rate > 80%

### **Monitoramento:**

- [ ] Logs de segurança ativos
- [ ] Alertas configurados
- [ ] Dashboard funcionando
- [ ] Métricas sendo coletadas

---

## 🎯 **CONCLUSÃO**

Após seguir todas as etapas, os patches de segurança críticos estarão implementados e o sistema estará protegido contra as vulnerabilidades identificadas na auditoria.

**Tempo Total Estimado:** 3-4 horas  
**Janela de Manutenção:** 45 minutos  
**Monitoramento:** 24 horas contínuas

**Status:** ✅ **PRONTO PARA IMPLEMENTAÇÃO**
