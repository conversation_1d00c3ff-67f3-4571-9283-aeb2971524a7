'use client';

import { useState, useEffect } from 'react';

// Interface para representar a versão legada da MediaQueryList
interface LegacyMediaQueryList extends MediaQueryList {
  addListener(listener: (event: MediaQueryListEvent) => void): void;
  removeListener(listener: (event: MediaQueryListEvent) => void): void;
}

/**
 * Hook para detectar se um media query corresponde ao estado atual da janela
 * @param query String contendo o media query a ser monitorado (ex: '(prefers-reduced-motion: reduce)')
 * @returns boolean indicando se o media query corresponde
 */
export function useMediaQuery(query: string): boolean {
  // Inicializar com false para SSR
  const [matches, setMatches] = useState<boolean>(false);
  const [mounted, setMounted] = useState<boolean>(false);

  useEffect(() => {
    // Marcar como montado
    setMounted(true);

    // Verificar se window está definido para SSR safety
    if (typeof window !== 'undefined') {
      // Criar MediaQueryList
      const mediaQuery = window.matchMedia(query);

      // Definir o estado inicial
      setMatches(mediaQuery.matches);

      // Função para atualizar o estado quando a media query mudar
      const updateMatches = (e: MediaQueryListEvent): void => {
        setMatches(e.matches);
      };

      // Adicionar listener
      try {
        // Abordagem moderna (mais recente)
        mediaQuery.addEventListener('change', updateMatches);
      } catch {
        // Fallback para navegadores mais antigos
        try {
          // Para suporte a navegadores mais antigos
          (mediaQuery as LegacyMediaQueryList).addListener(updateMatches);
        } catch (innerError) {
          console.error('Falha ao adicionar media query listener:', innerError);
        }
      }

      // Remover listener na limpeza
      return () => {
        try {
          mediaQuery.removeEventListener('change', updateMatches);
        } catch {
          try {
            // Para suporte a navegadores mais antigos
            (mediaQuery as LegacyMediaQueryList).removeListener(updateMatches);
          } catch (innerError) {
            console.error('Falha ao remover media query listener:', innerError);
          }
        }
      };
    }

    // Se window não existe, não faz nada
    return undefined;
  }, [query]);

  // Durante o SSR ou antes da montagem, retornar false
  return mounted ? matches : false;
}

export default useMediaQuery;
