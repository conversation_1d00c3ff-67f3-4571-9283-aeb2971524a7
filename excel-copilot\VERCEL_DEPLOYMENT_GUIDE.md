# Vercel Deployment Guide for Excel Copilot

This guide provides step-by-step instructions for deploying the Excel Copilot application to Vercel after applying the fixes for deployment issues.

## Prerequisites

- Vercel account with access to the Excel Copilot project
- Git credentials with push access to the repository
- Access to the project's database credentials

## Deployment Steps

### 1. Apply Fixes Locally

First, ensure all fixes have been applied to your local environment:

```bash
# Run all fix scripts to ensure the application is ready for deployment
npm run fix:all-errors

# Verify the build works locally
npm run build
```

### 2. Update Environment Variables in Vercel

1. Log in to your Vercel account
2. Go to the Excel Copilot project
3. Navigate to "Settings" > "Environment Variables"
4. Update the `DATABASE_URL` variable with proper formatting:
   ```
   DATABASE_URL="postgresql://postgres:<EMAIL>:5432/postgres"
   ```
   (Replace PASSWORD with the actual password)
5. Make sure other environment variables are correctly set:
   - `NEXTAUTH_SECRET`
   - `NEXTAUTH_URL`
   - `VERTEX_AI_ENABLED`
   - `VERTEX_AI_PROJECT_ID`
   - `VERTEX_AI_LOCATION`
   - `VERTEX_AI_MODEL_NAME`

### 3. Commit and Push Changes

Commit all the fixes to your repository:

```bash
git add .
git commit -m "Fix: Applied deployment fixes for Vercel"
git push
```

### 4. Deploy to Vercel

Use the deploy-vercel script to deploy the application:

```bash
npm run deploy-vercel
```

This will trigger a new deployment on Vercel with all the fixes applied.

### 5. Verify Deployment

After deployment completes:

1. Visit the application URL: https://excel-copilot-eight.vercel.app
2. Log in using Google or GitHub authentication
3. Create a new workbook or open an existing one
4. Test Excel file manipulation features
5. Test AI interaction features
6. Verify that all features are working correctly

### 6. Monitor for Issues

After deployment, monitor the application for any issues:

1. Check Vercel logs for any errors
2. Test all critical user flows
3. Monitor database connectivity
4. Test on different browsers and devices

## Troubleshooting

If you encounter issues during deployment:

1. **Database Connection Errors**

   - Check if the `DATABASE_URL` includes quotes and has the correct password
   - Verify that the database is accessible from Vercel's servers

2. **Build Failures**

   - Check the build logs in Vercel
   - Verify that all dependencies are properly installed
   - Make sure the `next.config.js` has the correct configuration

3. **Authentication Issues**

   - Verify that all OAuth credentials are properly configured
   - Check that `NEXTAUTH_URL` points to the correct domain

4. **Timeout Issues**
   - If you still encounter timeouts, consider increasing the `staticPageGenerationTimeout` further
   - Split large pages into smaller components

## Additional Resources

- [Vercel Documentation](https://vercel.com/docs)
- [Next.js Deployment Guide](https://nextjs.org/docs/deployment)
- [NextAuth.js Configuration](https://next-auth.js.org/configuration/options)
- [Prisma with Vercel](https://www.prisma.io/docs/guides/deployment/deployment-guides/deploying-to-vercel)
