'use client';

import * as React from 'react';

import {
  getFormFieldClasses,
  renderWithWrapper,
  type FormFieldWrapperProps,
  type FormFieldVariant,
  type FormFieldSize,
} from './form-field-styles';

export interface TextareaProps
  extends React.TextareaHTMLAttributes<HTMLTextAreaElement>,
    FormFieldWrapperProps {
  variant?: FormFieldVariant;
  textareaSize?: FormFieldSize;
}

const Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(
  (
    { className, wrapperClassName, variant = 'default', fieldSize = 'md', textareaSize, ...props },
    ref
  ) => {
    // Manter compatibilidade com textareaSize (deprecated)
    const size = textareaSize || fieldSize;

    const textareaElement = (
      <textarea
        className={getFormFieldClasses(variant, size, true, className)}
        ref={ref}
        {...props}
      />
    );

    return renderWithWrapper(textareaElement, wrapperClassName);
  }
);

Textarea.displayName = 'Textarea';

// Export both as named and default export
export { Textarea };
export default Textarea;
