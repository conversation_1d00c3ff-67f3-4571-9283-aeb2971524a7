/**
 * 🧪 HEALTH CHECK TEST ENDPOINT
 *
 * GET /api/health/test
 *
 * Endpoint simples para testar o sistema de health checks
 */

import { NextRequest, NextResponse } from 'next/server';

export async function GET(_request: NextRequest) {
  try {
    const timestamp = new Date().toISOString();

    // Teste básico de health check
    const healthResult = {
      status: 'healthy',
      timestamp,
      responseTime: 50,
      service: 'test',
      details: {
        message: 'Health check test endpoint working',
        environment: process.env.NODE_ENV,
        version: '1.0.0',
      },
    };

    return NextResponse.json(healthResult, {
      status: 200,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        Pragma: 'no-cache',
        Expires: '0',
      },
    });
  } catch (error: unknown) {
    return NextResponse.json(
      {
        status: 'unhealthy',
        service: 'test',
        timestamp: new Date().toISOString(),
        responseTime: 0,
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      {
        status: 500,
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
        },
      }
    );
  }
}

// Permitir apenas GET
export async function POST() {
  return NextResponse.json({ error: 'Method not allowed' }, { status: 405 });
}

export async function PUT() {
  return NextResponse.json({ error: 'Method not allowed' }, { status: 405 });
}

export async function DELETE() {
  return NextResponse.json({ error: 'Method not allowed' }, { status: 405 });
}
