import { ExcelOperation } from '@/lib/excel/types';
import { ExcelOperationType } from '@/types';
import { extractGroup } from '@/utils/regex-utils';

/**
 * Interface para configuração de regras de formatação condicional
 */
export interface ConditionalFormatRule {
  // Configurações básicas
  type:
    | 'cellValue'
    | 'colorScale'
    | 'dataBar'
    | 'iconSet'
    | 'topBottom'
    | 'textContains'
    | 'dateOccurring'
    | 'duplicateValues'
    | 'formula';
  range: string;

  // Regras específicas por tipo
  cellValue?: {
    operator:
      | 'equal'
      | 'notEqual'
      | 'greaterThan'
      | 'lessThan'
      | 'greaterThanOrEqual'
      | 'lessThanOrEqual'
      | 'between'
      | 'notBetween';
    values: string[];
    style: FormatStyle;
  };

  colorScale?: {
    min: {
      type: 'min' | 'number' | 'percent' | 'percentile' | 'formula';
      value?: number | string;
      color: string;
    };
    mid?: {
      type: 'mid' | 'number' | 'percent' | 'percentile' | 'formula';
      value?: number | string;
      color: string;
    };
    max: {
      type: 'max' | 'number' | 'percent' | 'percentile' | 'formula';
      value?: number | string;
      color: string;
    };
  };

  dataBar?: {
    min: { type: 'min' | 'number' | 'percent' | 'percentile' | 'formula'; value?: number | string };
    max: { type: 'max' | 'number' | 'percent' | 'percentile' | 'formula'; value?: number | string };
    color: string;
    gradient?: boolean;
    showValue?: boolean;
    negativeBarColor?: string;
    border?: boolean;
    borderColor?: string;
  };

  iconSet?: {
    type:
      | '3Arrows'
      | '3ArrowsGray'
      | '3Flags'
      | '3TrafficLights'
      | '3Signs'
      | '3Symbols'
      | '3Symbols2'
      | '4Arrows'
      | '4ArrowsGray'
      | '4RedToBlack'
      | '4Rating'
      | '4TrafficLights'
      | '5Arrows'
      | '5ArrowsGray'
      | '5Rating'
      | '5Quarters';
    reverse?: boolean;
    showValue?: boolean;
    thresholds: {
      value: number;
      type: 'number' | 'percent' | 'percentile' | 'formula';
    }[];
  };

  topBottom?: {
    type: 'top' | 'bottom';
    value: number;
    isPercent?: boolean;
    style: FormatStyle;
  };

  textContains?: {
    text: string;
    style: FormatStyle;
  };

  dateOccurring?: {
    type:
      | 'today'
      | 'yesterday'
      | 'tomorrow'
      | 'last7Days'
      | 'thisMonth'
      | 'lastMonth'
      | 'nextMonth'
      | 'thisWeek'
      | 'lastWeek'
      | 'nextWeek';
    style: FormatStyle;
  };

  duplicateValues?: {
    type: 'duplicate' | 'unique';
    style: FormatStyle;
  };

  formula?: {
    formula: string;
    style: FormatStyle;
  };
}

/**
 * Interface para formatação visual
 */
export interface FormatStyle {
  fill?: {
    type: 'solid';
    color: string;
  };
  font?: {
    bold?: boolean;
    italic?: boolean;
    underline?: boolean;
    strikethrough?: boolean;
    color?: string;
    size?: number;
  };
  border?: {
    top?: { style: 'thin' | 'medium' | 'thick' | 'dashed' | 'dotted' | 'double'; color: string };
    left?: { style: 'thin' | 'medium' | 'thick' | 'dashed' | 'dotted' | 'double'; color: string };
    bottom?: { style: 'thin' | 'medium' | 'thick' | 'dashed' | 'dotted' | 'double'; color: string };
    right?: { style: 'thin' | 'medium' | 'thick' | 'dashed' | 'dotted' | 'double'; color: string };
  };
}

/**
 * Extrai operações de formatação condicional a partir do texto de comando
 */
export function extractConditionalFormattingOperations(text: string): ExcelOperation[] {
  const operations: ExcelOperation[] = [];

  // Padrões de comandos para formatação condicional
  const patterns = [
    // Padrão para formatação condicional básica de valor
    {
      regex:
        /(?:aplique|adicione|crie)\s+(?:formatação|formato)\s+condicional\s+(?:n[ao]|para)(?:\s+intervalo|range|coluna[s]?|célula[s]?)?\s+([A-Z0-9:]+|\[.+?\]|coluna\s+\w+)(?:\s+onde|quando)(?:\s+(?:os?|as?))?\s+(?:valor(?:es)?|célula[s]?)\s+(?:for(?:em)?|estiver(?:em)?|seja[m]?)\s+(maior(?:\s+que)?|menor(?:\s+que)?|igual(?:\s+a)?|maior\s+ou\s+igual(?:\s+a)?|menor\s+ou\s+igual(?:\s+a)?|diferente(?:\s+de)?|entre)\s+(?:a|de)?\s+(.+?)(?:\s+com\s+(?:cor|estilo|formato|fundo)\s+(.+))?$/i,
      handler: (matches: RegExpMatchArray): ExcelOperation | null => {
        const range = matches[1]?.trim();
        const operator = matches[2]?.toLowerCase();
        const valueText = matches[3]?.trim();
        const styleText = matches[4]?.trim();

        if (!range || !operator || !valueText) {
          return null;
        }

        // Mapear operadores em português para inglês
        const operatorMap: Record<string, string> = {
          'maior que': 'greaterThan',
          maior: 'greaterThan',
          'menor que': 'lessThan',
          menor: 'lessThan',
          'igual a': 'equal',
          igual: 'equal',
          'maior ou igual a': 'greaterThanOrEqual',
          'maior ou igual': 'greaterThanOrEqual',
          'menor ou igual a': 'lessThanOrEqual',
          'menor ou igual': 'lessThanOrEqual',
          'diferente de': 'notEqual',
          diferente: 'notEqual',
          entre: 'between',
        };

        // Extrair valores
        let values: string[] = [];
        if (operator === 'entre') {
          // Para operador 'entre', precisamos de dois valores
          const betweenValues = valueText.split(/\s+e\s+/);
          if (betweenValues.length === 2) {
            values = betweenValues;
          } else {
            return null;
          }
        } else {
          values = [valueText];
        }

        // Extrair estilo
        const style: FormatStyle = {};
        if (styleText) {
          // Mapear cores comuns
          const colorMap: Record<string, string> = {
            vermelho: '#FF0000',
            verde: '#00FF00',
            azul: '#0000FF',
            amarelo: '#FFFF00',
            laranja: '#FFA500',
            roxo: '#800080',
            rosa: '#FFC0CB',
            cinza: '#808080',
            preto: '#000000',
            branco: '#FFFFFF',
          };

          const lowerStyleText = styleText.toLowerCase();

          // Verificar se há cor especificada
          for (const [colorName, colorValue] of Object.entries(colorMap)) {
            if (lowerStyleText.includes(colorName)) {
              style.fill = {
                type: 'solid',
                color: colorValue,
              };

              // Verificar se devemos aplicar a cor ao texto também
              if (lowerStyleText.includes('texto') || lowerStyleText.includes('fonte')) {
                style.font = {
                  color: colorValue,
                };
              }

              break;
            }
          }

          // Verificar formatação de texto
          if (lowerStyleText.includes('negrito')) {
            style.font = {
              ...style.font,
              bold: true,
            };
          }

          if (lowerStyleText.includes('itálico') || lowerStyleText.includes('italico')) {
            style.font = {
              ...style.font,
              italic: true,
            };
          }

          if (lowerStyleText.includes('sublinhado')) {
            style.font = {
              ...style.font,
              underline: true,
            };
          }
        }

        // Se nenhum estilo foi especificado, usar um padrão
        if (!style.fill && !style.font) {
          style.fill = {
            type: 'solid',
            color: '#FFEB9C', // Amarelo claro padrão
          };
        }

        return {
          type: ExcelOperationType.CONDITIONAL_FORMAT,
          data: {
            type: 'cellValue',
            range,
            cellValue: {
              operator: operatorMap[operator] as any,
              values,
              style,
            },
          },
        };
      },
    },

    // Padrão para escala de cores
    {
      regex:
        /(?:aplique|adicione|crie)\s+(?:uma)?\s+escala\s+de\s+cores\s+(?:n[ao]|para)(?:\s+intervalo|range|coluna[s]?|célula[s]?)?\s+([A-Z0-9:]+|\[.+?\]|coluna\s+\w+)(?:\s+de\s+(.+?)\s+(?:até|para)\s+(.+?))?(?:\s+com\s+(?:valor\s+)?(mínimo|minimo|menor|baixo)\s+(?:em|como|na cor)\s+(.+?)(?:\s+e\s+(?:valor\s+)?(máximo|maximo|maior|alto)\s+(?:em|como|na cor)\s+(.+?))?)?$/i,
      handler: (matches: RegExpMatchArray): ExcelOperation | null => {
        const range = matches[1]?.trim();

        if (!range) {
          return null;
        }

        // Cores padrão se não especificadas
        let minColor = '#FF8080'; // Vermelho claro
        let maxColor = '#80FF80'; // Verde claro

        // Verificar se cores foram especificadas
        if (matches[5]) {
          const minColorText = matches[5]?.toLowerCase().trim();
          const colorMap: Record<string, string> = {
            vermelho: '#FF0000',
            verde: '#00FF00',
            azul: '#0000FF',
            amarelo: '#FFFF00',
            laranja: '#FFA500',
            roxo: '#800080',
            rosa: '#FFC0CB',
            cinza: '#808080',
            preto: '#000000',
            branco: '#FFFFFF',
          };

          minColor = colorMap[minColorText] || minColor;
        }

        if (matches[7]) {
          const maxColorText = matches[7]?.toLowerCase().trim();
          const colorMap: Record<string, string> = {
            vermelho: '#FF0000',
            verde: '#00FF00',
            azul: '#0000FF',
            amarelo: '#FFFF00',
            laranja: '#FFA500',
            roxo: '#800080',
            rosa: '#FFC0CB',
            cinza: '#808080',
            preto: '#000000',
            branco: '#FFFFFF',
          };

          maxColor = colorMap[maxColorText] || maxColor;
        }

        return {
          type: ExcelOperationType.CONDITIONAL_FORMAT,
          data: {
            type: 'colorScale',
            range,
            colorScale: {
              min: { type: 'min', color: minColor },
              max: { type: 'max', color: maxColor },
            },
          },
        };
      },
    },

    // Padrão para barras de dados
    {
      regex:
        /(?:aplique|adicione|crie)\s+(?:uma)?\s+barra(?:s)?\s+de\s+dados\s+(?:n[ao]|para)(?:\s+intervalo|range|coluna[s]?|célula[s]?)?\s+([A-Z0-9:]+|\[.+?\]|coluna\s+\w+)(?:\s+(?:em|na|com|de)\s+cor\s+(.+?))?(?:\s+(?:com|e)\s+(?:borda|borda)\s+(.+?))?(?:\s+(?:gradient(?:e)?|degradê))?/i,
      handler: (matches: RegExpMatchArray): ExcelOperation | null => {
        const range = matches[1]?.trim();
        const colorText = matches[2]?.toLowerCase().trim();
        const borderText = matches[3]?.toLowerCase().trim();
        const hasGradient =
          matches[0]?.toLowerCase().includes('gradient') ||
          matches[0]?.toLowerCase().includes('degradê');

        if (!range) {
          return null;
        }

        // Cor padrão se não especificada
        let color = '#638EC6'; // Azul padrão do Excel

        // Verificar se cor foi especificada
        if (colorText) {
          const colorMap: Record<string, string> = {
            vermelho: '#FF0000',
            verde: '#00FF00',
            azul: '#0000FF',
            amarelo: '#FFFF00',
            laranja: '#FFA500',
            roxo: '#800080',
            rosa: '#FFC0CB',
            cinza: '#808080',
            preto: '#000000',
            branco: '#FFFFFF',
          };

          color = colorMap[colorText] || color;
        }

        // Configuração de borda
        let border = false;
        let borderColor = '#000000';

        if (borderText) {
          border = true;
          const colorMap: Record<string, string> = {
            vermelho: '#FF0000',
            verde: '#00FF00',
            azul: '#0000FF',
            amarelo: '#FFFF00',
            laranja: '#FFA500',
            roxo: '#800080',
            rosa: '#FFC0CB',
            cinza: '#808080',
            preto: '#000000',
            branco: '#FFFFFF',
          };

          borderColor = colorMap[borderText] || borderColor;
        }

        return {
          type: ExcelOperationType.CONDITIONAL_FORMAT,
          data: {
            type: 'dataBar',
            range,
            dataBar: {
              min: { type: 'min' },
              max: { type: 'max' },
              color,
              gradient: hasGradient !== false,
              showValue: true,
              border,
              borderColor,
            },
          },
        };
      },
    },

    // Padrão para conjunto de ícones
    {
      regex:
        /(?:aplique|adicione|crie)\s+(?:um)?\s+conjunto\s+de\s+ícones\s+(?:n[ao]|para)(?:\s+intervalo|range|coluna[s]?|célula[s]?)?\s+([A-Z0-9:]+|\[.+?\]|coluna\s+\w+)(?:\s+(?:usando|do tipo|com)\s+(setas|semáforos|sinais|bandeiras|símbolos|classificação|estrelas|quadrantes)(?:\s+(\d+))?)?(?:\s+(?:invertido|reverso))?/i,
      handler: (matches: RegExpMatchArray): ExcelOperation | null => {
        const range = matches[1]?.trim();
        const iconTypeText = matches[2]?.toLowerCase().trim();
        const countText = matches[3]?.trim();
        const isReversed =
          matches[0]?.toLowerCase().includes('invertido') ||
          matches[0]?.toLowerCase().includes('reverso');

        if (!range) {
          return null;
        }

        // Mapear tipos de ícones
        const iconTypeMap: Record<string, string> = {
          setas: 'Arrows',
          semáforos: 'TrafficLights',
          sinais: 'Signs',
          bandeiras: 'Flags',
          símbolos: 'Symbols',
          classificação: 'Rating',
          estrelas: 'Rating',
          quadrantes: 'Quarters',
        };

        // Determinar o tipo de ícone
        let iconSetType = '3TrafficLights'; // Padrão

        if (iconTypeText) {
          const baseType = iconTypeMap[iconTypeText] || 'TrafficLights';
          const count = countText ? parseInt(countText, 10) : 3;

          // Validar contagem
          const validCount = [3, 4, 5].includes(count) ? count : 3;

          iconSetType = `${validCount}${baseType}`;
        }

        // Configurar thresholds padrão
        const thresholds = [];

        if (iconSetType.startsWith('3')) {
          thresholds.push({ value: 67, type: 'percent' });
          thresholds.push({ value: 33, type: 'percent' });
        } else if (iconSetType.startsWith('4')) {
          thresholds.push({ value: 75, type: 'percent' });
          thresholds.push({ value: 50, type: 'percent' });
          thresholds.push({ value: 25, type: 'percent' });
        } else if (iconSetType.startsWith('5')) {
          thresholds.push({ value: 80, type: 'percent' });
          thresholds.push({ value: 60, type: 'percent' });
          thresholds.push({ value: 40, type: 'percent' });
          thresholds.push({ value: 20, type: 'percent' });
        }

        return {
          type: ExcelOperationType.CONDITIONAL_FORMAT,
          data: {
            type: 'iconSet',
            range,
            iconSet: {
              type: iconSetType as any,
              reverse: isReversed,
              showValue: true,
              thresholds,
            },
          },
        };
      },
    },

    // Padrão para top/bottom N valores
    {
      regex:
        /(?:destaque|realce|marque)\s+(?:os|as)?\s+(\d+)(?:\s+por\s+cento|\s*%)?\s+(?:valores|células)?\s+(maiores|melhores|top|superiores|menores|piores|bottom|inferiores)(?:\s+(?:valores|células))?(?:\s+(?:n[ao]|d[ao]|em)(?:\s+intervalo|range|coluna[s]?|célula[s]?)?\s+([A-Z0-9:]+|\[.+?\]|coluna\s+\w+))?(?:\s+(?:com|em|na)\s+cor\s+(.+?))?/i,
      handler: (matches: RegExpMatchArray): ExcelOperation | null => {
        const valueText = extractGroup(matches, 1);
        const topBottomText = extractGroup(matches, 2, '');
        const range = extractGroup(matches, 3);
        const colorText = extractGroup(matches, 4, '').toLowerCase();

        // Verificar tipo (top ou bottom)
        const isTop =
          topBottomText.includes('top') ||
          topBottomText.includes('melhores') ||
          topBottomText.includes('maiores') ||
          topBottomText.includes('superiores');

        const isPercent =
          matches[0]?.toLowerCase().includes('por cento') ||
          matches[0]?.toLowerCase().includes('%');

        if (!valueText || !range) {
          return null;
        }

        const value = parseInt(valueText, 10);

        // Estilo padrão se não especificado
        const style: FormatStyle = {
          fill: {
            type: 'solid',
            color: isTop ? '#C6EFCE' : '#FFC7CE', // Verde claro para top, vermelho claro para bottom
          },
        };

        // Verificar se cor foi especificada
        if (colorText) {
          const colorMap: Record<string, string> = {
            vermelho: '#FF0000',
            verde: '#00FF00',
            azul: '#0000FF',
            amarelo: '#FFFF00',
            laranja: '#FFA500',
            roxo: '#800080',
            rosa: '#FFC0CB',
            cinza: '#808080',
            preto: '#000000',
            branco: '#FFFFFF',
          };

          style.fill = {
            type: 'solid',
            color: colorMap[colorText] || (style.fill?.color ?? '#FFEB9C'),
          };
        }

        return {
          type: ExcelOperationType.CONDITIONAL_FORMAT,
          data: {
            type: 'topBottom',
            range,
            topBottom: {
              type: isTop ? 'top' : 'bottom',
              value,
              isPercent,
              style,
            },
          },
        };
      },
    },

    // Padrão para destacar texto que contém
    {
      regex:
        /(?:destaque|realce|marque)(?:\s+as)?\s+células\s+(?:que\s+)?(?:contenham|contêm|com|contendo)\s+(?:o texto|a palavra|o termo)\s+(?:"(.+?)"|'(.+?)'|(\w+))(?:\s+(?:n[ao]|d[ao]|em)(?:\s+intervalo|range|coluna[s]?|célula[s]?)?\s+([A-Z0-9:]+|\[.+?\]|coluna\s+\w+))?(?:\s+(?:com|em|na)\s+cor\s+(.+?))?/i,
      handler: (matches: RegExpMatchArray): ExcelOperation | null => {
        const text = matches[1] || matches[2] || matches[3];
        const range = matches[4]?.trim();
        const colorText = matches[5]?.toLowerCase().trim();

        if (!text || !range) {
          return null;
        }

        // Estilo padrão se não especificado
        const style: FormatStyle = {
          fill: {
            type: 'solid',
            color: '#FFEB9C', // Amarelo claro
          },
        };

        // Verificar se cor foi especificada
        if (colorText) {
          const colorMap: Record<string, string> = {
            vermelho: '#FF0000',
            verde: '#00FF00',
            azul: '#0000FF',
            amarelo: '#FFFF00',
            laranja: '#FFA500',
            roxo: '#800080',
            rosa: '#FFC0CB',
            cinza: '#808080',
            preto: '#000000',
            branco: '#FFFFFF',
          };

          style.fill = {
            type: 'solid',
            color: colorMap[colorText] || (style.fill?.color ?? '#FFEB9C'),
          };
        }

        return {
          type: ExcelOperationType.CONDITIONAL_FORMAT,
          data: {
            type: 'textContains',
            range,
            textContains: {
              text,
              style,
            },
          },
        };
      },
    },

    // Padrão para destacar valores duplicados
    {
      regex:
        /(?:destaque|realce|marque)(?:\s+os)?\s+(?:valores|células)?\s+(duplicados|únicos|unicos|repetidos)(?:\s+(?:n[ao]|d[ao]|em)(?:\s+intervalo|range|coluna[s]?|célula[s]?)?\s+([A-Z0-9:]+|\[.+?\]|coluna\s+\w+))?(?:\s+(?:com|em|na)\s+cor\s+(.+?))?/i,
      handler: (matches: RegExpMatchArray): ExcelOperation | null => {
        const typeText = extractGroup(matches, 1, '');
        const range = extractGroup(matches, 2);
        const colorText = extractGroup(matches, 3, '').toLowerCase();

        if (!range) {
          return null;
        }

        const isDuplicate = typeText.includes('duplicado') || typeText.includes('repetido');

        // Estilo padrão se não especificado
        const style: FormatStyle = {
          fill: {
            type: 'solid',
            color: isDuplicate ? '#FFC7CE' : '#C6EFCE', // Vermelho claro para duplicados, verde claro para únicos
          },
        };

        // Verificar se cor foi especificada
        if (colorText) {
          const colorMap: Record<string, string> = {
            vermelho: '#FF0000',
            verde: '#00FF00',
            azul: '#0000FF',
            amarelo: '#FFFF00',
            laranja: '#FFA500',
            roxo: '#800080',
            rosa: '#FFC0CB',
            cinza: '#808080',
            preto: '#000000',
            branco: '#FFFFFF',
          };

          style.fill = {
            type: 'solid',
            color: colorMap[colorText] || (style.fill?.color ?? '#FFEB9C'),
          };
        }

        return {
          type: ExcelOperationType.CONDITIONAL_FORMAT,
          data: {
            type: 'duplicateValues',
            range,
            duplicateValues: {
              type: isDuplicate ? 'duplicate' : 'unique',
              style,
            },
          },
        };
      },
    },

    // Padrão para fórmula personalizada
    {
      regex:
        /(?:aplique|adicione|crie)\s+(?:formatação|formato)\s+condicional\s+(?:com|usando)\s+(?:a\s+)?fórmula\s+(?:"(.+?)"|'(.+?)'|(\S+.+?\S+))(?:\s+(?:n[ao]|para)(?:\s+intervalo|range|coluna[s]?|célula[s]?)?\s+([A-Z0-9:]+|\[.+?\]|coluna\s+\w+))?(?:\s+(?:com|em|na)\s+cor\s+(.+?))?/i,
      handler: (matches: RegExpMatchArray): ExcelOperation | null => {
        const formula = matches[1] || matches[2] || matches[3];
        const range = matches[4]?.trim();
        const colorText = matches[5]?.toLowerCase().trim();

        if (!formula || !range) {
          return null;
        }

        // Estilo padrão se não especificado
        const style: FormatStyle = {
          fill: {
            type: 'solid',
            color: '#FFEB9C', // Amarelo claro
          },
        };

        // Verificar se cor foi especificada
        if (colorText) {
          const colorMap: Record<string, string> = {
            vermelho: '#FF0000',
            verde: '#00FF00',
            azul: '#0000FF',
            amarelo: '#FFFF00',
            laranja: '#FFA500',
            roxo: '#800080',
            rosa: '#FFC0CB',
            cinza: '#808080',
            preto: '#000000',
            branco: '#FFFFFF',
          };

          style.fill = {
            type: 'solid',
            color: colorMap[colorText] || (style.fill?.color ?? '#FFEB9C'),
          };
        }

        return {
          type: ExcelOperationType.CONDITIONAL_FORMAT,
          data: {
            type: ExcelOperationType.FORMULA,
            range,
            formula: {
              formula,
              style,
            },
          },
        };
      },
    },
  ];

  // Testar cada padrão
  for (const { regex, handler } of patterns) {
    const matches = text.match(regex);
    if (matches) {
      const operation = handler(matches);
      if (operation) {
        operations.push(operation);
      }
    }
  }

  return operations;
}

/**
 * Executa operação de formatação condicional
 */
export async function executeConditionalFormattingOperation(
  sheetData: any,
  operation: ExcelOperation
): Promise<{ updatedData: any; resultSummary: string }> {
  try {
    const { type, range } = operation.data;

    // Validar dados de entrada
    if (!range) {
      return {
        updatedData: sheetData,
        resultSummary: 'Erro: Intervalo não especificado para a formatação condicional.',
      };
    }

    // Criar estrutura para armazenar formatações condicionais
    const formatRecord = {
      type,
      range,
      ...operation.data,
    };

    // Atualizar dados da planilha
    const updatedData = {
      ...sheetData,
      conditionalFormats: [...(sheetData.conditionalFormats || []), formatRecord],
    };

    // Mensagem com base no tipo de formatação
    let formatTypeDescription = '';
    let tb;
    switch (type) {
      case 'cellValue':
        formatTypeDescription = `valores de célula ${operation.data.cellValue?.operator}`;
        break;
      case 'colorScale':
        formatTypeDescription = 'escala de cores';
        break;
      case 'dataBar':
        formatTypeDescription = 'barras de dados';
        break;
      case 'iconSet':
        formatTypeDescription = `conjunto de ícones ${operation.data.iconSet?.type}`;
        break;
      case 'topBottom':
        tb = operation.data.topBottom;
        formatTypeDescription = `${tb?.type === 'top' ? 'maiores' : 'menores'} ${tb?.value} ${tb?.isPercent ? '%' : 'valores'}`;
        break;
      case 'textContains':
        formatTypeDescription = `células contendo "${operation.data.textContains?.text}"`;
        break;
      case 'duplicateValues':
        formatTypeDescription = `valores ${operation.data.duplicateValues?.type === 'duplicate' ? 'duplicados' : 'únicos'}`;
        break;
      case ExcelOperationType.FORMULA:
        formatTypeDescription = `fórmula personalizada`;
        break;
      default:
        formatTypeDescription = 'regra personalizada';
    }

    return {
      updatedData,
      resultSummary: `Formatação condicional aplicada com sucesso: ${formatTypeDescription} no intervalo ${range}.`,
    };
  } catch (error) {
    return {
      updatedData: sheetData,
      resultSummary: `Erro ao aplicar formatação condicional: ${error instanceof Error ? error.message : String(error)}`,
    };
  }
}
