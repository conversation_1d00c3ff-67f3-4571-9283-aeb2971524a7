import { extractGroup } from '@/utils/regex-utils';

import { ExcelOperationType } from '../../types/index';
import { ExcelOperation } from '../excel/types';

/**
 * Interface para dados de operação de célula
 */
export interface CellOperationData {
  cell: string; // Referência da célula (ex: "A1")
  value: any; // Valor a ser definido
  formula?: boolean; // Se o valor é uma fórmula
}

/**
 * Executa uma operação de atualização de célula
 * @param sheetData Dados da planilha
 * @param operation Operação a ser executada
 * @returns Dados atualizados e resumo da operação
 */
export async function executeCellOperation(
  sheetData: any,
  operation: ExcelOperation
): Promise<{ updatedData: any; resultSummary: string }> {
  try {
    const { cell, value, formula = false } = operation.data as CellOperationData;

    if (!cell) {
      throw new Error('Parâmetros insuficientes para operação de célula');
    }

    // Clonar dados para não modificar o original diretamente
    const updatedData = { ...sheetData };

    // Garantir que temos uma estrutura de linhas
    if (!updatedData.rows) {
      updatedData.rows = [];
    }

    // Converter referência de célula para índices de linha e coluna
    const { row, col } = parseCellReference(cell);

    // Garantir que a linha existe
    while (updatedData.rows.length < row) {
      updatedData.rows.push([]);
    }

    // Garantir que a linha tenha colunas suficientes
    if (!updatedData.rows[row - 1]) {
      updatedData.rows[row - 1] = [];
    }

    while (updatedData.rows[row - 1].length < col) {
      updatedData.rows[row - 1].push(null);
    }

    // Atualizar o valor da célula
    updatedData.rows[row - 1][col - 1] = value;

    // Se for uma fórmula, armazenar em uma estrutura separada para processamento posterior
    if (formula) {
      if (!updatedData.formulas) {
        updatedData.formulas = {};
      }
      updatedData.formulas[cell] = value;
    }

    // Registrar histórico de alterações
    if (!updatedData.history) {
      updatedData.history = [];
    }
    updatedData.history.push({
      type: ExcelOperationType.CELL_UPDATE,
      cell,
      value,
      timestamp: Date.now(),
    });

    // Resumo da operação
    const resultSummary = `Célula ${cell} atualizada para ${value}`;

    return { updatedData, resultSummary };
  } catch (error) {
    console.error('Erro ao executar operação de célula:', error);
    throw new Error(
      `Falha ao atualizar célula: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
    );
  }
}

/**
 * Converte referência de célula (ex: "A1") para índices de linha e coluna
 * @param cellRef Referência de célula (ex: "A1")
 * @returns Índices de linha e coluna (1-indexed)
 */
function parseCellReference(cellRef: string): { row: number; col: number } {
  // Extrair parte alfabética (coluna) e numérica (linha)
  const match = cellRef.match(/([A-Za-z]+)([0-9]+)/);
  if (!match) {
    throw new Error(`Referência de célula inválida: ${cellRef}`);
  }

  const colStr = extractGroup(match, 1, '');
  const rowStr = extractGroup(match, 2, '');

  if (!colStr || !rowStr) {
    throw new Error(`Referência de célula inválida: ${cellRef}`);
  }

  // Converter coluna de alfabética para numérica (A=1, B=2, ...)
  let colNum = 0;
  for (let i = 0; i < colStr.length; i++) {
    colNum = colNum * 26 + (colStr.charCodeAt(i) - 64);
  }

  // Converter linha para número
  const rowNum = parseInt(rowStr, 10);

  if (isNaN(rowNum) || rowNum <= 0) {
    throw new Error(`Número de linha inválido: ${rowStr}`);
  }

  return { row: rowNum, col: colNum };
}

/**
 * Extrai operações de célula do texto da resposta de IA
 * @param response Resposta da IA
 * @returns Array de operações de célula
 */
export function extractCellOperations(response: string): ExcelOperation[] {
  const operations: ExcelOperation[] = [];

  // Verificar padrões de atualização de células estruturados
  const cellPattern =
    /OPERAÇÃO:\s*CÉLULA[\s\S]*?CÉLULA:\s*([^\n]+)[\s\S]*?VALOR:\s*([^\n]+)(?:[\s\S]*?FÓRMULA:\s*([^\n]+))?/gi;

  let match;
  while ((match = cellPattern.exec(response)) !== null) {
    const cell = extractGroup(match, 1);
    const value = extractGroup(match, 2);
    const formulaStr = extractGroup(match, 3);
    const isFormula = formulaStr.toLowerCase() === 'sim' || formulaStr.toLowerCase() === 'true';

    if (cell && value !== undefined) {
      // Converter valor para tipo apropriado
      const parsedValue = parseValueToType(value);

      const operation: ExcelOperation = {
        type: ExcelOperationType.CELL_UPDATE,
        data: {
          cell,
          value: parsedValue,
          formula: isFormula,
        },
      };

      operations.push(operation);
    }
  }

  // Também verificar padrões em linguagem natural
  const languagePatterns = [
    // "Defina a célula A1 com o valor 100"
    /(?:defin(?:a|ir)|atualiz(?:a|ar)|coloc(?:a|ar)|muda(?:r)?|alter(?:a|ar)|p[ôo][er])\s+(?:a\s+)?c[ée]lula\s+([A-Za-z][0-9]+)\s+(?:com|para|como)\s+(?:o\s+)?valor\s+(?:de\s+)?([^,;\s]+)/gi,

    // "Insira 150 na célula B3"
    /(?:insir(?:a|ir)|digit(?:a|ar)|entr(?:a|ar)|escrev(?:a|er))\s+([^,;\s]+)\s+n(?:a|o)\s+c[ée]lula\s+([A-Za-z][0-9]+)/gi,

    // "Célula C5: 200"
    /c[ée]lula\s+([A-Za-z][0-9]+)(?:\s*:|\s*=|\s+com|\s+recebe)\s+([^,;\n]+)/gi,

    // "Fórmula =SUM(A1:A10) na célula D5"
    /f[óo]rmula\s+([^,;\n]+)\s+n(?:a|o)\s+c[ée]lula\s+([A-Za-z][0-9]+)/gi,
  ];

  // Processar padrões em linguagem natural
  for (const pattern of languagePatterns) {
    let match;
    while ((match = pattern.exec(response)) !== null) {
      let cell,
        value,
        isFormula = false;

      // Extrair valores dependendo do padrão
      if (pattern.source.includes('f[óo]rmula')) {
        // Padrão de fórmula
        value = extractGroup(match, 1);
        cell = extractGroup(match, 2);
        isFormula = true;
      } else if (pattern.source.includes('n(?:a|o)\\s+c[ée]lula')) {
        // Padrão "Insira X na célula Y"
        value = extractGroup(match, 1);
        cell = extractGroup(match, 2);
      } else {
        // Outros padrões
        cell = extractGroup(match, 1);
        value = extractGroup(match, 2);
      }

      // Identificar se o valor é uma fórmula (começa com =)
      if (!isFormula && value && value.startsWith('=')) {
        isFormula = true;
      }

      if (cell && value !== undefined) {
        // Converter valor para tipo apropriado se não for fórmula
        const parsedValue = isFormula ? value : parseValueToType(value);

        const operation: ExcelOperation = {
          type: ExcelOperationType.CELL_UPDATE,
          data: {
            cell,
            value: parsedValue,
            formula: isFormula,
          },
        };

        operations.push(operation);
      }
    }
  }

  return operations;
}

/**
 * Tenta converter um valor string para o tipo apropriado (número, booleano, etc)
 * @param value Valor em string
 * @returns Valor convertido para o tipo adequado
 */
function parseValueToType(value: string): any {
  if (!value) return '';

  // Verificar se é número
  const numValue = Number(value.replace(',', '.'));
  if (!isNaN(numValue)) {
    return numValue;
  }

  // Verificar se é booleano
  if (['true', 'verdadeiro', 'sim', 'yes'].includes(value.toLowerCase())) {
    return true;
  }

  if (['false', 'falso', 'não', 'nao', 'no'].includes(value.toLowerCase())) {
    return false;
  }

  // Se não for número nem booleano, retornar string
  return value;
}
