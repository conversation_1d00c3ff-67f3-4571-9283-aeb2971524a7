/**
 * Extensões de tipos para serviços e componentes relacionados
 */

// Interface de extensão para Service
export interface Service {
  initialized?: boolean;
}

// Interface para detalhes de serviço (usado pelo AppInitializer)
export interface ServiceDetails {
  criticalServicesReady?: boolean;
  initializedServices?: string[];
  healthChecksComplete?: boolean;
}

// Extensão global do módulo para permitir acesso a initialized
declare global {
  interface Object {
    initialized?: boolean;
  }
}
