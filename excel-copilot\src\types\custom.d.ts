/**
 * Arquivo de declaração de tipos personalizado para o Excel Copilot
 * Este arquivo adiciona propriedades prefixadas com _ às interfaces existentes
 * para permitir o uso de propriedades dinâmicas/experimentais sem erros TypeScript.
 */

// Permitir propriedades com prefixo underscore em qualquer objeto
declare interface Object {
  [key: `_${string}`]: any;
}

// Declarações para estender interfaces específicas

// Para hooks de tema
declare module '@/hooks/use-theme' {
  interface UseThemeProps {
    _theme?: string;
  }
}

// Para componentes UI
declare module '@/components/ui/error-message' {
  interface ErrorStyleProps {
    _iconClassName?: string;
    _icon?: React.ReactNode;
  }
}

declare module '@/components/ui/input' {
  interface InputProps {
    _wrapperClassName?: string;
  }
}

declare module '@/components/ui/textarea' {
  interface TextareaProps {
    _wrapperClassName?: string;
  }
}

declare module '@/components/ui/theme-toggle' {
  interface ThemeToggleProps {
    _variant?: string;
  }
}

// Para Bridge/Desktop
declare module '@/lib/desktop-bridge' {
  interface BridgeState {
    _status?: string;
    _platform?: string;
    _disconnect?: () => void;
  }
}

// Para interfaces de colaboração
declare module '@/lib/collab' {
  interface CollabState {
    _submitCellEdit?: (data: any) => void;
    _userName?: string;
    _userId?: string;
  }
}

// Para ResultSummary e operações que podem retornar never
declare global {
  interface Array<T> {
    // Adiciona suporte para arrays que podem ser de tipo never
    push(...items: any[]): number;
  }
}

// Para interfaces que usam sheetData genérico
declare global {
  interface Record<K extends string | number | symbol, T> {
    charts?: any[];
    name?: string;
  }
}

// Adicionar suporte para propriedades dinâmicas em objetos de schema
declare module '@/lib/security/sanitization' {
  interface JSONSchema {
    type?: string;
    properties?: Record<string, any>;
    required?: string[];
  }
}

// Estender UserData para propriedades usadas em subscription-limits
declare module '@/lib/subscription-limits' {
  interface UserData {
    lastIpAddress?: string;
    createdAt?: Date;
  }
}

// Para dados de resposta de API
declare module '@/lib/desktop-bridge-connector' {
  interface BridgeResponse {
    data: {
      installed?: boolean;
      version?: string;
      comSupported?: boolean;
      name?: string;
    };
  }
}

// Tipos do useAIChat agora são exportados diretamente do hook
// Removidas duplicações para evitar conflitos de tipos

// Para middleware e segurança
declare module '@/lib/security' {
  function hasSuspiciousPatterns(value: any): boolean;
  function deepSanitizeObject(obj: any): any;
}

// Para contexto de locale
declare module '@/lib/locale' {
  interface LocaleContextType {
    _t?: (key: string) => string;
  }
}

// Adicionar interface para AppError
declare module '@/lib/errors' {
  interface AppError extends Error {
    code?: string;
    statusCode?: number;
  }
}
