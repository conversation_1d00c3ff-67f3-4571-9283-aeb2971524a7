import { NextRequest } from 'next/server';

import { optionalAuthMiddleware } from '@/middleware/auth';
import { withMiddleware } from '@/middleware/core';
import { ApiResponse } from '@/utils/api-response';

/**
 * Documentação da API do Excel Copilot
 */

// Configurar rota como dinâmica para permitir uso de headers em runtime
export const dynamic = 'force-dynamic';

const API_DOCS = {
  info: {
    title: 'Excel Copilot API',
    description: 'API para manipulação de planilhas Excel através de linguagem natural',
    version: '1.0.0',
    contact: {
      email: '<EMAIL>',
    },
    license: {
      name: 'Proprietário',
    },
  },
  servers: [
    {
      url: 'https://excel-copilot-eight.vercel.app',
      description: 'Servidor de Produção',
    },
    {
      url: 'http://localhost:3000',
      description: 'Servidor Local de Desenvolvimento',
    },
  ],
  tags: [
    {
      name: 'Auth',
      description: 'Operações de autenticação e autorização',
    },
    {
      name: 'Workbooks',
      description: 'Operações com planilhas',
    },
    {
      name: 'Sheets',
      description: 'Operações com folhas dentro de planilhas',
    },
    {
      name: 'AI',
      description: 'Operações com Inteligência Artificial para manipulação de dados',
    },
    {
      name: 'Excel',
      description: 'Operações específicas do Excel',
    },
    {
      name: 'Usuários',
      description: 'Operações de gerenciamento de usuários',
    },
    {
      name: 'Admin',
      description: 'Operações administrativas (requer permissão)',
    },
  ],
  paths: {
    '/api/auth/login': {
      post: {
        tags: ['Auth'],
        summary: 'Login de usuário',
        description: 'Autentica um usuário e retorna token de acesso',
        requiresAuth: false,
      },
    },
    '/api/auth/register': {
      post: {
        tags: ['Auth'],
        summary: 'Registro de usuário',
        description: 'Registra um novo usuário no sistema',
        requiresAuth: false,
      },
    },
    '/api/workbooks': {
      get: {
        tags: ['Workbooks'],
        summary: 'Listar planilhas',
        description: 'Lista todas as planilhas do usuário',
        requiresAuth: true,
      },
      post: {
        tags: ['Workbooks'],
        summary: 'Criar planilha',
        description: 'Cria uma nova planilha',
        requiresAuth: true,
      },
      patch: {
        tags: ['Workbooks'],
        summary: 'Atualizar planilha',
        description: 'Atualiza informações de uma planilha existente',
        requiresAuth: true,
      },
      delete: {
        tags: ['Workbooks'],
        summary: 'Excluir planilha',
        description: 'Exclui uma planilha existente',
        requiresAuth: true,
      },
    },
    '/api/workbooks/{id}': {
      get: {
        tags: ['Workbooks'],
        summary: 'Obter planilha',
        description: 'Obtém detalhes de uma planilha específica',
        requiresAuth: true,
      },
    },
    '/api/sheets': {
      post: {
        tags: ['Sheets'],
        summary: 'Criar folha',
        description: 'Cria uma nova folha em uma planilha existente',
        requiresAuth: true,
      },
      patch: {
        tags: ['Sheets'],
        summary: 'Atualizar folha',
        description: 'Atualiza dados de uma folha existente',
        requiresAuth: true,
      },
      delete: {
        tags: ['Sheets'],
        summary: 'Excluir folha',
        description: 'Exclui uma folha existente',
        requiresAuth: true,
      },
    },
    '/api/chat': {
      post: {
        tags: ['AI'],
        summary: 'Chat com IA',
        description: 'Envia mensagem para processamento por IA',
        requiresAuth: true,
      },
    },
    '/api/ai/status': {
      get: {
        tags: ['AI'],
        summary: 'Status da IA',
        description: 'Obtém o status dos serviços de IA',
        requiresAuth: false,
      },
    },
    '/api/excel/analyze': {
      post: {
        tags: ['Excel'],
        summary: 'Analisar dados',
        description: 'Analisa dados para obter insights',
        requiresAuth: true,
      },
    },
    '/api/excel/export': {
      post: {
        tags: ['Excel'],
        summary: 'Exportar planilha',
        description: 'Exporta planilha em diversos formatos',
        requiresAuth: true,
      },
    },
    '/api/excel/import': {
      post: {
        tags: ['Excel'],
        summary: 'Importar planilha',
        description: 'Importa planilha de diferentes formatos',
        requiresAuth: true,
      },
    },
    '/api/user/profile': {
      get: {
        tags: ['Usuários'],
        summary: 'Perfil de usuário',
        description: 'Obtém perfil do usuário atual',
        requiresAuth: true,
      },
      patch: {
        tags: ['Usuários'],
        summary: 'Atualizar perfil',
        description: 'Atualiza informações do perfil do usuário',
        requiresAuth: true,
      },
    },
    '/api/user/api-usage': {
      get: {
        tags: ['Usuários'],
        summary: 'Uso da API',
        description: 'Obtém estatísticas de uso da API pelo usuário',
        requiresAuth: true,
      },
    },
    '/api/webhooks/stripe': {
      post: {
        tags: ['Admin'],
        summary: 'Webhook Stripe',
        description: 'Recebe eventos de pagamento do Stripe',
        requiresAuth: false,
      },
    },
    '/api/metrics': {
      get: {
        tags: ['Admin'],
        summary: 'Métricas',
        description: 'Obtém métricas de desempenho da aplicação',
        requiresAuth: true,
        adminOnly: true,
      },
    },
    '/api/health/db': {
      get: {
        tags: ['Admin'],
        summary: 'Status do Banco',
        description: 'Verifica a saúde do banco de dados',
        requiresAuth: false,
      },
    },
  },
};

/**
 * GET /api/api-docs - Documentação da API
 */
export const GET = withMiddleware([optionalAuthMiddleware], async (req: NextRequest, ctx) => {
  try {
    // Filtrar rotas com base na autenticação
    let filteredPaths = { ...API_DOCS.paths };

    // Se não estiver autenticado, remover rotas admin
    if (!ctx.isAuthenticated) {
      filteredPaths = Object.entries(filteredPaths).reduce(
        (acc, [path, methods]) => {
          // Verificar se algum método da rota é adminOnly
          const hasAdminOnlyMethods = Object.values(methods as Record<string, unknown>).some(
            method =>
              method &&
              typeof method === 'object' &&
              'adminOnly' in method &&
              (method as Record<string, boolean>).adminOnly
          );

          if (!hasAdminOnlyMethods) {
            // Usar asserção de tipo para garantir compatibilidade
            (acc as Record<string, unknown>)[path] = methods;
          }

          return acc;
        },
        {} as typeof API_DOCS.paths
      );
    }

    return ApiResponse.success({
      ...API_DOCS,
      paths: filteredPaths,
    });
  } catch (error) {
    if (error instanceof Error) {
      return ApiResponse.error(error.message);
    }
    return ApiResponse.error('Erro ao gerar documentação da API');
  }
});
