{"timestamp": "2025-06-02T13:49:08.140Z", "environment": "production", "mocks_disabled": true, "integrations": {"vercel": {"status": "active", "type": "api_token"}, "supabase": {"status": "active", "type": "credentials"}, "stripe": {"status": "active", "type": "live_keys"}, "linear": {"status": "demo", "type": "demo_token"}, "github": {"status": "o<PERSON>h", "type": "oauth_credentials"}}, "next_steps": ["Para Linear: Obter token real em https://linear.app/settings/api", "Para GitHub: <PERSON><PERSON> j<PERSON> configurado via NextAuth", "Executar: node test-mcp-production.js para verificar"]}