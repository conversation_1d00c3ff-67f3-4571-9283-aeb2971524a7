#!/usr/bin/env node

/**
 * Script para testar se os logs de produção estão funcionando corretamente
 * Verifica se os logs de inicialização são silenciados em produção
 */

const fs = require('fs');
const path = require('path');

// Cores para output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m',
  bold: '\x1b[1m',
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function checkFileExists(filePath) {
  const fullPath = path.resolve(process.cwd(), filePath);
  return fs.existsSync(fullPath);
}

function checkLoggerConfiguration() {
  log('\n🔍 Verificando configuração do logger...', 'blue');

  const loggerPath = 'src/lib/logger.ts';
  if (!checkFileExists(loggerPath)) {
    log('❌ logger.ts não encontrado!', 'red');
    return false;
  }

  const content = fs.readFileSync(path.resolve(process.cwd(), loggerPath), 'utf8');

  // Verificar se tem as funções de log seguras
  if (!content.includes('initLogger') || !content.includes('safeConsoleLog')) {
    log('❌ Logger não tem funções de log seguras para produção!', 'red');
    return false;
  }

  // Verificar se está configurado para produção
  if (!content.includes("level: 'error'") || !content.includes("NODE_ENV !== 'production'")) {
    log('❌ Logger não está configurado corretamente para produção!', 'red');
    return false;
  }

  log('✅ Logger configurado corretamente', 'green');
  return true;
}

function checkAppInitializer() {
  log('\n🔍 Verificando app-initializer.ts...', 'blue');

  const initPath = 'src/lib/app-initializer.ts';
  if (!checkFileExists(initPath)) {
    log('❌ app-initializer.ts não encontrado!', 'red');
    return false;
  }

  const content = fs.readFileSync(path.resolve(process.cwd(), initPath), 'utf8');

  // Verificar se usa initLogger em vez de logger.info para logs de inicialização
  const problematicLogs = [
    "logger.info('Iniciando aplicação Excel Copilot",
    "logger.info('Handlers de ciclo de vida",
    "logger.info('Aplicação inicializada com serviços",
    "logger.info('Inicializando serviço:",
    "logger.info('Todos os serviços e verificações",
  ];

  let hasProblems = false;
  for (const logPattern of problematicLogs) {
    if (content.includes(logPattern)) {
      log(`❌ Encontrado log problemático: ${logPattern}`, 'red');
      hasProblems = true;
    }
  }

  // Verificar se usa initLogger corretamente
  const correctLogs = [
    "initLogger.info('Iniciando aplicação Excel Copilot",
    "initLogger.info('Handlers de ciclo de vida",
    "initLogger.info('Aplicação inicializada com serviços",
    "initLogger.info('Inicializando serviço:",
    "initLogger.info('Todos os serviços e verificações",
  ];

  let hasCorrectLogs = 0;
  for (const logPattern of correctLogs) {
    if (content.includes(logPattern)) {
      hasCorrectLogs++;
    }
  }

  if (hasProblems) {
    log('❌ app-initializer.ts ainda tem logs problemáticos!', 'red');
    return false;
  }

  if (hasCorrectLogs > 0) {
    log(
      `✅ app-initializer.ts usa initLogger corretamente (${hasCorrectLogs} logs corretos)`,
      'green'
    );
  } else {
    log('⚠️  app-initializer.ts não tem logs de inicialização detectados', 'yellow');
  }

  return true;
}

function checkManifest() {
  log('\n🔍 Verificando manifest.json...', 'blue');

  const manifestPath = 'public/manifest.json';
  if (!checkFileExists(manifestPath)) {
    log('❌ manifest.json não encontrado!', 'red');
    return false;
  }

  const content = fs.readFileSync(path.resolve(process.cwd(), manifestPath), 'utf8');

  try {
    const manifest = JSON.parse(content);

    // Verificar se os ícones existem
    let missingIcons = [];
    for (const icon of manifest.icons || []) {
      const iconPath = `public${icon.src}`;
      if (!checkFileExists(iconPath)) {
        missingIcons.push(icon.src);
      }
    }

    if (missingIcons.length > 0) {
      log(`❌ Ícones faltando: ${missingIcons.join(', ')}`, 'red');
      return false;
    }

    log('✅ Todos os ícones do manifest existem', 'green');
    return true;
  } catch (error) {
    log(`❌ Erro ao parsear manifest.json: ${error.message}`, 'red');
    return false;
  }
}

function checkEnvironmentDetection() {
  log('\n🔍 Verificando detecção de ambiente...', 'blue');

  const envPath = 'src/config/environment.ts';
  if (!checkFileExists(envPath)) {
    log('❌ environment.ts não encontrado!', 'red');
    return false;
  }

  const content = fs.readFileSync(path.resolve(process.cwd(), envPath), 'utf8');

  // Verificar se tem as funções de detecção de ambiente
  if (!content.includes('isDev()') || !content.includes('isProd()')) {
    log('❌ Funções de detecção de ambiente não encontradas!', 'red');
    return false;
  }

  // Verificar se considera NEXT_PUBLIC_FORCE_PRODUCTION
  if (!content.includes('NEXT_PUBLIC_FORCE_PRODUCTION')) {
    log('❌ Não considera NEXT_PUBLIC_FORCE_PRODUCTION!', 'red');
    return false;
  }

  log('✅ Detecção de ambiente configurada corretamente', 'green');
  return true;
}

function main() {
  log('🚀 Testando configurações de logs de produção...', 'bold');

  const checks = [
    checkLoggerConfiguration,
    checkAppInitializer,
    checkManifest,
    checkEnvironmentDetection,
  ];

  let allPassed = true;
  for (const check of checks) {
    if (!check()) {
      allPassed = false;
    }
  }

  log('\n📋 Resumo:', 'bold');
  if (allPassed) {
    log(
      '✅ Todas as verificações passaram! Os logs de produção devem estar funcionando corretamente.',
      'green'
    );
    log('\n💡 Para testar em produção:', 'cyan');
    log('   1. Defina NODE_ENV=production ou NEXT_PUBLIC_FORCE_PRODUCTION=true', 'cyan');
    log('   2. Execute npm run build && npm run start', 'cyan');
    log('   3. Verifique se os logs de inicialização não aparecem no console do navegador', 'cyan');
  } else {
    log('❌ Algumas verificações falharam. Corrija os problemas antes de fazer deploy.', 'red');
    process.exit(1);
  }
}

main();
