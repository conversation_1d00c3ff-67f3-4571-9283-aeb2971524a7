import {
  <PERSON>,
  ArrowRight,
  FileSpreadsheet,
  LineChart,
  Layers,
  Database,
  Code,
} from 'lucide-react';
import Link from 'next/link';
import { Suspense, lazy } from 'react';

import { CommandExamplesWrapper } from '@/components/command-examples-wrapper';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

// Lazy loading do componente pesado HeroSection para melhorar desempenho
const HeroSection = lazy(() =>
  import('@/components/hero-section').then(mod => ({
    default: mod.HeroSection,
  }))
);

// Definindo as cores fixas para cada feature
const featureColors = {
  blue: {
    bgFrom: 'from-blue-100',
    bgTo: 'to-blue-200',
    darkFrom: 'dark:from-blue-900/50',
    darkTo: 'dark:to-blue-800/30',
    text: 'text-blue-600',
    darkText: 'dark:text-blue-400',
  },
  indigo: {
    bgFrom: 'from-indigo-100',
    bgTo: 'to-indigo-200',
    darkFrom: 'dark:from-indigo-900/50',
    darkTo: 'dark:to-indigo-800/30',
    text: 'text-indigo-600',
    darkText: 'dark:text-indigo-400',
  },
  purple: {
    bgFrom: 'from-purple-100',
    bgTo: 'to-purple-200',
    darkFrom: 'dark:from-purple-900/50',
    darkTo: 'dark:to-purple-800/30',
    text: 'text-purple-600',
    darkText: 'dark:text-purple-400',
  },
  green: {
    bgFrom: 'from-green-100',
    bgTo: 'to-green-200',
    darkFrom: 'dark:from-green-900/50',
    darkTo: 'dark:to-green-800/30',
    text: 'text-green-600',
    darkText: 'dark:text-green-400',
  },
  amber: {
    bgFrom: 'from-amber-100',
    bgTo: 'to-amber-200',
    darkFrom: 'dark:from-amber-900/50',
    darkTo: 'dark:to-amber-800/30',
    text: 'text-amber-600',
    darkText: 'dark:text-amber-400',
  },
  rose: {
    bgFrom: 'from-rose-100',
    bgTo: 'to-rose-200',
    darkFrom: 'dark:from-rose-900/50',
    darkTo: 'dark:to-rose-800/30',
    text: 'text-rose-600',
    darkText: 'dark:text-rose-400',
  },
};

export default function HomePage() {
  return (
    <div className="min-h-screen">
      {/* Hero section com novo componente */}
      <div className="relative overflow-hidden bg-gradient-to-b from-blue-50 via-indigo-50/50 to-white dark:from-gray-900 dark:via-blue-950/20 dark:to-background pb-12">
        {/* Background decorations */}
        <div className="absolute inset-0 pointer-events-none overflow-hidden">
          <div className="absolute -top-[30%] -left-[10%] w-[60%] h-[60%] rounded-full bg-gradient-to-br from-blue-400/20 to-indigo-600/5 blur-3xl opacity-70"></div>
          <div className="absolute -bottom-[20%] right-[10%] w-[40%] h-[40%] rounded-full bg-gradient-to-bl from-indigo-400/20 to-blue-600/5 blur-3xl opacity-70"></div>
        </div>

        <div className="container relative mx-auto px-4 pt-16 pb-20 max-w-6xl z-10">
          <Suspense
            fallback={
              <div className="flex justify-center items-center min-h-[400px]">
                <div className="flex flex-col items-center">
                  <div className="w-10 h-10 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
                  <p className="mt-4 text-sm text-muted-foreground">Carregando...</p>
                </div>
              </div>
            }
          >
            <HeroSection />
          </Suspense>
        </div>
      </div>

      {/* Recursos e Como Funciona - Combinados */}
      <section className="py-12 bg-gradient-to-b from-white to-blue-50/50 dark:from-background dark:to-blue-950/10">
        <div className="container mx-auto px-4 max-w-6xl">
          {/* Features - Versão compacta */}
          <div className="text-center mb-10">
            <h2 className="text-2xl md:text-3xl font-bold mb-2">
              Transforme sua experiência com planilhas
            </h2>
            <p className="text-base text-muted-foreground max-w-2xl mx-auto">
              IA avançada que simplifica sua interação com dados
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-12">
            {[
              {
                icon: <Brain className="h-5 w-5" aria-hidden="true" />,
                title: 'Linguagem natural',
                description: 'Use comandos em português simples',
                color: 'blue',
              },
              {
                icon: <Layers className="h-5 w-5" aria-hidden="true" />,
                title: 'Planilhas complexas',
                description: 'Manipule dados facilmente',
                color: 'indigo',
              },
              {
                icon: <LineChart className="h-5 w-5" aria-hidden="true" />,
                title: 'Visualizações',
                description: 'Gráficos profissionais rápidos',
                color: 'purple',
              },
              {
                icon: <Database className="h-5 w-5" aria-hidden="true" />,
                title: 'Importação/exportação',
                description: 'Compatibilidade com Excel',
                color: 'green',
              },
              {
                icon: <Code className="h-5 w-5" aria-hidden="true" />,
                title: 'Fórmulas inteligentes',
                description: 'Funções avançadas simplificadas',
                color: 'amber',
              },
              {
                icon: <FileSpreadsheet className="h-5 w-5" aria-hidden="true" />,
                title: 'Templates prontos',
                description: 'Modelos para iniciar rapidamente',
                color: 'rose',
              },
            ].map((feature, index) => {
              // Obter as cores para este recurso
              const colorSet = featureColors[feature.color as keyof typeof featureColors];

              return (
                <div
                  key={index}
                  className="flex items-center gap-3 p-3 rounded-xl border border-gray-200 dark:border-gray-800 bg-white/60 dark:bg-gray-900/60 backdrop-blur-sm hover:shadow-sm transition-all focus-within:ring-2 focus-within:ring-primary/70"
                  tabIndex={0}
                  role="button"
                  aria-label={`Feature: ${feature.title} - ${feature.description}`}
                >
                  <div
                    className={cn(
                      'p-2 rounded-lg flex items-center justify-center bg-gradient-to-br',
                      colorSet.bgFrom,
                      colorSet.bgTo,
                      colorSet.darkFrom,
                      colorSet.darkTo,
                      colorSet.text,
                      colorSet.darkText
                    )}
                  >
                    {feature.icon}
                  </div>
                  <div>
                    <h3 className="text-sm font-semibold">{feature.title}</h3>
                    <p className="text-xs text-muted-foreground hidden xs:block">
                      {feature.description}
                    </p>
                  </div>
                </div>
              );
            })}
          </div>

          {/* How it works - Versão compacta */}
          <div className="bg-white dark:bg-gray-900/80 rounded-xl p-6 shadow-md border border-gray-200 dark:border-gray-800">
            <h3 className="text-xl font-bold mb-4 text-center">Como Funciona</h3>

            <div className="grid md:grid-cols-3 gap-4">
              {[
                {
                  step: '1',
                  title: 'Descreva o que precisa',
                  description: 'Use linguagem natural para pedir o que deseja.',
                },
                {
                  step: '2',
                  title: 'A IA cria em segundos',
                  description: 'O Excel Copilot interpreta seu pedido e cria tudo automaticamente.',
                },
                {
                  step: '3',
                  title: 'Personalize e exporte',
                  description: 'Ajuste conforme necessário e exporte como arquivo Excel.',
                },
              ].map((step, index) => (
                <div key={index} className="flex items-start gap-3">
                  <div
                    className="flex-shrink-0 flex items-center justify-center w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 text-white rounded-full shadow-sm text-sm font-bold"
                    aria-hidden="true"
                  >
                    {step.step}
                  </div>
                  <div>
                    <h4 className="text-sm font-semibold mb-1">
                      <span className="sr-only">Passo {step.step}: </span>
                      {step.title}
                    </h4>
                    <p className="text-xs text-muted-foreground">{step.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Examples section - Versão compacta */}
      <section className="py-8 sm:py-12" id="exemplos" aria-labelledby="exemplos-titulo">
        <div className="container mx-auto px-3 sm:px-4 max-w-6xl">
          <div className="text-center mb-4 sm:mb-6">
            <h2
              id="exemplos-titulo"
              className="text-xl sm:text-2xl md:text-3xl font-bold mb-1 sm:mb-2"
            >
              Experimente esses comandos
            </h2>
            <p className="text-sm sm:text-base text-muted-foreground max-w-2xl mx-auto">
              Veja o poder da linguagem natural em ação
            </p>
          </div>

          <div className="bg-white/60 dark:bg-gray-900/60 backdrop-blur-sm border border-gray-200 dark:border-gray-800 rounded-xl shadow-md p-2 sm:p-4">
            <Suspense
              fallback={
                <div
                  className="h-[200px] sm:h-[250px] flex items-center justify-center"
                  aria-live="polite"
                  aria-busy="true"
                >
                  <div className="flex flex-col items-center gap-2 sm:gap-3">
                    <div
                      className="w-6 sm:w-8 h-6 sm:h-8 rounded-full border-3 border-blue-200 border-t-blue-600 animate-spin"
                      role="status"
                    ></div>
                    <p className="text-xs sm:text-sm text-muted-foreground">
                      Carregando exemplos...
                    </p>
                  </div>
                </div>
              }
            >
              <CommandExamplesWrapper />
            </Suspense>
          </div>

          {/* CTA - Simplificada e integrada na seção de exemplos */}
          <div className="mt-6 sm:mt-8 bg-gradient-to-br from-blue-600 to-indigo-700 dark:from-blue-700 dark:to-indigo-900 rounded-xl overflow-hidden shadow-lg p-4 sm:p-6">
            <div className="flex flex-col md:flex-row items-center gap-3 sm:gap-4">
              <div
                className="p-2 sm:p-3 bg-white/20 backdrop-blur-sm rounded-full"
                aria-hidden="true"
              >
                <FileSpreadsheet className="h-6 w-6 sm:h-8 sm:w-8 text-white" />
              </div>

              <div className="text-center md:text-left flex-1">
                <h2 className="text-lg sm:text-xl md:text-2xl font-bold text-white mb-2">
                  Comece sua jornada agora
                </h2>
                <p className="text-xs sm:text-sm text-blue-100 mb-3 sm:mb-4 max-w-2xl">
                  Pare de perder tempo com fórmulas complexas. Use o poder da IA agora.
                </p>
                <Button
                  className="rounded-full bg-white hover:bg-gray-100 text-blue-600 px-4 sm:px-5 py-1.5 sm:py-2 text-xs sm:text-sm font-medium border-0 shadow-md focus:ring-2 focus:ring-white/70 focus:ring-offset-2 focus:ring-offset-blue-600"
                  asChild
                >
                  <Link href="/dashboard">
                    Criar minha primeira planilha{' '}
                    <ArrowRight className="ml-1 h-3 w-3 sm:h-4 sm:w-4" aria-hidden="true" />
                  </Link>
                </Button>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
