# Reestruturação do Projeto Excel Copilot

Este documento descreve a reestruturação realizada no projeto Excel Copilot para eliminar duplicidades, separar responsabilidades e melhorar a organização do código.

## Principais Mudanças

### 1. Unificação de Providers e Contexts

- **Antes**: Código disperso entre `providers/` e `contexts/`
- **Depois**: Nova pasta `context-providers/` centraliza todos os contextos e providers

#### Benefícios:

- Elimina redundância conceitual
- Facilita encontrar state containers específicos
- Permite composição de contextos via o novo `ProviderComposer`

#### Arquivos chave:

- `context-providers/index.ts`: Ponto central de exportação de todos os providers
- `context-providers/ProviderComposer.tsx`: Utilitário para compor múltiplos providers
- `context-providers/LocaleProvider.tsx`: Provider para internacionalização (migrado de contexts/)
- `context-providers/ThemeProvider.tsx`: Provider para temas (nova implementação explícita)

### 2. Separação Clara entre Utils e Lib

- **Antes**: Sobreposição de responsabilidades entre `utils/` e `lib/`
- **Depois**: Fronteiras claras baseadas em estado e efeitos colaterais

#### Regras de Separação:

##### `utils/`: Funções Puras

- **Sem estado**: Não mantém estado entre chamadas
- **Determinísticas**: Mesma entrada sempre produz mesma saída
- **Sem efeitos colaterais**: Não modifica nada fora do seu escopo
- **Sem dependências externas**: Não interage com APIs externas

##### `lib/`: Abstrações com Estado

- **Gerencia estado**: Mantém e gerencia estado interno
- **Interage com serviços externos**: Comunicação com APIs, bancos de dados
- **Implementa lógica complexa**: Classes e serviços com funcionalidades específicas
- **Encapsula efeitos colaterais**: Logs, telemetria, persistência

#### Nova Organização Interna:

##### Utils:

- `utils/functions/`: Utilitários gerais (strings, arrays, números)
- `utils/data/`: Processamento de dados (json.ts)
- `utils/http/`: Relacionados a HTTP (api-response.ts)
- `utils/excel/`: Operações puras com dados de Excel

##### Lib:

- `lib/ai/`: Abstrações para inteligência artificial
- `lib/excel/`: Manipulação de workbooks
- `lib/middleware/`: Middlewares de autenticação e autorização
- `lib/security/`: Segurança e criptografia
- `lib/operations/`: Operações complexas de negócio

## Exemplos de Migração

### Exemplo 1: Função Pura

```typescript
// utils/functions/index.ts
export function formatCurrency(value: number): string {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL',
  }).format(value);
}
```

### Exemplo 2: Abstração com Estado

```typescript
// lib/excel/WorkbookManager.ts
export class WorkbookManager {
  private workbooks = new Map<string, Workbook>();

  async loadWorkbook(id: string): Promise<Workbook> {
    // Lógica com efeitos colaterais e estado
  }
}
```

## Convenções e Padrões

### Exportações

- Arquivos `index.ts` em cada diretório para facilitar importações
- Documentação JSDoc para todas as funções e classes

### Nomenclatura

- `utils/`: Funções com nomes que indicam ação (format, parse, convert)
- `lib/`: Classes e serviços com nomes que indicam responsabilidade (Manager, Service, Provider)

### Testes

- Funções em `utils/` devem ter testes de unidade
- Abstrações em `lib/` devem ter testes de integração

## Próximos Passos

1. Migrar providers para arquivos individuais em `context-providers/`
2. Continuar migração de código com estado de `utils/` para `lib/`
3. Atualizar imports no projeto para usar as novas localizações
4. Adicionar regras de ESLint para enforçar a separação de responsabilidades
