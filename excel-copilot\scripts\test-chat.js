/**
 * Script para testar a funcionalidade de chat
 */

const { PrismaClient } = require('@prisma/client');

// Inicializar cliente Prisma
const prisma = new PrismaClient();

/**
 * Função principal
 */
async function main() {
  console.log('=== TESTE DE FUNCIONALIDADE DE CHAT ===\n');

  try {
    // 1. Buscar workbooks para teste
    console.log('Buscando workbooks disponíveis...');

    const workbooks = await prisma.workbook.findMany({
      select: {
        id: true,
        name: true,
      },
      take: 1,
    });

    if (workbooks.length === 0) {
      throw new Error('Nenhum workbook encontrado para teste');
    }

    const testWorkbook = workbooks[0];
    console.log(`✅ Workbook encontrado: ${testWorkbook.name} (ID: ${testWorkbook.id})`);

    // 2. Adicionar mensagem de teste
    console.log('\nAdicionando mensagem de teste ao histórico de chat...');

    const testMessage = 'Calcular a soma da coluna de vendas';
    const testResponse = 'A soma da coluna de vendas é 1500';

    const chatMessage = await prisma.chatHistory.create({
      data: {
        workbookId: testWorkbook.id,
        userId: 'test-user',
        message: testMessage,
        response: testResponse,
      },
    });

    console.log(`✅ Mensagem adicionada com ID: ${chatMessage.id}`);

    // 3. Verificar se a mensagem foi salva corretamente
    console.log('\nVerificando se a mensagem foi salva...');

    const savedMessage = await prisma.chatHistory.findUnique({
      where: {
        id: chatMessage.id,
      },
    });

    if (savedMessage) {
      console.log('✅ Mensagem encontrada no banco de dados:');
      console.log(`  - Mensagem: ${savedMessage.message}`);
      console.log(`  - Resposta: ${savedMessage.response}`);
      console.log(`  - Data: ${savedMessage.createdAt}`);
    } else {
      throw new Error('Mensagem não encontrada no banco de dados');
    }

    // 4. Listar o histórico de chat
    console.log('\nListando histórico de chat recente:');

    const chatHistory = await prisma.chatHistory.findMany({
      where: {
        workbookId: testWorkbook.id,
      },
      orderBy: {
        createdAt: 'desc',
      },
      take: 5,
    });

    chatHistory.forEach((entry, index) => {
      console.log(`\nMensagem ${index + 1}:`);
      console.log(`  - Mensagem: ${entry.message}`);
      console.log(`  - Resposta: ${entry.response}`);
      console.log(`  - Data: ${entry.createdAt}`);
    });

    console.log('\n✅ TESTE DE CHAT CONCLUÍDO COM SUCESSO');
  } catch (error) {
    console.error('❌ Erro no teste:', error);
  } finally {
    // Fechar conexão com o banco de dados
    await prisma.$disconnect();
    console.log('\nConexão com o banco de dados fechada');
  }
}

// Executar função principal
main().catch(error => {
  console.error('❌ Erro fatal:', error);
  process.exit(1);
});
