/**
 * Monitor de memória para verificar uso de recursos
 */
export class MemoryMonitor {
  private static instance: MemoryMonitor;
  private intervalId: NodeJS.Timeout | null = null;

  private constructor() {}

  public static getInstance(): MemoryMonitor {
    if (!MemoryMonitor.instance) {
      MemoryMonitor.instance = new MemoryMonitor();
    }
    return MemoryMonitor.instance;
  }

  public startMonitoring(intervalMs: number = 30000): void {
    if (this.intervalId) {
      this.stopMonitoring();
    }

    this.intervalId = setInterval(() => {
      this.checkMemoryUsage();
    }, intervalMs);
  }

  public stopMonitoring(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
  }

  private checkMemoryUsage(): void {
    if (typeof window !== 'undefined' && window.performance && 'memory' in window.performance) {
      // Definindo uma interface para o memory info
      interface PerformanceMemory {
        usedJSHeapSize: number;
        totalJSHeapSize: number;
        jsHeapSizeLimit: number;
      }

      const memoryInfo = (window.performance as { memory: PerformanceMemory }).memory;
      const usedHeapSize = memoryInfo.usedJSHeapSize / (1024 * 1024);
      const totalHeapSize = memoryInfo.totalJSHeapSize / (1024 * 1024);
      const usagePercentage = (usedHeapSize / totalHeapSize) * 100;

      // Pode implementar log ou telemetria aqui
      if (usagePercentage > 80) {
        // Alerta de uso alto de memória
      }
    }
  }
}
