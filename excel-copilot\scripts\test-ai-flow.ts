#!/usr/bin/env ts-node
/**
 * Script para testar manualmente o fluxo completo de IA para Excel
 *
 * Execução: ts-node scripts/test-ai-flow.ts
 */

import { ExcelAIProcessor } from '../src/lib/ai/ExcelAIProcessor';
import { executeExcelOperations, createExcelFile } from '../src/lib/excel';
import fs from 'fs';
import path from 'path';
import { PrismaClient } from '@prisma/client';
import { ExcelOperationType } from '../src/types/index';
import { AIProcessingContext } from '../src/lib/ai/ExcelAIProcessor';

// Cliente Prisma para acesso ao banco de dados
const prisma = new PrismaClient();

// Dados de exemplo para testar
const sampleData = {
  headers: ['Produto', 'Preço', 'Quantidade', 'Total'],
  rows: [
    ['Produto A', 100, 2, 200],
    ['Produto B', 150, 3, 450],
    ['Produto C', 75, 5, 375],
    ['Produto D', 200, 1, 200],
    ['Produto E', 50, 10, 500],
  ],
};

// Comandos de exemplo para testar o processamento
const sampleCommands = [
  'Criar uma tabela com os dados de vendas',
  'Calcular a soma total da coluna Preço',
  'Criar um gráfico de barras com produtos e totais',
  'Ordenar a tabela por valor total de forma decrescente',
  'Aplicar formatação de moeda na coluna de preço',
];

// Exemplos de comandos para processamento
const commandExamples = [
  'Calcule a média de vendas por mês',
  'Crie um gráfico de barras mostrando vendas por trimestre',
  'Filtre os registros onde o valor é maior que 1000',
  'Ordene a tabela por data em ordem decrescente',
  'Aplique formatação de moeda na coluna de preço',
];

// Contexto para processamento
const aiContext: Partial<AIProcessingContext> = {
  activeSheet: 'Planilha1',
  headers: ['Nome', 'Valor', 'Data'],
  selection: 'A1:C5',
  recentOperations: [],
};

/**
 * Função principal
 */
async function main() {
  console.log('=== TESTE DE FLUXO COMPLETO DE IA PARA EXCEL ===\n');

  try {
    // 1. Inicializar o processador de IA (modo de teste para não depender da API externa)
    console.log('Inicializando processador de IA...');
    const aiProcessor = new ExcelAIProcessor(
      {
        activeSheet: 'Vendas',
        headers: sampleData.headers,
        selection: 'A1:D6',
        recentOperations: [],
      },
      true
    ); // true ativa o modo de teste

    console.log('Processador de IA inicializado com sucesso');

    // 2. Criar um workbook temporário no banco de dados para teste
    console.log('\nCriando workbook de teste no banco de dados...');

    // Verificar se já existe um workbook de teste
    let testWorkbook = await prisma.workbook.findFirst({
      where: {
        name: 'Workbook de Teste Manual',
      },
      include: {
        sheets: true,
      },
    });

    // Se não existir, criar um novo
    if (!testWorkbook) {
      testWorkbook = await prisma.workbook.create({
        data: {
          name: 'Workbook de Teste Manual',
          userId: 'test-user',
          sheets: {
            create: {
              name: 'Vendas',
              data: JSON.stringify(sampleData),
            },
          },
        },
        include: {
          sheets: true,
        },
      });

      console.log(`Workbook criado com ID: ${testWorkbook.id}`);
    } else {
      console.log(`Usando workbook existente com ID: ${testWorkbook.id}`);
    }

    // 3. Testar processamento de comandos
    console.log('\n=== TESTANDO PROCESSAMENTO DE COMANDOS ===');

    for (const command of sampleCommands) {
      console.log(`\nProcessando comando: "${command}"`);

      const result = await aiProcessor.processQuery(command);

      if (result.success) {
        console.log(
          `✅ Comando processado com sucesso. ${result.operations.length} operação(ões) gerada(s)`
        );
        console.log('Tipos de operações:', result.operations.map(op => op.type).join(', '));
      } else {
        console.log(`❌ Falha ao processar comando: ${result.message}`);
      }

      // 4. Para o comando de soma, testar execução das operações
      if (command.toLowerCase().includes('soma')) {
        console.log('\n=== TESTANDO EXECUÇÃO DE OPERAÇÕES ===');

        if (testWorkbook.sheets && testWorkbook.sheets.length > 0) {
          const sheet = testWorkbook.sheets[0];
          if (sheet && sheet.data) {
            const sheetData = JSON.parse(sheet.data as string);

            console.log('Executando operações...');
            try {
              const execResult = await executeExcelOperations(sheetData, result.operations);

              console.log('Resumo da execução:', execResult.resultSummary);

              // Atualizar dados no banco de dados
              await prisma.sheet.update({
                where: {
                  id: sheet.id,
                },
                data: {
                  data: JSON.stringify(execResult.updatedData),
                },
              });

              console.log('✅ Dados atualizados no banco de dados');
            } catch (error) {
              console.log('❌ Erro ao executar operações:', error);
            }
          } else {
            console.log('❌ Sheet não tem dados');
          }
        } else {
          console.log('❌ Workbook não tem sheets');
        }
      }
    }

    // 5. Testar exportação para Excel
    console.log('\n=== TESTANDO EXPORTAÇÃO PARA EXCEL ===');

    // Buscar dados atualizados
    const updatedSheet = await prisma.sheet.findFirst({
      where: {
        workbookId: testWorkbook.id,
      },
    });

    if (updatedSheet) {
      const sheetData = JSON.parse(updatedSheet.data as string);

      console.log('Criando arquivo Excel...');
      const excelBlob = await createExcelFile(
        [
          {
            name: updatedSheet.name,
            data: sheetData,
          },
        ],
        'Workbook de Teste Manual'
      );

      // Salvar arquivo
      const outputDir = path.join(__dirname, '../temp');

      // Criar diretório se não existir
      if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
      }

      const filePath = path.join(outputDir, 'teste-manual.xlsx');
      const buffer = await excelBlob.arrayBuffer();

      fs.writeFileSync(filePath, Buffer.from(buffer));

      console.log(`✅ Arquivo Excel salvo em: ${filePath}`);
    }

    console.log('\n=== TESTE CONCLUÍDO COM SUCESSO ===');
  } catch (error) {
    console.error('❌ Erro no teste:', error);
  } finally {
    // Fechar conexão com o banco de dados
    await prisma.$disconnect();
  }
}

// Executar função principal
main().catch(console.error);
