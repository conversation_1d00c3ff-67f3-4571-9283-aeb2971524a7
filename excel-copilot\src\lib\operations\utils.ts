/**
 * Utilitários para operações com células e planilhas
 */
import { safeArrayAccess, extractGroup } from '@/utils';

// Definindo os tipos localmente já que não existem no módulo @/types
export interface CellCoordinate {
  row: number;
  col: number;
}

export interface RangeCoordinate {
  startRow: number;
  startCol: number;
  endRow: number;
  endCol: number;
}

/**
 * Analisa uma referência de célula no formato A1, retornando o índice de linha e coluna
 * @param cellRef Referência de célula (ex: A1, B2, etc.)
 */
export function parseCellReference(cellRef: string | undefined): { row: number; col: number } {
  if (!cellRef) {
    throw new Error('Referência de célula inválida: indefinida');
  }

  const match = cellRef.match(/([A-Za-z]+)([0-9]+)/);
  if (!match) {
    throw new Error(`Referência de célula inválida: ${cellRef}`);
  }

  const colStr = extractGroup(match, 1, '').toUpperCase();
  const rowStr = extractGroup(match, 2, '');

  // Converter a letra da coluna para índice numérico (A=0, B=1, ...)
  let colIndex = 0;
  for (let i = 0; i < colStr.length; i++) {
    colIndex = colIndex * 26 + (colStr.charCodeAt(i) - 'A'.charCodeAt(0) + 1);
  }
  colIndex--; // Ajuste para base 0

  // Converter número da linha para índice numérico (1=0, 2=1, ...)
  const rowIndex = parseInt(rowStr, 10) - 1;

  return { row: rowIndex, col: colIndex };
}

/**
 * Converte índice de coluna para letra (ex: 0 -> A, 1 -> B, etc.)
 * @param index Índice da coluna
 */
export function columnIndexToLetter(index: number): string {
  let letter = '';
  let tempIndex = index;

  while (tempIndex >= 0) {
    letter = String.fromCharCode(65 + (tempIndex % 26)) + letter;
    tempIndex = Math.floor(tempIndex / 26) - 1;
  }

  return letter;
}

/**
 * Converte letra de coluna para índice (ex: A -> 0, B -> 1, etc.)
 * @param column Letra da coluna
 */
export function columnLetterToIndex(column: string | undefined): number {
  if (!column) {
    throw new Error('Coluna inválida: indefinida');
  }

  column = column.toUpperCase();
  let index = 0;

  for (let i = 0; i < column.length; i++) {
    index = index * 26 + (column.charCodeAt(i) - 'A'.charCodeAt(0) + 1);
  }

  return index - 1; // Ajuste para base 0
}

/**
 * Retorna a faixa de células entre dois pontos
 * @param startCol Coluna inicial
 * @param startRow Linha inicial
 * @param endCol Coluna final
 * @param endRow Linha final
 */
export function getCellRange(
  startCol: string | undefined,
  startRow: string | undefined,
  endCol: string | undefined,
  endRow: string | undefined
): { startRow: number; startCol: number; endRow: number; endCol: number } {
  if (!startCol || !startRow || !endCol || !endRow) {
    throw new Error('Parâmetros de range inválidos');
  }

  const startColIndex = columnLetterToIndex(startCol);
  const endColIndex = columnLetterToIndex(endCol);
  const startRowIndex = parseInt(startRow, 10) - 1;
  const endRowIndex = parseInt(endRow, 10) - 1;

  return {
    startRow: startRowIndex,
    startCol: startColIndex,
    endRow: endRowIndex,
    endCol: endColIndex,
  };
}

/**
 * Divide uma string de range (ex: "A1:B2") em partes de início e fim
 * @param range String do range de células
 * @returns Objeto com início e fim
 */
export function splitRange(range: string): { start: string; end: string } {
  if (!range || !range.includes(':')) {
    return { start: range || '', end: range || '' };
  }

  const parts = range.split(':');
  // Garantir que start e end nunca sejam undefined
  return {
    start: safeArrayAccess(parts, 0) || '',
    end: safeArrayAccess(parts, 1) || '',
  };
}

/**
 * Verifica se uma string possui referência no formato A1
 * @param text Texto a verificar
 */
export function hasCellReference(text: string): boolean {
  return /[A-Za-z]+[0-9]+/.test(text);
}

/**
 * Obtém a primeira referência de célula em um texto
 * @param text Texto para buscar referência
 */
export function extractCellReference(text: string): string | null {
  const match = text.match(/[A-Za-z]+[0-9]+/);
  return match ? match[0] : null;
}
