#!/usr/bin/env node

/**
 * Script para finalizar a configuração do Excel Copilot
 *
 * Este script:
 * 1. Verifica todas as integrações MCP
 * 2. Testa endpoints principais
 * 3. Valida configurações de produção
 * 4. Gera relatório final
 */

const fs = require('fs');
const path = require('path');

// Cores para output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m',
  bold: '\x1b[1m',
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logHeader(message) {
  console.log('\n' + '='.repeat(60));
  log(message, 'bold');
  console.log('='.repeat(60));
}

async function checkEnvironmentVariables() {
  logHeader('🔧 Verificando Variáveis de Ambiente');

  const envPath = path.join(process.cwd(), '.env.local');

  if (!fs.existsSync(envPath)) {
    log('❌ Arquivo .env.local não encontrado', 'red');
    return false;
  }

  const envContent = fs.readFileSync(envPath, 'utf8');

  // Variáveis críticas para verificar
  const criticalVars = [
    'DATABASE_URL',
    'NEXTAUTH_SECRET',
    'NEXTAUTH_URL',
    'VERCEL_API_TOKEN',
    'STRIPE_SECRET_KEY',
    'SUPABASE_URL',
    'VERTEX_AI_PROJECT_ID',
  ];

  // Variáveis MCP
  const mcpVars = [
    { name: 'VERCEL_API_TOKEN', service: 'Vercel MCP' },
    { name: 'LINEAR_API_KEY', service: 'Linear MCP' },
    { name: 'GITHUB_TOKEN', service: 'GitHub MCP' },
    { name: 'SUPABASE_URL', service: 'Supabase MCP' },
    { name: 'STRIPE_SECRET_KEY', service: 'Stripe MCP' },
  ];

  let allCriticalConfigured = true;
  let mcpStatus = {};

  // Verificar variáveis críticas
  log('\n📋 Variáveis Críticas:', 'blue');
  criticalVars.forEach(varName => {
    const isConfigured =
      envContent.includes(`${varName}=`) && !envContent.includes(`${varName}=""`);

    if (isConfigured) {
      log(`✅ ${varName}: CONFIGURADO`, 'green');
    } else {
      log(`❌ ${varName}: NÃO CONFIGURADO`, 'red');
      allCriticalConfigured = false;
    }
  });

  // Verificar variáveis MCP
  log('\n🔌 Integrações MCP:', 'blue');
  mcpVars.forEach(({ name, service }) => {
    const isConfigured = envContent.includes(`${name}=`) && !envContent.includes(`${name}=""`);

    mcpStatus[service] = isConfigured;

    if (isConfigured) {
      log(`✅ ${service}: CONFIGURADO`, 'green');
    } else {
      log(`⚠️ ${service}: PENDENTE`, 'yellow');
    }
  });

  return { allCriticalConfigured, mcpStatus };
}

async function checkFileStructure() {
  logHeader('📁 Verificando Estrutura de Arquivos');

  const criticalFiles = [
    'src/lib/github-integration.ts',
    'src/lib/vercel-integration.ts',
    'src/lib/linear-integration.ts',
    'src/lib/supabase-integration.ts',
    'src/lib/stripe-integration.ts',
    'src/app/api/github/status/route.ts',
    'src/app/api/vercel/status/route.ts',
    'src/app/api/linear/status/route.ts',
    'src/app/api/supabase/status/route.ts',
    'src/app/api/stripe/status/route.ts',
    'docs/GITHUB_MCP_INTEGRATION.md',
    'mcp.json',
  ];

  let allFilesExist = true;

  criticalFiles.forEach(filePath => {
    const fullPath = path.join(process.cwd(), filePath);

    if (fs.existsSync(fullPath)) {
      log(`✅ ${filePath}`, 'green');
    } else {
      log(`❌ ${filePath}: FALTANDO`, 'red');
      allFilesExist = false;
    }
  });

  return allFilesExist;
}

async function checkMCPConfiguration() {
  logHeader('🔌 Verificando Configuração MCP');

  const mcpConfigPath = path.join(process.cwd(), 'mcp.json');

  if (!fs.existsSync(mcpConfigPath)) {
    log('❌ Arquivo mcp.json não encontrado', 'red');
    return false;
  }

  try {
    const mcpConfig = JSON.parse(fs.readFileSync(mcpConfigPath, 'utf8'));

    const expectedMCPs = ['vercel', 'linear', 'github', 'supabase', 'stripe'];
    const configuredMCPs = Object.keys(mcpConfig.mcpServers || {});

    log('\n📊 MCPs Configuradas:', 'blue');
    expectedMCPs.forEach(mcpName => {
      if (configuredMCPs.includes(mcpName)) {
        const mcpData = mcpConfig.mcpServers[mcpName];
        log(`✅ ${mcpName}: ${mcpData.status || 'configured'}`, 'green');
      } else {
        log(`❌ ${mcpName}: NÃO CONFIGURADO`, 'red');
      }
    });

    return configuredMCPs.length === expectedMCPs.length;
  } catch (error) {
    log(`❌ Erro ao ler mcp.json: ${error.message}`, 'red');
    return false;
  }
}

async function generateFinalReport(envStatus, filesOk, mcpOk) {
  logHeader('📊 Relatório Final do Projeto');

  const totalMCPs = 5;
  const configuredMCPs = Object.values(envStatus.mcpStatus).filter(Boolean).length;
  const completionPercentage = Math.round((configuredMCPs / totalMCPs) * 100);

  log('\n🎯 Status Geral:', 'cyan');
  log(`📁 Estrutura de Arquivos: ${filesOk ? '✅ OK' : '❌ PROBLEMAS'}`, filesOk ? 'green' : 'red');
  log(
    `🔧 Configuração Crítica: ${envStatus.allCriticalConfigured ? '✅ OK' : '❌ PROBLEMAS'}`,
    envStatus.allCriticalConfigured ? 'green' : 'red'
  );
  log(`🔌 Configuração MCP: ${mcpOk ? '✅ OK' : '⚠️ PARCIAL'}`, mcpOk ? 'green' : 'yellow');

  log('\n📈 Métricas:', 'cyan');
  log(`🔌 MCPs Configuradas: ${configuredMCPs}/${totalMCPs} (${completionPercentage}%)`, 'blue');

  log('\n🚀 Status das Integrações MCP:', 'cyan');
  Object.entries(envStatus.mcpStatus).forEach(([service, configured]) => {
    const status = configured ? '✅ FUNCIONANDO' : '⚠️ PENDENTE';
    const color = configured ? 'green' : 'yellow';
    log(`   ${service}: ${status}`, color);
  });

  // Determinar status geral
  if (filesOk && envStatus.allCriticalConfigured && completionPercentage >= 80) {
    log('\n🎉 PROJETO PRONTO PARA PRODUÇÃO! 🚀', 'green');

    if (completionPercentage < 100) {
      log('\n📝 Próximos Passos:', 'yellow');
      Object.entries(envStatus.mcpStatus).forEach(([service, configured]) => {
        if (!configured) {
          log(`   • Configurar ${service}`, 'yellow');
        }
      });
    }
  } else {
    log('\n⚠️ PROJETO PRECISA DE AJUSTES', 'yellow');

    if (!filesOk) {
      log('   • Verificar arquivos faltantes', 'red');
    }
    if (!envStatus.allCriticalConfigured) {
      log('   • Configurar variáveis críticas', 'red');
    }
  }

  log('\n📚 Documentação:', 'cyan');
  log('   • README.md: Visão geral completa', 'blue');
  log('   • PROJETO_COMPLETO.md: Status detalhado', 'blue');
  log('   • docs/: Documentação específica de cada MCP', 'blue');

  log('\n🔗 Links Úteis:', 'cyan');
  log('   • GitHub Tokens: https://github.com/settings/tokens', 'blue');
  log('   • Linear API: https://linear.app/settings/api', 'blue');
  log('   • Vercel Tokens: https://vercel.com/account/tokens', 'blue');
}

async function main() {
  try {
    log('🚀 Excel Copilot - Verificação Final do Projeto', 'bold');

    // Verificar variáveis de ambiente
    const envStatus = await checkEnvironmentVariables();

    // Verificar estrutura de arquivos
    const filesOk = await checkFileStructure();

    // Verificar configuração MCP
    const mcpOk = await checkMCPConfiguration();

    // Gerar relatório final
    await generateFinalReport(envStatus, filesOk, mcpOk);
  } catch (error) {
    log(`\n❌ Erro durante a verificação: ${error.message}`, 'red');
    process.exit(1);
  }
}

// Executar script
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { main };
