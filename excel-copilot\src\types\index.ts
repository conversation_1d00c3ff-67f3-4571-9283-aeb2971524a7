/**
 * Central export file for all types used in Excel Copilot
 */

// Helper functions
export function generateId(): string {
  return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
}

export function createOperation(type: string, params: any): ExcelOperation {
  return {
    type,
    data: params,
    id: generateId(),
  };
}

export function createWebSocketMessage(type: string, payload: any, id?: string): WebSocketMessage {
  return {
    id: id || generateId(),
    type,
    payload,
  };
}

// Tipos principais do Excel Copilot
export enum ExcelOperationType {
  FORMULA = 'FORMULA',
  FILTER = 'FILTER',
  SORT = 'SORT',
  FORMAT = 'FORMAT',
  CHART = 'CHART',
  CELL_UPDATE = 'CELL_UPDATE',
  // CELL = 'CELL_UPDATE', // Alias para compatibilidade - comentado para evitar erro de compilação
  COLUMN_OPERATION = 'COLUMN_OPERATION',
  // COLUMN = 'COLUMN_OPERATION', // Alias para compatibilidade - comentado para evitar erro de compilação
  ROW_OPERATION = 'ROW_OPERATION',
  // ROW = 'ROW_OPERATION', // Alias para compatibilidade - comentado para evitar erro de compilação
  TABLE = 'TABLE',
  DATA_TRANSFORMATION = 'DATA_TRANSFORMATION',
  // DATA_TRANSFORM = 'DATA_TRANSFORMATION', // Alias para compatibilidade - comentado para evitar erro de compilação
  PIVOT_TABLE = 'PIVOT_TABLE',
  CONDITIONAL_FORMAT = 'CONDITIONAL_FORMAT',
  // CONDITIONAL_FORMATTING = 'CONDITIONAL_FORMAT', // Alias para compatibilidade - comentado para evitar erro de compilação
  ADVANCED_CHART = 'ADVANCED_CHART',
  ADVANCED_VISUALIZATION = 'ADVANCED_VISUALIZATION',
  // Operações adicionais
  RANGE_UPDATE = 'RANGE_UPDATE',
  CELL_MERGE = 'CELL_MERGE',
  CELL_SPLIT = 'CELL_SPLIT',
  NAMED_RANGE = 'NAMED_RANGE',
  VALIDATION = 'VALIDATION',
  FREEZE_PANES = 'FREEZE_PANES',
  SHEET_OPERATION = 'SHEET_OPERATION',
  // Operações para análises avançadas (anteriormente usadas como extensões)
  ANALYSIS = 'ANALYSIS', // Mudado de 'FORMULA' para 'ANALYSIS' para evitar duplicação
  GENERIC = 'GENERIC', // Mudado de 'TABLE' para 'GENERIC' para evitar duplicação
}

// Tipos de gráficos suportados
export enum ChartType {
  LINE = 'LINE',
  BAR = 'BAR',
  COLUMN = 'COLUMN',
  AREA = 'AREA',
  SCATTER = 'SCATTER',
  PIE = 'PIE',
}

// Operadores de filtro
export enum FilterOperator {
  EQUALS = 'equals',
  NOT_EQUALS = 'notEquals',
  GREATER_THAN = 'greaterThan',
  LESS_THAN = 'lessThan',
  GREATER_THAN_OR_EQUAL = 'greaterThanOrEqual',
  LESS_THAN_OR_EQUAL = 'lessThanOrEqual',
  CONTAINS = 'contains',
  NOT_CONTAINS = 'notContains',
  BEGINS_WITH = 'beginsWith',
  ENDS_WITH = 'endsWith',
  BETWEEN = 'between',
}

// Estado da conexão WebSocket
export enum WebSocketStatus {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  ERROR = 'error',
}

// Interface principal para operações Excel
export interface ExcelOperation {
  type: string;
  data?: any;
  id?: string;
  command?: string;
  parameters?: Record<string, any>;
}

// Interfaces para tipos de operação específicos
export interface FormulaOperationData {
  cell: string;
  formula: string;
  description?: string;
}

export interface ColumnOperationData {
  column?: string;
  columnName?: string;
  columnIndex?: number;
  operation?: string;
  targetCell?: string;
  description?: string;
}

export interface ChartOperationData {
  type: string;
  title?: string;
  dataRange?: {
    columns?: string[];
    startRow?: number;
    endRow?: number;
  };
  options?: {
    showLegend?: boolean;
    xAxisTitle?: string;
    yAxisTitle?: string;
    colors?: string[];
  };
}

export interface FilterOperationData {
  column: string | number;
  operator: FilterOperator;
  value: any;
  value2?: any;
}

export interface SortOperationData {
  column: string | number;
  direction: 'asc' | 'desc';
}

export interface TableOperation {
  range?: string;
  hasHeaders?: boolean;
  name?: string;
  style?: string;
}

export interface FormatOperation {
  range?: string;
  format?: {
    type?: string;
    style?: {
      bold?: boolean;
      italic?: boolean;
      underline?: boolean;
      color?: string;
      backgroundColor?: string;
    };
    numberFormat?: string;
  };
}

export interface CellUpdateOperation {
  cell?: string;
  value?: any;
}

export interface PivotTableOperation {
  sourceRange?: string;
  destinationRange?: string;
  rowFields?: string[];
  columnFields?: string[];
  dataFields?: string[];
}

// Interface para mensagens WebSocket
export interface WebSocketMessage {
  id: string;
  type: string;
  payload?: any;
  error?: any;
}

// Interface para resultado de operações
export interface OperationResult {
  success: boolean;
  message?: string;
  data?: any;
  error?: string;
}

// Interface para resultados do parser de comandos
export interface CommandParserResult {
  operations: ExcelOperation[];
  error?: string;
  message?: string;
  success: boolean;
}

// Tipos adicionais que podem ser necessários
export type WebSocketMessageType =
  | 'CONNECT'
  | 'DISCONNECT'
  | 'OPERATION'
  | 'RESPONSE'
  | 'ERROR'
  | 'EVENT'
  | 'STATUS';

// Interfaces para estruturas de dados
export interface WorkbookData {
  id: string;
  name?: string;
  sheets?: SheetData[];
}

export interface SheetData {
  id: string;
  name?: string;
  workbookId?: string;
  data?: any;
}

export interface ChartData {
  id: string;
  type: string;
  title?: string;
  data?: any;
}

export interface WorkbookInfo {
  id: string;
  name?: string;
  sheets?: WorksheetInfo[];
}

export interface WorksheetInfo {
  id: string;
  name?: string;
}

export interface WorksheetData {
  worksheetId: string;
  worksheetName?: string;
  data?: any;
}

export interface ChartInfo {
  id: string;
  type: string;
  worksheetId?: string;
}

export interface CreateChartOptions {
  type: string;
  worksheetId: string;
  range?: string;
  title?: string;
}

export interface DataAnalysisResult {
  type: string;
  data: any;
}

// Interfaces completas para operações específicas
export interface FilterOperation extends FilterOperationData {
  type: string;
}
export interface SortOperation extends SortOperationData {
  type: string;
}
export interface ChartOperation extends ChartOperationData {
  type: string;
}

// Tipos de erros Excel
export enum ExcelErrorType {
  FORMULA_ERROR = 'FORMULA_ERROR',
  REFERENCE_ERROR = 'REFERENCE_ERROR',
  VALUE_ERROR = 'VALUE_ERROR',
  NAME_ERROR = 'NAME_ERROR',
  RANGE_ERROR = 'RANGE_ERROR',
  SYNTAX_ERROR = 'SYNTAX_ERROR',
  DATA_VALIDATION_ERROR = 'DATA_VALIDATION_ERROR',
  FORMAT_ERROR = 'FORMAT_ERROR',
  OPERATION_NOT_SUPPORTED = 'OPERATION_NOT_SUPPORTED',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
}
