import { ENV } from '@/config/unified-environment';

/**
 * Configurações temporárias para autenticação durante o desenvolvimento
 *
 * Este arquivo fornece valores padrão seguros quando o .env.local não está disponível.
 * Em produção, essas variáveis devem vir de variáveis de ambiente seguras.
 */

// Chave secreta para JWT durante desenvolvimento
export const DEVELOPMENT_AUTH_SECRET = 'supersecret_development_key_do_not_use_in_production';

// URL base do aplicativo
export const APP_URL = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';

/**
 * Recupera o secret usado para autenticação
 * Importante para validação e assinatura de JWT
 */
export function getAuthSecret(): string {
  // Verificar se o secret está definido
  if (!ENV.NEXTAUTH_SECRET) {
    if (ENV.IS_DEVELOPMENT) {
      // Usar um valor fixo para desenvolvimento local
      console.warn(
        'NEXTAUTH_SECRET não está definido no ambiente de desenvolvimento. Usando valor padrão.'
      );
      return 'development-auth-secret-insecure-do-not-use-in-production';
    } else {
      // Em produção, isso é um erro crítico
      throw new Error(
        'NEXTAUTH_SECRET não está definido! Esta variável é necessária para autenticação segura.'
      );
    }
  }

  return ENV.NEXTAUTH_SECRET;
}

/**
 * Recupera a URL base para autenticação
 */
export function getAuthUrl(): string {
  // Verificar se a URL está definida
  if (!ENV.NEXTAUTH_URL) {
    if (ENV.IS_DEVELOPMENT) {
      // Usar localhost em desenvolvimento
      return 'http://localhost:3000';
    } else {
      // Em produção, isso é um erro crítico
      throw new Error('NEXTAUTH_URL não está definido!');
    }
  }

  return ENV.NEXTAUTH_URL;
}

// Obtém a chave secreta para JWT
export function getJwtSecret(): string {
  // Em desenvolvimento, usa a chave fixa se JWT_SECRET não estiver definido
  if (process.env.NODE_ENV === 'development') {
    return process.env.JWT_SECRET || DEVELOPMENT_AUTH_SECRET;
  }

  // Em produção, usa JWT_SECRET ou cai para NEXTAUTH_SECRET
  return process.env.JWT_SECRET || getAuthSecret();
}
