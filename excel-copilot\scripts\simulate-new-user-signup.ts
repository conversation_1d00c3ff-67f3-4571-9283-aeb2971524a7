#!/usr/bin/env tsx

/**
 * Script para simular o processo de registro de um novo usuário
 * Demonstra o funcionamento do callback signIn do NextAuth.js
 */

import { config } from 'dotenv';
import { resolve } from 'path';
import { PrismaClient } from '@prisma/client';

// Carregar variáveis de ambiente
config({ path: resolve(process.cwd(), '.env.local') });

// Constantes locais
const PLANS = {
  FREE: 'free',
  PRO_MONTHLY: 'pro_monthly',
  PRO_ANNUAL: 'pro_annual',
};

const API_CALL_LIMITS = {
  [PLANS.FREE]: 50,
  [PLANS.PRO_MONTHLY]: 500,
  [PLANS.PRO_ANNUAL]: 1000,
};

const prisma = new PrismaClient();

/**
 * Simula o callback signIn do NextAuth.js para um novo usuário
 */
async function simulateSignInCallback(userEmail: string, isNewUser: boolean = true): Promise<void> {
  console.log('🔐 SIMULANDO CALLBACK SIGNIN DO NEXTAUTH.JS');
  console.log('='.repeat(50));
  console.log(`📧 Email: ${userEmail}`);
  console.log(`🆕 Novo usuário: ${isNewUser ? 'Sim' : 'Não'}`);

  try {
    // 1. Simular criação do usuário (normalmente feito pelo PrismaAdapter)
    let user;

    if (isNewUser) {
      console.log('\n📝 Criando novo usuário...');
      user = await prisma.user.create({
        data: {
          email: userEmail,
          name: userEmail.split('@')[0] || null,
          emailVerified: new Date(),
        },
      });
      console.log(`✅ Usuário criado: ${user.id}`);
    } else {
      user = await prisma.user.findUnique({
        where: { email: userEmail },
      });
      if (!user) {
        throw new Error('Usuário não encontrado');
      }
      console.log(`✅ Usuário existente encontrado: ${user.id}`);
    }

    // 2. Simular o callback signIn implementado
    console.log('\n🔄 Executando callback signIn...');

    if (isNewUser && user.id) {
      console.log(`[AUTH] Novo usuário detectado: ${user.email} (ID: ${user.id})`);

      // Verificar se já existe uma assinatura (prevenção de duplicação)
      const existingSubscription = await prisma.subscription.findFirst({
        where: { userId: user.id },
      });

      if (!existingSubscription) {
        console.log('📋 Criando assinatura Free automaticamente...');

        // Criar assinatura Free para o novo usuário
        const freeSubscription = await prisma.subscription.create({
          data: {
            userId: user.id,
            plan: PLANS.FREE,
            status: 'active',
            apiCallsLimit: API_CALL_LIMITS[PLANS.FREE] || 50,
            apiCallsUsed: 0,
            currentPeriodStart: new Date(),
            cancelAtPeriodEnd: false,
          },
        });

        console.log(`✅ Assinatura Free criada automaticamente para usuário ${user.email}:`);
        console.log(`   📋 Subscription ID: ${freeSubscription.id}`);
        console.log(`   🎯 Plano: ${freeSubscription.plan}`);
        console.log(`   📊 API Calls Limit: ${freeSubscription.apiCallsLimit}`);
        console.log(`   📅 Criada em: ${freeSubscription.createdAt.toISOString()}`);
      } else {
        console.log(`ℹ️  Usuário ${user.email} já possui assinatura existente:`);
        console.log(`   🎯 Plano: ${existingSubscription.plan}`);
        console.log(`   📊 Status: ${existingSubscription.status}`);
      }
    }

    // 3. Verificar resultado final
    console.log('\n🔍 Verificando resultado final...');

    const finalUser = await prisma.user.findUnique({
      where: { id: user.id },
      include: {
        subscriptions: {
          orderBy: { createdAt: 'desc' },
        },
      },
    });

    if (finalUser) {
      console.log(`✅ Usuário final:`);
      console.log(`   📧 Email: ${finalUser.email}`);
      console.log(`   🆔 ID: ${finalUser.id}`);
      console.log(`   📋 Assinaturas: ${finalUser.subscriptions.length}`);

      if (finalUser.subscriptions.length > 0) {
        const subscription = finalUser.subscriptions[0];
        if (subscription) {
          console.log(`   🎯 Plano atual: ${subscription.plan}`);
          console.log(`   📊 Status: ${subscription.status}`);
          console.log(
            `   📞 API Calls: ${subscription.apiCallsUsed}/${subscription.apiCallsLimit}`
          );
        }
      }
    }

    console.log('\n✅ SIMULAÇÃO CONCLUÍDA COM SUCESSO!');
    console.log('🎉 Callback signIn funcionando corretamente');
  } catch (error) {
    console.error('💥 Erro durante a simulação:', error);
    throw error;
  }
}

/**
 * Testa diferentes cenários de login
 */
async function testLoginScenarios(): Promise<void> {
  console.log('🧪 TESTANDO CENÁRIOS DE LOGIN');
  console.log('='.repeat(50));

  try {
    // Cenário 1: Novo usuário
    console.log('\n📋 CENÁRIO 1: NOVO USUÁRIO');
    console.log('-'.repeat(30));
    await simulateSignInCallback('<EMAIL>', true);

    // Cenário 2: Usuário existente (segundo login)
    console.log('\n📋 CENÁRIO 2: USUÁRIO EXISTENTE (SEGUNDO LOGIN)');
    console.log('-'.repeat(30));
    await simulateSignInCallback('<EMAIL>', false);

    // Cenário 3: Verificar integridade final
    console.log('\n📋 CENÁRIO 3: VERIFICAÇÃO DE INTEGRIDADE');
    console.log('-'.repeat(30));

    const totalUsers = await prisma.user.count();
    const usersWithSubscription = await prisma.user.count({
      where: {
        subscriptions: {
          some: {},
        },
      },
    });

    console.log(`📊 Total de usuários: ${totalUsers}`);
    console.log(`📊 Usuários com assinatura: ${usersWithSubscription}`);
    console.log(
      `📊 Integridade: ${totalUsers > 0 ? ((usersWithSubscription / totalUsers) * 100).toFixed(1) : 0}%`
    );

    if (usersWithSubscription === totalUsers) {
      console.log('✅ TODOS OS USUÁRIOS POSSUEM ASSINATURA!');
    } else {
      console.log('⚠️  Alguns usuários não possuem assinatura');
    }
  } catch (error) {
    console.error('💥 Erro nos testes:', error);
  }
}

/**
 * Limpa dados de teste
 */
async function cleanup(): Promise<void> {
  console.log('\n🧹 LIMPANDO DADOS DE TESTE...');

  try {
    // Remover usuário de teste criado
    const testUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' },
      include: { subscriptions: true },
    });

    if (testUser) {
      // Remover assinaturas primeiro (devido à foreign key)
      await prisma.subscription.deleteMany({
        where: { userId: testUser.id },
      });

      // Remover usuário
      await prisma.user.delete({
        where: { id: testUser.id },
      });

      console.log('✅ Dados de teste removidos');
    } else {
      console.log('ℹ️  Nenhum dado de teste para remover');
    }
  } catch (error) {
    console.log('⚠️  Erro ao limpar dados de teste:', error);
  }
}

async function main(): Promise<void> {
  try {
    await testLoginScenarios();

    // Perguntar se deve limpar os dados de teste
    console.log('\n❓ Deseja limpar os dados de teste? (Pressione Ctrl+C para manter)');

    // Aguardar 3 segundos e depois limpar
    await new Promise(resolve => setTimeout(resolve, 3000));
    await cleanup();
  } catch (error) {
    console.error('💥 Erro na execução:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Executar script se chamado diretamente
if (require.main === module) {
  main().catch(console.error);
}

export { simulateSignInCallback, testLoginScenarios };
