import { NextRequest, NextResponse } from 'next/server';

import { logger } from '@/lib/logger';
import { SupabaseMonitoringService, SupabaseStorageObject } from '@/lib/supabase-integration';
import { apiRateLimiter } from '@/middleware/rate-limit';
import { ApiResponse } from '@/utils/api-response';

// Forçar o modo dinâmico para essa rota
export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

/**
 * GET /api/supabase/storage
 * Obtém informações sobre o storage do Supabase
 */
export async function GET(request: NextRequest) {
  try {
    // Aplicar rate limiting
    const rateLimitResult = await apiRateLimiter(request, new NextResponse());
    if (rateLimitResult) {
      return rateLimitResult;
    }

    // Verificar se temos as credenciais necessárias
    const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    const projectUrl = process.env.SUPABASE_URL;

    if (!serviceRoleKey || !projectUrl) {
      return ApiResponse.error(
        'Credenciais do Supabase não configuradas',
        'SUPABASE_NOT_CONFIGURED',
        500
      );
    }

    // Criar instância do serviço de monitoramento
    const supabaseService = new SupabaseMonitoringService({
      serviceRoleKey,
      projectUrl,
    });

    // Obter parâmetros da query
    const { searchParams } = new URL(request.url);
    const bucketName = searchParams.get('bucket');
    const includeObjects = searchParams.get('objects') === 'true';
    const path = searchParams.get('path') || '';

    if (bucketName) {
      // Obter conteúdo de um bucket específico
      const bucketContents = await supabaseService.getBucketContents(bucketName, path);

      const response = {
        bucket: bucketContents.bucket
          ? {
              name: bucketContents.bucket.name,
              public: bucketContents.bucket.public,
              file_size_limit: bucketContents.bucket.file_size_limit,
              allowed_mime_types: bucketContents.bucket.allowed_mime_types,
              created_at: bucketContents.bucket.created_at,
              updated_at: bucketContents.bucket.updated_at,
            }
          : null,
        summary: {
          totalObjects: bucketContents.totalObjects,
          totalSize: formatBytes(bucketContents.totalSize),
          path: path || '/',
        },
        objects: includeObjects
          ? bucketContents.objects.map(obj => ({
              name: obj.name,
              id: obj.id,
              size: formatBytes(obj.metadata?.size || 0),
              mimetype: obj.metadata?.mimetype,
              created_at: obj.created_at,
              updated_at: obj.updated_at,
              last_accessed_at: obj.last_accessed_at,
            }))
          : [],
        timestamp: new Date().toISOString(),
      };

      logger.info('Conteúdo do bucket Supabase obtido com sucesso', {
        bucket: bucketName,
        totalObjects: bucketContents.totalObjects,
        path,
      });

      return ApiResponse.success(response);
    } else {
      // Obter resumo geral do storage
      const storageSummary = await supabaseService.getStorageSummary();

      const response = {
        summary: {
          totalBuckets: storageSummary.totalBuckets,
          publicBuckets: storageSummary.publicBuckets,
          privateBuckets: storageSummary.privateBuckets,
          totalObjects: storageSummary.bucketDetails.reduce(
            (acc, detail) => acc + detail.objectCount,
            0
          ),
          totalSize: formatBytes(
            storageSummary.bucketDetails.reduce((acc, detail) => acc + detail.totalSize, 0)
          ),
        },
        buckets: storageSummary.bucketDetails.map(detail => ({
          name: detail.bucket.name,
          public: detail.bucket.public,
          objectCount: detail.objectCount,
          totalSize: formatBytes(detail.totalSize),
          file_size_limit: detail.bucket.file_size_limit,
          allowed_mime_types: detail.bucket.allowed_mime_types,
          created_at: detail.bucket.created_at,
          updated_at: detail.bucket.updated_at,
        })),
        timestamp: new Date().toISOString(),
      };

      logger.info('Resumo do storage Supabase obtido com sucesso', {
        totalBuckets: storageSummary.totalBuckets,
        totalObjects: response.summary.totalObjects,
      });

      return ApiResponse.success(response);
    }
  } catch (error) {
    logger.error('Erro ao obter storage do Supabase', { error });

    if (error instanceof Error) {
      return ApiResponse.error(
        `Erro ao acessar storage: ${error.message}`,
        'SUPABASE_STORAGE_ERROR',
        500
      );
    }

    return ApiResponse.error('Erro interno do servidor', 'INTERNAL_ERROR', 500);
  }
}

/**
 * POST /api/supabase/storage
 * Executa operações específicas no storage (limpeza, análise, etc.)
 */
export async function POST(request: NextRequest) {
  try {
    // Aplicar rate limiting
    const rateLimitResult = await apiRateLimiter(request, new NextResponse());
    if (rateLimitResult) {
      return rateLimitResult;
    }

    const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    const projectUrl = process.env.SUPABASE_URL;

    if (!serviceRoleKey || !projectUrl) {
      return ApiResponse.error(
        'Credenciais do Supabase não configuradas',
        'SUPABASE_NOT_CONFIGURED',
        500
      );
    }

    const body = await request.json();
    const { action, bucketName, options: _options } = body;

    const supabaseService = new SupabaseMonitoringService({
      serviceRoleKey,
      projectUrl,
    });

    switch (action) {
      case 'analyze_bucket':
        // Análise detalhada de um bucket específico
        if (!bucketName) {
          return ApiResponse.error(
            'Nome do bucket é obrigatório para análise',
            'MISSING_BUCKET_NAME',
            400
          );
        }

        {
          const bucketContents = await supabaseService.getBucketContents(bucketName);

          if (!bucketContents.bucket) {
            return ApiResponse.error(
              `Bucket '${bucketName}' não encontrado`,
              'BUCKET_NOT_FOUND',
              404
            );
          }

          // Analisar tipos de arquivo
          const fileTypes = new Map<string, { count: number; totalSize: number }>();
          let oldestFile: SupabaseStorageObject | null = null;
          let newestFile: SupabaseStorageObject | null = null;
          let largestFile: SupabaseStorageObject | null = null;

          bucketContents.objects.forEach(obj => {
            const mimetype = obj.metadata?.mimetype || 'unknown';
            const size = obj.metadata?.size || 0;
            const createdAt = new Date(obj.created_at);

            // Contar tipos de arquivo
            const current = fileTypes.get(mimetype) || { count: 0, totalSize: 0 };
            fileTypes.set(mimetype, {
              count: current.count + 1,
              totalSize: current.totalSize + size,
            });

            // Encontrar arquivos mais antigos/novos/maiores
            if (!oldestFile || createdAt < new Date(oldestFile.created_at)) {
              oldestFile = obj;
            }
            if (!newestFile || createdAt > new Date(newestFile.created_at)) {
              newestFile = obj;
            }
            if (!largestFile || size > (largestFile.metadata?.size || 0)) {
              largestFile = obj;
            }
          });

          const analysis = {
            bucket: {
              name: bucketContents.bucket.name,
              public: bucketContents.bucket.public,
              file_size_limit: bucketContents.bucket.file_size_limit,
              allowed_mime_types: bucketContents.bucket.allowed_mime_types,
            },
            statistics: {
              totalObjects: bucketContents.totalObjects,
              totalSize: formatBytes(bucketContents.totalSize),
              averageFileSize:
                bucketContents.totalObjects > 0
                  ? formatBytes(bucketContents.totalSize / bucketContents.totalObjects)
                  : '0 Bytes',
            },
            fileTypes: Array.from(fileTypes.entries())
              .map(([mimetype, stats]) => ({
                mimetype,
                count: stats.count,
                totalSize: formatBytes(stats.totalSize),
                percentage: ((stats.count / bucketContents.totalObjects) * 100).toFixed(1),
              }))
              .sort((a, b) => b.count - a.count),
            files: {
              oldest: oldestFile
                ? {
                    name: (oldestFile as SupabaseStorageObject).name,
                    size: formatBytes((oldestFile as SupabaseStorageObject).metadata?.size || 0),
                    created_at: (oldestFile as SupabaseStorageObject).created_at,
                  }
                : null,
              newest: newestFile
                ? {
                    name: (newestFile as SupabaseStorageObject).name,
                    size: formatBytes((newestFile as SupabaseStorageObject).metadata?.size || 0),
                    created_at: (newestFile as SupabaseStorageObject).created_at,
                  }
                : null,
              largest: largestFile
                ? {
                    name: (largestFile as SupabaseStorageObject).name,
                    size: formatBytes((largestFile as SupabaseStorageObject).metadata?.size || 0),
                    created_at: (largestFile as SupabaseStorageObject).created_at,
                  }
                : null,
            },
            recommendations: [] as Array<{
              type: string;
              priority: string;
              message: string;
              action: string;
            }>,
          };

          // Gerar recomendações
          const recommendations = [];

          if (bucketContents.totalObjects > 10000) {
            recommendations.push({
              type: 'performance',
              priority: 'medium',
              message: 'Bucket tem muitos objetos, considere organizar em subpastas',
              action: 'organize_files',
            });
          }

          if (bucketContents.totalSize > 1024 * 1024 * 1024 * 5) {
            // 5GB
            recommendations.push({
              type: 'cost',
              priority: 'medium',
              message: 'Bucket está usando muito espaço, considere arquivar arquivos antigos',
              action: 'archive_old_files',
            });
          }

          if (
            !bucketContents.bucket.public &&
            bucketContents.bucket.allowed_mime_types?.length === 0
          ) {
            recommendations.push({
              type: 'security',
              priority: 'low',
              message: 'Considere definir tipos de arquivo permitidos para maior segurança',
              action: 'set_mime_restrictions',
            });
          }

          analysis.recommendations = recommendations;

          return ApiResponse.success({
            action: 'bucket_analyzed',
            bucketName,
            analysis,
            timestamp: new Date().toISOString(),
          });
        }

      case 'refresh_storage': {
        // Forçar atualização das informações de storage
        const refreshedSummary = await supabaseService.getStorageSummary();

        return ApiResponse.success({
          action: 'storage_refreshed',
          summary: {
            totalBuckets: refreshedSummary.totalBuckets,
            publicBuckets: refreshedSummary.publicBuckets,
            privateBuckets: refreshedSummary.privateBuckets,
            buckets: refreshedSummary.buckets.map(bucket => ({
              name: bucket.name,
              public: bucket.public,
              created_at: bucket.created_at,
            })),
          },
          timestamp: new Date().toISOString(),
        });
      }

      default:
        return ApiResponse.error(`Ação '${action}' não suportada`, 'UNSUPPORTED_ACTION', 400);
    }
  } catch (error) {
    logger.error('Erro no POST storage Supabase', { error });

    if (error instanceof Error) {
      return ApiResponse.error(
        `Erro na operação: ${error.message}`,
        'SUPABASE_OPERATION_ERROR',
        500
      );
    }

    return ApiResponse.error('Erro interno do servidor', 'INTERNAL_ERROR', 500);
  }
}

/**
 * Formata bytes em formato legível
 */
function formatBytes(bytes: number): string {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
