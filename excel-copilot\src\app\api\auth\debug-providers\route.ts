import { NextRequest, NextResponse } from 'next/server';

import { ENV } from '@/config/unified-environment';

export const dynamic = 'force-dynamic';

export async function GET(_request: NextRequest) {
  try {
    // Debug providers logging

    // Verificar as condições que determinam quais provedores usar
    const skipAuthProviders = ENV.FEATURES.SKIP_AUTH_PROVIDERS;
    const isDevelopment = ENV.IS_DEVELOPMENT;
    const skipAuthProvidersEnv = process.env.AUTH_SKIP_PROVIDERS;
    const nodeEnv = process.env.NODE_ENV;

    // Replicar a lógica exata do arquivo de opções
    const shouldUseDevProviders =
      skipAuthProviders || (isDevelopment && process.env.AUTH_SKIP_PROVIDERS !== 'false');

    const debugInfo = {
      timestamp: new Date().toISOString(),
      environment: nodeEnv,

      conditions: {
        'ENV.FEATURES.SKIP_AUTH_PROVIDERS': skipAuthProviders,
        'ENV.IS_DEVELOPMENT': isDevelopment,
        'process.env.AUTH_SKIP_PROVIDERS': skipAuthProvidersEnv,
        'process.env.NODE_ENV': nodeEnv,
        shouldUseDevProviders: shouldUseDevProviders,
      },

      envFeatures: {
        SKIP_AUTH_PROVIDERS: ENV.FEATURES.SKIP_AUTH_PROVIDERS,
        USE_MOCK_AI: ENV.FEATURES.USE_MOCK_AI,
        ENABLE_REALTIME_COLLABORATION: ENV.FEATURES.ENABLE_REALTIME_COLLABORATION,
        ENABLE_DESKTOP_INTEGRATION: ENV.FEATURES.ENABLE_DESKTOP_INTEGRATION,
        ENABLE_STRIPE_INTEGRATION: ENV.FEATURES.ENABLE_STRIPE_INTEGRATION,
      } as Record<string, boolean>,

      envFlags: {
        IS_DEVELOPMENT: ENV.IS_DEVELOPMENT,
        IS_PRODUCTION: ENV.IS_PRODUCTION,
        IS_TEST: ENV.IS_TEST,
        IS_SERVER: ENV.IS_SERVER,
      } as Record<string, boolean>,

      oauthCredentials: {
        googleClientId: {
          exists: !!process.env.AUTH_GOOGLE_CLIENT_ID,
          length: process.env.AUTH_GOOGLE_CLIENT_ID?.length || 0,
          preview: process.env.AUTH_GOOGLE_CLIENT_ID
            ? `${process.env.AUTH_GOOGLE_CLIENT_ID.substring(0, 10)}...`
            : 'undefined',
        },
        googleClientSecret: {
          exists: !!process.env.AUTH_GOOGLE_CLIENT_SECRET,
          length: process.env.AUTH_GOOGLE_CLIENT_SECRET?.length || 0,
          preview: process.env.AUTH_GOOGLE_CLIENT_SECRET
            ? `${process.env.AUTH_GOOGLE_CLIENT_SECRET.substring(0, 10)}...`
            : 'undefined',
        },
        githubClientId: {
          exists: !!process.env.AUTH_GITHUB_CLIENT_ID,
          length: process.env.AUTH_GITHUB_CLIENT_ID?.length || 0,
          preview: process.env.AUTH_GITHUB_CLIENT_ID
            ? `${process.env.AUTH_GITHUB_CLIENT_ID.substring(0, 10)}...`
            : 'undefined',
        },
        githubClientSecret: {
          exists: !!process.env.AUTH_GITHUB_CLIENT_SECRET,
          length: process.env.AUTH_GITHUB_CLIENT_SECRET?.length || 0,
          preview: process.env.AUTH_GITHUB_CLIENT_SECRET
            ? `${process.env.AUTH_GITHUB_CLIENT_SECRET.substring(0, 10)}...`
            : 'undefined',
        },
      },

      recommendation: shouldUseDevProviders
        ? 'Usando provedores de desenvolvimento - OAuth social desabilitado'
        : 'Deveria usar provedores OAuth sociais (Google/GitHub)',

      issues: [] as string[],
    };

    // Identificar problemas
    if (shouldUseDevProviders && ENV.IS_PRODUCTION) {
      debugInfo.issues.push('Provedores de desenvolvimento ativados em produção');
    }

    if (!shouldUseDevProviders) {
      if (!process.env.AUTH_GOOGLE_CLIENT_ID) {
        debugInfo.issues.push('GOOGLE_CLIENT_ID não configurado');
      }
      if (!process.env.AUTH_GOOGLE_CLIENT_SECRET) {
        debugInfo.issues.push('GOOGLE_CLIENT_SECRET não configurado');
      }
      if (!process.env.AUTH_GITHUB_CLIENT_ID) {
        debugInfo.issues.push('GITHUB_CLIENT_ID não configurado');
      }
      if (!process.env.AUTH_GITHUB_CLIENT_SECRET) {
        debugInfo.issues.push('GITHUB_CLIENT_SECRET não configurado');
      }
    }

    // Debug providers completed

    return NextResponse.json(debugInfo, {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
      },
    });
  } catch (error) {
    // Error handling for debug providers

    return NextResponse.json(
      {
        error: 'Erro interno durante debug de provedores',
        message: error instanceof Error ? error.message : 'Erro desconhecido',
        stack: error instanceof Error ? error.stack : undefined,
        timestamp: new Date().toISOString(),
      },
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
}
