import { Loader2 } from 'lucide-react';
import { Metadata } from 'next';
import { notFound, redirect } from 'next/navigation';
import { Session } from 'next-auth';
import { getServerSession } from 'next-auth/next';
import { Suspense } from 'react';

import { SpreadsheetEditor } from '@/components/workbook/SpreadsheetEditor';
import { prisma } from '@/server/db/client';

// Definição de metadados dinâmicos para SEO
export async function generateMetadata({ params }: { params: { id: string } }): Promise<Metadata> {
  try {
    const workbook = await prisma.workbook.findUnique({
      where: {
        id: params.id,
      },
      select: {
        name: true,
        description: true,
      },
    });

    if (!workbook) {
      return {
        title: 'Planilha não encontrada',
      };
    }

    return {
      title: `${workbook.name} | Excel Copilot`,
      description: workbook.description || 'Edite sua planilha com comandos em linguagem natural',
    };
  } catch {
    return {
      title: '<PERSON><PERSON><PERSON> | Excel Copilot',
    };
  }
}

// Função para carregar dados da planilha
async function getWorkbookData(workbookId: string) {
  const session = (await getServerSession()) as Session | null;

  if (!session || !session.user) {
    redirect('/auth/signin?callbackUrl=/workbook/' + workbookId);
    return null;
  }

  try {
    const workbook = await prisma.workbook.findUnique({
      where: {
        id: workbookId,
        userId: session.user.id,
      },
      include: {
        sheets: true,
      },
    });

    if (!workbook) {
      return null;
    }

    return workbook;
  } catch (error) {
    console.error('Erro ao buscar workbook:', error);
    return null;
  }
}

export default async function WorkbookPage({
  params,
  searchParams
}: {
  params: { id: string };
  searchParams?: { command?: string };
}) {
  const workbook = await getWorkbookData(params.id);

  if (!workbook) {
    notFound();
  }

  // Extrair comando de IA da URL se presente
  const initialCommand = searchParams?.command;

  // Processar dados da planilha
  const mainSheet = workbook.sheets[0];
  // Definindo um valor padrão para spreadsheetData para garantir que nunca seja undefined
  let spreadsheetData = {
    headers: ['A', 'B', 'C'],
    rows: [
      ['', '', ''],
      ['', '', ''],
      ['', '', ''],
    ],
    charts: [],
    name: mainSheet?.name || 'Nova Planilha',
  };

  if (mainSheet && mainSheet.data) {
    try {
      const sheetData = JSON.parse(mainSheet.data);
      // Converter para formato esperado pelo editor
      spreadsheetData = {
        headers: sheetData.headers || ['A', 'B', 'C'],
        rows: sheetData.rows || [
          ['', '', ''],
          ['', '', ''],
          ['', '', ''],
        ],
        charts: sheetData.charts || [],
        name: mainSheet.name,
      };
    } catch (e) {
      console.error('Erro ao analisar dados da planilha:', e);
    }
  }

  return (
    <div className="w-full h-[calc(100vh-64px)] flex flex-col">
      <Suspense
        fallback={
          <div className="h-full w-full flex items-center justify-center">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <span className="ml-2">Carregando planilha...</span>
          </div>
        }
      >
        <SpreadsheetEditor
          workbookId={params.id}
          initialData={spreadsheetData}
          initialCommand={initialCommand}
        />
      </Suspense>
    </div>
  );
}
