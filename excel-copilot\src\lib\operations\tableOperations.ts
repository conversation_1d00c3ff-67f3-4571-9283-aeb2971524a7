import { safeArrayAccess } from '@/utils';
import { extractGroup } from '@/utils/regex-utils';

import { ExcelOperationType } from '../../types/index';
import { ExcelOperation } from '../excel/types';

/**
 * Interface para dados de operação de tabela
 */
export interface TableOperationData {
  range: string; // Range dos dados da tabela (ex: "A1:D10")
  hasHeaders?: boolean; // Se o primeiro row contém cabeçalhos
  tableName?: string; // Nome opcional para a tabela
  style?: string; // Estilo da tabela (ex: "TableStyleMedium2")
  firstRowAsHeaders?: boolean; // Se a primeira linha deve ser tratada como cabeçalhos
  totalsRow?: boolean; // Se a tabela deve ter uma linha de totais
  filterButtons?: boolean; // Se a tabela deve ter botões de filtro
}

/**
 * Executa uma operação de criação/manipulação de tabela
 * @param sheetData Dados da planilha
 * @param operation Operação a ser executada
 * @returns Dados atualizados e resumo da operação
 */
export async function executeTableOperation(
  sheetData: any,
  operation: ExcelOperation
): Promise<{ updatedData: any; resultSummary: string }> {
  try {
    const {
      range,
      hasHeaders: _hasHeaders = true,
      tableName,
      style = 'TableStyleMedium2',
      firstRowAsHeaders = true,
      totalsRow = false,
      filterButtons = true,
    } = operation.data as TableOperationData;

    if (!range) {
      throw new Error('Parâmetros insuficientes para operação de tabela');
    }

    // Clonar dados para não modificar o original diretamente
    const updatedData = { ...sheetData };

    // Garantir que temos um array de tabelas
    if (!updatedData.tables) {
      updatedData.tables = [];
    }

    // Extrair coordenadas do range
    const rangeCoords = parseRange(range);
    const { startRow: _startRow, startCol, endRow: _endRow, endCol } = rangeCoords;

    // Extrair dados do range
    const tableData = extractTableData(updatedData, rangeCoords);

    // Determinar cabeçalhos
    let headers = [];
    if (firstRowAsHeaders && tableData.length > 0 && tableData[0]) {
      headers = tableData[0].map(String);
      tableData.shift(); // Remover a linha de cabeçalhos dos dados
    } else {
      // Gerar cabeçalhos automaticamente (Coluna1, Coluna2, etc.)
      headers = Array.from({ length: endCol - startCol + 1 }, (_, i) => `Coluna${i + 1}`);
    }

    // Gerar ID único para a tabela
    const tableId = tableName || `Table_${Date.now()}`;

    // Criar objeto de tabela
    const table = {
      id: tableId,
      range,
      rangeCoords,
      headers,
      data: tableData,
      style,
      hasFilterButtons: filterButtons,
      hasTotalsRow: totalsRow,
    };

    // Adicionar a tabela à planilha
    updatedData.tables.push(table);

    // Atualizar metadados da planilha
    if (!updatedData.metadata) {
      updatedData.metadata = {};
    }

    if (!updatedData.metadata.tableRanges) {
      updatedData.metadata.tableRanges = [];
    }

    updatedData.metadata.tableRanges.push({
      id: tableId,
      range,
      name: tableName,
    });

    // Registrar histórico de alterações
    if (!updatedData.history) {
      updatedData.history = [];
    }
    updatedData.history.push({
      type: ExcelOperationType.TABLE,
      tableId,
      range,
      timestamp: Date.now(),
    });

    // Resumo da operação
    const resultSummary = `Tabela ${tableName || 'sem nome'} criada no range ${range}`;

    return { updatedData, resultSummary };
  } catch (error) {
    console.error('Erro ao executar operação de tabela:', error);
    throw new Error(
      `Falha ao criar tabela: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
    );
  }
}

/**
 * Extrai dados de uma tabela a partir do range especificado
 * @param sheetData Dados da planilha
 * @param rangeCoords Coordenadas do range
 * @returns Dados da tabela no formato de matriz
 */
function extractTableData(
  sheetData: any,
  rangeCoords: { startRow: number; startCol: number; endRow: number; endCol: number }
): any[][] {
  const { startRow, startCol, endRow, endCol } = rangeCoords;
  const tableData = [];

  // Garantir que temos dados nas linhas
  if (!sheetData.rows || sheetData.rows.length === 0) {
    return [];
  }

  // Extrair dados do range (1-indexed para 0-indexed)
  for (let row = startRow - 1; row < endRow; row++) {
    if (row >= sheetData.rows.length) break;

    const rowData = [];
    for (let col = startCol - 1; col < endCol; col++) {
      // Se a célula existir, obter seu valor
      if (sheetData.rows[row] && col < sheetData.rows[row].length) {
        rowData.push(sheetData.rows[row][col]);
      } else {
        // Caso contrário, inserir célula vazia
        rowData.push(null);
      }
    }
    tableData.push(rowData);
  }

  return tableData;
}

/**
 * Cria uma tabela a partir de dados
 * @param data Dados da tabela
 * @param hasHeaders Se os dados possuem cabeçalhos
 * @returns Objeto com dados da tabela e cabeçalhos
 */
export function createTableFromData(data: any[][], hasHeaders: boolean = true): any {
  let headers: string[] = [];
  let tableData: any[][] = [];

  if (data.length === 0) {
    return { headers: [], tableData: [] };
  }

  // Para tabelas com cabeçalhos, extrair a primeira linha
  if (hasHeaders) {
    const headerRow = safeArrayAccess(data, 0);
    headers = headerRow ? headerRow.map(String) : [];
    tableData = data.slice(1);
  } else {
    // Para tabelas sem cabeçalhos, gerar cabeçalhos automáticos (A, B, C...)
    const columnCount = data[0]?.length || 0;
    headers = Array.from({ length: columnCount }, (_, i) => String.fromCharCode(65 + i));
    tableData = [...data];
  }

  return { headers, tableData };
}

/**
 * Converte range (ex: "A1:C3") para índices de linha e coluna
 * @param range Range de células (ex: "A1:C3")
 * @returns Índices de linha e coluna
 */
function parseRange(range: string): {
  startRow: number;
  startCol: number;
  endRow: number;
  endCol: number;
} {
  // Dividir o range em células de início e fim
  const parts = range.split(':');
  if (parts.length !== 2) {
    throw new Error(`Range inválido: ${range}`);
  }

  // Converter cada célula para índices
  const start = parseCellReference(safeArrayAccess(parts, 0) || '');
  const end = parseCellReference(safeArrayAccess(parts, 1) || '');

  return {
    startRow: start.row,
    startCol: start.col,
    endRow: end.row,
    endCol: end.col,
  };
}

/**
 * Converte referência de célula (ex: "A1") para índices de linha e coluna
 * @param cellRef Referência de célula (ex: "A1")
 * @returns Índices de linha e coluna
 */
function parseCellReference(cellRef: string): { row: number; col: number } {
  // Extrair parte alfabética (coluna) e numérica (linha)
  const match = cellRef.match(/([A-Za-z]+)([0-9]+)/);
  if (!match) {
    throw new Error(`Referência de célula inválida: ${cellRef}`);
  }

  const colStr = extractGroup(match, 1).toUpperCase();
  const rowStr = extractGroup(match, 2);

  // Converter coluna de alfabética para numérica (A=1, B=2, ...)
  let colNum = 0;
  for (let i = 0; i < colStr.length; i++) {
    colNum = colNum * 26 + (colStr.charCodeAt(i) - 64);
  }

  // Converter linha para número
  const rowNum = parseInt(rowStr, 10);

  return { row: rowNum, col: colNum };
}

/**
 * Extrai operações de tabela do texto da resposta de IA
 * @param response Resposta da IA
 * @returns Array de operações de tabela
 */
export function extractTableOperations(response: string): ExcelOperation[] {
  const operations: ExcelOperation[] = [];

  // Verificar padrões de criação de tabelas estruturados
  const tablePattern =
    /OPERAÇÃO:\s*TABELA[\s\S]*?RANGE:\s*([^\n]+)(?:[\s\S]*?NOME:\s*([^\n]+))?(?:[\s\S]*?ESTILO:\s*([^\n]+))?(?:[\s\S]*?CABEÇALHOS:\s*([^\n]+))?(?:[\s\S]*?FILTROS:\s*([^\n]+))?(?:[\s\S]*?TOTAIS:\s*([^\n]+))?/gi;

  let match;
  while ((match = tablePattern.exec(response)) !== null) {
    const range = match[1]?.trim();
    const tableName = match[2]?.trim();
    const style = match[3]?.trim();
    const hasHeaders =
      match[4]?.trim().toLowerCase() === 'sim' ||
      match[4]?.trim().toLowerCase() === 'true' ||
      match[4] === undefined;
    const hasFilters =
      match[5]?.trim().toLowerCase() === 'sim' ||
      match[5]?.trim().toLowerCase() === 'true' ||
      match[5] === undefined;
    const hasTotals =
      match[6]?.trim().toLowerCase() === 'sim' || match[6]?.trim().toLowerCase() === 'true';

    if (range) {
      const operation: ExcelOperation = {
        type: ExcelOperationType.TABLE,
        data: {
          range,
          tableName,
          style,
          firstRowAsHeaders: hasHeaders,
          filterButtons: hasFilters,
          totalsRow: hasTotals,
        },
      };

      operations.push(operation);
    }
  }

  // Também verificar padrões em linguagem natural
  const languagePatterns = [
    // "Criar tabela no range A1:D10"
    /cri(?:ar|e)\s+(?:uma\s+)?tabela\s+(?:n[ao]|d[ao]|para|com|usando)?\s+(?:intervalo|range)\s+([A-Z][0-9]+:[A-Z][0-9]+)(?:(?:\s+com)?\s+(?:o\s+)?nome\s+(?:de\s+)?"?([^",;\n]+)"?)?/gi,

    // "Converter range B2:F15 em tabela com nome Vendas"
    /convert(?:a|er)\s+(?:o\s+)?(?:intervalo|range)\s+([A-Z][0-9]+:[A-Z][0-9]+)\s+em\s+(?:uma\s+)?tabela(?:\s+com\s+(?:o\s+)?nome\s+(?:de\s+)?"?([^",;\n]+)"?)?/gi,

    // "Formatar como tabela o range A1:H20 usando estilo Medium 2"
    /format(?:ar|e)\s+(?:como|em)\s+(?:uma\s+)?tabela\s+(?:o\s+)?(?:intervalo|range)\s+([A-Z][0-9]+:[A-Z][0-9]+)(?:\s+(?:usando|com)\s+(?:o\s+)?estilo\s+([^,;\n]+))?/gi,
  ];

  // Processar padrões em linguagem natural
  for (const pattern of languagePatterns) {
    let match;
    while ((match = pattern.exec(response)) !== null) {
      const range = match[1]?.trim();
      let tableName = match[2]?.trim();
      let style;

      // Se o pattern tem "estilo" na segunda captura, é o terceiro padrão
      if (pattern.source.includes('estilo')) {
        style = match[2]?.trim();
        tableName = ''; // Usar string vazia em vez de undefined
      }

      if (range) {
        const operation: ExcelOperation = {
          type: ExcelOperationType.TABLE,
          data: {
            range,
            tableName,
            style,
            firstRowAsHeaders: true, // Padrão para comandos em linguagem natural
            filterButtons: true, // Padrão para comandos em linguagem natural
          },
        };

        operations.push(operation);
      }
    }
  }

  return operations;
}
