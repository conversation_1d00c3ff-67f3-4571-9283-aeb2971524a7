/**
 * Interceptor específico para capturar e prevenir erros relacionados ao GoogleGenerativeAI
 */

import { isAIRelatedError, isInterceptorLog } from './constants';

// Verificar se estamos no cliente
if (typeof window !== 'undefined') {
  console.log('[Error Interceptor] Inicializando interceptação de erros de IA');

  // Interceptar erros globais
  const originalErrorHandler = window.onerror;
  window.onerror = function (message, source, lineno, colno, error) {
    const messageStr = String(message);

    // Usar função utilitária padronizada
    if (isAIRelatedError(messageStr)) {
      console.warn('[Error Interceptor] Erro de IA interceptado e suprimido:', messageStr);
      console.warn('[Error Interceptor] Fonte:', source);
      console.warn('[Error Interceptor] Este erro foi causado por tentativa de usar IA no cliente');

      // Suprimir o erro para evitar que apareça no console
      return true;
    }

    // Se não for um erro de IA, usar o handler original
    if (originalErrorHandler) {
      return originalErrorHandler.call(this, message, source, lineno, colno, error);
    }

    return false;
  };

  // Interceptar unhandled promise rejections
  const originalUnhandledRejection = window.onunhandledrejection;
  window.onunhandledrejection = function (event) {
    const reason = event.reason;
    const reasonStr = String(reason);

    // Usar função utilitária padronizada
    if (isAIRelatedError(reasonStr)) {
      console.warn(
        '[Error Interceptor] Promise rejection de IA interceptada e suprimida:',
        reasonStr
      );
      console.warn(
        '[Error Interceptor] Esta rejeição foi causada por tentativa de usar IA no cliente'
      );

      // Prevenir que a rejeição apareça no console
      event.preventDefault();
      return;
    }

    // Se não for um erro de IA, usar o handler original
    if (originalUnhandledRejection) {
      return originalUnhandledRejection.call(window, event);
    }
  };

  // Interceptar console.error para suprimir erros específicos de IA
  const originalConsoleError = console.error;
  console.error = function (...args) {
    const message = args.join(' ');

    // Usar função utilitária padronizada
    if (isAIRelatedError(message)) {
      console.warn('[Error Interceptor] Console.error de IA interceptado e suprimido:', message);
      return;
    }

    // Se não for um erro de IA, usar o console.error original
    return originalConsoleError.apply(this, args);
  };

  // Interceptar console.warn para suprimir warnings específicos de IA
  const originalConsoleWarn = console.warn;
  console.warn = function (...args) {
    const message = args.join(' ');

    // Usar função utilitária padronizada
    if (isAIRelatedError(message)) {
      // Suprimir warnings de IA, mas manter nossos próprios warnings do interceptor
      if (!isInterceptorLog(message)) {
        console.info('[Error Interceptor] Console.warn de IA interceptado e suprimido:', message);
        return;
      }
    }

    // Se não for um warning de IA, usar o console.warn original
    return originalConsoleWarn.apply(this, args);
  };

  // Interceptar tentativas de criar instâncias de GoogleGenerativeAI através de eval ou Function
  const originalEval = window.eval;
  window.eval = function (code) {
    const codeStr = String(code);

    // Usar função utilitária padronizada
    if (isAIRelatedError(codeStr)) {
      console.warn('[Error Interceptor] Tentativa de eval com código de IA interceptada:', codeStr);
      throw new Error('Execução de código de IA via eval não é permitida no cliente');
    }

    return originalEval.call(this, code);
  };

  // Interceptar Function constructor
  const OriginalFunction = window.Function;
  window.Function = function (...args: string[]) {
    const code = args[args.length - 1];
    const codeStr = String(code);

    // Usar função utilitária padronizada
    if (isAIRelatedError(codeStr)) {
      console.warn(
        '[Error Interceptor] Tentativa de Function constructor com código de IA interceptada:',
        codeStr
      );
      throw new Error('Criação de função com código de IA não é permitida no cliente');
    }

    return new OriginalFunction(...args);
  } as FunctionConstructor;

  // Copiar propriedades estáticas do Function original
  Object.setPrototypeOf(window.Function, OriginalFunction);
  Object.defineProperty(window.Function, 'prototype', {
    value: OriginalFunction.prototype,
    writable: false,
  });

  console.log('[Error Interceptor] Interceptação de erros de IA configurada com sucesso');
}

export {};
