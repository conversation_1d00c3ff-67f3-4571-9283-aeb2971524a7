/**
 * Tipos para eventos de formulário
 */

import { ChangeEvent } from 'react';

// Evento de input genérico
export type FormInputEvent = ChangeEvent<HTMLInputElement>;

// Eventos de textarea
export type FormTextareaEvent = ChangeEvent<HTMLTextAreaElement>;

// Tipos compatíveis de eventos
export type FormEvent = FormInputEvent | FormTextareaEvent;

// Tipo unificado para handlers que funcionam com ambos
export type UnifiedChangeEventHandler = ((e: FormInputEvent) => void) &
  ((e: FormTextareaEvent) => void);

// Helper para verificar o tipo de evento
export function isInputEvent(event: FormEvent): event is FormInputEvent {
  return (event.target as HTMLInputElement).type !== undefined;
}

export function isTextareaEvent(event: FormEvent): event is FormTextareaEvent {
  return (event.target as HTMLTextAreaElement).rows !== undefined;
}

// Funções de ajuda para converter tipos de eventos
export function toTextareaEvent(e: FormInputEvent): FormTextareaEvent {
  return e as unknown as FormTextareaEvent;
}

export function toInputEvent(e: FormTextareaEvent): FormInputEvent {
  return e as unknown as FormInputEvent;
}

// Função helper para criar um handler unificado
export function createUnifiedChangeHandler(
  handler: (value: string) => void
): UnifiedChangeEventHandler {
  return ((e: FormEvent) => {
    handler(e.target.value);
  }) as UnifiedChangeEventHandler;
}
