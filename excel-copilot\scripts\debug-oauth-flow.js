#!/usr/bin/env node

/**
 * Script para debugar o fluxo OAuth completo
 */

const https = require('https');
const { URL } = require('url');

const BASE_URL = 'https://excel-copilot-eight.vercel.app';

// Função para fazer requisições HTTP
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port || 443,
      path: urlObj.pathname + urlObj.search,
      method: options.method || 'GET',
      headers: {
        'User-Agent': 'OAuth-Debug-Script/1.0',
        Accept: 'application/json, text/html, */*',
        ...options.headers,
      },
    };

    const req = https.request(requestOptions, res => {
      let data = '';
      res.on('data', chunk => (data += chunk));
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          body: data,
          url: url,
          redirectLocation: res.headers.location,
        });
      });
    });

    req.on('error', reject);
    req.setTimeout(15000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    if (options.body) {
      req.write(options.body);
    }
    req.end();
  });
}

// Função para testar o início do fluxo OAuth
async function testOAuthInitiation() {
  console.log('🔍 Testando início do fluxo OAuth...\n');

  const providers = ['google', 'github'];

  for (const provider of providers) {
    console.log(`📍 Testando ${provider.toUpperCase()}:`);

    try {
      // 1. Testar endpoint de signin do provider
      const signinUrl = `${BASE_URL}/api/auth/signin/${provider}`;
      console.log(`   Testando: ${signinUrl}`);

      const signinResponse = await makeRequest(signinUrl);
      console.log(`   Status: ${signinResponse.statusCode}`);

      if (signinResponse.statusCode === 302) {
        const redirectUrl = signinResponse.redirectLocation;
        console.log(`   ✅ Redirecionamento para: ${redirectUrl}`);

        // Verificar se o redirect é para o provedor correto
        if (provider === 'google' && redirectUrl?.includes('accounts.google.com')) {
          console.log(`   ✅ Redirecionamento para Google correto`);
        } else if (provider === 'github' && redirectUrl?.includes('github.com')) {
          console.log(`   ✅ Redirecionamento para GitHub correto`);
        } else {
          console.log(`   ⚠️  Redirecionamento inesperado: ${redirectUrl}`);
        }

        // Extrair parâmetros importantes do redirect
        if (redirectUrl) {
          const redirectUrlObj = new URL(redirectUrl);
          const params = redirectUrlObj.searchParams;

          console.log(`   Client ID: ${params.get('client_id') ? '✅ Presente' : '❌ Ausente'}`);
          console.log(`   Redirect URI: ${params.get('redirect_uri') || 'N/A'}`);
          console.log(`   State: ${params.get('state') ? '✅ Presente' : '❌ Ausente'}`);
          console.log(`   Scope: ${params.get('scope') || 'N/A'}`);
        }
      } else {
        console.log(`   ❌ Status inesperado: ${signinResponse.statusCode}`);
        if (signinResponse.body) {
          console.log(`   Resposta: ${signinResponse.body.substring(0, 200)}...`);
        }
      }
    } catch (error) {
      console.log(`   ❌ Erro: ${error.message}`);
    }

    console.log('');
  }
}

// Função para testar providers endpoint
async function testProvidersEndpoint() {
  console.log('🔍 Testando endpoint de providers...\n');

  try {
    const response = await makeRequest(`${BASE_URL}/api/auth/providers`);

    if (response.statusCode === 200) {
      const providers = JSON.parse(response.body);
      console.log('📊 Providers configurados:');

      Object.entries(providers).forEach(([key, provider]) => {
        console.log(`   ${key}:`);
        console.log(`     ID: ${provider.id}`);
        console.log(`     Name: ${provider.name}`);
        console.log(`     Type: ${provider.type}`);
        console.log(`     Signin URL: ${provider.signinUrl}`);
        console.log(`     Callback URL: ${provider.callbackUrl}`);
      });
    } else {
      console.log(`❌ Erro ao acessar providers: ${response.statusCode}`);
    }
  } catch (error) {
    console.log(`❌ Erro: ${error.message}`);
  }

  console.log('');
}

// Função para testar CSRF token
async function testCSRFToken() {
  console.log('🔍 Testando CSRF token...\n');

  try {
    const response = await makeRequest(`${BASE_URL}/api/auth/csrf`);

    if (response.statusCode === 200) {
      const data = JSON.parse(response.body);
      console.log(`✅ CSRF Token: ${data.csrfToken ? 'Presente' : 'Ausente'}`);
      if (data.csrfToken) {
        console.log(`   Token: ${data.csrfToken.substring(0, 20)}...`);
      }
    } else {
      console.log(`❌ Erro ao obter CSRF: ${response.statusCode}`);
    }
  } catch (error) {
    console.log(`❌ Erro: ${error.message}`);
  }

  console.log('');
}

// Função para testar sessão atual
async function testCurrentSession() {
  console.log('🔍 Testando sessão atual...\n');

  try {
    const response = await makeRequest(`${BASE_URL}/api/auth/session`);

    if (response.statusCode === 200) {
      const session = JSON.parse(response.body);

      if (session && session.user) {
        console.log('✅ Usuário logado:');
        console.log(`   ID: ${session.user.id}`);
        console.log(`   Nome: ${session.user.name}`);
        console.log(`   Email: ${session.user.email}`);
      } else {
        console.log('ℹ️  Nenhum usuário logado (esperado)');
      }
    } else {
      console.log(`❌ Erro ao verificar sessão: ${response.statusCode}`);
    }
  } catch (error) {
    console.log(`❌ Erro: ${error.message}`);
  }

  console.log('');
}

// Função para verificar configurações específicas do NextAuth
async function checkNextAuthConfig() {
  console.log('🔍 Verificando configurações NextAuth...\n');

  try {
    const response = await makeRequest(`${BASE_URL}/api/auth/debug`);

    if (response.statusCode === 200) {
      const debug = JSON.parse(response.body);

      console.log('📊 Configurações NextAuth:');
      console.log(`   NEXTAUTH_URL: ${debug.authConfig.NEXTAUTH_URL}`);
      console.log(`   Environment: ${debug.environment}`);
      console.log(`   Ready: ${debug.ready ? '✅' : '❌'}`);

      if (debug.issues && debug.issues.length > 0) {
        console.log('\n⚠️  Problemas encontrados:');
        debug.issues.forEach(issue => console.log(`   - ${issue}`));
      }

      console.log('\n🔑 Status dos Providers:');
      Object.entries(debug.providersStatus).forEach(([provider, status]) => {
        console.log(`   ${provider}: ${status}`);
      });
    } else {
      console.log(`❌ Erro ao acessar debug: ${response.statusCode}`);
    }
  } catch (error) {
    console.log(`❌ Erro: ${error.message}`);
  }

  console.log('');
}

// Função principal
async function main() {
  console.log('🚀 Iniciando debug completo do fluxo OAuth\n');
  console.log(`🌐 URL base: ${BASE_URL}\n`);

  await checkNextAuthConfig();
  await testCurrentSession();
  await testCSRFToken();
  await testProvidersEndpoint();
  await testOAuthInitiation();

  console.log('📋 Próximos passos para resolver problemas:');
  console.log('1. Verificar se as URLs de callback estão corretas nos provedores OAuth');
  console.log('2. Confirmar que Client ID e Secret estão corretos');
  console.log('3. Verificar se o OAuth consent screen está configurado (Google)');
  console.log('4. Testar o login manualmente no navegador');
  console.log('\n✅ Debug concluído!');
}

// Executar se chamado diretamente
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { testOAuthInitiation, testProvidersEndpoint, testCSRFToken };
