import { NextRequest } from 'next/server';

import { GitHubClient } from '@/lib/github-integration';
import { logger } from '@/lib/logger';
import { ApiResponse } from '@/utils/api-response';

// Forçar o modo dinâmico para essa rota
export const dynamic = 'force-dynamic';

/**
 * GET /api/github/repositories
 * Lista repositórios do usuário/organização
 */
export async function GET(request: NextRequest) {
  try {
    // Verificar se temos as credenciais necessárias
    const token = process.env.MCP_GITHUB_TOKEN;
    const owner = process.env.MCP_GITHUB_OWNER;

    if (!token) {
      return ApiResponse.error('GITHUB_TOKEN não configurado', 'GITHUB_NOT_CONFIGURED', 500);
    }

    // Obter parâmetros da query
    const { searchParams } = new URL(request.url);
    const type =
      (searchParams.get('type') as 'all' | 'owner' | 'public' | 'private' | 'member') || 'owner';
    const sort =
      (searchParams.get('sort') as 'created' | 'updated' | 'pushed' | 'full_name') || 'updated';
    const direction = (searchParams.get('direction') as 'asc' | 'desc') || 'desc';
    const per_page = parseInt(searchParams.get('per_page') || '30');
    const page = parseInt(searchParams.get('page') || '1');

    // Validar parâmetros
    if (per_page < 1 || per_page > 100) {
      return ApiResponse.badRequest('Parâmetro per_page deve estar entre 1 e 100');
    }

    if (page < 1) {
      return ApiResponse.badRequest('Parâmetro page deve ser maior que 0');
    }

    // Criar cliente GitHub
    const githubClient = new GitHubClient({
      token,
      ...(owner && { owner }),
    });

    // Obter repositórios
    const repositories = await githubClient.getRepositories({
      type,
      sort,
      direction,
      per_page,
      page,
    });

    // Formatar resposta
    const formattedRepositories = repositories.repositories.map(repo => ({
      id: repo.id,
      name: repo.name,
      fullName: repo.full_name,
      description: repo.description,
      private: repo.private,
      htmlUrl: repo.html_url,
      cloneUrl: repo.clone_url,
      sshUrl: repo.ssh_url,
      defaultBranch: repo.default_branch,
      language: repo.language,
      stars: repo.stargazers_count,
      forks: repo.forks_count,
      openIssues: repo.open_issues_count,
      createdAt: repo.created_at,
      updatedAt: repo.updated_at,
      pushedAt: repo.pushed_at,
      owner: {
        login: repo.owner.login,
        id: repo.owner.id,
        avatarUrl: repo.owner.avatar_url,
        type: repo.owner.type,
      },
    }));

    const response = {
      repositories: formattedRepositories,
      pagination: {
        page,
        perPage: per_page,
        total: repositories.total,
        hasNext: formattedRepositories.length === per_page,
      },
      filters: {
        type,
        sort,
        direction,
      },
      timestamp: new Date().toISOString(),
    };

    logger.info('Repositórios GitHub obtidos com sucesso', {
      count: formattedRepositories.length,
      type,
      sort,
    });

    return ApiResponse.success(response);
  } catch (error) {
    logger.error('Erro ao obter repositórios do GitHub', { error });

    if (error instanceof Error) {
      return ApiResponse.error(
        `Erro ao conectar com GitHub: ${error.message}`,
        'GITHUB_API_ERROR',
        500
      );
    }

    return ApiResponse.error('Erro interno do servidor', 'INTERNAL_ERROR', 500);
  }
}

/**
 * POST /api/github/repositories
 * Obtém detalhes de um repositório específico
 */
export async function POST(request: NextRequest) {
  try {
    const token = process.env.MCP_GITHUB_TOKEN;

    if (!token) {
      return ApiResponse.error('GITHUB_TOKEN não configurado', 'GITHUB_NOT_CONFIGURED', 500);
    }

    // Obter dados do body
    const body = await request.json();
    const { owner, repo } = body;

    if (!owner || !repo) {
      return ApiResponse.badRequest('owner e repo são obrigatórios');
    }

    // Criar cliente GitHub
    const githubClient = new GitHubClient({ token });

    // Obter detalhes do repositório
    const repository = await githubClient.getRepository(owner, repo);

    // Obter issues e PRs recentes
    const [issues, pullRequests, workflowRuns] = await Promise.all([
      githubClient.getIssues({ owner, repo, state: 'open', per_page: 5 }),
      githubClient.getPullRequests({ owner, repo, state: 'open', per_page: 5 }),
      githubClient.getWorkflowRuns({ owner, repo, per_page: 5 }),
    ]);

    const response = {
      repository: {
        id: repository.id,
        name: repository.name,
        fullName: repository.full_name,
        description: repository.description,
        private: repository.private,
        htmlUrl: repository.html_url,
        cloneUrl: repository.clone_url,
        sshUrl: repository.ssh_url,
        defaultBranch: repository.default_branch,
        language: repository.language,
        stars: repository.stargazers_count,
        forks: repository.forks_count,
        openIssues: repository.open_issues_count,
        createdAt: repository.created_at,
        updatedAt: repository.updated_at,
        pushedAt: repository.pushed_at,
        owner: {
          login: repository.owner.login,
          id: repository.owner.id,
          avatarUrl: repository.owner.avatar_url,
          type: repository.owner.type,
        },
      },
      stats: {
        openIssues: issues.total,
        openPullRequests: pullRequests.total,
        recentWorkflowRuns: workflowRuns.total,
      },
      recentActivity: {
        issues: issues.issues.slice(0, 3),
        pullRequests: pullRequests.pullRequests.slice(0, 3),
        workflowRuns: workflowRuns.workflowRuns.slice(0, 3),
      },
      timestamp: new Date().toISOString(),
    };

    logger.info('Detalhes do repositório GitHub obtidos com sucesso', {
      repository: `${owner}/${repo}`,
    });

    return ApiResponse.success(response);
  } catch (error) {
    logger.error('Erro ao obter detalhes do repositório GitHub', { error });

    if (error instanceof Error) {
      return ApiResponse.error(
        `Erro ao conectar com GitHub: ${error.message}`,
        'GITHUB_API_ERROR',
        500
      );
    }

    return ApiResponse.error('Erro interno do servidor', 'INTERNAL_ERROR', 500);
  }
}
