/**
 * Script para unificar as definições de tipos ExcelOperationType e ExcelOperation
 * Este script garante que src/lib/excel/types.ts importe os tipos de src/types/index.ts
 */
const fs = require('fs');
const path = require('path');

// Caminhos dos arquivos
const typesIndexPath = path.join(__dirname, '..', 'src', 'types', 'index.ts');
const excelTypesPath = path.join(__dirname, '..', 'src', 'lib', 'excel', 'types.ts');
const execOperationsPath = path.join(
  __dirname,
  '..',
  'src',
  'lib',
  'excel',
  'executionOperations.ts'
);

// Verifica se os arquivos existem
if (!fs.existsSync(typesIndexPath)) {
  console.error(`Arquivo ${typesIndexPath} não encontrado!`);
  process.exit(1);
}

if (!fs.existsSync(excelTypesPath)) {
  console.error(`Arquivo ${excelTypesPath} não encontrado!`);
  process.exit(1);
}

console.log('🔍 Iniciando unificação de tipos de operação Excel...');

// Lê o conteúdo do arquivo de tipos de excel
let excelTypesContent = fs.readFileSync(excelTypesPath, 'utf8');

// Verifica se já existe uma importação de ExcelOperationType
const hasImport = /import\s+{\s*ExcelOperationType\s*}\s+from\s+['"]@\/types\/index['"]/g.test(
  excelTypesContent
);

// Verifica se há definição local de ExcelOperationType
const hasLocalEnum = /export\s+enum\s+ExcelOperationType/g.test(excelTypesContent);

// Define o texto de substituição
const importStatement =
  "import { ExcelOperationType, ExcelOperation as BaseExcelOperation } from '@/types/index';\n\n";

// Se não tiver importação e tiver definição local, adiciona a importação e comenta a definição local
if (!hasImport && hasLocalEnum) {
  console.log('📝 Adicionando importação de ExcelOperationType de @/types/index');

  // Substitui a definição local pelo import
  excelTypesContent = excelTypesContent.replace(
    /export\s+enum\s+ExcelOperationType\s*{[^}]*}/gs,
    `// Importado de @/types/index\n// export enum ExcelOperationType {...}`
  );

  // Adiciona a importação no início do arquivo
  excelTypesContent = importStatement + excelTypesContent;
}

// Substitui a definição de ExcelOperation para ser um alias de BaseExcelOperation
if (excelTypesContent.includes('export interface ExcelOperation')) {
  console.log('📝 Substituindo interface ExcelOperation por um alias do tipo importado');

  excelTypesContent = excelTypesContent.replace(
    /export\s+interface\s+ExcelOperation\s*{[^}]*}/gs,
    `// Interface unificada, usando o tipo de @/types/index\nexport type ExcelOperation = BaseExcelOperation;`
  );
}

// Salva as alterações no arquivo
fs.writeFileSync(excelTypesPath, excelTypesContent);
console.log(`✅ Arquivo ${excelTypesPath} atualizado com sucesso!`);

// Verifica e corrige problemas em executionOperations.ts
if (fs.existsSync(execOperationsPath)) {
  console.log(`🔍 Verificando problemas de tipo em ${execOperationsPath}...`);

  let execContent = fs.readFileSync(execOperationsPath, 'utf8');

  // Verifica se já importa ExcelOperationType de @/types/index
  const hasTypeImport =
    /import\s+{\s*ExcelOperationType\s*}\s+from\s+['"]@\/types\/index['"]/g.test(execContent);

  if (!hasTypeImport) {
    console.log('📝 Adicionando importação de ExcelOperationType em executionOperations.ts');

    // Adiciona a importação se não existir
    execContent = execContent.replace(
      /import[^\n]*ExcelOperation[^\n]*;\n/,
      match => match + `import { ExcelOperationType } from '@/types/index';\n`
    );

    // Salva as alterações
    fs.writeFileSync(execOperationsPath, execContent);
    console.log(`✅ Arquivo ${execOperationsPath} atualizado com sucesso!`);
  }
}

console.log('✅ Processo de unificação de tipos concluído!');
console.log('Execute npm run typecheck para verificar se os problemas foram resolvidos');
