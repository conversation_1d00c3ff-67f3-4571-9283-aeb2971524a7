name: Test Coverage

on:
  push:
    branches: [main, master, develop]
  pull_request:
    branches: [main, master, develop]

jobs:
  build-and-test:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [18.x]

    steps:
      - uses: actions/checkout@v3

      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v3
        with:
          node-version: ${{ matrix.node-version }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run tests and generate coverage report
        run: node scripts/test-coverage-report.js

      - name: Archive coverage results
        uses: actions/upload-artifact@v3
        with:
          name: coverage-report
          path: coverage/

      - name: Create comment with coverage report
        if: github.event_name == 'pull_request'
        uses: actions/github-script@v6
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const fs = require('fs');
            try {
              const coverageFile = 'coverage/coverage-summary.json';
              
              if (!fs.existsSync(coverageFile)) {
                console.log('Coverage report file not found');
                return;
              }
              
              const coverageData = JSON.parse(fs.readFileSync(coverageFile, 'utf8'));
              const totalCoverage = coverageData.total;
              
              // Format coverage report for PR comment
              const body = `## Relatório de Cobertura de Testes
              
              | Métrica | Cobertura | Meta |
              |---------|-----------|------|
              | Linhas | ${totalCoverage.lines.pct.toFixed(2)}% | 80% |
              | Funções | ${totalCoverage.functions.pct.toFixed(2)}% | 80% |
              | Branches | ${totalCoverage.branches.pct.toFixed(2)}% | 80% |
              | Statements | ${totalCoverage.statements.pct.toFixed(2)}% | 80% |
              
              Verificação completa: ${totalCoverage.lines.pct >= 80 ? '✅ Passou' : '❌ Falhou'}
              
              [Ver relatório completo](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }})
              `;
              
              github.rest.issues.createComment({
                issue_number: context.issue.number,
                owner: context.repo.owner,
                repo: context.repo.repo,
                body: body
              });
              
            } catch (error) {
              console.error('Error creating coverage comment:', error);
            }
