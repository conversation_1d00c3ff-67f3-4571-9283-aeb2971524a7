/**
 * Utilitários para operações seguras com Excel
 *
 * Este arquivo fornece funções para manipulação tipada e segura de dados do Excel,
 * focando em access patterns que respeitam a flag exactOptionalPropertyTypes.
 */

import { createExcelError } from './error-utils';
import { safeArrayAccess } from './regex-utils';

// Tipos básicos para Excel
export type CellValue = string | number | boolean | null | undefined;
export type CellCoordinate = { row: number; col: number };
export type RangeCoordinate = {
  startRow: number;
  startCol: number;
  endRow: number;
  endCol: number;
};

// Matriz de células do Excel
export type ExcelMatrix = CellValue[][];

/**
 * Converte referência de célula (ex: "A1") para coordenadas (row, col)
 * @param cellRef Referência de célula no formato Excel
 * @returns Coordenadas da célula
 */
export function parseCellReference(cellRef: string | undefined): CellCoordinate {
  if (!cellRef || typeof cellRef !== 'string') {
    throw createExcelError('Referência de célula inválida', 'INVALID_CELL_REF');
  }

  const match = cellRef.match(/([A-Za-z]+)([0-9]+)/);
  if (!match) {
    throw createExcelError(`Referência de célula inválida: ${cellRef}`, 'INVALID_CELL_REF');
  }

  const colStr = (match[1] || '').toUpperCase();
  const rowStr = match[2] || '';

  if (!colStr || !rowStr) {
    throw createExcelError(`Referência de célula mal formatada: ${cellRef}`, 'INVALID_CELL_REF');
  }

  // Converter a letra da coluna para índice numérico (A=0, B=1, ...)
  let colIndex = 0;
  for (let i = 0; i < colStr.length; i++) {
    colIndex = colIndex * 26 + (colStr.charCodeAt(i) - 'A'.charCodeAt(0) + 1);
  }
  colIndex--; // Ajuste para base 0

  // Converter número da linha para índice numérico (1=0, 2=1, ...)
  const rowIndex = parseInt(rowStr, 10) - 1;

  if (isNaN(rowIndex) || rowIndex < 0) {
    throw createExcelError(`Índice de linha inválido: ${rowStr}`, 'INVALID_ROW_INDEX');
  }

  return { row: rowIndex, col: colIndex };
}

/**
 * Converte índice de coluna para letra (ex: 0 -> A, 1 -> B, etc.)
 * @param index Índice da coluna
 * @returns Letra da coluna
 */
export function columnIndexToLetter(index: number): string {
  if (isNaN(index) || index < 0) {
    throw createExcelError(`Índice de coluna inválido: ${index}`, 'INVALID_COLUMN_INDEX');
  }

  let letter = '';
  let tempIndex = index;

  do {
    const remainder = tempIndex % 26;
    letter = String.fromCharCode(65 + remainder) + letter;
    tempIndex = Math.floor(tempIndex / 26) - 1;
  } while (tempIndex >= 0);

  return letter;
}

/**
 * Gera uma referência de célula a partir de coordenadas (ex: {row: 0, col: 0} -> "A1")
 * @param coordinate Coordenadas da célula
 * @returns Referência de célula no formato Excel
 */
export function generateCellReference(coordinate: CellCoordinate): string {
  if (!coordinate || typeof coordinate.row !== 'number' || typeof coordinate.col !== 'number') {
    throw createExcelError('Coordenadas de célula inválidas', 'INVALID_CELL_COORDINATE');
  }

  const colLetter = columnIndexToLetter(coordinate.col);
  const rowNumber = coordinate.row + 1; // Ajuste da base 0 para base 1

  return `${colLetter}${rowNumber}`;
}

/**
 * Acessa uma célula de uma matriz de forma segura
 * @param matrix Matriz de células
 * @param rowIndex Índice da linha
 * @param colIndex Índice da coluna
 * @param defaultValue Valor padrão caso a célula não exista
 * @returns Valor da célula ou o valor padrão
 */
export function getCellValue<T = CellValue>(
  matrix: T[][] | undefined | null,
  rowIndex: number,
  colIndex: number,
  defaultValue: T | undefined = undefined
): T | undefined {
  if (!matrix || !Array.isArray(matrix)) {
    return defaultValue;
  }

  if (rowIndex < 0 || colIndex < 0 || rowIndex >= matrix.length) {
    return defaultValue;
  }

  const row = matrix[rowIndex];

  if (!row || !Array.isArray(row) || colIndex >= row.length) {
    return defaultValue;
  }

  return row[colIndex] !== undefined ? row[colIndex] : defaultValue;
}

/**
 * Define o valor de uma célula em uma matriz, expandindo-a se necessário
 * @param matrix Matriz a ser modificada
 * @param rowIndex Índice da linha
 * @param colIndex Índice da coluna
 * @param value Valor a ser definido
 * @returns Matriz atualizada
 */
export function setCellValue<T = CellValue>(
  matrix: T[][] | undefined | null,
  rowIndex: number,
  colIndex: number,
  value: T
): T[][] {
  if (!matrix || !Array.isArray(matrix)) {
    matrix = [];
  }

  // Criar uma nova matriz para evitar mutação
  const result = [...matrix];

  // Garantir que temos linhas suficientes
  while (result.length <= rowIndex) {
    result.push([]);
  }

  // Garantir que a linha tem colunas suficientes
  const currentRow = result[rowIndex];

  // Clone a linha atual ou crie uma nova se não existir
  const newRow = currentRow ? [...currentRow] : [];
  result[rowIndex] = newRow;

  // Preencher com valores undefined até o índice desejado
  while (newRow.length <= colIndex) {
    newRow.push(undefined as unknown as T);
  }

  // Definir o valor
  newRow[colIndex] = value;

  return result;
}

/**
 * Converte uma string de range (ex: "A1:C3") para coordenadas
 * @param range String de range
 * @returns Coordenadas do range
 */
export function parseRange(range: string | undefined): RangeCoordinate {
  if (!range || typeof range !== 'string') {
    throw createExcelError('Range inválido: indefinido', 'INVALID_RANGE');
  }

  const parts = range.split(':');
  if (parts.length !== 2) {
    throw createExcelError(`Range inválido: ${range}`, 'INVALID_RANGE_FORMAT');
  }

  // Converter células de início e fim para coordenadas
  const startCell = safeArrayAccess(parts, 0) || '';
  const endCell = safeArrayAccess(parts, 1) || '';

  if (!startCell || !endCell) {
    throw createExcelError(`Range com partes faltando: ${range}`, 'INVALID_RANGE_PARTS');
  }

  const start = parseCellReference(startCell);
  const end = parseCellReference(endCell);

  return {
    startRow: start.row,
    startCol: start.col,
    endRow: end.row,
    endCol: end.col,
  };
}

/**
 * Extrai valores de um range de células
 * @param matrix Matriz de células
 * @param range Coordenadas do range
 * @returns Matriz com os valores do range
 */
export function getRangeValues<T = CellValue>(
  matrix: T[][] | undefined | null,
  range: RangeCoordinate
): T[][] {
  const result: T[][] = [];

  if (!matrix || !Array.isArray(matrix)) {
    return result;
  }

  const { startRow, startCol, endRow, endCol } = range;

  // Normalizar coordenadas para garantir que start <= end
  const normalizedRange = {
    startRow: Math.min(startRow, endRow),
    startCol: Math.min(startCol, endCol),
    endRow: Math.max(startRow, endRow),
    endCol: Math.max(startCol, endCol),
  };

  // Extrair valores
  for (let r = normalizedRange.startRow; r <= normalizedRange.endRow; r++) {
    const resultRow: T[] = [];

    for (let c = normalizedRange.startCol; c <= normalizedRange.endCol; c++) {
      const value = getCellValue(matrix, r, c);
      resultRow.push(value as T);
    }

    result.push(resultRow);
  }

  return result;
}

/**
 * Verifica se uma string contém uma referência de célula válida
 * @param text Texto a verificar
 * @returns true se contiver uma referência de célula
 */
export function hasCellReference(text: string | undefined | null): boolean {
  if (!text) return false;
  return /[A-Za-z]+[0-9]+/.test(text);
}

/**
 * Extrai a primeira referência de célula de um texto
 * @param text Texto a analisar
 * @returns Referência de célula ou null se não encontrada
 */
export function extractCellReference(text: string | undefined | null): string | null {
  if (!text) return null;

  const match = text.match(/[A-Za-z]+[0-9]+/);
  return match ? match[0] : null;
}
