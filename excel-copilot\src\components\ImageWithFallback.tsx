'use client';

import Image, { ImageProps } from 'next/image';
import { useState, useEffect } from 'react';

type ImageWithFallbackProps = ImageProps & {
  fallback?: string;
};

/**
 * Componente de imagem com fallback para tratamento de erros
 */
export function ImageWithFallback({
  src,
  fallback = '/images/placeholder.svg',
  alt,
  ...props
}: ImageWithFallbackProps) {
  const [error, setError] = useState<boolean>(false);
  const [_loadedSrc, setLoadedSrc] = useState<string | null>(null);

  useEffect(() => {
    setError(false);
    setLoadedSrc(null);
  }, [src]);

  const onError = () => {
    setError(true);
  };

  return (
    <Image
      {...props}
      src={error ? fallback : src}
      alt={alt}
      onError={onError}
      className={`${props.className || ''} ${error ? 'fallback-image' : ''}`}
    />
  );
}
