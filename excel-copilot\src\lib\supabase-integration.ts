/**
 * Supabase MCP Integration - Excel Copilot
 *
 * Cliente e serviços para integração com Supabase API
 * Permite monitoramento de banco de dados, storage e real-time via MCP
 */

import { ENV } from '@/config/unified-environment';

import { logger } from './logger';

// Cache simples para evitar muitas requisições
const cache = new Map<string, { data: unknown; timestamp: number }>();
const CACHE_TTL = 5 * 60 * 1000; // 5 minutos

function getCachedData<T>(key: string): T | null {
  const cached = cache.get(key);
  if (cached && Date.now() - cached.timestamp < CACHE_TTL) {
    return cached.data as T;
  }
  return null;
}

function setCachedData<T>(key: string, data: T): void {
  cache.set(key, { data, timestamp: Date.now() });
}

// Tipos para Supabase
export interface SupabaseProject {
  id: string;
  name: string;
  organization_id: string;
  database: {
    host: string;
    version: string;
  };
  status: 'ACTIVE_HEALTHY' | 'ACTIVE_UNHEALTHY' | 'PAUSED' | 'INACTIVE';
  created_at: string;
  updated_at: string;
}

export interface SupabaseTable {
  id: number;
  schema: string;
  name: string;
  rls_enabled: boolean;
  rls_forced: boolean;
  replica_identity: string;
  bytes: number;
  size: string;
  seq_scan_count: number;
  seq_tup_read: number;
  idx_scan_count: number;
  idx_tup_fetch: number;
  row_count: number;
  dead_row_count: number;
}

export interface SupabaseBucket {
  id: string;
  name: string;
  owner: string;
  public: boolean;
  file_size_limit?: number;
  allowed_mime_types?: string[];
  created_at: string;
  updated_at: string;
}

export interface SupabaseStorageObject {
  name: string;
  id: string;
  updated_at: string;
  created_at: string;
  last_accessed_at: string;
  metadata?: {
    eTag?: string;
    size?: number;
    mimetype?: string;
    cacheControl?: string;
    lastModified?: string;
    contentLength?: number;
    httpStatusCode?: number;
  };
}

export interface SupabaseHealthStatus {
  configured: boolean;
  databaseAccessible: boolean;
  storageAccessible: boolean;
  realtimeAccessible: boolean;
  lastSync?: string | undefined;
  projectInfo?: SupabaseProject | undefined;
  tableCount: number;
  bucketCount: number;
  storageUsage: number;
  recentActivity: number;
}

export interface SupabaseMetrics {
  database: {
    connections: number;
    queries_per_second: number;
    cache_hit_rate: number;
    table_count: number;
    total_size: string;
  };
  storage: {
    total_objects: number;
    total_size: number;
    bucket_count: number;
    bandwidth_used: number;
  };
  realtime: {
    active_connections: number;
    messages_per_second: number;
    channels_count: number;
  };
}

/**
 * Cliente base para Supabase Management API
 */
export class SupabaseClient {
  private serviceRoleKey: string;
  private projectUrl: string;
  private projectRef: string;
  private managementApiUrl = 'https://api.supabase.com/v1';

  constructor(options?: { serviceRoleKey?: string; projectUrl?: string }) {
    this.serviceRoleKey = options?.serviceRoleKey || ENV.SUPABASE_SERVICE_ROLE_KEY || '';
    this.projectUrl = options?.projectUrl || ENV.SUPABASE_URL || '';

    // Extrair project ref da URL
    const urlMatch = this.projectUrl.match(/https:\/\/([^.]+)\.supabase\.co/);
    this.projectRef = urlMatch?.[1] || '';

    if (!this.serviceRoleKey) {
      logger.warn('Supabase service role key não configurada');
    }
    if (!this.projectRef) {
      logger.warn('Supabase project ref não pode ser extraído da URL');
    }
  }

  /**
   * Faz uma requisição para a Management API do Supabase
   */
  private async managementRequest<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    if (!this.serviceRoleKey) {
      throw new Error('Supabase service role key não configurada');
    }

    const url = `${this.managementApiUrl}${endpoint}`;
    const headers = {
      Authorization: `Bearer ${this.serviceRoleKey}`,
      'Content-Type': 'application/json',
      apikey: this.serviceRoleKey,
      ...options.headers,
    };

    try {
      const response = await fetch(url, {
        ...options,
        headers,
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Supabase Management API error: ${response.status} - ${errorText}`);
      }

      return await response.json();
    } catch (error) {
      logger.error('Erro na requisição Supabase Management API', { endpoint, error });
      throw error;
    }
  }

  /**
   * Faz uma requisição para a API REST do projeto
   */
  private async projectRequest<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    if (!this.serviceRoleKey || !this.projectUrl) {
      throw new Error('Supabase configuração incompleta');
    }

    const url = `${this.projectUrl}/rest/v1${endpoint}`;
    const headers = {
      Authorization: `Bearer ${this.serviceRoleKey}`,
      apikey: this.serviceRoleKey,
      'Content-Type': 'application/json',
      ...options.headers,
    };

    try {
      const response = await fetch(url, {
        ...options,
        headers,
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Supabase REST API error: ${response.status} - ${errorText}`);
      }

      return await response.json();
    } catch (error) {
      logger.error('Erro na requisição Supabase REST API', { endpoint, error });
      throw error;
    }
  }

  /**
   * Obtém informações do projeto
   */
  async getProjectInfo(): Promise<SupabaseProject> {
    const cacheKey = `project-info-${this.projectRef}`;
    const cached = getCachedData<SupabaseProject>(cacheKey);
    if (cached) return cached;

    const project = await this.managementRequest<SupabaseProject>(`/projects/${this.projectRef}`);

    setCachedData(cacheKey, project);
    return project;
  }

  /**
   * Lista tabelas do banco de dados
   */
  async getTables(): Promise<SupabaseTable[]> {
    const cacheKey = `tables-${this.projectRef}`;
    const cached = getCachedData<SupabaseTable[]>(cacheKey);
    if (cached) return cached;

    // Usar query SQL para obter informações das tabelas
    const query = `
      SELECT
        schemaname as schema,
        tablename as name,
        pg_total_relation_size(schemaname||'.'||tablename) as bytes,
        pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
      FROM pg_tables
      WHERE schemaname = 'public'
      ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC
    `;

    const tables = await this.projectRequest<SupabaseTable[]>(
      `/rpc/sql?query=${encodeURIComponent(query)}`
    );

    setCachedData(cacheKey, tables);
    return tables;
  }

  /**
   * Lista buckets de storage
   */
  async getStorageBuckets(): Promise<SupabaseBucket[]> {
    const cacheKey = `buckets-${this.projectRef}`;
    const cached = getCachedData<SupabaseBucket[]>(cacheKey);
    if (cached) return cached;

    const url = `${this.projectUrl}/storage/v1/bucket`;
    const response = await fetch(url, {
      headers: {
        Authorization: `Bearer ${this.serviceRoleKey}`,
        apikey: this.serviceRoleKey,
      },
    });

    if (!response.ok) {
      throw new Error(`Storage API error: ${response.status}`);
    }

    const buckets = await response.json();
    setCachedData(cacheKey, buckets);
    return buckets;
  }

  /**
   * Lista objetos em um bucket
   */
  async getStorageObjects(bucketName: string, path = ''): Promise<SupabaseStorageObject[]> {
    const url = `${this.projectUrl}/storage/v1/object/list/${bucketName}`;
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${this.serviceRoleKey}`,
        apikey: this.serviceRoleKey,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        limit: 100,
        offset: 0,
        sortBy: { column: 'name', order: 'asc' },
        prefix: path,
      }),
    });

    if (!response.ok) {
      throw new Error(`Storage list error: ${response.status}`);
    }

    return await response.json();
  }

  /**
   * Obtém métricas do projeto
   */
  async getProjectMetrics(): Promise<SupabaseMetrics> {
    const cacheKey = `metrics-${this.projectRef}`;
    const cached = getCachedData<SupabaseMetrics>(cacheKey);
    if (cached) return cached;

    // Simular métricas básicas (em produção, usar APIs específicas)
    const [tables, buckets] = await Promise.all([
      this.getTables().catch(() => []),
      this.getStorageBuckets().catch(() => []),
    ]);

    const metrics: SupabaseMetrics = {
      database: {
        connections: 0, // Seria obtido via SQL ou Management API
        queries_per_second: 0,
        cache_hit_rate: 0,
        table_count: tables.length,
        total_size: tables.reduce((acc, table) => acc + (table.bytes || 0), 0).toString(),
      },
      storage: {
        total_objects: 0,
        total_size: 0,
        bucket_count: buckets.length,
        bandwidth_used: 0,
      },
      realtime: {
        active_connections: 0,
        messages_per_second: 0,
        channels_count: 0,
      },
    };

    setCachedData(cacheKey, metrics);
    return metrics;
  }

  /**
   * Verifica saúde da conexão Supabase
   */
  async checkHealth(): Promise<SupabaseHealthStatus> {
    try {
      if (!this.serviceRoleKey || !this.projectUrl) {
        return {
          configured: false,
          databaseAccessible: false,
          storageAccessible: false,
          realtimeAccessible: false,
          tableCount: 0,
          bucketCount: 0,
          storageUsage: 0,
          recentActivity: 0,
        };
      }

      // Testar conectividade com diferentes serviços
      const [projectInfo, tables, buckets] = await Promise.allSettled([
        this.getProjectInfo(),
        this.getTables(),
        this.getStorageBuckets(),
      ]);

      const databaseAccessible = tables.status === 'fulfilled';
      const storageAccessible = buckets.status === 'fulfilled';
      const realtimeAccessible = true; // Assumir que está acessível se o projeto está ativo

      return {
        configured: true,
        databaseAccessible,
        storageAccessible,
        realtimeAccessible,
        lastSync: new Date().toISOString(),
        projectInfo: projectInfo.status === 'fulfilled' ? projectInfo.value : undefined,
        tableCount: databaseAccessible ? (tables.value as SupabaseTable[]).length : 0,
        bucketCount: storageAccessible ? (buckets.value as SupabaseBucket[]).length : 0,
        storageUsage: 0, // Seria calculado a partir dos objetos
        recentActivity: 0, // Seria obtido via logs ou métricas
      };
    } catch (error) {
      logger.error('Supabase health check failed:', error);

      return {
        configured: !!this.serviceRoleKey,
        databaseAccessible: false,
        storageAccessible: false,
        realtimeAccessible: false,
        tableCount: 0,
        bucketCount: 0,
        storageUsage: 0,
        recentActivity: 0,
      };
    }
  }
}

/**
 * Serviço de monitoramento Supabase de alto nível
 */
export class SupabaseMonitoringService {
  private client: SupabaseClient;

  constructor(options?: { serviceRoleKey?: string; projectUrl?: string }) {
    this.client = new SupabaseClient(options);
  }

  /**
   * Obtém status geral do projeto Supabase
   */
  async getProjectStatus(): Promise<{
    status: 'healthy' | 'degraded' | 'down';
    project: SupabaseProject | null;
    services: {
      database: boolean;
      storage: boolean;
      realtime: boolean;
    };
    metrics: SupabaseMetrics | null;
    message: string;
  }> {
    try {
      const healthCheck = await this.client.checkHealth();

      let status: 'healthy' | 'degraded' | 'down' = 'healthy';
      let message = 'Todos os serviços funcionando normalmente';

      if (!healthCheck.configured) {
        status = 'down';
        message = 'Supabase não configurado';
      } else if (!healthCheck.databaseAccessible && !healthCheck.storageAccessible) {
        status = 'down';
        message = 'Serviços principais inacessíveis';
      } else if (!healthCheck.databaseAccessible || !healthCheck.storageAccessible) {
        status = 'degraded';
        message = 'Alguns serviços com problemas';
      }

      const metrics = healthCheck.configured
        ? await this.client.getProjectMetrics().catch(() => null)
        : null;

      return {
        status,
        project: healthCheck.projectInfo || null,
        services: {
          database: healthCheck.databaseAccessible,
          storage: healthCheck.storageAccessible,
          realtime: healthCheck.realtimeAccessible,
        },
        metrics,
        message,
      };
    } catch (error) {
      logger.error('Erro ao obter status do projeto Supabase:', error);
      return {
        status: 'down',
        project: null,
        services: {
          database: false,
          storage: false,
          realtime: false,
        },
        metrics: null,
        message: 'Erro ao conectar com Supabase',
      };
    }
  }

  /**
   * Obtém resumo das tabelas do banco
   */
  async getDatabaseSummary(): Promise<{
    tables: SupabaseTable[];
    totalTables: number;
    totalSize: string;
    largestTables: SupabaseTable[];
  }> {
    try {
      const tables = await this.client.getTables();

      const totalSize = tables.reduce((acc, table) => acc + (table.bytes || 0), 0);
      const largestTables = tables.sort((a, b) => (b.bytes || 0) - (a.bytes || 0)).slice(0, 5);

      return {
        tables,
        totalTables: tables.length,
        totalSize: this.formatBytes(totalSize),
        largestTables,
      };
    } catch (error) {
      logger.error('Erro ao obter resumo do banco:', error);
      throw error;
    }
  }

  /**
   * Obtém resumo do storage
   */
  async getStorageSummary(): Promise<{
    buckets: SupabaseBucket[];
    totalBuckets: number;
    publicBuckets: number;
    privateBuckets: number;
    bucketDetails: Array<{
      bucket: SupabaseBucket;
      objectCount: number;
      totalSize: number;
    }>;
  }> {
    try {
      const buckets = await this.client.getStorageBuckets();

      const bucketDetails = await Promise.all(
        buckets.map(async bucket => {
          try {
            const objects = await this.client.getStorageObjects(bucket.name);
            const totalSize = objects.reduce((acc, obj) => acc + (obj.metadata?.size || 0), 0);

            return {
              bucket,
              objectCount: objects.length,
              totalSize,
            };
          } catch (error) {
            logger.warn(`Erro ao obter objetos do bucket ${bucket.name}:`, error);
            return {
              bucket,
              objectCount: 0,
              totalSize: 0,
            };
          }
        })
      );

      return {
        buckets,
        totalBuckets: buckets.length,
        publicBuckets: buckets.filter(b => b.public).length,
        privateBuckets: buckets.filter(b => !b.public).length,
        bucketDetails,
      };
    } catch (error) {
      logger.error('Erro ao obter resumo do storage:', error);
      throw error;
    }
  }

  /**
   * Obtém objetos de um bucket específico
   */
  async getBucketContents(
    bucketName: string,
    path = ''
  ): Promise<{
    bucket: SupabaseBucket | null;
    objects: SupabaseStorageObject[];
    totalObjects: number;
    totalSize: number;
  }> {
    try {
      const [buckets, objects] = await Promise.all([
        this.client.getStorageBuckets(),
        this.client.getStorageObjects(bucketName, path),
      ]);

      const bucket = buckets.find(b => b.name === bucketName) || null;
      const totalSize = objects.reduce((acc, obj) => acc + (obj.metadata?.size || 0), 0);

      return {
        bucket,
        objects,
        totalObjects: objects.length,
        totalSize,
      };
    } catch (error) {
      logger.error(`Erro ao obter conteúdo do bucket ${bucketName}:`, error);
      throw error;
    }
  }

  /**
   * Obtém métricas de performance
   */
  async getPerformanceMetrics(): Promise<{
    database: {
      tableCount: number;
      totalSize: string;
      avgQueryTime: number;
      connectionCount: number;
    };
    storage: {
      bucketCount: number;
      totalObjects: number;
      totalSize: string;
      bandwidthUsed: string;
    };
    realtime: {
      activeConnections: number;
      messagesPerSecond: number;
      channelsCount: number;
    };
  }> {
    try {
      const metrics = await this.client.getProjectMetrics();

      return {
        database: {
          tableCount: metrics.database.table_count,
          totalSize: this.formatBytes(parseInt(metrics.database.total_size)),
          avgQueryTime: 0, // Seria obtido via logs
          connectionCount: metrics.database.connections,
        },
        storage: {
          bucketCount: metrics.storage.bucket_count,
          totalObjects: metrics.storage.total_objects,
          totalSize: this.formatBytes(metrics.storage.total_size),
          bandwidthUsed: this.formatBytes(metrics.storage.bandwidth_used),
        },
        realtime: {
          activeConnections: metrics.realtime.active_connections,
          messagesPerSecond: metrics.realtime.messages_per_second,
          channelsCount: metrics.realtime.channels_count,
        },
      };
    } catch (error) {
      logger.error('Erro ao obter métricas de performance:', error);
      throw error;
    }
  }

  /**
   * Formata bytes em formato legível
   */
  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}
