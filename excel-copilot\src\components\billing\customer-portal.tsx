import { Credit<PERSON>ard, <PERSON><PERSON>hart, Shield, Zap, ExternalLink } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { getPlanDisplayName } from '@/lib/stripe';

interface CustomerPortalProps {
  subscription: {
    id: string;
    plan: string;
    status: string;
    currentPeriodEnd: Date | null;
    apiCallsUsed: number;
    apiCallsLimit: number;
    cancelAtPeriodEnd: boolean;
  };
  onCreatePortalSession: () => Promise<string | null>;
}

export function CustomerPortal({ subscription, onCreatePortalSession }: CustomerPortalProps) {
  const [isLoading, setIsLoading] = useState(false);

  // Calcular a porcentagem de uso da API
  const apiUsagePercent = Math.min(
    Math.round((subscription.apiCallsUsed / subscription.apiCallsLimit) * 100),
    100
  );

  // Formatar a data de término do período
  const formatEndDate = (date: Date | null) => {
    if (!date) return 'N/A';
    return new Intl.DateTimeFormat('pt-BR', {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
    }).format(new Date(date));
  };

  // Abrir o portal do Stripe
  const handleManageSubscription = async () => {
    try {
      setIsLoading(true);
      const url = await onCreatePortalSession();
      if (url) {
        window.location.href = url;
      } else {
        throw new Error('Não foi possível obter o link do portal');
      }
    } catch (error) {
      console.error('Erro ao abrir portal de assinatura:', error);
      toast.error('Não foi possível acessar o portal de faturamento. Tente novamente mais tarde.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="border-primary/30">
      <CardHeader className="pb-2">
        <CardTitle className="flex items-center gap-2">
          <Shield
            className={subscription.plan !== 'free' ? 'text-primary' : 'text-muted-foreground'}
            size={18}
          />
          {getPlanDisplayName(subscription.plan)}

          {subscription.status === 'past_due' && (
            <span className="text-xs font-normal ml-2 py-0.5 px-2 rounded bg-amber-100 text-amber-800 dark:bg-amber-900/60 dark:text-amber-200">
              Pagamento pendente
            </span>
          )}

          {subscription.cancelAtPeriodEnd && (
            <span className="text-xs font-normal ml-2 py-0.5 px-2 rounded bg-rose-100 text-rose-800 dark:bg-rose-900/60 dark:text-rose-200">
              Cancelamento agendado
            </span>
          )}
        </CardTitle>
        <CardDescription>
          {subscription.id === 'free'
            ? 'Plano gratuito com recursos básicos'
            : 'Acesso a todos os recursos pro'}
        </CardDescription>
      </CardHeader>
      <CardContent className="pb-2 space-y-6">
        {/* Uso da API */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <div className="flex items-center gap-1.5 font-medium">
              <BarChart size={16} className="text-muted-foreground" />
              <span>Uso da API</span>
            </div>
            <span>
              {subscription.apiCallsUsed} / {subscription.apiCallsLimit} chamadas
            </span>
          </div>
          <Progress value={apiUsagePercent} className="h-2" />
          <p className="text-xs text-muted-foreground">
            {apiUsagePercent >= 80
              ? 'Você está próximo do seu limite mensal.'
              : 'Seu uso está dentro do esperado.'}
          </p>
        </div>

        {/* Informações adicionais */}
        {subscription.id !== 'free' && (
          <div className="space-y-4 pt-2">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <p className="text-muted-foreground mb-1">Status</p>
                <p className="font-medium">
                  {subscription.status === 'active' && 'Ativo'}
                  {subscription.status === 'trialing' && 'Em trial'}
                  {subscription.status === 'past_due' && 'Pagamento pendente'}
                  {subscription.status === 'canceled' && 'Cancelado'}
                  {subscription.status === 'incomplete' && 'Incompleto'}
                </p>
              </div>
              <div>
                <p className="text-muted-foreground mb-1">Próxima cobrança</p>
                <p className="font-medium">{formatEndDate(subscription.currentPeriodEnd)}</p>
              </div>
            </div>

            {subscription.cancelAtPeriodEnd && (
              <div className="rounded-md bg-amber-50 dark:bg-amber-950/30 p-3 text-sm text-amber-800 dark:text-amber-200 border border-amber-200 dark:border-amber-800/40">
                <p>
                  Sua assinatura será encerrada em {formatEndDate(subscription.currentPeriodEnd)}.
                </p>
              </div>
            )}
          </div>
        )}
      </CardContent>
      <CardFooter className="pt-2">
        {subscription.id !== 'free' ? (
          <Button
            onClick={() => handleManageSubscription()}
            className="w-full"
            disabled={isLoading}
          >
            {isLoading ? (
              'Carregando...'
            ) : (
              <>
                <CreditCard className="mr-2 h-4 w-4" />
                Gerenciar Assinatura
                <ExternalLink className="ml-2 h-3.5 w-3.5" />
              </>
            )}
          </Button>
        ) : (
          <Button
            variant="outline"
            className="w-full"
            onClick={() => (window.location.href = '/pricing')}
          >
            <Zap className="mr-2 h-4 w-4" />
            Fazer Upgrade
          </Button>
        )}
      </CardFooter>
    </Card>
  );
}
