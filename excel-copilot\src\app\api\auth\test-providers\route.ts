import { NextRequest, NextResponse } from 'next/server';

import { ENV } from '@/config/unified-environment';

export const dynamic = 'force-dynamic';

export async function GET(_request: NextRequest) {
  try {
    // Test providers logging

    // Verificar variáveis de ambiente
    const googleClientId = ENV.API_KEYS.GOOGLE_CLIENT_ID;
    const googleClientSecret = ENV.API_KEYS.GOOGLE_CLIENT_SECRET;
    const githubClientId = ENV.API_KEYS.GITHUB_CLIENT_ID;
    const githubClientSecret = ENV.API_KEYS.GITHUB_CLIENT_SECRET;
    const nextAuthSecret = ENV.NEXTAUTH_SECRET;
    const nextAuthUrl = ENV.NEXTAUTH_URL;

    // Testar se as variáveis estão definidas e não são strings vazias
    const googleConfigValid =
      googleClientId &&
      googleClientSecret &&
      googleClientId.length > 10 &&
      googleClientSecret.length > 10;

    const githubConfigValid =
      githubClientId &&
      githubClientSecret &&
      githubClientId.length > 10 &&
      githubClientSecret.length > 10;

    const nextAuthConfigValid =
      nextAuthSecret &&
      nextAuthUrl &&
      nextAuthSecret.length > 10 &&
      nextAuthUrl.startsWith('https://');

    // Verificar formato dos Client IDs
    const googleClientIdFormat = googleClientId?.includes('.apps.googleusercontent.com');
    const githubClientIdFormat =
      githubClientId?.match(/^[a-f0-9]{20}$/i) || githubClientId?.startsWith('Iv1.');

    // URLs de callback esperadas
    const expectedCallbacks = {
      google: `${nextAuthUrl}/api/auth/callback/google`,
      github: `${nextAuthUrl}/api/auth/callback/github`,
    };

    const testResults = {
      timestamp: new Date().toISOString(),
      environment: ENV.NODE_ENV,
      nextAuthUrl: nextAuthUrl,

      google: {
        configured: googleConfigValid,
        clientIdPresent: !!googleClientId,
        clientIdLength: googleClientId?.length || 0,
        clientIdFormat: googleClientIdFormat,
        clientSecretPresent: !!googleClientSecret,
        clientSecretLength: googleClientSecret?.length || 0,
        expectedCallback: expectedCallbacks.google,
        issues: [] as string[],
      },

      github: {
        configured: githubConfigValid,
        clientIdPresent: !!githubClientId,
        clientIdLength: githubClientId?.length || 0,
        clientIdFormat: githubClientIdFormat,
        clientSecretPresent: !!githubClientSecret,
        clientSecretLength: githubClientSecret?.length || 0,
        expectedCallback: expectedCallbacks.github,
        issues: [] as string[],
      },

      nextAuth: {
        configured: nextAuthConfigValid,
        secretPresent: !!nextAuthSecret,
        secretLength: nextAuthSecret?.length || 0,
        urlPresent: !!nextAuthUrl,
        urlFormat: nextAuthUrl?.startsWith('https://'),
        issues: [] as string[],
      },

      overallStatus: 'unknown' as string,
      recommendations: [] as string[],
    };

    // Adicionar problemas específicos
    if (!googleConfigValid) {
      if (!googleClientId) {
        testResults.google.issues.push('GOOGLE_CLIENT_ID não definido');
      } else if (googleClientId.length <= 10) {
        testResults.google.issues.push('GOOGLE_CLIENT_ID muito curto (possível erro)');
      }

      if (!googleClientSecret) {
        testResults.google.issues.push('GOOGLE_CLIENT_SECRET não definido');
      } else if (googleClientSecret.length <= 10) {
        testResults.google.issues.push('GOOGLE_CLIENT_SECRET muito curto (possível erro)');
      }

      if (!googleClientIdFormat) {
        testResults.google.issues.push(
          'GOOGLE_CLIENT_ID não tem formato esperado (.apps.googleusercontent.com)'
        );
      }
    }

    if (!githubConfigValid) {
      if (!githubClientId) {
        testResults.github.issues.push('GITHUB_CLIENT_ID não definido');
      } else if (githubClientId.length <= 10) {
        testResults.github.issues.push('GITHUB_CLIENT_ID muito curto (possível erro)');
      }

      if (!githubClientSecret) {
        testResults.github.issues.push('GITHUB_CLIENT_SECRET não definido');
      } else if (githubClientSecret.length <= 10) {
        testResults.github.issues.push('GITHUB_CLIENT_SECRET muito curto (possível erro)');
      }

      if (!githubClientIdFormat) {
        testResults.github.issues.push(
          'GITHUB_CLIENT_ID não tem formato esperado (20 chars hex ou Iv1.*)'
        );
      }
    }

    if (!nextAuthConfigValid) {
      if (!nextAuthSecret) {
        testResults.nextAuth.issues.push('NEXTAUTH_SECRET não definido');
      } else if (nextAuthSecret.length <= 10) {
        testResults.nextAuth.issues.push(
          'NEXTAUTH_SECRET muito curto (mínimo 32 caracteres recomendado)'
        );
      }

      if (!nextAuthUrl) {
        testResults.nextAuth.issues.push('NEXTAUTH_URL não definido');
      } else if (!nextAuthUrl.startsWith('https://')) {
        testResults.nextAuth.issues.push('NEXTAUTH_URL deve começar com https://');
      }
    }

    // Determinar status geral
    if (googleConfigValid && githubConfigValid && nextAuthConfigValid) {
      testResults.overallStatus = 'success';
    } else if (nextAuthConfigValid && (googleConfigValid || githubConfigValid)) {
      testResults.overallStatus = 'partial';
    } else {
      testResults.overallStatus = 'error';
    }

    // Adicionar recomendações
    if (!googleConfigValid) {
      testResults.recommendations.push('Configurar Google OAuth no Google Cloud Console');
      testResults.recommendations.push(
        'Verificar GOOGLE_CLIENT_ID e GOOGLE_CLIENT_SECRET no Vercel'
      );
    }

    if (!githubConfigValid) {
      testResults.recommendations.push('Configurar GitHub OAuth App no GitHub');
      testResults.recommendations.push(
        'Verificar GITHUB_CLIENT_ID e GITHUB_CLIENT_SECRET no Vercel'
      );
    }

    if (!nextAuthConfigValid) {
      testResults.recommendations.push('Configurar NEXTAUTH_SECRET e NEXTAUTH_URL no Vercel');
    }

    // Test providers completed

    return NextResponse.json(testResults, {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
      },
    });
  } catch (error) {
    console.error('🚨 [TEST-PROVIDERS] Erro durante teste:', error);

    return NextResponse.json(
      {
        error: 'Erro interno durante teste de provedores',
        message: error instanceof Error ? error.message : 'Erro desconhecido',
        timestamp: new Date().toISOString(),
      },
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
}
