// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
  relationMode = "prisma"
}

// Modelos para autenticação
model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@index([userId])
  @@index([provider])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([expires])
}

model User {
  id            String    @id @default(cuid())
  name          String?
  email         String?   @unique
  emailVerified DateTime?
  image         String?
  accounts      Account[]
  sessions      Session[]
  workbooks     Workbook[]
  chatHistory   ChatHistory[]
  subscriptions Subscription[]
  apiUsage      ApiUsage[]
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  @@index([email])
  @@index([updatedAt])
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@index([expires])
}

// Modelos específicos da aplicação
model Workbook {
  id          String   @id @default(cuid())
  name        String
  description String?  @db.Text
  userId      String
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  sheets      Sheet[]
  isPublic    Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  chatHistory ChatHistory[]

  @@index([userId])
  @@index([isPublic])
  @@index([updatedAt])
  @@index([name])
}

model Sheet {
  id         String   @id @default(cuid())
  name       String
  workbookId String
  workbook   Workbook @relation(fields: [workbookId], references: [id], onDelete: Cascade)
  data       String?  @db.Text // Armazena os dados da planilha em formato JSON (serializado)
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  @@index([workbookId])
  @@index([updatedAt])
  @@index([name])
}

model ChatHistory {
  id         String   @id @default(cuid())
  userId     String
  user       User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  message    String   @db.Text
  response   String   @db.Text
  workbookId String?
  workbook   Workbook? @relation(fields: [workbookId], references: [id], onDelete: SetNull)
  createdAt  DateTime @default(now())

  @@index([userId])
  @@index([workbookId])
  @@index([createdAt])
}

// Modelos de pagamento e assinatura
model Subscription {
  id                String   @id @default(cuid())
  userId            String
  user              User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  stripeCustomerId  String?
  stripeSubscriptionId String?  @unique
  stripePriceId     String?
  status            String   @default("active")
  plan              String
  cancelAtPeriodEnd Boolean  @default(false)
  currentPeriodStart DateTime?
  currentPeriodEnd   DateTime?
  apiCallsLimit     Int      @default(50)
  apiCallsUsed      Int      @default(0)
  payments          Payment[]
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  @@index([userId])
  @@index([status])
  @@index([plan])
  @@index([createdAt])
}

model Payment {
  id                String   @id @default(cuid())
  amount            Int      // em centavos
  currency          String   @default("BRL")
  stripePaymentId   String?
  stripeInvoiceId   String?
  status            String   // succeeded, pending, failed
  subscriptionId    String?
  subscription      Subscription? @relation(fields: [subscriptionId], references: [id], onDelete: SetNull)
  metadata          String?  @db.Text // JSON com informações adicionais
  createdAt         DateTime @default(now())

  @@index([subscriptionId])
  @@index([status])
  @@index([stripePaymentId])
}

model ApiUsage {
  id          String   @id @default(cuid())
  userId      String
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  count       Int      @default(1)
  endpoint    String   // qual endpoint foi usado (chat, análise, etc)
  workbookId  String?
  billable    Boolean  @default(true)
  createdAt   DateTime @default(now())

  @@index([userId])
  @@index([createdAt])
  @@index([endpoint])
  @@index([workbookId])
} 