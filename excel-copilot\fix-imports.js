const { exec } = require('child_process');
const fs = require('fs');
const path = require('path');

// Processa o resultado do ESLint para encontrar arquivos com problemas de import
function processLintOutput(output) {
  const fileIssues = {};

  // Extrair informações do output
  const lines = output.split('\n');
  let currentFile = null;

  for (const line of lines) {
    // Verifica se é uma linha de arquivo
    if (line.startsWith('./')) {
      currentFile = line.split(':')[0].substring(2); // Remove './'
    } else if (
      currentFile &&
      line.includes('Warning:') &&
      (line.includes('import/no-named-as-default') ||
        line.includes('import/no-named-as-default-member'))
    ) {
      // Extrai o número da linha e a recomendação
      const namedExportMatch = line.match(
        /Check if you meant to write `import \{([^}]+)\} from ['"]([^'"]+)['"]`/
      );
      if (namedExportMatch) {
        const [_, exportName, packageName] = namedExportMatch;

        if (!fileIssues[currentFile]) {
          fileIssues[currentFile] = [];
        }

        // Extrai o número da linha
        const lineMatch = line.match(/(\d+):(\d+)\s+Warning:/);
        if (lineMatch) {
          const [_, lineNum, colNum] = lineMatch;

          fileIssues[currentFile].push({
            line: parseInt(lineNum),
            column: parseInt(colNum),
            exportName: exportName.trim(),
            packageName: packageName.trim(),
            type: 'named-export',
          });
        }
      }
    }
  }

  return fileIssues;
}

// Corrige os problemas de import
function fixImports(fileIssues) {
  for (const [filePath, issues] of Object.entries(fileIssues)) {
    try {
      const fullPath = path.join(process.cwd(), filePath);
      const content = fs.readFileSync(fullPath, 'utf8');
      const lines = content.split('\n');

      // Ordenar problemas por linha em ordem decrescente
      issues.sort((a, b) => b.line - a.line);

      let modified = false;

      for (const issue of issues) {
        const line = lines[issue.line - 1];

        if (issue.type === 'named-export') {
          // Verifica se já é um import nomeado
          if (line.includes(`{ ${issue.exportName} }`)) {
            continue;
          }

          // Substitui import default por import nomeado
          const defaultImportRegex = new RegExp(
            `import\\s+([^\\s{]+)\\s+from\\s+['"]${issue.packageName}['"]`,
            'g'
          );
          const match = defaultImportRegex.exec(line);

          if (match) {
            const defaultName = match[1];
            const newLine = line.replace(
              defaultImportRegex,
              `import { ${issue.exportName} } from '${issue.packageName}'`
            );

            lines[issue.line - 1] = newLine;
            modified = true;
          }
        }
      }

      if (modified) {
        fs.writeFileSync(fullPath, lines.join('\n'), 'utf8');
        console.log(`✓ Corrigido: ${filePath}`);
      }
    } catch (error) {
      console.error(`Erro ao processar ${filePath}:`, error.message);
    }
  }
}

// Executa o lint e processa os resultados
exec('npm run lint', (error, stdout, stderr) => {
  if (error) {
    console.error(`Erro ao executar lint: ${error.message}`);
    return;
  }

  console.log('Processando resultados do lint para corrigir imports...');
  const fileIssues = processLintOutput(stdout);

  console.log(`Encontrados problemas de import em ${Object.keys(fileIssues).length} arquivos.`);
  fixImports(fileIssues);

  console.log('Concluído! Execute npm run lint novamente para verificar as correções.');
});
