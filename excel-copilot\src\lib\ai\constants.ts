/**
 * Constantes centralizadas para interceptação de IA no cliente
 * Este arquivo centraliza todas as listas e padrões usados pelos interceptadores
 */

// Lista padronizada de módulos que devem ser bloqueados no cliente
export const BLOCKED_AI_MODULES = [
  '@google/generative-ai',
  '@google-cloud/vertexai',
  '@google-cloud/aiplatform',
  '@google/genai',
  'google-generative-ai',
  'generative-ai',
] as const;

// Lista padronizada de domínios de API que devem ser bloqueados no cliente
export const BLOCKED_API_DOMAINS = [
  'aiplatform.googleapis.com',
  'generativelanguage.googleapis.com',
  'ml.googleapis.com',
] as const;

// Padrões de erro relacionados à IA que devem ser interceptados
export const AI_ERROR_PATTERNS = [
  'Neither apiKey nor config.authenticator provided',
  'GoogleGenerativeAI',
  'generative-ai',
  '_setAuthenticator',
  'Failed to fetch RSC payload',
] as const;

// Prefixos de logs dos interceptadores (para evitar suprimir nossos próprios logs)
export const INTERCEPTOR_LOG_PREFIXES = [
  '[AI Client Polyfill]',
  '[Error Interceptor]',
  '[Webpack Interceptor]',
] as const;

// Função utilitária para verificar se um módulo deve ser bloqueado
export function isBlockedAIModule(moduleId: string): boolean {
  return BLOCKED_AI_MODULES.some(blocked => moduleId.includes(blocked));
}

// Função utilitária para verificar se um domínio deve ser bloqueado
export function isBlockedAPIDomain(url: string): boolean {
  return BLOCKED_API_DOMAINS.some(domain => url.includes(domain));
}

// Função utilitária para verificar se uma mensagem é um erro relacionado à IA
export function isAIRelatedError(message: string): boolean {
  return AI_ERROR_PATTERNS.some(pattern => message.includes(pattern));
}

// Função utilitária para verificar se uma mensagem é de um dos nossos interceptadores
export function isInterceptorLog(message: string): boolean {
  return INTERCEPTOR_LOG_PREFIXES.some(prefix => message.includes(prefix));
}

// PRODUÇÃO: Classes de bloqueio para evitar uso de IA no cliente
// Estas não são mocks funcionais, apenas bloqueios de segurança
export const AI_BLOCK_CLASSES = {
  GoogleGenerativeAI: class BlockedGoogleGenerativeAI {
    constructor() {
      throw new Error('GoogleGenerativeAI não é permitido no cliente. Use APIs do servidor.');
    }
  },

  VertexAI: class BlockedVertexAI {
    constructor() {
      throw new Error('VertexAI não é permitido no cliente. Use APIs do servidor.');
    }
  },

  GenerativeModel: class BlockedGenerativeModel {
    constructor() {
      throw new Error('GenerativeModel não é permitido no cliente. Use APIs do servidor.');
    }
  },
} as const;

const AIConstants = {
  BLOCKED_AI_MODULES,
  BLOCKED_API_DOMAINS,
  AI_ERROR_PATTERNS,
  INTERCEPTOR_LOG_PREFIXES,
  AI_BLOCK_CLASSES,
  isBlockedAIModule,
  isBlockedAPIDomain,
  isAIRelatedError,
  isInterceptorLog,
};

export default AIConstants;
