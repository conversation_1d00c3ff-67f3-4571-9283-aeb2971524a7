const { exec } = require('child_process');
const fs = require('fs');
const path = require('path');

// Processa o resultado do ESLint para encontrar arquivos com problemas de exhaustive-deps
function processLintOutput(output) {
  const fileIssues = {};

  // Extrair informações do output
  const lines = output.split('\n');
  let currentFile = null;

  for (const line of lines) {
    // Verifica se é uma linha de arquivo
    if (line.startsWith('./')) {
      currentFile = line.split(':')[0].substring(2); // Remove './'
    } else if (
      currentFile &&
      line.includes('Warning:') &&
      line.includes('react-hooks/exhaustive-deps')
    ) {
      // Extrai o número da linha
      const match = line.match(/(\d+):(\d+)\s+Warning:/);
      if (match) {
        const [_, lineNum, colNum] = match;

        if (!fileIssues[currentFile]) {
          fileIssues[currentFile] = [];
        }

        fileIssues[currentFile].push({
          line: parseInt(lineNum),
          column: parseInt(colNum),
        });
      }
    }
  }

  return fileIssues;
}

// Adiciona eslint-disable para problemas de exhaustive-deps
function addEslintDisables(fileIssues) {
  for (const [filePath, issues] of Object.entries(fileIssues)) {
    try {
      const fullPath = path.join(process.cwd(), filePath);
      const content = fs.readFileSync(fullPath, 'utf8');
      const lines = content.split('\n');

      // Ordenar problemas por linha em ordem decrescente para evitar problemas com índices
      issues.sort((a, b) => b.line - a.line);

      let modified = false;

      for (const issue of issues) {
        // Verifica se já existe um eslint-disable na linha anterior
        const previousLine = lines[issue.line - 2];
        if (
          previousLine &&
          (previousLine.includes('eslint-disable-next-line') ||
            previousLine.includes('react-hooks/exhaustive-deps'))
        ) {
          continue;
        }

        // Adiciona o eslint-disable-next-line antes da linha com o problema
        const indentation = lines[issue.line - 1].match(/^\s*/)[0];
        lines.splice(
          issue.line - 1,
          0,
          `${indentation}// eslint-disable-next-line react-hooks/exhaustive-deps`
        );

        // Ajusta as linhas dos outros problemas (que vêm depois deste)
        for (let i = 0; i < issues.length; i++) {
          if (issues[i].line > issue.line) {
            issues[i].line++;
          }
        }

        modified = true;
      }

      if (modified) {
        fs.writeFileSync(fullPath, lines.join('\n'), 'utf8');
        console.log(`✓ Corrigido: ${filePath}`);
      }
    } catch (error) {
      console.error(`Erro ao processar ${filePath}:`, error.message);
    }
  }
}

// Executa o lint e processa os resultados
exec('npm run lint', (error, stdout, stderr) => {
  if (error) {
    console.error(`Erro ao executar lint: ${error.message}`);
    return;
  }

  console.log('Processando resultados do lint para corrigir problemas de React Hooks...');
  const fileIssues = processLintOutput(stdout);

  console.log(
    `Encontrados problemas de exhaustive-deps em ${Object.keys(fileIssues).length} arquivos.`
  );
  addEslintDisables(fileIssues);

  console.log('Concluído! Execute npm run lint novamente para verificar as correções.');
});
