/**
 * Web Worker para processamento de operações Excel em thread separada
 *
 * Este worker executa operações pesadas de Excel (fórmulas, gráficos, filtros, etc.)
 * em uma thread separada para não bloquear a UI principal.
 *
 * Compatível com Vercel serverless e Next.js
 */

import { ExcelOperation, ExcelOperationType } from '../types';
import { executeOperation } from '../lib/operations';
import { logger } from '../lib/logger';

// Logger seguro para workers - só loga em desenvolvimento
const isDev = process.env.NODE_ENV !== 'production';
const workerLogger = {
  log: (message: string, metadata?: any) => {
    if (isDev) logger.debug(message, metadata);
  },
  error: (message: string, error?: any) => {
    // Erros sempre são logados, mesmo em produção
    logger.error(message, error, { component: 'ExcelWorker' });
  }
};

// Interfaces para comunicação com o worker
interface WorkerMessage {
  operation: ExcelOperation;
  sheetData: unknown;
  requestId: string;
  operationType: string;
}

interface WorkerResponse {
  requestId: string;
  success: boolean;
  result?: {
    updatedData: unknown;
    resultSummary: string;
    modifiedCells?: Array<{ row: number; col: number }>;
  };
  error?: string;
}

// Configuração do worker
const WORKER_CONFIG = {
  timeout: 30000, // 30 segundos
  maxRetries: 3,
  enableLogging: true,
};

/**
 * Processa uma operação Excel
 */
async function processExcelOperation(
  operation: ExcelOperation,
  sheetData: unknown
): Promise<{ updatedData: unknown; resultSummary: string; modifiedCells?: Array<{ row: number; col: number }> }> {
  try {
    // Log da operação (apenas em desenvolvimento)
    if (WORKER_CONFIG.enableLogging) {
      workerLogger.log(`Processando operação: ${operation.type}`, {
        operationType: operation.type,
        hasData: !!sheetData,
        operationId: operation.id,
      });
    }

    // Executar a operação usando o sistema existente
    const result = await executeOperation(sheetData, operation);

    // Normalizar resultado para o formato esperado
    const normalizedResult = {
      updatedData: result.updatedData,
      resultSummary: result.resultSummary,
      modifiedCells: [], // TODO: Implementar tracking de células modificadas
    };

    if (WORKER_CONFIG.enableLogging) {
      workerLogger.log(`Operação concluída: ${operation.type}`, {
        success: true,
        resultSummary: result.resultSummary,
      });
    }

    return normalizedResult;
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';

    if (WORKER_CONFIG.enableLogging) {
      console.error(`[Excel Worker] Erro na operação: ${operation.type}`, {
        error: errorMessage,
        operationType: operation.type,
        operationId: operation.id,
      });
    }

    throw new Error(`Erro ao processar operação ${operation.type}: ${errorMessage}`);
  }
}

/**
 * Valida se a operação é suportada
 */
function validateOperation(operation: ExcelOperation): void {
  if (!operation || typeof operation !== 'object') {
    throw new Error('Operação inválida: deve ser um objeto');
  }

  if (!operation.type) {
    throw new Error('Operação inválida: tipo não especificado');
  }

  // Verificar se o tipo é suportado
  const supportedTypes = [
    ExcelOperationType.FORMULA,
    ExcelOperationType.CHART,
    ExcelOperationType.FILTER,
    ExcelOperationType.SORT,
    ExcelOperationType.CELL_UPDATE,
    ExcelOperationType.TABLE,
  ];

  if (!supportedTypes.includes(operation.type as ExcelOperationType)) {
    throw new Error(`Tipo de operação não suportado: ${operation.type}`);
  }

  if (!operation.data) {
    throw new Error('Operação inválida: dados não especificados');
  }
}

/**
 * Handler principal de mensagens do worker
 */
self.onmessage = async (event: MessageEvent<WorkerMessage>) => {
  const { operation, sheetData, requestId, operationType } = event.data;

  let response: WorkerResponse;

  try {
    // Validar entrada
    if (!requestId) {
      throw new Error('ID da requisição não fornecido');
    }

    if (!operation) {
      throw new Error('Operação não fornecida');
    }

    // Validar operação
    validateOperation(operation);

    // Processar operação
    const result = await processExcelOperation(operation, sheetData);

    // Resposta de sucesso
    response = {
      requestId,
      success: true,
      result,
    };

  } catch (error) {
    // Resposta de erro
    const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido no worker';

    response = {
      requestId: requestId || 'unknown',
      success: false,
      error: errorMessage,
    };

    // Log do erro
    if (WORKER_CONFIG.enableLogging) {
      workerLogger.error('Erro fatal no processamento', {
        error: errorMessage,
        requestId,
        operationType,
      });
    }
  }

  // Enviar resposta de volta para a thread principal
  self.postMessage(response);
};

/**
 * Handler de erros não capturados
 */
self.onerror = (error) => {
  workerLogger.error('Erro não capturado no worker', error);

  // Extrair mensagem de erro de forma segura
  const errorMessage = typeof error === 'string'
    ? error
    : error instanceof ErrorEvent
      ? error.message
      : 'Erro desconhecido';

  // Tentar enviar resposta de erro se possível
  self.postMessage({
    requestId: 'error',
    success: false,
    error: `Erro fatal no worker: ${errorMessage}`,
  });
};

/**
 * Handler de erros de promise rejeitadas
 */
self.onunhandledrejection = (event) => {
  workerLogger.error('Promise rejeitada não tratada', event.reason);

  // Tentar enviar resposta de erro se possível
  self.postMessage({
    requestId: 'unhandled-rejection',
    success: false,
    error: `Promise rejeitada: ${event.reason || 'Erro desconhecido'}`,
  });
};

// Log de inicialização
if (WORKER_CONFIG.enableLogging) {
  workerLogger.log('Worker inicializado com sucesso', {
    timeout: WORKER_CONFIG.timeout,
    maxRetries: WORKER_CONFIG.maxRetries,
    supportedOperations: [
      'FORMULA',
      'CHART',
      'FILTER',
      'SORT',
      'CELL_UPDATE',
      'TABLE',
    ],
  });
}