/**
 * Utilitários para migração de rotas do Pages Router para App Router
 *
 * Este módulo fornece funções e tipos para facilitar a migração de rotas,
 * garantindo consistência e compatibilidade entre as diferentes abordagens.
 */

import { NextApiRequest, NextApiResponse } from 'next';
import { NextRequest, NextResponse } from 'next/server';

/**
 * Tipo para handlers do Pages Router
 */
export type PagesHandler = (req: NextApiRequest, res: NextApiResponse) => Promise<void>;

/**
 * Tipo para handlers do App Router
 */
export type AppHandler = (request: NextRequest) => Promise<NextResponse>;

/**
 * Tipos de métodos HTTP suportados
 */
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';

/**
 * Interface para funções mockadas com Jest
 */
interface JestMockFunction<T = any, Y extends any[] = any[]> {
  (...args: Y): T;
  mock: {
    calls: Y[];
    instances: T[];
    invocationCallOrder: number[];
    results: { type: string; value: T }[];
  };
  mockReturnThis: () => JestMockFunction<T, Y>;
}

/**
 * Interface para o objeto de resposta mockado
 */
interface MockNextApiResponse {
  statusCode?: number;
  status: JestMockFunction<MockNextApiResponse, [number]>;
  json: JestMockFunction<void, [any]>;
  send: JestMockFunction<void, [any]>;
  setHeader: JestMockFunction<NextApiResponse, [string, string | number | readonly string[]]>;
  end: JestMockFunction<NextApiResponse, [any?]>;
}

/**
 * Converte um handler do Pages Router para o formato App Router
 *
 * @param pagesHandler - Handler no formato Pages Router
 * @returns Handler no formato App Router
 */
export function convertToAppHandler(pagesHandler: PagesHandler): AppHandler {
  return async function appHandler(request: NextRequest): Promise<NextResponse> {
    // Criar resposta mock para capturar os métodos do NextApiResponse
    const mockResponse: MockNextApiResponse = {
      status: jest.fn().mockReturnThis() as any,
      json: jest.fn() as any,
      send: jest.fn() as any,
      setHeader: jest.fn() as any,
      end: jest.fn() as any,
    };

    // Criar request mock compatível com NextApiRequest
    const mockRequest: Partial<NextApiRequest> = {
      headers: Object.fromEntries(request.headers.entries()),
      method: request.method,
      body: await getRequestBody(request),
      query: Object.fromEntries(new URL(request.url).searchParams.entries()),
      cookies: Object.fromEntries(
        request.cookies.getAll().map(cookie => [cookie.name, cookie.value])
      ),
    };

    try {
      // Executar o handler original com mocks
      await pagesHandler(mockRequest as NextApiRequest, mockResponse as unknown as NextApiResponse);

      // Verificar qual método foi chamado para determinar a resposta
      if (mockResponse.json && mockResponse.json.mock.calls.length > 0) {
        const jsonData = mockResponse.json.mock.calls[0]?.[0];
        const statusCode = mockResponse.status?.mock.calls[0]?.[0] ?? 200;

        return NextResponse.json(jsonData, { status: statusCode });
      }

      if (mockResponse.send && mockResponse.send.mock.calls.length > 0) {
        const data = mockResponse.send.mock.calls[0]?.[0];
        const statusCode = mockResponse.status?.mock.calls[0]?.[0] ?? 200;

        // Se dados json, retornar como json
        if (typeof data === 'object') {
          return NextResponse.json(data, { status: statusCode });
        }

        // Caso contrário, retornar como texto
        const response = new NextResponse(String(data), { status: statusCode });

        // Aplicar headers se existirem
        if (mockResponse.setHeader) {
          mockResponse.setHeader.mock.calls.forEach(
            ([name, value]: [string, string | number | readonly string[]]) => {
              response.headers.set(name.toString(), value.toString());
            }
          );
        }

        return response;
      }

      // Resposta vazia (end foi chamado)
      if (mockResponse.end && mockResponse.end.mock.calls.length > 0) {
        const statusCode = mockResponse.status?.mock.calls[0]?.[0] ?? 204;
        return new NextResponse(null, { status: statusCode });
      }

      // Fallback
      return NextResponse.json(
        { message: 'Handler executed but no response method was called' },
        { status: 500 }
      );
    } catch (error) {
      console.error('Erro ao executar handler migrado:', error);
      return NextResponse.json(
        {
          error: 'Internal Server Error',
          message: error instanceof Error ? error.message : 'Unknown error',
        },
        { status: 500 }
      );
    }
  };
}

/**
 * Extrai o corpo da requisição de um NextRequest
 */
async function getRequestBody(request: NextRequest): Promise<any> {
  const contentType = request.headers.get('content-type') || '';

  if (contentType.includes('application/json')) {
    try {
      return await request.json();
    } catch {
      return {};
    }
  }

  if (contentType.includes('application/x-www-form-urlencoded')) {
    try {
      const formData = await request.formData();
      return Object.fromEntries(formData.entries());
    } catch {
      return {};
    }
  }

  return {};
}

/**
 * Cria um objeto de rotas para App Router a partir de um handler do Pages Router
 *
 * @param handler - Handler do Pages Router ou objeto com handlers por método
 * @returns Objeto com métodos HTTP para App Router
 *
 * @example
 * // Em app/api/exemplo/route.ts
 * import { createAppRouteHandlers } from '@/utils/route-migration';
 * import { handler } from '@/pages/api/exemplo';
 *
 * export const { GET, POST } = createAppRouteHandlers(handler);
 */
export function createAppRouteHandlers(
  handler: PagesHandler | Record<HttpMethod, PagesHandler>
): Record<HttpMethod, AppHandler> {
  // Se for um único handler, criar handlers para todos os métodos
  if (typeof handler === 'function') {
    const appHandler = convertToAppHandler(handler);
    return {
      GET: appHandler,
      POST: appHandler,
      PUT: appHandler,
      DELETE: appHandler,
      PATCH: appHandler,
    };
  }

  // Se for um objeto com handlers por método, converter cada um
  const result: Partial<Record<HttpMethod, AppHandler>> = {};

  (Object.entries(handler) as [HttpMethod, PagesHandler][]).forEach(([method, methodHandler]) => {
    result[method] = convertToAppHandler(methodHandler);
  });

  return result as Record<HttpMethod, AppHandler>;
}

/**
 * Adiciona aviso de depreciação a um handler do Pages Router
 *
 * @param handler - Handler original
 * @param newPath - Caminho para a nova API
 * @returns Handler com aviso de depreciação
 */
export function deprecateHandler(handler: PagesHandler, newPath: string): PagesHandler {
  return async (req: NextApiRequest, res: NextApiResponse) => {
    // Adicionar header de aviso
    res.setHeader('X-API-Deprecated', 'true');
    res.setHeader(
      'X-API-Deprecation-Warning',
      `Esta rota será descontinuada. Migre para: ${newPath}`
    );

    // Log de aviso
    console.warn(
      `[DEPRECATED API] Rota legada acessada: ${req.url}. ` +
        `Migre para: ${newPath}. ` +
        `User-Agent: ${req.headers['user-agent'] || 'Não informado'}. ` +
        `Referer: ${req.headers.referer || 'Não informado'}`
    );

    // Executar handler original
    return handler(req, res);
  };
}
