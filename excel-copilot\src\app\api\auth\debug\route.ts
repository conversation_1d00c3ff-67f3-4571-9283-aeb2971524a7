import { NextRequest, NextResponse } from 'next/server';

import { ENV } from '@/config/unified-environment';

// Forçar o modo dinâmico para essa rota (corrige erro de static generation)
export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
  try {
    // Auth debug logging

    // Verificar variáveis de ambiente críticas
    const authConfig = {
      NEXTAUTH_SECRET: !!ENV.NEXTAUTH_SECRET,
      NEXTAUTH_URL: ENV.NEXTAUTH_URL,
      GOOGLE_CLIENT_ID: !!ENV.API_KEYS.GOOGLE_CLIENT_ID,
      GOOGLE_CLIENT_SECRET: !!ENV.API_KEYS.GOOGLE_CLIENT_SECRET,
      GITHUB_CLIENT_ID: !!ENV.API_KEYS.GITHUB_CLIENT_ID,
      GITHUB_CLIENT_SECRET: !!ENV.API_KEYS.GITHUB_CLIENT_SECRET,
      DATABASE_URL: !!ENV.DATABASE_URL,
      NODE_ENV: ENV.NODE_ENV,
      IS_PRODUCTION: ENV.IS_PRODUCTION,
      IS_DEVELOPMENT: ENV.IS_DEVELOPMENT,
    };

    // Verificar se as credenciais estão configuradas
    const googleConfigured = authConfig.GOOGLE_CLIENT_ID && authConfig.GOOGLE_CLIENT_SECRET;
    const githubConfigured = authConfig.GITHUB_CLIENT_ID && authConfig.GITHUB_CLIENT_SECRET;
    const basicConfigured =
      authConfig.NEXTAUTH_SECRET && authConfig.NEXTAUTH_URL && authConfig.DATABASE_URL;

    const issues = [];

    if (!basicConfigured) {
      if (!authConfig.NEXTAUTH_SECRET) issues.push('NEXTAUTH_SECRET não configurado');
      if (!authConfig.NEXTAUTH_URL) issues.push('NEXTAUTH_URL não configurado');
      if (!authConfig.DATABASE_URL) issues.push('DATABASE_URL não configurado');
    }

    if (!googleConfigured) {
      issues.push('Credenciais do Google não configuradas (GOOGLE_CLIENT_ID/GOOGLE_CLIENT_SECRET)');
    }

    if (!githubConfigured) {
      issues.push('Credenciais do GitHub não configuradas (GITHUB_CLIENT_ID/GITHUB_CLIENT_SECRET)');
    }

    const debugInfo = {
      timestamp: new Date().toISOString(),
      environment: ENV.NODE_ENV,
      authConfig,
      providersStatus: {
        google: googleConfigured ? 'Configurado' : 'Não configurado',
        github: githubConfigured ? 'Configurado' : 'Não configurado',
      },
      issues,
      ready: issues.length === 0,
      url: request.url,
      headers: Object.fromEntries(request.headers.entries()),
    };

    // Debug info logged

    return NextResponse.json(debugInfo, {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
      },
    });
  } catch (error) {
    console.error('🚨 [AUTH-DEBUG] Erro ao verificar configuração:', error);

    return NextResponse.json(
      {
        error: 'Erro ao verificar configuração de autenticação',
        details: error instanceof Error ? error.message : 'Erro desconhecido',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
