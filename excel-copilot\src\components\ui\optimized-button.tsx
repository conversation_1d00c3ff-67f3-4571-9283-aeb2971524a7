/**
 * Versão otimizada do Button com React.memo e comparação customizada
 * Este componente resolve problemas de performance em re-renderizações desnecessárias
 */

'use client';

import React, { memo } from 'react';

import { usePerformanceMonitor } from '@/utils/performance-monitor';

import { Button, type ButtonProps } from './button';

/**
 * Função de comparação customizada para React.memo
 * Evita re-renderizações quando apenas props irrelevantes mudaram
 */
function areButtonPropsEqual(prevProps: ButtonProps, nextProps: ButtonProps): boolean {
  // Comparar props críticas que afetam a renderização
  const criticalProps: (keyof ButtonProps)[] = [
    'variant',
    'size',
    'disabled',
    'children',
    'className',
    'animated',
    'icon',
    'iconPosition',
    'asChild',
  ];

  // Verificar se alguma prop crítica mudou
  for (const prop of criticalProps) {
    if (prevProps[prop] !== nextProps[prop]) {
      return false;
    }
  }

  // Comparação especial para children se for ReactNode complexo
  if (typeof prevProps.children === 'object' && typeof nextProps.children === 'object') {
    // Para objetos React, usar comparação de referência
    return prevProps.children === nextProps.children;
  }

  // Comparação especial para handlers de eventos
  const eventHandlers = ['onClick', 'onMouseEnter', 'onMouseLeave', 'onFocus', 'onBlur'];
  for (const handler of eventHandlers) {
    const prevHandler = prevProps[handler as keyof ButtonProps];
    const nextHandler = nextProps[handler as keyof ButtonProps];

    // Se ambos são undefined/null, continuar
    if (!prevHandler && !nextHandler) continue;

    // Se apenas um é undefined/null, são diferentes
    if (!prevHandler || !nextHandler) return false;

    // Para funções, comparar referência (assumindo que funções estáveis são memoizadas)
    if (prevHandler !== nextHandler) return false;
  }

  return true;
}

/**
 * Button otimizado com React.memo
 * Use este componente em listas ou componentes que re-renderizam frequentemente
 */
const OptimizedButtonComponent = memo(Button, areButtonPropsEqual);

export const OptimizedButton = React.forwardRef<HTMLButtonElement, ButtonProps>((props, ref) => {
  usePerformanceMonitor('OptimizedButton', true);
  return <OptimizedButtonComponent {...props} ref={ref} />;
});

OptimizedButton.displayName = 'OptimizedButton';

/**
 * Hook para criar props de botão estáveis
 * Use este hook quando você precisar passar handlers que não mudam frequentemente
 */
export function useStableButtonProps(
  baseProps: Omit<ButtonProps, 'onClick'>,
  onClick?: () => void,
  _dependencies: React.DependencyList = []
): ButtonProps {
  const stableOnClick = React.useCallback(() => {
    onClick?.();
  }, [onClick]);

  return React.useMemo(
    () => ({
      ...baseProps,
      onClick: stableOnClick,
    }),
    [baseProps, stableOnClick]
  );
}

/**
 * Componente de botão para ações frequentes (como em listas)
 * Automaticamente otimizado para performance
 */
interface ActionButtonProps extends Omit<ButtonProps, 'onClick'> {
  onAction: () => void;
  actionId?: string | number;
}

export const ActionButton = memo<ActionButtonProps>(
  ({ onAction, actionId, ...buttonProps }) => {
    usePerformanceMonitor('ActionButton', true);

    const handleClick = React.useCallback(() => {
      onAction();
    }, [onAction]); // actionId não precisa estar nas dependências pois não é usado na função

    return <Button {...buttonProps} onClick={handleClick} />;
  },
  (prevProps, nextProps) => {
    // Comparação customizada incluindo actionId
    return (
      areButtonPropsEqual(prevProps, nextProps) &&
      prevProps.actionId === nextProps.actionId &&
      prevProps.onAction === nextProps.onAction
    );
  }
);

ActionButton.displayName = 'ActionButton';

export default OptimizedButton;
