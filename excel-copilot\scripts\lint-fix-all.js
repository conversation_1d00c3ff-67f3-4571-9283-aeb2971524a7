#!/usr/bin/env node

/**
 * Script para executar todos os processos de linting e formatação
 * em um único comando, seguin<PERSON> as melhores práticas.
 */

const { execSync } = require('child_process');

// Cores para console
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  magenta: '\x1b[35m',
};

// Função para executar comandos e lidar com erros
function runCommand(command, options = {}) {
  const { ignoreError = false, showOutput = true } = options;

  console.log(`${colors.blue}> Executando:${colors.reset} ${command}`);

  try {
    const output = execSync(command, {
      encoding: 'utf-8',
      stdio: showOutput ? 'inherit' : 'pipe',
    });

    return { success: true, output };
  } catch (error) {
    if (!ignoreError) {
      console.error(`${colors.red}Erro ao executar comando:${colors.reset} ${command}`);
      console.error(`${colors.yellow}${error.message}${colors.reset}`);
    }

    return { success: false, error };
  }
}

// Função para exibir progresso
function showProgress(step, total, message) {
  const percent = Math.floor((step / total) * 100);
  const progressBar =
    '█'.repeat(Math.floor(percent / 4)) + '░'.repeat(25 - Math.floor(percent / 4));
  console.log(`${colors.cyan}[${progressBar}] ${percent}%${colors.reset} - ${message}`);
}

// Passos de linting
const steps = [
  {
    name: 'Corrigindo variáveis não utilizadas',
    command: 'node scripts/fix-unused-vars.js',
    ignoreError: true,
  },
  {
    name: 'Formatando código com Prettier',
    command: 'node scripts/format-code.js',
    ignoreError: true,
  },
  {
    name: 'Executando ESLint com correção automática',
    command: 'npx eslint --fix src/**/*.{ts,tsx}',
    ignoreError: true,
  },
  {
    name: 'Removendo imports não utilizados',
    command:
      'npx eslint --fix --rule "unused-imports/no-unused-imports: error" --plugin unused-imports "src/**/*.{ts,tsx}"',
    ignoreError: true,
  },
  {
    name: 'Verificando problemas restantes',
    command: 'npx eslint src/**/*.{ts,tsx}',
    ignoreError: true,
    showOutput: true,
  },
];

// Função principal
async function main() {
  console.log(`\n${colors.magenta}=== PROCESSO DE LINTING AUTOMATIZADO ===${colors.reset}\n`);
  console.log(
    `${colors.yellow}Este script executará todos os passos de linting em sequência.${colors.reset}`
  );
  console.log(
    `${colors.yellow}Alguns erros podem ser ignorados para permitir que o processo continue.${colors.reset}\n`
  );

  const startTime = Date.now();

  for (let i = 0; i < steps.length; i++) {
    const step = steps[i];
    showProgress(i, steps.length, step.name);

    const result = runCommand(step.command, {
      ignoreError: step.ignoreError,
      showOutput: step.showOutput !== false,
    });

    if (!result.success && !step.ignoreError) {
      console.error(
        `\n${colors.red}O processo de linting falhou no passo: ${step.name}${colors.reset}`
      );
      process.exit(1);
    }

    console.log(`${colors.green}✓ Concluído: ${step.name}${colors.reset}\n`);
  }

  const endTime = Date.now();
  const duration = ((endTime - startTime) / 1000).toFixed(2);

  console.log(`\n${colors.magenta}=== PROCESSO DE LINTING CONCLUÍDO ===${colors.reset}`);
  console.log(`${colors.green}Tempo total: ${duration} segundos${colors.reset}`);
  console.log(
    `${colors.yellow}Verifique os warnings restantes e corrija-os manualmente conforme necessário.${colors.reset}`
  );
  console.log(
    `${colors.cyan}Para mais detalhes, consulte o arquivo LINTING_STATUS.md${colors.reset}\n`
  );
}

main().catch(error => {
  console.error(`${colors.red}Erro ao executar o script:${colors.reset}`, error);
  process.exit(1);
});
