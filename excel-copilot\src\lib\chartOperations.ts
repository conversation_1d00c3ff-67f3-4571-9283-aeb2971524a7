import { logger } from '@/lib/logger';
import { prisma } from '@/server/db/client';

import { ExcelOperation } from './excel/types';
import { canAddChart as checkChartLimit } from './subscription-limits';

// Definindo um tipo para resultado da verificação de limites
type _LimitCheckResult = {
  allowed: boolean;
  message?: string | undefined;
  limit: number;
};

/**
 * Tipos de gráficos suportados
 */
export type ChartType =
  | 'bar'
  | 'column'
  | 'line'
  | 'pie'
  | 'scatter'
  | 'area'
  | 'doughnut'
  | 'radar'
  | 'bubble';

/**
 * Interface para dados de operação de gráfico
 */
export interface ChartOperationData {
  type: ChartType;
  title?: string;
  dataRange: {
    columns: string[]; // Pode ser nomes de coluna ou letras (A, B, etc.)
    startRow?: number;
    endRow?: number;
  };
  options?: {
    showLegend?: boolean;
    xAxisTitle?: string;
    yAxisTitle?: string;
    colors?: string[];
  };
}

/**
 * Executa uma operação de gráfico com verificações de segurança adicionais
 * @param sheetData Dados da planilha
 * @param operation Operação a ser executada
 * @returns Dados atualizados e resumo da operação
 */
interface SheetDataWithCharts {
  charts?: Array<unknown>;
  [key: string]: unknown;
}

export async function executeChartOperation(
  sheetData: unknown,
  operation: ExcelOperation,
  userId?: string,
  sheetId?: string
): Promise<{ updatedData: unknown; resultSummary: string }> {
  try {
    // Verificar se os dados da operação são válidos
    validateChartOperation(operation);

    // Verificar se já existem gráficos
    const typedSheetData = sheetData as SheetDataWithCharts;
    const existingCharts = Array.isArray(typedSheetData.charts) ? typedSheetData.charts : [];

    // Se houver userId e sheetId, verificar limite de gráficos
    if (userId && sheetId) {
      // Verificar se o usuário é proprietário da planilha
      const isOwner = await validateSheetOwnership(userId, sheetId);
      if (!isOwner) {
        // Registrar tentativa de acesso não autorizado
        await logSecurityEvent(userId, 'unauthorized_chart_operation', {
          sheetId,
          operation: 'add_chart',
          chartType: operation.chartType,
        });

        throw new Error('Você não tem permissão para adicionar gráficos a esta planilha.');
      }

      // Verificar limites do plano
      const limitCheck = await canAddChart(userId, sheetId, existingCharts.length);
      if (!limitCheck.allowed) {
        // Registrar tentativa de burlar limite
        await logSecurityEvent(userId, 'limit_exceeded', {
          operation: 'add_chart',
          currentCount: existingCharts.length,
          limit: limitCheck.limit,
        });

        throw new Error(limitCheck.message || `Limite de gráficos excedido para seu plano.`);
      }

      // Registrar operação bem-sucedida para auditoria
      await logUserAction(userId, 'chart_added', {
        sheetId,
        chartType: operation.chartType,
        chartCount: existingCharts.length + 1,
      });
    }

    // Clonar dados para não modificar o original
    const updatedData: SheetDataWithCharts = { ...(sheetData as object) };

    // Inicializar array de gráficos se não existir
    if (!updatedData.charts) {
      updatedData.charts = [];
    }

    // Limitar número máximo de gráficos para evitar DoS
    const MAX_CHARTS_ALLOWED = 50;
    if (updatedData.charts.length >= MAX_CHARTS_ALLOWED) {
      throw new Error(`Número máximo de gráficos excedido (${MAX_CHARTS_ALLOWED}).`);
    }

    // Criar o novo gráfico com validação de dados
    const newChart = {
      id: `chart_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: sanitizeChartType(operation.chartType || 'column'),
      dataRange: sanitizeDataRange(operation.dataRange),
      position: sanitizePosition(operation.position || 'auto'),
      title: sanitizeText(operation.title) || `Gráfico de ${operation.chartType || 'coluna'}`,
      config: sanitizeConfig(operation.config || {}),
    };

    // Adicionar o novo gráfico
    updatedData.charts.push(newChart);

    return {
      updatedData,
      resultSummary: `Gráfico de ${operation.chartType} criado com dados de ${operation.dataRange}`,
    };
  } catch (error) {
    logger.error('[CHART_OPERATION_ERROR]', { operation, error });
    throw error instanceof Error ? error : new Error('Erro ao executar operação de gráfico');
  }
}

/**
 * Validar operação de gráfico para evitar injeção de dados maliciosos
 */
function validateChartOperation(operation: ExcelOperation): void {
  if (!operation) {
    throw new Error('Operação de gráfico inválida');
  }

  if (operation.type !== 'chart') {
    throw new Error(`Tipo de operação inválido: ${operation.type}. Esperado: 'chart'`);
  }

  if (!operation.chartType) {
    throw new Error('Tipo de gráfico não especificado');
  }

  if (!operation.dataRange) {
    throw new Error('Range de dados não especificado');
  }

  // Verificar se o tipo de gráfico é suportado
  const supportedTypes = [
    'bar',
    'column',
    'line',
    'pie',
    'scatter',
    'area',
    'doughnut',
    'radar',
    'bubble',
  ];
  if (!supportedTypes.includes(operation.chartType)) {
    throw new Error(`Tipo de gráfico não suportado: ${operation.chartType}`);
  }

  // Verificar formato de range
  if (typeof operation.dataRange === 'string') {
    if (!/^[A-Z]+[0-9]+:[A-Z]+[0-9]+$/.test(operation.dataRange)) {
      throw new Error(`Formato de range inválido: ${operation.dataRange}`);
    }
  }
}

/**
 * Sanitiza o tipo de gráfico
 */
function sanitizeChartType(type: string): ChartType {
  const validTypes: ChartType[] = [
    'bar',
    'column',
    'line',
    'pie',
    'scatter',
    'area',
    'doughnut',
    'radar',
    'bubble',
  ];
  return validTypes.includes(type as ChartType) ? (type as ChartType) : 'column';
}

/**
 * Sanitiza o range de dados
 */
function sanitizeDataRange(range: unknown): string {
  if (typeof range === 'string') {
    // Verificar se o formato é válido (ex: A1:C5)
    if (/^[A-Z]+[0-9]+:[A-Z]+[0-9]+$/.test(range)) {
      return range;
    }
  }

  // Valor padrão seguro se o range for inválido
  return 'A1:B2';
}

/**
 * Sanitiza posição do gráfico
 */
function sanitizePosition(position: unknown): string {
  const validPositions = ['auto', 'top', 'bottom', 'left', 'right', 'center'];

  if (typeof position === 'string' && validPositions.includes(position)) {
    return position;
  }

  // Posição padrão se for inválida
  return 'auto';
}

/**
 * Sanitiza texto para evitar XSS
 */
function sanitizeText(text: unknown): string {
  if (typeof text !== 'string') {
    return '';
  }

  // Remover possíveis tags HTML e limitar tamanho
  return text.replace(/<[^>]*>/g, '').substring(0, 100);
}

/**
 * Sanitiza configurações do gráfico
 */
interface ChartConfig {
  showLegend?: boolean;
  xAxisTitle?: string;
  yAxisTitle?: string;
  colors?: string[];
  [key: string]: unknown;
}

function sanitizeConfig(config: unknown): ChartConfig {
  if (typeof config !== 'object' || config === null) {
    return {};
  }

  const typedConfig = config as Record<string, unknown>;
  const sanitizedConfig: ChartConfig = {};

  // Permitir apenas propriedades seguras
  const allowedProps = ['showLegend', 'xAxisTitle', 'yAxisTitle', 'colors'];

  for (const prop of allowedProps) {
    if (prop in typedConfig) {
      if (prop === 'colors' && Array.isArray(typedConfig.colors)) {
        // Validar cores - permitir apenas formatos CSS válidos
        sanitizedConfig.colors = typedConfig.colors
          .filter((color): color is string => typeof color === 'string')
          .map(color => (color.match(/^(rgba?\(|hsla?\(|#)/i) ? color : '#000000'))
          .slice(0, 10); // Limitar número de cores
      } else if (prop === 'showLegend') {
        sanitizedConfig.showLegend = Boolean(typedConfig.showLegend);
      } else if (typeof typedConfig[prop] === 'string') {
        if (prop === 'xAxisTitle' || prop === 'yAxisTitle') {
          sanitizedConfig[prop] = sanitizeText(typedConfig[prop] as string);
        }
      }
    }
  }

  return sanitizedConfig;
}

/**
 * Verifica se o usuário é proprietário da planilha
 */
async function validateSheetOwnership(userId: string, sheetId: string): Promise<boolean> {
  try {
    const sheet = await prisma.sheet.findUnique({
      where: { id: sheetId },
      include: { workbook: true },
    });

    return sheet?.workbook.userId === userId;
  } catch (error) {
    logger.error('[VALIDATE_SHEET_OWNERSHIP_ERROR]', { userId, sheetId, error });
    return false;
  }
}

/**
 * Registra eventos de segurança para auditoria
 */
async function logSecurityEvent(
  userId: string,
  eventType: string,
  details: unknown
): Promise<void> {
  try {
    await prisma.securityLog.create({
      data: {
        userId,
        eventType,
        details: JSON.stringify(details),
        timestamp: new Date(),
      },
    });
  } catch (error) {
    logger.error('[SECURITY_LOG_ERROR]', { userId, eventType, details, error });
  }
}

/**
 * Registra ações do usuário para análise e detecção de padrões
 */
async function logUserAction(userId: string, action: string, details: unknown): Promise<void> {
  try {
    await prisma.userActionLog.create({
      data: {
        userId,
        action,
        details: JSON.stringify(details),
        timestamp: new Date(),
      },
    });
  } catch (error) {
    logger.error('[USER_ACTION_LOG_ERROR]', { userId, action, details, error });
  }
}

// Função exportada para uso em outros arquivos
export async function canAddChart(
  userId: string,
  sheetId: string,
  currentChartCount: number
): Promise<_LimitCheckResult> {
  try {
    // Usar a implementação do subscription-limits.ts quando disponível
    return await checkChartLimit(userId, sheetId, currentChartCount);
  } catch (error) {
    logger.error('Erro ao verificar limite de gráficos', { userId, sheetId, error });
    // Fallback para implementação local em caso de erro
    const defaultLimit = 5; // Limite padrão razoável
    const allowed = currentChartCount < defaultLimit;

    return {
      allowed,
      message: allowed ? undefined : `Limite de ${defaultLimit} gráficos atingido.`,
      limit: defaultLimit,
    };
  }
}
