import React from 'react';
import { render } from '@testing-library/react';
import '@testing-library/jest-dom';

// Importar componentes para testar
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui';
import { Label } from '@/components/ui/label';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

// Mock dos componentes que dependem de navegador/window
jest.mock('@/components/ui/toast', () => ({
  ToastProvider: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="toast-provider">{children}</div>
  ),
  Toaster: () => <div data-testid="toaster" />,
}));

jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    back: jest.fn(),
    forward: jest.fn(),
  }),
  useSearchParams: () => ({ get: jest.fn() }),
}));

describe('Testes de Regressão Visual', () => {
  // Testes de snapshot para componentes de UI básicos
  describe('Componentes de UI Básicos', () => {
    test('Button renderiza corretamente', () => {
      const { container } = render(<Button>Botão de Teste</Button>);
      expect(container).toMatchSnapshot();
    });

    test('Button com variant e size renderiza corretamente', () => {
      const { container } = render(
        <Button variant="destructive" size="sm">
          Botão Pequeno Destrutivo
        </Button>
      );
      expect(container).toMatchSnapshot();
    });

    test('Card com título renderiza corretamente', () => {
      const { container } = render(
        <Card>
          <CardHeader>
            <CardTitle>Título do Card</CardTitle>
          </CardHeader>
          <CardContent>
            <p>Conteúdo do card para teste</p>
          </CardContent>
        </Card>
      );
      expect(container).toMatchSnapshot();
    });

    test('Input renderiza corretamente', () => {
      const { container } = render(<Input placeholder="Digite aqui..." />);
      expect(container).toMatchSnapshot();
    });

    test('Input com Label renderiza corretamente', () => {
      const { container } = render(
        <div>
          <Label htmlFor="email">Email</Label>
          <Input id="email" placeholder="Digite seu email" type="email" />
        </div>
      );
      expect(container).toMatchSnapshot();
    });

    test('Tabs renderizam corretamente', () => {
      const { container } = render(
        <Tabs defaultValue="tab1">
          <TabsList>
            <TabsTrigger value="tab1">Aba 1</TabsTrigger>
            <TabsTrigger value="tab2">Aba 2</TabsTrigger>
          </TabsList>
          <TabsContent value="tab1">Conteúdo da Aba 1</TabsContent>
          <TabsContent value="tab2">Conteúdo da Aba 2</TabsContent>
        </Tabs>
      );
      expect(container).toMatchSnapshot();
    });
  });

  // Testes para verificar comportamento responsivo
  describe('Responsividade', () => {
    beforeAll(() => {
      // Configurar viewport para mobile
      Object.defineProperty(window, 'innerWidth', { value: 375, configurable: true });
      Object.defineProperty(window, 'innerHeight', { value: 667, configurable: true });
      window.dispatchEvent(new Event('resize'));
    });

    test('Componentes são responsivos em viewport mobile', () => {
      const { container } = render(
        <div style={{ width: '100%' }}>
          <Card className="w-full">
            <CardHeader>
              <CardTitle>Card Responsivo</CardTitle>
            </CardHeader>
            <CardContent>
              <Input placeholder="Input responsivo" />
              <Button className="mt-4 w-full">Botão Responsivo</Button>
            </CardContent>
          </Card>
        </div>
      );
      expect(container).toMatchSnapshot('mobile-viewport');
    });

    afterAll(() => {
      // Restaurar viewport para desktop
      Object.defineProperty(window, 'innerWidth', { value: 1280, configurable: true });
      Object.defineProperty(window, 'innerHeight', { value: 800, configurable: true });
      window.dispatchEvent(new Event('resize'));
    });
  });
});
