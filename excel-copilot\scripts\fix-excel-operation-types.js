/**
 * <PERSON>ript para automatizar a correção de tipos de operação Excel
 * Este script substitui strings literais por valores do enum ExcelOperationType
 */
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Diretório base para buscar os arquivos
const srcDirs = [path.join(__dirname, '..', 'src')];

// Extensões de arquivo a considerar
const fileExtensions = ['.ts', '.tsx'];

// Mapeamento de tipos de operação
const operationTypeMap = {
  formula: 'ExcelOperationType.FORMULA',
  filter: 'ExcelOperationType.FILTER',
  sort: 'ExcelOperationType.SORT',
  format: 'ExcelOperationType.FORMAT',
  chart: 'ExcelOperationType.CHART',
  cell: 'ExcelOperationType.CELL',
  table: 'ExcelOperationType.TABLE',
  CELL_UPDATE: 'ExcelOperationType.CELL_UPDATE',
  COLUMN_OPERATION: 'ExcelOperationType.COLUMN_OPERATION',
  ROW_OPERATION: 'ExcelOperationType.ROW_OPERATION',
  DATA_TRANSFORMATION: 'ExcelOperationType.DATA_TRANSFORMATION',
  CONDITIONAL_FORMAT: 'ExcelOperationType.CONDITIONAL_FORMAT',
  ADVANCED_CHART: 'ExcelOperationType.ADVANCED_CHART',
  ADVANCED_VISUALIZATION: 'ExcelOperationType.ADVANCED_VISUALIZATION',
  CONDITIONAL_FORMATTING: 'ExcelOperationType.CONDITIONAL_FORMATTING',
};

// Padrões de string para buscar ocorrências
const patterns = [
  // Padrão para type: 'nome_do_tipo',
  /type:\s*['"]([a-zA-Z_]+)['"],/g,

  // Padrão para case 'nome_do_tipo':
  /case\s*['"]([a-zA-Z_]+)['"]:/g,

  // Padrão para type === 'nome_do_tipo'
  /type\s*===\s*['"]([a-zA-Z_]+)['"]|type\s*==\s*['"]([a-zA-Z_]+)['"]|op\.type\s*===\s*['"]([a-zA-Z_]+)['"]|op\.type\s*==\s*['"]([a-zA-Z_]+)['"]/g,
];

/**
 * Verifica e adiciona a importação necessária para ExcelOperationType
 * @param {string} content Conteúdo do arquivo
 * @returns {string} Conteúdo atualizado com importação se necessário
 */
function ensureImport(content, hasChanges) {
  // Se não tivemos mudanças, não precisamos adicionar importação
  if (!hasChanges) return content;

  // Verificar se o arquivo já importa ExcelOperationType
  if (
    content.includes('import { ExcelOperationType }') ||
    content.includes('import {ExcelOperationType}') ||
    content.includes('import type { ExcelOperationType }')
  ) {
    return content;
  }

  // Verificar de onde importar
  const importSource = content.includes('@/types') ? '@/types/index' : '@/types';

  // Adicionar importação
  const importStatement = `import { ExcelOperationType } from '${importSource}';\n`;

  // Inserir após as importações existentes ou no topo do arquivo
  const importSection = content.match(/import.*?from.*?;(\r?\n|$)/gs);
  if (importSection && importSection.length > 0) {
    const lastImport = importSection[importSection.length - 1];
    const lastImportIndex = content.lastIndexOf(lastImport) + lastImport.length;
    return (
      content.substring(0, lastImportIndex) + importStatement + content.substring(lastImportIndex)
    );
  } else {
    return importStatement + content;
  }
}

/**
 * Corrige tipos de operação em um arquivo
 * @param {string} filePath Caminho do arquivo
 */
function fixExcelOperationTypes(filePath) {
  console.log(`\nVerificando: ${filePath}`);

  // Ler conteúdo do arquivo
  let content = fs.readFileSync(filePath, 'utf8');
  const originalContent = content;

  // Realizar substituições para cada padrão
  let hasChanges = false;
  for (const pattern of patterns) {
    let match;
    while ((match = pattern.exec(content)) !== null) {
      const typeString = match[1] || match[2] || match[3] || match[4];
      if (typeString && operationTypeMap[typeString]) {
        // Encontrar o texto exato para substituição
        const fullMatch = match[0];
        let replacement;

        // Criar substituição apropriada
        if (fullMatch.includes('type:')) {
          replacement = fullMatch
            .replace(`'${typeString}'`, operationTypeMap[typeString])
            .replace(`"${typeString}"`, operationTypeMap[typeString]);
        } else if (fullMatch.includes('case')) {
          replacement = fullMatch
            .replace(`'${typeString}'`, operationTypeMap[typeString])
            .replace(`"${typeString}"`, operationTypeMap[typeString]);
        } else if (fullMatch.includes('===') || fullMatch.includes('==')) {
          replacement = fullMatch
            .replace(`'${typeString}'`, operationTypeMap[typeString])
            .replace(`"${typeString}"`, operationTypeMap[typeString]);
        }

        if (replacement !== fullMatch) {
          console.log(`Substituindo: ${fullMatch.trim()} -> ${replacement.trim()}`);
          content = content.replace(fullMatch, replacement);
          hasChanges = true;
        }
      }
    }
  }

  // Garantir que a importação de ExcelOperationType esteja presente
  content = ensureImport(content, hasChanges);

  // Salvar alterações
  if (content !== originalContent) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`✅ Corrigido arquivo: ${filePath}`);
    return true;
  } else {
    console.log(`✓ Nenhuma alteração necessária em: ${filePath}`);
    return false;
  }
}

/**
 * Processa diretório recursivamente
 * @param {string} dir Diretório a ser processado
 */
function processDirectory(dir) {
  const files = fs.readdirSync(dir);

  for (const file of files) {
    const fullPath = path.join(dir, file);
    const stat = fs.statSync(fullPath);

    if (stat.isDirectory()) {
      processDirectory(fullPath);
    } else if (fileExtensions.some(ext => file.endsWith(ext))) {
      // Verificar se é um arquivo de operações do Excel
      if (
        file.includes('Operation') ||
        file.includes('operation') ||
        fullPath.includes('excel') ||
        fullPath.includes('Excel')
      ) {
        fixExcelOperationTypes(fullPath);
      }
    }
  }
}

/**
 * Função principal
 */
function main() {
  console.log('🔍 Iniciando correção de tipos de operação Excel...');

  // Processar diretórios
  for (const dir of srcDirs) {
    if (fs.existsSync(dir)) {
      console.log(`\nProcessando diretório: ${dir}`);
      processDirectory(dir);
    } else {
      console.log(`⚠️ Diretório não encontrado: ${dir}`);
    }
  }

  console.log('\n✅ Processo concluído!');
  console.log('Execute a verificação de tipos para ver se os problemas foram resolvidos:');
  console.log('npm run typecheck');
}

// Executar script
main();
