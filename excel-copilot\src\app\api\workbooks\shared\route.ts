// Forçar o modo dinâmico para essa rota
export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

import { NextRequest, NextResponse } from 'next/server';

import { ENV } from '@/config/unified-environment';
import { getCurrentUserId } from '@/lib/auth';
import { logger } from '@/lib/logger';
import { prisma } from '@/server/db/client';

/**
 * GET /api/workbooks/shared
 * Listar planilhas compartilhadas com o usuário
 */
export async function GET(_req: NextRequest): Promise<NextResponse> {
  try {
    // Verificar autenticação e obter ID do usuário
    const userId = await getCurrentUserId();

    if (!userId) {
      if (ENV.IS_DEVELOPMENT && ENV.FEATURES?.SKIP_AUTH_PROVIDERS) {
        logger.warn('Permitindo acesso sem autenticação em modo de desenvolvimento');

        // Retornar planilhas mock para desenvolvimento
        return NextResponse.json({
          workbooks: [
            {
              id: 'shared-1',
              name: 'Planilha Compartilhada 1',
              createdAt: new Date(),
              updatedAt: new Date(),
              sharedBy: {
                name: 'Usuário Exemplo',
                email: '<EMAIL>',
              },
              sheets: [{ id: 'sheet-1' }],
            },
            {
              id: 'shared-2',
              name: 'Planilha Compartilhada 2',
              createdAt: new Date(),
              updatedAt: new Date(),
              sharedBy: {
                name: 'Outro Usuário',
                email: '<EMAIL>',
              },
              sheets: [{ id: 'sheet-2' }],
            },
          ],
        });
      }

      return NextResponse.json(
        { error: 'Não autorizado. Faça login para continuar.' },
        { status: 401 }
      );
    }

    // Buscar planilhas compartilhadas com o usuário usando o novo modelo WorkbookShare
    const sharedWorkbooks = await prisma.workbookShare.findMany({
      where: {
        sharedWithUserId: userId,
      },
      include: {
        workbook: {
          include: {
            sheets: {
              select: {
                id: true,
              },
            },
          },
        },
        sharedByUser: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    // Formatar os resultados para a resposta
    const formattedSharedWorkbooks = sharedWorkbooks.map(share => ({
      id: share.workbook.id,
      name: share.workbook.name,
      createdAt: share.workbook.createdAt,
      updatedAt: share.workbook.updatedAt,
      sheets: share.workbook.sheets,
      sharedBy: {
        id: share.sharedByUser.id,
        name: share.sharedByUser.name,
        email: share.sharedByUser.email,
        image: share.sharedByUser.image,
      },
      sharedAt: share.createdAt,
      permissionLevel: share.permissionLevel,
    }));

    return NextResponse.json({ workbooks: formattedSharedWorkbooks });
  } catch (error) {
    logger.error('Erro ao listar workbooks compartilhados:', error);
    return NextResponse.json(
      {
        error: 'Erro ao buscar planilhas compartilhadas',
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/workbooks/shared
 * Compartilhar uma planilha com outro usuário
 */
export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    // Verificar autenticação e obter ID do usuário
    const userId = await getCurrentUserId();

    if (!userId) {
      return NextResponse.json(
        { error: 'Não autorizado. Faça login para continuar.' },
        { status: 401 }
      );
    }

    // Obter dados do corpo da requisição
    const data = await req.json();
    const { workbookId, userEmail, permissionLevel = 'READ' } = data;

    if (!workbookId || !userEmail) {
      return NextResponse.json(
        {
          error: 'Dados inválidos',
          details: 'ID da planilha e email do usuário são obrigatórios',
        },
        { status: 400 }
      );
    }

    // Verificar se a planilha existe e pertence ao usuário atual
    const workbook = await prisma.workbook.findFirst({
      where: {
        id: workbookId,
        userId: userId,
      },
    });

    if (!workbook) {
      return NextResponse.json(
        {
          error: 'Planilha não encontrada ou sem permissão',
          details: 'A planilha não existe ou você não tem permissão para compartilhá-la',
        },
        { status: 404 }
      );
    }

    // Buscar o usuário com quem compartilhar pelo email
    const targetUser = await prisma.user.findUnique({
      where: {
        email: userEmail,
      },
      select: {
        id: true,
        email: true,
      },
    });

    if (!targetUser) {
      return NextResponse.json(
        {
          error: 'Usuário não encontrado',
          details: 'Não foi encontrado um usuário com este email',
        },
        { status: 404 }
      );
    }

    // Verificar se já existe um compartilhamento para este usuário e planilha
    const existingShare = await prisma.workbookShare.findUnique({
      where: {
        workbookId_sharedWithUserId: {
          workbookId: workbookId,
          sharedWithUserId: targetUser.id,
        },
      },
    });

    if (existingShare) {
      // Atualizar permissão se já existir
      const updatedShare = await prisma.workbookShare.update({
        where: {
          id: existingShare.id,
        },
        data: {
          permissionLevel: permissionLevel,
          updatedAt: new Date(),
        },
      });

      return NextResponse.json({
        message: 'Permissão de compartilhamento atualizada',
        share: updatedShare,
      });
    }

    // Criar novo compartilhamento
    const newShare = await prisma.workbookShare.create({
      data: {
        workbookId: workbookId,
        sharedByUserId: userId,
        sharedWithUserId: targetUser.id,
        permissionLevel: permissionLevel,
      },
    });

    return NextResponse.json(
      {
        message: 'Planilha compartilhada com sucesso',
        share: newShare,
      },
      { status: 201 }
    );
  } catch (error) {
    logger.error('Erro ao compartilhar planilha:', error);
    return NextResponse.json(
      {
        error: 'Erro ao compartilhar planilha',
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
}
