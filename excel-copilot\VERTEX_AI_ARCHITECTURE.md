# 🚀 Arquitetura de IA - Vertex AI (Google Cloud Platform)

## 📋 **V<PERSON><PERSON> Geral**

O Excel Copilot utiliza **exclusivamente** o Google Cloud Vertex AI para funcionalidades de IA, **NÃO** o Google AI Studio. Esta arquitetura garante maior segurança, escalabilidade e controle empresarial.

## 🔧 **Configuração de Autenticação**

### **Método Recomendado: Service Account (Produção)**

1. **Arquivo de Credenciais JSON**:

   ```
   excel-copilot/vertex-credentials.json
   ```

2. **Estrutura do Arquivo**:
   ```json
   ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
   ```

### **Variáveis de Ambiente**

```bash
# Configuração do Vertex AI
VERTEX_AI_ENABLED=true
VERTEX_AI_PROJECT_ID="seu-projeto-gcp"
VERTEX_AI_LOCATION="us-central1"
VERTEX_AI_MODEL_NAME="gemini-1.5-pro"

# Opcional: Caminho customizado para credenciais
VERTEX_AI_CREDENTIALS_PATH="/caminho/para/credenciais.json"

# Alternativa: API Key (menos seguro)
VERTEX_AI_API_KEY="sua-api-key-vertex-ai"
```

## 🏗️ **Arquitetura do Sistema**

### **Componentes Principais**

1. **GeminiService** (`src/lib/ai/gemini-service.ts`)

   - Serviço principal que usa Vertex AI
   - Gerencia cache e rate limiting
   - Implementa retry com backoff exponencial

2. **AIProvider** (`src/lib/ai/provider.ts`)

   - Interface unificada para IA
   - Carregamento dinâmico no servidor
   - Fallback para mock no cliente

3. **Dynamic Import Manager** (`src/lib/ai/dynamic-import.ts`)
   - Carregamento seguro de módulos Vertex AI
   - Prevenção de erros no cliente

### **Fluxo de Execução**

```
Cliente → AIProvider → Dynamic Import → GeminiService → Vertex AI
                                    ↓
                                Mock (fallback)
```

## 🔒 **Segurança**

### **Boas Práticas Implementadas**

- ✅ Service Account com permissões mínimas
- ✅ Credenciais nunca expostas no cliente
- ✅ Carregamento dinâmico apenas no servidor
- ✅ Validação de entrada e sanitização
- ✅ Rate limiting e timeout configuráveis

### **Permissões Necessárias no GCP**

```
roles/aiplatform.user
roles/ml.developer
```

## 🚫 **O que NÃO Usar**

### **Google AI Studio (Removido)**

- ❌ `@google/generative-ai`
- ❌ `GoogleGenerativeAI`
- ❌ `GOOGLE_AI_API_KEY`

### **Por que Vertex AI é Superior**

1. **Segurança Empresarial**: Service accounts vs API keys
2. **Controle de Acesso**: IAM granular
3. **Auditoria**: Logs detalhados no Cloud Console
4. **Escalabilidade**: Quotas empresariais
5. **Compliance**: Certificações de segurança

## 🔧 **Configuração na Vercel**

### **Variáveis de Ambiente Necessárias**

```bash
# Controle de IA
USE_MOCK_AI=false
VERTEX_AI_ENABLED=true
VERTEX_AI_PROJECT_ID=seu-projeto-gcp
VERTEX_AI_LOCATION=us-central1
VERTEX_AI_MODEL_NAME=gemini-1.5-pro

# Credenciais (via variável de ambiente)
VERTEX_AI_CREDENTIALS='{"type":"service_account",...}'
```

### **Modo Mock (Desenvolvimento/Fallback)**

```bash
# Para desenvolvimento sem credenciais
USE_MOCK_AI=true
VERTEX_AI_ENABLED=false
```

## 🐛 **Resolução de Problemas**

### **Erro: "Neither apiKey nor config.authenticator provided"**

**Causa**: Tentativa de usar Google AI Studio em vez de Vertex AI

**Solução**:

1. Verificar se `VERTEX_AI_ENABLED=true`
2. Confirmar que credenciais estão configuradas
3. Verificar se não há imports de `@google/generative-ai`

### **Erro: "Project ID not found"**

**Solução**:

1. Definir `VERTEX_AI_PROJECT_ID`
2. Verificar credenciais JSON
3. Confirmar permissões no GCP

### **Modo Mock Ativado Inesperadamente**

**Verificar**:

1. `USE_MOCK_AI=false`
2. `VERTEX_AI_ENABLED=true`
3. Credenciais válidas
4. Ambiente servidor (não cliente)

## 📊 **Monitoramento**

### **Logs Importantes**

```
✅ "Cliente Vertex AI inicializado com sucesso"
✅ "GeminiService inicializado com sucesso"
⚠️  "Usando modo mock para respostas de IA"
❌ "Erro ao inicializar cliente Vertex AI"
```

### **Health Check**

```bash
# Verificar status da IA
curl https://seu-app.vercel.app/api/health/ai
```

## 🔄 **Migração de Google AI Studio**

Se você encontrar código usando Google AI Studio:

1. **Remover dependências**:

   ```bash
   npm uninstall @google/generative-ai
   ```

2. **Substituir imports**:

   ```typescript
   // ❌ Remover
   import { GoogleGenerativeAI } from '@google/generative-ai';

   // ✅ Usar
   import { VertexAI } from '@google-cloud/vertexai';
   ```

3. **Atualizar configuração**:

   ```typescript
   // ❌ Remover
   const genAI = new GoogleGenerativeAI(apiKey);

   // ✅ Usar
   const vertexAI = new VertexAI({
     project: projectId,
     location: location,
   });
   ```

## 📞 **Suporte**

Para problemas relacionados à configuração do Vertex AI:

1. Verificar logs da aplicação
2. Validar credenciais no GCP Console
3. Confirmar quotas e limites
4. Testar com modo mock primeiro
