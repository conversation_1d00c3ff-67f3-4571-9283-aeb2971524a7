# Configuração do Supabase para o Excel Copilot

## Visão Geral

Este documento descreve como configurar corretamente o Supabase como banco de dados para o Excel Copilot, incluindo a integração com Prisma ORM. Essa configuração foi otimizada para desempenho e escalabilidade.

## Pré-requisitos

- Conta ativa no Supabase
- Projeto criado no Supabase com PostgreSQL ativo
- Credenciais de acesso ao banco de dados (senha do usuário postgres)

## Configuração Passo a Passo

### 1. Configurar as URLs de Conexão

Para o Excel Copilot, precisamos de duas URLs de conexão diferentes:

1. **URL de Conexão Pooling** (para a aplicação em produção)

   ```
   postgresql://postgres:[SENHA]@aws-0-[REGION].pooler.supabase.co:6543/postgres?pgbouncer=true&connection_limit=1&pool_timeout=20
   ```

2. **URL de Conexão Direta** (para operações CLI do Prisma)
   ```
   postgresql://postgres:[SENHA]@db.[ID-PROJETO].supabase.co:5432/postgres
   ```

### 2. Preparar o arquivo .env

Use o script `create-env.js` para gerar o arquivo .env:

```bash
node create-env.js production
```

Ou crie/atualize o arquivo `.env` manualmente com as seguintes configurações:

```env
# URL para conexão com pooling (pgbouncer=true) - uso geral da aplicação
DATABASE_URL="postgresql://postgres:[SENHA]@aws-0-[REGION].pooler.supabase.co:6543/postgres?pgbouncer=true&connection_limit=1&pool_timeout=20"

# URL direta para migrações e operações CLI do Prisma
DIRECT_URL="postgresql://postgres:[SENHA]@db.[ID-PROJETO].supabase.co:5432/postgres"

# Outras configurações de ambiente...
```

> **Importante**: Certifique-se de que todos os caracteres especiais na senha estejam devidamente codificados usando `encodeURIComponent()` no JavaScript.

### 3. Verificar o schema.prisma

Confirme que o arquivo `prisma/schema.prisma` inclui o campo `directUrl` e as configurações necessárias:

```prisma
generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["metrics"]
  binaryTargets   = ["native", "rhel-openssl-1.0.x"]
}

datasource db {
  provider     = "postgresql"
  url          = env("DATABASE_URL")      // Conexão transação para aplicação (porta 6543)
  directUrl    = env("DIRECT_URL")        // Conexão direta para migrations (porta 5432)
  relationMode = "prisma"
}
```

### 4. Gerar o Cliente Prisma

Execute o comando para gerar o cliente Prisma atualizado:

```bash
npx prisma generate
```

### 5. Executar Migrações

Para aplicar migrações existentes ao banco de dados:

```bash
npx prisma migrate deploy
```

Para criar novas migrações:

```bash
npx prisma migrate dev --name [nome-da-migracao]
```

## Configuração na Vercel

Para configurar o banco de dados na Vercel:

1. Acesse seu projeto na Vercel
2. Vá para **Settings > Environment Variables**
3. Adicione as variáveis `DATABASE_URL` e `DIRECT_URL` com os mesmos valores do arquivo `.env`
4. Salve as alterações e reimplemente o projeto

## Verificação da Conexão

Após a implantação, você pode verificar a conexão com o banco de dados acessando:

```
https://[seu-dominio]/api/health/db
```

Este endpoint retornará um status `healthy` se a conexão com o banco de dados estiver funcionando corretamente.

## Solução de Problemas

### Erro de Autenticação

Se você encontrar o erro `P1000: Authentication failed against database server`:

1. Verifique se a senha está corretamente codificada com `encodeURIComponent()`
2. Confirme se a senha é válida tentando fazer login diretamente no Supabase
3. Verifique se não há espaços extras na string de conexão

### Erro "Can't reach database server"

Se você encontrar o erro `P1001: Can't reach database server`:

1. Verifique se o IP da sua máquina/servidor está na lista de IPs permitidos nas configurações de rede do Supabase
2. Confirme se o host está correto (formato `db.[ID-PROJETO].supabase.co`)
3. Para servidores Vercel, verifique se está usando o modo pooling correto

### Erro com Prepared Statements

Se você encontrar erros relacionados a prepared statements:

1. Certifique-se de que a URL para a aplicação inclui `?pgbouncer=true`
2. Verifique se está usando a porta correta para o Supavisor (6543, não 5432)
3. Adicione o parâmetro `?pgbouncer=true&connection_limit=1` à URL

## Otimizações Avançadas

Para otimizar ainda mais o desempenho do banco de dados:

1. **Configurar índices adequados** para suas consultas mais frequentes
2. **Implementar caching de consultas** para reduzir a carga no banco de dados
3. **Monitorar consultas lentas** e otimizá-las
4. **Configurar timeouts adequados** para evitar conexões penduradas

## Notas de Segurança

- Nunca armazene a senha do banco de dados em arquivos de código-fonte
- Use variáveis de ambiente para armazenar as URLs de conexão
- Considere criar um usuário de banco de dados dedicado (não postgres) para produção
- Configure regras de acesso ao Supabase para maior segurança
- Implemente backups regulares do banco de dados

## Recursos Adicionais

- [Documentação do Prisma para Supabase](https://www.prisma.io/docs/orm/overview/databases/supabase)
- [Guia do Supabase para conexão PostgreSQL](https://supabase.com/docs/guides/database/connecting-to-postgres)
- [Solução de problemas Prisma + Supabase](https://supabase.com/docs/guides/database/prisma/prisma-troubleshooting)
