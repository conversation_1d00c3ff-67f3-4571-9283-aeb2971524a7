#!/usr/bin/env node

/**
 * Script para facilitar o deploy do Excel Copilot na Vercel
 * Este script verifica se todas as configurações necessárias estão prontas
 * antes de iniciar o deploy.
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
});

// Cores para console
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
};

// Banner
console.log(`${colors.bright}${colors.blue}
========================================================
   Excel Copilot - Assistente de Deploy para Vercel
========================================================
${colors.reset}`);

/**
 * Executa um comando shell
 */
function runCommand(command) {
  try {
    return execSync(command, { stdio: 'inherit' });
  } catch (error) {
    console.error(`${colors.red}Erro ao executar comando: ${command}${colors.reset}`);
    console.error(error);
    return false;
  }
}

/**
 * Verifica se um pacote está instalado
 */
function isPackageInstalled(packageName) {
  try {
    execSync(`npm list ${packageName} --depth=0`, { stdio: 'ignore' });
    return true;
  } catch (error) {
    return false;
  }
}

/**
 * Verifica se o CLI da Vercel está instalado
 */
function checkVercelCLI() {
  console.log(`${colors.cyan}Verificando CLI da Vercel...${colors.reset}`);

  if (!isPackageInstalled('vercel')) {
    console.log(`${colors.yellow}CLI da Vercel não encontrado. Instalando...${colors.reset}`);
    runCommand('npm install -g vercel');
    return true;
  }

  console.log(`${colors.green}✓ CLI da Vercel já está instalado.${colors.reset}`);
  return true;
}

/**
 * Verifica se as variáveis de ambiente necessárias estão configuradas
 */
function checkEnvVars() {
  console.log(`${colors.cyan}Verificando configurações de ambiente...${colors.reset}`);

  const requiredVars = ['DATABASE_URL', 'NEXTAUTH_SECRET', 'NEXTAUTH_URL'];

  const missingVars = [];

  // Verifica se o arquivo .env existe na raiz do projeto
  const envPath = path.join(process.cwd(), '.env');
  if (!fs.existsSync(envPath)) {
    console.log(`${colors.yellow}Arquivo .env não encontrado.${colors.reset}`);
    return false;
  }

  // Lê o arquivo .env
  const envContent = fs.readFileSync(envPath, 'utf8');
  const envLines = envContent.split('\n');

  // Procura por cada variável requerida
  for (const varName of requiredVars) {
    let found = false;

    for (const line of envLines) {
      if (line.trim().startsWith(`${varName}=`) && !line.trim().startsWith('#')) {
        found = true;
        break;
      }
    }

    if (!found) {
      missingVars.push(varName);
    }
  }

  if (missingVars.length > 0) {
    console.log(
      `${colors.yellow}As seguintes variáveis de ambiente estão faltando no arquivo .env:${colors.reset}`
    );
    missingVars.forEach(v => console.log(`  - ${v}`));
    return false;
  }

  console.log(
    `${colors.green}✓ Todas as variáveis de ambiente necessárias encontradas.${colors.reset}`
  );
  return true;
}

/**
 * Verifica se o schema do Prisma está configurado para MySQL ou PostgreSQL
 */
function checkPrismaSchema() {
  console.log(`${colors.cyan}Verificando configuração do Prisma...${colors.reset}`);

  const schemaPath = path.join(process.cwd(), 'prisma', 'schema.prisma');
  if (!fs.existsSync(schemaPath)) {
    console.log(`${colors.red}Arquivo schema.prisma não encontrado.${colors.reset}`);
    return false;
  }

  const schemaContent = fs.readFileSync(schemaPath, 'utf8');

  // Verifica se o provider é mysql ou postgresql
  if (
    !schemaContent.includes('provider = "mysql"') &&
    !schemaContent.includes('provider = "postgresql"')
  ) {
    console.log(
      `${colors.yellow}O schema do Prisma não está configurado corretamente.${colors.reset}`
    );
    console.log(
      `${colors.yellow}Você deve alterar o provider para "mysql" ou "postgresql" antes do deploy.${colors.reset}`
    );
    return false;
  }

  console.log(`${colors.green}✓ Schema do Prisma configurado corretamente.${colors.reset}`);
  return true;
}

/**
 * Executa o login na Vercel se necessário
 */
async function vercelLogin() {
  return new Promise(resolve => {
    console.log(`${colors.cyan}Verificando login na Vercel...${colors.reset}`);

    try {
      // Tenta executar um comando simples para verificar se já está logado
      execSync('vercel whoami', { stdio: 'ignore' });
      console.log(`${colors.green}✓ Já está logado na Vercel.${colors.reset}`);
      resolve(true);
    } catch (error) {
      console.log(`${colors.yellow}É necessário fazer login na Vercel.${colors.reset}`);

      rl.question(`${colors.bright}Deseja fazer login agora? (S/n) ${colors.reset}`, answer => {
        if (answer.toLowerCase() !== 'n') {
          if (runCommand('vercel login')) {
            console.log(`${colors.green}✓ Login realizado com sucesso.${colors.reset}`);
            resolve(true);
          } else {
            console.log(`${colors.red}× Falha ao fazer login.${colors.reset}`);
            resolve(false);
          }
        } else {
          console.log(`${colors.red}× Login necessário para continuar.${colors.reset}`);
          resolve(false);
        }
      });
    }
  });
}

/**
 * Função principal
 */
async function main() {
  let shouldContinue = true;

  // Verifica todas as dependências e configurações
  if (!checkVercelCLI()) shouldContinue = false;
  if (!checkPrismaSchema()) shouldContinue = false;
  if (!checkEnvVars()) shouldContinue = false;
  if (!(await vercelLogin())) shouldContinue = false;

  if (!shouldContinue) {
    console.log(`${colors.red}${colors.bright}
× Há problemas que precisam ser resolvidos antes do deploy.
  Por favor, corrija os problemas acima e tente novamente.
${colors.reset}`);
    rl.close();
    return;
  }

  console.log(`${colors.green}${colors.bright}
✓ Todas as verificações passaram! Pronto para o deploy.
${colors.reset}`);

  // Pergunta se deve continuar com o deploy
  rl.question(
    `${colors.bright}Deseja continuar com o deploy na Vercel? (S/n) ${colors.reset}`,
    answer => {
      if (answer.toLowerCase() !== 'n') {
        console.log(`${colors.blue}Iniciando deploy na Vercel...${colors.reset}`);

        // Executa o deploy
        if (runCommand('vercel --prod')) {
          console.log(`${colors.green}${colors.bright}
✓ Deploy realizado com sucesso!
${colors.reset}`);

          // Lembra sobre a migração do banco de dados
          console.log(`${colors.yellow}${colors.bright}
IMPORTANTE: Lembre-se de executar a migração do banco de dados:
  $ npx prisma migrate deploy
${colors.reset}`);
        } else {
          console.log(`${colors.red}${colors.bright}
× Ocorreu um erro durante o deploy.
  Verifique a saída acima para mais detalhes.
${colors.reset}`);
        }
      } else {
        console.log(`${colors.blue}Deploy cancelado pelo usuário.${colors.reset}`);
      }

      rl.close();
    }
  );
}

// Executa a função principal
main().catch(error => {
  console.error(`${colors.red}Erro inesperado:${colors.reset}`, error);
  rl.close();
  process.exit(1);
});
