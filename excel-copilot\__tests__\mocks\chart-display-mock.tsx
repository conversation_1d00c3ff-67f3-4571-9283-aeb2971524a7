import React from 'react';

interface ChartDataset {
  label: string;
  data: number[];
  backgroundColor: string | string[];
  borderColor?: string | string[];
  borderWidth?: number;
}

interface ChartData {
  labels: string[];
  datasets: ChartDataset[];
}

interface ChartDisplayProps {
  chartData: ChartData;
  chartType?: 'bar' | 'line' | 'pie' | 'doughnut';
  height?: number;
  width?: number;
  className?: string;
}

/**
 * Componente que exibe um gráfico com os dados fornecidos
 * Esta é uma versão simplificada para testes
 */
export const ChartDisplay: React.FC<ChartDisplayProps> = ({
  chartData,
  chartType = 'bar',
  height = 300,
  width = 600,
  className = '',
}) => {
  if (!chartData || !chartData.labels || !chartData.datasets || chartData.datasets.length === 0) {
    return (
      <div className="p-4 text-center text-gray-500">Dados insuficientes para gerar o gráfico</div>
    );
  }

  // Verificação adicional
  const dataset = chartData.datasets[0];
  if (!dataset || !dataset.data || dataset.data.length === 0) {
    return (
      <div className="p-4 text-center text-gray-500">Dados insuficientes para gerar o gráfico</div>
    );
  }

  return (
    <div
      className={`chart-container ${className}`}
      style={{ position: 'relative', height: `${height}px`, width: `${width}px` }}
      data-testid="chart-display"
    >
      <div className="chart-header mb-2">
        <span className="font-medium">{dataset.label}</span>
      </div>

      {/* Visualização simplificada do gráfico para testes */}
      <div className="chart-visualization">
        <div className="flex h-48 items-end justify-around space-x-1 border-b border-l">
          {chartData.labels.map((label, index) => (
            <div key={index} className="flex flex-col items-center">
              <div
                className="w-8"
                style={{
                  height: `${Math.min(100, ((dataset.data[index] || 0) / Math.max(...dataset.data)) * 100)}%`,
                  backgroundColor: Array.isArray(dataset.backgroundColor)
                    ? dataset.backgroundColor[index]
                    : dataset.backgroundColor,
                }}
              ></div>
              <span className="mt-1 text-xs">{label}</span>
            </div>
          ))}
        </div>
      </div>

      {/* Legenda simplificada */}
      <div className="chart-legend mt-4 flex justify-center">
        <div className="flex items-center">
          <div
            className="mr-2 h-3 w-3"
            style={{
              backgroundColor: Array.isArray(dataset.backgroundColor)
                ? dataset.backgroundColor[0]
                : dataset.backgroundColor,
            }}
          ></div>
          <span className="text-sm">{dataset.label}</span>
        </div>
      </div>
    </div>
  );
};
