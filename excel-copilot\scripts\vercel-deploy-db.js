// Script para executar a migração do Prisma na Vercel
const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔄 Iniciando migração do banco de dados para Vercel...');

try {
  // Verificar se as variáveis de ambiente necessárias estão configuradas
  if (!process.env.DB_DATABASE_URL) {
    throw new Error('Variável de ambiente DATABASE_URL não encontrada');
  }

  console.log('✅ Variáveis de ambiente verificadas');
  console.log('🔄 Gerando cliente Prisma...');

  // Gerar o cliente Prisma
  execSync('npx prisma generate', { stdio: 'inherit' });

  console.log('✅ Cliente Prisma gerado com sucesso');
  console.log('🔄 Executando migração do banco de dados...');

  // Executar a migração do banco de dados
  execSync('npx prisma migrate deploy', { stdio: 'inherit' });

  console.log('✅ Migração do banco de dados concluída com sucesso');

  // Verificar se é necessário executar o seed
  if (process.env.RUN_SEED === 'true') {
    console.log('🔄 Executando seed do banco de dados...');
    execSync('npx prisma db seed', { stdio: 'inherit' });
    console.log('✅ Seed do banco de dados concluído com sucesso');
  }

  console.log('✅ Setup do banco de dados concluído com sucesso!');
  process.exit(0);
} catch (error) {
  console.error('❌ Erro durante a migração do banco de dados:', error.message);
  process.exit(1);
}
