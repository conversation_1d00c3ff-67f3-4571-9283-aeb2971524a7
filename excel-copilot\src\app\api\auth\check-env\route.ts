import { NextRequest, NextResponse } from 'next/server';

export const dynamic = 'force-dynamic';

export async function GET(_request: NextRequest) {
  try {
    // Environment check logging

    // Verificar diretamente as variáveis de ambiente
    const envVars = {
      GOOGLE_CLIENT_ID: process.env.AUTH_GOOGLE_CLIENT_ID,
      GOOGLE_CLIENT_SECRET: process.env.AUTH_GOOGLE_CLIENT_SECRET,
      GITHUB_CLIENT_ID: process.env.AUTH_GITHUB_CLIENT_ID,
      GITHUB_CLIENT_SECRET: process.env.AUTH_GITHUB_CLIENT_SECRET,
      NEXTAUTH_SECRET: process.env.AUTH_NEXTAUTH_SECRET,
      NEXTAUTH_URL: process.env.AUTH_NEXTAUTH_URL,
      NODE_ENV: process.env.NODE_ENV,
      VERCEL: process.env.VERCEL,
      VERCEL_ENV: process.env.VERCEL_ENV,
    };

    const results = {
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV,
      isVercel: !!process.env.VERCEL,
      vercelEnv: process.env.VERCEL_ENV,

      variables: Object.entries(envVars).map(([key, value]) => ({
        name: key,
        exists: !!value,
        length: value?.length || 0,
        preview: value ? `${value.substring(0, 10)}...` : 'undefined',
        isEmpty: value === '',
        isUndefined: value === undefined,
      })),

      issues: [] as string[],
      recommendations: [] as string[],
    };

    // Verificar problemas específicos
    Object.entries(envVars).forEach(([key, value]) => {
      if (!value) {
        results.issues.push(`${key} não está definido`);
      } else if (value === '') {
        results.issues.push(`${key} está vazio`);
      } else if (value.length < 10) {
        results.issues.push(`${key} muito curto (${value.length} caracteres)`);
      }
    });

    // Verificar se estamos em produção
    if (process.env.NODE_ENV === 'production') {
      if (!process.env.VERCEL) {
        results.issues.push('Não está rodando no Vercel em produção');
      }
    }

    // Adicionar recomendações
    if (results.issues.length > 0) {
      results.recommendations.push(
        'Verificar configuração das variáveis de ambiente no Vercel Dashboard'
      );
      results.recommendations.push(
        'Confirmar que as variáveis estão definidas para o ambiente de produção'
      );
      results.recommendations.push('Fazer redeploy após configurar as variáveis');
    }

    // Environment check completed

    return NextResponse.json(results, {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
      },
    });
  } catch (error) {
    // Error handling for environment check

    return NextResponse.json(
      {
        error: 'Erro interno durante verificação de ambiente',
        message: error instanceof Error ? error.message : 'Erro desconhecido',
        timestamp: new Date().toISOString(),
      },
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
  }
}
