/**
 * Utilitários para acessar arrays e objetos com segurança de tipos
 */

/**
 * Acessa um elemento de array com índice seguro.
 * Retorna undefined se o índice estiver fora dos limites.
 */
export function safeArrayGet<T>(array: T[] | undefined | null, index: number): T | undefined {
  if (!array || index < 0 || index >= array.length) {
    return undefined;
  }
  return array[index];
}

/**
 * Acessa propriedades aninhadas de um objeto com segurança.
 */
export function safeGet<T, K extends keyof T>(obj: T | undefined | null, key: K): T[K] | undefined {
  if (!obj) {
    return undefined;
  }
  return obj[key];
}

/**
 * Acessa propriedade aninhada com tipagem mais forte para quando
 * você sabe que a propriedade existe.
 */
export function getOrDefault<T, K extends keyof T>(
  obj: T | undefined | null,
  key: K,
  defaultValue: T[K]
): T[K] {
  if (!obj) {
    return defaultValue;
  }
  return obj[key] === undefined ? defaultValue : obj[key];
}

/**
 * Verifica se uma propriedade de objeto é definida antes de usá-la.
 * Útil para objetos que podem ter propriedades undefined.
 */
export function isDefined<T, K extends keyof T>(obj: T | undefined | null, key: K): boolean {
  return !!obj && obj[key] !== undefined;
}

/**
 * Wrapper seguro para trabalhar com objetos potencialmente nulos.
 * @param obj O objeto a ser verificado
 * @param callback Uma função para ser executada se o objeto não for nulo
 * @param defaultValue Valor padrão a ser retornado se o objeto for nulo
 */
export function withSafeObject<T, R>(
  obj: T | undefined | null,
  callback: (safeObject: T) => R,
  defaultValue: R
): R {
  if (!obj) {
    return defaultValue;
  }
  return callback(obj);
}

/**
 * Acessa uma propriedade de objeto aninhada com mais de um nível de profundidade.
 * @param obj O objeto a ser acessado
 * @param path Caminho para a propriedade (no formato 'a.b.c')
 * @param defaultValue Valor padrão se a propriedade não existir
 */
export function getNestedProperty<T = any>(
  obj: Record<string, any> | undefined | null,
  path: string,
  defaultValue: T
): T {
  if (!obj || !path) {
    return defaultValue;
  }

  const properties = path.split('.');
  let current: any = obj;

  for (const prop of properties) {
    if (current === undefined || current === null || typeof current !== 'object') {
      return defaultValue;
    }
    current = current[prop];
  }

  return current === undefined ? defaultValue : current;
}
