/**
 * Utilit<PERSON>rios (Utils)
 *
 * Este módulo centraliza a exportação de todas as funções utilitárias puras.
 *
 * Diretrizes para o módulo utils:
 * - Apenas funções puras que não dependem de estado
 * - Funções determinísticas (mesma entrada sempre produz mesma saída)
 * - Sem efeitos colaterais (não modificam estado externo)
 * - Sem dependências de serviços externos ou estado global
 *
 * Diferença entre utils/ e lib/:
 * - utils/: Funções puras, sem estado, sem efeitos colaterais
 * - lib/: Classes, abstrações, serviços, gerenciadores de estado
 *
 * Estrutura:
 * - utils/functions/: Funções utilitárias gerais (strings, arrays, objetos)
 * - utils/data/: Manipulação de dados (JSON, CSV, etc)
 * - utils/http/: Utilitários para HTTP e APIs
 * - utils/excel/: Funções para manipulação de dados Excel
 * - utils/formatting/: Formatação de dados (moeda, data, números)
 */

// Funções gerais
export * from './functions';

// Manipulação de dados (sem exportar o que será exportado explicitamente abaixo)
// export * from './data/json';

// Exportamos seletivamente para evitar colisões
export {
  isValidJson,
  areJsonEqual,
  parseJsonSafely as parseJsonSafelyFromData,
  stringifyJsonSafely as stringifyJsonSafelyFromData,
} from './data/json';

// HTTP e APIs
export * from './http/api-response';

// Utilitários para acesso seguro a dados
export { safeArrayGet, withSafeObject, getNestedProperty } from './safe-access';

// Utilitários para JSON
export { parseJsonSafely, stringifyJsonSafely } from './json';

// Ferramentas de migração e rotas
export { convertToAppHandler, createAppRouteHandlers, deprecateHandler } from './route-migration';

export type { PagesHandler, AppHandler, HttpMethod } from './route-migration';

// Utilitários de tipos para Regex
export { safeRegexMatch, extractGroup, safeSplit, safeReplace, safeTest } from './regex-utils';

// Utilitários para tipagens opcionais
export {
  safeArrayAccess,
  safeObjectAccess,
  safeNestedAccess,
  toNullableString,
  isNullOrUndefined,
  withDefault,
} from './optional-helpers';

// Utilitários para resolver problemas de tipagem TypeScript
export {
  makeExactOptional,
  makePrismaCompatible,
  isDefined,
  isNonEmptyString,
  isValidNumber,
  isNonEmptyArray,
  createOptionalObject,
  safeEventHandler,
  createComponentProps,
  sanitizeFormData,
  normalizeApiData,
  safeCallback,
} from './type-helpers';

export type { SafeEventHandler, SafeComponentProps } from './type-helpers';

// Exportar utilitários de log
export * from './logger-utils';

// Exportar utilitários de erro
export * from './error-utils';

// Exportar utilitários de Excel
export * from './excel-utils';
