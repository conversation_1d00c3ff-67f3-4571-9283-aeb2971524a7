import { useState, useEffect } from 'react';

/**
 * Hook para detectar se um media query é válido
 * @param query O media query a ser verificado (ex: "(prefers-reduced-motion: reduce)")
 * @returns boolean indicando se o media query corresponde
 */
export function useMediaQuery(query: string): boolean {
  // Estado para armazenar se o media query corresponde
  const [matches, setMatches] = useState<boolean>(() => {
    // Verificar se estamos no navegador antes de usar window.matchMedia
    if (typeof window !== 'undefined') {
      return window.matchMedia(query).matches;
    }
    return false; // Valor padrão para SSR
  });

  useEffect(() => {
    // Retornar early se não estamos no navegador
    if (typeof window === 'undefined') return;

    // Criar o objeto MediaQueryList
    const mediaQueryList = window.matchMedia(query);

    // Atualizar o estado para o valor atual
    setMatches(mediaQueryList.matches);

    // Função handler para quando o valor do media query mudar
    const handler = (event: MediaQueryListEvent) => {
      setMatches(event.matches);
    };

    // Adicionar o evento listener
    // Usando método moderno se disponível, ou fallback para o modo legado
    if (mediaQueryList.addEventListener) {
      mediaQueryList.addEventListener('change', handler);
    } else {
      // Suporte para navegadores mais antigos (Safari < 14, IE, etc)
      mediaQueryList.addListener(handler);
    }

    // Cleanup: remover o evento listener quando o componente desmontar
    return () => {
      if (mediaQueryList.removeEventListener) {
        mediaQueryList.removeEventListener('change', handler);
      } else {
        // Suporte para navegadores mais antigos
        mediaQueryList.removeListener(handler);
      }
    };
  }, [query]); // Re-executar apenas se a query mudar

  return matches;
}
