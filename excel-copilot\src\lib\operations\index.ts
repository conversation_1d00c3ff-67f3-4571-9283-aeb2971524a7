// Exportar todas as funções de operações

import { ExcelOperationType } from '../../types';
import { ExcelOperation } from '../excel/types';

import { extractCellOperations, executeCellOperation } from './cellOperations';
import { extractChartOperations, executeChartOperation } from './chartOperations';
import {
  extractFilterOperations,
  executeFilterOperation,
  extractSortOperations,
  executeSortOperation,
} from './filterOperations';
import { extractFormulaOperations, executeFormulaOperation } from './formulaOperations';
import { extractTableOperations, executeTableOperation } from './tableOperations';

/**
 * Extrai todas as operações suportadas de uma resposta da IA
 * @param response Texto da resposta da IA
 * @returns Array de operações do Excel
 */
export function extractAllOperations(response: string): ExcelOperation[] {
  // Extrair todas as operações dos diversos tipos suportados
  const formulaOps = extractFormulaOperations(response);
  const chartOps = extractChartOperations(response);
  const filterOps = extractFilterOperations(response);
  const sortOps = extractSortOperations(response);
  const cellOps = extractCellOperations(response);
  const tableOps = extractTableOperations(response);

  // Combinar todas as operações em um único array
  return [...formulaOps, ...chartOps, ...filterOps, ...sortOps, ...cellOps, ...tableOps];
}

/**
 * Executa uma operação do Excel nos dados da planilha
 * @param sheetData Dados da planilha
 * @param operation Operação a ser executada
 * @returns Dados atualizados e resumo da operação
 */
export async function executeOperation(
  sheetData: any,
  operation: ExcelOperation
): Promise<{ updatedData: any; resultSummary: string }> {
  // Executar a operação específica com base no tipo
  switch (operation.type) {
    case ExcelOperationType.FORMULA:
      return executeFormulaOperation(sheetData, operation);
    case ExcelOperationType.CHART:
      return executeChartOperation(sheetData, operation);
    case ExcelOperationType.FILTER:
      return executeFilterOperation(sheetData, operation);
    case ExcelOperationType.SORT:
      return executeSortOperation(sheetData, operation);
    case ExcelOperationType.CELL_UPDATE:
      return executeCellOperation(sheetData, operation);
    case ExcelOperationType.TABLE:
      return executeTableOperation(sheetData, operation);
    default:
      throw new Error(`Tipo de operação não suportado: ${operation.type}`);
  }
}

// Reexportar todas as funções específicas para acesso direto
export {
  extractFormulaOperations,
  executeFormulaOperation,
  extractChartOperations,
  executeChartOperation,
  extractFilterOperations,
  executeFilterOperation,
  extractSortOperations,
  executeSortOperation,
  extractCellOperations,
  executeCellOperation,
  extractTableOperations,
  executeTableOperation,
};
