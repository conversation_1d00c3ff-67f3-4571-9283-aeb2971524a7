'use client';

import { motion, HTMLMotionProps } from 'framer-motion';
import React from 'react';

import { useReducedMotion } from '@/hooks/useReducedMotion';
import { getAnimationProps, AnimationVariant } from '@/lib/animations';
import { cn } from '@/lib/utils';

import { Card as CardComponent } from './card';

// Interface base para componentes de animação
export interface AnimationProps extends HTMLMotionProps<'div'> {
  delay?: number;
  duration?: number;
  className?: string;
  children: React.ReactNode;
}

/**
 * Componente que respeita preferências de movimento reduzido
 * Renderiza a versão sem animação quando o usuário prefere movimento reduzido
 */
export interface MotionSafeProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  className?: string;
}

export function MotionSafe({ children, fallback, className }: MotionSafeProps) {
  const prefersReducedMotion = useReducedMotion();

  if (prefersReducedMotion) {
    return fallback ? (
      <div className={className}>{fallback}</div>
    ) : (
      <div className={className}>{children}</div>
    );
  }

  return <>{children}</>;
}

// Reexportar Card com animação pré-configurada
export const AnimatedCard = (props: React.ComponentProps<typeof CardComponent>) => (
  <CardComponent animated {...props} />
);

/**
 * Função utilitária para obter props de animação com configurações personalizadas
 * @deprecated Considere usar getAnimationProps diretamente e aplicar sobrescritas específicas
 */
export function getCustomAnimationProps(
  variant: AnimationVariant,
  customProps: Partial<AnimationProps> = {}
): HTMLMotionProps<'div'> {
  const { delay, duration, ...rest } = customProps;
  const baseProps = getAnimationProps(variant);

  // Se não há personalizações específicas, retorne diretamente
  if (delay === undefined && duration === undefined && Object.keys(rest).length === 0) {
    return baseProps;
  }

  // Copia profunda necessária apenas se há modificações
  const result = { ...baseProps };

  // Ajustar a transição apenas se necessário
  if ((delay !== undefined || duration !== undefined) && baseProps.transition) {
    result.transition = {
      ...baseProps.transition,
      ...(delay !== undefined ? { delay } : {}),
      ...(duration !== undefined ? { duration } : {}),
    };
  }

  return { ...result, ...rest };
}

/**
 * Container que anima seus filhos em sequência
 */
export function StaggerContainer({ className, children, delay = 0, ...props }: AnimationProps) {
  return (
    <motion.div
      {...getAnimationProps('list')}
      transition={{
        staggerChildren: 0.1,
        delayChildren: delay,
      }}
      className={className}
      {...props}
    >
      {children}
    </motion.div>
  );
}

/**
 * Item para usar dentro de um StaggerContainer
 */
export function StaggerItem({ className, children, delay, duration, ...props }: AnimationProps) {
  const _animationProps = {
    ...(delay !== undefined ? { delay } : {}),
    ...(duration !== undefined ? { duration } : {}),
  };

  return (
    <motion.div
      {...getAnimationProps('listItem')}
      transition={{
        ...(getAnimationProps('listItem').transition as any),
        ...(delay !== undefined ? { delay } : {}),
        ...(duration !== undefined ? { duration } : {}),
      }}
      className={className}
      {...props}
    >
      {children}
    </motion.div>
  );
}

/**
 * Componente unificado para animações de fade/slide
 * @param type - Tipo de animação: "fade" (apenas fade) ou "slide" (fade com movimento)
 */
export interface FadeProps extends AnimationProps {
  type?: 'fade' | 'slide';
}

export function Fade({ className, children, delay, duration, type = 'fade', ...props }: FadeProps) {
  // Escolher a variante de animação com base no tipo
  const variant: AnimationVariant = type === 'fade' ? 'fadeIn' : 'slideIn';

  return (
    <motion.div
      {...getAnimationProps(variant)}
      transition={{
        ...(getAnimationProps(variant).transition as any),
        ...(delay !== undefined ? { delay } : {}),
        ...(duration !== undefined ? { duration } : {}),
      }}
      className={className}
      {...props}
    >
      {children}
    </motion.div>
  );
}

// Manter compatibilidade com código existente
export const FadeIn = (props: AnimationProps) => <Fade {...props} type="fade" />;
export const FadeInUp = (props: AnimationProps) => <Fade {...props} type="slide" />;

/**
 * Componente para transições de página
 */
export function PageTransition({ className, children, ...props }: AnimationProps) {
  return (
    <motion.div {...getAnimationProps('page')} className={cn('w-full', className)} {...props}>
      {children}
    </motion.div>
  );
}
