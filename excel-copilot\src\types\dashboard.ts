/**
 * Tipos para o sistema de dashboard do Excel Copilot
 */

export interface DashboardMetrics {
  totalWorkbooks: number;
  workbooksThisWeek: number;
  workbooksThisMonth: number;
  aiCommandsUsed: number;
  aiCommandsThisWeek: number;
  collaborators: number;
  activeCollaborations: number;
  weeklyActivity: WeeklyActivityData[];
  recentActivity: ActivityItem[];
  topTemplates: TemplateUsage[];
  usageStats: UsageStats;
}

export interface WeeklyActivityData {
  date: string;
  workbooks: number;
  commands: number;
  collaborations: number;
}

export interface ActivityItem {
  id: string;
  type: 'workbook_created' | 'workbook_edited' | 'ai_command' | 'collaboration';
  title: string;
  description: string;
  timestamp: Date;
  metadata?: Record<string, any>;
}

export interface TemplateUsage {
  id: string;
  name: string;
  usage: number;
}

export interface UsageStats {
  totalSessions: number;
  averageSessionTime: number;
  lastActiveDate: Date | null;
}

export interface MetricCardData {
  title: string;
  value: number | string;
  icon: React.ReactNode;
  description?: string;
  trend?: {
    value: number;
    isPositive: boolean;
    period: string;
  };
}

export interface QuickActionItem {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  action: () => void | Promise<void>;
  variant?: 'default' | 'secondary' | 'outline';
  disabled?: boolean;
  badge?: string;
}

export interface RecentWorkbook {
  id: string;
  name: string;
  updatedAt: Date;
  createdAt: Date;
  description?: string;
  sheets?: number;
}

export interface DashboardRealtimeEvent {
  type: 'workbook_created' | 'workbook_updated' | 'collaboration_started' | 'collaboration_ended' | 'ai_command_executed' | 'metrics_updated';
  data: any;
  timestamp: Date;
  userId: string;
}

export interface DashboardNotification {
  id: string;
  type: 'success' | 'info' | 'warning' | 'error';
  title: string;
  message: string;
  timestamp: Date;
  read: boolean;
  actionUrl?: string;
}

export interface DashboardFilter {
  period: '24h' | '7d' | '30d' | '90d';
  workbookType?: 'all' | 'created' | 'shared' | 'recent';
  activityType?: 'all' | 'workbooks' | 'ai' | 'collaboration';
}

export interface DashboardPreferences {
  showRealtimeNotifications: boolean;
  autoRefreshInterval: number; // em segundos
  defaultView: 'overview' | 'detailed' | 'compact';
  favoriteMetrics: string[];
  hiddenSections: string[];
}

export interface DashboardState {
  metrics: DashboardMetrics | null;
  isLoading: boolean;
  error: string | null;
  lastUpdated: Date | null;
  isConnected: boolean;
  notifications: DashboardNotification[];
  preferences: DashboardPreferences;
  filter: DashboardFilter;
}

// Tipos para APIs
export interface DashboardMetricsResponse {
  success: boolean;
  data: DashboardMetrics;
  cached: boolean;
  error?: string;
}

export interface DashboardMetricsRequest {
  period?: '24h' | '7d' | '30d';
  includeActivity?: boolean;
  includeTemplates?: boolean;
}

// Tipos para componentes
export interface MetricCardProps {
  title: string;
  value: number | string;
  icon: React.ReactNode;
  description?: string;
  trend?: {
    value: number;
    isPositive: boolean;
    period: string;
  };
  isLoading?: boolean;
  className?: string;
}

export interface ActivityChartProps {
  data: WeeklyActivityData[];
  isLoading: boolean;
  className?: string;
  height?: number;
  showLegend?: boolean;
}

export interface RecentActivityProps {
  activities: ActivityItem[];
  isLoading: boolean;
  className?: string;
  maxItems?: number;
  showActions?: boolean;
}

export interface QuickActionsProps {
  className?: string;
  onCreateWorkbook?: () => void;
  onImportFile?: () => void;
  recentWorkbooks?: RecentWorkbook[];
  maxActions?: number;
}

// Tipos para hooks
export interface UseDashboardMetricsReturn {
  metrics: DashboardMetrics | null;
  isLoading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  lastUpdated: Date | null;
}

export interface UseDashboardRealtimeReturn extends UseDashboardMetricsReturn {
  isConnected: boolean;
}

export interface UseDashboardNotificationsReturn {
  notifications: DashboardNotification[];
  unreadCount: number;
  markAsRead: (id: string) => void;
  markAllAsRead: () => void;
  addNotification: (notification: Omit<DashboardNotification, 'id' | 'timestamp' | 'read'>) => void;
  removeNotification: (id: string) => void;
}

// Constantes
export const DASHBOARD_REFRESH_INTERVALS = {
  FAST: 30, // 30 segundos
  NORMAL: 60, // 1 minuto
  SLOW: 300, // 5 minutos
  MANUAL: 0 // Apenas manual
} as const;

export const DASHBOARD_METRIC_TYPES = {
  WORKBOOKS: 'workbooks',
  AI_COMMANDS: 'ai_commands',
  COLLABORATIONS: 'collaborations',
  USAGE: 'usage'
} as const;

export const DASHBOARD_ACTIVITY_TYPES = {
  WORKBOOK_CREATED: 'workbook_created',
  WORKBOOK_EDITED: 'workbook_edited',
  AI_COMMAND: 'ai_command',
  COLLABORATION: 'collaboration'
} as const;

export const DASHBOARD_CHART_COLORS = {
  PRIMARY: '#3b82f6',
  SECONDARY: '#8b5cf6',
  ACCENT: '#10b981',
  WARNING: '#f59e0b',
  DANGER: '#ef4444',
  SUCCESS: '#22c55e',
  INFO: '#06b6d4'
} as const;

// Utilitários de tipo
export type DashboardMetricType = keyof typeof DASHBOARD_METRIC_TYPES;
export type DashboardActivityType = keyof typeof DASHBOARD_ACTIVITY_TYPES;
export type DashboardRefreshInterval = typeof DASHBOARD_REFRESH_INTERVALS[keyof typeof DASHBOARD_REFRESH_INTERVALS];

// Validadores de tipo
export function isDashboardMetrics(obj: any): obj is DashboardMetrics {
  return obj && 
    typeof obj.totalWorkbooks === 'number' &&
    typeof obj.aiCommandsUsed === 'number' &&
    Array.isArray(obj.weeklyActivity) &&
    Array.isArray(obj.recentActivity);
}

export function isActivityItem(obj: any): obj is ActivityItem {
  return obj &&
    typeof obj.id === 'string' &&
    typeof obj.type === 'string' &&
    typeof obj.title === 'string' &&
    typeof obj.description === 'string' &&
    obj.timestamp instanceof Date;
}

export function isWeeklyActivityData(obj: any): obj is WeeklyActivityData {
  return obj &&
    typeof obj.date === 'string' &&
    typeof obj.workbooks === 'number' &&
    typeof obj.commands === 'number' &&
    typeof obj.collaborations === 'number';
}
