# 🔧 Configuração de Ambiente Unificada - Excel Copilot v2.0

## 📋 Visão Geral

O Excel Copilot v2.0 introduz um sistema completamente reformulado de gerenciamento de configurações de ambiente, eliminando conflitos e inconsistências que existiam no sistema anterior.

### ✅ Problemas Resolvidos

- **Múltiplas flags conflitantes** para controle de IA
- **Validação inconsistente** entre ambientes
- **Nomenclatura não padronizada** de variáveis
- **Falta de hierarquia clara** de precedência
- **Documentação fragmentada** em múltiplos arquivos

### 🎯 Benefícios do Novo Sistema

- ✅ **Uma única fonte da verdade** para configurações
- ✅ **Validação rigorosa** com mensagens específicas
- ✅ **Nomenclatura padronizada** com prefixos consistentes
- ✅ **Hierarquia clara** de precedência entre variáveis
- ✅ **Compatibilidade mantida** com sistema antigo
- ✅ **Diagnóstico automático** de problemas
- ✅ **Correções automáticas** para problemas comuns

## 🚀 Início Rápido

### 1. Diagnóstico da Configuração Atual

```bash
# Verificar configuração atual
npm run env:diagnose

# Aplicar correções automáticas
npm run env:fix

# Gerar relatório detalhado
npm run env:report
```

### 2. Configuração para Desenvolvimento

```bash
# Copiar template
cp .env.example .env.local

# Editar configurações
nano .env.local

# Validar configuração
npm run env:diagnose
```

### 3. Configuração para Produção

```bash
# No painel do Vercel, configure as variáveis obrigatórias:
NEXTAUTH_SECRET=sua-chave-secreta-forte
NEXTAUTH_URL=https://excel-copilot-eight.vercel.app
DATABASE_URL=postgresql://...
GOOGLE_CLIENT_ID=...
GOOGLE_CLIENT_SECRET=...
```

## 📚 Convenções de Nomenclatura

### Prefixos Padronizados

| Prefixo      | Categoria               | Exemplo                 |
| ------------ | ----------------------- | ----------------------- |
| `AUTH_*`     | Autenticação            | `AUTH_NEXTAUTH_SECRET`  |
| `DB_*`       | Banco de dados          | `DB_DATABASE_URL`       |
| `AI_*`       | Inteligência artificial | `AI_ENABLED`            |
| `MCP_*`      | Model Context Protocol  | `MCP_VERCEL_TOKEN`      |
| `STRIPE_*`   | Pagamentos              | `STRIPE_SECRET_KEY`     |
| `SECURITY_*` | Segurança               | `SECURITY_CORS_ORIGINS` |
| `DEV_*`      | Desenvolvimento         | `DEV_LOG_LEVEL`         |

### Migração de Variáveis Legadas

| Variável Antiga      | Nova Variável        | Status           |
| -------------------- | -------------------- | ---------------- |
| `USE_MOCK_AI`        | `AI_USE_MOCK`        | ⚠️ Descontinuada |
| `VERTEX_AI_ENABLED`  | `AI_ENABLED`         | ⚠️ Descontinuada |
| `FORCE_GOOGLE_MOCKS` | `AI_USE_MOCK`        | ⚠️ Descontinuada |
| `DISABLE_STRIPE`     | `STRIPE_ENABLED`     | ⚠️ Descontinuada |
| `VERCEL_API_TOKEN`   | `MCP_VERCEL_TOKEN`   | ⚠️ Descontinuada |
| `LINEAR_API_KEY`     | `MCP_LINEAR_API_KEY` | ⚠️ Descontinuada |

## 🔧 Configuração de IA Unificada

### Hierarquia de Precedência (IA)

O novo sistema resolve conflitos seguindo esta ordem de precedência:

1. **`NEXT_PUBLIC_DISABLE_VERTEX_AI=true`** → IA completamente desabilitada
2. **`FORCE_GOOGLE_MOCKS=true`** → Sempre usar mocks
3. **`USE_MOCK_AI=true`** → Usar mocks
4. **`VERTEX_AI_ENABLED=false`** → IA desabilitada
5. **Ausência de `VERTEX_AI_PROJECT_ID`** → Usar mocks automaticamente

### Configuração Recomendada

#### Desenvolvimento

```bash
AI_ENABLED=false
AI_USE_MOCK=true
```

#### Produção

```bash
AI_ENABLED=true
AI_USE_MOCK=false
VERTEX_AI_PROJECT_ID=excel-copilot
VERTEX_AI_LOCATION=us-central1
VERTEX_AI_MODEL_NAME=gemini-2.0-flash-001
```

## 🛠️ Utilitário de Diagnóstico

### Comandos Disponíveis

```bash
# Diagnóstico completo
npm run env:diagnose

# Aplicar correções automáticas
npm run env:fix

# Gerar relatório em arquivo
npm run env:report

# Validação programática
npm run env:validate
```

### Exemplo de Saída

```
🔍 DIAGNÓSTICO DE AMBIENTE - EXCEL COPILOT
============================================================

✅ Status Geral: CONFIGURAÇÃO OK
🌍 Ambiente: development
📊 Variáveis carregadas: 45

📋 STATUS DOS SERVIÇOS:
------------------------------
🔐 Autenticação: ENABLED
🤖 Inteligência Artificial: MOCK
🗄️ Banco de Dados: ENABLED
💳 Stripe: ENABLED
🔌 Vercel MCP: ENABLED
🔌 Linear MCP: DISABLED
🔌 GitHub MCP: DISABLED

⚠️ AVISOS (2):
  1. Nenhum provider OAuth configurado - usando modo de desenvolvimento
  2. AI habilitada sem VERTEX_AI_PROJECT_ID - usando modo mock

💡 SUGESTÕES DE MELHORIA (3):
  1. Configurar GOOGLE_CLIENT_ID para autenticação OAuth
  2. Configurar VERTEX_AI_PROJECT_ID para IA em produção
  3. Configurar MCP_LINEAR_API_KEY para integração Linear
```

## 🔒 Configurações de Segurança

### Variáveis Obrigatórias em Produção

```bash
# Autenticação (CRÍTICAS)
NEXTAUTH_SECRET=chave-forte-32-caracteres-minimo
NEXTAUTH_URL=https://excel-copilot-eight.vercel.app
DATABASE_URL=postgresql://...

# OAuth (RECOMENDADAS)
GOOGLE_CLIENT_ID=...
GOOGLE_CLIENT_SECRET=...

# Segurança (OPCIONAIS)
SECURITY_CORS_ORIGINS=https://excel-copilot-eight.vercel.app
SECURITY_CSRF_SECRET=...
SECURITY_RATE_LIMIT_ENABLED=true
```

### Validações de Segurança

O sistema automaticamente verifica:

- ✅ Força do `NEXTAUTH_SECRET` (mínimo 32 caracteres)
- ✅ URLs de produção não podem ser `localhost`
- ✅ Pelo menos um provider OAuth em produção
- ✅ Configuração de CORS em produção
- ✅ Credenciais não podem estar vazias

## 📁 Estrutura de Arquivos

```
excel-copilot/
├── .env.example                    # Template principal (v2.0)
├── .env.local                      # Desenvolvimento (criar)
├── .env.production                 # Produção (criar)
├── src/config/
│   └── unified-environment.ts      # Sistema unificado
├── scripts/
│   └── diagnose-environment.js     # Utilitário de diagnóstico
└── docs/
    └── environment-configuration.md # Esta documentação
```

## 🔄 Migração do Sistema Antigo

### Passo 1: Backup da Configuração Atual

```bash
# Fazer backup dos arquivos atuais
cp .env.local .env.local.backup
cp .env.production .env.production.backup
```

### Passo 2: Executar Diagnóstico

```bash
# Verificar problemas na configuração atual
npm run env:diagnose
```

### Passo 3: Aplicar Correções

```bash
# Aplicar correções automáticas
npm run env:fix
```

### Passo 4: Validar Nova Configuração

```bash
# Verificar se tudo está funcionando
npm run env:diagnose
npm run dev
```

## 🚨 Solução de Problemas

### Problemas Comuns

#### 1. Conflitos de IA

**Problema**: Múltiplas flags de IA configuradas

```bash
❌ Múltiplas flags de mock configuradas: USE_MOCK_AI, FORCE_GOOGLE_MOCKS
```

**Solução**: Use apenas uma flag

```bash
# Remover flags antigas
# USE_MOCK_AI=true
# FORCE_GOOGLE_MOCKS=true

# Usar nova flag unificada
AI_USE_MOCK=true
```

#### 2. Validação Falha em Produção

**Problema**: Variáveis obrigatórias ausentes

```bash
❌ NEXTAUTH_SECRET é obrigatória em produção
```

**Solução**: Configure todas as variáveis obrigatórias

```bash
NEXTAUTH_SECRET=sua-chave-secreta-forte-aqui
NEXTAUTH_URL=https://excel-copilot-eight.vercel.app
DATABASE_URL=postgresql://...
```

#### 3. OAuth Não Funciona

**Problema**: Callbacks OAuth falham

```bash
❌ NEXTAUTH_URL não pode ser localhost em produção
```

**Solução**: Use URL de produção correta

```bash
NEXTAUTH_URL=https://excel-copilot-eight.vercel.app
```

### Comandos de Emergência

```bash
# Resetar configuração para padrões
cp .env.example .env.local
npm run env:fix

# Verificar conectividade com serviços
npm run check:external-services

# Logs detalhados de diagnóstico
DEBUG=* npm run env:diagnose
```

## 📞 Suporte

### Recursos de Ajuda

- 📖 **Documentação**: `README.md`
- 🔧 **Diagnóstico**: `npm run env:diagnose`
- 🐛 **Issues**: [GitHub Issues](https://github.com/seu-usuario/excel-copilot/issues)
- 💬 **Discussões**: [GitHub Discussions](https://github.com/seu-usuario/excel-copilot/discussions)

### Informações para Suporte

Ao reportar problemas, inclua:

1. **Saída do diagnóstico**: `npm run env:report`
2. **Ambiente**: desenvolvimento/produção
3. **Versão**: `npm run env:validate`
4. **Logs de erro**: console do navegador/terminal

---

**Versão**: 2.0.0  
**Última atualização**: Janeiro 2025  
**Autor**: Excel Copilot Team
