#!/usr/bin/env node

/**
 * Script para configurar variáveis de ambiente na Vercel para produção
 * Garante que as integrações MCP funcionem corretamente
 */

// eslint-disable-next-line @typescript-eslint/no-require-imports
const fs = require('fs');
// eslint-disable-next-line @typescript-eslint/no-require-imports, @typescript-eslint/no-unused-vars
const path = require('path');

// Cores para output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m',
};

// eslint-disable-next-line no-console
console.log(`${colors.bold}🚀 CONFIGURAÇÃO VERCEL PARA PRODUÇÃO${colors.reset}\n`);

// Variáveis de ambiente críticas para produção na Vercel
const VERCEL_ENV_VARS = {
  // ======================================
  // CONFIGURAÇÕES BÁSICAS
  // ======================================
  NODE_ENV: 'production',
  NEXT_PUBLIC_FORCE_PRODUCTION: 'true',

  // ======================================
  // DESATIVAR MOCKS (CRÍTICO)
  // ======================================
  FORCE_GOOGLE_MOCKS: 'false',
  USE_MOCK_AI: 'false',
  NEXT_PUBLIC_DISABLE_VERTEX_AI: 'false',
  VERTEX_AI_ENABLED: 'true',

  // ======================================
  // NEXTAUTH (AUTENTICAÇÃO)
  // ======================================
  NEXTAUTH_SECRET: 'dW5jL4x7Q2tPaDZkVzFqc3pVTEhuMDdYZ0tLbldnRkxRV3hNeUJTRHJSWQ',
  NEXTAUTH_URL: 'https://excel-copilot-eight.vercel.app',

  // ======================================
  // OAUTH PROVIDERS
  // ======================================
  GOOGLE_CLIENT_ID: '217111050148-1gocm6a0sa9jcrk8s08dubqn8n2001lv.apps.googleusercontent.com',
  GOOGLE_CLIENT_SECRET: '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',
  GITHUB_CLIENT_ID: '********************',
  GITHUB_CLIENT_SECRET: '7c80b91c934dc9845a8ce7a362581d8ab45f2c3e',

  // ======================================
  // VERTEX AI
  // ======================================
  VERTEX_AI_PROJECT_ID: 'excel-copilot',
  VERTEX_AI_LOCATION: 'us-central1',
  VERTEX_AI_MODEL_NAME: 'gemini-2.0-flash-001',

  // ======================================
  // SUPABASE (BANCO DE DADOS)
  // ======================================
  DATABASE_URL:
    'postgresql://postgres.eliuoignzzxnjkcmmtml:<EMAIL>:6543/postgres?pgbouncer=true&connection_limit=1&pool_timeout=20',
  DIRECT_URL:
    'postgresql://postgres.eliuoignzzxnjkcmmtml:<EMAIL>:5432/postgres',
  SUPABASE_URL: 'https://eliuoignzzxnjkcmmtml.supabase.co',
  SUPABASE_ANON_KEY:
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVsaXVvaWduenp4bmprY21tdG1sIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY1NDU2MTQsImV4cCI6MjA2MjEyMTYxNH0.rMyGA-hjWQNxJDdLSi3gYtSi8Gg2TeDxAs8f2gx8Zdk',
  SUPABASE_SERVICE_ROLE_KEY:
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVsaXVvaWduenp4bmprY21tdG1sIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NjU0NTYxNCwiZXhwIjoyMDYyMTIxNjE0fQ.hHguPBu7OV6CJBSmwe3r7JwG1Ob__NWt-dWAnRsofP8',
  NEXT_PUBLIC_SUPABASE_URL: 'https://eliuoignzzxnjkcmmtml.supabase.co',
  NEXT_PUBLIC_SUPABASE_ANON_KEY:
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVsaXVvaWduenp4bmprY21tdG1sIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY1NDU2MTQsImV4cCI6MjA2MjEyMTYxNH0.rMyGA-hjWQNxJDdLSi3gYtSi8Gg2TeDxAs8f2gx8Zdk',

  // ======================================
  // STRIPE (PAGAMENTOS)
  // ======================================
  STRIPE_SECRET_KEY:
    '***********************************************************************************************************',
  STRIPE_WEBHOOK_SECRET: 'whsec_U2oN7gw62XH6DKOsGNbuqtbCAYLMDx8U',
  NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY:
    'pk_live_51RGJ6nRrKLXtzZkMtpujgPAZR4MmRmQQrImSNrq6vdCLe6gfWulXfJDaDl1K2u3DKeKUegsXvzceFVi8xwnwroic00ER63lsVr',
  NEXT_PUBLIC_STRIPE_PRICE_MONTHLY: 'price_1RJeZYRrKLXtzZkMf1SS2CRR',
  NEXT_PUBLIC_STRIPE_PRICE_ANNUAL: 'price_1RJecORrKLXtzZkMy1RSRpMV',

  // ======================================
  // INTEGRAÇÕES MCP
  // ======================================
  // Vercel MCP
  VERCEL_API_TOKEN: '************************',
  VERCEL_PROJECT_ID: 'prj_IQemY1bXbDdiiQmHDfRAYLUqMZIg',
  VERCEL_TEAM_ID: 'team_BLCIn3CF09teqBeBn8u0fLqp',

  // Linear MCP - Usa integração MCP (não precisa de token direto)
  LINEAR_API_KEY: 'mcp_integration_enabled',

  // GitHub MCP - Usa OAuth (não precisa de token pessoal)
  GITHUB_TOKEN: 'OAUTH_MODE',
  GITHUB_OWNER: 'cauaprjct',
  GITHUB_REPO: 'excel-copilot',

  // ======================================
  // CONFIGURAÇÕES ADICIONAIS
  // ======================================
  APP_NAME: 'Excel Copilot',
  APP_VERSION: '1.0.0',
  APP_URL: 'https://excel-copilot-eight.vercel.app',
  NEXT_PUBLIC_APP_URL: 'https://excel-copilot-eight.vercel.app',

  // Segurança
  CSRF_SECRET: 'fZMaY9UO4bXdsFi7lp3gH6kRwEcV2qJ5',
  SKIP_AUTH_PROVIDERS: 'false',
  NEXT_PUBLIC_DISABLE_CSRF: 'false',

  // Cache e Performance
  AI_CACHE_SIZE: '200',
  AI_CACHE_TTL: '7200',
  EXCEL_CACHE_SIZE: '100',
  EXCEL_CACHE_TTL: '1800',
  CACHE_DEFAULT_TTL: '3600',
};

// Gerar comandos para configurar na Vercel
function generateVercelCommands() {
  // eslint-disable-next-line no-console
  console.log(`${colors.blue}📋 Comandos para configurar na Vercel:${colors.reset}\n`);

  // eslint-disable-next-line no-console
  console.log(`${colors.yellow}# Execute estes comandos no terminal:${colors.reset}`);
  // eslint-disable-next-line no-console
  console.log(`${colors.yellow}# (Certifique-se de estar logado na Vercel CLI)${colors.reset}\n`);

  Object.entries(VERCEL_ENV_VARS).forEach(([key, value]) => {
    // Escapar aspas duplas no valor
    const escapedValue = value.replace(/"/g, '\\"');
    // eslint-disable-next-line no-console
    console.log(`vercel env add ${key} production`);
    // eslint-disable-next-line no-console
    console.log(`# Valor: ${escapedValue}\n`);
  });
}

// Gerar arquivo .env.production para referência
function generateProductionEnvFile() {
  // eslint-disable-next-line no-console
  console.log(`${colors.blue}📝 Gerando arquivo .env.production para referência...${colors.reset}`);

  let envContent = '# Variáveis de ambiente para produção na Vercel\n';
  envContent += '# Este arquivo é apenas para referência - configure na Vercel CLI\n\n';

  Object.entries(VERCEL_ENV_VARS).forEach(([key, value]) => {
    envContent += `${key}="${value}"\n`;
  });

  fs.writeFileSync('.env.production', envContent);
  // eslint-disable-next-line no-console
  console.log(`${colors.green}✅ Arquivo .env.production criado${colors.reset}`);
}

// Gerar script de configuração automática
function generateConfigScript() {
  // eslint-disable-next-line no-console
  console.log(`${colors.blue}📝 Gerando script de configuração automática...${colors.reset}`);

  let script = '#!/bin/bash\n\n';
  script += '# Script para configurar variáveis de ambiente na Vercel\n';
  script += '# Execute: chmod +x configure-vercel.sh && ./configure-vercel.sh\n\n';
  script += 'echo "🚀 Configurando variáveis de ambiente na Vercel..."\n\n';

  Object.entries(VERCEL_ENV_VARS).forEach(([key, value]) => {
    const escapedValue = value.replace(/"/g, '\\"').replace(/'/g, "\\'");
    script += `echo "Configurando ${key}..."\n`;
    script += `echo "${escapedValue}" | vercel env add ${key} production\n\n`;
  });

  script += 'echo "✅ Configuração concluída!"\n';
  script += 'echo "Execute: vercel --prod para fazer deploy"\n';

  fs.writeFileSync('configure-vercel.sh', script);
  // eslint-disable-next-line no-console
  console.log(`${colors.green}✅ Script configure-vercel.sh criado${colors.reset}`);
}

// Verificar configuração atual
function checkCurrentConfig() {
  // eslint-disable-next-line no-console
  console.log(`${colors.blue}🔍 Verificando configuração atual...${colors.reset}\n`);

  const envFile = '.env.local';
  if (!fs.existsSync(envFile)) {
    // eslint-disable-next-line no-console
    console.log(`${colors.red}❌ Arquivo .env.local não encontrado${colors.reset}`);
    return;
  }

  const envContent = fs.readFileSync(envFile, 'utf8');

  const criticalVars = [
    'LINEAR_API_KEY',
    'GITHUB_TOKEN',
    'FORCE_GOOGLE_MOCKS',
    'USE_MOCK_AI',
    'VERTEX_AI_ENABLED',
  ];

  criticalVars.forEach(varName => {
    const regex = new RegExp(`${varName}="([^"]*)"`, 'i');
    const match = envContent.match(regex);

    if (match) {
      const value = match[1];
      const expectedValue = VERCEL_ENV_VARS[varName];

      if (value === expectedValue) {
        // eslint-disable-next-line no-console
        console.log(`${colors.green}✅ ${varName}: ${value}${colors.reset}`);
      } else {
        // eslint-disable-next-line no-console
        console.log(
          `${colors.yellow}⚠️ ${varName}: ${value} (esperado: ${expectedValue})${colors.reset}`
        );
      }
    } else {
      // eslint-disable-next-line no-console
      console.log(`${colors.red}❌ ${varName}: não encontrado${colors.reset}`);
    }
  });
}

// Função principal
function main() {
  // eslint-disable-next-line no-console
  console.log(
    `${colors.bold}🎯 OBJETIVO: Configurar Excel Copilot para funcionar na Vercel${colors.reset}\n`
  );

  // eslint-disable-next-line no-console
  console.log(`${colors.blue}📋 Configurações aplicadas:${colors.reset}`);
  // eslint-disable-next-line no-console
  console.log(`✅ Linear MCP: Usa integração MCP (mcp_integration_enabled)`);
  // eslint-disable-next-line no-console
  console.log(`✅ GitHub MCP: Usa OAuth via NextAuth (OAUTH_MODE)`);
  // eslint-disable-next-line no-console
  console.log(`✅ Mocks: Completamente desativados`);
  // eslint-disable-next-line no-console
  console.log(`✅ IA: Vertex AI habilitado para produção`);
  // eslint-disable-next-line no-console
  console.log(`✅ Pagamentos: Stripe LIVE configurado\n`);

  checkCurrentConfig();
  generateProductionEnvFile();
  generateConfigScript();

  // eslint-disable-next-line no-console
  console.log(`\n${colors.bold}📋 PRÓXIMOS PASSOS:${colors.reset}`);
  // eslint-disable-next-line no-console
  console.log(`1. ${colors.yellow}Execute: chmod +x configure-vercel.sh${colors.reset}`);
  // eslint-disable-next-line no-console
  console.log(`2. ${colors.yellow}Execute: ./configure-vercel.sh${colors.reset}`);
  // eslint-disable-next-line no-console
  console.log(`3. ${colors.yellow}Execute: vercel --prod${colors.reset}`);
  // eslint-disable-next-line no-console
  console.log(`4. ${colors.green}Teste: https://excel-copilot-eight.vercel.app${colors.reset}`);

  // eslint-disable-next-line no-console
  console.log(`\n${colors.green}🎉 Configuração preparada para produção na Vercel!${colors.reset}`);
}

// Executar se chamado diretamente
if (require.main === module) {
  main();
}

module.exports = { VERCEL_ENV_VARS, generateVercelCommands, generateProductionEnvFile };
