/**
 * Tipos para colaboradores ativos
 */
export interface ActiveCollaborator {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  position?: { row: number; col: number };
  lastActive: Date;
  status: 'active' | 'idle' | 'offline';
  socket: string; // ID do socket
}

/**
 * Store para manter dados de colaboração em memória
 * Este é um singleton que gerencia o estado dos colaboradores ativos
 */
export class CollaborationStore {
  private static instance: CollaborationStore;
  private activeCollaborators: Map<string, ActiveCollaborator[]> = new Map();
  private userSockets: Map<string, string> = new Map(); // userId -> socketId
  private socketUsers: Map<string, string> = new Map(); // socketId -> userId
  private workbookSockets: Map<string, Set<string>> = new Map(); // workbookId -> Set<socketId>

  private constructor() {}

  /**
   * Obtém a instância singleton da store
   */
  public static getInstance(): CollaborationStore {
    if (!CollaborationStore.instance) {
      CollaborationStore.instance = new CollaborationStore();
    }
    return CollaborationStore.instance;
  }

  /**
   * Adiciona um colaborador ativo
   * @param workbookId ID da planilha
   * @param collaborator Dados do colaborador
   */
  public addCollaborator(workbookId: string, collaborator: ActiveCollaborator): void {
    // Obter lista atual ou criar nova
    const collaborators = this.activeCollaborators.get(workbookId) || [];

    // Verificar se já existe
    const existingIndex = collaborators.findIndex(c => c.id === collaborator.id);
    if (existingIndex >= 0) {
      // Atualizar existente
      collaborators[existingIndex] = collaborator;
    } else {
      // Adicionar novo
      collaborators.push(collaborator);
    }

    // Atualizar maps
    this.activeCollaborators.set(workbookId, collaborators);
    this.userSockets.set(collaborator.id, collaborator.socket);
    this.socketUsers.set(collaborator.socket, collaborator.id);

    // Adicionar à lista de sockets da planilha
    const workbookSocketsSet = this.workbookSockets.get(workbookId) || new Set();
    workbookSocketsSet.add(collaborator.socket);
    this.workbookSockets.set(workbookId, workbookSocketsSet);
  }

  /**
   * Remove um colaborador
   * @param socketId ID do socket do colaborador
   * @returns Lista de IDs de planilhas afetadas
   */
  public removeCollaborator(socketId: string): string[] {
    const userId = this.socketUsers.get(socketId);
    if (!userId) return [];

    const affectedWorkbooks: string[] = [];

    // Remover de todas as planilhas
    this.activeCollaborators.forEach((collaborators, workbookId) => {
      const index = collaborators.findIndex(c => c.id === userId);
      if (index >= 0) {
        collaborators.splice(index, 1);
        this.activeCollaborators.set(workbookId, collaborators);
        affectedWorkbooks.push(workbookId);

        // Remover da lista de sockets da planilha
        const workbookSocketsSet = this.workbookSockets.get(workbookId);
        if (workbookSocketsSet) {
          workbookSocketsSet.delete(socketId);
          if (workbookSocketsSet.size === 0) {
            this.workbookSockets.delete(workbookId);
          } else {
            this.workbookSockets.set(workbookId, workbookSocketsSet);
          }
        }
      }
    });

    // Remover dos maps
    this.userSockets.delete(userId);
    this.socketUsers.delete(socketId);

    return affectedWorkbooks;
  }

  /**
   * Obtém colaboradores de uma planilha
   * @param workbookId ID da planilha
   * @returns Lista de colaboradores ativos
   */
  public getCollaborators(workbookId: string): ActiveCollaborator[] {
    return this.activeCollaborators.get(workbookId) || [];
  }

  /**
   * Atualiza posição do cursor
   * @param socketId ID do socket
   * @param position Nova posição
   * @returns Dados atualizados ou null se usuário não encontrado
   */
  public updateCollaboratorPosition(
    socketId: string,
    position: { row: number; col: number }
  ): { userId: string; workbookIds: string[] } | null {
    const userId = this.socketUsers.get(socketId);
    if (!userId) return null;

    const updatedWorkbooks: string[] = [];

    // Atualizar em todas as planilhas
    this.activeCollaborators.forEach((collaborators, workbookId) => {
      const collaborator = collaborators.find(c => c.id === userId);
      if (collaborator) {
        collaborator.position = position;
        collaborator.lastActive = new Date();
        collaborator.status = 'active';
        updatedWorkbooks.push(workbookId);
      }
    });

    return { userId, workbookIds: updatedWorkbooks };
  }

  /**
   * Atualiza status de um colaborador
   * @param userId ID do usuário
   * @param status Novo status
   * @returns Lista de IDs de planilhas afetadas
   */
  public updateCollaboratorStatus(userId: string, status: 'active' | 'idle' | 'offline'): string[] {
    const updatedWorkbooks: string[] = [];

    this.activeCollaborators.forEach((collaborators, workbookId) => {
      const collaborator = collaborators.find(c => c.id === userId);
      if (collaborator) {
        collaborator.status = status;
        collaborator.lastActive = new Date();
        updatedWorkbooks.push(workbookId);
      }
    });

    return updatedWorkbooks;
  }

  /**
   * Obtém sockets de uma planilha
   * @param workbookId ID da planilha
   * @returns Lista de IDs de socket
   */
  public getWorkbookSockets(workbookId: string): string[] {
    const sockets = this.workbookSockets.get(workbookId);
    return sockets ? Array.from(sockets) : [];
  }

  /**
   * Obtém ID do usuário a partir do ID do socket
   * @param socketId ID do socket
   * @returns ID do usuário
   */
  public getUserIdFromSocket(socketId: string): string | undefined {
    return this.socketUsers.get(socketId);
  }

  /**
   * Obtém ID do socket a partir do ID do usuário
   * @param userId ID do usuário
   * @returns ID do socket
   */
  public getSocketFromUserId(userId: string): string | undefined {
    return this.userSockets.get(userId);
  }
}
