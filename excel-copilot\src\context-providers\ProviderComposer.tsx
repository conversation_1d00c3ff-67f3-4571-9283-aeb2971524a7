'use client';

import React, { ReactNode } from 'react';

type ProviderComponent = React.ComponentType<{ children: ReactNode }>;

interface ProviderWithProps {
  provider: ProviderComponent;
  props?: Record<string, unknown>;
}

interface ProviderComposerProps {
  providers: ProviderWithProps[];
  children: ReactNode;
}

/**
 * Componente que facilita a composição de múltiplos providers
 * sem aninhar explicitamente na árvore de componentes
 */
export const ProviderComposer: React.FC<ProviderComposerProps> = ({ providers, children }) => {
  return providers.reduceRight((accumulator, { provider: Provider, props = {} }) => {
    return <Provider {...props}>{accumulator}</Provider>;
  }, children);
};

/**
 * Componente de Provider Único
 */
interface SingleProviderProps {
  provider: ProviderComponent;
  props?: Record<string, unknown>;
  children: ReactNode;
}

export const SingleProvider: React.FC<SingleProviderProps> = ({
  provider: Provider,
  props = {},
  children,
}) => {
  return <Provider {...props}>{children}</Provider>;
};
