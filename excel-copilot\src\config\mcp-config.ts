/**
 * 🔌 CONFIGURAÇÃO UNIFICADA MCP - EXCEL COPILOT
 *
 * Este arquivo centraliza TODAS as configurações de Model Context Protocol (MCP)
 * da aplicação, eliminando duplicações e inconsistências.
 *
 * PRINCÍPIOS:
 * 1. Uma única fonte da verdade para MCPs
 * 2. Interface consistente para todas as integrações
 * 3. Validação rigorosa de credenciais
 * 4. Fallbacks seguros e previsíveis
 * 5. Tipos TypeScript para todas as configurações
 *
 * <AUTHOR> Copilot Team
 * @version 2.0.0
 */

import { z } from 'zod';

// ============================================================================
// TIPOS E INTERFACES
// ============================================================================

/**
 * Status de uma integração MCP
 */
export type MCPStatus = 'enabled' | 'disabled' | 'error' | 'pending';

/**
 * Tipos de MCP suportados
 */
export type MCPType = 'vercel' | 'linear' | 'github' | 'supabase' | 'stripe';

/**
 * Interface base para configuração MCP
 */
export interface MCPConfig {
  enabled: boolean;
  status: MCPStatus;
  credentials: Record<string, string>;
  endpoints: Record<string, string>;
  timeouts: Record<string, number>;
  lastCheck?: Date;
  errorMessage?: string;
}

/**
 * Interface para resultado de health check
 */
export interface MCPHealthCheck {
  healthy: boolean;
  responseTime: number;
  lastCheck: Date;
  error?: string;
}

/**
 * Interface para configuração completa de MCPs
 */
export interface MCPConfiguration {
  vercel: MCPConfig;
  linear: MCPConfig;
  github: MCPConfig;
  supabase: MCPConfig;
  stripe: MCPConfig;
}

// ============================================================================
// SCHEMAS DE VALIDAÇÃO
// ============================================================================

/**
 * Schema para validação de tokens/credenciais
 */
const credentialSchema = z.string().min(1, 'Credencial não pode estar vazia');

/**
 * Schema para validação de URLs
 */
const urlSchema = z.string().url('Deve ser uma URL válida');

/**
 * Schema para configuração do Vercel MCP
 */
const _vercelConfigSchema = z.object({
  token: credentialSchema.optional(),
  projectId: z.string().optional(),
  teamId: z.string().optional(),
});

/**
 * Schema para configuração do Linear MCP
 */
const _linearConfigSchema = z.object({
  apiKey: credentialSchema.optional(),
});

/**
 * Schema para configuração do GitHub MCP
 */
const _githubConfigSchema = z.object({
  token: credentialSchema.optional(),
  owner: z.string().optional(),
  repo: z.string().optional(),
});

/**
 * Schema para configuração do Supabase MCP
 */
const _supabaseConfigSchema = z.object({
  url: urlSchema.optional(),
  serviceRoleKey: credentialSchema.optional(),
  anonKey: credentialSchema.optional(),
});

/**
 * Schema para configuração do Stripe MCP
 */
const _stripeConfigSchema = z.object({
  secretKey: credentialSchema.optional(),
  webhookSecret: credentialSchema.optional(),
  publishableKey: credentialSchema.optional(),
});

// ============================================================================
// UTILITÁRIOS
// ============================================================================

/**
 * Obtém variável de ambiente com fallback
 */
function getEnvVar(key: string, defaultValue: string = ''): string {
  if (typeof process !== 'undefined' && process.env) {
    return process.env[key] || defaultValue;
  }
  return defaultValue;
}

/**
 * Converte string para boolean
 */
function _parseBoolean(value: string | undefined, defaultValue: boolean = false): boolean {
  if (!value) return defaultValue;
  return value.toLowerCase() === 'true';
}

/**
 * Verifica se estamos no servidor
 */
function _isServerSide(): boolean {
  return typeof window === 'undefined';
}

/**
 * Mascara credenciais para logs
 */
function _maskCredential(credential: string): string {
  if (!credential || credential.length < 8) return '***';
  return credential.substring(0, 4) + '***' + credential.substring(credential.length - 4);
}

// ============================================================================
// CONFIGURAÇÕES ESPECÍFICAS POR MCP
// ============================================================================

/**
 * Configuração do Vercel MCP
 */
function createVercelConfig(): MCPConfig {
  const token = getEnvVar('MCP_VERCEL_TOKEN') || getEnvVar('MCP_VERCEL_TOKEN');
  const projectId = getEnvVar('MCP_VERCEL_PROJECT_ID') || getEnvVar('MCP_VERCEL_PROJECT_ID');
  const teamId = getEnvVar('MCP_VERCEL_TEAM_ID') || getEnvVar('MCP_VERCEL_TEAM_ID');

  const enabled = Boolean(token && projectId);
  return {
    enabled,
    status: enabled ? 'enabled' : 'disabled',
    credentials: {
      token: token || '',
      projectId: projectId || '',
      teamId: teamId || '',
    },
    endpoints: {
      api: 'https://api.vercel.com',
      status: '/api/vercel/status',
      deployments: '/api/vercel/deployments',
      logs: '/api/vercel/logs',
    },
    timeouts: {
      api: 30000,
      healthCheck: 10000,
    },
  };
}

/**
 * Configuração do Linear MCP
 */
function createLinearConfig(): MCPConfig {
  const apiKey = getEnvVar('MCP_LINEAR_API_KEY') || getEnvVar('MCP_LINEAR_API_KEY');
  const enabled = Boolean(apiKey);

  return {
    enabled,
    status: enabled ? 'enabled' : 'disabled',
    credentials: {
      apiKey: apiKey || '',
    },
    endpoints: {
      api: 'https://api.linear.app/graphql',
      status: '/api/linear/status',
      issues: '/api/linear/issues',
      teams: '/api/linear/teams',
    },
    timeouts: {
      api: 30000,
      healthCheck: 10000,
    },
  };
}

/**
 * Configuração do GitHub MCP
 */
function createGitHubConfig(): MCPConfig {
  const token = getEnvVar('MCP_GITHUB_TOKEN') || getEnvVar('MCP_GITHUB_TOKEN');
  const owner = getEnvVar('MCP_GITHUB_OWNER') || getEnvVar('MCP_GITHUB_OWNER');
  const repo = getEnvVar('MCP_GITHUB_REPO') || getEnvVar('MCP_GITHUB_REPO');

  const enabled = Boolean(token);

  return {
    enabled,
    status: enabled ? 'enabled' : 'disabled',
    credentials: {
      token: token || '',
      owner: owner || '',
      repo: repo || '',
    },
    endpoints: {
      api: 'https://api.github.com',
      status: '/api/github/status',
      repositories: '/api/github/repositories',
      issues: '/api/github/issues',
      workflows: '/api/github/workflows',
    },
    timeouts: {
      api: 30000,
      healthCheck: 10000,
    },
  };
}

/**
 * Configuração do Supabase MCP
 */
function createSupabaseConfig(): MCPConfig {
  const url = getEnvVar('SUPABASE_URL');
  const serviceRoleKey = getEnvVar('SUPABASE_SERVICE_ROLE_KEY');
  const anonKey = getEnvVar('SUPABASE_ANON_KEY');

  const enabled = Boolean(url && (serviceRoleKey || anonKey));

  return {
    enabled,
    status: enabled ? 'enabled' : 'disabled',
    credentials: {
      url: url || '',
      serviceRoleKey: serviceRoleKey || '',
      anonKey: anonKey || '',
    },
    endpoints: {
      api: url || '',
      status: '/api/supabase/status',
      tables: '/api/supabase/tables',
      storage: '/api/supabase/storage',
    },
    timeouts: {
      api: 30000,
      healthCheck: 10000,
    },
  };
}

/**
 * Configuração do Stripe MCP
 */
function createStripeConfig(): MCPConfig {
  const secretKey = getEnvVar('STRIPE_SECRET_KEY');
  const webhookSecret = getEnvVar('STRIPE_WEBHOOK_SECRET');
  const publishableKey = getEnvVar('NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY');

  const enabled = Boolean(secretKey);

  return {
    enabled,
    status: enabled ? 'enabled' : 'disabled',
    credentials: {
      secretKey: secretKey || '',
      webhookSecret: webhookSecret || '',
      publishableKey: publishableKey || '',
    },
    endpoints: {
      api: 'https://api.stripe.com',
      status: '/api/stripe/status',
      customers: '/api/stripe/customers',
      subscriptions: '/api/stripe/subscriptions',
      payments: '/api/stripe/payments',
    },
    timeouts: {
      api: 30000,
      healthCheck: 10000,
    },
  };
}

// ============================================================================
// CLASSE PRINCIPAL
// ============================================================================

/**
 * Classe para gerenciar configurações MCP unificadas
 */
export class MCPManager {
  private static instance: MCPManager;
  private config: MCPConfiguration;
  private lastValidation?: Date;

  private constructor() {
    this.config = this.loadConfiguration();
  }

  /**
   * Singleton pattern
   */
  public static getInstance(): MCPManager {
    if (!MCPManager.instance) {
      MCPManager.instance = new MCPManager();
    }
    return MCPManager.instance;
  }

  /**
   * Carrega configuração de todas as MCPs
   */
  private loadConfiguration(): MCPConfiguration {
    return {
      vercel: createVercelConfig(),
      linear: createLinearConfig(),
      github: createGitHubConfig(),
      supabase: createSupabaseConfig(),
      stripe: createStripeConfig(),
    };
  }

  /**
   * Obtém configuração de uma MCP específica
   */
  public getMCPConfig(type: MCPType): MCPConfig {
    return { ...this.config[type] };
  }

  /**
   * Obtém todas as configurações MCP
   */
  public getAllConfigs(): MCPConfiguration {
    return { ...this.config };
  }

  /**
   * Verifica se uma MCP está habilitada
   */
  public isEnabled(type: MCPType): boolean {
    return this.config[type].enabled;
  }

  /**
   * Obtém lista de MCPs habilitadas
   */
  public getEnabledMCPs(): MCPType[] {
    return (Object.keys(this.config) as MCPType[]).filter(type => this.isEnabled(type));
  }

  /**
   * Obtém estatísticas das MCPs
   */
  public getStats(): { total: number; enabled: number; disabled: number } {
    const total = Object.keys(this.config).length;
    const enabled = this.getEnabledMCPs().length;
    const disabled = total - enabled;

    return { total, enabled, disabled };
  }

  /**
   * Valida configuração de uma MCP específica
   */
  public validateMCP(type: MCPType): { valid: boolean; errors: string[] } {
    const config = this.config[type];
    const errors: string[] = [];

    if (!config.enabled) {
      return { valid: true, errors: [] }; // MCP desabilitada é válida
    }

    // Validações específicas por tipo
    switch (type) {
      case 'vercel':
        if (!config.credentials.token) errors.push('Token do Vercel é obrigatório');
        if (!config.credentials.projectId) errors.push('Project ID do Vercel é obrigatório');
        break;

      case 'linear':
        if (!config.credentials.apiKey) errors.push('API Key do Linear é obrigatória');
        break;

      case 'github':
        if (!config.credentials.token) errors.push('Token do GitHub é obrigatório');
        break;

      case 'supabase':
        if (!config.credentials.url) errors.push('URL do Supabase é obrigatória');
        if (!config.credentials.serviceRoleKey && !config.credentials.anonKey) {
          errors.push('Service Role Key ou Anon Key do Supabase é obrigatória');
        }
        break;

      case 'stripe':
        if (!config.credentials.secretKey) errors.push('Secret Key do Stripe é obrigatória');
        break;
    }

    return { valid: errors.length === 0, errors };
  }

  /**
   * Valida todas as configurações MCP
   */
  public validateAll(): {
    valid: boolean;
    results: Record<MCPType, { valid: boolean; errors: string[] }>;
  } {
    const results = {} as Record<MCPType, { valid: boolean; errors: string[] }>;
    let allValid = true;

    (Object.keys(this.config) as MCPType[]).forEach(type => {
      results[type] = this.validateMCP(type);
      if (!results[type].valid) {
        allValid = false;
      }
    });

    this.lastValidation = new Date();
    return { valid: allValid, results };
  }

  /**
   * Gera relatório de configuração MCP
   */
  public generateReport(): string {
    const stats = this.getStats();
    const validation = this.validateAll();

    let report = '🔌 RELATÓRIO DE CONFIGURAÇÃO MCP - EXCEL COPILOT\n';
    report += '='.repeat(60) + '\n\n';

    // Estatísticas gerais
    report += `📊 ESTATÍSTICAS:\n`;
    report += `   Total de MCPs: ${stats.total}\n`;
    report += `   Habilitadas: ${stats.enabled}\n`;
    report += `   Desabilitadas: ${stats.disabled}\n\n`;

    // Status por MCP
    report += `📋 STATUS POR MCP:\n`;
    report += '-'.repeat(30) + '\n';

    (Object.keys(this.config) as MCPType[]).forEach(type => {
      const config = this.config[type];
      const validation_result = validation.results[type];

      const statusIcon = config.enabled ? '✅' : '⚪';
      const validIcon = validation_result.valid ? '✅' : '❌';

      report += `${statusIcon} ${type.toUpperCase().padEnd(10)}: ${config.status.toUpperCase()} ${validIcon}\n`;
      if (!validation_result.valid) {
        validation_result.errors.forEach(error => {
          report += `     ❌ ${error}\n`;
        });
      }
    });

    report += `\n📅 Última validação: ${this.lastValidation?.toISOString() || 'Nunca'}\n`;
    report += `🎯 Status geral: ${validation.valid ? '✅ TODAS VÁLIDAS' : '❌ CORREÇÕES NECESSÁRIAS'}\n`;

    return report;
  }

  /**
   * Recarrega configuração (útil para testes)
   */
  public reload(): void {
    this.config = this.loadConfiguration();
    delete this.lastValidation;
  }
}

// ============================================================================
// EXPORTAÇÕES
// ============================================================================

/**
 * Instância singleton do gerenciador MCP
 */
export const mcpManager = MCPManager.getInstance();

/**
 * Função de conveniência para obter configuração MCP
 */
export function getMCPConfig(type: MCPType): MCPConfig {
  return mcpManager.getMCPConfig(type);
}

/**
 * Função de conveniência para verificar se MCP está habilitada
 */
export function isMCPEnabled(type: MCPType): boolean {
  return mcpManager.isEnabled(type);
}

/**
 * Função de conveniência para validar MCPs
 */
export function validateMCPs(): {
  valid: boolean;
  results: Record<MCPType, { valid: boolean; errors: string[] }>;
} {
  return mcpManager.validateAll();
}

/**
 * Configuração compatível com sistema antigo (para migração gradual)
 */
export const MCP_CONFIG = {
  VERCEL: mcpManager.getMCPConfig('vercel'),
  LINEAR: mcpManager.getMCPConfig('linear'),
  GITHUB: mcpManager.getMCPConfig('github'),
  SUPABASE: mcpManager.getMCPConfig('supabase'),
  STRIPE: mcpManager.getMCPConfig('stripe'),
};
