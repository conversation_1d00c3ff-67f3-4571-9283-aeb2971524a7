import { test, expect } from '@playwright/test';

test.describe('Comandos de IA e Análise Avançada', () => {
  // Configuração para autenticar e preparar uma planilha com dados
  test.beforeEach(async ({ page }) => {
    // Simular autenticação
    await page.context().addCookies([
      {
        name: 'next-auth.session-token',
        value: 'mock-session-token',
        domain: 'localhost',
        path: '/',
        httpOnly: true,
        secure: false,
      },
    ]);

    // Configurar dados do usuário
    await page.evaluate(() => {
      localStorage.setItem(
        'user',
        JSON.stringify({
          id: 'test-user-id',
          name: 'Test User',
          email: '<EMAIL>',
        })
      );
    });

    // Navegar para o dashboard
    await page.goto('/dashboard');

    // Criar uma nova planilha para testes
    await page.getByText('Criar Nova Planilha').click();
    await page.getByLabel('No<PERSON> da Planilha').fill('Planilha de Análise IA E2E');
    await page.getByRole('button', { name: /Criar/i }).click();

    // Verificar redirecionamento para a planilha
    await expect(page.url()).toContain('/workbook/');

    // Preencher a planilha com dados de exemplo para análise
    await page.locator('.excel-grid .cell').first().dblclick();
    await page.keyboard.type('Data');
    await page.keyboard.press('Tab');
    await page.keyboard.type('Vendedor');
    await page.keyboard.press('Tab');
    await page.keyboard.type('Produto');
    await page.keyboard.press('Tab');
    await page.keyboard.type('Valor');
    await page.keyboard.press('Tab');
    await page.keyboard.type('Região');
    await page.keyboard.press('Enter');

    // Linha 2
    await page.keyboard.type('01/01/2023');
    await page.keyboard.press('Tab');
    await page.keyboard.type('João');
    await page.keyboard.press('Tab');
    await page.keyboard.type('Notebook');
    await page.keyboard.press('Tab');
    await page.keyboard.type('3500');
    await page.keyboard.press('Tab');
    await page.keyboard.type('Sul');
    await page.keyboard.press('Enter');

    // Linha 3
    await page.keyboard.type('05/01/2023');
    await page.keyboard.press('Tab');
    await page.keyboard.type('Maria');
    await page.keyboard.press('Tab');
    await page.keyboard.type('Monitor');
    await page.keyboard.press('Tab');
    await page.keyboard.type('800');
    await page.keyboard.press('Tab');
    await page.keyboard.type('Norte');
    await page.keyboard.press('Enter');

    // Linha 4
    await page.keyboard.type('10/01/2023');
    await page.keyboard.press('Tab');
    await page.keyboard.type('Pedro');
    await page.keyboard.press('Tab');
    await page.keyboard.type('Notebook');
    await page.keyboard.press('Tab');
    await page.keyboard.type('3200');
    await page.keyboard.press('Tab');
    await page.keyboard.type('Sudeste');
    await page.keyboard.press('Enter');

    // Linha 5
    await page.keyboard.type('15/01/2023');
    await page.keyboard.press('Tab');
    await page.keyboard.type('João');
    await page.keyboard.press('Tab');
    await page.keyboard.type('Smartphone');
    await page.keyboard.press('Tab');
    await page.keyboard.type('2100');
    await page.keyboard.press('Tab');
    await page.keyboard.type('Sul');
    await page.keyboard.press('Enter');
  });

  test('deve executar análise com tabela dinâmica via comando', async ({ page }) => {
    // Navegar para a aba de chat
    await page.getByRole('tab', { name: 'Chat' }).click();

    // Enviar comando para criar tabela dinâmica
    await page
      .getByPlaceholder('Digite sua mensagem...')
      .fill(
        'Crie uma tabela dinâmica com vendedor nas linhas e região nas colunas, somando os valores de venda'
      );
    await page.getByRole('button', { name: 'Enviar' }).click();

    // Verificar resposta do assistente
    await expect(page.getByText(/Tabela dinâmica criada/i)).toBeVisible({ timeout: 10000 });

    // Verificar se a tabela dinâmica é exibida
    await expect(page.locator('.pivot-table')).toBeVisible();

    // Verificar se os dados corretos estão na tabela
    await expect(page.locator('.pivot-table')).toContainText('João');
    await expect(page.locator('.pivot-table')).toContainText('Sul');
  });

  test('deve executar comando de filtro complexo', async ({ page }) => {
    // Navegar para a aba de chat
    await page.getByRole('tab', { name: 'Chat' }).click();

    // Enviar comando para filtrar dados
    await page
      .getByPlaceholder('Digite sua mensagem...')
      .fill('Filtre os dados para mostrar apenas vendas acima de 3000 na região Sul');
    await page.getByRole('button', { name: 'Enviar' }).click();

    // Verificar resposta do assistente
    await expect(page.getByText(/Filtro aplicado/i)).toBeVisible({ timeout: 10000 });

    // Verificar se a tabela filtrada ainda mostra os cabeçalhos
    await expect(page.locator('.excel-grid')).toContainText('Data');
    await expect(page.locator('.excel-grid')).toContainText('Valor');

    // Verificar se apenas os dados filtrados estão visíveis
    // Deveria mostrar apenas a venda do Notebook por João
    await expect(page.locator('.excel-grid')).toContainText('João');
    await expect(page.locator('.excel-grid')).toContainText('Notebook');
    await expect(page.locator('.excel-grid')).toContainText('3500');
    await expect(page.locator('.excel-grid')).toContainText('Sul');
  });

  test('deve realizar análise de tendência com visualização', async ({ page }) => {
    // Navegar para a aba de chat
    await page.getByRole('tab', { name: 'Chat' }).click();

    // Enviar comando para analisar tendência
    await page
      .getByPlaceholder('Digite sua mensagem...')
      .fill('Analise a tendência de vendas por data e mostre um gráfico de linha');
    await page.getByRole('button', { name: 'Enviar' }).click();

    // Verificar resposta do assistente
    await expect(page.getByText(/Análise de tendência/i)).toBeVisible({ timeout: 10000 });

    // Verificar se o gráfico é exibido
    await expect(page.locator('.chart-container canvas')).toBeVisible();

    // Verificar se há uma análise textual junto com o gráfico
    await expect(page.getByText(/tendência/i)).toBeVisible();
  });

  test('deve aplicar formatação condicional via comando', async ({ page }) => {
    // Navegar para a aba de chat
    await page.getByRole('tab', { name: 'Chat' }).click();

    // Enviar comando para formatação condicional
    await page
      .getByPlaceholder('Digite sua mensagem...')
      .fill(
        'Aplique formatação condicional na coluna de Valor, destacando em verde valores acima de 3000'
      );
    await page.getByRole('button', { name: 'Enviar' }).click();

    // Verificar resposta do assistente
    await expect(page.getByText(/Formatação condicional aplicada/i)).toBeVisible({
      timeout: 10000,
    });

    // Voltar para a visualização de dados
    await page.getByRole('tab', { name: 'Dados' }).click();

    // Verificar se a formatação foi aplicada (célula com valor 3500 deve ter classe de estilo específica)
    // Na linha 2, coluna 4 (índice 3) temos o valor 3500
    await expect(page.locator('.excel-grid .row').nth(1).locator('.cell').nth(3)).toHaveClass(
      /bg-green/
    );
  });

  test('deve responder a consultas de linguagem natural sobre dados', async ({ page }) => {
    // Navegar para a aba de chat
    await page.getByRole('tab', { name: 'Chat' }).click();

    // Enviar pergunta em linguagem natural
    await page.getByPlaceholder('Digite sua mensagem...').fill('Qual vendedor teve mais vendas?');
    await page.getByRole('button', { name: 'Enviar' }).click();

    // Verificar resposta do assistente
    await expect(page.getByText(/João/i)).toBeVisible({ timeout: 10000 });

    // Fazer outra pergunta
    await page.getByPlaceholder('Digite sua mensagem...').fill('Qual o valor médio das vendas?');
    await page.getByRole('button', { name: 'Enviar' }).click();

    // Verificar resposta com o cálculo
    await expect(page.getByText(/média/i)).toBeVisible({ timeout: 10000 });
    await expect(page.getByText(/2400/i)).toBeVisible({ timeout: 10000 });
  });
});
