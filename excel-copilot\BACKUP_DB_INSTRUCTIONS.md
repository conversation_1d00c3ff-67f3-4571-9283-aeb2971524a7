# Instruções para Backup do Banco de Dados - Excel Copilot

Este documento descreve como configurar e usar o sistema de backup automático do banco de dados para o Excel Copilot.

## Visão Geral

O Excel Copilot inclui um sistema de backup automatizado para garantir a segurança dos dados. O sistema oferece:

- Backup sob demanda
- Backup agendado (diário/semanal)
- Retenção configurável de backups antigos
- Suporte a diferentes tipos de banco de dados (MySQL/PlanetScale, SQLite)

## Comandos Disponíveis

Os seguintes comandos foram adicionados ao `package.json`:

```bash
# Backup manual padrão (retenção de 30 dias)
npm run db:backup

# Backup diário (retenção de 7 dias)
npm run db:backup:daily

# Backup semanal (retenção de 30 dias, pasta separada)
npm run db:backup:weekly
```

## Agendamento Automático

### No Linux/macOS (usando cron)

Para configurar backups automáticos usando cron:

1. <PERSON><PERSON> o editor de crontab:

   ```bash
   crontab -e
   ```

2. <PERSON><PERSON><PERSON> as seguin<PERSON> linhas para backups diários e semanais:

   ```
   # Backup diário às 3:00 AM
   0 3 * * * cd /caminho/para/excel-copilot && npm run db:backup:daily >> /caminho/para/logs/backup-daily.log 2>&1

   # Backup semanal aos domingos às 2:00 AM
   0 2 * * 0 cd /caminho/para/excel-copilot && npm run db:backup:weekly >> /caminho/para/logs/backup-weekly.log 2>&1
   ```

3. Salve e saia do editor

### No Windows (usando Agendador de Tarefas)

1. Abra o "Agendador de Tarefas" (Task Scheduler)
2. Clique em "Criar Tarefa Básica"
3. Dê um nome como "Excel Copilot - Backup Diário"
4. Escolha "Diariamente" e defina o horário para 3:00 AM
5. Escolha "Iniciar um programa"
6. Em "Programa/script", insira: `C:\Windows\System32\cmd.exe`
7. Em "Argumentos", insira: `/c cd /d C:\caminho\para\excel-copilot && npm run db:backup:daily >> logs\backup-daily.log 2>&1`
8. Finalize o assistente

Repita para o backup semanal, alterando a frequência para "Semanalmente" e escolhendo o domingo.

## Localização dos Backups

Por padrão, os backups são armazenados em:

- Backup padrão e diário: `./backups/`
- Backup semanal: `./backups/weekly/`

Os arquivos são nomeados no formato `excel-copilot-backup-YYYY-MM-DDTHH-mm-ss.json` ou `.sql`/`.db`.

## Opções Adicionais do Script

O script de backup aceita os seguintes parâmetros:

```bash
# Definir diretório de saída personalizado
node scripts/backup-database.js --output=./meu-diretorio

# Definir período de retenção (em dias)
node scripts/backup-database.js --retain=15

# Combinar opções
node scripts/backup-database.js --output=./meus-backups --retain=60
```

## Armazenamento Externo

Para maior segurança, é altamente recomendável copiar os backups para um local externo. Isso pode ser feito:

### Opção 1: Backup para serviço de nuvem

Adicione ao seu crontab ou Agendador de Tarefas:

```bash
# Exemplo para AWS S3 (requer AWS CLI configurado)
0 5 * * * aws s3 sync /caminho/para/excel-copilot/backups s3://meu-bucket-de-backup/excel-copilot

# Exemplo para Google Cloud Storage (requer gsutil configurado)
0 5 * * * gsutil -m rsync -r /caminho/para/excel-copilot/backups gs://meu-bucket-de-backup/excel-copilot
```

### Opção 2: Backup para disco externo

No Linux:

```bash
0 5 * * * rsync -av /caminho/para/excel-copilot/backups/ /media/disco-externo/excel-copilot-backups/
```

No Windows, crie um script batch `backup-to-external.bat`:

```batch
@echo off
xcopy /E /Y /I C:\caminho\para\excel-copilot\backups E:\excel-copilot-backups
```

## Restauração de Backup

Para restaurar a partir de um backup:

### Backup JSON (via Prisma)

1. Pare o serviço do Excel Copilot
2. Execute o script de restauração:
   ```bash
   node scripts/restore-database.js --file=./backups/excel-copilot-backup-2025-01-01T12-00-00.json
   ```

### Backup SQL (MySQL/PlanetScale)

1. Pare o serviço do Excel Copilot
2. Restaure usando o cliente MySQL:
   ```bash
   mysql -h seu-host -u seu-usuario -p seu-banco < ./backups/excel-copilot-backup-2025-01-01T12-00-00.sql
   ```

### Backup SQLite (.db)

1. Pare o serviço do Excel Copilot
2. Faça backup do arquivo SQLite atual (por precaução)
3. Copie o arquivo de backup para o local correto (substitua o arquivo existente)

## Monitoramento de Backups

É importante monitorar regularmente os backups para garantir que estão sendo criados corretamente:

1. Verifique os logs após cada backup agendado
2. Configure alertas para falhas de backup
3. Teste periodicamente a restauração dos backups em um ambiente de teste

## Troubleshooting

### Erro de permissão ao executar o backup

Certifique-se de que o usuário que executa o script tem permissão de escrita no diretório de backup.

### Erro de conexão com o banco de dados

Verifique se as credenciais do banco de dados estão corretas no arquivo `.env.production` ou `.env`.

### Backup muito grande ou lento

Para bancos de dados grandes, considere:

- Fazer backup apenas de tabelas essenciais
- Aumentar o timeout de conexão
- Agendar em horários de baixo tráfego

## Considerações de Segurança

- Proteja os arquivos de backup com permissões adequadas
- Criptografe backups sensíveis antes de armazená-los externamente
- Implemente rotação de backups e retenção para gerenciar espaço
- Nunca armazene backups no mesmo servidor da aplicação em produção
