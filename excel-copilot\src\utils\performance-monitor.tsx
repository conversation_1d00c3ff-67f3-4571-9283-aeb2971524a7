/**
 * Sistema de monitoramento de performance para componentes UI otimizados
 * Monitora re-renderizações, tempos de carregamento e métricas de performance
 */

import React from 'react';

interface PerformanceMetric {
  componentName: string;
  renderCount: number;
  lastRenderTime: number;
  averageRenderTime: number;
  totalRenderTime: number;
  isOptimized: boolean;
}

interface RenderEvent {
  componentName: string;
  timestamp: number;
  renderTime: number;
  props?: Record<string, unknown>;
  isOptimized: boolean;
}

class PerformanceMonitor {
  private metrics: Map<string, PerformanceMetric> = new Map();
  private renderEvents: RenderEvent[] = [];
  private isEnabled: boolean = false;

  constructor() {
    // Habilitar apenas em desenvolvimento
    this.isEnabled = process.env.NODE_ENV === 'development';
  }

  /**
   * Registra uma renderização de componente
   */
  recordRender(
    componentName: string,
    renderTime: number,
    isOptimized: boolean = false,
    props?: Record<string, unknown>
  ): void {
    if (!this.isEnabled) return;

    const timestamp = Date.now();

    // Registrar evento
    this.renderEvents.push({
      componentName,
      timestamp,
      renderTime,
      props: props || {},
      isOptimized,
    });

    // Atualizar métricas
    const existing = this.metrics.get(componentName);
    if (existing) {
      existing.renderCount++;
      existing.lastRenderTime = renderTime;
      existing.totalRenderTime += renderTime;
      existing.averageRenderTime = existing.totalRenderTime / existing.renderCount;
    } else {
      this.metrics.set(componentName, {
        componentName,
        renderCount: 1,
        lastRenderTime: renderTime,
        averageRenderTime: renderTime,
        totalRenderTime: renderTime,
        isOptimized,
      });
    }

    // Limitar histórico de eventos (últimos 1000)
    if (this.renderEvents.length > 1000) {
      this.renderEvents = this.renderEvents.slice(-1000);
    }
  }

  /**
   * Obtém métricas de um componente específico
   */
  getComponentMetrics(componentName: string): PerformanceMetric | undefined {
    return this.metrics.get(componentName);
  }

  /**
   * Obtém todas as métricas
   */
  getAllMetrics(): PerformanceMetric[] {
    return Array.from(this.metrics.values());
  }

  /**
   * Obtém eventos de renderização recentes
   */
  getRecentRenderEvents(limit: number = 100): RenderEvent[] {
    return this.renderEvents.slice(-limit);
  }

  /**
   * Compara performance entre componentes otimizados e não otimizados
   */
  getPerformanceComparison(): {
    optimized: PerformanceMetric[];
    nonOptimized: PerformanceMetric[];
    improvement: {
      averageRenderTimeReduction: number;
      renderCountReduction: number;
    };
  } {
    const optimized = this.getAllMetrics().filter(m => m.isOptimized);
    const nonOptimized = this.getAllMetrics().filter(m => !m.isOptimized);

    const avgOptimized =
      optimized.reduce((sum, m) => sum + m.averageRenderTime, 0) / optimized.length || 0;
    const avgNonOptimized =
      nonOptimized.reduce((sum, m) => sum + m.averageRenderTime, 0) / nonOptimized.length || 0;

    const avgRenderCountOptimized =
      optimized.reduce((sum, m) => sum + m.renderCount, 0) / optimized.length || 0;
    const avgRenderCountNonOptimized =
      nonOptimized.reduce((sum, m) => sum + m.renderCount, 0) / nonOptimized.length || 0;

    return {
      optimized,
      nonOptimized,
      improvement: {
        averageRenderTimeReduction:
          avgNonOptimized > 0 ? ((avgNonOptimized - avgOptimized) / avgNonOptimized) * 100 : 0,
        renderCountReduction:
          avgRenderCountNonOptimized > 0
            ? ((avgRenderCountNonOptimized - avgRenderCountOptimized) /
                avgRenderCountNonOptimized) *
              100
            : 0,
      },
    };
  }

  /**
   * Gera relatório de performance
   */
  generateReport(): string {
    const comparison = this.getPerformanceComparison();
    const totalComponents = this.metrics.size;
    const optimizedCount = comparison.optimized.length;
    const nonOptimizedCount = comparison.nonOptimized.length;

    return `
📊 RELATÓRIO DE PERFORMANCE - COMPONENTES UI

📈 Resumo Geral:
- Total de componentes monitorados: ${totalComponents}
- Componentes otimizados: ${optimizedCount}
- Componentes não otimizados: ${nonOptimizedCount}

🚀 Melhorias de Performance:
- Redução média no tempo de renderização: ${comparison.improvement.averageRenderTimeReduction.toFixed(2)}%
- Redução média no número de renderizações: ${comparison.improvement.renderCountReduction.toFixed(2)}%

🔝 Top 5 Componentes Mais Renderizados:
${this.getAllMetrics()
  .sort((a, b) => b.renderCount - a.renderCount)
  .slice(0, 5)
  .map(
    (m, i) =>
      `${i + 1}. ${m.componentName}: ${m.renderCount} renders (${m.isOptimized ? '✅ Otimizado' : '❌ Não otimizado'})`
  )
  .join('\n')}

⚡ Top 5 Componentes Mais Lentos:
${this.getAllMetrics()
  .sort((a, b) => b.averageRenderTime - a.averageRenderTime)
  .slice(0, 5)
  .map(
    (m, i) =>
      `${i + 1}. ${m.componentName}: ${m.averageRenderTime.toFixed(2)}ms (${m.isOptimized ? '✅ Otimizado' : '❌ Não otimizado'})`
  )
  .join('\n')}
    `.trim();
  }

  /**
   * Limpa todas as métricas
   */
  clear(): void {
    this.metrics.clear();
    this.renderEvents = [];
  }

  /**
   * Exporta dados para análise externa
   */
  exportData() {
    const summary = this.getPerformanceComparison();
    return {
      metrics: this.getAllMetrics(),
      events: this.renderEvents,
      summary,
    };
  }
}

// Instância singleton
export const performanceMonitor = new PerformanceMonitor();

/**
 * Hook para monitorar performance de componentes React
 */
export function usePerformanceMonitor(componentName: string, isOptimized: boolean = false) {
  const startTime = React.useRef<number>(0);

  React.useEffect(() => {
    startTime.current = performance.now();
  });

  React.useEffect(() => {
    const renderTime = performance.now() - startTime.current;
    performanceMonitor.recordRender(componentName, renderTime, isOptimized);
  });

  return {
    recordCustomMetric: (metricName: string, value: number) => {
      performanceMonitor.recordRender(`${componentName}.${metricName}`, value, isOptimized);
    },
  };
}

/**
 * HOC para monitoramento automático de performance
 */
export function withPerformanceMonitoring<P extends object>(
  Component: React.ComponentType<P>,
  componentName?: string,
  isOptimized: boolean = false
) {
  const WrappedComponent = React.forwardRef<unknown, P>((props, ref) => {
    const name = componentName || Component.displayName || Component.name || 'UnknownComponent';
    usePerformanceMonitor(name, isOptimized);

    // Usar JSX em vez de createElement para melhor tipagem
    return <Component {...(props as P)} ref={ref} />;
  });

  WrappedComponent.displayName = `withPerformanceMonitoring(${componentName || Component.displayName || Component.name})`;

  return WrappedComponent;
}
