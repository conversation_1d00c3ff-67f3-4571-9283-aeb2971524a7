'use client';

import { useEffect } from 'react';

/**
 * Componente para suprimir erros de RSC (React Server Components) no cliente
 * Intercepta e suprime erros relacionados a _rsc que aparecem no console
 */
export function RSCErrorSuppressor() {
  useEffect(() => {
    // Interceptar erros de console relacionados a RSC
    const originalConsoleError = console.error;
    const originalConsoleWarn = console.warn;

    console.error = (...args: any[]) => {
      const message = args.join(' ');

      // Padrões de erro RSC para suprimir
      const rscErrorPatterns = [
        '_rsc=',
        'RSC payload',
        'Failed to load resource: the server responded with a status of 404',
        'Rejeição de Promise não tratada',
        'Erro da aplicação',
        'ChunkLoadError',
        'Loading chunk',
        'Loading CSS chunk',
        'fetch error',
        'Network Error',
      ];

      // Verificar se é um erro RSC
      const isRSCError = rscErrorPatterns.some(pattern =>
        message.toLowerCase().includes(pattern.toLowerCase())
      );

      // Se não for erro RSC, mostrar normalmente
      if (!isRSCError) {
        originalConsoleError.apply(console, args);
      } else {
        // Log silencioso para debug se necessário (apenas em desenvolvimento)
        if (process.env.NODE_ENV === 'development') {
          // Usar console.debug que é menos intrusivo
          console.debug('[RSC Error Suppressed]:', message);
        }
      }
    };

    console.warn = (...args: any[]) => {
      const message = args.join(' ');

      // Padrões de warning RSC para suprimir
      const rscWarningPatterns = ['_rsc=', 'RSC', 'chunk', 'hydration'];

      // Verificar se é um warning RSC
      const isRSCWarning = rscWarningPatterns.some(pattern =>
        message.toLowerCase().includes(pattern.toLowerCase())
      );

      // Se não for warning RSC, mostrar normalmente
      if (!isRSCWarning) {
        originalConsoleWarn.apply(console, args);
      }
    };

    // Interceptar erros de Promise rejeitada
    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      const reason = event.reason?.toString() || '';

      // Padrões de erro de Promise relacionados a RSC
      const rscPromisePatterns = ['_rsc=', 'fetch', '404', 'ChunkLoadError', 'Loading chunk'];

      const isRSCPromiseError = rscPromisePatterns.some(pattern =>
        reason.toLowerCase().includes(pattern.toLowerCase())
      );

      if (isRSCPromiseError) {
        // Prevenir que o erro apareça no console
        event.preventDefault();

        if (process.env.NODE_ENV === 'development') {
          console.debug('[RSC Promise Error Suppressed]:', reason);
        }
      }
    };

    // Interceptar erros globais
    const handleError = (event: ErrorEvent) => {
      const message = event.message || '';

      // Padrões de erro global relacionados a RSC
      const rscGlobalPatterns = ['_rsc=', 'ChunkLoadError', 'Loading chunk', 'fetch'];

      const isRSCGlobalError = rscGlobalPatterns.some(pattern =>
        message.toLowerCase().includes(pattern.toLowerCase())
      );

      if (isRSCGlobalError) {
        // Prevenir que o erro apareça no console
        event.preventDefault();

        if (process.env.NODE_ENV === 'development') {
          console.debug('[RSC Global Error Suppressed]:', message);
        }
      }
    };

    // Adicionar listeners
    window.addEventListener('unhandledrejection', handleUnhandledRejection);
    window.addEventListener('error', handleError);

    // Cleanup
    return () => {
      console.error = originalConsoleError;
      console.warn = originalConsoleWarn;
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
      window.removeEventListener('error', handleError);
    };
  }, []);

  return null; // Componente não renderiza nada
}
