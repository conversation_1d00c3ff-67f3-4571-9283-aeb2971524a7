const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

/**
 * Este script ajuda a encontrar e potencialmente corrigir problemas de
 * "Object is possibly 'null' or 'undefined'" no código TypeScript.
 */

console.log('🔍 Procurando por erros de propriedades null ou undefined...');

// Diretórios a serem verificados
const dirsToCheck = ['src', '__tests__'];

// Padrões comuns a serem buscados
const patterns = [
  "\\w+\\s+is\\s+possibly\\s+'null'",
  "\\w+\\s+is\\s+possibly\\s+'undefined'",
  "Object\\s+is\\s+possibly\\s+'null'",
];

// Armazenar resultados
const results = [];

// Buscar por erros no código
try {
  for (const dir of dirsToCheck) {
    for (const pattern of patterns) {
      try {
        const output = execSync(
          `npx grep-cli --glob "**/*.{ts,tsx}" --ignore-case "${pattern}" ${dir}`,
          { encoding: 'utf8' }
        ).trim();

        if (output) {
          const lines = output.split('\n').filter(Boolean);
          results.push(...lines);
        }
      } catch (e) {
        // grep-cli retorna status code 1 quando não encontra correspondências
        if (e.status !== 1) {
          console.error(`Erro ao buscar padrão ${pattern} em ${dir}:`, e.message);
        }
      }
    }
  }
} catch (e) {
  console.error('Erro ao executar busca:', e.message);
}

// Exibir resultados e sugestões
if (results.length > 0) {
  console.log(`\n🔴 Encontrado ${results.length} potenciais problemas de null/undefined checks.`);
  console.log('\nAqui estão alguns exemplos e sugestões para correção:');

  // Mostrar até 10 exemplos
  const examples = results.slice(0, 10);
  examples.forEach((result, index) => {
    console.log(`\n${index + 1}. ${result}`);
  });

  // Mostrar recomendações
  console.log('\n📝 Recomendações para correção:');
  console.log(
    '1. Use operador de opcional chaining (?.) para acessar propriedades que podem ser null/undefined'
  );
  console.log('   Exemplo: object.property -> object?.property');

  console.log('\n2. Use operador de coalescência nula (??) para fornecer valores padrão');
  console.log('   Exemplo: variable || defaultValue -> variable ?? defaultValue');

  console.log('\n3. Adicione verificações de null/undefined antes de acessar propriedades');
  console.log('   Exemplo:');
  console.log('   if (object && object.property) { /* código */ }');

  console.log('\n4. Use type guards para refinar tipos');
  console.log('   Exemplo:');
  console.log('   if (typeof variable === "string") { /* variable é string aqui */ }');

  if (results.length > 10) {
    console.log(`\n⚠️ Mais ${results.length - 10} problemas encontrados mas não exibidos.`);
  }
} else {
  console.log('✅ Nenhum problema óbvio de null/undefined encontrado.');
}

console.log('\n📋 Para uma verificação completa, execute:');
console.log('npx tsc --noEmit --project tsconfig.json');
