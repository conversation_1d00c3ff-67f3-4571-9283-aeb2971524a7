/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  images: {
    domains: [
      'lh3.googleusercontent.com',
      'github.com',
      'avatars.githubusercontent.com',
      'avatar.vercel.sh',
    ],
    dangerouslyAllowSVG: true,
    contentDispositionType: 'attachment',
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
  },
  experimental: {
    optimizeCss: true,
    scrollRestoration: true,
    forceSwcTransforms: true,
    webpackBuildWorker: true,
    serverComponentsExternalPackages: ['@google-cloud/vertexai', 'googleapis'],
  },
  compiler: {
    removeConsole:
      process.env.NODE_ENV === 'production'
        ? {
            exclude: ['error', 'warn'],
          }
        : false,
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  compress: true,
  excludeDefaultMomentLocales: true,
  serverRuntimeConfig: {
    dynamicRoutes: ['/api/api-docs', '/api/metrics', '/api/ws', '/api/socket'],
  },
  async headers() {
    return [
      {
        source: '/:path*',
        headers: [
          {
            key: 'X-DNS-Prefetch-Control',
            value: 'on',
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block',
          },
          {
            key: 'X-Frame-Options',
            value: 'SAMEORIGIN',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin',
          },
          {
            key: 'Content-Security-Policy',
            value:
              "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://apis.google.com https://js.stripe.com https://accounts.google.com https://github.com; script-src-elem 'self' 'unsafe-inline' https://js.stripe.com https://cdn.jsdelivr.net https://apis.google.com https://accounts.google.com https://github.com; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://fonts.googleapis.com fonts.gstatic.com; img-src 'self' data: https: blob:; font-src 'self' https://cdn.jsdelivr.net https://fonts.googleapis.com https://fonts.gstatic.com data:; connect-src 'self' https://api.openai.com https://api.stripe.com https://*.vercel.app https://*.googleapis.com https://*.google.com https://github.com https://accounts.google.com fonts.googleapis.com fonts.gstatic.com; frame-src 'self' https://js.stripe.com https://accounts.google.com https://github.com; object-src 'none'; base-uri 'self'; form-action 'self' https://accounts.google.com https://github.com; frame-ancestors 'self'; upgrade-insecure-requests;",
          },
          {
            key: 'Permissions-Policy',
            value: 'camera=(), microphone=(), geolocation=(), interest-cohort=()',
          },
        ],
      },
    ];
  },
  webpack: (config, { isServer, webpack }) => {
    config.cache = {
      type: 'filesystem',
      buildDependencies: {
        config: [__filename],
      },
    };

    const forceGoogleMocks =
      process.env.NODE_ENV !== 'production' &&
      (process.env.AI_USE_MOCK === 'true' || process.env.AI_ENABLED === 'true');

    if (forceGoogleMocks) {
      // eslint-disable-next-line @typescript-eslint/no-require-imports
      const path = require('path');
      const mockModulesPath = path.resolve(__dirname, 'src/lib/ai/mock-modules.js');
      // eslint-disable-next-line @typescript-eslint/no-require-imports
      const fs = require('fs');

      const mockDir = path.resolve(__dirname, 'src/lib/ai');
      if (!fs.existsSync(mockDir)) {
        fs.mkdirSync(mockDir, { recursive: true });
      }

      if (!fs.existsSync(mockModulesPath)) {
        const mockContent = `
module.exports = {
  VertexAI: class MockVertexAI {
    constructor() {}
    getGenerativeModel() {
      return {
        generateContent: async () => ({ response: { text: () => "Mock response" }}),
        streamGenerateContent: async () => ({ stream: [] })
      };
    }
  },
  GoogleGenAI: class MockGoogleGenAI {
    constructor() {}
    getGenerativeModel() {
      return {
        generateContent: async () => ({ response: { text: () => "Mock response" }}),
        streamGenerateContent: async () => ({ stream: [] })
      };
    }
  },
  HarmCategory: {},
  HarmBlockThreshold: {}
};`;
        fs.writeFileSync(mockModulesPath, mockContent);
      }

      config.resolve.alias = {
        ...config.resolve.alias,
        '@google-cloud/vertexai': mockModulesPath,
        '@google-cloud/aiplatform': mockModulesPath,
        '@google/genai': mockModulesPath,
      };

      config.plugins.push(
        new webpack.NormalModuleReplacementPlugin(/^@google-cloud\/vertexai$/, resource => {
          resource.request = mockModulesPath;
        }),
        new webpack.NormalModuleReplacementPlugin(/^@google-cloud\/aiplatform$/, resource => {
          resource.request = mockModulesPath;
        }),
        new webpack.NormalModuleReplacementPlugin(/^@google\/genai$/, resource => {
          resource.request = mockModulesPath;
        })
      );
    }

    if (isServer) {
      config.externals = [
        ...(Array.isArray(config.externals)
          ? config.externals
          : config.externals
            ? [config.externals]
            : []),
        {
          '@google-cloud/aiplatform': 'commonjs @google-cloud/aiplatform',
          '@google-cloud/vertexai': 'commonjs @google-cloud/vertexai',
          '@google/genai': 'commonjs @google/genai',
          'google-gax': 'commonjs google-gax',
        },
      ];
    }

    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
        dns: false,
        child_process: false,
        http2: false,
        process: require.resolve('process/browser'),
        path: require.resolve('path-browserify'),
        crypto: require.resolve('crypto-browserify'),
        stream: require.resolve('stream-browserify'),
        buffer: require.resolve('buffer'),
        util: require.resolve('util'),
        events: require.resolve('events'),
        url: require.resolve('url'),
        http: require.resolve('stream-http'),
        https: require.resolve('https-browserify'),
        assert: require.resolve('assert'),
        zlib: require.resolve('browserify-zlib'),
        querystring: require.resolve('querystring-es3'),
        os: require.resolve('os-browserify/browser'),
        'node:events': require.resolve('events'),
        'node:process': require.resolve('process/browser'),
        'node:util': require.resolve('util'),
        'node:path': require.resolve('path-browserify'),
        'node:crypto': require.resolve('crypto-browserify'),
        'node:stream': require.resolve('stream-browserify'),
        'node:buffer': require.resolve('buffer'),
        'node:url': require.resolve('url'),
        'node:http': require.resolve('stream-http'),
        'node:https': require.resolve('https-browserify'),
        'node:assert': require.resolve('assert'),
        'node:zlib': require.resolve('browserify-zlib'),
        'node:querystring': require.resolve('querystring-es3'),
        'node:os': require.resolve('os-browserify/browser'),
        'node:fs': false,
        'node:net': false,
        'node:tls': false,
        'node:dns': false,
        'node:child_process': false,
        'node:http2': false,
        '@google-cloud/vertexai': false,
        '@google-cloud/aiplatform': false,
        '@google/genai': false,
        '@google/generative-ai': false,
        'google-generative-ai': false,
        'generative-ai': false,
        'google-auth-library': false,
        'gcp-metadata': false,
        'google-logging-utils': false,
        'google-gax': false,
        '@grpc/grpc-js': false,
        grpc: false,
      };

      config.plugins.push(
        new webpack.ProvidePlugin({
          process: 'process/browser',
          Buffer: ['buffer', 'Buffer'],
        })
      );
    }

    return config;
  },
  productionBrowserSourceMaps: false,
  poweredByHeader: false,
  output: 'standalone',
  staticPageGenerationTimeout: 180,
  i18n: {
    locales: ['pt-BR', 'en-US', 'es'],
    defaultLocale: 'pt-BR',
    localeDetection: false,
  },
  transpilePackages: [],
  async redirects() {
    return [
      {
        source: '/api/:path*',
        has: [
          {
            type: 'header',
            key: 'x-legacy-api',
            value: '(?<legacyValue>.*)',
          },
        ],
        destination: '/api/legacy-redirect/:path*',
        permanent: false,
      },
    ];
  },
};

module.exports = nextConfig;
