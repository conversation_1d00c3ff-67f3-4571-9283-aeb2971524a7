#!/usr/bin/env node

/**
 * Script para formatar automaticamente o código com Prettier
 * Resolve problemas de formatação reportados pelo ESLint
 */

const { execSync } = require('child_process');

// Cores para console
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
};

console.log(`${colors.cyan}Iniciando formatação de código...${colors.reset}`);

try {
  // Formatar arquivos TypeScript e JavaScript
  console.log(`${colors.blue}Formatando arquivos TypeScript e JavaScript...${colors.reset}`);
  execSync('npx prettier --write "src/**/*.{ts,tsx,js,jsx}"', { stdio: 'inherit' });

  // Formatar arquivos JSON e CSS
  console.log(`${colors.blue}Formatando arquivos JSON, CSS, SCSS e YAML...${colors.reset}`);
  execSync('npx prettier --write "src/**/*.{json,css,scss,yaml,yml}"', { stdio: 'inherit' });

  // Formatar arquivos de configuração na raiz
  console.log(`${colors.blue}Formatando arquivos de configuração...${colors.reset}`);
  execSync('npx prettier --write "*.{json,js,ts,yml,yaml}"', { stdio: 'inherit' });

  console.log(`${colors.green}Formatação de código concluída com sucesso!${colors.reset}`);
} catch (error) {
  console.error(`${colors.red}Erro ao formatar código:${colors.reset}`, error.message);
  process.exit(1);
}
