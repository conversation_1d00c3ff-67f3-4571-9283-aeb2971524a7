/**
 * Testes de integração para VercelMonitoringService
 * Testa a integração entre VercelClient e VercelMonitoringService
 */

import { VercelMonitoringService, VercelClient } from '@/lib/vercel-integration';

// Mock do VercelClient para testes de integração
jest.mock('@/lib/vercel-integration', () => {
  const originalModule = jest.requireActual('@/lib/vercel-integration');

  return {
    ...originalModule,
    VercelClient: jest.fn().mockImplementation(() => ({
      getProject: jest.fn(),
      getDeployments: jest.fn(),
      getProjectMetrics: jest.fn(),
      getDeploymentLogs: jest.fn(),
    })),
  };
});

// Mock do logger
jest.mock('@/lib/logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
  },
}));

describe('VercelMonitoringService Integration', () => {
  let service: VercelMonitoringService;
  let mockClient: jest.Mocked<VercelClient>;

  beforeEach(() => {
    jest.clearAllMocks();
    service = new VercelMonitoringService('test_token', 'team_test123', 'prj_test123');
    mockClient = (service as any).client as jest.Mocked<VercelClient>;
  });

  describe('getProjectStatus', () => {
    it('deve integrar dados do projeto, deployments e métricas', async () => {
      // Arrange
      const mockProject = {
        id: 'prj_test123',
        name: 'excel-copilot',
        framework: 'nextjs',
        createdAt: *************,
        updatedAt: Date.now(),
        accountId: 'acc_test123',
        directoryListing: false,
        env: [],
      };

      const mockDeployments = [
        {
          uid: 'dpl_prod123',
          name: 'excel-copilot',
          url: 'https://excel-copilot.vercel.app',
          state: 'READY' as const,
          type: 'LAMBDAS' as const,
          created: *************,
          ready: *************,
          target: 'production' as const,
        },
      ];

      mockClient.getDeployments.mockResolvedValue(mockDeployments);
      mockClient.checkRecentErrors.mockResolvedValue({
        hasErrors: false,
        errorCount: 0,
        errors: [],
      });

      // Act
      const result = await service.getProjectStatus();

      // Assert
      expect(result).toHaveProperty('status');
      expect(result).toHaveProperty('lastDeployment');
      expect(result).toHaveProperty('recentErrors');
      expect(result).toHaveProperty('uptime');
      expect(result).toHaveProperty('message');
      expect(result.status).toBe('healthy');
      expect(result.lastDeployment?.uid).toBe('dpl_prod123');
      expect(result.recentErrors).toBe(0);
    });

    it('deve determinar status degraded baseado em erros', async () => {
      // Arrange
      const mockDeployments = [
        {
          uid: 'dpl_test123',
          name: 'excel-copilot',
          url: 'https://excel-copilot.vercel.app',
          state: 'READY' as const,
          type: 'LAMBDAS' as const,
          created: *************,
          ready: *************,
          target: 'production' as const,
        },
      ];

      mockClient.getDeployments.mockResolvedValue(mockDeployments);
      mockClient.checkRecentErrors.mockResolvedValue({
        hasErrors: true,
        errorCount: 5,
        errors: [],
      });

      // Act
      const result = await service.getProjectStatus();

      // Assert
      expect(result.status).toBe('degraded');
      expect(result.recentErrors).toBe(5);
    });

    it('deve lidar com falhas na API do Vercel', async () => {
      // Arrange
      mockClient.getDeployments.mockRejectedValue(new Error('API Error'));

      // Act
      const result = await service.getProjectStatus();

      // Assert
      expect(result.status).toBe('down');
      expect(result.message).toBe('Erro ao conectar com Vercel API');
    });
  });

  describe('getFilteredLogs', () => {
    it('deve obter logs de deployments recentes', async () => {
      // Arrange
      const mockDeployments = [
        {
          uid: 'dpl_recent1',
          name: 'excel-copilot',
          url: 'https://excel-copilot.vercel.app',
          state: 'READY' as const,
          type: 'LAMBDAS' as const,
          created: Date.now() - 3600000, // 1 hora atrás
          // Omitir buildingAt para compatibilidade com exactOptionalPropertyTypes
          ready: Date.now() - 3500000,
          target: 'production' as const,
        },
        {
          uid: 'dpl_recent2',
          name: 'excel-copilot',
          url: 'https://excel-copilot-preview.vercel.app',
          state: 'READY' as const,
          type: 'LAMBDAS' as const,
          created: Date.now() - 7200000, // 2 horas atrás
          // Omitir buildingAt para compatibilidade com exactOptionalPropertyTypes
          ready: Date.now() - 7100000,
          target: 'staging' as const, // Usar 'staging' em vez de 'preview'
        },
      ];

      const mockLogs1 = [
        {
          timestamp: Date.now() - 1800000,
          message: 'Request processed',
          level: 'info' as const,
          source: 'lambda' as const,
          requestId: 'req_1',
          deploymentId: 'dpl_recent1',
        },
      ];

      const mockLogs2 = [
        {
          timestamp: Date.now() - 3600000,
          message: 'Preview deployment ready',
          level: 'info' as const,
          source: 'lambda' as const,
          requestId: 'req_2',
          deploymentId: 'dpl_recent2',
        },
      ];

      mockClient.getDeployments.mockResolvedValue(mockDeployments);
      mockClient.getDeploymentLogs
        .mockResolvedValueOnce(mockLogs1)
        .mockResolvedValueOnce(mockLogs2);

      // Act
      const result = await service.getFilteredLogs({ limit: 10 });

      // Assert
      expect(result).toHaveLength(2);
      expect(result[0]?.message).toBe('Request processed');
      expect(result[1]?.message).toBe('Preview deployment ready');
    });

    it('deve filtrar logs por nível', async () => {
      // Arrange
      const mockDeployments = [
        {
          uid: 'dpl_test',
          name: 'excel-copilot',
          url: 'https://excel-copilot.vercel.app',
          state: 'READY' as const,
          type: 'LAMBDAS' as const,
          created: Date.now() - 3600000,
          ready: Date.now() - 3500000,
          target: 'production' as const,
        },
      ];

      const mockLogs = [
        {
          timestamp: Date.now() - 1800000,
          message: 'Info message',
          level: 'info' as const,
          source: 'lambda' as const,
          requestId: 'req_1',
          deploymentId: 'dpl_test',
        },
        {
          timestamp: Date.now() - 1700000,
          message: 'Error message',
          level: 'error' as const,
          source: 'lambda' as const,
          requestId: 'req_2',
          deploymentId: 'dpl_test',
        },
      ];

      mockClient.getDeployments.mockResolvedValue(mockDeployments);
      mockClient.getDeploymentLogs.mockResolvedValue(mockLogs);

      // Act
      const result = await service.getFilteredLogs({ level: 'error' });

      // Assert
      expect(result).toHaveLength(1);
      expect(result[0]?.level).toBe('error');
    });

    it('deve lidar com deployments sem logs', async () => {
      // Arrange
      const mockDeployments = [
        {
          uid: 'dpl_empty',
          name: 'excel-copilot',
          url: 'https://excel-copilot.vercel.app',
          state: 'READY' as const,
          type: 'LAMBDAS' as const,
          created: Date.now() - 3600000,
          ready: Date.now() - 3500000,
          target: 'production' as const,
        },
      ];

      mockClient.getDeployments.mockResolvedValue(mockDeployments);
      mockClient.getDeploymentLogs.mockResolvedValue([]);

      // Act
      const result = await service.getFilteredLogs();

      // Assert
      expect(result).toHaveLength(0);
    });
  });

  describe('error handling', () => {
    it('deve lidar com erros de deployment logs', async () => {
      // Arrange
      const mockDeployments = [
        {
          uid: 'dpl_error',
          name: 'excel-copilot',
          url: 'https://excel-copilot.vercel.app',
          state: 'READY' as const,
          type: 'LAMBDAS' as const,
          created: Date.now() - 3600000,
          ready: Date.now() - 3500000,
          target: 'production' as const,
        },
      ];

      mockClient.getDeployments.mockResolvedValue(mockDeployments);
      mockClient.getDeploymentLogs.mockRejectedValue(new Error('Logs Error'));

      // Act
      const result = await service.getFilteredLogs();

      // Assert
      expect(result).toHaveLength(0);
    });
  });
});
