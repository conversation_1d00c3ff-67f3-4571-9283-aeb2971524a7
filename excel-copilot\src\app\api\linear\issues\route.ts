/**
 * API endpoint para gerenciamento de issues do Linear
 * GET /api/linear/issues - Lista issues
 * POST /api/linear/issues - Cria nova issue
 */

import { NextRequest, NextResponse } from 'next/server';

import { LinearClient, LinearMonitoringService } from '@/lib/linear-integration';
import { logger } from '@/lib/logger';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);

    // Parâmetros de filtro
    const teamId = searchParams.get('teamId');
    const assigneeId = searchParams.get('assigneeId');
    const state = searchParams.get('state');
    const limit = parseInt(searchParams.get('limit') || '50');
    const includeArchived = searchParams.get('includeArchived') === 'true';
    const excelCopilotOnly = searchParams.get('excelCopilotOnly') === 'true';

    const linearClient = new LinearClient();

    if (excelCopilotOnly) {
      // Usa o serviço de monitoramento para filtrar issues do Excel Copilot
      const linearService = new LinearMonitoringService();
      const excelCopilotIssues = await linearService.getExcelCopilotIssues();

      return NextResponse.json({
        status: 'success',
        issues: excelCopilotIssues.issues,
        summary: excelCopilotIssues.summary,
        filters: {
          excelCopilotOnly: true,
          total: excelCopilotIssues.issues.length,
        },
        timestamp: new Date().toISOString(),
      });
    }

    // Lista issues com filtros
    const issues = await linearClient.getIssues({
      ...(teamId && { teamId }),
      ...(assigneeId && { assigneeId }),
      ...(state && { state }),
      limit,
      includeArchived,
    });

    // Estatísticas das issues retornadas
    const summary = {
      total: issues.issues.length,
      byState: issues.issues.reduce(
        (acc, issue) => {
          const stateName = issue.state.name;
          acc[stateName] = (acc[stateName] || 0) + 1;
          return acc;
        },
        {} as Record<string, number>
      ),
      byTeam: issues.issues.reduce(
        (acc, issue) => {
          const teamName = issue.team.name;
          acc[teamName] = (acc[teamName] || 0) + 1;
          return acc;
        },
        {} as Record<string, number>
      ),
    };

    return NextResponse.json({
      status: 'success',
      issues: issues.issues,
      summary,
      filters: {
        teamId,
        assigneeId,
        state,
        limit,
        includeArchived,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error('Erro ao listar issues Linear:', error);

    return NextResponse.json(
      {
        status: 'error',
        message: error instanceof Error ? error.message : 'Erro interno do servidor',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { teamId, title, description, assigneeId, stateId, priority, labelIds, type } = body;

    // Validação básica
    if (!teamId || !title) {
      return NextResponse.json({ error: 'teamId e title são obrigatórios' }, { status: 400 });
    }

    const linearClient = new LinearClient();

    // Se for uma issue do Excel Copilot, usa o serviço especializado
    if (type && ['bug', 'feature', 'improvement'].includes(type)) {
      const linearService = new LinearMonitoringService();

      const newIssue = await linearService.createExcelCopilotIssue({
        type: type as 'bug' | 'feature' | 'improvement',
        title,
        description: description || '',
        priority,
        assigneeId,
      });

      return NextResponse.json({
        status: 'success',
        action: 'excel_copilot_issue_created',
        issue: newIssue.issue,
        timestamp: new Date().toISOString(),
      });
    }

    // Criação de issue padrão
    const newIssue = await linearClient.createIssue({
      teamId,
      title,
      description,
      assigneeId,
      stateId,
      priority,
      labelIds,
    });

    return NextResponse.json({
      status: 'success',
      action: 'issue_created',
      issue: newIssue.issue,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error('Erro ao criar issue Linear:', error);

    return NextResponse.json(
      {
        status: 'error',
        message: error instanceof Error ? error.message : 'Erro interno do servidor',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

export async function PATCH(request: NextRequest) {
  try {
    const body = await request.json();
    const { issueId, ...updateData } = body;

    if (!issueId) {
      return NextResponse.json({ error: 'issueId é obrigatório' }, { status: 400 });
    }

    const linearClient = new LinearClient();
    const updatedIssue = await linearClient.updateIssue(issueId, updateData);

    return NextResponse.json({
      status: 'success',
      action: 'issue_updated',
      issue: updatedIssue.issue,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error('Erro ao atualizar issue Linear:', error);

    return NextResponse.json(
      {
        status: 'error',
        message: error instanceof Error ? error.message : 'Erro interno do servidor',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
