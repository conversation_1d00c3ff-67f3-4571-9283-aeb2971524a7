import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import type { Session } from 'next-auth';

import { authOptions } from '@/server/auth/options';

export const dynamic = 'force-dynamic';

/**
 * Endpoint para debug detalhado do fluxo OAuth
 * Captura informações sobre o estado atual da sessão e configurações
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const debugType = searchParams.get('type') || 'full';

    // Obter informações da sessão atual
    let sessionInfo = null;
    try {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const session = (await getServerSession(authOptions as any)) as Session | null;
      sessionInfo = {
        exists: !!session,
        user: session?.user
          ? {
              // eslint-disable-next-line @typescript-eslint/no-explicit-any
              id: (session.user as any)?.id || 'N/A',
              email: session.user.email || 'N/A',
              name: session.user.name || 'N/A',
            }
          : null,
        expires: session?.expires || 'N/A',
      };
    } catch (sessionError) {
      sessionInfo = {
        error: sessionError instanceof Error ? sessionError.message : 'Erro desconhecido',
        exists: false,
      };
    }

    // Informações sobre headers e cookies
    const headers = Object.fromEntries(request.headers.entries());
    const cookies = request.headers.get('cookie') || '';

    // Informações sobre a URL atual
    const urlInfo = {
      origin: request.nextUrl.origin,
      pathname: request.nextUrl.pathname,
      searchParams: Object.fromEntries(request.nextUrl.searchParams.entries()),
      host: request.nextUrl.host,
      protocol: request.nextUrl.protocol,
    };

    // Verificar configurações OAuth específicas
    const oauthConfig = {
      googleClientId: process.env.AUTH_GOOGLE_CLIENT_ID ? 'Configurado' : 'Ausente',
      googleClientSecret: process.env.AUTH_GOOGLE_CLIENT_SECRET ? 'Configurado' : 'Ausente',
      githubClientId: process.env.AUTH_GITHUB_CLIENT_ID ? 'Configurado' : 'Ausente',
      githubClientSecret: process.env.AUTH_GITHUB_CLIENT_SECRET ? 'Configurado' : 'Ausente',
      nextAuthUrl: process.env.AUTH_NEXTAUTH_URL || 'Ausente',
      nextAuthSecret: process.env.AUTH_NEXTAUTH_SECRET ? 'Configurado' : 'Ausente',
      nodeEnv: process.env.NODE_ENV || 'undefined',
    };

    // URLs de callback esperadas
    const expectedCallbacks = {
      google: `${process.env.AUTH_NEXTAUTH_URL}/api/auth/callback/google`,
      github: `${process.env.AUTH_NEXTAUTH_URL}/api/auth/callback/github`,
    };

    // Verificar se há erros nos parâmetros da URL
    const urlErrors = {
      error: searchParams.get('error'),
      errorDescription: searchParams.get('error_description'),
      state: searchParams.get('state'),
      code: searchParams.get('code'),
    };

    // Informações sobre o User-Agent
    const userAgent = headers['user-agent'] || 'Desconhecido';
    const isBot = /bot|crawler|spider/i.test(userAgent);

    // Verificar se há problemas com CSRF
    const csrfToken = cookies.includes('next-auth.csrf-token');
    const sessionToken = cookies.includes('next-auth.session-token');

    const debugInfo = {
      timestamp: new Date().toISOString(),
      debugType,
      session: sessionInfo,
      oauth: oauthConfig,
      callbacks: expectedCallbacks,
      url: urlInfo,
      errors: urlErrors,
      security: {
        csrfToken: csrfToken ? 'Presente' : 'Ausente',
        sessionToken: sessionToken ? 'Presente' : 'Ausente',
        isBot,
        userAgent: userAgent.substring(0, 100), // Limitar tamanho
      },
      headers:
        debugType === 'full'
          ? headers
          : {
              'user-agent': headers['user-agent'],
              referer: headers['referer'],
              origin: headers['origin'],
              host: headers['host'],
            },
      recommendations: [] as string[],
    };

    // Gerar recomendações baseadas no estado atual
    if (!sessionInfo?.exists) {
      debugInfo.recommendations.push('Usuário não está autenticado');
    }

    if (urlErrors.error) {
      debugInfo.recommendations.push(`Erro OAuth detectado: ${urlErrors.error}`);
      if (urlErrors.errorDescription) {
        debugInfo.recommendations.push(`Descrição: ${urlErrors.errorDescription}`);
      }
    }

    if (!csrfToken) {
      debugInfo.recommendations.push('Token CSRF ausente - possível problema de cookies');
    }

    if (isBot) {
      debugInfo.recommendations.push('Requisição detectada como bot - OAuth pode não funcionar');
    }

    // Verificar se a URL de callback está correta
    if (urlInfo.pathname.includes('/api/auth/callback/')) {
      const provider = urlInfo.pathname.split('/').pop();
      debugInfo.recommendations.push(`Callback detectado para provedor: ${provider}`);

      if (urlErrors.code) {
        debugInfo.recommendations.push('Código de autorização recebido - fluxo OAuth em andamento');
      }
    }

    return NextResponse.json(debugInfo, {
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        Pragma: 'no-cache',
        Expires: '0',
      },
    });
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('Erro no debug do fluxo OAuth:', error);

    return NextResponse.json(
      {
        error: 'Erro ao executar debug do fluxo OAuth',
        message: error instanceof Error ? error.message : 'Erro desconhecido',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

/**
 * Endpoint POST para capturar dados de debug enviados pelo frontend
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // eslint-disable-next-line no-console
    console.log('🔍 [DEBUG-FLOW] Dados recebidos do frontend:', {
      timestamp: new Date().toISOString(),
      url: request.url,
      body,
    });

    return NextResponse.json({
      status: 'success',
      message: 'Dados de debug recebidos',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('Erro ao processar dados de debug:', error);

    return NextResponse.json(
      {
        error: 'Erro ao processar dados de debug',
        message: error instanceof Error ? error.message : 'Erro desconhecido',
      },
      { status: 500 }
    );
  }
}
