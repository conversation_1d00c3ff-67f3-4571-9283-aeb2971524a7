import { NextRequest } from 'next/server';

import { createExcelAIProcessor, ExcelAIProcessor } from '@/lib/ai';
import { executeExcelOperations, parseAICommandToExcelOperations } from '@/lib/excel';
import { logger } from '@/lib/logger';
import { ApiResponse } from '@/utils/api-response';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { command, currentData, contextData } = body;

    if (!command || !currentData) {
      return ApiResponse.error(
        'Comando e dados atuais são obrigatórios',
        'MISSING_REQUIRED_FIELDS',
        400
      );
    }

    // Tentar processamento por novo processor
    try {
      // Criar instância do processador com contexto da planilha atual
      const _aiProcessor = createExcelAIProcessor();

      // Preparar os dados de contexto para o processador
      const defaultContextData = {
        activeSheet: currentData.name || 'Sheet1',
        headers: currentData.headers || [],
        selection: `A1:${String.fromCharCode(65 + (currentData.headers?.length || 1) - 1)}${currentData.rows?.length || 1}`,
        recentOperations: [],
        ...contextData,
      };

      // Criar processador contextualizado
      const contextualizedProcessor = new ExcelAIProcessor(defaultContextData);

      // Processar o comando usando o AI Processor
      const parserResult = await contextualizedProcessor.processQuery(command);

      // Verificar se temos operações a executar
      if (parserResult.operations && parserResult.operations.length > 0 && !parserResult.error) {
        // Executar operações extraídas
        const excelOpResult = await executeExcelOperations(currentData, parserResult.operations);

        return ApiResponse.success({
          success: true,
          updatedData: excelOpResult.updatedData,
          resultSummary: Array.isArray(excelOpResult.resultSummary)
            ? excelOpResult.resultSummary
            : [String(excelOpResult.resultSummary)],
          modifiedCells: excelOpResult.modifiedCells,
          operations: parserResult.operations,
          processorUsed: 'new',
        });
      }
    } catch (err) {
      logger.warn('Erro no novo processor, tentando fallback:', err);
    }

    // Fallback para o método antigo se o novo falhar
    try {
      const legacyParserResult = await parseAICommandToExcelOperations(command);

      if (legacyParserResult.success && legacyParserResult.operations.length > 0) {
        // Executar operações extraídas
        const excelOpResult = await executeExcelOperations(
          currentData,
          legacyParserResult.operations
        );

        return ApiResponse.success({
          success: true,
          updatedData: excelOpResult.updatedData,
          resultSummary: Array.isArray(excelOpResult.resultSummary)
            ? excelOpResult.resultSummary
            : [String(excelOpResult.resultSummary)],
          modifiedCells: excelOpResult.modifiedCells,
          operations: legacyParserResult.operations,
          processorUsed: 'legacy',
        });
      } else {
        // Resposta sem operações reconhecidas
        return ApiResponse.success({
          success: false,
          message:
            legacyParserResult.message || 'Não foi possível extrair operações Excel deste comando.',
          operations: [],
          processorUsed: 'legacy',
        });
      }
    } catch (error) {
      logger.error('Erro no parser de comandos:', error);

      return ApiResponse.error('Erro ao processar comando Excel', 'PROCESSING_ERROR', 500, {
        error: error instanceof Error ? error.message : 'Erro desconhecido',
      });
    }

    // Retorno padrão caso nenhum processor consiga processar o comando
    return ApiResponse.success({
      success: false,
      message:
        'Não foi possível processar o comando. Tente reformular ou use um exemplo da lista de sugestões.',
      operations: [],
      processorUsed: 'none',
    });
  } catch (error) {
    logger.error('Erro na API de processamento Excel:', error);

    return ApiResponse.error('Erro interno do servidor', 'INTERNAL_SERVER_ERROR', 500, {
      error: error instanceof Error ? error.message : 'Erro desconhecido',
    });
  }
}
