#!/usr/bin/env node

/**
 * Script para testar a configuração do Vertex AI
 * Este script verifica se as credenciais e configurações estão corretas
 */

const fs = require('fs');
const path = require('path');

// Cores para output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m',
  bold: '\x1b[1m',
};

console.log(`${colors.blue}${colors.bold}🧪 Teste de Configuração do Vertex AI${colors.reset}\n`);

// Verificar se estamos na raiz do projeto
const packageJsonPath = path.join(process.cwd(), 'package.json');
if (!fs.existsSync(packageJsonPath)) {
  console.error(
    `${colors.red}❌ Execute este script na raiz do projeto Excel Copilot${colors.reset}`
  );
  process.exit(1);
}

// Carregar variáveis de ambiente
require('dotenv').config({ path: '.env.local' });

let hasErrors = false;

console.log(`${colors.cyan}📋 Verificando configuração do ambiente...${colors.reset}`);

// 1. Verificar variáveis de ambiente básicas
const requiredEnvVars = {
  VERTEX_AI_ENABLED: process.env.AI_ENABLED,
  VERTEX_AI_PROJECT_ID: process.env.AI_VERTEX_PROJECT_ID,
  VERTEX_AI_LOCATION: process.env.AI_VERTEX_LOCATION,
  VERTEX_AI_MODEL_NAME: process.env.AI_VERTEX_MODEL,
};

console.log(`\n${colors.blue}🔧 Variáveis de ambiente:${colors.reset}`);
for (const [key, value] of Object.entries(requiredEnvVars)) {
  if (value) {
    console.log(`${colors.green}✅ ${key}: ${value}${colors.reset}`);
  } else {
    console.log(`${colors.red}❌ ${key}: não definida${colors.reset}`);
    hasErrors = true;
  }
}

// 2. Verificar credenciais
console.log(`\n${colors.blue}🔐 Credenciais:${colors.reset}`);

const hasVertexCredentials = !!process.env.VERTEX_AI_CREDENTIALS;
const hasGoogleCredentials = !!process.env.GOOGLE_APPLICATION_CREDENTIALS;

if (hasVertexCredentials) {
  console.log(`${colors.green}✅ VERTEX_AI_CREDENTIALS: presente${colors.reset}`);

  // Tentar parsear as credenciais
  try {
    const credentials = JSON.parse(process.env.VERTEX_AI_CREDENTIALS);
    console.log(`${colors.green}✅ Credenciais JSON válidas${colors.reset}`);
    console.log(
      `${colors.cyan}   Project ID: ${credentials.project_id || 'não encontrado'}${colors.reset}`
    );
    console.log(
      `${colors.cyan}   Client Email: ${credentials.client_email || 'não encontrado'}${colors.reset}`
    );
    console.log(`${colors.cyan}   Type: ${credentials.type || 'não encontrado'}${colors.reset}`);
  } catch (error) {
    console.log(
      `${colors.red}❌ Erro ao parsear VERTEX_AI_CREDENTIALS: ${error.message}${colors.reset}`
    );
    hasErrors = true;
  }
} else if (hasGoogleCredentials) {
  console.log(
    `${colors.green}✅ GOOGLE_APPLICATION_CREDENTIALS: ${process.env.GOOGLE_APPLICATION_CREDENTIALS}${colors.reset}`
  );
} else {
  console.log(`${colors.red}❌ Nenhuma credencial encontrada${colors.reset}`);
  hasErrors = true;
}

// 3. Verificar arquivo de credenciais local
console.log(`\n${colors.blue}📁 Arquivos de credenciais:${colors.reset}`);

const credentialFiles = ['vertex-credentials.json', '.vertex-credentials-temp.json'];

for (const file of credentialFiles) {
  const filePath = path.join(process.cwd(), file);
  if (fs.existsSync(filePath)) {
    console.log(`${colors.green}✅ ${file}: encontrado${colors.reset}`);
  } else {
    console.log(`${colors.yellow}⚠️  ${file}: não encontrado${colors.reset}`);
  }
}

// 4. Testar inicialização do serviço (apenas se não houver erros críticos)
if (!hasErrors && process.env.AI_ENABLED === 'true') {
  console.log(`\n${colors.blue}🚀 Testando inicialização do Vertex AI...${colors.reset}`);

  try {
    // Simular a lógica de inicialização
    const { VertexAI } = require('@google-cloud/vertexai');

    // Configurar credenciais se necessário
    if (process.env.VERTEX_AI_CREDENTIALS && !process.env.GOOGLE_APPLICATION_CREDENTIALS) {
      const credentials = JSON.parse(process.env.VERTEX_AI_CREDENTIALS);
      const tempPath = path.join(process.cwd(), '.vertex-credentials-temp.json');
      fs.writeFileSync(tempPath, JSON.stringify(credentials, null, 2));
      process.env.GOOGLE_APPLICATION_CREDENTIALS = tempPath;
      console.log(`${colors.cyan}📝 Arquivo temporário de credenciais criado${colors.reset}`);
    }

    const vertexAI = new VertexAI({
      project: process.env.AI_VERTEX_PROJECT_ID,
      location: process.env.AI_VERTEX_LOCATION || 'us-central1',
    });

    console.log(`${colors.green}✅ Cliente Vertex AI inicializado com sucesso${colors.reset}`);

    // Tentar obter o modelo
    const model = vertexAI.preview.getGenerativeModel({
      model: process.env.AI_VERTEX_MODEL || 'gemini-1.5-pro',
    });

    console.log(`${colors.green}✅ Modelo Gemini carregado com sucesso${colors.reset}`);
  } catch (error) {
    console.log(`${colors.red}❌ Erro ao inicializar Vertex AI: ${error.message}${colors.reset}`);
    hasErrors = true;
  }
}

// 5. Resumo final
console.log(`\n${colors.bold}📊 Resumo:${colors.reset}`);

if (hasErrors) {
  console.log(
    `${colors.red}❌ Configuração do Vertex AI tem problemas que precisam ser corrigidos${colors.reset}`
  );
  console.log(`\n${colors.yellow}💡 Próximos passos:${colors.reset}`);
  console.log(`1. Verifique se todas as variáveis de ambiente estão definidas na Vercel`);
  console.log(`2. Confirme se as credenciais JSON estão válidas`);
  console.log(`3. Teste o endpoint de health check: /api/health/ai`);
  process.exit(1);
} else {
  console.log(`${colors.green}✅ Configuração do Vertex AI está correta!${colors.reset}`);
  console.log(`\n${colors.cyan}🎉 O serviço de IA deve funcionar corretamente${colors.reset}`);
}
