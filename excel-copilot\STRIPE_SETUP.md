# Configuração do Stripe para Excel Copilot

Este documento detalha como configurar o Stripe para processar pagamentos de assinatura no Excel Copilot.

## Pré-requisitos

1. Uma conta no [Stripe](https://stripe.com)
2. Chaves de API do Stripe (teste ou produção)

## Etapas de Configuração

### 1. Criar Conta e Obter Chaves API

1. Crie uma conta em [stripe.com](https://stripe.com) se ainda não tiver uma
2. No painel do Stripe, vá para **Desenvolvedores > Chaves de API**
3. Anote as seguintes chaves:
   - **Chave publicável** (`pk_test_...` ou `pk_live_...`)
   - **Chave secreta** (`sk_test_...` ou `sk_live_...`)

### 2. Configurar Produtos e Preços

1. No painel do Stripe, vá para **Produtos > Adicionar produto**
2. Crie os produtos correspondentes aos planos:

#### Plano Pro Mensal

- **Nome**: Excel Copilot Pro Mensal
- **Descrição**: Acesso a recursos avançados do Excel Copilot
- **Preço**: R$20,00/mês
- **Tipo de faturamento**: Recorrente (mensal)
- **ID**: `pro_monthly` (ou qualquer ID personalizado)
- Anote o ID do preço (`price_...`)

#### Plano Pro Anual

- **Nome**: Excel Copilot Pro Anual
- **Descrição**: Acesso a recursos avançados do Excel Copilot com desconto anual
- **Preço**: R$200,00/ano (equivalente a R$16,67/mês)
- **Tipo de faturamento**: Recorrente (anual)
- **ID**: `pro_annual` (ou qualquer ID personalizado)
- Anote o ID do preço (`price_...`)

### 3. Configurar Webhook

1. No painel do Stripe, vá para **Desenvolvedores > Webhooks**
2. Clique em **Adicionar endpoint**
3. URL do endpoint: `https://seu-dominio.com/api/webhooks/stripe`
4. Selecione os eventos para escutar:
   - `checkout.session.completed`
   - `invoice.paid`
   - `customer.subscription.updated`
   - `customer.subscription.deleted`
5. Clique em **Adicionar endpoint**
6. Anote o **Segredo de assinatura do webhook** (`whsec_...`)

### 4. Configurar Variáveis de Ambiente

Adicione as seguintes variáveis em seu arquivo `.env.local`:

```
STRIPE_SECRET_KEY="sk_test_your_test_key"
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY="pk_test_your_test_key"
STRIPE_WEBHOOK_SECRET="whsec_your_webhook_secret"
NEXT_PUBLIC_STRIPE_PRICE_MONTHLY="price_id_do_plano_mensal"
NEXT_PUBLIC_STRIPE_PRICE_ANNUAL="price_id_do_plano_anual"
```

### 5. Testar Integração

1. Inicie a aplicação: `npm run dev`
2. Acesse a página de preços: `/pricing`
3. Inicie um checkout do plano Pro Mensal
4. Use os dados de teste do Stripe para pagamento:

   - Cartão: `4242 4242 4242 4242`
   - Data: Qualquer data futura
   - CVV: Qualquer 3 dígitos

5. Para testar eventos de webhook localmente, use o Stripe CLI:
   ```
   stripe listen --forward-to localhost:3000/api/webhooks/stripe
   ```

## Monitoramento e Depuração

### Logs do Stripe

Para depurar problemas de pagamento:

1. Acesse o painel do Stripe
2. Vá para **Desenvolvedores > Logs**
3. Procure por eventos relacionados a `checkout.session` ou `subscription`

### Logs do Webhook

Para verificar se os webhooks estão funcionando:

1. Acesse o painel do Stripe
2. Vá para **Desenvolvedores > Webhooks**
3. Selecione seu endpoint
4. Examine eventos recentes e respostas

## Implementações Adicionais

### Portal de Gerenciamento do Cliente

O Excel Copilot implementa o Portal de Clientes do Stripe, que permite aos usuários:

- Atualizar método de pagamento
- Visualizar faturas
- Cancelar assinaturas

O portal é acessado em `/dashboard/account`.

### Uso de API e Cobrança por Uso Excedente

O sistema monitora o uso de chamadas à API e:

1. Limita usuários do plano Free quando excedem o limite
2. Para planos Pro, permite uso excedente e gera faturamento adicional
3. Atualiza contadores de uso em tempo real

## Migração para Produção

Ao migrar para produção, lembre-se de:

1. Substituir as chaves de teste pelas chaves de produção do Stripe
2. Atualizar a URL do webhook para o domínio de produção
3. Testar o fluxo completo de pagamento em um ambiente de staging

## Resolução de Problemas

### Webhook não recebido

- Verifique se a URL do webhook está correta
- Verifique se a assinatura do webhook está configurada corretamente
- Use o Stripe CLI para depurar

### Erros de Pagamento

- Verifique os logs do Stripe para detalhes do erro
- Verifique se as chaves API estão configuradas corretamente
- Confirme se os IDs de preço estão corretos em suas variáveis de ambiente

### Assinatura não Atualizada

- Verifique se o webhook para `checkout.session.completed` está sendo recebido
- Verifique se o usuário tem um ID vinculado corretamente nos metadados do Stripe
- Verifique os logs do servidor para erros na atualização do banco de dados
