/**
 * Middleware de rate limiting baseado no plano do usuário
 * Implementa limitações específicas por plano de assinatura
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';

import { logger } from '@/lib/logger';
import { PLANS } from '@/lib/stripe';
import { prisma } from '@/server/db/client';

// Cache de rate limiting em memória (em produção, usar Redis)
const rateLimitCache = new Map<string, { count: number; resetTime: number }>();

// Configurações de rate limiting por plano
const RATE_LIMIT_CONFIG = {
  [PLANS.FREE]: {
    requestsPerMinute: 30,
    requestsPerHour: 300,
    requestsPerDay: 1000,
  },
  [PLANS.PRO_MONTHLY]: {
    requestsPerMinute: 120,
    requestsPerHour: 2000,
    requestsPerDay: 10000,
  },
  [PLANS.PRO_ANNUAL]: {
    requestsPerMinute: 240,
    requestsPerHour: 5000,
    requestsPerDay: 25000,
  },
};

interface RateLimitResult {
  allowed: boolean;
  limit: number;
  remaining: number;
  resetTime: number;
  retryAfter?: number;
}

/**
 * Verifica rate limit para um usuário específico
 */
export async function checkRateLimit(
  userId: string,
  plan: string,
  timeWindow: 'minute' | 'hour' | 'day' = 'minute'
): Promise<RateLimitResult> {
  const now = Date.now();
  const windowMs = getWindowMs(timeWindow);
  const key = `${userId}:${timeWindow}`;

  // Obter configuração do plano
  const config = RATE_LIMIT_CONFIG[plan] || RATE_LIMIT_CONFIG[PLANS.FREE];
  const limit = getLimit(config, timeWindow);

  // Verificar cache
  const cached = rateLimitCache.get(key);
  const resetTime = Math.floor(now / windowMs) * windowMs + windowMs;

  if (!cached || cached.resetTime <= now) {
    // Primeira requisição ou janela expirada
    rateLimitCache.set(key, { count: 1, resetTime });

    return {
      allowed: true,
      limit,
      remaining: limit - 1,
      resetTime,
    };
  }

  if (cached.count >= limit) {
    // Limite excedido
    const retryAfter = Math.ceil((cached.resetTime - now) / 1000);

    logger.warn(`[RATE_LIMIT_EXCEEDED] Usuário ${userId} excedeu limite do plano ${plan}:`, {
      timeWindow,
      limit,
      count: cached.count,
      retryAfter,
    });

    return {
      allowed: false,
      limit,
      remaining: 0,
      resetTime: cached.resetTime,
      retryAfter,
    };
  }

  // Incrementar contador
  cached.count++;
  rateLimitCache.set(key, cached);

  return {
    allowed: true,
    limit,
    remaining: limit - cached.count,
    resetTime: cached.resetTime,
  };
}

/**
 * Obtém o plano atual do usuário de forma otimizada para middleware
 */
async function getUserPlanForMiddleware(userId: string): Promise<string> {
  try {
    // Cache simples para middleware (evitar múltiplas consultas)
    const cacheKey = `middleware_plan_${userId}`;
    const cached = rateLimitCache.get(cacheKey);
    const now = Date.now();

    // Cache por 5 minutos para middleware (mais frequente que o cache principal)
    if (cached && now - cached.resetTime < 5 * 60 * 1000) {
      return cached.count as any; // Reutilizando estrutura do cache
    }

    // Buscar assinatura ativa do usuário
    const subscription = await prisma.subscription.findFirst({
      where: {
        userId,
        OR: [{ status: 'active' }, { status: 'trialing' }],
        AND: [
          {
            OR: [{ currentPeriodEnd: { gt: new Date() } }, { currentPeriodEnd: null }],
          },
        ],
      },
      orderBy: { createdAt: 'desc' },
      select: { plan: true }, // Otimização: buscar apenas o campo necessário
    });

    const plan = subscription?.plan || PLANS.FREE;

    // Atualizar cache
    rateLimitCache.set(cacheKey, {
      count: plan as any,
      resetTime: now + 5 * 60 * 1000,
    });

    return plan;
  } catch (error) {
    logger.error('[MIDDLEWARE_GET_PLAN_ERROR]', { userId, error });
    // Em caso de erro, usar plano FREE como fallback seguro
    return PLANS.FREE;
  }
}

/**
 * Middleware de rate limiting baseado no plano
 */
export async function planBasedRateLimiter(
  request: NextRequest,
  timeWindow: 'minute' | 'hour' | 'day' = 'minute'
): Promise<NextResponse | null> {
  try {
    // Verificar autenticação
    const session = await getServerSession();
    if (!session?.user) {
      // Para usuários não autenticados, aplicar limite mais restritivo
      return await applyAnonymousRateLimit(request, timeWindow);
    }

    const userId = (session.user as any).id;

    // CORREÇÃO CRÍTICA: Obter plano real do usuário
    const userPlan = await getUserPlanForMiddleware(userId);

    logger.info('[PLAN_RATE_LIMIT]', {
      userId,
      userPlan,
      path: request.nextUrl.pathname,
      method: request.method,
    });

    // Verificar rate limit
    const rateLimitResult = await checkRateLimit(userId, userPlan, timeWindow);

    if (!rateLimitResult.allowed) {
      // Limite excedido - retornar erro 429
      const response = NextResponse.json(
        {
          error: 'Rate limit exceeded',
          message: `Limite de requisições excedido para o plano ${userPlan}. Tente novamente em ${rateLimitResult.retryAfter} segundos.`,
          plan: userPlan,
          limit: rateLimitResult.limit,
          retryAfter: rateLimitResult.retryAfter,
        },
        { status: 429 }
      );

      // Adicionar headers de rate limiting
      response.headers.set('X-RateLimit-Limit', rateLimitResult.limit.toString());
      response.headers.set('X-RateLimit-Remaining', '0');
      response.headers.set('X-RateLimit-Reset', rateLimitResult.resetTime.toString());
      response.headers.set('Retry-After', rateLimitResult.retryAfter?.toString() || '60');

      return response;
    }

    // Rate limit OK - adicionar headers informativos
    const response = NextResponse.next();
    response.headers.set('X-RateLimit-Limit', rateLimitResult.limit.toString());
    response.headers.set('X-RateLimit-Remaining', rateLimitResult.remaining.toString());
    response.headers.set('X-RateLimit-Reset', rateLimitResult.resetTime.toString());
    response.headers.set('X-User-Plan', userPlan);

    return null; // Continuar processamento
  } catch (error) {
    logger.error('[RATE_LIMITER_ERROR]', error);

    // Em caso de erro, permitir requisição mas logar
    logger.warn('[RATE_LIMITER_FALLBACK] Permitindo requisição devido a erro no rate limiter');
    return null;
  }
}

/**
 * Rate limiting para usuários anônimos (mais restritivo)
 */
async function applyAnonymousRateLimit(
  request: NextRequest,
  timeWindow: 'minute' | 'hour' | 'day'
): Promise<NextResponse | null> {
  const ip = request.ip || request.headers.get('x-forwarded-for') || 'unknown';
  const key = `anonymous:${ip}:${timeWindow}`;

  // Limites mais restritivos para usuários anônimos
  const anonymousLimits = {
    minute: 10,
    hour: 100,
    day: 500,
  };

  const limit = anonymousLimits[timeWindow];
  const now = Date.now();
  const windowMs = getWindowMs(timeWindow);
  const resetTime = Math.floor(now / windowMs) * windowMs + windowMs;

  const cached = rateLimitCache.get(key);

  if (!cached || cached.resetTime <= now) {
    rateLimitCache.set(key, { count: 1, resetTime });
    return null;
  }

  if (cached.count >= limit) {
    const retryAfter = Math.ceil((cached.resetTime - now) / 1000);

    logger.warn(`[ANONYMOUS_RATE_LIMIT_EXCEEDED] IP ${ip} excedeu limite anônimo:`, {
      timeWindow,
      limit,
      count: cached.count,
    });

    return NextResponse.json(
      {
        error: 'Rate limit exceeded',
        message:
          'Muitas requisições. Faça login para limites maiores ou tente novamente mais tarde.',
        retryAfter,
      },
      { status: 429 }
    );
  }

  cached.count++;
  rateLimitCache.set(key, cached);

  return null;
}

/**
 * Utilitários
 */
function getWindowMs(timeWindow: 'minute' | 'hour' | 'day'): number {
  switch (timeWindow) {
    case 'minute':
      return 60 * 1000;
    case 'hour':
      return 60 * 60 * 1000;
    case 'day':
      return 24 * 60 * 60 * 1000;
    default:
      return 60 * 1000;
  }
}

function getLimit(config: any, timeWindow: 'minute' | 'hour' | 'day'): number {
  switch (timeWindow) {
    case 'minute':
      return config.requestsPerMinute;
    case 'hour':
      return config.requestsPerHour;
    case 'day':
      return config.requestsPerDay;
    default:
      return config.requestsPerMinute;
  }
}

/**
 * Limpa cache expirado (deve ser chamado periodicamente)
 */
export function cleanupRateLimitCache(): void {
  const now = Date.now();

  for (const [key, value] of rateLimitCache.entries()) {
    if (value.resetTime <= now) {
      rateLimitCache.delete(key);
    }
  }

  logger.debug(`[RATE_LIMIT_CLEANUP] Cache limpo. Entradas restantes: ${rateLimitCache.size}`);
}

// Limpar cache a cada 5 minutos
setInterval(cleanupRateLimitCache, 5 * 60 * 1000);
