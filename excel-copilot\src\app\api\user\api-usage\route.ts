// Forçar o modo dinâmico para essa rota
export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';

import { logger } from '@/lib/logger';
import { API_CALL_LIMITS, PLANS } from '@/lib/stripe';
import { prisma } from '@/server/db/client';
import { SessionUser } from '@/types/next-auth';

export async function GET(_req: NextRequest) {
  try {
    // Verificar autenticação
    const session = await getServerSession();
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Não autorizado. Faça login para continuar.' },
        { status: 401 }
      );
    }

    // Obter o ID do usuário com tipagem correta
    const userId = (session.user as SessionUser).id;

    // Buscar a assinatura ativa do usuário
    const subscription = await prisma.subscription.findFirst({
      where: {
        userId,
        OR: [{ status: 'active' }, { status: 'trialing' }],
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    // Se não tiver assinatura, usar valores do plano Free
    if (!subscription) {
      return NextResponse.json({
        plan: PLANS.FREE,
        apiCallsLimit: API_CALL_LIMITS[PLANS.FREE],
        apiCallsUsed: 0,
        percentUsed: 0,
      });
    }

    // Buscar contagem de chamadas de API do último mês
    const startOfMonth = new Date();
    startOfMonth.setDate(1);
    startOfMonth.setHours(0, 0, 0, 0);

    const apiUsage = await prisma.apiUsage.aggregate({
      where: {
        userId,
        createdAt: {
          gte: startOfMonth,
        },
      },
      _sum: {
        count: true,
      },
    });

    const apiCallsUsed = apiUsage._sum.count || 0;

    // Calcular porcentagem de uso
    const percentUsed = Math.min(
      Math.round((apiCallsUsed / subscription.apiCallsLimit) * 100),
      100
    );

    // Atualizar a contagem na assinatura para referência futura
    await prisma.subscription.update({
      where: { id: subscription.id },
      data: { apiCallsUsed },
    });

    return NextResponse.json({
      plan: subscription.plan,
      apiCallsLimit: subscription.apiCallsLimit,
      apiCallsUsed,
      percentUsed,
      currentPeriodEnd: subscription.currentPeriodEnd,
    });
  } catch (error) {
    logger.error('[API_USAGE_ERROR]', error);
    return NextResponse.json({ error: 'Erro ao buscar informações de uso.' }, { status: 500 });
  }
}
