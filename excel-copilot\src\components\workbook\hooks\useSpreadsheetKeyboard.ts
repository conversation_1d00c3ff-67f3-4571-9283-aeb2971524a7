import { useCallback, useEffect } from 'react';
import { toast } from 'sonner';

import { useSpreadsheetContext } from '../SpreadsheetContext';

interface UseSpreadsheetKeyboardProps {
  onSave?: () => void;
  onUndo?: () => void;
  onRedo?: () => void;
  onToggleCommandPalette?: () => void;
  onToggleFullScreen?: () => void;
  onEscape?: () => void;
}

/**
 * Hook para gerenciar atalhos de teclado do SpreadsheetEditor
 */
export function useSpreadsheetKeyboard({
  onSave,
  onUndo,
  onRedo,
  onToggleCommandPalette,
  onToggleFullScreen,
  onEscape,
}: UseSpreadsheetKeyboardProps) {
  const { state, actions } = useSpreadsheetContext();

  // Função para mostrar atalhos de teclado
  const showKeyboardShortcuts = useCallback(() => {
    actions.updateUI({ showKeyboardShortcuts: true });
  }, [actions]);

  const hideKeyboardShortcuts = useCallback(() => {
    actions.updateUI({ showKeyboardShortcuts: false });
  }, [actions]);

  // Handler principal de teclado
  const handleKeyDown = useCallback(
    (event: KeyboardEvent) => {
      const { ctrlKey, metaKey, shiftKey, key, altKey } = event;
      const isCtrlOrCmd = ctrlKey || metaKey;

      // Prevenir ações se estiver salvando
      if (state.ui.isSaving && (isCtrlOrCmd || key === 'Escape')) {
        event.preventDefault();
        return;
      }

      // Atalhos principais
      switch (key) {
        case 's':
          if (isCtrlOrCmd) {
            event.preventDefault();
            if (onSave && !state.readOnly) {
              onSave();
              toast.success('Salvando planilha...', { duration: 1000 });
            }
          }
          break;

        case 'z':
          if (isCtrlOrCmd && !shiftKey) {
            event.preventDefault();
            if (onUndo) {
              onUndo();
              toast.success('Desfazer', { duration: 1000 });
            }
          }
          break;

        case 'y':
          if (isCtrlOrCmd) {
            event.preventDefault();
            if (onRedo) {
              onRedo();
              toast.success('Refazer', { duration: 1000 });
            }
          }
          break;

        case 'z':
          if (isCtrlOrCmd && shiftKey) {
            event.preventDefault();
            if (onRedo) {
              onRedo();
              toast.success('Refazer', { duration: 1000 });
            }
          }
          break;

        case 'k':
          if (isCtrlOrCmd) {
            event.preventDefault();
            if (onToggleCommandPalette) {
              onToggleCommandPalette();
            }
          }
          break;

        case 'Enter':
          if (isCtrlOrCmd) {
            event.preventDefault();
            if (onToggleCommandPalette) {
              onToggleCommandPalette();
            }
          }
          break;

        case 'F11':
          event.preventDefault();
          if (onToggleFullScreen) {
            onToggleFullScreen();
          }
          break;

        case 'Escape':
          if (onEscape) {
            onEscape();
          }
          break;

        case '?':
          if (shiftKey) {
            event.preventDefault();
            showKeyboardShortcuts();
          }
          break;

        // Atalhos para adicionar linhas/colunas
        case 'r':
          if (isCtrlOrCmd && shiftKey && !state.readOnly) {
            event.preventDefault();
            actions.addRow();
            toast.success('Nova linha adicionada', { duration: 1000 });
          }
          break;

        case 'c':
          if (isCtrlOrCmd && shiftKey && !state.readOnly) {
            event.preventDefault();
            actions.addColumn();
            toast.success('Nova coluna adicionada', { duration: 1000 });
          }
          break;

        // Atalho para alternar painel de IA
        case 'i':
          if (isCtrlOrCmd && altKey) {
            event.preventDefault();
            actions.updateUI({ aiPanelCollapsed: !state.ui.aiPanelCollapsed });
          }
          break;

        // Atalho para foco no chat (mobile)
        case 'm':
          if (isCtrlOrCmd && state.ui.isMobileView) {
            event.preventDefault();
            actions.updateUI({ showMobileChat: !state.ui.showMobileChat });
          }
          break;

        default:
          break;
      }
    },
    [
      state.ui.isSaving,
      state.ui.aiPanelCollapsed,
      state.ui.isMobileView,
      state.ui.showMobileChat,
      state.readOnly,
      onSave,
      onUndo,
      onRedo,
      onToggleCommandPalette,
      onToggleFullScreen,
      onEscape,
      actions,
      showKeyboardShortcuts,
    ]
  );

  // Registrar event listeners
  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleKeyDown]);

  // Lista de atalhos para exibição
  const keyboardShortcuts = [
    { keys: ['Ctrl', 'S'], description: 'Salvar planilha', mac: ['⌘', 'S'] },
    { keys: ['Ctrl', 'Z'], description: 'Desfazer', mac: ['⌘', 'Z'] },
    { keys: ['Ctrl', 'Y'], description: 'Refazer', mac: ['⌘', 'Y'] },
    { keys: ['Ctrl', 'Shift', 'Z'], description: 'Refazer (alternativo)', mac: ['⌘', '⇧', 'Z'] },
    { keys: ['Ctrl', 'K'], description: 'Abrir paleta de comandos', mac: ['⌘', 'K'] },
    { keys: ['Ctrl', 'Enter'], description: 'Abrir paleta de comandos', mac: ['⌘', '↵'] },
    { keys: ['F11'], description: 'Alternar tela cheia', mac: ['F11'] },
    { keys: ['Escape'], description: 'Fechar modais/painéis', mac: ['Esc'] },
    { keys: ['?'], description: 'Mostrar atalhos', mac: ['?'] },
    { keys: ['Ctrl', 'Shift', 'R'], description: 'Adicionar linha', mac: ['⌘', '⇧', 'R'] },
    { keys: ['Ctrl', 'Shift', 'C'], description: 'Adicionar coluna', mac: ['⌘', '⇧', 'C'] },
    { keys: ['Ctrl', 'Alt', 'I'], description: 'Alternar painel IA', mac: ['⌘', '⌥', 'I'] },
    { keys: ['Ctrl', 'M'], description: 'Chat mobile', mac: ['⌘', 'M'] },
  ];

  return {
    showKeyboardShortcuts,
    hideKeyboardShortcuts,
    keyboardShortcuts,
    isShowingShortcuts: state.ui.showKeyboardShortcuts,
  };
}
