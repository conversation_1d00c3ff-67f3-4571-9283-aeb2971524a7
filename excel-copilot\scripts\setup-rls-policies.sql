-- =====================================================
-- CONFIGURAÇÃO DE ROW LEVEL SECURITY (RLS) POLICIES
-- Excel Copilot - Supabase Integration
-- =====================================================

-- Habilitar RLS nas tabelas principais
ALTER TABLE "User" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "Workbook" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "Sheet" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "Cell" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "WorkbookShare" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "Session" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "Account" ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- POLÍTICAS PARA TABELA USER
-- =====================================================

-- Usuários podem ver apenas seus próprios dados
CREATE POLICY "Users can view own profile" ON "User"
    FOR SELECT USING (auth.uid()::text = id);

-- Usuários podem atualizar apenas seus próprios dados
CREATE POLICY "Users can update own profile" ON "User"
    FOR UPDATE USING (auth.uid()::text = id);

-- Permitir inserção de novos usuários (para registro)
CREATE POLICY "Allow user registration" ON "User"
    FOR INSERT WITH CHECK (auth.uid()::text = id);

-- =====================================================
-- POLÍTICAS PARA TABELA WORKBOOK
-- =====================================================

-- Usuários podem ver workbooks que possuem ou que foram compartilhados com eles
CREATE POLICY "Users can view own workbooks" ON "Workbook"
    FOR SELECT USING (
        userId = auth.uid()::text 
        OR id IN (
            SELECT workbookId FROM "WorkbookShare" 
            WHERE userId = auth.uid()::text
        )
    );

-- Usuários podem criar novos workbooks
CREATE POLICY "Users can create workbooks" ON "Workbook"
    FOR INSERT WITH CHECK (userId = auth.uid()::text);

-- Usuários podem atualizar apenas seus próprios workbooks
CREATE POLICY "Users can update own workbooks" ON "Workbook"
    FOR UPDATE USING (userId = auth.uid()::text);

-- Usuários podem deletar apenas seus próprios workbooks
CREATE POLICY "Users can delete own workbooks" ON "Workbook"
    FOR DELETE USING (userId = auth.uid()::text);

-- =====================================================
-- POLÍTICAS PARA TABELA SHEET
-- =====================================================

-- Usuários podem ver sheets de workbooks que têm acesso
CREATE POLICY "Users can view accessible sheets" ON "Sheet"
    FOR SELECT USING (
        workbookId IN (
            SELECT id FROM "Workbook" 
            WHERE userId = auth.uid()::text 
            OR id IN (
                SELECT workbookId FROM "WorkbookShare" 
                WHERE userId = auth.uid()::text
            )
        )
    );

-- Usuários podem criar sheets em workbooks que possuem
CREATE POLICY "Users can create sheets in own workbooks" ON "Sheet"
    FOR INSERT WITH CHECK (
        workbookId IN (
            SELECT id FROM "Workbook" 
            WHERE userId = auth.uid()::text
        )
    );

-- Usuários podem atualizar sheets em workbooks que possuem ou têm permissão de edição
CREATE POLICY "Users can update accessible sheets" ON "Sheet"
    FOR UPDATE USING (
        workbookId IN (
            SELECT id FROM "Workbook" 
            WHERE userId = auth.uid()::text
        )
        OR workbookId IN (
            SELECT workbookId FROM "WorkbookShare" 
            WHERE userId = auth.uid()::text 
            AND permission IN ('EDIT', 'ADMIN')
        )
    );

-- Usuários podem deletar sheets apenas em workbooks que possuem
CREATE POLICY "Users can delete sheets in own workbooks" ON "Sheet"
    FOR DELETE USING (
        workbookId IN (
            SELECT id FROM "Workbook" 
            WHERE userId = auth.uid()::text
        )
    );

-- =====================================================
-- POLÍTICAS PARA TABELA CELL
-- =====================================================

-- Usuários podem ver células de sheets que têm acesso
CREATE POLICY "Users can view accessible cells" ON "Cell"
    FOR SELECT USING (
        sheetId IN (
            SELECT id FROM "Sheet" 
            WHERE workbookId IN (
                SELECT id FROM "Workbook" 
                WHERE userId = auth.uid()::text 
                OR id IN (
                    SELECT workbookId FROM "WorkbookShare" 
                    WHERE userId = auth.uid()::text
                )
            )
        )
    );

-- Usuários podem criar células em sheets que têm acesso de edição
CREATE POLICY "Users can create cells in editable sheets" ON "Cell"
    FOR INSERT WITH CHECK (
        sheetId IN (
            SELECT id FROM "Sheet" 
            WHERE workbookId IN (
                SELECT id FROM "Workbook" 
                WHERE userId = auth.uid()::text
            )
            OR workbookId IN (
                SELECT workbookId FROM "WorkbookShare" 
                WHERE userId = auth.uid()::text 
                AND permission IN ('EDIT', 'ADMIN')
            )
        )
    );

-- Usuários podem atualizar células em sheets que têm acesso de edição
CREATE POLICY "Users can update cells in editable sheets" ON "Cell"
    FOR UPDATE USING (
        sheetId IN (
            SELECT id FROM "Sheet" 
            WHERE workbookId IN (
                SELECT id FROM "Workbook" 
                WHERE userId = auth.uid()::text
            )
            OR workbookId IN (
                SELECT workbookId FROM "WorkbookShare" 
                WHERE userId = auth.uid()::text 
                AND permission IN ('EDIT', 'ADMIN')
            )
        )
    );

-- Usuários podem deletar células apenas em workbooks que possuem
CREATE POLICY "Users can delete cells in own workbooks" ON "Cell"
    FOR DELETE USING (
        sheetId IN (
            SELECT id FROM "Sheet" 
            WHERE workbookId IN (
                SELECT id FROM "Workbook" 
                WHERE userId = auth.uid()::text
            )
        )
    );

-- =====================================================
-- POLÍTICAS PARA TABELA WORKBOOKSHARE
-- =====================================================

-- Usuários podem ver compartilhamentos de seus workbooks ou compartilhamentos com eles
CREATE POLICY "Users can view relevant shares" ON "WorkbookShare"
    FOR SELECT USING (
        userId = auth.uid()::text 
        OR workbookId IN (
            SELECT id FROM "Workbook" 
            WHERE userId = auth.uid()::text
        )
    );

-- Apenas donos de workbooks podem criar compartilhamentos
CREATE POLICY "Workbook owners can create shares" ON "WorkbookShare"
    FOR INSERT WITH CHECK (
        workbookId IN (
            SELECT id FROM "Workbook" 
            WHERE userId = auth.uid()::text
        )
    );

-- Apenas donos de workbooks podem atualizar compartilhamentos
CREATE POLICY "Workbook owners can update shares" ON "WorkbookShare"
    FOR UPDATE USING (
        workbookId IN (
            SELECT id FROM "Workbook" 
            WHERE userId = auth.uid()::text
        )
    );

-- Apenas donos de workbooks podem deletar compartilhamentos
CREATE POLICY "Workbook owners can delete shares" ON "WorkbookShare"
    FOR DELETE USING (
        workbookId IN (
            SELECT id FROM "Workbook" 
            WHERE userId = auth.uid()::text
        )
    );

-- =====================================================
-- POLÍTICAS PARA TABELA SESSION
-- =====================================================

-- Usuários podem ver apenas suas próprias sessões
CREATE POLICY "Users can view own sessions" ON "Session"
    FOR SELECT USING (userId = auth.uid()::text);

-- Usuários podem criar suas próprias sessões
CREATE POLICY "Users can create own sessions" ON "Session"
    FOR INSERT WITH CHECK (userId = auth.uid()::text);

-- Usuários podem atualizar suas próprias sessões
CREATE POLICY "Users can update own sessions" ON "Session"
    FOR UPDATE USING (userId = auth.uid()::text);

-- Usuários podem deletar suas próprias sessões
CREATE POLICY "Users can delete own sessions" ON "Session"
    FOR DELETE USING (userId = auth.uid()::text);

-- =====================================================
-- POLÍTICAS PARA TABELA ACCOUNT
-- =====================================================

-- Usuários podem ver apenas suas próprias contas
CREATE POLICY "Users can view own accounts" ON "Account"
    FOR SELECT USING (userId = auth.uid()::text);

-- Usuários podem criar suas próprias contas
CREATE POLICY "Users can create own accounts" ON "Account"
    FOR INSERT WITH CHECK (userId = auth.uid()::text);

-- Usuários podem atualizar suas próprias contas
CREATE POLICY "Users can update own accounts" ON "Account"
    FOR UPDATE USING (userId = auth.uid()::text);

-- Usuários podem deletar suas próprias contas
CREATE POLICY "Users can delete own accounts" ON "Account"
    FOR DELETE USING (userId = auth.uid()::text);

-- =====================================================
-- FUNÇÕES AUXILIARES PARA RLS
-- =====================================================

-- Função para verificar se usuário tem acesso a um workbook
CREATE OR REPLACE FUNCTION user_has_workbook_access(workbook_id text, user_id text)
RETURNS boolean AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM "Workbook" 
        WHERE id = workbook_id AND userId = user_id
    ) OR EXISTS (
        SELECT 1 FROM "WorkbookShare" 
        WHERE workbookId = workbook_id AND userId = user_id
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Função para verificar se usuário pode editar um workbook
CREATE OR REPLACE FUNCTION user_can_edit_workbook(workbook_id text, user_id text)
RETURNS boolean AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM "Workbook" 
        WHERE id = workbook_id AND userId = user_id
    ) OR EXISTS (
        SELECT 1 FROM "WorkbookShare" 
        WHERE workbookId = workbook_id 
        AND userId = user_id 
        AND permission IN ('EDIT', 'ADMIN')
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- COMENTÁRIOS E DOCUMENTAÇÃO
-- =====================================================

COMMENT ON POLICY "Users can view own profile" ON "User" IS 
'Permite que usuários vejam apenas seu próprio perfil';

COMMENT ON POLICY "Users can view own workbooks" ON "Workbook" IS 
'Permite que usuários vejam workbooks que possuem ou que foram compartilhados com eles';

COMMENT ON POLICY "Users can view accessible sheets" ON "Sheet" IS 
'Permite que usuários vejam sheets de workbooks que têm acesso (próprios ou compartilhados)';

COMMENT ON POLICY "Users can view accessible cells" ON "Cell" IS 
'Permite que usuários vejam células de sheets que têm acesso';

COMMENT ON POLICY "Users can view relevant shares" ON "WorkbookShare" IS 
'Permite que usuários vejam compartilhamentos relevantes (seus workbooks ou compartilhamentos com eles)';

-- =====================================================
-- VERIFICAÇÃO DE POLÍTICAS
-- =====================================================

-- Verificar se todas as políticas foram criadas
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
FROM pg_policies 
WHERE schemaname = 'public' 
AND tablename IN ('User', 'Workbook', 'Sheet', 'Cell', 'WorkbookShare', 'Session', 'Account')
ORDER BY tablename, policyname;
