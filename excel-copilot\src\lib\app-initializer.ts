/**
 * Inicializador centralizado da aplicação Excel Copilot
 *
 * Este módulo é responsável pela inicialização centralizada de todos
 * os serviços e validações necessárias para o funcionamento da aplicação,
 * respeitando as dependências entre serviços.
 */

import { ENV } from '@/config/unified-environment';
// CORREÇÃO: Não importar dynamic-import que causa problemas no build
// import { dynamicImportManager } from '@/lib/ai/dynamic-import';
import { validateEnvironment } from '@/lib/env-validator';
import { checkAllServicesHealth, HealthCheckResult } from '@/lib/health-checker';
import { logger } from '@/lib/logger';
import { RateLimiter } from '@/lib/security/rate-limiter';
import { ServiceManager, getServiceManager, ServicePriority } from '@/lib/services';
// import { telemetry } from '@/lib/telemetry'; // Removido - não existe mais

// Mock telemetry para compatibilidade
const telemetry = {
  initialize: () => {},
  recordError: () => {}
};

import { logError, toError } from './utils/error-utils';
// Import the dynamic import manager

// Declaração global para rate limiters
declare global {
  // eslint-disable-next-line no-var
  var excelCopilotRateLimiters: Map<string, RateLimiter> | undefined;
}

/**
 * Interface para result de inicialização
 */
export interface InitializationResult {
  success: boolean;
  message: string;
  details?: unknown;
  error?: string;
  healthChecks?: HealthCheckResult[];
}

/**
 * Estado da inicialização da aplicação
 */
class InitializationState {
  private static instance: InitializationState;
  private _initialized: boolean = false;
  private _initializationResult: InitializationResult | null = null;
  private _initializationPromise: Promise<InitializationResult> | null = null;
  private _shutdownPromise: Promise<void> | null = null;
  private _isShuttingDown: boolean = false;

  private constructor() {}

  public static getInstance(): InitializationState {
    if (!InitializationState.instance) {
      InitializationState.instance = new InitializationState();
    }
    return InitializationState.instance;
  }

  get initialized(): boolean {
    return this._initialized;
  }

  get initializationResult(): InitializationResult | null {
    return this._initializationResult;
  }

  set initializationResult(result: InitializationResult | null) {
    this._initializationResult = result;
    this._initialized = result?.success || false;
  }

  get initializationPromise(): Promise<InitializationResult> | null {
    return this._initializationPromise;
  }

  set initializationPromise(promise: Promise<InitializationResult> | null) {
    this._initializationPromise = promise;
  }

  get isShuttingDown(): boolean {
    return this._isShuttingDown;
  }

  set isShuttingDown(value: boolean) {
    this._isShuttingDown = value;
  }

  get shutdownPromise(): Promise<void> | null {
    return this._shutdownPromise;
  }

  set shutdownPromise(promise: Promise<void> | null) {
    this._shutdownPromise = promise;
  }
}

/**
 * Configuração dos rate limiters globais
 */
export function setupRateLimiters(): Map<string, RateLimiter> {
  const rateLimiters = new Map<string, RateLimiter>();

  // Rate limiter para API de chat
  rateLimiters.set(
    'chat',
    new RateLimiter({
      windowMs: 60 * 1000, // 1 minuto
      maxRequests: 50,
      message: 'Muitas requisições de chat, por favor tente novamente mais tarde',
      maxEntries: 2000, // Limite de 2000 IPs distintos
    })
  );

  // Rate limiter para operações de Excel
  rateLimiters.set(
    'excel',
    new RateLimiter({
      windowMs: 60 * 1000, // 1 minuto
      maxRequests: 100,
      message: 'Muitas operações de Excel, por favor tente novamente mais tarde',
      maxEntries: 1500, // Limite de 1500 IPs distintos
    })
  );

  // Rate limiter para APIs gerais
  rateLimiters.set(
    'api',
    new RateLimiter({
      windowMs: 60 * 1000, // 1 minuto
      maxRequests: 200,
      message: 'Muitas requisições, por favor tente novamente mais tarde',
      maxEntries: 5000, // Limite de 5000 IPs distintos
    })
  );

  return rateLimiters;
}

/**
 * Inicializa todos os serviços da aplicação de forma centralizada
 *
 * Esta função é a porta de entrada para inicialização de todos os componentes,
 * serviços e configurações necessárias para o funcionamento da aplicação.
 */
export async function initializeApplication(): Promise<InitializationResult> {
  const state = InitializationState.getInstance();

  // Se já tiver uma inicialização em andamento, retorna a promise existente
  if (state.initializationPromise) {
    return state.initializationPromise;
  }

  // Se já estiver inicializado, retorna o resultado anterior
  if (state.initialized && state.initializationResult) {
    return state.initializationResult;
  }

  // Cria uma nova promise de inicialização
  const initPromise = _performInitialization();
  state.initializationPromise = initPromise;

  // Aguarda a inicialização e armazena o resultado
  const result = await initPromise;
  state.initializationResult = result;
  state.initializationPromise = null;

  // Registra handlers para gerenciar o ciclo de vida e shutdown graceful
  if (result.success) {
    setupLifecycleManagement();
  }

  return result;
}

/**
 * Configura tratamento de eventos de ciclo de vida do processo
 * para garantir um shutdown graceful em caso de encerramentos
 */
function setupLifecycleManagement(): void {
  if (typeof process !== 'undefined') {
    // Registrar os handlers apenas uma vez
    const handlers = process.listeners('SIGTERM').length + process.listeners('SIGINT').length;

    if (handlers === 0) {
      // Handler para SIGTERM (sinal de término, ex: kubectl delete, systemctl stop)
      process.on('SIGTERM', async () => {
        logger.info('Sinal SIGTERM recebido. Iniciando encerramento graceful...');
        await shutdownApplication();
        process.exit(0);
      });

      // Handler para SIGINT (Ctrl+C)
      process.on('SIGINT', async () => {
        logger.info('Sinal SIGINT recebido. Iniciando encerramento graceful...');
        await shutdownApplication();
        process.exit(0);
      });

      // Handler para exceções não tratadas
      process.on('uncaughtException', async error => {
        logger.error('Exceção não tratada:', error);
        await shutdownApplication();
        process.exit(1);
      });

      // Handler para rejeições de promise não tratadas
      process.on('unhandledRejection', async reason => {
        logError('Rejeição de Promise não tratada:', reason);
        // Não saímos para permitir que o processo continue funcionando
      });

      // Registramos um callback para execução antes do encerramento do programa em Node.js
      if (process.on && typeof process.on === 'function') {
        process.on('beforeExit', async () => {
          logger.info('Processo chegando ao fim natural. Realizando limpeza final...');
          await shutdownApplication();
        });
      }

      // Usar import dinâmico sem await em função síncrona
      import('@/lib/logger').then(({ initLogger }) => {
        initLogger.info('Handlers de ciclo de vida e graceful shutdown registrados');
      });
    }
  } else {
    // Ambiente sem process (ex: alguns ambientes serverless)
    logger.warn('Objeto process não disponível. Graceful shutdown não registrado.');
  }
}

/**
 * Finaliza todos os serviços da aplicação de forma ordenada
 * Respeita a ordem inversa de dependências para shutdown seguro
 *
 * @param timeout Tempo máximo em ms para aguardar a finalização (default: 5000)
 * @returns Promise que resolve quando todos os serviços forem finalizados
 */
export async function shutdownApplication(timeout: number = 5000): Promise<void> {
  const state = InitializationState.getInstance();

  // Se já está em processo de shutdown, retorna a promise existente
  if (state.isShuttingDown && state.shutdownPromise) {
    return state.shutdownPromise;
  }

  // Se não está inicializado, não há o que finalizar
  if (!state.initialized) {
    logger.debug('Aplicação não inicializada. Nada para finalizar.');
    return Promise.resolve();
  }

  logger.info('Iniciando processo de encerramento da aplicação...');
  state.isShuttingDown = true;

  // Cria uma nova promise de shutdown
  const serviceManager = getServiceManager();
  const shutdownPromise = Promise.race([
    // Tempo máximo para aguardar finalização
    new Promise<void>(resolve => {
      setTimeout(() => {
        logger.warn(`Timeout de ${timeout}ms atingido durante shutdown. Forçando finalização.`);
        resolve();
      }, timeout);
    }),

    // Finalização ordenada dos serviços
    serviceManager.shutdown(),
  ]);

  state.shutdownPromise = shutdownPromise;

  try {
    await shutdownPromise;

    // Registra que a aplicação foi finalizada com sucesso
    state.initializationResult = null;
    logger.info('Aplicação finalizada com sucesso');
  } catch (error) {
    const errorMsg = error instanceof Error ? error.message : String(error);
    logError(`Erro durante finalização da aplicação: ${errorMsg}`, error);
  } finally {
    state.isShuttingDown = false;
    state.shutdownPromise = null;
  }
}

/**
 * Configuração dos serviços da aplicação
 * @param serviceManager Gerenciador de serviços
 */
function registerServices(serviceManager: ServiceManager) {
  // Registrar serviços por ordem de prioridade (do mais básico para o mais dependente)

  // Serviços de prioridade CRITICAL (infraestrutura básica)
  serviceManager.registerService('logger', {
    priority: ServicePriority.CRITICAL,
    initialize: async () => {
      // Usar initLogger para silenciar em produção
      const { initLogger } = await import('@/lib/logger');
      initLogger.info('Serviço de logging inicializado');
      return true;
    },
    shutdown: async () => {
      const { initLogger } = await import('@/lib/logger');
      initLogger.info('Serviço de logging finalizado');
    },
  });

  serviceManager.registerService('environment', {
    priority: ServicePriority.CRITICAL,
    dependencies: ['logger'],
    initialize: async () => {
      try {
        const isValid = validateEnvironment();
        if (!isValid) {
          throw new Error('Configuração de ambiente inválida');
        }
        const { initLogger } = await import('@/lib/logger');
        initLogger.info('Validação de ambiente concluída com sucesso');
        return true;
      } catch (error) {
        logError('Erro na validação de ambiente:', error);
        return false;
      }
    },
  });

  // Serviços de prioridade HIGH (funcionalidades importantes)
  serviceManager.registerService('telemetry', {
    priority: ServicePriority.HIGH,
    dependencies: ['logger', 'environment'],
    initialize: async () => {
      // Verificar configuração de telemetria
      const telemetryEnabled = ENV.FEATURES?.TELEMETRY_SAMPLE_RATE > 0;
      const { initLogger } = await import('@/lib/logger');

      if (telemetryEnabled) {
        telemetry.initialize({
          environment: ENV.NODE_ENV,
        });
        initLogger.info(
          'Inicializando telemetria (ambiente: ' +
            ENV.NODE_ENV +
            ', amostragem: ' +
            (ENV.FEATURES?.TELEMETRY_SAMPLE_RATE || 0) +
            '%)'
        );
        initLogger.info('Serviço de telemetria inicializado');
      } else {
        initLogger.info('Telemetria desabilitada via configuração');
      }
      return true;
    },
    shutdown: async () => {
      // Apenas tenta descarregar dados de telemetria se estiver habilitada
      const telemetryEnabled = ENV.FEATURES?.TELEMETRY_SAMPLE_RATE > 0;

      if (telemetryEnabled) {
        try {
          // Tentativa de flush, mas sem causar erro se não for possível
          await Promise.resolve();
          const { initLogger } = await import('@/lib/logger');
          initLogger.info('Serviço de telemetria finalizado');
        } catch (err) {
          logger.warn('Erro ao finalizar telemetria:', toError(err));
        }
      }
    },
  });

  // Serviços de prioridade MEDIUM (componentes principais)
  serviceManager.registerService('rate-limiters', {
    priority: ServicePriority.MEDIUM,
    dependencies: ['environment'],
    initialize: async () => {
      try {
        // Verificar se estamos no lado do cliente (browser)
        if (typeof window !== 'undefined') {
          const { initLogger } = await import('@/lib/logger');
          initLogger.info(
            'Rate limiters: Detectado ambiente cliente, configurando versão simplificada'
          );
          // No cliente, não criamos limiters reais, apenas retornamos sucesso
          return true;
        }

        // Configuração normal no servidor
        const rateLimiters = setupRateLimiters();
        // Armazenamos os rate limiters como uma variável global
        global.excelCopilotRateLimiters = rateLimiters;
        logger.info('Rate limiters configurados com sucesso');
        return true;
      } catch (error) {
        logError('Erro ao configurar rate limiters:', error);
        // Criar um Map vazio para evitar falhas catastróficas
        global.excelCopilotRateLimiters = new Map();
        return true; // Retornar true para não quebrar a inicialização
      }
    },
  });

  // Serviços de prioridade LOW (funcionalidades específicas)
  serviceManager.registerService('ai-service', {
    priority: ServicePriority.LOW,
    dependencies: ['environment', 'telemetry'],
    initialize: async () => {
      // BLOQUEIO TOTAL NO CLIENTE - NÃO EXECUTAR NENHUM CÓDIGO DE IA
      if (typeof window !== 'undefined') {
        const { initLogger } = await import('@/lib/logger');
        initLogger.info(
          'AI Service: Ambiente cliente detectado - IA completamente desabilitada no navegador'
        );
        return true; // Retorna sucesso sem fazer nada
      }

      // APENAS NO SERVIDOR - continuar com inicialização normal
      const { initLogger } = await import('@/lib/logger');

      // Se o modo mock está explicitamente ativado, não inicializar Vertex AI
      if (ENV.FEATURES.USE_MOCK_AI) {
        initLogger.info('AI Service: Modo mock ativado, pulando inicialização do Vertex AI');
        return true;
      }

      // Verificar se NEXT_PUBLIC_DISABLE_VERTEX_AI está ativo
      if (process.env.AI_ENABLED === 'true') {
        initLogger.info('AI Service: NEXT_PUBLIC_DISABLE_VERTEX_AI ativo, pulando inicialização');
        return true;
      }

      // Verificar se FORCE_GOOGLE_MOCKS está ativo
      if (process.env.AI_USE_MOCK === 'true') {
        initLogger.info('AI Service: FORCE_GOOGLE_MOCKS ativo, usando modo mock');
        return true;
      }

      // Verificar se o Vertex AI está configurado
      if (!ENV.VERTEX_AI.ENABLED) {
        logger.warn('AI Service: Vertex AI não configurado, usando modo mock');
        return true;
      }

      try {
        // CORREÇÃO: Carregar serviço AI sem usar dynamic-import problemático
        initLogger.info('AI Service: Modo simplificado - serviços de IA disponíveis via API');
        return true;
      } catch (error) {
        logError('AI Service: Erro ao inicializar Gemini Service:', error);
        // Em caso de erro, não falhar a aplicação, apenas usar mock
        logger.warn('AI Service: Fallback para modo mock devido a erro na inicialização');
        return true;
      }
    },
    shutdown: async () => {
      // BLOQUEIO TOTAL NO CLIENTE - NÃO EXECUTAR NENHUM CÓDIGO DE IA
      if (typeof window !== 'undefined') {
        return; // Não fazer nada no cliente
      }

      // APENAS NO SERVIDOR - continuar com shutdown normal
      if (ENV.VERTEX_AI.ENABLED) {
        try {
          // CORREÇÃO: Shutdown simplificado sem dynamic-import
          logger.info('AI Service: Shutdown simplificado - serviços finalizados');
        } catch (error) {
          logError('AI Service: Erro ao finalizar Gemini Service:', error);
        }
      }
    },
  });

  // ... outros serviços ...
}

/**
 * Inicializa os serviços na ordem correta respeitando as dependências
 */
async function _initializeServicesInOrder(serviceManager: ServiceManager): Promise<void> {
  const initializationOrder = serviceManager.getInitializationOrder();

  const { initLogger } = await import('@/lib/logger');
  initLogger.info(`Ordem de inicialização de serviços: ${initializationOrder.join(', ')}`);

  for (const serviceName of initializationOrder) {
    if (serviceManager.isServiceInitialized(serviceName)) {
      logger.debug(`Serviço '${serviceName}' já inicializado, pulando.`);
      continue;
    }

    // Usar initLogger para silenciar em produção
    const { initLogger } = await import('@/lib/logger');
    initLogger.info(`Inicializando serviço: ${serviceName}`);

    try {
      // Usar any para evitar erros de TypeScript já que não temos uma interface comum
      const service = serviceManager.getService(serviceName) as {
        initialize?: () => Promise<void>;
      };

      // Se o serviço tiver um método initialize, chamá-lo
      if (service && typeof service.initialize === 'function') {
        await service.initialize();
      }

      serviceManager.setServiceInitialized(serviceName, true);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logError(`Falha ao inicializar serviço '${serviceName}': ${errorMessage}`, error);
      throw error;
    }
  }
}

/**
 * Implementação da inicialização que é executada apenas uma vez
 * Centraliza toda a lógica de inicialização de serviços que antes
 * estava distribuída entre app-initializer e services.ts
 */
async function _performInitialization(): Promise<InitializationResult> {
  // Usar initLogger para silenciar em produção
  const { initLogger } = await import('@/lib/logger');
  initLogger.info('Iniciando aplicação Excel Copilot...');

  try {
    // 1. Validação do ambiente usando o validador centralizado
    const envValidation = validateEnvironment();
    const isVercel = process.env.VERCEL === '1';

    if (!envValidation.valid && ENV.IS_PRODUCTION && !isVercel) {
      logger.error(
        `Inicialização falhou: Variáveis de ambiente obrigatórias não configuradas: ${envValidation.missing.join(', ')}`
      );
      return {
        success: false,
        message: 'Configuração de ambiente incompleta',
        details: envValidation,
      };
    }

    // 2. Inicialização do ServiceManager com sistema de dependências
    const serviceManager = getServiceManager();
    if (serviceManager.isInitialized()) {
      logger.info('ServiceManager já inicializado, reutilizando instância.');
    } else {
      // Registrar todos os serviços com suas dependências
      registerServices(serviceManager);

      // Definir categorias de serviços por prioridade
      const servicePriorities = {
        // P0: Críticos para renderização inicial - carregados imediatamente
        critical: ['environment', 'logger'],
        // P1: Alta prioridade - carregados logo após a renderização inicial
        high: ['rate-limiters'],
        // P2: Média prioridade - carregados quando o navegador estiver ocioso
        medium: ['telemetry'],
        // P3: Baixa prioridade - APENAS NO SERVIDOR (não no cliente) E APENAS SE IA ESTIVER HABILITADA
        low:
          typeof window === 'undefined' && ENV.VERTEX_AI.ENABLED && !ENV.FEATURES.USE_MOCK_AI
            ? ['ai-service']
            : [],
      };

      // Inicializar serviços críticos imediatamente
      await initializeServicesByPriority(serviceManager, servicePriorities.critical);

      // Mecanismo para rastrear serviços inicializados e atualizar o estado
      const initializedServices = new Set<string>(servicePriorities.critical);
      let healthChecksComplete = false;
      let allInitialized = false;

      // Atualizar estado da inicialização
      const updateInitializationState = () => {
        // Se já retornamos o resultado inicial, atualiza o resultado para refletir o status atual
        if (InitializationState.getInstance().initializationResult) {
          const currentResult = InitializationState.getInstance().initializationResult;
          if (currentResult) {
            // Criar novo resultado com status atual
            const updatedResult: InitializationResult = {
              ...currentResult,
              details: {
                ...((currentResult.details as Record<string, unknown>) || {}),
                initializedServices: Array.from(initializedServices),
                healthChecksComplete,
                allInitialized,
              },
            };

            // Atualizar o estado de inicialização
            InitializationState.getInstance().initializationResult = updatedResult;

            // Se todos os serviços estiverem inicializados e as verificações de saúde concluídas,
            // marcar a inicialização como concluída
            if (allInitialized && healthChecksComplete) {
              // Usar import dinâmico sem await em função síncrona
              import('@/lib/logger').then(({ initLogger }) => {
                initLogger.info('Todos os serviços e verificações completos');
              });
            }
          }
        }
      };

      // Iniciar os serviços de alta prioridade logo após retornar ao event loop
      setTimeout(async () => {
        try {
          await initializeServicesByPriority(serviceManager, servicePriorities.high);
          // Registrar serviços de alta prioridade como inicializados
          servicePriorities.high.forEach(s => initializedServices.add(s));
          updateInitializationState();

          // Iniciar serviços de média prioridade quando o navegador estiver ocioso
          scheduleIdleInitialization(
            serviceManager,
            servicePriorities.medium,
            1000,
            15000,
            async () => {
              // Registrar serviços de média prioridade como inicializados
              servicePriorities.medium.forEach(s => initializedServices.add(s));
              updateInitializationState();
            }
          );

          // Iniciar serviços de baixa prioridade após mais idle time
          scheduleIdleInitialization(
            serviceManager,
            servicePriorities.low,
            3000,
            30000,
            async () => {
              // Registrar serviços de baixa prioridade como inicializados
              servicePriorities.low.forEach(s => initializedServices.add(s));
              updateInitializationState();

              // Marcar todos os serviços como inicializados
              allInitialized = true;
              serviceManager.setInitialized(true);
              const { initLogger } = await import('@/lib/logger');
              initLogger.info('Inicialização progressiva de serviços concluída com sucesso');
              updateInitializationState();
            }
          );

          // Verificações de saúde somente após inicializar os serviços essenciais
          if (ENV.IS_PRODUCTION) {
            scheduleIdleInitialization(serviceManager, [], 5000, 60000, async () => {
              try {
                const healthResults = await checkAllServicesHealth();
                logger.info(
                  `Verificações de saúde concluídas: ${healthResults.length} serviços verificados`
                );
                healthChecksComplete = true;
                updateInitializationState();
              } catch (error) {
                logger.warn('Erro nas verificações de saúde:', error);
              }
            });
          } else {
            // Em ambiente de desenvolvimento, podemos pular verificações de saúde
            setTimeout(() => {
              healthChecksComplete = true;
              updateInitializationState();
            }, 5000);
          }
        } catch (error) {
          logError('Erro na inicialização progressiva de serviços:', error);
        }
      }, 100);
    }

    // 3. Retornar sucesso imediatamente após serviços críticos
    logger.info(`Aplicação inicializada com serviços essenciais no ambiente: ${ENV.NODE_ENV}`);

    // Coletar serviços críticos inicializados
    const initializedCriticalServices = ['environment', 'logger'].filter(name =>
      serviceManager.isServiceInitialized(name)
    );

    return {
      success: true,
      message: 'Aplicação inicializada com sucesso (inicialização progressiva em andamento)',
      details: {
        environment: ENV.NODE_ENV,
        progressiveInit: true,
        criticalServicesReady: true,
        initializedServices: initializedCriticalServices,
        healthChecksComplete: false,
        allInitialized: false,
      },
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);

    logger.error(`Falha na inicialização da aplicação: ${errorMessage}`, toError(error));

    // Também enviar para telemetria se disponível
    try {
      telemetry.recordError('app_initialization_failure', errorMessage);
    } catch (e) {
      logError('Erro ao registrar falha de inicialização na telemetria:', e);
    }

    return {
      success: false,
      message: 'Falha na inicialização da aplicação',
      error: errorMessage,
      details: {
        environment: ENV.NODE_ENV,
      },
    };
  }
}

/**
 * Inicializar serviços por prioridade
 * @param serviceManager Gerenciador de serviços
 * @param serviceNames Lista de nomes de serviços a inicializar
 */
async function initializeServicesByPriority(
  serviceManager: ServiceManager,
  serviceNames: string[]
): Promise<void> {
  const startTime = performance.now();

  for (const serviceName of serviceNames) {
    if (serviceManager.isServiceInitialized(serviceName)) {
      logger.debug(`Serviço '${serviceName}' já inicializado, pulando.`);
      continue;
    }

    try {
      const { initLogger } = await import('@/lib/logger');
      initLogger.info(`Inicializando serviço: ${serviceName}`);
      const service = serviceManager.getService(serviceName) as {
        initialize?: () => Promise<void>;
      };

      if (service && typeof service.initialize === 'function') {
        await service.initialize();
      }

      serviceManager.setServiceInitialized(serviceName, true);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logError(`Falha ao inicializar serviço '${serviceName}': ${errorMessage}`, error);
      throw error; // Re-throw para interromper a inicialização
    }
  }

  const duration = performance.now() - startTime;
  logger.debug(
    `Inicialização de serviços [${serviceNames.join(', ')}] concluída em ${duration.toFixed(2)}ms`
  );
}

/**
 * Agendar inicialização de serviços durante tempo ocioso do navegador
 * @param serviceManager Gerenciador de serviços
 * @param serviceNames Lista de serviços para inicializar
 * @param minIdleTime Tempo mínimo de ociosidade para iniciar (ms)
 * @param timeout Tempo máximo para aguardar antes de forçar inicialização
 * @param callback Função opcional a executar após inicialização
 */
function scheduleIdleInitialization(
  serviceManager: ServiceManager,
  serviceNames: string[],
  minIdleTime: number = 1000,
  timeout: number = 10000,
  callback?: () => Promise<void>
): void {
  // Se não há serviços para inicializar e não há callback, retorna
  if (serviceNames.length === 0 && !callback) {
    return;
  }

  // Inicializar serviços em tempo ocioso
  const startScheduleTime = performance.now();

  // Fallback para navegadores que não suportam requestIdleCallback
  const requestIdleCallbackPolyfill =
    typeof window !== 'undefined' && 'requestIdleCallback' in window
      ? window.requestIdleCallback
      : (cb: IdleRequestCallback) =>
          setTimeout(
            () =>
              cb({
                didTimeout: false,
                timeRemaining: () => 10000,
              }),
            1
          );

  // Timeout para garantir que a inicialização ocorra mesmo sem idle time
  const timeoutId = setTimeout(() => {
    logger.info(
      `Timeout de ${timeout}ms atingido, forçando inicialização de serviços: ${serviceNames.join(', ')}`
    );
    performInitialization();
  }, timeout);

  // Função de inicialização a ser executada em idle time ou timeout
  const performInitialization = async () => {
    clearTimeout(timeoutId);

    // Executa a inicialização dos serviços
    if (serviceNames.length > 0) {
      await initializeServicesByPriority(serviceManager, serviceNames);
    }

    // Executa callback se fornecido
    if (callback) {
      await callback();
    }

    // Log de performance
    const totalTime = performance.now() - startScheduleTime;
    const { initLogger } = await import('@/lib/logger');
    initLogger.info(
      `Inicialização em idle time concluída após ${totalTime.toFixed(2)}ms desde o agendamento`
    );
  };

  // Verificar se o serviço já foi inicializado para evitar chamadas recursivas desnecessárias
  const allServicesInitialized = serviceNames.every(name => {
    const service = serviceManager.getService(name);
    return service && service.initialized;
  });

  if (allServicesInitialized) {
    logger.debug(`Serviços [${serviceNames.join(', ')}] já inicializados, pulando agendamento.`);
    return;
  }

  // Agendar para execução em idle time - limitando a 1 tentativa para evitar recursão excessiva
  requestIdleCallbackPolyfill(
    deadline => {
      const timeElapsed = performance.now() - startScheduleTime;

      // Se já passou o tempo mínimo e temos tempo restante suficiente, ou houve timeout
      if ((timeElapsed >= minIdleTime && deadline.timeRemaining() > 50) || deadline.didTimeout) {
        performInitialization();
      } else {
        // Executar imediatamente ao invés de tentar recursivamente para evitar múltiplas chamadas
        setTimeout(performInitialization, Math.min(1000, timeout / 2));
      }
    },
    { timeout: minIdleTime }
  );
}

/**
 * Obtém um rate limiter pelo nome
 */
export function getRateLimiter(name: string): RateLimiter | null {
  // No cliente, sempre retornamos null para rate limiters
  if (typeof window !== 'undefined') {
    return null;
  }

  // No servidor, buscamos da variável global
  const rateLimiters = global.excelCopilotRateLimiters;

  if (!rateLimiters) {
    logger.warn(`Tentativa de acessar rate limiter '${name}' antes da inicialização`);
    return null;
  }

  return rateLimiters.get(name) || null;
}

// Exportar uma instância do estado de inicialização
export const initializationState = InitializationState.getInstance();
