#!/usr/bin/env node

/**
 * Script para verificar e corrigir problemas de metadata em arquivos de layout
 * Este script verifica se há a diretiva "use client" em arquivos que exportam metadata
 * e remove essa diretiva para tornar os arquivos server-side
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

// Cores para mensagens de terminal
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
};

// Função principal
async function main() {
  console.log(`${colors.cyan}Excel Copilot - Correção de Metadata em Layouts${colors.reset}`);
  console.log(
    `${colors.yellow}Este script irá corrigir problemas de exportação de metadata em arquivos de layout${colors.reset}\n`
  );

  const rootDir = path.resolve(__dirname, '..');
  const srcDir = path.join(rootDir, 'src');
  const appDir = path.join(srcDir, 'app');

  // Verifica se o diretório app existe
  if (!fs.existsSync(appDir)) {
    console.log(
      `${colors.red}Diretório app não encontrado. Verifique se você está executando este script na pasta raiz do projeto.${colors.reset}`
    );
    process.exit(1);
  }

  try {
    // Encontra todos os arquivos layout.tsx
    const layoutFiles = glob.sync('**/layout.tsx', { cwd: appDir });

    if (layoutFiles.length === 0) {
      console.log(
        `${colors.yellow}Nenhum arquivo layout.tsx encontrado no diretório app.${colors.reset}`
      );
      process.exit(0);
    }

    console.log(
      `${colors.blue}Encontrados ${layoutFiles.length} arquivos de layout para verificação.${colors.reset}\n`
    );

    let filesFixed = 0;

    // Processa cada arquivo
    for (const file of layoutFiles) {
      const filePath = path.join(appDir, file);
      const content = fs.readFileSync(filePath, 'utf8');

      // Verifica se o arquivo exporta metadata
      const hasMetadata = /export\s+const\s+metadata/.test(content);

      // Verifica se tem a diretiva "use client"
      const hasUseClient = /['"]use client['"]/.test(content);

      if (hasMetadata && hasUseClient) {
        console.log(`${colors.yellow}Arquivo: ${file}${colors.reset}`);
        console.log(
          `  ${colors.red}[ERRO] Exportação de metadata com diretiva "use client"${colors.reset}`
        );

        // Remove a diretiva "use client" e qualquer linha em branco que a siga
        const updatedContent = content
          .replace(/['"]use client['"]\s*;?\s*(\r?\n)+/, '')
          .replace(/['"]use client['"]\s*;?/, '');

        // Salva as alterações no arquivo
        fs.writeFileSync(filePath, updatedContent);

        console.log(
          `  ${colors.green}Correção aplicada: Removida diretiva "use client"${colors.reset}`
        );
        filesFixed++;
      } else if (hasMetadata) {
        console.log(`${colors.green}Arquivo: ${file}${colors.reset}`);
        console.log(
          `  ${colors.green}OK: Arquivo exporta metadata mas não tem diretiva "use client"${colors.reset}`
        );
      }
    }

    console.log(`\n${colors.blue}Verificação concluída!${colors.reset}`);
    console.log(`${colors.cyan}Resumo:${colors.reset}`);
    console.log(`  ${colors.green}Arquivos processados: ${layoutFiles.length}${colors.reset}`);
    console.log(`  ${colors.green}Arquivos corrigidos: ${filesFixed}${colors.reset}`);

    // Também verifica arquivos page.tsx que exportam metadata
    console.log(
      `\n${colors.blue}Verificando arquivos page.tsx que exportam metadata...${colors.reset}`
    );

    const pageFiles = glob.sync('**/page.tsx', { cwd: appDir });
    let pagesWithMetadata = 0;

    for (const file of pageFiles) {
      const filePath = path.join(appDir, file);
      const content = fs.readFileSync(filePath, 'utf8');

      // Verifica se o arquivo exporta metadata
      const hasMetadata = /export\s+const\s+metadata/.test(content);

      // Verifica se tem a diretiva "use client"
      const hasUseClient = /['"]use client['"]/.test(content);

      if (hasMetadata && hasUseClient) {
        console.log(`${colors.yellow}Arquivo: ${file}${colors.reset}`);
        console.log(
          `  ${colors.red}[ERRO] Arquivo page.tsx exporta metadata com diretiva "use client"${colors.reset}`
        );
        console.log(
          `  ${colors.yellow}Recomendação: Mover a exportação de metadata para o layout.tsx da mesma pasta${colors.reset}`
        );
        pagesWithMetadata++;
      }
    }

    if (pagesWithMetadata === 0) {
      console.log(
        `  ${colors.green}Nenhum arquivo page.tsx exportando metadata encontrado.${colors.reset}`
      );
    }

    if (filesFixed > 0 || pagesWithMetadata > 0) {
      console.log(`\n${colors.yellow}Próximos passos:${colors.reset}`);
      console.log(
        `  1. Execute o comando 'npm run build' para verificar se os problemas foram resolvidos`
      );
      console.log(
        `  2. Se ainda houver problemas, considere mover a exportação de metadata para arquivos de layout específicos`
      );
    } else {
      console.log(
        `\n${colors.green}Todos os arquivos de layout estão configurados corretamente!${colors.reset}`
      );
    }
  } catch (error) {
    console.error(`${colors.red}Erro ao processar os arquivos:${colors.reset}`, error);
    process.exit(1);
  }
}

// Executa a função principal
main().catch(error => {
  console.error(`${colors.red}Erro ao executar o script:${colors.reset}`, error);
  process.exit(1);
});
