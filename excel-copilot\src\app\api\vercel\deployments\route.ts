import { NextRequest } from 'next/server';

import { logger } from '@/lib/logger';
import { VercelClient } from '@/lib/vercel-integration';
import { ApiResponse } from '@/utils/api-response';

// Configurar rota como dinâmica
export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

/**
 * GET /api/vercel/deployments
 * Lista deployments do projeto
 */
export async function GET(request: NextRequest) {
  try {
    // Verificar se temos as credenciais necessárias
    const apiToken = process.env.MCP_VERCEL_TOKEN;
    const projectId = process.env.MCP_VERCEL_PROJECT_ID;
    const teamId = process.env.MCP_VERCEL_TEAM_ID;

    if (!apiToken) {
      return ApiResponse.error('VERCEL_API_TOKEN não configurado', 'VERCEL_CONFIG_ERROR', 500);
    }

    // Obter parâmetros da query
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '20');
    const state = searchParams.get('state');

    // Validar parâmetros
    if (limit < 1 || limit > 100) {
      return ApiResponse.error(
        'Parâmetro limit deve estar entre 1 e 100',
        'INVALID_PARAMETER',
        400
      );
    }

    if (
      state &&
      !['BUILDING', 'ERROR', 'INITIALIZING', 'QUEUED', 'READY', 'CANCELED'].includes(state)
    ) {
      return ApiResponse.error('Parâmetro state inválido', 'INVALID_PARAMETER', 400);
    }

    // Criar cliente Vercel
    const vercelClientOptions: { apiToken: string; teamId?: string; projectId?: string } = {
      apiToken,
    };
    if (teamId) vercelClientOptions.teamId = teamId;
    if (projectId) vercelClientOptions.projectId = projectId;
    const vercelClient = new VercelClient(vercelClientOptions);

    // Obter deployments
    const deployments = await vercelClient.getDeployments(limit);

    // Filtrar por estado se especificado
    const filteredDeployments = state ? deployments.filter(d => d.state === state) : deployments;

    // Formatar deployments para resposta
    const formattedDeployments = filteredDeployments.map(deployment => ({
      id: deployment.uid,
      name: deployment.name,
      url: deployment.url,
      state: deployment.state,
      target: deployment.target,
      source: deployment.source,
      created: new Date(deployment.created).toISOString(),
      buildingAt: deployment.buildingAt ? new Date(deployment.buildingAt).toISOString() : null,
      ready: deployment.ready ? new Date(deployment.ready).toISOString() : null,
      duration:
        deployment.ready && deployment.buildingAt ? deployment.ready - deployment.buildingAt : null,
      alias: deployment.alias,
      inspectorUrl: deployment.inspectorUrl,
    }));

    const response = {
      deployments: formattedDeployments,
      total: formattedDeployments.length,
      filters: {
        limit,
        state: state || 'all',
      },
      timestamp: new Date().toISOString(),
    };

    logger.info('Deployments Vercel obtidos com sucesso', {
      count: formattedDeployments.length,
      state,
    });

    return ApiResponse.success(response);
  } catch (error) {
    logger.error('Erro ao obter deployments do Vercel', { error });

    if (error instanceof Error) {
      return ApiResponse.error(
        `Erro ao conectar com Vercel: ${error.message}`,
        'VERCEL_API_ERROR',
        500
      );
    }

    return ApiResponse.error('Erro interno do servidor', 'INTERNAL_ERROR', 500);
  }
}

/**
 * POST /api/vercel/deployments
 * Obtém detalhes de um deployment específico
 */
export async function POST(request: NextRequest) {
  try {
    const apiToken = process.env.MCP_VERCEL_TOKEN;
    const projectId = process.env.MCP_VERCEL_PROJECT_ID;
    const teamId = process.env.MCP_VERCEL_TEAM_ID;

    if (!apiToken) {
      return ApiResponse.error('VERCEL_API_TOKEN não configurado', 'VERCEL_CONFIG_ERROR', 500);
    }

    // Obter ID do deployment do body
    const body = await request.json();
    const { deploymentId, includeLogs = false } = body;

    if (!deploymentId) {
      return ApiResponse.error('deploymentId é obrigatório', 'INVALID_PARAMETER', 400);
    }

    const vercelClientOptions: { apiToken: string; teamId?: string; projectId?: string } = {
      apiToken,
    };
    if (teamId) vercelClientOptions.teamId = teamId;
    if (projectId) vercelClientOptions.projectId = projectId;
    const vercelClient = new VercelClient(vercelClientOptions);

    // Obter detalhes do deployment
    const deployment = await vercelClient.getDeployment(deploymentId);

    // Formatar deployment
    const formattedDeployment = {
      id: deployment.uid,
      name: deployment.name,
      url: deployment.url,
      state: deployment.state,
      target: deployment.target,
      source: deployment.source,
      created: new Date(deployment.created).toISOString(),
      buildingAt: deployment.buildingAt ? new Date(deployment.buildingAt).toISOString() : null,
      ready: deployment.ready ? new Date(deployment.ready).toISOString() : null,
      duration:
        deployment.ready && deployment.buildingAt ? deployment.ready - deployment.buildingAt : null,
      alias: deployment.alias,
      inspectorUrl: deployment.inspectorUrl,
      meta: deployment.meta,
    };

    let logs = null;
    if (includeLogs) {
      try {
        const rawLogs = await vercelClient.getDeploymentLogs(deploymentId, { limit: 100 });
        logs = rawLogs.map(log => ({
          timestamp: new Date(log.timestamp).toISOString(),
          level: log.level,
          source: log.source,
          message: log.message,
          requestId: log.requestId,
          region: log.region,
        }));
      } catch (error) {
        logger.warn(`Erro ao obter logs do deployment ${deploymentId}`, error);
        logs = [];
      }
    }

    const response = {
      deployment: formattedDeployment,
      logs,
      timestamp: new Date().toISOString(),
    };

    logger.info('Detalhes do deployment obtidos com sucesso', {
      deploymentId,
      state: deployment.state,
      includeLogs,
    });

    return ApiResponse.success(response);
  } catch (error) {
    logger.error('Erro ao obter detalhes do deployment', { error });

    if (error instanceof Error) {
      return ApiResponse.error(
        `Erro ao conectar com Vercel: ${error.message}`,
        'VERCEL_API_ERROR',
        500
      );
    }

    return ApiResponse.error('Erro interno do servidor', 'INTERNAL_ERROR', 500);
  }
}
