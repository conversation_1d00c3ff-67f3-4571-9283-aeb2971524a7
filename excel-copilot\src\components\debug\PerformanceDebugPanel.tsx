/**
 * Painel de debug para monitoramento de performance dos componentes UI
 * Visível apenas em desenvolvimento
 */

'use client';

import { BarChart3, Clock, Eye, RefreshCw, TrendingUp, X } from 'lucide-react';
import React, { useState, useEffect } from 'react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { performanceMonitor } from '@/utils/performance-monitor';

interface PerformanceDebugPanelProps {
  isVisible?: boolean;
  onClose?: () => void;
}

export function PerformanceDebugPanel({ isVisible = false, onClose }: PerformanceDebugPanelProps) {
  const [metrics, setMetrics] = useState(performanceMonitor.getAllMetrics());
  const [comparison, setComparison] = useState(performanceMonitor.getPerformanceComparison());
  const [recentEvents, setRecentEvents] = useState(performanceMonitor.getRecentRenderEvents(20));
  const [autoRefresh, setAutoRefresh] = useState(true);

  // Atualizar métricas periodicamente
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
      setMetrics(performanceMonitor.getAllMetrics());
      setComparison(performanceMonitor.getPerformanceComparison());
      setRecentEvents(performanceMonitor.getRecentRenderEvents(20));
    }, 2000);

    return () => clearInterval(interval);
  }, [autoRefresh]);

  const handleRefresh = () => {
    setMetrics(performanceMonitor.getAllMetrics());
    setComparison(performanceMonitor.getPerformanceComparison());
    setRecentEvents(performanceMonitor.getRecentRenderEvents(20));
  };

  const handleClear = () => {
    performanceMonitor.clear();
    handleRefresh();
  };

  const handleExport = () => {
    const data = performanceMonitor.exportData();
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `performance-metrics-${new Date().toISOString()}.json`;
    a.click();
    URL.revokeObjectURL(url);
  };

  // Não renderizar em produção
  if (process.env.NODE_ENV === 'production' || !isVisible) {
    return null;
  }

  return (
    <div className="fixed top-4 right-4 w-96 max-h-[80vh] bg-background border border-border rounded-lg shadow-lg z-50">
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center gap-2">
          <BarChart3 className="h-5 w-5" />
          <h3 className="font-semibold">Performance Monitor</h3>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setAutoRefresh(!autoRefresh)}
            className={autoRefresh ? 'text-green-600' : 'text-gray-400'}
          >
            <RefreshCw className={`h-4 w-4 ${autoRefresh ? 'animate-spin' : ''}`} />
          </Button>
          <Button variant="ghost" size="sm" onClick={handleRefresh}>
            <Eye className="h-4 w-4" />
          </Button>
          {onClose && (
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>

      <ScrollArea className="h-[60vh]">
        <div className="p-4 space-y-4">
          {/* Resumo Geral */}
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm">Resumo Geral</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="flex justify-between text-xs">
                <span>Total de componentes:</span>
                <span className="font-mono">{metrics.length}</span>
              </div>
              <div className="flex justify-between text-xs">
                <span>Componentes otimizados:</span>
                <span className="font-mono text-green-600">{comparison.optimized.length}</span>
              </div>
              <div className="flex justify-between text-xs">
                <span>Componentes não otimizados:</span>
                <span className="font-mono text-red-600">{comparison.nonOptimized.length}</span>
              </div>
            </CardContent>
          </Card>

          {/* Melhorias de Performance */}
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm flex items-center gap-2">
                <TrendingUp className="h-4 w-4" />
                Melhorias de Performance
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="flex justify-between text-xs">
                <span>Redução tempo de render:</span>
                <span className="font-mono text-green-600">
                  {comparison.improvement.averageRenderTimeReduction.toFixed(1)}%
                </span>
              </div>
              <div className="flex justify-between text-xs">
                <span>Redução re-renderizações:</span>
                <span className="font-mono text-green-600">
                  {comparison.improvement.renderCountReduction.toFixed(1)}%
                </span>
              </div>
            </CardContent>
          </Card>

          {/* Top Componentes */}
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm">Top Componentes (Renders)</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-1">
                {metrics
                  .sort((a, b) => b.renderCount - a.renderCount)
                  .slice(0, 5)
                  .map((metric, index) => (
                    <div key={metric.componentName} className="flex justify-between text-xs">
                      <span className="truncate flex-1">
                        {index + 1}. {metric.componentName}
                        {metric.isOptimized && <span className="text-green-600 ml-1">✓</span>}
                      </span>
                      <span className="font-mono ml-2">{metric.renderCount}</span>
                    </div>
                  ))}
              </div>
            </CardContent>
          </Card>

          {/* Componentes Mais Lentos */}
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm flex items-center gap-2">
                <Clock className="h-4 w-4" />
                Componentes Mais Lentos
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-1">
                {metrics
                  .sort((a, b) => b.averageRenderTime - a.averageRenderTime)
                  .slice(0, 5)
                  .map((metric, index) => (
                    <div key={metric.componentName} className="flex justify-between text-xs">
                      <span className="truncate flex-1">
                        {index + 1}. {metric.componentName}
                        {metric.isOptimized && <span className="text-green-600 ml-1">✓</span>}
                      </span>
                      <span className="font-mono ml-2">
                        {metric.averageRenderTime.toFixed(1)}ms
                      </span>
                    </div>
                  ))}
              </div>
            </CardContent>
          </Card>

          {/* Eventos Recentes */}
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm">Eventos Recentes</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-1 max-h-32 overflow-y-auto">
                {recentEvents.map((event, index) => (
                  <div key={index} className="flex justify-between text-xs">
                    <span className="truncate flex-1">
                      {event.componentName}
                      {event.isOptimized && <span className="text-green-600 ml-1">✓</span>}
                    </span>
                    <span className="font-mono ml-2">{event.renderTime.toFixed(1)}ms</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Ações */}
          <div className="flex gap-2">
            <Button variant="outline" size="sm" onClick={handleClear} className="flex-1">
              Limpar
            </Button>
            <Button variant="outline" size="sm" onClick={handleExport} className="flex-1">
              Exportar
            </Button>
          </div>
        </div>
      </ScrollArea>
    </div>
  );
}

/**
 * Hook para controlar a visibilidade do painel de debug
 */
export function usePerformanceDebugPanel() {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Ctrl + Shift + P para toggle do painel
      if (event.ctrlKey && event.shiftKey && event.key === 'P') {
        event.preventDefault();
        setIsVisible(prev => !prev);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, []);

  return {
    isVisible,
    show: () => setIsVisible(true),
    hide: () => setIsVisible(false),
    toggle: () => setIsVisible(prev => !prev),
  };
}
