/**
 * Script simples para testar a conectividade com o banco de dados
 */

const { PrismaClient } = require('@prisma/client');

// Inicializar cliente Prisma
const prisma = new PrismaClient();

/**
 * Função principal
 */
async function main() {
  console.log('=== TESTE DE CONEXÃO COM BANCO DE DADOS ===\n');

  try {
    // 1. Verificar conexão com o banco de dados
    console.log('Verificando conexão com o banco de dados...');

    // Tentativa de contagem de registros
    const workbookCount = await prisma.workbook.count();
    console.log(`✅ Conexão OK. Existem ${workbookCount} workbooks no banco de dados.`);

    // 2. Listar workbooks existentes
    console.log('\nListando workbooks existentes:');

    const workbooks = await prisma.workbook.findMany({
      include: {
        sheets: true,
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      take: 10, // Limitar a 10 registros
    });

    if (workbooks.length > 0) {
      workbooks.forEach((workbook, index) => {
        console.log(`\nWorkbook ${index + 1}:`);
        console.log(`  ID: ${workbook.id}`);
        console.log(`  Nome: ${workbook.name}`);
        console.log(`  Criado: ${workbook.createdAt}`);
        console.log(`  Número de planilhas: ${workbook.sheets?.length || 0}`);

        if (workbook.user) {
          console.log(
            `  Usuário: ${workbook.user.name || 'Anônimo'} (${workbook.user.email || 'Sem email'})`
          );
        }
      });
    } else {
      console.log('Nenhum workbook encontrado.');

      // 3. Criar um workbook de teste
      console.log('\nCriando workbook de teste...');

      const testWorkbook = await prisma.workbook.create({
        data: {
          name: 'Workbook de Teste DB',
          userId: 'test-user', // Usuário de teste
          sheets: {
            create: {
              name: 'Planilha de Teste',
              data: JSON.stringify({
                headers: ['Coluna A', 'Coluna B', 'Coluna C'],
                rows: [
                  ['Valor A1', 'Valor B1', 'Valor C1'],
                  ['Valor A2', 'Valor B2', 'Valor C2'],
                ],
              }),
            },
          },
        },
      });

      console.log(`✅ Workbook criado com ID: ${testWorkbook.id}`);
    }

    // 4. Verificar histórico de chat
    console.log('\nVerificando histórico de chat:');

    const chatHistory = await prisma.chatHistory.findMany({
      take: 5,
      orderBy: {
        createdAt: 'desc',
      },
    });

    if (chatHistory.length > 0) {
      chatHistory.forEach((entry, index) => {
        console.log(`\nMensagem ${index + 1}:`);
        console.log(`  Workbook: ${entry.workbookId || 'N/A'}`);
        console.log(`  Mensagem: ${entry.message}`);
        console.log(`  Resposta: ${entry.response}`);
        console.log(`  Data: ${entry.createdAt}`);
      });
    } else {
      console.log('Nenhum histórico de chat encontrado');

      // Adicionar uma entrada de teste ao histórico
      const workbooks = await prisma.workbook.findMany({ take: 1 });

      if (workbooks.length > 0) {
        await prisma.chatHistory.create({
          data: {
            userId: 'test-user',
            workbookId: workbooks[0].id,
            message: 'Teste de conexão com banco de dados',
            response: 'Conexão bem-sucedida!',
          },
        });

        console.log('✅ Entrada de teste adicionada ao histórico de chat');
      }
    }

    console.log('\n✅ TESTE DE BANCO DE DADOS CONCLUÍDO COM SUCESSO');
  } catch (error) {
    console.error('❌ Erro no teste:', error);
  } finally {
    // Fechar conexão com o banco de dados
    await prisma.$disconnect();
  }
}

// Executar função principal
main().catch(error => {
  console.error('❌ Erro fatal:', error);
  process.exit(1);
});
