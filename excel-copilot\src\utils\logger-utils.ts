/**
 * Utilitários para tipagem segura de logs
 *
 * Este arquivo fornece funções para garantir tipagem correta ao usar o logger,
 * especialmente para lidar com dados desconhecidos (unknown).
 */

import { logger } from '../lib/logger';

// Tipo para metadados de log aceitos pelo logger
export type LogMetadata = Record<string, any>;

/**
 * Converte um valor desconhecido para um objeto Error tipado ou undefined
 * @param value Valor potencialmente um erro
 * @returns Error tipado ou undefined
 */
export function toErrorType(value: unknown): Error | undefined {
  if (!value) return undefined;

  if (value instanceof Error) {
    return value;
  }

  if (typeof value === 'string') {
    return new Error(value);
  }

  try {
    const errorMessage = typeof value === 'object' ? JSON.stringify(value) : String(value);

    return new Error(errorMessage);
  } catch {
    return new Error('Erro não serializável');
  }
}

/**
 * Converte um valor desconhecido para um objeto de metadados válido
 * @param value Valor potencialmente um objeto de metadados
 * @returns Objeto de metadados válido ou undefined
 */
export function toLogMetadata(value: unknown): LogMetadata | undefined {
  if (!value) return undefined;

  if (typeof value === 'object' && value !== null) {
    // Se for um Error, extrair informações úteis
    if (value instanceof Error) {
      return {
        message: value.message,
        name: value.name,
        stack: value.stack,
      };
    }

    // Para outros objetos, tentar usar diretamente
    return value as LogMetadata;
  }

  // Para tipos primitivos, envolver em um objeto
  return { value };
}

/**
 * Versão tipada da função log.info
 * @param message Mensagem de log
 * @param metadata Metadados adicionais (aceitando unknown)
 */
export function logInfo(message: string, metadata?: unknown): void {
  logger.info(message, toLogMetadata(metadata));
}

/**
 * Versão tipada da função log.warn
 * @param message Mensagem de log
 * @param metadata Metadados adicionais (aceitando unknown)
 */
export function logWarn(message: string, metadata?: unknown): void {
  logger.warn(message, toLogMetadata(metadata));
}

/**
 * Versão tipada da função log.error
 * @param message Mensagem de log
 * @param error Erro (aceitando unknown)
 */
export function logError(message: string, error?: unknown): void {
  logger.error(message, toErrorType(error));
}

/**
 * Versão tipada da função log.debug
 * @param message Mensagem de log
 * @param metadata Metadados adicionais (aceitando unknown)
 */
export function logDebug(message: string, metadata?: unknown): void {
  logger.debug(message, toLogMetadata(metadata));
}

/**
 * Versão tipada da função log.fatal
 * @param message Mensagem de log
 * @param error Erro (aceitando unknown)
 */
export function logFatal(message: string, error?: unknown): void {
  logger.fatal(message, toErrorType(error));
}

/**
 * Versão tipada da função log.trace
 * @param message Mensagem de log
 * @param metadata Metadados adicionais (aceitando unknown)
 */
export function logTrace(message: string, metadata?: unknown): void {
  logger.trace(message, toLogMetadata(metadata));
}

/**
 * Wrapper tipado para o logger inteiro
 */
export const typedLogger = {
  info: logInfo,
  warn: logWarn,
  error: logError,
  debug: logDebug,
  fatal: logFatal,
  trace: logTrace,
};
