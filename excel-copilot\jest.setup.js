/**
 * Configuração global para testes Jest
 *
 * Este arquivo configura o ambiente de teste, inicializando serviços
 * necessários para todos os testes e definindo configurações globais.
 */

// Importar matchers do jest-dom
require('@testing-library/jest-dom');

// Aumentar o timeout para testes mais complexos
jest.setTimeout(30000);

// Evitar carregamento do server MSW se Response não estiver definido
if (typeof Response !== 'undefined') {
  try {
    const { server } = require('./__tests__/mocks/server');

    beforeAll(() => {
      console.log('Starting global mock server...');
      server.listen();
    });

    afterEach(() => {
      console.log('Resetting mock handlers...');
      server.resetHandlers();
    });

    afterAll(() => {
      console.log('Closing global mock server...');
      server.close();
    });
  } catch (error) {
    console.warn('Mock server não disponível:', error.message);
  }
} else {
  console.warn('Response não está definido, pulando configuração do MSW');
}

// Suprimir logs durante os testes
const originalConsoleLog = console.log;
let mockConsoleWarn;
let mockConsoleError;

// Se a variável de ambiente ENABLE_TEST_LOGS não estiver definida, vamos silenciar logs
if (!process.env.ENABLE_TEST_LOGS) {
  global.console.log = jest.fn();
  mockConsoleWarn = console.warn;
  mockConsoleError = console.error;
  console.warn = jest.fn();
  console.error = jest.fn();
}

// Restaurar os logs originais após os testes
afterAll(() => {
  global.console.log = originalConsoleLog;
  console.warn = mockConsoleWarn;
  console.error = mockConsoleError;
});

// MockDate para garantir timestamps consistentes em testes
try {
  const MockDate = require('mockdate');
  // Define uma data fixa para todos os testes
  const fixedDate = new Date('2023-05-15T10:00:00Z');

  beforeAll(() => {
    MockDate.set(fixedDate);
  });

  afterAll(() => {
    MockDate.reset();
  });
} catch (error) {
  console.warn('MockDate não está disponível:', error.message);
}

// Configurar variáveis de ambiente para teste
process.env.NEXT_PUBLIC_API_URL = 'http://localhost:3000';
process.env.NEXT_PUBLIC_APP_ENV = 'test';
process.env.AI_ENABLED = 'false';
process.env.AI_ENABLED = 'true';
process.env.AI_VERTEX_PROJECT_ID = 'excel-copilot';
process.env.AI_VERTEX_LOCATION = 'us-central1';
process.env.AI_VERTEX_MODEL = 'gemini-2.0-flash-001';
process.env.GOOGLE_APPLICATION_CREDENTIALS = './vertex-credentials.json';

// Adicionar variáveis do Upstash Redis para testes
process.env.UPSTASH_REDIS_REST_URL = 'https://cunning-pup-26344.upstash.io';
process.env.UPSTASH_REDIS_REST_TOKEN = 'AWboAAIjcDFkNjhiODgzNTEwMWE0MTQ5ODg0YTFhZDM3NjY5YTlmYXAxMA';

// Adicionar variáveis do Supabase/PostgreSQL para testes
process.env.POSTGRES_HOST = 'db.cunning-pup-26344.supabase.co';
process.env.POSTGRES_USER = 'postgres';
process.env.POSTGRES_DATABASE = 'postgres';
process.env.DB_DATABASE_URL =
  'postgresql://postgres:<EMAIL>:5432/postgres';
process.env.POSTGRES_URL =
  'postgresql://postgres:<EMAIL>:5432/postgres';
process.env.POSTGRES_PRISMA_URL =
  'postgresql://postgres:<EMAIL>:5432/postgres?pgbouncer=true';
process.env.POSTGRES_URL_NON_POOLING =
  'postgresql://postgres:<EMAIL>:5432/postgres';
process.env.DB_DIRECT_URL =
  'postgresql://postgres:<EMAIL>:5432/postgres';
process.env.SUPABASE_URL = 'https://cunning-pup-26344.supabase.co';
process.env.SUPABASE_ANON_KEY =
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImN1bm5pbmctcHVwLTI2MzQ0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE2ODkwODk2MzksImV4cCI6MjAwNDY2NTYzOX0.dS';
process.env.SUPABASE_SERVICE_ROLE_KEY =
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImN1bm5pbmctcHVwLTI2MzQ0Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTY4OTA4OTYzOSwiZXhwIjoyMDA0NjY1NjM5fQ.aW';

// Adicionar variáveis de autenticação OAuth para testes
process.env.AUTH_NEXTAUTH_URL = 'http://localhost:3000';
process.env.AUTH_NEXTAUTH_SECRET = 'MgnbS9kIQOSKndKsfO1lWIRN7oMw4tdU2MbBV+Iq2dU=';
process.env.AUTH_GOOGLE_CLIENT_ID =
  '578421141166-3d5os0jkm2rhlk2lcgh0jdsidrrc7t31.apps.googleusercontent.com';
process.env.AUTH_GOOGLE_CLIENT_SECRET = '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';
process.env.AUTH_GITHUB_CLIENT_ID = '********************';
process.env.AUTH_GITHUB_CLIENT_SECRET = '3a1a6bec15581e9bc6d531ca0c7307b4a03aa2c9';

// Adicionar variáveis OAuth sem prefixo AUTH_ para compatibilidade
process.env.GOOGLE_CLIENT_ID = process.env.AUTH_GOOGLE_CLIENT_ID;
process.env.GOOGLE_CLIENT_SECRET = process.env.AUTH_GOOGLE_CLIENT_SECRET;
process.env.GITHUB_CLIENT_ID = process.env.AUTH_GITHUB_CLIENT_ID;
process.env.GITHUB_CLIENT_SECRET = process.env.AUTH_GITHUB_CLIENT_SECRET;
process.env.DATABASE_URL = process.env.DB_DATABASE_URL;

// Adicionar variáveis específicas para testes de integração
process.env.NEXTAUTH_URL = process.env.AUTH_NEXTAUTH_URL;
process.env.NEXTAUTH_SECRET = process.env.AUTH_NEXTAUTH_SECRET;

// Extensões para o Jest
const { TextDecoder, TextEncoder } = require('util');

global.TextEncoder = TextEncoder;
global.TextDecoder = TextDecoder;

// Mock do objeto window quando não disponível (node)
global.window = global.window || {
  localStorage: {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn(),
    clear: jest.fn(),
  },
  sessionStorage: {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn(),
    clear: jest.fn(),
  },
  addEventListener: jest.fn(),
  removeEventListener: jest.fn(),
  dispatchEvent: jest.fn(),
  matchMedia: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
};

// Mock para Stripe para evitar erros de credenciais
jest.mock('stripe', () => {
  return jest.fn().mockImplementation(() => ({
    customers: {
      create: jest.fn().mockResolvedValue({ id: 'cus_mock123' }),
      list: jest.fn().mockResolvedValue({ data: [] }),
    },
    checkout: {
      sessions: {
        create: jest.fn().mockResolvedValue({ id: 'cs_mock123', url: 'https://mock-checkout.url' }),
      },
    },
    subscriptions: {
      list: jest.fn().mockResolvedValue({ data: [] }),
      create: jest.fn().mockResolvedValue({ id: 'sub_mock123' }),
      update: jest.fn().mockResolvedValue({ id: 'sub_mock123' }),
    },
    billingPortal: {
      sessions: {
        create: jest.fn().mockResolvedValue({ url: 'https://mock-billing-portal.url' }),
      },
    },
    products: {
      list: jest.fn().mockResolvedValue({ data: [] }),
    },
    prices: {
      list: jest.fn().mockResolvedValue({ data: [] }),
    },
  }));
});

// Mock para o servidor
jest.mock('@/server/trpc/react', () => ({
  TRPCReactProvider: ({ children }) => children,
}));

// Mock para o serviço de IA
jest.mock('@/server/ai/vertex-ai-service', () => {
  return {
    VertexAIService: {
      getInstance: jest.fn().mockReturnValue({
        sendMessage: jest.fn().mockResolvedValue('Resposta mock do Vertex AI'),
        isServiceAvailable: jest.fn().mockResolvedValue(true),
      }),
    },
    GeminiServiceError: class GeminiServiceError extends Error {
      constructor(message, type = 'unknown', details = null, recoverable = false) {
        super(message);
        this.name = 'GeminiServiceError';
        this.type = type;
        this.details = details;
        this.recoverable = recoverable;
      }
    },
    GeminiErrorType: {
      TIMEOUT: 'timeout',
      API_UNAVAILABLE: 'api_unavailable',
      INVALID_REQUEST: 'invalid_request',
      CONTENT_FILTERED: 'content_filtered',
      RATE_LIMITED: 'rate_limited',
      UNKNOWN: 'unknown',
      INVALID_RESPONSE_FORMAT: 'invalid_response_format',
      CONTEXT_LIMIT_EXCEEDED: 'context_limit_exceeded',
      TOKEN_LIMIT_EXCEEDED: 'token_limit_exceeded',
      RETRY_FAILED: 'retry_failed',
    },
  };
});

// Mock para o módulo de root da API
jest.mock('@/server/api/root', () => {
  return {
    appRouter: {
      createCaller: jest.fn().mockReturnValue({
        workbook: {
          getAll: jest.fn().mockResolvedValue([]),
          getById: jest.fn().mockResolvedValue(null),
          getTemplates: jest.fn().mockResolvedValue([]),
        },
      }),
    },
  };
});

// Mock para o módulo de operações do processor
jest.mock('@/lib/operations/processor', () => ({
  extractOperations: jest.fn().mockImplementation(() => [
    {
      type: 'COLUMN_OPERATION',
      data: {
        operation: 'SUM',
        column: 'A',
      },
    },
  ]),
}));

// Mock para o banco de dados Prisma
jest.mock('@/server/db/client', () => {
  return {
    prisma: {
      workbook: {
        findMany: jest.fn().mockResolvedValue([]),
        findUnique: jest.fn().mockResolvedValue(null),
        create: jest.fn().mockResolvedValue({ id: 'mock-workbook-id' }),
        update: jest.fn().mockResolvedValue(null),
        delete: jest.fn().mockResolvedValue(null),
      },
      user: {
        findUnique: jest.fn().mockResolvedValue({ id: 'mock-user-id' }),
        update: jest.fn().mockResolvedValue(null),
      },
      sheet: {
        findMany: jest.fn().mockResolvedValue([]),
        create: jest.fn().mockResolvedValue(null),
      },
      subscription: {
        findUnique: jest.fn().mockResolvedValue(null),
        create: jest.fn().mockResolvedValue(null),
        update: jest.fn().mockResolvedValue(null),
      },
      $connect: jest.fn(),
      $disconnect: jest.fn(),
    },
  };
});

// Configurar mock para servidor
global.server = {
  handleRequest: jest.fn().mockImplementation((path, method, body) => {
    // Mock para diferentes endpoints
    if (path.includes('/api/workbook') || path.includes('/api/excel')) {
      return {
        status: 200,
        body: {
          success: true,
          data: { id: 'mock-workbook-id', sheets: [] },
        },
      };
    }

    if (path.includes('/api/auth')) {
      return {
        status: 200,
        body: {
          success: true,
          user: { id: 'mock-user-id', email: '<EMAIL>' },
        },
      };
    }

    // Default para outros endpoints
    return {
      status: 404,
      body: {
        success: false,
        error: 'Endpoint não encontrado',
      },
    };
  }),
};

// Mock para o next/navigation
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    prefetch: jest.fn(),
    back: jest.fn(),
    forward: jest.fn(),
    refresh: jest.fn(),
    pathname: '/',
    query: {},
  }),
  useSearchParams: () => ({
    get: jest.fn(),
  }),
  usePathname: () => '/',
  useParams: () => ({}),
}));

// Mock para next-auth
jest.mock('next-auth/react', () => ({
  useSession: () => ({
    data: {
      user: {
        id: 'test-user-id',
        name: 'Test User',
        email: '<EMAIL>',
        image: null,
      },
    },
    status: 'authenticated',
  }),
  signIn: jest.fn(),
  signOut: jest.fn(),
  getSession: jest.fn(),
}));

// Mock para o context do ToastProvider
jest.mock('@/components/ui/use-toast', () => ({
  useToast: () => ({
    toast: jest.fn(),
  }),
}));

// Adicionar fetch global para testes
global.fetch = jest.fn();

// Mock para BroadcastChannel que não existe no ambiente jest-jsdom
class MockBroadcastChannel {
  constructor(name) {
    this.name = name;
  }
  postMessage() {}
  close() {}
  addEventListener() {}
  removeEventListener() {}
  dispatchEvent() {
    return true;
  }
}

// Adiciona propriedades para compatibilidade com a API
Object.defineProperties(MockBroadcastChannel.prototype, {
  onmessage: {
    get() {
      return null;
    },
    set() {},
  },
  onmessageerror: {
    get() {
      return null;
    },
    set() {},
  },
});

global.BroadcastChannel = MockBroadcastChannel;

// Mock para IntersectionObserver
class MockIntersectionObserver {
  constructor(callback) {
    this.callback = callback;
  }
  observe() {}
  unobserve() {}
  disconnect() {}
}

global.IntersectionObserver = MockIntersectionObserver;

// Armazenar a implementação original do console.error
let originalConsoleError = console.error;

// Substituir console.error para suprimir mensagens específicas durante os testes
console.error = (...args) => {
  // Suprimir warnings específicos que sabemos serem falsos positivos
  if (
    typeof args[0] === 'string' &&
    (args[0].includes('Warning: ReactDOM.render is no longer supported') ||
      args[0].includes('Warning: Using UNSAFE_') ||
      args[0].includes('Warning: findDOMNode is deprecated') ||
      args[0].includes('Warning: React does not recognize the') ||
      args[0].includes('Warning: The tag <null> is unrecognized'))
  ) {
    return;
  }
  // Para todos os outros erros, usar o comportamento original
  originalConsoleError(...args);
};

// Repetir o mesmo padrão para console.warn
let originalConsoleWarn = console.warn;
console.warn = (...args) => {
  // Suprimir warnings específicos
  if (
    typeof args[0] === 'string' &&
    (args[0].includes('Warning: useLayoutEffect does nothing on the server') ||
      args[0].includes('Warning: React does not recognize the'))
  ) {
    return;
  }
  originalConsoleWarn(...args);
};

// Restaurar as funções originais após os testes
afterAll(() => {
  console.error = originalConsoleError;
  console.warn = originalConsoleWarn;
});

// Configurar matchMedia para testes
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // Obsoleto
    removeListener: jest.fn(), // Obsoleto
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// Configuração para o ResizeObserver
class ResizeObserverMock {
  observe = jest.fn();
  unobserve = jest.fn();
  disconnect = jest.fn();
}

window.ResizeObserver = ResizeObserverMock;

// Implementação mock para IntersectionObserver
class IntersectionObserverMock {
  constructor(callback) {
    this.callback = callback;
  }
  observe = jest.fn();
  unobserve = jest.fn();
  disconnect = jest.fn();
  // Método para simular entradas sendo observadas
  mockEntries(entries) {
    this.callback(entries, this);
  }
}

window.IntersectionObserver = IntersectionObserverMock;

// Configurar getComputedStyle
Object.defineProperty(window, 'getComputedStyle', {
  value: () => ({
    getPropertyValue: prop => {
      return '';
    },
  }),
});

// Configurações adicionais para WebCrypto API
if (typeof crypto === 'undefined' || !crypto.subtle) {
  const nodeCrypto = require('crypto');

  Object.defineProperty(global, 'crypto', {
    value: {
      getRandomValues: arr => {
        return nodeCrypto.randomFillSync(arr);
      },
      subtle: {
        // Implementações básicas apenas para mock
        digest: async (algorithm, data) => {
          return Buffer.from('mockhash');
        },
        // Outras operações conforme necessário
      },
    },
  });
}

// Implementação para fetch quando não disponível
if (typeof fetch === 'undefined') {
  global.fetch = jest.fn();
  global.Response = jest.fn();
  global.Headers = jest.fn();
  global.Request = jest.fn();
}

// Mock para localStorage quando necessário
if (typeof localStorage === 'undefined') {
  let store = {};
  Object.defineProperty(global, 'localStorage', {
    value: {
      getItem: jest.fn(key => store[key] || null),
      setItem: jest.fn((key, value) => {
        store[key] = String(value);
      }),
      removeItem: jest.fn(key => {
        delete store[key];
      }),
      clear: jest.fn(() => {
        store = {};
      }),
      length: 0,
      key: jest.fn(() => null),
    },
    writable: true,
  });
}

// Segunda parte de configuração - substituir apenas para certos testes específicos
// Usar nome diferente para evitar conflito de redeclaração
let setupConsoleError = console.error;

// Função auxiliar para silenciar erros em testes específicos
global.silenceConsoleErrors = () => {
  console.error = jest.fn();
};

// Função para restaurar o console.error original
global.restoreConsoleErrors = () => {
  console.error = setupConsoleError;
};

// Extended expect matchers
expect.extend({
  toContainObject(received, expected) {
    const pass = this.equals(received, expect.arrayContaining([expect.objectContaining(expected)]));
    return {
      pass,
      message: () => `expected ${this.utils.printReceived(received)} 
        ${pass ? 'not ' : ''}to contain object ${this.utils.printExpected(expected)}`,
    };
  },
});
