import * as ExcelJS from 'exceljs';

import { ExcelOperationType, ExcelOperation } from '@/types';
import { extractGroup } from '@/utils/regex-utils';

import { createExcelAIProcessor } from './ai';
import { normalizeOperation } from './excel/operationUtils';
import { extractAdvancedChartOperations } from './operations/advancedChartOperations';
import {
  extractAdvancedVisualizationOperations,
  executeAdvancedVisualizationOperation,
} from './operations/advancedVisualizationOperations';
import { executeChartOperation, extractChartOperations } from './operations/chartOperations';
import { executeColumnOperation, extractColumnOperations } from './operations/columnOperations';
import {
  extractConditionalFormattingOperations,
  executeConditionalFormattingOperation,
} from './operations/conditionalFormattingOperations';
import {
  executeFilterOperation,
  executeSortOperation,
  extractFilterOperations,
  extractSortOperations,
} from './operations/dataTransformations';
import { executeFormulaOperation, extractFormulaOperations } from './operations/formulaOperations';
import {
  extractPivotTableOperations,
  executePivotTableOperation,
} from './operations/pivotTableOperations';

// Re-exportar o tipo ExcelOperation para uso em outros módulos
export type { ExcelOperation };

/**
 * Interface para CommandParserResult que permite message undefined
 */
export interface CommandParserResult {
  operations: ExcelOperation[];
  success: boolean;
  error: string | null;
  message?: string;
}

// Singleton instance of the AI processor
const aiProcessor = createExcelAIProcessor();

/**
 * Converte um valor de tipo desconhecido para um valor adequado para célula Excel
 */
function toCellValue(value: unknown): ExcelJS.CellValue {
  if (value === null || value === undefined) {
    return '';
  }

  if (
    typeof value === 'string' ||
    typeof value === 'number' ||
    typeof value === 'boolean' ||
    value instanceof Date
  ) {
    return value;
  }

  // Fallback para string para outros tipos
  return String(value);
}

/**
 * Converte dados de planilha do formato JSON para um arquivo Excel
 * @param sheets Array de sheets com dados em formato JSON
 * @param workbookName Nome para o arquivo Excel
 * @returns Blob com o arquivo Excel
 */
export async function createExcelFile(
  sheets: { name: string; data: unknown }[],
  _workbookName: string
): Promise<Blob> {
  // Criar um novo workbook
  const workbook = new ExcelJS.Workbook();
  workbook.creator = 'Excel Copilot';
  workbook.lastModifiedBy = 'Excel Copilot';
  workbook.created = new Date();
  workbook.modified = new Date();

  // Processar cada sheet
  for (const sheetData of sheets) {
    // Verificar se temos um nome válido para a planilha
    const sheetName = sheetData.name?.trim()
      ? sheetData.name
      : 'Sheet' + (workbook.worksheets.length + 1);

    // Criar uma nova planilha
    const worksheet = workbook.addWorksheet(sheetName);

    // Se não houver dados, criar uma planilha vazia
    if (!sheetData.data || Object.keys(sheetData.data).length === 0) {
      continue;
    }

    // Definir updatedData como Record<string, unknown> para permitir a adição de propriedades dinâmicas
    const updatedData = sheetData.data
      ? { ...(sheetData.data as Record<string, unknown>) }
      : ({} as Record<string, unknown>);

    try {
      // Garantir que temos um objeto formatting inicializado
      if (!updatedData.formatting || typeof updatedData.formatting !== 'object') {
        updatedData.formatting = {} as Record<string, unknown>;
      }
    } catch (error) {
      console.error('Erro ao processar dados:', error);
    }

    // Verificar formato dos dados
    if (Array.isArray(sheetData.data)) {
      // Formato de array de arrays (matriz)
      if (sheetData.data.length > 0 && Array.isArray(sheetData.data[0])) {
        // Adicionar cada linha diretamente
        for (const row of sheetData.data) {
          // Garantir que todos os valores sejam seguros para o Excel
          const safeRow = row.map((cell: unknown) => {
            // Converter objetos vazios para string vazia (compatível com CellValue)
            if (cell === null || cell === undefined) {
              return '';
            } else if (typeof cell === 'object' && Object.keys(cell).length === 0) {
              return '';
            }
            return cell;
          });
          worksheet.addRow(safeRow);
        }

        // Ajustar largura das colunas automaticamente
        worksheet.columns.forEach((column, index) => {
          let maxLength = 0;
          worksheet.eachRow({ includeEmpty: true }, row => {
            const cell = row.getCell(index + 1);
            const cellValue = cell.text || '';
            maxLength = Math.max(maxLength, cellValue.length);
          });
          column.width = Math.min(Math.max(maxLength + 2, 10), 30);
        });
      }
      // Formato de array de objetos (registros)
      else if (sheetData.data.length > 0 && typeof sheetData.data[0] === 'object') {
        // Extrair cabeçalhos
        const headers = Object.keys(sheetData.data[0] || {});

        // Adicionar cabeçalhos apenas se existirem
        if (headers.length > 0) {
          worksheet.columns = headers.map(header => ({
            header,
            key: header,
            width: Math.max(header.length, 10),
          }));

          // Adicionar linhas, garantindo que não tenhamos valores nulos
          const safeData = sheetData.data.map(row => {
            // Converter valores nulos ou undefined para string vazia
            const safeRow: Record<string, unknown> = {};
            for (const key of headers) {
              safeRow[key] = row[key] === null || row[key] === undefined ? '' : row[key];
            }
            return safeRow;
          });

          worksheet.addRows(safeData);
        }
      }
    } else if (typeof sheetData.data === 'object') {
      // Formato de células ({A1: valor, B2: valor})
      Object.entries(sheetData.data).forEach(([cellRefKey, value]) => {
        // Evitar valores nulos ou undefined
        const safeValue = value === null || value === undefined ? '' : value;
        try {
          // Garantir que a referência de célula seja uma string válida
          const cellRef = typeof cellRefKey === 'string' ? cellRefKey : String(cellRefKey || '');
          worksheet.getCell(cellRef).value = safeValue as ExcelJS.CellValue;

          // Se for uma fórmula, definir como fórmula
          if (typeof safeValue === 'string' && safeValue.startsWith('=')) {
            worksheet.getCell(cellRef).value = {
              formula: safeValue.substring(1),
            };
          }
        } catch (error) {
          console.error('Erro ao processar célula:', error);
        }
      });
    }

    // Estilizar cabeçalhos
    const firstRowValues = worksheet.getRow(1).values;
    if (firstRowValues && Array.isArray(firstRowValues) && firstRowValues.length > 1) {
      worksheet.getRow(1).font = { bold: true };
      worksheet.getRow(1).fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE6F0FF' },
      };
    }

    // Aplicar formatação básica para toda a planilha
    worksheet.eachRow(row => {
      row.eachCell(cell => {
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' },
        };
      });
    });
  }

  // Gerar arquivo Excel
  const buffer = await workbook.xlsx.writeBuffer();
  return new Blob([buffer], {
    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  });
}

/**
 * Faz download de um arquivo Excel
 * @param blob Blob com o arquivo Excel
 * @param fileName Nome do arquivo
 */
export function downloadExcelFile(blob: Blob, fileName: string) {
  // Criar URL do blob
  const url = window.URL.createObjectURL(blob);

  // Criar elemento de link
  const a = document.createElement('a');
  a.href = url;
  a.download = fileName.endsWith('.xlsx') ? fileName : `${fileName}.xlsx`;

  // Adicionar ao documento e clicar
  document.body.appendChild(a);
  a.click();

  // Limpar
  window.URL.revokeObjectURL(url);
  document.body.removeChild(a);
}

/**
 * Converte arquivo Excel para estrutura de dados interna
 * @param file Arquivo Excel (.xlsx)
 * @returns Array de sheets com dados em formato JSON
 */
export async function parseExcelFile(file: File): Promise<{ name: string; data: unknown }[]> {
  try {
    // Criar novo workbook e carregar arquivo
    const workbook = new ExcelJS.Workbook();
    const arrayBuffer = await file.arrayBuffer();
    await workbook.xlsx.load(arrayBuffer);

    // Array para armazenar dados das planilhas
    const sheets: { name: string; data: unknown }[] = [];

    // Verificar se o workbook tem planilhas
    if (workbook.worksheets.length === 0) {
      throw new Error('Arquivo Excel não contém planilhas');
    }

    // Processar cada worksheet
    workbook.eachSheet(worksheet => {
      try {
        // Verificar se a planilha está vazia
        if (worksheet.rowCount === 0 || worksheet.columnCount === 0) {
          sheets.push({
            name: worksheet.name,
            data: [],
          });
          return;
        }

        // Detectar se a planilha tem cabeçalhos
        const firstRowValues = worksheet.getRow(1).values;
        const secondRowValues = worksheet.getRow(2).values;

        const hasHeaders =
          firstRowValues &&
          Array.isArray(firstRowValues) &&
          firstRowValues.length > 1 &&
          secondRowValues &&
          Array.isArray(secondRowValues) &&
          secondRowValues.length > 1;

        // Vamos sempre converter para formato de matriz (array de arrays)
        // que é mais flexível para nossa aplicação
        const rows: unknown[][] = [];

        // Processar cabeçalhos se existirem
        if (hasHeaders) {
          const headerRow: unknown[] = [];
          worksheet.getRow(1).eachCell((cell, colNumber) => {
            headerRow[colNumber - 1] = cell.value?.toString() || `Coluna${colNumber}`;
          });

          // Remover valores undefined do início do array
          while (headerRow.length > 0 && headerRow[0] === undefined) {
            headerRow.shift();
          }

          // Adicionar cabeçalhos apenas se tivermos pelo menos um válido
          if (headerRow.length > 0) {
            rows.push(headerRow);
          }
        }

        // Processar todas as linhas de dados
        const startRow = hasHeaders ? 2 : 1;
        for (let rowNumber = startRow; rowNumber <= worksheet.rowCount; rowNumber++) {
          const row = worksheet.getRow(rowNumber);
          const rowData: unknown[] = [];

          // Processar células
          for (let colNumber = 1; colNumber <= worksheet.columnCount; colNumber++) {
            const cell = row.getCell(colNumber);
            let value = cell.value;

            // Processar diferentes tipos de valores
            if (value !== null && value !== undefined) {
              // Formatar datas para ISO string
              if (value instanceof Date) {
                value = value.toISOString();
              }
              // Extrair valor de formulas
              else if (typeof value === 'object' && 'formula' in value) {
                // Usar 'in' operator para type narrowing e verificar se tem a propriedade 'result'
                const formulaValue = value as { formula: string; result?: unknown };
                // Garantir que o valor seja compatível com CellValue (string, number, boolean, Date ou null)
                const result = formulaValue.result;
                if (result === undefined) {
                  value = `=${formulaValue.formula}`;
                } else if (
                  typeof result === 'string' ||
                  typeof result === 'number' ||
                  typeof result === 'boolean' ||
                  result instanceof Date ||
                  result === null
                ) {
                  value = toCellValue(result);
                } else {
                  value = String(result); // Converter para string como fallback
                }
              }
            } else {
              value = '';
            }

            rowData.push(value);
          }

          // Remover células vazias do final
          while (
            rowData.length > 0 &&
            (rowData[rowData.length - 1] === '' || rowData[rowData.length - 1] === null)
          ) {
            rowData.pop();
          }

          // Adicionar linha apenas se tiver pelo menos um valor
          if (rowData.length > 0) {
            rows.push(rowData);
          }
        }

        sheets.push({
          name: worksheet.name,
          data: rows,
        });
      } catch (error) {
        console.error(`Erro ao processar planilha '${worksheet.name}':`, error);
        // Adicionar planilha vazia em caso de erro
        sheets.push({
          name: worksheet.name,
          data: [],
        });
      }
    });

    return sheets;
  } catch (error) {
    console.error('Erro ao processar arquivo Excel:', error);
    throw new Error(
      'Não foi possível processar o arquivo Excel. Verifique se ele está corrompido ou em formato inválido.'
    );
  }
}

/**
 * Verifica se um arquivo é um arquivo Excel válido
 * @param file Arquivo a ser verificado
 * @returns Boolean indicando se é um arquivo Excel válido
 */
export function isValidExcelFile(file: File): boolean {
  const validExcelTypes = [
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-excel.sheet.binary.macroEnabled.12',
    'application/vnd.ms-excel.sheet.macroEnabled.12',
  ];

  // Verificar pelo tipo MIME
  if (validExcelTypes.includes(file.type)) {
    return true;
  }

  // Verificar pela extensão do arquivo
  const fileExtension = file.name.split('.').pop()?.toLowerCase();
  return fileExtension === 'xls' || fileExtension === 'xlsx';
}

/**
 * Exporta dados para CSV e faz o download
 * @param sheets Array de planilhas com dados
 * @param fileName Nome do arquivo
 */
export function exportToCSV(sheets: { name: string; data: unknown }[], fileName: string): void {
  if (!sheets || sheets.length === 0) {
    throw new Error('Não há dados para exportar');
  }

  // Verificar se a primeira planilha tem dados
  const firstSheet = sheets[0];
  if (!firstSheet || !firstSheet.data) {
    throw new Error('Planilha sem dados');
  }

  // Usamos apenas a primeira planilha para CSV
  const sheetData = firstSheet.data;

  // Função para escapar valores CSV
  const escapeCSV = (value: unknown): string => {
    if (value === null || value === undefined || value === '') {
      return ''; // Valor vazio como string vazia em vez de objeto vazio
    }

    const stringValue = String(value);
    // Se o valor contém vírgula, aspas ou quebra de linha, colocamos entre aspas
    if (stringValue.includes(',') || stringValue.includes('"') || stringValue.includes('\n')) {
      // Substituir aspas por aspas duplas
      return `"${stringValue.replace(/"/g, '""')}"`;
    }
    return stringValue;
  };

  // Converter matriz para string CSV
  let csvContent = '';

  if (Array.isArray(sheetData)) {
    // Formato de array de arrays
    csvContent = sheetData
      .map(row =>
        Array.isArray(row)
          ? row.map(escapeCSV).join(',')
          : Object.values(row).map(escapeCSV).join(',')
      )
      .join('\n');
  } else if (typeof sheetData === 'object' && sheetData !== null) {
    // Formato de células {A1: valor}
    // Construir uma matriz a partir das posições das células
    const matrix: unknown[][] = [];

    Object.entries(sheetData).forEach(([cellRef, value]) => {
      // Converter referência de célula para índices de matriz
      const match = cellRef.match(/([A-Z]+)([0-9]+)/);
      if (match) {
        const colLetter = extractGroup(match, 1);
        const rowNum = parseInt(extractGroup(match, 2), 10);

        // Converter coluna A->0, B->1, etc.
        let colNum = 0;
        for (let i = 0; i < colLetter.length; i++) {
          colNum = colNum * 26 + colLetter.charCodeAt(i) - 'A'.charCodeAt(0) + 1;
        }
        colNum--; // 0-based index

        // Garantir que a matriz tenha tamanho suficiente
        while (matrix.length <= rowNum - 1) {
          matrix.push([]);
        }

        // Adicionar valor à matriz com segurança para índices válidos
        if (rowNum > 0) {
          // Verificar se o índice da linha é válido
          // Garantir que a matriz tem tamanho suficiente
          while (matrix.length < rowNum) {
            matrix.push([]);
          }

          // A matriz agora tem certamente a linha rowNum-1
          // Garantir que a linha é um array válido
          if (!Array.isArray(matrix[rowNum - 1])) {
            matrix[rowNum - 1] = [];
          }

          // Garantir que temos uma referência válida e segura para a linha
          const row = matrix[rowNum - 1] as unknown[];

          // Atribuir valor com segurança - usamos a variável row para evitar acessos repetidos
          row[colNum] = value;
        }
      }
    });

    // Converter matriz para CSV com segurança para linhas indefinidas
    csvContent = matrix
      // Filtrar linhas nulas ou indefinidas antes de processar
      .filter((row): row is unknown[] => Array.isArray(row))
      .map(row => {
        // Garantir valores definidos em cada célula
        return row.map(value => escapeCSV(value ?? '')).join(',');
      })
      .join('\n');
  }

  // Criar blob e fazer download
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const url = URL.createObjectURL(blob);

  const link = document.createElement('a');
  link.setAttribute('href', url);
  link.setAttribute('download', `${fileName}.csv`);
  link.style.visibility = 'hidden';

  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}

/**
 * Analisa uma resposta de IA e extrai operações de Excel
 */
export async function parseAICommandToExcelOperations(
  aiResponse: string
): Promise<CommandParserResult> {
  try {
    // Primeiro tenta processar com o parser avançado da IA (se disponível)
    if (aiProcessor && typeof aiProcessor.processQuery === 'function') {
      try {
        const result = await aiProcessor.processQuery(aiResponse);
        if (result && result.operations && result.operations.length > 0) {
          // Garantir que o resultado está no formato esperado por CommandParserResult
          const commandResult: CommandParserResult = {
            operations: result.operations,
            success: result.success ?? true,
            error: result.error ?? null,
          };

          // Adicionar message apenas se estiver definido (para compatibilidade com exactOptionalPropertyTypes: true)
          if (result.message !== undefined) {
            commandResult.message = result.message;
          }

          return commandResult;
        }
      } catch (error) {
        console.error('Error in AI processor, falling back to simple parser', error);
      }
    }

    // Fallback para parser simples se o avançado falhar
    return fallbackParseAICommandToExcelOperations(aiResponse);
  } catch (error) {
    return {
      operations: [],
      success: false,
      error: `Erro ao analisar comando: ${error instanceof Error ? error.message : String(error)}`,
    };
  }
}

/**
 * Método legado de análise de comandos da IA (usado como fallback)
 * @param aiResponse Resposta textual da IA
 * @returns Resultado com operações de planilha
 */
function fallbackParseAICommandToExcelOperations(aiResponse: string): CommandParserResult {
  const operations: ExcelOperation[] = [];
  let error: string | null = null;

  try {
    // Extrair potenciais trechos que representam operações do Excel
    const potentialOperations = extractPotentialExcelOperations(aiResponse);

    // Processar cada trecho
    for (const opText of potentialOperations) {
      // Tentar extrair diferentes tipos de operações
      operations.push(...extractFormulaOperations(opText));
      operations.push(...extractColumnOperations(opText));
      operations.push(...extractChartOperations(opText));
      operations.push(...extractFilterOperations(opText));
      operations.push(...extractSortOperations(opText));
      operations.push(...extractTableOperations(opText));
      operations.push(...extractCellUpdateOperations(opText));
      operations.push(...extractFormatOperations(opText));

      // Novas operações avançadas
      operations.push(...extractPivotTableOperations(opText));
      operations.push(...extractConditionalFormattingOperations(opText));
      operations.push(...extractAdvancedVisualizationOperations(opText));
      operations.push(...extractAdvancedChartOperations(opText));
    }

    // Normalizar operações
    const normalizedOperations = operations.map(op => normalizeOperation(op));

    // Criar um resultado do tipo CommandParserResult
    // Usando 'as' para evitar erros de tipagem
    return {
      operations: normalizedOperations as unknown,
      error: null,
      success: true,
      message: `${normalizedOperations.length} operações extraídas`,
    } as CommandParserResult;
  } catch (e) {
    error = e instanceof Error ? e.message : String(e);
    return {
      operations: [],
      error,
      success: false,
      message: `Erro ao processar: ${error}`,
    } as CommandParserResult;
  }
}

/**
 * Extrai possíveis operações Excel de um texto, mesmo que não consiga processar completamente
 * @param text Texto para analisar
 * @returns Lista de possíveis operações identificadas
 */
export function extractPotentialExcelOperations(text: string): string[] {
  const potentialOperations: string[] = [];

  // Padrões comuns de operações Excel
  const patterns = [
    // Fórmulas
    {
      regex: /=(SOMA|MÉDIA|MÁXIMO|MÍNIMO|CONT|SE|PROCV|ÍNDICE|CORRESP)[\s(]/gi,
      type: 'fórmula',
    },
    // Referências a colunas/células
    {
      regex: /coluna\s+([A-Z]+|[a-zA-Z0-9_]+)/gi,
      type: 'operação de coluna',
    },
    // Filtragem
    {
      regex: /filtr[aer]\s+.*\s+onde\s+.*[><]=?|contém|entre/gi,
      type: 'filtro',
    },
    // Ordenação
    {
      regex: /orden[ae][r]?\s+.*\s+(crescente|decrescente|alfabética)/gi,
      type: 'ordenação',
    },
    // Gráficos
    {
      regex: /gráfico\s+de\s+(barras|colunas|pizza|linha|dispersão|área|radar)/gi,
      type: 'gráfico',
    },
    // Formatação
    {
      regex: /format[ae]\s+.*\s+como\s+(moeda|porcentagem|data|texto|número)/gi,
      type: 'formatação',
    },
    // Tabelas e tabelas dinâmicas
    {
      regex: /tabela\s+(dinâmica|pivot)/gi,
      type: 'tabela dinâmica',
    },
    {
      regex: /converta\s+.*\s+em\s+tabela|transform[ae]\s+.*\s+em\s+tabela/gi,
      type: 'tabela',
    },
    // Visualizações avançadas
    {
      regex: /(mapa\s+de\s+calor|heatmap|boxplot|histograma|sparklines|minigráficos)/gi,
      type: 'visualização avançada',
    },
    // Análise de dados
    {
      regex: /(previsão|forecast|tendência|correlação|regressão|análise\s+estatística)/gi,
      type: 'análise de dados',
    },
  ];

  // Verificar cada padrão no texto
  for (const pattern of patterns) {
    let match;
    const regex = new RegExp(pattern.regex);

    // Usar método exec para iterar através das correspondências
    while ((match = regex.exec(text)) !== null) {
      potentialOperations.push(`${pattern.type} ${match[0].trim()}`);

      // Evitar loops infinitos com regex que não tem a flag global
      if (!regex.global) break;
    }
  }

  return potentialOperations;
}

/**
 * Extrai operações de tabela do texto
 * @param text Texto para analisar
 * @returns Operações de tabela extraídas
 */
function extractTableOperations(text: string): ExcelOperation[] {
  const operations: ExcelOperation[] = [];

  // Padrão para tabela dinâmica: "criar tabela dinâmica/pivot com <campo1> nas linhas, <campo2> nas colunas e <campo3> nos valores"
  const pivotPattern =
    /criar\s+(tabela\s+dinâmica|pivot)\s+com\s+(.+?)\s+nas\s+linhas,\s+(.+?)\s+nas\s+colunas\s+e\s+(.+?)\s+(?:nos|como)\s+valores/i;

  const pivotMatches = text.match(pivotPattern);
  if (pivotMatches && pivotMatches.length >= 5) {
    operations.push({
      type: 'TABLE',
      data: {
        subtype: 'PIVOT_TABLE',
        rowsField: pivotMatches[2]?.trim() || '',
        columnsField: pivotMatches[3]?.trim() || '',
        valuesField: pivotMatches[4]?.trim() || '',
        aggregation: 'SUM',
      },
    });
  }

  return operations;
}

/**
 * Extrai operações de atualização de células do texto
 * @param text Texto para analisar
 * @returns Operações de atualização de células extraídas
 */
function extractCellUpdateOperations(text: string): ExcelOperation[] {
  const operations: ExcelOperation[] = [];

  // Padrão para definir valor de célula: "definir/colocar valor <valor> na célula <ref>"
  const valuePattern =
    /(?:definir|colocar|mudar)\s+(?:o\s+)?valor\s+(?:para\s+)?(\d+(?:[,.]\d+)?)\s+na\s+célula\s+([A-Z]+\d+)/i;

  const valueMatches = text.match(valuePattern);
  if (valueMatches && valueMatches.length >= 3) {
    operations.push({
      type: ExcelOperationType.CELL_UPDATE,
      data: {
        cell: valueMatches[2] || '',
        value: parseFloat((valueMatches[1] || '0').replace(',', '.')),
        valueType: 'number',
      },
    });
  }

  return operations;
}

/**
 * Extrai operações de formatação do texto
 * @param text Texto para analisar
 * @returns Operações de formatação extraídas
 */
function extractFormatOperations(text: string): ExcelOperation[] {
  const operations: ExcelOperation[] = [];

  // Padrão para formatação condicional: "destacar/colorir células (com valores) acima/abaixo de <valor> de/em <cor>"
  const condFormatPattern =
    /(?:destacar|colorir)\s+células\s+(?:com\s+valores\s+)?(acima|abaixo)\s+(?:de|do)\s+(\d+(?:[,.]\d+)?)\s+(?:de|em|com)\s+(?:cor\s+)?(\w+)/i;

  const condFormatMatches = text.match(condFormatPattern);
  if (condFormatMatches && condFormatMatches.length >= 4) {
    const direction = condFormatMatches[1]?.toLowerCase() || '';
    const value = condFormatMatches[2] || '0';
    const color = condFormatMatches[3]?.toLowerCase() || 'vermelho';

    operations.push({
      type: ExcelOperationType.FORMAT,
      data: {
        format: 'conditional',
        condition: direction === 'acima' ? '>' : '<',
        value: parseFloat(value.replace(',', '.')),
        color: color,
      },
    });
  }

  return operations;
}

/**
 * Normaliza nomes de funções Excel para seus equivalentes em português
 * @param formula Fórmula em formato string
 * @returns Fórmula normalizada
 */
function _normalize(formula: string): string {
  const functionMap: { [key: string]: string } = {
    // Funções matemáticas
    sum: 'SOMA',
    average: 'MÉDIA',
    count: 'CONT.NÚM',
    max: 'MÁXIMO',
    min: 'MÍNIMO',
    median: 'MED',
    stdev: 'DESVPAD',

    // Funções lógicas
    if: 'SE',
    and: 'E',
    or: 'OU',
    not: 'NÃO',

    // Funções de texto
    left: 'ESQUERDA',
    right: 'DIREITA',
    mid: 'EXT.TEXTO',
    len: 'NÚM.CARACT',
    find: 'LOCALIZAR',
    substitute: 'SUBSTITUIR',
    concatenate: 'CONCATENAR',

    // Funções de data
    today: 'HOJE',
    now: 'AGORA',
    year: 'ANO',
    month: 'MÊS',
    day: 'DIA',

    // Funções de pesquisa
    vlookup: 'PROCV',
    hlookup: 'PROCH',
    lookup: 'PROC',
    index: 'ÍNDICE',
    match: 'CORRESP',
  };

  // Verificar se a fórmula está em inglês e converter para português
  for (const [en, pt] of Object.entries(functionMap)) {
    const regex = new RegExp(`=\\s*(${en})\\s*\\(`, 'i');
    if (regex.test(formula)) {
      formula = formula.replace(regex, `=${pt}(`);
      break;
    }
  }

  return formula;
}

/**
 * Executa uma série de operações Excel em dados de planilha
 */
export async function executeExcelOperations(
  sheetData: Record<string, unknown>,
  operations: ExcelOperation[]
): Promise<{
  updatedData: Record<string, unknown>;
  resultSummary: string[];
  modifiedCells?: Array<{ row: number; col: number }>;
  errors?: string[];
}> {
  if (!operations || operations.length === 0) {
    return {
      updatedData: sheetData,
      resultSummary: ['Nenhuma operação para executar'],
    };
  }

  // Clone os dados usando JSON para evitar problemas de tipagem com spread
  let currentData = JSON.parse(JSON.stringify(sheetData)) as Record<string, unknown>;
  const resultSummary: string[] = [];
  const allModifiedCells: Array<{ row: number; col: number }> = [];
  const errors: string[] = [];

  for (const operation of operations) {
    try {
      // Aplicar transformações iniciais e verificar tipo de operação
      const normalizedOperation = normalizeOperation(operation);

      // Log para debugging quando necessário
      if (process.env.NODE_ENV === 'development') {
        // Executando operação: ${normalizedOperation.type}
      }

      let result;

      // Tratamento de erros específicos por tipo de operação
      switch (normalizedOperation.type) {
        case ExcelOperationType.COLUMN_OPERATION:
          result = await executeColumnOperation(currentData, normalizedOperation);
          break;
        case ExcelOperationType.FORMULA:
          result = await executeFormulaOperation(currentData, normalizedOperation);
          break;
        case ExcelOperationType.CHART:
          result = await executeChartOperation(currentData, normalizedOperation);
          break;
        case ExcelOperationType.FILTER:
          result = await executeFilterOperation(currentData, normalizedOperation);
          break;
        case ExcelOperationType.SORT:
          result = await executeSortOperation(currentData, normalizedOperation);
          break;
        case ExcelOperationType.PIVOT_TABLE:
          result = await executePivotTableOperation(currentData, normalizedOperation);
          break;
        case ExcelOperationType.CONDITIONAL_FORMAT:
          result = await executeConditionalFormattingOperation(currentData, normalizedOperation);
          break;
        case ExcelOperationType.ADVANCED_VISUALIZATION:
          result = await executeAdvancedVisualizationOperation(currentData, normalizedOperation);
          break;
        case ExcelOperationType.TABLE:
          result = await executeTableOperation(currentData, normalizedOperation);
          break;
        case ExcelOperationType.CELL_UPDATE:
          result = await executeCellUpdateOperation(currentData, normalizedOperation);
          break;
        case ExcelOperationType.FORMAT:
          result = await executeFormatOperation(currentData, normalizedOperation);
          break;
        // Tipos genéricos ou desconhecidos
        default:
          // Tentar mapear tipo desconhecido para uma operação conhecida
          {
            const mappedOperation = mapUnknownOperationType(normalizedOperation);
            result = await handleGenericOperation(currentData, mappedOperation);
          }
          break;
      }

      // Atualizar dados e resumo
      if (result) {
        currentData = result.updatedData as Record<string, unknown>;
        if (typeof result.resultSummary === 'string') {
          resultSummary.push(result.resultSummary);
        } else if (Array.isArray(result.resultSummary)) {
          // Conversão explícita e uso de concatenação ao invés de spread
          resultSummary.push(String(result.resultSummary));
        }

        // Adicionar células modificadas se houver
        if (
          'modifiedCells' in result &&
          Array.isArray(result.modifiedCells) &&
          result.modifiedCells.length > 0
        ) {
          // Adicionar um a um em vez de usar spread operator
          for (const cell of result.modifiedCells) {
            allModifiedCells.push(cell);
          }
        }
      }
    } catch (error) {
      // Registrar erro e continuar para próxima operação
      const errorMessage =
        error instanceof Error
          ? `Erro ao executar operação ${operation.type}: ${error.message}`
          : `Erro desconhecido ao executar operação ${operation.type}`;

      console.error(errorMessage, error);
      errors.push(errorMessage);
      resultSummary.push(`⚠️ ${errorMessage}`);
    }
  }

  // Deduplica células modificadas
  const uniqueCells = allModifiedCells.filter(
    (cell, index, self) => index === self.findIndex(c => c.row === cell.row && c.col === cell.col)
  );

  // Criar objeto resultado com propriedades básicas
  const result: {
    updatedData: Record<string, unknown>;
    resultSummary: string[];
    modifiedCells?: Array<{ row: number; col: number }>;
    errors?: string[];
  } = {
    updatedData: currentData,
    resultSummary,
  };

  // Adicionar propriedades opcionais apenas se tiverem valores
  if (uniqueCells.length > 0) {
    result.modifiedCells = uniqueCells;
  }

  if (errors.length > 0) {
    result.errors = errors;
  }

  return result;
}

/**
 * Tenta determinar e executar uma operação genérica
 */
async function handleGenericOperation(
  sheetData: Record<string, unknown>,
  operation: ExcelOperation
): Promise<{
  updatedData: Record<string, unknown>;
  resultSummary: string;
  modifiedCells?: Array<{ row: number; col: number }>;
}> {
  // Tentar determinar o tipo real da operação com base em propriedades
  if (
    operation.data &&
    typeof operation.data === 'object' &&
    ('formula' in operation.data || 'formula_type' in operation.data)
  ) {
    // Garantir que o objeto tem a propriedade data
    const formulaOperation: ExcelOperation = {
      ...operation,
      type: ExcelOperationType.FORMULA,
      data: operation.data || {},
    };
    return executeFormulaOperation(sheetData, formulaOperation);
  }

  if (operation.data && typeof operation.data === 'object' && 'chart_type' in operation.data) {
    // Garantir que o objeto tem a propriedade data
    const chartOperation: ExcelOperation = {
      ...operation,
      type: ExcelOperationType.CHART,
      data: operation.data || {},
    };
    return executeChartOperation(sheetData, chartOperation);
  }

  if (
    operation.data &&
    typeof operation.data === 'object' &&
    'data' in operation.data &&
    Array.isArray(operation.data.data)
  ) {
    // Garantir que o objeto tem a propriedade data
    const tableOperation: ExcelOperation = {
      ...operation,
      type: ExcelOperationType.TABLE,
      data: operation.data || {},
    };
    return executeTableOperation(sheetData, tableOperation);
  }

  // Fallback para manter os dados inalterados se não conseguir identificar a operação
  return {
    updatedData: sheetData as Record<string, unknown>,
    resultSummary: 'Operação genérica não suportada',
    modifiedCells: [],
  };
}

/**
 * Tenta mapear tipos desconhecidos para operações conhecidas
 */
function mapUnknownOperationType(operation: ExcelOperation): ExcelOperation {
  const data = operation.data || {};

  // Propriedades que indicam o tipo de operação
  if (data.formula || data.range) {
    return { ...operation, type: ExcelOperationType.FORMULA };
  }

  if (data.chart_type || data.title) {
    return { ...operation, type: ExcelOperationType.CHART };
  }

  if (data.data && Array.isArray(data.data)) {
    return { ...operation, type: ExcelOperationType.TABLE };
  }

  if (data.order_by || data.direction) {
    return { ...operation, type: ExcelOperationType.SORT };
  }

  if (data.condition || data.filter_column) {
    return { ...operation, type: ExcelOperationType.FILTER };
  }

  if (data.background_color || data.text_color || data.format) {
    return { ...operation, type: ExcelOperationType.FORMAT };
  }

  // Fallback para operação genérica
  return { ...operation, type: 'GENERIC' };
}

/**
 * Executa operações de formatação em dados de planilha
 * @param sheetData Dados da planilha
 * @param operation Operação de formatação
 * @returns Dados atualizados e resumo da operação
 */
interface _SheetDataWithFormatting {
  headers?: string[];
  rows?: unknown[];
  formatting?: Record<string, FormatInfo>;
  [key: string]: unknown;
}

interface FormatInfo {
  type: string;
  decimals?: number;
  locale?: string;
  dateFormat?: string;
  condition?: string;
  value?: string | number;
  color?: string;
  [key: string]: unknown;
}

async function executeFormatOperation(
  sheetData: unknown,
  operation: ExcelOperation
): Promise<{ updatedData: Record<string, unknown>; resultSummary: string }> {
  const { target, format, decimals, locale, dateFormat, condition, value, color } = operation.data;

  // Clone os dados para não modificar o original
  const updatedData =
    typeof sheetData === 'object' && sheetData !== null
      ? { ...(sheetData as Record<string, unknown>) }
      : ({} as Record<string, unknown>);

  // Inicializar propriedade formatting se não existir
  if (!updatedData.formatting || typeof updatedData.formatting !== 'object') {
    updatedData.formatting = {} as Record<string, unknown>;
  } else {
    // Garantir que formatting seja tratado como Record<string, unknown>
    updatedData.formatting = updatedData.formatting as Record<string, unknown>;
  }

  let resultSummary = '';

  try {
    // Verificar formato dos dados (tabular ou outro)
    if (updatedData.headers && Array.isArray(updatedData.headers) && updatedData.rows) {
      // Formato tabular
      let columnIndex = -1;

      // Determinar o índice da coluna
      if (/^[A-Z]+$/.test(target)) {
        // Se for letra de coluna (ex: "A", "B")
        columnIndex = target.charCodeAt(0) - 65; // A=0, B=1, etc.
      } else {
        // Se for nome de coluna
        columnIndex = updatedData.headers.findIndex((h: string) => h === target);
      }

      if (columnIndex >= 0) {
        // Adicionar metadata de formatação
        if (!updatedData.formatting) {
          updatedData.formatting = {};
        }

        const formatInfo: FormatInfo = { type: format };

        if (format === 'currency') {
          formatInfo.decimals = decimals || 2;
          formatInfo.locale = locale || 'pt-BR';
          resultSummary = `Coluna ${target} formatada como moeda com ${decimals || 2} casas decimais`;
        } else if (format === 'percentage') {
          formatInfo.decimals = decimals || 0;
          resultSummary = `Coluna ${target} formatada como porcentagem com ${decimals || 0} casas decimais`;
        } else if (format === 'date') {
          formatInfo.dateFormat = dateFormat || 'dd/mm/yyyy';
          resultSummary = `Coluna ${target} formatada como data no formato ${dateFormat || 'dd/mm/yyyy'}`;
        } else if (format === 'conditional') {
          formatInfo.condition = condition;
          formatInfo.value = value;
          formatInfo.color = color;
          resultSummary = `Formatação condicional aplicada na coluna ${target} (valores ${condition === '>' ? 'maiores' : 'menores'} que ${value} destacados em ${color})`;
        }

        // Armazenar informações de formatação para a coluna
        const formatObj = updatedData.formatting as Record<string, unknown>;
        formatObj[target] = formatInfo;
        updatedData.formatting = formatObj;
      }
    } else {
      // Formato de células (A1: valor)
      if (target && typeof target === 'string' && target.includes(':')) {
        // Formato de intervalo (A1:B5)
        if (!updatedData.formatting) {
          updatedData.formatting = {};
        }

        const formatObj = updatedData.formatting as Record<string, unknown>;
        formatObj[target] = {
          type: format,
          decimals: decimals || 2,
          locale: locale || 'pt-BR',
          dateFormat: dateFormat || 'dd/mm/yyyy',
        };
        updatedData.formatting = formatObj;

        resultSummary = `Intervalo ${target} formatado como ${format}`;
      }
    }

    return {
      updatedData: updatedData as Record<string, unknown>,
      resultSummary,
    };
  } catch (error) {
    console.error('Erro ao aplicar formatação:', error);
    throw new Error(
      `Falha ao aplicar formatação: ${error instanceof Error ? error.message : String(error)}`
    );
  }
}

/**
 * Executa operações de atualização de células em dados de planilha
 * @param sheetData Dados da planilha
 * @param operation Operação de atualização de célula
 * @returns Dados atualizados e resumo da operação
 */
async function executeCellUpdateOperation(
  sheetData: unknown,
  operation: ExcelOperation
): Promise<{ updatedData: Record<string, unknown>; resultSummary: string }> {
  const { cell, value, _valueType } = operation.data;

  // Clone os dados para não modificar o original
  const updatedData =
    typeof sheetData === 'object' && sheetData !== null
      ? { ...(sheetData as Record<string, unknown>) }
      : ({} as Record<string, unknown>);

  try {
    // Verificar formato dos dados (tabular ou cells)
    if (
      updatedData.headers &&
      Array.isArray(updatedData.headers) &&
      updatedData.rows &&
      Array.isArray(updatedData.rows)
    ) {
      // Formato tabular - precisamos converter referência de célula (A1) para linha/coluna
      const colLetter = cell.match(/^[A-Z]+/)?.[0] || '';
      const rowNumber = parseInt(cell.match(/\d+/)?.[0] || '0', 10);

      if (colLetter && rowNumber > 0) {
        const colIndex = colLetter.charCodeAt(0) - 65; // A=0, B=1, etc.
        const rowIndex = rowNumber - 1; // 0-indexed

        // Verificar se temos linhas/colunas suficientes
        if (rowIndex >= updatedData.rows.length) {
          // Adicionar linhas vazias até o índice necessário
          while (updatedData.rows.length <= rowIndex) {
            updatedData.rows.push(Array(updatedData.headers.length).fill(''));
          }
        }

        // Atualizar célula
        if (updatedData.rows[rowIndex]) {
          updatedData.rows[rowIndex][colIndex] = value;
        }
      }
    } else {
      // Formato de células (A1: valor)
      updatedData[cell] = value;
    }

    return {
      updatedData: updatedData as Record<string, unknown>,
      resultSummary: `Célula ${cell} atualizada com o valor "${value}"`,
    };
  } catch (error) {
    console.error('Erro ao atualizar célula:', error);
    throw new Error(
      `Falha ao atualizar célula: ${error instanceof Error ? error.message : String(error)}`
    );
  }
}

/**
 * Executa operações de tabela em dados de planilha
 * @param sheetData Dados da planilha
 * @param operation Operação de tabela
 * @returns Dados atualizados e resumo da operação
 */
async function executeTableOperation(
  sheetData: unknown,
  operation: ExcelOperation
): Promise<{ updatedData: Record<string, unknown>; resultSummary: string }> {
  const {
    subtype,
    range,
    hasHeaders,
    allData,
    function: _aggregateFunction,
    rowsField,
    columnsField,
    valuesField,
  } = operation.data;

  // Clone os dados para não modificar o original
  const updatedData =
    typeof sheetData === 'object' && sheetData !== null
      ? { ...(sheetData as Record<string, unknown>) }
      : ({} as Record<string, unknown>);
  let resultSummary = '';

  try {
    // Verificar o subtipo da operação
    switch (subtype) {
      case 'CREATE_TABLE':
        // Verificar metadata da tabela
        if (!updatedData.tables || !Array.isArray(updatedData.tables)) {
          updatedData.tables = [];
        }

        if (allData) {
          // Transformar todos os dados em tabela
          updatedData.isTable = true;
          updatedData.tableHeaders = hasHeaders;
          resultSummary = 'Todos os dados foram convertidos em tabela';
        } else if (range) {
          // Criar tabela em um intervalo específico
          if (Array.isArray(updatedData.tables)) {
            updatedData.tables.push({
              range: String(range),
              hasHeaders,
            });
            resultSummary = `Intervalo ${String(range)} convertido em tabela`;
          }
        }
        break;

      case 'ADD_TOTAL_ROW':
        if (
          !updatedData.tables ||
          !Array.isArray(updatedData.tables) ||
          updatedData.tables.length === 0
        ) {
          // Se não houver tabelas, assumir todos os dados
          if (
            updatedData.headers &&
            Array.isArray(updatedData.headers) &&
            updatedData.rows &&
            Array.isArray(updatedData.rows)
          ) {
            // Criar nova linha com totais
            const headers = updatedData.headers as string[];
            const rows = updatedData.rows as unknown[][];
            const totalRow = Array(headers.length).fill('');

            // Preencher com fórmulas de soma para colunas numéricas
            headers.forEach((header: string, idx: number) => {
              const hasNumbers = rows.some(
                (row: unknown[]) =>
                  typeof row[idx] === 'number' ||
                  (typeof row[idx] === 'string' && !isNaN(Number(row[idx])))
              );

              if (hasNumbers) {
                totalRow[idx] = {
                  formula: `=SOMA(${String.fromCharCode(65 + idx)}2:${String.fromCharCode(65 + idx)}${rows.length + 1})`,
                  result: rows.reduce((sum: number, row: unknown[]) => {
                    const val = parseFloat(String(row[idx]));
                    return sum + (isNaN(val) ? 0 : val);
                  }, 0),
                };
              }
            });

            // Adicionar rótulo "Total" na primeira coluna
            totalRow[0] = totalRow[0] || 'Total';

            // Adicionar à última linha
            updatedData.rows = [...rows, totalRow];
            resultSummary = 'Linha de total adicionada à tabela';
          }
        } else {
          // Adicionar total a uma tabela específica
          if (Array.isArray(updatedData.tables) && updatedData.tables.length > 0) {
            // Implementação para tabelas específicas poderia ir aqui
            // Por enquanto, apenas retornamos a mensagem
            resultSummary = 'Linha de total adicionada à tabela';
          }
        }
        break;

      case 'PIVOT_TABLE':
        // Simular uma tabela dinâmica básica
        if (
          updatedData.headers &&
          Array.isArray(updatedData.headers) &&
          updatedData.rows &&
          Array.isArray(updatedData.rows) &&
          rowsField &&
          columnsField &&
          valuesField
        ) {
          // Obter índices dos campos
          const rowsIdx = updatedData.headers.findIndex((h: string) => h === rowsField);
          const colsIdx = updatedData.headers.findIndex((h: string) => h === columnsField);
          const valsIdx = updatedData.headers.findIndex((h: string) => h === valuesField);

          if (rowsIdx >= 0 && colsIdx >= 0 && valsIdx >= 0) {
            // Criar tabela dinâmica simples
            const pivotData: Record<string, Record<string, number>> = {};
            const uniqueRows = new Set<string>();
            const uniqueCols = new Set<string>();

            // Processar dados
            if (Array.isArray(updatedData.rows)) {
              updatedData.rows.forEach((row: unknown[]) => {
                const rowKey = String(row[rowsIdx] ?? '');
                const colKey = String(row[colsIdx] ?? '');
                const value = parseFloat(String(row[valsIdx] ?? '0')) || 0;

                uniqueRows.add(rowKey);
                uniqueCols.add(colKey);

                if (!pivotData[rowKey]) {
                  pivotData[rowKey] = {};
                }

                if (!pivotData[rowKey][colKey]) {
                  pivotData[rowKey][colKey] = 0;
                }

                pivotData[rowKey][colKey] += value;
              });
            }

            // Criar nova estrutura para tabela dinâmica
            const pivotHeaders = ['', ...Array.from(uniqueCols)];
            const pivotRows = Array.from(uniqueRows).map(rowKey => {
              const row = [rowKey];
              Array.from(uniqueCols).forEach(colKey => {
                // Converter explicitamente para string
                const cellValue = String(pivotData[rowKey]?.[colKey] || 0);
                row.push(cellValue);
              });
              return row;
            });

            // Armazenar tabela dinâmica
            updatedData.pivotTable = {
              headers: pivotHeaders,
              rows: pivotRows,
              rowsField,
              columnsField,
              valuesField,
            };

            resultSummary = `Tabela dinâmica criada com ${rowsField} nas linhas, ${columnsField} nas colunas e ${valuesField} como valores`;
          }
        }
        break;
    }

    return { updatedData, resultSummary };
  } catch (error) {
    console.error('Erro ao executar operação de tabela:', error);
    throw new Error(
      `Falha ao executar operação de tabela: ${error instanceof Error ? error.message : String(error)}`
    );
  }
}
