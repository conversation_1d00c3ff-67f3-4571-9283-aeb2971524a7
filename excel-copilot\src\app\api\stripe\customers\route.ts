import { NextRequest } from 'next/server';

import { logger } from '@/lib/logger';
import { StripeClient } from '@/lib/stripe-integration';
import { ApiResponse } from '@/utils/api-response';

// Configurar rota como dinâmica
export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

/**
 * GET /api/stripe/customers
 * Lista clientes do Stripe com filtros opcionais
 */
export async function GET(request: NextRequest) {
  try {
    const apiKey = process.env.STRIPE_SECRET_KEY;

    if (!apiKey) {
      return ApiResponse.error('STRIPE_SECRET_KEY não configurado', 'STRIPE_NOT_CONFIGURED', 500);
    }

    // Obter parâmetros de query
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '50');
    const email = searchParams.get('email') || undefined;
    const createdAfter = searchParams.get('created_after');
    const createdBefore = searchParams.get('created_before');

    // Validar parâmetros
    if (limit > 100) {
      return ApiResponse.error('Limite máximo é 100 clientes', 'INVALID_LIMIT', 400);
    }

    // Preparar filtros de data
    let created: { gte?: number; lte?: number } | undefined;
    if (createdAfter || createdBefore) {
      created = {};
      if (createdAfter) {
        const timestamp = parseInt(createdAfter);
        if (isNaN(timestamp)) {
          return ApiResponse.error(
            'created_after deve ser um timestamp Unix válido',
            'INVALID_TIMESTAMP',
            400
          );
        }
        created.gte = timestamp;
      }
      if (createdBefore) {
        const timestamp = parseInt(createdBefore);
        if (isNaN(timestamp)) {
          return ApiResponse.error(
            'created_before deve ser um timestamp Unix válido',
            'INVALID_TIMESTAMP',
            400
          );
        }
        created.lte = timestamp;
      }
    }

    // Criar cliente Stripe
    const stripeClient = new StripeClient({ apiKey });

    // Obter clientes
    const result = await stripeClient.getCustomers({
      limit,
      ...(email && { email }),
      ...(created && { created }),
    });

    // Preparar resposta
    const response = {
      customers: result.customers.map(customer => ({
        id: customer.id,
        email: customer.email,
        name: customer.name,
        created: new Date(customer.created * 1000).toISOString(),
        subscriptions: customer.subscriptions,
        totalSpent: customer.totalSpent / 100, // Converter de centavos
        currency: customer.currency,
        defaultPaymentMethod: customer.defaultPaymentMethod,
      })),
      pagination: {
        limit,
        count: result.customers.length,
        hasMore: result.customers.length === limit,
      },
      filters: {
        email,
        createdAfter: createdAfter
          ? new Date(parseInt(createdAfter) * 1000).toISOString()
          : undefined,
        createdBefore: createdBefore
          ? new Date(parseInt(createdBefore) * 1000).toISOString()
          : undefined,
      },
      timestamp: new Date().toISOString(),
    };

    logger.info('Clientes Stripe obtidos com sucesso', {
      count: result.customers.length,
      filters: { email, createdAfter, createdBefore },
    });

    return ApiResponse.success(response);
  } catch (error) {
    logger.error('Erro ao obter clientes Stripe', { error });

    if (error instanceof Error) {
      return ApiResponse.error(
        `Erro ao buscar clientes: ${error.message}`,
        'STRIPE_API_ERROR',
        500
      );
    }

    return ApiResponse.error('Erro interno do servidor', 'INTERNAL_ERROR', 500);
  }
}

/**
 * POST /api/stripe/customers
 * Busca clientes com critérios avançados
 */
export async function POST(request: NextRequest) {
  try {
    const apiKey = process.env.STRIPE_SECRET_KEY;

    if (!apiKey) {
      return ApiResponse.error('STRIPE_SECRET_KEY não configurado', 'STRIPE_NOT_CONFIGURED', 500);
    }

    // Obter dados do corpo da requisição
    const body = await request.json();
    const {
      filters = {},
      analytics = false,
      includeSubscriptions: _includeSubscriptions = true,
      includePayments = false,
    } = body;

    const stripeClient = new StripeClient({ apiKey });

    // Obter clientes com filtros
    const result = await stripeClient.getCustomers({
      limit: filters.limit || 50,
      email: filters.email,
      created: filters.created,
    });

    const response: Record<string, unknown> = {
      customers: result.customers,
      summary: {
        totalCustomers: result.customers.length,
        totalSpent: result.customers.reduce((sum, c) => sum + c.totalSpent, 0) / 100,
        averageSpent:
          result.customers.length > 0
            ? result.customers.reduce((sum, c) => sum + c.totalSpent, 0) /
              result.customers.length /
              100
            : 0,
      },
      timestamp: new Date().toISOString(),
    };

    // Adicionar analytics se solicitado
    if (analytics) {
      const customersByMonth = result.customers.reduce(
        (acc, customer) => {
          const month = new Date(customer.created * 1000).toISOString().substring(0, 7);
          acc[month] = (acc[month] || 0) + 1;
          return acc;
        },
        {} as Record<string, number>
      );

      const subscriptionStats = result.customers.reduce(
        (acc, customer) => {
          acc.withSubscriptions += customer.subscriptions.total > 0 ? 1 : 0;
          acc.activeSubscriptions += customer.subscriptions.active;
          acc.canceledSubscriptions += customer.subscriptions.canceled;
          return acc;
        },
        { withSubscriptions: 0, activeSubscriptions: 0, canceledSubscriptions: 0 }
      );

      response.analytics = {
        customersByMonth,
        subscriptionStats,
        paymentMethods: result.customers.reduce(
          (acc, customer) => {
            const method = customer.defaultPaymentMethod?.type || 'none';
            acc[method] = (acc[method] || 0) + 1;
            return acc;
          },
          {} as Record<string, number>
        ),
      };
    }

    // Adicionar dados de pagamentos se solicitado
    if (includePayments) {
      const paymentsPromises = result.customers.slice(0, 10).map(async customer => {
        try {
          const payments = await stripeClient.getPayments({
            customer: customer.id,
            limit: 5,
          });
          return {
            customerId: customer.id,
            recentPayments: payments.payments,
          };
        } catch (error) {
          logger.warn(`Erro ao obter pagamentos do cliente ${customer.id}:`, error);
          return {
            customerId: customer.id,
            recentPayments: [],
          };
        }
      });

      const paymentsData = await Promise.all(paymentsPromises);
      response.payments = paymentsData;
    }

    logger.info('Busca avançada de clientes realizada', {
      count: result.customers.length,
      analytics,
      includePayments,
    });

    return ApiResponse.success(response);
  } catch (error) {
    logger.error('Erro na busca avançada de clientes', { error });

    if (error instanceof Error) {
      return ApiResponse.error(`Erro na busca: ${error.message}`, 'STRIPE_SEARCH_ERROR', 500);
    }

    return ApiResponse.error('Erro interno do servidor', 'INTERNAL_ERROR', 500);
  }
}
