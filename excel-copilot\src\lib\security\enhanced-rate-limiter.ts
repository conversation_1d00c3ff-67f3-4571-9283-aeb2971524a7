import { NextRequest, NextResponse } from 'next/server';

import { ENV } from '@/config/unified-environment';
import { logger } from '@/lib/logger';

// Tipos para o rate limiter avançado
interface RateLimitRecord {
  count: number;
  resetTime: number;
  blockedUntil?: number;
  consecutiveViolations?: number;
}

interface EnhancedRateLimiterOptions {
  // Período de tempo da janela (ms)
  windowMs: number;

  // Máximo de requisições na janela
  maxRequests: number;

  // Mensagem de erro
  message?: string;

  // Padrões de caminho para aplicar o limiter
  pathPatterns?: RegExp[];

  // Função para obter identificador único
  getIdentifier?: (req: NextRequest) => string;

  // Tempo de bloqueio base quando limite é excedido (ms)
  blockDurationMs?: number;

  // Fator de multiplicação para bloqueios subsequentes
  escalationFactor?: number;

  // Número máximo de entradas no store
  maxEntries?: number;

  // Se deve rejeitar silenciosamente (sem logs) acessos de clientes já bloqueados
  silentReject?: boolean;

  // Endpoints sensíveis que exigem regras mais rígidas
  sensitiveEndpoints?: RegExp[];

  // Limite para endpoints sensíveis (menor que maxRequests)
  sensitiveMaxRequests?: number;
}

interface RateLimitResult {
  allowed: boolean;
  limited: boolean;
  remaining: number;
  resetTime: number;
  blockRemaining?: number;
  retryAfter?: number;
  message?: string;
  identifier: string;
  isSensitive?: boolean;
}

/**
 * Rate Limiter avançado com proteção contra abusos
 */
export class EnhancedRateLimiter {
  private store: Map<string, RateLimitRecord> = new Map();
  private readonly windowMs: number;
  private readonly maxRequests: number;
  private readonly message: string;
  private readonly blockDurationMs: number;
  private readonly escalationFactor: number;
  private readonly maxEntries: number;
  private readonly silentReject: boolean;
  private readonly pathPatterns: RegExp[];
  private readonly sensitiveEndpoints: RegExp[];
  private readonly sensitiveMaxRequests: number;
  private readonly getIdentifier: (req: NextRequest) => string;

  private lastCleanup: number = Date.now();

  constructor(options: EnhancedRateLimiterOptions) {
    this.windowMs = options.windowMs;
    this.maxRequests = options.maxRequests;
    this.message = options.message || 'Muitas requisições. Por favor, tente novamente mais tarde.';
    this.blockDurationMs = options.blockDurationMs || 10 * 60 * 1000; // 10 minutos padrão
    this.escalationFactor = options.escalationFactor || 2; // Dobrar tempo de bloqueio a cada violação
    this.maxEntries = options.maxEntries || 10000;
    this.silentReject = options.silentReject || false;
    this.pathPatterns = options.pathPatterns || [/.*/]; // Por padrão, aplica a todos os caminhos
    this.sensitiveEndpoints = options.sensitiveEndpoints || [];
    this.sensitiveMaxRequests = options.sensitiveMaxRequests || Math.floor(this.maxRequests / 2);

    this.getIdentifier =
      options.getIdentifier ||
      ((req: NextRequest) => {
        // Identificador padrão: endereço IP + user agent resumido
        const ip = req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip') || 'unknown';

        const userAgent = req.headers.get('user-agent') || 'unknown';
        const userAgentHash = userAgent.substring(0, 20); // Usar apenas parte do UA

        return `${ip}:${userAgentHash}`;
      });

    // Iniciar limpeza periódica
    if (typeof setInterval !== 'undefined') {
      setInterval(() => this.cleanup(), 10 * 60 * 1000); // Limpar a cada 10 minutos
    }
  }

  /**
   * Limpa entradas antigas para evitar vazamento de memória
   */
  private cleanup(): void {
    const now = Date.now();
    let cleanedEntries = 0;

    // Limpar apenas a cada 10 minutos
    if (now - this.lastCleanup < 10 * 60 * 1000) {
      return;
    }

    this.lastCleanup = now;

    for (const [key, record] of this.store.entries()) {
      // Remover entradas expiradas (resetTime passou e não está bloqueado)
      if (record.resetTime < now && (!record.blockedUntil || record.blockedUntil < now)) {
        this.store.delete(key);
        cleanedEntries++;
      }
    }

    if (cleanedEntries > 0 && !this.silentReject) {
      logger.debug(`[RateLimiter] Limpeza: ${cleanedEntries} entradas removidas`);
    }

    // Se o armazenamento estiver muito grande, limpe as entradas mais antigas
    if (this.store.size > this.maxEntries) {
      const entriesToRemove = Math.floor(this.store.size * 0.2); // Remover 20% das entradas
      const entries = Array.from(this.store.entries());

      // Ordenar pela data de expiração (mais antigas primeiro)
      entries.sort((a, b) => {
        // Garantir que a e b existem e têm o índice 1 definido
        if (!a || !a[1] || !b || !b[1]) return 0;
        return a[1].resetTime - b[1].resetTime;
      });

      // Remover entradas mais antigas
      for (let i = 0; i < entriesToRemove; i++) {
        if (i < entries.length && entries[i]) {
          const entry = entries[i];
          if (entry && entry[0]) {
            this.store.delete(entry[0]);
          }
        }
      }

      if (!this.silentReject) {
        logger.warn(
          `[RateLimiter] Armazenamento excedeu ${this.maxEntries} entradas, ${entriesToRemove} antigas removidas`
        );
      }
    }
  }

  /**
   * Verifica se uma requisição deve ser permitida ou limitada
   */
  public check(req: NextRequest): RateLimitResult {
    const path = req.nextUrl.pathname;
    const method = req.method;

    // Verificar se o caminho deve ser limitado
    const shouldLimit = this.pathPatterns.some(pattern => pattern.test(path));
    if (!shouldLimit) {
      return {
        allowed: true,
        limited: false,
        remaining: this.maxRequests,
        resetTime: Date.now() + this.windowMs,
        identifier: 'exempt',
      };
    }

    // Verificar se é um endpoint sensível
    const isSensitive = this.sensitiveEndpoints.some(pattern => pattern.test(path));
    const effectiveMaxRequests = isSensitive ? this.sensitiveMaxRequests : this.maxRequests;

    const identifier = this.getIdentifier(req);
    const now = Date.now();
    const key = `${identifier}:${path}`;

    // Obter registro existente ou criar novo
    let record = this.store.get(key);

    if (!record) {
      record = {
        count: 0,
        resetTime: now + this.windowMs,
        consecutiveViolations: 0,
      };
    }

    // Verificar se está bloqueado
    if (record.blockedUntil && now < record.blockedUntil) {
      // Cliente está temporariamente bloqueado
      const blockRemaining = Math.ceil((record.blockedUntil - now) / 1000);

      // Registrar tentativas durante bloqueio (exceto se silentReject)
      if (!this.silentReject) {
        logger.warn(
          `[RateLimiter] Acesso bloqueado: ${key} ainda bloqueado por ${blockRemaining}s`
        );
      }

      return {
        allowed: false,
        limited: true,
        remaining: 0,
        resetTime: record.resetTime,
        blockRemaining,
        retryAfter: blockRemaining,
        message: this.message,
        identifier,
        isSensitive,
      };
    }

    // Reiniciar contador se a janela de tempo expirou
    if (now > record.resetTime) {
      record.count = 0;
      record.resetTime = now + this.windowMs;

      // Reduzir contador de violações consecutivas se não houve bloqueio recente
      if (record.consecutiveViolations && record.consecutiveViolations > 0) {
        record.consecutiveViolations = Math.max(0, record.consecutiveViolations - 1);
      }
    }

    // Incrementar contador
    record.count++;

    // Verificar se excedeu o limite
    const allowed = record.count <= effectiveMaxRequests;

    // Calcular penalidade se limite foi excedido
    if (!allowed) {
      // Incrementar contador de violações consecutivas
      record.consecutiveViolations = (record.consecutiveViolations || 0) + 1;

      // Calcular duração do bloqueio com escalamento exponencial
      const blockDuration =
        this.blockDurationMs *
        Math.pow(
          this.escalationFactor,
          Math.min(5, record.consecutiveViolations - 1) // Limitar a 5 escalas para evitar tempos absurdos
        );

      // Aplicar bloqueio
      record.blockedUntil = now + blockDuration;

      // Registrar violação (exceto se silentReject)
      if (!this.silentReject) {
        const referer = req.headers.get('referer') || 'direct';
        const userAgent = req.headers.get('user-agent') || 'unknown';

        logger.warn(`[RateLimiter] Limite excedido: ${key} - ${method} ${path}
          - Violações consecutivas: ${record.consecutiveViolations}
          - Duração do bloqueio: ${Math.floor(blockDuration / 1000)}s
          - UA: ${userAgent.substring(0, 50)}
          - Origem: ${referer.substring(0, 50)}`);

        // Registrar possível tentativa de ataque ou abuso
        if (record.consecutiveViolations >= 3) {
          logger.error(`[SEGURANÇA] Possível abuso detectado: ${identifier}
            - Violações: ${record.consecutiveViolations}
            - Endpoint: ${method} ${path}
            - Bloqueado até: ${new Date(record.blockedUntil).toISOString()}`);
        }
      }
    }

    // Atualizar store
    this.store.set(key, record);

    // Sempre limpar a store quando ficar muito grande
    if (this.store.size > this.maxEntries) {
      this.cleanup();
    }

    // Calcular respostas
    const remaining = Math.max(0, effectiveMaxRequests - record.count);
    const retryAfter = !allowed ? Math.ceil((record.blockedUntil! - now) / 1000) : undefined;

    return {
      allowed,
      limited: !allowed,
      remaining,
      resetTime: record.resetTime,
      ...(retryAfter && { retryAfter, blockRemaining: retryAfter }),
      ...(!allowed && this.message && { message: this.message }),
      identifier,
      isSensitive,
    };
  }

  /**
   * Middleware para aplicar rate limiting em rotas Next.js
   */
  public middleware(req: NextRequest): NextResponse | null {
    const result = this.check(req);

    if (result.limited) {
      // Criar resposta de erro
      const headers = new Headers();
      headers.set('Content-Type', 'application/json');
      headers.set(
        'X-RateLimit-Limit',
        String(result.isSensitive ? this.sensitiveMaxRequests : this.maxRequests)
      );
      headers.set('X-RateLimit-Remaining', String(result.remaining));
      headers.set('X-RateLimit-Reset', String(Math.floor(result.resetTime / 1000)));

      if (result.retryAfter) {
        headers.set('Retry-After', String(result.retryAfter));
      }

      return new NextResponse(
        JSON.stringify({
          error: {
            status: 429,
            message: result.message || this.message,
          },
        }),
        {
          status: 429,
          headers,
        }
      );
    }

    return null;
  }
}

/**
 * Cria um conjunto de rate limiters pré-configurados para diferentes cenários
 */
export function createRateLimiters() {
  const limiters = {
    // Limiter para APIs gerais
    api: new EnhancedRateLimiter({
      windowMs: 60 * 1000, // 1 minuto
      maxRequests: ENV.IS_PRODUCTION ? 100 : 500,
      pathPatterns: [/^\/api\/.*/i],
      blockDurationMs: 5 * 60 * 1000, // 5 minutos
      message: 'Muitas requisições. Por favor, tente novamente em alguns minutos.',
    }),

    // Limiter específico para operações Excel (mais permissivo)
    excel: new EnhancedRateLimiter({
      windowMs: 60 * 1000, // 1 minuto
      maxRequests: ENV.IS_PRODUCTION ? 200 : 1000,
      pathPatterns: [/^\/api\/excel\/.*/i],
      blockDurationMs: 3 * 60 * 1000, // 3 minutos
      message: 'Muitas operações Excel em um curto período. Por favor, aguarde alguns minutos.',
    }),

    // Limiter restritivo para operações sensíveis (pagamentos, autenticação)
    secure: new EnhancedRateLimiter({
      windowMs: 60 * 1000, // 1 minuto
      maxRequests: ENV.IS_PRODUCTION ? 20 : 100,
      pathPatterns: [
        /^\/api\/auth\/.*/i,
        /^\/api\/payment\/.*/i,
        /^\/api\/subscription\/.*/i,
        /^\/api\/webhooks\/.*/i,
        /^\/api\/admin\/.*/i,
      ],
      sensitiveEndpoints: [
        /^\/api\/auth\/signin/i,
        /^\/api\/payment\/create-checkout/i,
        /^\/api\/admin\/.*/i,
      ],
      sensitiveMaxRequests: ENV.IS_PRODUCTION ? 10 : 50,
      blockDurationMs: 15 * 60 * 1000, // 15 minutos
      escalationFactor: 3, // Escalamento mais agressivo
      message: 'Muitas tentativas. Por razões de segurança, aguarde antes de tentar novamente.',
    }),

    // Limiter específico para API de IA/chat (com cotas)
    ai: new EnhancedRateLimiter({
      windowMs: 60 * 1000, // 1 minuto
      maxRequests: ENV.IS_PRODUCTION ? 30 : 200,
      pathPatterns: [/^\/api\/chat\/.*/i, /^\/api\/ai\/.*/i],
      blockDurationMs: 2 * 60 * 1000, // 2 minutos
      message: 'Cota de solicitações de IA excedida. Por favor, aguarde um momento.',
    }),
  };

  return limiters;
}
