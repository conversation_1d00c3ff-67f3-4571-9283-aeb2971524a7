import { NextRequest, NextResponse } from 'next/server';
import { ZodError } from 'zod';

import { AppError, ErrorType, normalizeError } from '../errors';
import { logger } from '../logger';
import { requireCSRFToken } from '../security/csrf-protection';
import { hasSuspiciousPatterns } from '../security/pattern-detection';
import { sanitizeInput } from '../security/sanitization';
import { getTypedSession, getUserId } from '../session-helpers';

// Tipo para funções de validação com suporte a mensagens de erro customizadas
export interface ValidationResult<T = any> {
  isValid: boolean;
  data?: T;
  errors?: any;
  message?: string;
}

// Estendendo a interface NextRequest para incluir propriedades adicionais
interface ExtendedNextRequest extends NextRequest {
  requestId?: string;
  validatedBody?: any;
  sanitizedQuery?: Record<string, string>;
  userSession?: any;
}

// Tipo de função para handlers de API
type ApiHandler = (req: ExtendedNextRequest, context?: any) => Promise<NextResponse | Response>;

/**
 * Middleware para tratamento de erros nas APIs
 * @param handler Função manipuladora de requisição API
 * @returns Resposta com tratamento de erro padronizado
 */
export function withErrorHandling(handler: any) {
  return async (request: NextRequest, ...args: any[]) => {
    const startTime = Date.now();
    const requestMethod = request.method;
    const requestUrl = request.url;
    const requestId = request.headers.get('x-request-id') || 'no-id';

    try {
      // Executar o manipulador de rota original
      const response = await handler(request, ...args);
      return response;
    } catch (error: any) {
      // Log do erro com detalhes da requisição
      logger.error(`[API Error] ${requestMethod} ${requestUrl} [${requestId}]: ${error.message}`, {
        requestId,
        method: requestMethod,
        url: requestUrl,
        duration: Date.now() - startTime,
        errorStack: error.stack,
        errorName: error.name,
      });

      // Tratamento específico para erros conhecidos
      if (error instanceof AppError) {
        return formatAppError(error);
      }

      if (error instanceof ZodError) {
        return formatZodError(error);
      }

      // Erros do banco de dados ou externos
      if (isDatabaseError(error)) {
        return formatDatabaseError(error);
      }

      if (isExternalServiceError(error)) {
        return formatExternalServiceError(error);
      }

      // Erro geral desconhecido
      return formatGeneralError(error);
    }
  };
}

/**
 * Formata uma resposta de erro para AppError
 * @param error Erro da aplicação
 * @returns Resposta formatada como JSON
 */
function formatAppError(error: AppError): NextResponse {
  return NextResponse.json(
    {
      success: false,
      error: {
        type: error.type,
        message: error.message,
        code: error.status,
        details: error.details,
      },
    },
    { status: error.status || 500 }
  );
}

/**
 * Formata uma resposta de erro para ZodError
 * @param error Erro de validação Zod
 * @returns Resposta formatada como JSON
 */
function formatZodError(error: ZodError): NextResponse {
  return NextResponse.json(
    {
      success: false,
      error: {
        type: ErrorType.VALIDATION_ERROR,
        message: 'Erro de validação nos dados fornecidos',
        details: error.errors.map(err => ({
          path: err.path.join('.'),
          message: err.message,
        })),
      },
    },
    { status: 400 }
  );
}

/**
 * Formata uma resposta de erro para erros de banco de dados
 * @param error Erro do banco de dados
 * @returns Resposta formatada como JSON
 */
function formatDatabaseError(error: any): NextResponse {
  let message = 'Erro ao acessar o banco de dados';
  let statusCode = 500;

  // Detectar erros específicos do DB
  if (error.code === 'P2002') {
    message = 'O registro já existe com esses dados';
    statusCode = 409;
  } else if (error.code === 'P2025') {
    message = 'Registro não encontrado';
    statusCode = 404;
  }

  return NextResponse.json(
    {
      success: false,
      error: {
        type: ErrorType.DATABASE_ERROR,
        message,
        code: error.code,
      },
    },
    { status: statusCode }
  );
}

/**
 * Formata uma resposta de erro para serviços externos
 * @param error Erro de serviço externo
 * @returns Resposta formatada como JSON
 */
function formatExternalServiceError(error: any): NextResponse {
  const isNetworkError = error.message?.includes('network') || error.code === 'ECONNREFUSED';

  return NextResponse.json(
    {
      success: false,
      error: {
        type: ErrorType.EXTERNAL_SERVICE,
        message: isNetworkError ? 'Erro de conexão com serviço externo' : 'Erro no serviço externo',
        serviceName: error.serviceName || 'unknown',
      },
    },
    { status: isNetworkError ? 503 : 502 }
  );
}

/**
 * Formata uma resposta de erro geral
 * @param error Erro genérico
 * @returns Resposta formatada como JSON
 */
function formatGeneralError(error: any): NextResponse {
  const sanitizedError = {
    message: sanitizeInput(error.message) || 'Erro interno no servidor',
    type: ErrorType.UNKNOWN_ERROR,
  };

  return NextResponse.json(
    {
      success: false,
      error: sanitizedError,
    },
    { status: 500 }
  );
}

/**
 * Verifica se é um erro de banco de dados
 * @param error Erro para verificar
 * @returns true se for erro de DB
 */
function isDatabaseError(error: any): boolean {
  return (
    error.name === 'PrismaClientKnownRequestError' ||
    error.name === 'PrismaClientValidationError' ||
    error.name === 'PrismaClientInitializationError' ||
    error.name === 'PrismaClientRustPanicError' ||
    error.name === 'PrismaClientUnknownRequestError' ||
    error.code?.startsWith('P')
  );
}

/**
 * Verifica se é um erro de serviço externo
 * @param error Erro para verificar
 * @returns true se for erro de serviço externo
 */
function isExternalServiceError(error: any): boolean {
  return (
    error.isAxiosError === true ||
    error.type === ErrorType.EXTERNAL_SERVICE ||
    error.code === 'ECONNREFUSED' ||
    error.message?.includes('fetch failed') ||
    error.name === 'FetchError'
  );
}

/**
 * Middleware de autenticação para proteger rotas de API
 * @param handler Função manipuladora
 * @param options Opções adicionais
 * @returns Função manipuladora com verificação de autenticação
 */
export function withAuth(
  handler: ApiHandler,
  options: {
    adminOnly?: boolean;
    allowPublic?: boolean;
    csrfProtection?: boolean;
  } = {}
): ApiHandler {
  return async (req: ExtendedNextRequest, context?: any) => {
    // Obter a sessão do usuário usando helper tipado
    const session = await getTypedSession();

    // Verificar CSRF se ativado (padrão = true para mutações)
    if (options.csrfProtection !== false && !['GET', 'HEAD', 'OPTIONS'].includes(req.method)) {
      try {
        requireCSRFToken(req);
      } catch (error) {
        // Converter para AppError padronizado
        throw normalizeError(error, 'CSRF Validation');
      }
    }

    // Verificar se autenticação é necessária
    if (!options.allowPublic) {
      if (!session) {
        throw new AppError('Autenticação necessária', ErrorType.AUTHENTICATION_ERROR, 401, {
          path: req.nextUrl.pathname,
          requestId: req.requestId,
        } as any);
      }

      // Verificar permissão de admin se necessário
      if (options.adminOnly && (session.user as any)?.role !== 'ADMIN') {
        throw new AppError(
          'Acesso restrito a administradores',
          ErrorType.AUTHORIZATION_ERROR,
          403,
          { path: req.nextUrl.pathname, requestId: req.requestId } as any
        );
      }

      // Adicionar sessão do usuário à requisição
      req.userSession = session;
    }

    return handler(req, context);
  };
}

/**
 * Middleware para validação de solicitação
 * @param handler Função manipuladora
 * @param validator Função de validação que retorna resultado
 */
export function withValidation<T = any>(
  handler: ApiHandler,
  validator: (body: any) => ValidationResult<T>
): ApiHandler {
  return async (req: ExtendedNextRequest, context?: any) => {
    // Apenas para solicitações com corpo (POST, PUT, etc)
    if (['POST', 'PUT', 'PATCH'].includes(req.method || '')) {
      try {
        // Clonar a request para permitir múltiplas leituras do corpo
        const clonedReq = req.clone();
        const body = await safeParseJson(clonedReq);

        // Verificar padrões suspeitos no corpo da requisição
        detectMaliciousContent(body);

        // Sanitizar o corpo antes da validação para remover possíveis injeções
        const sanitizedBody = sanitizeInput(body);

        // Validar o corpo
        const validation = validator(sanitizedBody);

        if (!validation.isValid) {
          throw new AppError(
            validation.message || 'Dados de entrada inválidos',
            ErrorType.VALIDATION_ERROR,
            400,
            {
              errors: validation.errors,
              path: req.nextUrl.pathname,
              requestId: req.requestId,
            } as any
          );
        }

        // Adicionar corpo validado à requisição
        req.validatedBody = validation.data;
      } catch (error) {
        // Se já for um AppError, propague
        if (error instanceof AppError) {
          throw error;
        }

        // Erro ao processar o corpo da requisição
        throw new AppError(
          'Formato de dados inválido',
          ErrorType.INVALID_INPUT,
          400,
          {
            path: req.nextUrl.pathname,
            requestId: req.requestId,
            originalError: error instanceof Error ? error.message : String(error),
          } as any,
          error
        );
      }
    }

    return handler(req, context);
  };
}

/**
 * Middleware para limitar taxa de requisições
 * @param handler Função manipuladora
 * @param limiter Instância do limitador de taxa
 */
export function withRateLimit(
  handler: ApiHandler,
  limiter: {
    isRateLimited: (id: string) => Promise<boolean>;
    incrementCounter: (id: string) => Promise<void>;
  }
): ApiHandler {
  return async (req: ExtendedNextRequest, context?: any) => {
    // Obter identificador para rate limiting (IP ou ID de usuário autenticado)
    const session = await getTypedSession();
    const identifier = getUserId(session as any) || req.headers.get('x-forwarded-for') || 'unknown';

    // Verificar se está acima do limite
    const limited = await limiter.isRateLimited(identifier);

    if (limited) {
      throw new AppError(
        'Taxa limite excedida. Tente novamente mais tarde.',
        ErrorType.RATE_LIMIT_ERROR,
        429,
        { path: req.nextUrl.pathname, requestId: req.requestId } as any
      );
    }

    // Incrementar contador de requisições
    await limiter.incrementCounter(identifier);

    return handler(req, context);
  };
}

/**
 * Função para parse seguro de JSON
 */
async function safeParseJson(req: Request | NextRequest): Promise<any> {
  try {
    return await req.json();
  } catch (error) {
    throw new AppError(
      'Erro ao processar corpo da requisição: formato JSON inválido',
      ErrorType.INVALID_INPUT,
      400,
      { originalError: error instanceof Error ? error.message : String(error) } as any,
      error
    );
  }
}

/**
 * Gera um ID único para a requisição
 */
function generateRequestId(): string {
  return Date.now().toString(36) + Math.random().toString(36).substring(2, 10).toUpperCase();
}

/**
 * Sanitiza parâmetros de query para evitar injeção
 */
function sanitizeQueryParams(req: ExtendedNextRequest): Record<string, string> {
  const sanitized: Record<string, string> = {};
  const url = new URL(req.url);

  // Sanitizar cada parâmetro
  for (const [key, value] of url.searchParams.entries()) {
    // Eliminar valores com padrões suspeitos
    if (!hasSuspiciousPatterns(value)) {
      sanitized[key] = value;
    }
  }

  return sanitized;
}

/**
 * Verifica padrões suspeitos que podem indicar tentativas de ataque
 */
function checkForSuspiciousPatterns(req: ExtendedNextRequest): void {
  const url = new URL(req.url);

  // Verificar path da URL
  if (hasSuspiciousPatterns(url.pathname)) {
    throw new AppError(
      'Requisição bloqueada por motivos de segurança',
      ErrorType.AUTHORIZATION_ERROR,
      403,
      { path: url.pathname, requestId: req.requestId } as any
    );
  }

  // Verificar parâmetros de query
  for (const [key, value] of url.searchParams.entries()) {
    if (hasSuspiciousPatterns(value)) {
      throw new AppError(
        'Parâmetros de consulta contêm padrões suspeitos',
        ErrorType.AUTHORIZATION_ERROR,
        403,
        { path: url.pathname, param: key, requestId: req.requestId } as any
      );
    }
  }
}

/**
 * Detecta conteúdo malicioso no corpo da requisição
 */
function detectMaliciousContent(body: any): void {
  if (!body || typeof body !== 'object') return;

  // Verificar campos comuns que podem conter injeções
  const sensitiveFields = [
    'query',
    'search',
    'filter',
    'name',
    'description',
    'html',
    'content',
    'code',
  ];

  for (const field of sensitiveFields) {
    if (body[field] && typeof body[field] === 'string' && hasSuspiciousPatterns(body[field])) {
      throw new AppError(
        'Conteúdo bloqueado por motivos de segurança',
        ErrorType.AUTHORIZATION_ERROR,
        403,
        { field } as any
      );
    }
  }

  // Verificar recursivamente objetos aninhados
  for (const key of Object.keys(body)) {
    if (typeof body[key] === 'object' && body[key] !== null) {
      detectMaliciousContent(body[key]);
    }
  }
}
