#!/usr/bin/env tsx

/**
 * Script de demonstração para correção automática de problemas de assinatura
 * Uso: npx tsx scripts/demo-subscription-fix.ts
 */

import { config } from 'dotenv';
import { resolve } from 'path';
import { PrismaClient } from '@prisma/client';

// Carregar variáveis de ambiente
config({ path: resolve(process.cwd(), '.env.local') });

// Constantes locais
const PLANS = {
  FREE: 'free',
  PRO_MONTHLY: 'pro_monthly',
  PRO_ANNUAL: 'pro_annual',
};

const API_CALL_LIMITS = {
  [PLANS.FREE]: 50,
  [PLANS.PRO_MONTHLY]: 500,
  [PLANS.PRO_ANNUAL]: 1000,
};

const prisma = new PrismaClient();

async function demonstrateSubscriptionFix(): Promise<void> {
  console.log('🔧 Demonstração de Correção Automática de Assinaturas\n');

  try {
    // 1. Verificar estado atual
    console.log('📊 Estado atual do sistema:');

    const totalUsers = await prisma.user.count();
    const usersWithSubscription = await prisma.user.count({
      where: {
        subscriptions: {
          some: {},
        },
      },
    });
    const usersWithoutSubscription = totalUsers - usersWithSubscription;

    console.log(`   👥 Total de usuários: ${totalUsers}`);
    console.log(`   ✅ Usuários com assinatura: ${usersWithSubscription}`);
    console.log(`   ⚠️  Usuários sem assinatura: ${usersWithoutSubscription}`);

    // 2. Simular verificação de problemas
    console.log('\n🔍 Verificando problemas potenciais...');

    const subscriptions = await prisma.subscription.findMany({
      include: {
        user: {
          select: {
            email: true,
          },
        },
      },
    });

    let issuesFound = 0;
    let issuesFixed = 0;

    // Verificar limites de API inconsistentes
    for (const subscription of subscriptions) {
      const expectedLimit = API_CALL_LIMITS[subscription.plan as keyof typeof API_CALL_LIMITS];

      if (expectedLimit && subscription.apiCallsLimit !== expectedLimit) {
        issuesFound++;
        console.log(
          `   🔧 Corrigindo limite de API para ${subscription.user.email}: ${subscription.apiCallsLimit} → ${expectedLimit}`
        );

        // Corrigir o limite
        await prisma.subscription.update({
          where: { id: subscription.id },
          data: { apiCallsLimit: expectedLimit },
        });

        issuesFixed++;
      }
    }

    // Verificar assinaturas com status inconsistente
    const inactiveSubscriptions = await prisma.subscription.findMany({
      where: {
        status: { not: 'active' },
        currentPeriodEnd: null, // Planos Free devem ser ativos
        plan: PLANS.FREE,
      },
      include: {
        user: {
          select: {
            email: true,
          },
        },
      },
    });

    for (const subscription of inactiveSubscriptions) {
      issuesFound++;
      console.log(`   🔧 Ativando assinatura Free para ${subscription.user.email}`);

      await prisma.subscription.update({
        where: { id: subscription.id },
        data: { status: 'active' },
      });

      issuesFixed++;
    }

    // 3. Relatório final
    console.log('\n' + '='.repeat(50));
    console.log('📋 RELATÓRIO DE CORREÇÃO AUTOMÁTICA');
    console.log('='.repeat(50));
    console.log(`🔍 Problemas encontrados: ${issuesFound}`);
    console.log(`✅ Problemas corrigidos: ${issuesFixed}`);

    if (issuesFound === 0) {
      console.log('\n🎉 Nenhum problema encontrado! Sistema está funcionando perfeitamente.');
      console.log('\n💡 Demonstração das funcionalidades implementadas:');
      console.log('   ✅ Atribuição automática de plano Free para novos usuários');
      console.log('   ✅ Migração de usuários existentes sem assinatura');
      console.log('   ✅ Validação de integridade automática');
      console.log('   ✅ Rate limiting baseado em plano');
      console.log('   ✅ Correção automática de inconsistências');
      console.log('   ✅ Monitoramento e logs detalhados');
    } else {
      console.log('\n✅ Todos os problemas foram corrigidos automaticamente!');
    }

    // 4. Verificar estado final
    console.log('\n📊 Estado final do sistema:');

    const finalStats = await prisma.subscription.groupBy({
      by: ['plan', 'status'],
      _count: {
        plan: true,
      },
    });

    finalStats.forEach(stat => {
      console.log(`   - ${stat.plan} (${stat.status}): ${stat._count.plan} usuários`);
    });

    // 5. Demonstrar funcionalidades de monitoramento
    console.log('\n📈 Funcionalidades de Monitoramento Ativas:');
    console.log('   🔍 Verificação de integridade em tempo real');
    console.log('   📊 Rate limiting baseado no plano do usuário');
    console.log('   🚨 Detecção automática de inconsistências');
    console.log('   📝 Logs estruturados para auditoria');
    console.log('   🔧 Correção automática de problemas menores');
  } catch (error) {
    console.error('💥 Erro durante a demonstração:', error);
    throw error;
  }
}

async function main(): Promise<void> {
  try {
    await demonstrateSubscriptionFix();
  } catch (error) {
    console.error('\n💥 Falha na demonstração:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Executar script se chamado diretamente
if (require.main === module) {
  main().catch(console.error);
}

export { demonstrateSubscriptionFix };
