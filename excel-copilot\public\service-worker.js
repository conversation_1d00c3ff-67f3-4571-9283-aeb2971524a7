// Nome e versão do cache para controle de atualização
const CACHE_NAME = 'excel-copilot-cache-v1';
const DATA_CACHE_NAME = 'excel-copilot-data-cache-v1';

// Assets essenciais para funcionamento offline
const STATIC_ASSETS = [
  '/',
  '/index.html',
  '/manifest.json',
  '/favicon.ico',
  '/offline.html',
  '/static/css/main.css',
  '/static/js/main.js',
];

// URLs de API que devem ser armazenadas em cache separado
const API_URLS = ['/api/workbooks'];

// Instalação do Service Worker - Pré-cache de recursos estáticos
self.addEventListener('install', event => {
  event.waitUntil(
    caches
      .open(CACHE_NAME)
      .then(cache => {
        console.log('Service Worker: Caching static assets');
        return cache.addAll(STATIC_ASSETS);
      })
      .then(() => self.skipWaiting())
  );
});

// Ativação do Service Worker - Limpeza de caches antigos
self.addEventListener('activate', event => {
  event.waitUntil(
    caches
      .keys()
      .then(cacheNames => {
        return Promise.all(
          cacheNames.map(cacheName => {
            if (cacheName !== CACHE_NAME && cacheName !== DATA_CACHE_NAME) {
              console.log('Service Worker: Removing old cache', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => self.clients.claim())
  );
});

// Interceptação de requisições - Estratégia Cache First com Network Fallback para estáticos
// e Network First com Cache Fallback para APIs
self.addEventListener('fetch', event => {
  // Verificar se é uma requisição de API
  const isApiRequest = event.request.url.includes('/api/');

  // Não interceptar requisições de navegação ou POST
  if (event.request.mode === 'navigate' && event.request.method === 'GET') {
    event.respondWith(
      fetch(event.request).catch(() => {
        return caches.match('/offline.html');
      })
    );
    return;
  }

  // Para requisições que não são GET, tentar enviar e enfileirar se offline
  if (event.request.method !== 'GET') {
    // Se for uma requisição mutável (PUT, POST, DELETE) e estiver offline,
    // adicionar à fila de sincronização
    event.respondWith(
      fetch(event.request).catch(error => {
        console.log('Failed request, queueing for sync:', event.request.url);

        // Se for uma requisição relacionada a planilhas, adicionar à fila de sincronização
        if (event.request.url.includes('/api/workbooks')) {
          // Salvar request para sincronização posterior
          saveRequestForSync(event.request.clone())
            .then(() => {
              // Registrar evento de sincronização
              return self.registration.sync.register('sync-workbooks');
            })
            .catch(err => console.error('Error queuing for sync:', err));
        }

        // Retornar resposta offline apropriada
        return new Response(
          JSON.stringify({
            error: true,
            message:
              'Você está offline. Esta alteração será sincronizada quando você ficar online.',
            offlineQueued: true,
          }),
          {
            status: 503,
            headers: { 'Content-Type': 'application/json' },
          }
        );
      })
    );
    return;
  }

  // Estratégia para requisições de API: Network First, com fallback para Cache
  if (isApiRequest) {
    event.respondWith(
      fetch(event.request)
        .then(response => {
          // Se a resposta for válida, clone-a e armazene no cache
          if (response && response.status === 200) {
            const responseClone = response.clone();
            caches.open(DATA_CACHE_NAME).then(cache => {
              cache.put(event.request, responseClone);
            });
          }
          return response;
        })
        .catch(() => {
          // Se falhar, tentar buscar do cache
          return caches.match(event.request).then(cachedResponse => {
            if (cachedResponse) {
              // Adicionar header indicando que é um dado em cache
              const headers = new Headers(cachedResponse.headers);
              headers.append('X-Excel-Copilot-Cache', 'true');

              return new Response(cachedResponse.body, {
                status: cachedResponse.status,
                statusText: cachedResponse.statusText,
                headers,
              });
            }

            // Se não estiver no cache, retornar resposta de erro offline
            return new Response(
              JSON.stringify({
                error: true,
                message: 'Você está offline e este recurso não está disponível no cache.',
              }),
              {
                status: 503,
                headers: { 'Content-Type': 'application/json' },
              }
            );
          });
        })
    );
    return;
  }

  // Estratégia para recursos estáticos: Cache First, com fallback para Network
  event.respondWith(
    caches.match(event.request).then(cachedResponse => {
      // Se encontrado no cache, retornar
      if (cachedResponse) {
        return cachedResponse;
      }

      // Se não estiver no cache, buscar da rede
      return fetch(event.request).then(response => {
        // Verificar se a resposta é válida
        if (!response || response.status !== 200 || response.type !== 'basic') {
          return response;
        }

        // Clonar a resposta, pois o corpo só pode ser lido uma vez
        const responseClone = response.clone();

        // Armazenar no cache
        caches.open(CACHE_NAME).then(cache => {
          cache.put(event.request, responseClone);
        });

        return response;
      });
    })
  );
});

// Evento de sincronização background - processamento de operações em fila
self.addEventListener('sync', event => {
  if (event.tag === 'sync-workbooks') {
    event.waitUntil(syncPendingRequests());
  }
});

// Função para salvar uma requisição para sincronização posterior
async function saveRequestForSync(request) {
  try {
    // Abrir o banco offline
    const db = await openOfflineDB();

    // Extrair dados da requisição
    const requestData = {
      url: request.url,
      method: request.method,
      headers: Array.from(request.headers.entries()),
      timestamp: Date.now(),
      body: await request.clone().text(),
    };

    // Adicionar à store pendingRequests
    const tx = db.transaction('pendingRequests', 'readwrite');
    await tx.objectStore('pendingRequests').add(requestData);
    await tx.complete;

    console.log('Request saved for later sync:', requestData.url);

    // Fechar o banco
    db.close();

    return true;
  } catch (error) {
    console.error('Error saving request for sync:', error);
    return false;
  }
}

// Função para sincronizar requisições pendentes
async function syncPendingRequests() {
  try {
    // Abrir o banco offline
    const db = await openOfflineDB();

    // Obter todas as requisições pendentes
    const tx = db.transaction('pendingRequests', 'readonly');
    const pendingRequests = await tx.objectStore('pendingRequests').getAll();

    // Fechar a transação
    await tx.complete;

    console.log(`Found ${pendingRequests.length} requests to sync`);

    // Processar cada requisição
    const syncPromises = pendingRequests.map(async requestData => {
      try {
        // Reconstruir a requisição
        const request = new Request(requestData.url, {
          method: requestData.method,
          headers: new Headers(requestData.headers),
          body: requestData.method !== 'GET' ? requestData.body : undefined,
        });

        // Tentar enviar a requisição
        const response = await fetch(request);

        if (response.ok) {
          // Se sucesso, remover da fila
          const deleteTx = db.transaction('pendingRequests', 'readwrite');
          await deleteTx.objectStore('pendingRequests').delete(requestData.id);
          await deleteTx.complete;
          console.log('Successfully synced request:', requestData.url);
          return { success: true, request: requestData };
        } else {
          console.error('Sync error:', requestData.url, response.status);
          return { success: false, request: requestData, error: `Status ${response.status}` };
        }
      } catch (error) {
        console.error('Error syncing request:', requestData.url, error);
        return { success: false, request: requestData, error: error.message };
      }
    });

    // Aguardar todas as sincronizações
    const results = await Promise.all(syncPromises);

    // Fechar o banco
    db.close();

    // Notificar todas as janelas abertas do resultado da sincronização
    self.clients.matchAll().then(clients => {
      clients.forEach(client => {
        client.postMessage({
          type: 'SYNC_COMPLETED',
          results: results,
          timestamp: Date.now(),
          success: results.every(r => r.success),
        });
      });
    });

    return results;
  } catch (error) {
    console.error('Error during sync process:', error);
    return [];
  }
}

// Função para abrir o IndexedDB offline
function openOfflineDB() {
  return new Promise((resolve, reject) => {
    const request = indexedDB.open('excelCopilotOfflineDB', 1);

    request.onupgradeneeded = event => {
      const db = event.target.result;

      // Criar object store para requisições pendentes
      if (!db.objectStoreNames.contains('pendingRequests')) {
        const store = db.createObjectStore('pendingRequests', {
          keyPath: 'id',
          autoIncrement: true,
        });
        store.createIndex('url', 'url', { unique: false });
        store.createIndex('timestamp', 'timestamp', { unique: false });
      }

      // Criar object store para dados offline
      if (!db.objectStoreNames.contains('workbooks')) {
        const store = db.createObjectStore('workbooks', {
          keyPath: 'id',
        });
        store.createIndex('userId', 'userId', { unique: false });
        store.createIndex('updatedAt', 'updatedAt', { unique: false });
      }
    };

    request.onsuccess = event => {
      resolve(event.target.result);
    };

    request.onerror = event => {
      reject(event.target.error);
    };
  });
}

// Evento de mensagem - Comunicação com a página
self.addEventListener('message', event => {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }

  if (event.data && event.data.type === 'TRIGGER_SYNC') {
    self.registration.sync.register('sync-workbooks');
  }
});

// Evento de push - Notificações
self.addEventListener('push', event => {
  if (!event.data) return;

  try {
    const data = event.data.json();

    const options = {
      body: data.body || 'Nova atualização disponível',
      icon: '/icons/icon-192x192.png',
      badge: '/icons/badge-72x72.png',
      data: {
        url: data.url || '/',
      },
      actions: data.actions || [],
    };

    event.waitUntil(self.registration.showNotification(data.title || 'Excel Copilot', options));
  } catch (error) {
    console.error('Error showing notification:', error);
  }
});

// Evento de notificação click - Navegação
self.addEventListener('notificationclick', event => {
  event.notification.close();

  if (event.notification.data && event.notification.data.url) {
    event.waitUntil(clients.openWindow(event.notification.data.url));
  }
});
