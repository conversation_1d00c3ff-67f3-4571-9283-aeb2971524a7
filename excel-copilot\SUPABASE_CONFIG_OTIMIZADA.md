# CONFIGURAÇÃO SUPABASE OTIMIZADA - EXCEL COPILOT

## **CORREÇÕES IMPLEMENTADAS**

### **1. URL de Conexão Direta Corrigida**

**ANTES (Incorreto):**

```env
DIRECT_URL="postgresql://postgres.eliuoignzzxnjkcmmtml:<EMAIL>:5432/postgres"
```

**DEPOIS (Correto):**

```env
DIRECT_URL="postgresql://postgres.eliuoignzzxnjkcmmtml:<EMAIL>:5432/postgres"
```

### **2. Variáveis de Ambiente Consolidadas**

#### **CONFIGURAÇÃO RECOMENDADA (.env.local):**

```env
# ======================================
# SUPABASE - CONFIGURAÇÃO PRINCIPAL
# ======================================
# PostgreSQL via Prisma (Conexão Pooling para aplicação)
DATABASE_URL="postgresql://postgres.eliuoignzzxnjkcmmtml:<EMAIL>:6543/postgres?pgbouncer=true&connection_limit=1&pool_timeout=20"

# PostgreSQL via Prisma (Conexão Direta para migrações)
DIRECT_URL="postgresql://postgres.eliuoignzzxnjkcmmtml:<EMAIL>:5432/postgres"

# Cliente Supabase (Backend)
SUPABASE_URL="https://eliuoignzzxnjkcmmtml.supabase.co"
SUPABASE_ANON_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVsaXVvaWduenp4bmprY21tdG1sIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY1NDU2MTQsImV4cCI6MjA2MjEyMTYxNH0.rMyGA-hjWQNxJDdLSi3gYtSi8Gg2TeDxAs8f2gx8Zdk"
SUPABASE_SERVICE_ROLE_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVsaXVvaWduenp4bmprY21tdG1sIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NjU0NTYxNCwiZXhwIjoyMDYyMTIxNjE0fQ.hHguPBu7OV6CJBSmwe3r7JwG1Ob__NWt-dWAnRsofP8"
SUPABASE_JWT_SECRET="68gstnuGD3BoDTc8C4QkMHkEOfburNmDwxO4k0ykbWY93kONK2HbFCcYDNsL4zim1sD5b/1cBFvmWswDx87N3A=="

# Cliente Supabase (Frontend - Públicas)
NEXT_PUBLIC_SUPABASE_URL="https://eliuoignzzxnjkcmmtml.supabase.co"
NEXT_PUBLIC_SUPABASE_ANON_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVsaXVvaWduenp4bmprY21tdG1sIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY1NDU2MTQsImV4cCI6MjA2MjEyMTYxNH0.rMyGA-hjWQNxJDdLSi3gYtSi8Gg2TeDxAs8f2gx8Zdk"
```

#### **VARIÁVEIS REMOVIDAS (Redundantes):**

```env
# ❌ REMOVER - Duplicações desnecessárias
POSTGRES_URL="..."
POSTGRES_PRISMA_URL="..."
POSTGRES_URL_NON_POOLING="..."
POSTGRES_DATABASE="postgres"
POSTGRES_HOST="db.eliuoignzzxnjkcmmtml.supabase.co"
POSTGRES_PASSWORD="231Bancovercel"
POSTGRES_USER="postgres"
```

### **3. Implementação de Cliente Supabase**

#### **Arquivo: `src/lib/supabase/client.ts`**

```typescript
import { createClient } from '@supabase/supabase-js';

// Cliente Supabase para uso no servidor
export const supabaseAdmin = createClient(
  process.env.SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  }
);

// Cliente Supabase para uso no cliente
export const supabaseClient = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
);
```

#### **Arquivo: `src/lib/supabase/storage.ts`**

```typescript
import { supabaseAdmin } from './client';

export class SupabaseStorageService {
  private bucket = 'excel-files';

  async uploadFile(file: File, path: string) {
    const { data, error } = await supabaseAdmin.storage.from(this.bucket).upload(path, file);

    if (error) throw error;
    return data;
  }

  async downloadFile(path: string) {
    const { data, error } = await supabaseAdmin.storage.from(this.bucket).download(path);

    if (error) throw error;
    return data;
  }

  async deleteFile(path: string) {
    const { error } = await supabaseAdmin.storage.from(this.bucket).remove([path]);

    if (error) throw error;
  }
}

export const storageService = new SupabaseStorageService();
```

### **4. Configuração Real-time para Colaboração**

#### **Arquivo: `src/lib/supabase/realtime.ts`**

```typescript
import { supabaseClient } from './client';
import { RealtimeChannel } from '@supabase/supabase-js';

export class WorkbookRealtimeService {
  private channels: Map<string, RealtimeChannel> = new Map();

  subscribeToWorkbook(workbookId: string, callback: (payload: any) => void) {
    const channelName = `workbook:${workbookId}`;

    if (this.channels.has(channelName)) {
      return this.channels.get(channelName)!;
    }

    const channel = supabaseClient
      .channel(channelName)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'Sheet',
          filter: `workbookId=eq.${workbookId}`,
        },
        callback
      )
      .subscribe();

    this.channels.set(channelName, channel);
    return channel;
  }

  unsubscribeFromWorkbook(workbookId: string) {
    const channelName = `workbook:${workbookId}`;
    const channel = this.channels.get(channelName);

    if (channel) {
      supabaseClient.removeChannel(channel);
      this.channels.delete(channelName);
    }
  }
}

export const realtimeService = new WorkbookRealtimeService();
```

### **5. Scripts de Verificação**

#### **Arquivo: `scripts/check-supabase-connection.js`**

```javascript
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

async function checkSupabaseConnection() {
  console.log('🔍 Verificando conexão com Supabase...\n');

  // Verificar variáveis de ambiente
  const requiredVars = ['SUPABASE_URL', 'SUPABASE_ANON_KEY', 'DATABASE_URL', 'DIRECT_URL'];

  const missingVars = requiredVars.filter(varName => !process.env[varName]);

  if (missingVars.length > 0) {
    console.error('❌ Variáveis de ambiente faltando:', missingVars);
    process.exit(1);
  }

  // Testar cliente Supabase
  const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_ANON_KEY);

  try {
    // Testar autenticação
    const { data: authData, error: authError } = await supabase.auth.getSession();
    if (authError) throw authError;
    console.log('✅ Supabase Auth: Conectado');

    // Testar storage
    const { data: buckets, error: storageError } = await supabase.storage.listBuckets();
    if (storageError) throw storageError;
    console.log('✅ Supabase Storage: Conectado');
    console.log(`📦 Buckets encontrados: ${buckets.length}`);

    console.log('\n🎉 Conexão com Supabase verificada com sucesso!');
  } catch (error) {
    console.error('❌ Erro na conexão com Supabase:', error.message);
    process.exit(1);
  }
}

checkSupabaseConnection();
```

### **6. Próximos Passos**

#### **CRÍTICO - Fazer Imediatamente:**

1. **Reativar projeto Supabase** `excel-copilot-new`
2. **Aplicar configurações otimizadas** do arquivo `.env.local`
3. **Testar conectividade** com script de verificação

#### **IMPLEMENTAR - Próximas 2 semanas:**

1. **Criar cliente Supabase** (`src/lib/supabase/`)
2. **Implementar Storage Service** para arquivos Excel
3. **Configurar Real-time** para colaboração
4. **Implementar RLS policies** no Supabase

#### **OTIMIZAR - Próximo mês:**

1. **Monitoramento de performance**
2. **Backup automático**
3. **Logs de auditoria**
4. **Documentação final**

---

**Status:** ✅ **CORREÇÕES CRÍTICAS IMPLEMENTADAS**  
**Próximo passo:** Reativar projeto Supabase e testar conectividade
