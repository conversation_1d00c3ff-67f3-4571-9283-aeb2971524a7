/**
 * Sistema de logging estruturado para eventos de autenticação
 * Monitora e registra todas as atividades relacionadas ao OAuth
 */

import { logger } from '@/lib/logger';
import { NextRequest } from 'next/server';

export interface AuthEvent {
  type: AuthEventType;
  userId?: string | undefined;
  email?: string | undefined;
  provider?: string | undefined;
  ip?: string | undefined;
  userAgent?: string | undefined;
  sessionId?: string | undefined;
  error?: string | undefined;
  metadata?: Record<string, any> | undefined;
  timestamp: string;
}

export enum AuthEventType {
  // Eventos de Login
  LOGIN_ATTEMPT = 'login_attempt',
  LOGIN_SUCCESS = 'login_success',
  LOGIN_FAILURE = 'login_failure',

  // Eventos OAuth
  OAUTH_REDIRECT = 'oauth_redirect',
  OAUTH_CALLBACK = 'oauth_callback',
  OAUTH_SUCCESS = 'oauth_success',
  OAUTH_ERROR = 'oauth_error',

  // Eventos de Sessão
  SESSION_CREATED = 'session_created',
  SESSION_REFRESHED = 'session_refreshed',
  SESSION_EXPIRED = 'session_expired',

  // Eventos de Logout
  LOGOUT_SUCCESS = 'logout_success',
  LOGOUT_ERROR = 'logout_error',

  // Eventos de Segurança
  SUSPICIOUS_ACTIVITY = 'suspicious_activity',
  RATE_LIMIT_EXCEEDED = 'rate_limit_exceeded',
  INVALID_TOKEN = 'invalid_token',

  // Eventos de Configuração
  PROVIDER_CONFIG_ERROR = 'provider_config_error',
  AUTH_CONFIG_LOADED = 'auth_config_loaded',
}

/**
 * Extrai informações do request de forma segura
 */
function extractRequestInfo(request?: NextRequest): {
  ip?: string | undefined;
  userAgent?: string | undefined;
} {
  if (!request) return {};

  const forwardedFor = request.headers.get('x-forwarded-for');
  const ip =
    forwardedFor?.split(',')[0]?.trim() ||
    request.headers.get('x-real-ip') ||
    request.ip ||
    'unknown';

  const userAgent = request.headers.get('user-agent') || 'unknown';

  return { ip, userAgent };
}

/**
 * Classe principal para logging de autenticação
 */
export class AuthLogger {
  /**
   * Registra evento de tentativa de login
   */
  static logLoginAttempt(
    provider: string,
    email?: string,
    request?: NextRequest,
    metadata?: Record<string, any>
  ): void {
    const event: AuthEvent = {
      type: AuthEventType.LOGIN_ATTEMPT,
      provider,
      email,
      ...extractRequestInfo(request),
      metadata,
      timestamp: new Date().toISOString(),
    };

    logger.info('Auth login attempt', event);
  }

  /**
   * Registra evento de login bem-sucedido
   */
  static logLoginSuccess(
    userId: string,
    email: string,
    provider: string,
    sessionId: string,
    request?: NextRequest,
    metadata?: Record<string, any>
  ): void {
    const event: AuthEvent = {
      type: AuthEventType.LOGIN_SUCCESS,
      userId,
      email,
      provider,
      sessionId,
      ...extractRequestInfo(request),
      metadata,
      timestamp: new Date().toISOString(),
    };

    logger.info('Auth login success', event);
  }

  /**
   * Registra evento de falha no login
   */
  static logLoginFailure(
    provider: string,
    error: string,
    email?: string,
    request?: NextRequest,
    metadata?: Record<string, any>
  ): void {
    const event: AuthEvent = {
      type: AuthEventType.LOGIN_FAILURE,
      provider,
      email,
      error,
      ...extractRequestInfo(request),
      metadata,
      timestamp: new Date().toISOString(),
    };

    logger.warn('Auth login failure', event);
  }

  /**
   * Registra redirecionamento OAuth
   */
  static logOAuthRedirect(
    provider: string,
    request?: NextRequest,
    metadata?: Record<string, any>
  ): void {
    const event: AuthEvent = {
      type: AuthEventType.OAUTH_REDIRECT,
      provider,
      ...extractRequestInfo(request),
      metadata,
      timestamp: new Date().toISOString(),
    };

    logger.info('OAuth redirect', event);
  }

  /**
   * Registra callback OAuth
   */
  static logOAuthCallback(
    provider: string,
    success: boolean,
    error?: string,
    request?: NextRequest,
    metadata?: Record<string, any>
  ): void {
    const event: AuthEvent = {
      type: success ? AuthEventType.OAUTH_SUCCESS : AuthEventType.OAUTH_ERROR,
      provider,
      error,
      ...extractRequestInfo(request),
      metadata,
      timestamp: new Date().toISOString(),
    };

    if (success) {
      logger.info('OAuth callback success', event);
    } else {
      logger.error('OAuth callback error', event);
    }
  }

  /**
   * Registra criação de sessão
   */
  static logSessionCreated(
    userId: string,
    email: string,
    sessionId: string,
    provider: string,
    request?: NextRequest
  ): void {
    const event: AuthEvent = {
      type: AuthEventType.SESSION_CREATED,
      userId,
      email,
      sessionId,
      provider,
      ...extractRequestInfo(request),
      timestamp: new Date().toISOString(),
    };

    logger.info('Session created', event);
  }

  /**
   * Registra logout
   */
  static logLogout(
    userId?: string,
    sessionId?: string,
    request?: NextRequest,
    error?: string
  ): void {
    const event: AuthEvent = {
      type: error ? AuthEventType.LOGOUT_ERROR : AuthEventType.LOGOUT_SUCCESS,
      userId,
      sessionId,
      error,
      ...extractRequestInfo(request),
      timestamp: new Date().toISOString(),
    };

    if (error) {
      logger.warn('Logout error', event);
    } else {
      logger.info('Logout success', event);
    }
  }

  /**
   * Registra atividade suspeita
   */
  static logSuspiciousActivity(
    reason: string,
    request?: NextRequest,
    userId?: string,
    metadata?: Record<string, any>
  ): void {
    const event: AuthEvent = {
      type: AuthEventType.SUSPICIOUS_ACTIVITY,
      userId,
      error: reason,
      ...extractRequestInfo(request),
      metadata,
      timestamp: new Date().toISOString(),
    };

    logger.error('Suspicious auth activity detected', event);
  }

  /**
   * Registra excesso de rate limit
   */
  static logRateLimitExceeded(
    limitType: string,
    request?: NextRequest,
    metadata?: Record<string, any>
  ): void {
    const event: AuthEvent = {
      type: AuthEventType.RATE_LIMIT_EXCEEDED,
      error: `Rate limit exceeded: ${limitType}`,
      ...extractRequestInfo(request),
      metadata,
      timestamp: new Date().toISOString(),
    };

    logger.warn('Auth rate limit exceeded', event);
  }

  /**
   * Registra erro de configuração de provider
   */
  static logProviderConfigError(
    provider: string,
    error: string,
    metadata?: Record<string, any>
  ): void {
    const event: AuthEvent = {
      type: AuthEventType.PROVIDER_CONFIG_ERROR,
      provider,
      error,
      metadata,
      timestamp: new Date().toISOString(),
    };

    logger.error('Auth provider config error', event);
  }
}

/**
 * Middleware para logging automático de requests de autenticação
 */
export function createAuthLoggingMiddleware() {
  return (request: NextRequest) => {
    const url = new URL(request.url);
    const pathname = url.pathname;

    // Detectar tipo de request OAuth
    if (pathname.includes('/api/auth/signin')) {
      const provider = url.searchParams.get('provider') || 'unknown';
      AuthLogger.logLoginAttempt(provider, undefined, request);
    }

    if (pathname.includes('/api/auth/callback')) {
      const provider = pathname.split('/').pop() || 'unknown';
      const error = url.searchParams.get('error');

      if (error) {
        AuthLogger.logOAuthCallback(provider, false, error, request);
      }
    }

    if (pathname.includes('/api/auth/signout')) {
      AuthLogger.logLogout(undefined, undefined, request);
    }
  };
}
