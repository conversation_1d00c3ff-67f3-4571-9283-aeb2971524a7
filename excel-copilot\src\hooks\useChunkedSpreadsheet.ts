import { useState, useCallback, useEffect, useRef } from 'react';

import { logError } from '../lib/utils/error-utils';

import { useChunkedData } from './useChunkedData';

/**
 * Interface para dados da planilha
 */
interface SpreadsheetData {
  headers: string[];
  rows: unknown[][];
  charts?: unknown[];
  name: string;
}

interface UseChunkedSpreadsheetOptions {
  workbookId: string;
  sheetId: string;
  initialData?: SpreadsheetData;
  chunkSize?: number;
  onDataChange?: (data: SpreadsheetData) => void;
  onError?: (error: Error) => void;
}

/**
 * Hook para gerenciar dados de planilha em chunks
 */
export function useChunkedSpreadsheet({
  workbookId,
  sheetId,
  initialData,
  chunkSize = 1000,
  onDataChange,
  onError,
}: UseChunkedSpreadsheetOptions) {
  // Estado para armazenar a planilha completa
  const [spreadsheetData, setSpreadsheetData] = useState<SpreadsheetData>(
    initialData || {
      headers: [],
      rows: [],
      charts: [],
      name: 'Nova Planilha',
    }
  );

  // Referência para o estado atual para usar em callbacks
  const spreadsheetDataRef = useRef(spreadsheetData);
  useEffect(() => {
    spreadsheetDataRef.current = spreadsheetData;
  }, [spreadsheetData]);

  // Contadores para updates e necessidade de persistência
  const [updateCounter, setUpdateCounter] = useState(0);
  const [needsPersistence, setNeedsPersistence] = useState(false);

  // Estado para loading
  const [isLoading, setIsLoading] = useState(false);

  // Função para carregar um chunk
  const loadChunk = useCallback(
    async (chunkIndex: number): Promise<unknown[][]> => {
      try {
        const response = await fetch(
          `/api/workbooks/${workbookId}/sheets/${sheetId}/chunks?chunk=${chunkIndex}&chunkSize=${chunkSize}`
        );

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(
            errorData.details || errorData.error || `Erro ${response.status} ao carregar chunk`
          );
        }

        const data = await response.json();
        return data.rowsChunk || [];
      } catch (error) {
        logError(`Erro ao carregar chunk ${chunkIndex}:`, error);
        throw error;
      }
    },
    [workbookId, sheetId, chunkSize]
  );

  // Inicializar o hook de dados em chunks
  const chunkedData = useChunkedData<unknown[]>({
    loadChunk,
    initialData: initialData?.rows || [],
    totalItems: initialData?.rows?.length || 0,
    chunkSize,
    onLoadingChange: setIsLoading,
    // Fornecer uma função de erro padrão se onError não for definido
    onError:
      onError ||
      ((error: Error) => {
        console.error('Erro no carregamento de dados em chunks:', error);
      }),
  });

  // Função para atualizar uma célula específica
  const updateCell = useCallback(
    (rowIndex: number, colIndex: number, value: unknown) => {
      // Primeiro, atualizar no gerenciador de chunks
      const currentRow =
        chunkedData.getItem(rowIndex) || Array(spreadsheetDataRef.current.headers.length).fill('');
      const newRow = Array.isArray(currentRow)
        ? [...currentRow]
        : Array(spreadsheetDataRef.current.headers.length).fill('');
      newRow[colIndex] = value;
      chunkedData.updateItem(rowIndex, newRow);

      // Incrementar contador de atualizações e marcar para persistência
      setUpdateCounter(prev => prev + 1);
      setNeedsPersistence(true);

      // Notificar mudança se necessário (throttled para não sobrecarregar)
      if (updateCounter % 10 === 0 || needsPersistence) {
        // Gerar um novo objeto de dados para notificar componentes pais
        const updateData = async () => {
          // Obter as linhas atualmente visíveis apenas
          const visibleRows = await chunkedData.getRange(0, chunkedData.totalItems - 1);

          const updatedData: SpreadsheetData = {
            ...spreadsheetDataRef.current,
            rows: visibleRows,
          };

          setSpreadsheetData(updatedData);

          // Notificar componente pai se necessário
          if (onDataChange) {
            onDataChange(updatedData);
          }
        };

        updateData();
      }
    },
    [chunkedData, updateCounter, needsPersistence, onDataChange]
  );

  // Função para carregar um intervalo de linhas
  const loadRowRange = useCallback(
    async (startRow: number, endRow: number) => {
      try {
        // Obter linhas do gerenciador de chunks
        const rows = await chunkedData.getRange(startRow, endRow);
        return rows;
      } catch (error) {
        logError(`Erro ao carregar intervalo de linhas ${startRow}-${endRow}:`, error);
        if (onError) {
          onError(error instanceof Error ? error : new Error(String(error)));
        }
        return [];
      }
    },
    [chunkedData, onError]
  );

  // Função para salvar dados atualizados no servidor
  const persistChanges = useCallback(async () => {
    if (!needsPersistence) return;

    try {
      // Obter todas as linhas carregadas para salvar
      const allRows = await chunkedData.getRange(0, chunkedData.totalItems - 1);

      const dataToSave = {
        ...spreadsheetDataRef.current,
        rows: allRows,
      };

      const response = await fetch(`/api/workbooks/${workbookId}/sheets/${sheetId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: dataToSave.name,
          data: JSON.stringify(dataToSave),
        }),
      });

      if (!response.ok) {
        throw new Error('Erro ao salvar planilha');
      }

      setNeedsPersistence(false);
    } catch (error) {
      logError('Erro ao persistir mudanças:', error);
      if (onError) {
        onError(error instanceof Error ? error : new Error(String(error)));
      }
    }
  }, [workbookId, sheetId, chunkedData, needsPersistence, onError]);

  // Adicionar linha
  const addRow = useCallback(async () => {
    // Criar nova linha vazia
    const newRow = Array(spreadsheetDataRef.current.headers.length).fill('');

    // Incrementar contador de itens
    chunkedData.setTotal(chunkedData.totalItems + 1);

    // Adicionar ao final
    await chunkedData.updateItem(chunkedData.totalItems - 1, newRow);

    // Marcar para atualização e persistência
    setUpdateCounter(prev => prev + 1);
    setNeedsPersistence(true);

    // Atualizar dados
    const updatedData = {
      ...spreadsheetDataRef.current,
      rows: [...(await chunkedData.getRange(0, chunkedData.totalItems - 1))],
    };

    setSpreadsheetData(updatedData);

    // Notificar componente pai
    if (onDataChange) {
      onDataChange(updatedData);
    }
  }, [chunkedData, onDataChange]);

  // Remover linha
  const removeRow = useCallback(
    async (rowIndex: number) => {
      if (rowIndex < 0 || rowIndex >= chunkedData.totalItems) return;

      try {
        // Obter todas as linhas
        const allRows = await chunkedData.getRange(0, chunkedData.totalItems - 1);

        // Remover a linha especificada
        allRows.splice(rowIndex, 1);

        // Redefinir número total de itens
        chunkedData.setTotal(allRows.length);

        // Atualizar todas as linhas após a exclusão
        for (let i = 0; i < allRows.length; i++) {
          const row = allRows[i];
          if (row) {
            // Verificar se a linha existe
            await chunkedData.updateItem(i, row);
          }
        }

        // Atualizar dados
        const updatedData = {
          ...spreadsheetDataRef.current,
          rows: allRows,
        };

        setSpreadsheetData(updatedData);
        setNeedsPersistence(true);

        // Notificar componente pai
        if (onDataChange) {
          onDataChange(updatedData);
        }
      } catch (error) {
        logError(`Erro ao remover linha ${rowIndex}:`, error);
        if (onError) {
          onError(error instanceof Error ? error : new Error(String(error)));
        }
      }
    },
    [chunkedData, onDataChange, onError]
  );

  return {
    spreadsheetData,
    updateCell,
    loadRowRange,
    persistChanges,
    addRow,
    removeRow,
    isLoading: isLoading || chunkedData.isLoading,
    needsPersistence,
    totalRows: chunkedData.totalItems,
    chunkSize: chunkedData.chunkSize,
    activeChunks: chunkedData.activeChunks,
  };
}
