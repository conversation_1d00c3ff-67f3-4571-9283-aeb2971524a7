import { ReactNode } from 'react';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ANIMATIONS, BORDERS, SPACING, TYPOGRAPHY } from '@/lib/design-tokens';
import { cn } from '@/lib/utils';

/**
 * Props para o componente FeatureCard
 */
interface FeatureCardProps {
  title: string;
  description: string;
  icon: ReactNode;
  className?: string;
}

/**
 * Card para exibição de funcionalidades
 */
export function FeatureCard({ title, description, icon, className }: FeatureCardProps) {
  return (
    <Card
      className={cn(
        'overflow-hidden group',
        ANIMATIONS.transition.medium,
        BORDERS.radius.lg,
        'hover:shadow-md',
        className
      )}
    >
      <CardHeader className={cn(SPACING.padding.md, 'pb-2 flex flex-row items-start space-y-0')}>
        <div
          className={cn(
            'mr-4 rounded-full p-2',
            'bg-primary/10 text-primary',
            ANIMATIONS.transition.medium,
            'group-hover:bg-primary group-hover:text-primary-foreground'
          )}
        >
          {icon}
        </div>
        <CardTitle className={cn(TYPOGRAPHY.size.lg, TYPOGRAPHY.weight.semibold)}>
          {title}
        </CardTitle>
      </CardHeader>
      <CardContent
        className={cn(SPACING.padding.md, 'pt-0', TYPOGRAPHY.size.sm, 'text-muted-foreground')}
      >
        <p>{description}</p>
      </CardContent>
    </Card>
  );
}
