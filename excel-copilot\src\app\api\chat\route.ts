import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';

import { processAIMessage } from '../../../lib/ai/ai-adapter';
import { logger } from '../../../lib/logger';
import { canUseAdvancedAI } from '../../../lib/subscription-limits';
import { rateLimit } from '../../../lib/utils/rate-limit';
import { prisma } from '../../../server/db/client';

export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

/**
 * Nova rota de API para chat compatível com o Vercel AI SDK
 */
export async function POST(req: NextRequest) {
  try {
    // Verificar autenticação
    const session = await getServerSession();
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Não autorizado. Faça login para continuar.' },
        { status: 401 }
      );
    }

    const userId = session.user.id;

    // CORREÇÃO DE SEGURANÇA: Aplicar rate limiting
    const rateLimitResult = await rateLimit(userId, 60); // 60 requests por minuto
    if (!rateLimitResult.success) {
      return NextResponse.json(
        {
          error: 'Muitas requisições. Tente novamente mais tarde.',
          retryAfter: Math.ceil((rateLimitResult.reset - Date.now()) / 1000),
        },
        { status: 429 }
      );
    }

    // Extrair mensagens do corpo da requisição
    const { messages, workbookId } = await req.json();

    if (!Array.isArray(messages) || messages.length === 0) {
      return NextResponse.json({ error: 'Formato de mensagem inválido.' }, { status: 400 });
    }

    // CORREÇÃO DE SEGURANÇA: Verificar se pode usar comandos avançados de IA
    const lastMessage = messages[messages.length - 1];
    if (lastMessage?.content) {
      const aiPermissionCheck = await canUseAdvancedAI(userId, lastMessage.content);
      if (!aiPermissionCheck.allowed) {
        return NextResponse.json(
          {
            error: aiPermissionCheck.message || 'Comando não permitido para seu plano atual.',
            upgradeRequired: true,
          },
          { status: 403 }
        );
      }
    }

    // Buscar dados da planilha se um ID foi fornecido
    let sheetData: string | null = null;

    if (workbookId) {
      // Buscar workbook que pertence ao usuário ou está compartilhado com ele
      const workbook = await prisma.workbook.findFirst({
        where: {
          id: workbookId,
          OR: [
            { userId },
            {
              shares: {
                some: {
                  sharedWithUserId: userId,
                },
              },
            },
          ],
        },
        include: {
          sheets: {
            take: 1, // Por simplicidade, usar apenas a primeira planilha
          },
        },
      });

      // Se temos um workbook e ele tem sheets, pegar os dados
      if (workbook && workbook.sheets && workbook.sheets.length > 0) {
        sheetData = workbook.sheets[0]?.data || null;
      }
    }

    // Opções para o processamento da mensagem
    const aiOptions = {
      workbookId: workbookId || '',
      userId,
      context: {
        sheetData,
      },
      temperature: 0.7,
    };

    // Registrar uso da API
    await prisma.apiUsage.create({
      data: {
        userId,
        count: 1,
        endpoint: 'chat',
        billable: true,
      },
    });

    // Processar a mensagem com o adaptador AI
    return processAIMessage(messages, aiOptions);
  } catch (error) {
    logger.error('[CHAT_API_ERROR]', error);
    return NextResponse.json(
      {
        error: 'Ocorreu um erro ao processar sua mensagem. Por favor, tente novamente.',
        details: process.env.NODE_ENV === 'development' ? String(error) : undefined,
      },
      { status: 500 }
    );
  }
}
