export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { z } from 'zod';

import { prisma } from '@/server/db/client';

// Interface para tipagem do usuário da sessão
interface SessionUser {
  id: string;
  name?: string;
  email?: string;
}

// Esquema de validação para salvar planilha
const saveWorkbookSchema = z.object({
  workbookId: z.string().optional(),
  name: z.string().min(1, 'Nome é obrigatório'),
  description: z.string().optional(),
  sheets: z
    .array(
      z.object({
        id: z.string().optional(),
        name: z.string().min(1, 'Nome da planilha é obrigatório'),
        data: z.any(),
      })
    )
    .min(1, 'Pelo menos uma planilha é necessária'),
});

/**
 * Rota de API para salvar planilhas
 */
export async function POST(req: NextRequest) {
  try {
    // Obter a sessão para identificar o usuário
    const session = await getServerSession();

    // Extrair dados do corpo da requisição
    const body = await req.json();

    // Validar dados
    const result = saveWorkbookSchema.safeParse(body);
    if (!result.success) {
      return NextResponse.json(
        { error: 'Dados inválidos', details: result.error.format() },
        { status: 400 }
      );
    }

    const { workbookId, name, description, sheets } = result.data;

    // Permitir salvar sem login, mas com um ID temporário
    // Em produção, isso seria substituído por autenticação obrigatória
    const userId = session?.user ? (session.user as SessionUser).id : 'guest';

    // Determinar se é uma atualização ou criação
    if (workbookId) {
      // Atualizar workbook existente
      const existingWorkbook = await prisma.workbook.findUnique({
        where: {
          id: workbookId,
          userId,
        },
        include: { sheets: true },
      });

      if (!existingWorkbook) {
        return NextResponse.json(
          { error: 'Planilha não encontrada ou sem permissão' },
          { status: 404 }
        );
      }

      // Atualizar workbook
      const updatedWorkbook = await prisma.workbook.update({
        where: { id: workbookId },
        data: {
          name,
          description: description || null,
          updatedAt: new Date(),
        },
      });

      // Atualizar planilhas
      for (const sheet of sheets) {
        const sheetData = JSON.stringify(sheet.data);

        if (sheet.id) {
          // Atualizar planilha existente
          await prisma.sheet.update({
            where: { id: sheet.id },
            data: {
              name: sheet.name,
              data: sheetData,
              updatedAt: new Date(),
            },
          });
        } else {
          // Criar nova planilha
          await prisma.sheet.create({
            data: {
              name: sheet.name,
              data: sheetData,
              workbookId: updatedWorkbook.id,
            },
          });
        }
      }

      return NextResponse.json({
        success: true,
        workbookId: updatedWorkbook.id,
        message: 'Planilha atualizada com sucesso',
      });
    } else {
      // Criar novo workbook (implementação simples sem função atômica)
      const newWorkbook = await prisma.workbook.create({
        data: {
          name,
          description: description || null,
          userId,
          isPublic: false,
          sheets: {
            create: sheets.map(sheet => ({
              name: sheet.name,
              data: JSON.stringify(sheet.data),
            })),
          },
        },
      });

      return NextResponse.json({
        success: true,
        workbookId: newWorkbook.id,
        message: 'Planilha criada com sucesso',
      });
    }
  } catch (error) {
    console.error('Erro ao salvar planilha:', error);

    const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}
