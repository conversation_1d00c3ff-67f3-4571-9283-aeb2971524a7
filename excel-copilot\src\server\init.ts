/**
 * Arquivo de inicialização do servidor Excel Copilot
 * Executado automaticamente no startup do Next.js
 */

import { PrismaClient } from '@prisma/client';

import { aiProvider } from '@/lib/ai/provider';
import { logger } from '@/lib/logger';

// Classe de inicialização do servidor
export class ServerInitializer {
  private prisma: PrismaClient;
  private aiProviderInstance = aiProvider;
  private static instance: ServerInitializer;

  private constructor() {
    logger.info('🚀 Inicializando servidor Excel Copilot...');
    this.prisma = new PrismaClient();
  }

  // Padrão Singleton para garantir uma única instância
  public static getInstance(): ServerInitializer {
    if (!ServerInitializer.instance) {
      ServerInitializer.instance = new ServerInitializer();
    }
    return ServerInitializer.instance;
  }

  // Método principal de inicialização
  public async initialize(): Promise<boolean> {
    try {
      // Verificar conexão com o banco de dados
      await this.connectDatabase();

      // Inicializar provedor de IA
      await this.initializeAIProvider();

      // Inicializar outros serviços necessários
      await this.initializeServices();

      // Usar initLogger para silenciar em produção
      const { initLogger } = await import('@/lib/logger');
      initLogger.info('✅ Servidor Excel Copilot inicializado com sucesso!');
      return true;
    } catch (error) {
      logger.error('❌ Erro na inicialização do servidor:', error);
      return false;
    }
  }

  // Conectar ao banco de dados
  private async connectDatabase(): Promise<void> {
    try {
      logger.info('📊 Conectando ao banco de dados...');
      await this.prisma.$connect();

      // Verificar se o banco está acessível realizando uma consulta simples
      await this.prisma.user.findFirst({
        select: { id: true },
        take: 1,
      });

      logger.info('✅ Conexão com o banco de dados estabelecida.');
    } catch (error) {
      logger.error('❌ Erro ao conectar ao banco de dados:', error);
      throw new Error(`Falha na conexão com o banco de dados: ${error}`);
    }
  }

  // Configurar o provedor de IA
  private async initializeAIProvider(): Promise<void> {
    try {
      logger.info('🤖 Inicializando provedor de IA...');
      // O aiProvider já é inicializado como singleton na importação
      // Verificamos apenas se está disponível

      // Se estiver em modo mock, avisar no log
      if (process.env.AI_USE_MOCK === 'true') {
        logger.warn('⚠️ Modo mock de IA ativado. A IA terá funcionalidade limitada.');
      } else {
        // Verificar se o provedor está funcionando com uma chamada simples
        // Em um cenário real, faríamos uma validação da API aqui
        logger.info('✅ Provedor de IA inicializado.');
      }
    } catch (error) {
      logger.error('❌ Erro ao inicializar provedor de IA:', error);
      throw new Error(`Falha na inicialização do provedor de IA: ${error}`);
    }
  }

  // Inicializar outros serviços necessários
  private async initializeServices(): Promise<void> {
    try {
      logger.info('⚙️ Inicializando serviços adicionais...');

      // Aqui seriam inicializados outros serviços necessários
      // Por exemplo: serviços de cache, filas, etc.

      logger.info('✅ Serviços adicionais inicializados.');
    } catch (error) {
      logger.error('❌ Erro ao inicializar serviços adicionais:', error);
      throw new Error(`Falha na inicialização de serviços: ${error}`);
    }
  }

  // Getter para o cliente Prisma
  public getPrisma(): PrismaClient {
    return this.prisma;
  }

  // Getter para o provedor de IA
  public getAIProvider() {
    return this.aiProviderInstance;
  }

  // Método para encerrar conexões
  public async shutdown(): Promise<void> {
    logger.info('🛑 Encerrando conexões...');
    await this.prisma.$disconnect();
    logger.info('✅ Conexões encerradas.');
  }
}

// Exportar uma instância única do inicializador
const serverInitializer = ServerInitializer.getInstance();

// Exportar função de inicialização para uso no startup
export async function initializeServer(): Promise<boolean> {
  return await serverInitializer.initialize();
}

// Exportar o inicializador para uso em outros módulos
export default serverInitializer;
