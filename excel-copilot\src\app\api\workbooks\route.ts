export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

import { NextRequest } from 'next/server';

import { cacheManager, CACHE_KEYS, CACHE_TTL } from '@/lib/cache/cache-manager';
import { logger } from '@/lib/logger';
import { authMiddleware } from '@/middleware/auth';
import { withMiddleware } from '@/middleware/core';
import { metricsMiddleware, withMetrics } from '@/middleware/metrics';
import { apiRateLimiter } from '@/middleware/rate-limit';
import {
  createWorkbookSchema,
  updateWorkbookSchema,
  workbookIdSchema,
  workbookFilterSchema,
} from '@/schemas/workbook';
import { recordApiUsage } from '@/server/api-usage';
import { invalidateCache } from '@/server/db/query-cache';
import { WorkbookService } from '@/server/services/workbook-service';
import { ApiResponse } from '@/utils/api-response';

/**
 * GET /api/workbooks - Lista workbooks do usuário
 */
export const GET = withMiddleware(
  [apiRateLimiter, metricsMiddleware, authMiddleware],
  async (req: NextRequest, ctx) => {
    try {
      logger.info('🚀 API /workbooks: Iniciando processamento', { userId: ctx.userId });

      // Extrair e validar parâmetros de consulta
      const searchParams = req.nextUrl.searchParams;
      const filterInput = {
        isPublic: searchParams.has('isPublic')
          ? searchParams.get('isPublic') === 'true'
          : undefined,
        search: searchParams.get('search') || undefined,
        limit: searchParams.has('limit')
          ? parseInt(searchParams.get('limit') as string, 10)
          : undefined,
        page: searchParams.has('page')
          ? parseInt(searchParams.get('page') as string, 10)
          : undefined,
      };

      logger.info('📋 API /workbooks: Filtros extraídos', filterInput);

      // Validar filtros
      const filterResult = workbookFilterSchema.safeParse(filterInput);

      if (!filterResult.success) {
        logger.warn('❌ API /workbooks: Filtros inválidos', filterResult.error);
        return withMetrics(
          ApiResponse.badRequest('Parâmetros de filtro inválidos', filterResult.error.format()),
          ctx
        );
      }

      logger.info('✅ API /workbooks: Filtros validados', filterResult.data);

      // Gerar chave de cache baseada nos filtros
      const cacheKey = CACHE_KEYS.WORKBOOK_LIST(ctx.userId);

      // Tentar buscar do cache primeiro (apenas para filtros simples)
      const isSimpleQuery = !filterResult.data.search && !filterResult.data.isPublic;
      let workbooks;

      if (isSimpleQuery) {
        workbooks = await cacheManager.getOrSet(
          cacheKey,
          () => WorkbookService.getUserWorkbooks(ctx.userId, filterResult.data),
          CACHE_TTL.WORKBOOK_METADATA
        );
      } else {
        // Para consultas complexas, buscar diretamente
        logger.info('🔍 API /workbooks: Buscando workbooks (query complexa)...');
        workbooks = await WorkbookService.getUserWorkbooks(ctx.userId, filterResult.data);
      }
      logger.info('📊 API /workbooks: Workbooks encontrados', {
        count: workbooks?.workbooks?.length || 0,
        data: workbooks,
      });

      // Registrar uso da API
      await recordApiUsage({
        userId: ctx.userId,
        endpoint: 'workbooks/list',
        count: 1,
      });

      logger.info('🎯 API /workbooks: Criando resposta de sucesso');
      const response = ApiResponse.success(workbooks);
      logger.info('📤 API /workbooks: Resposta criada', {
        status: response.status,
        hasBody: !!response.body,
      });

      return withMetrics(response, ctx);
    } catch (error) {
      logger.error('❌ API /workbooks: Erro no processamento', error);
      if (error instanceof Error) {
        return withMetrics(ApiResponse.error(error.message), ctx);
      }
      return withMetrics(ApiResponse.error('Erro ao buscar workbooks'), ctx);
    }
  }
);

/**
 * POST /api/workbooks - Cria um novo workbook
 */
export const POST = withMiddleware(
  [apiRateLimiter, metricsMiddleware, authMiddleware],
  async (req: NextRequest, ctx) => {
    try {
      // Validar corpo da requisição
      const body = await req.json();
      const result = createWorkbookSchema.safeParse(body);

      if (!result.success) {
        return withMetrics(
          ApiResponse.badRequest('Dados inválidos para criar workbook', result.error.format()),
          ctx
        );
      }

      // Criar workbook
      const workbook = await WorkbookService.createWorkbook(result.data, ctx.userId);

      // Invalidar cache relacionado a workbooks do usuário
      await cacheManager.invalidateUserCache(ctx.userId);
      invalidateCache(`user-workbooks:${ctx.userId}`);

      // Registrar uso da API
      await recordApiUsage({
        userId: ctx.userId,
        endpoint: 'workbooks/create',
        count: 1,
        workbookId: workbook.id,
      });

      return withMetrics(ApiResponse.success(workbook, undefined, 201), ctx);
    } catch (error) {
      if (error instanceof Error) {
        return withMetrics(ApiResponse.error(error.message), ctx);
      }
      return withMetrics(ApiResponse.error('Erro ao criar workbook'), ctx);
    }
  }
);

/**
 * PATCH /api/workbooks - Atualiza um workbook existente
 */
export const PATCH = withMiddleware(
  [apiRateLimiter, metricsMiddleware, authMiddleware],
  async (req: NextRequest, ctx) => {
    try {
      // Validar corpo da requisição
      const body = await req.json();
      const result = updateWorkbookSchema.safeParse(body);

      if (!result.success) {
        return withMetrics(
          ApiResponse.badRequest('Dados inválidos para atualizar workbook', result.error.format()),
          ctx
        );
      }

      // Atualizar workbook
      const workbook = await WorkbookService.updateWorkbook(result.data, ctx.userId);

      // Invalidar cache relacionado a workbooks
      invalidateCache(`user-workbooks:${ctx.userId}`);

      // Registrar uso da API
      await recordApiUsage({
        userId: ctx.userId,
        endpoint: 'workbooks/update',
        count: 1,
        workbookId: result.data.id,
      });

      return withMetrics(ApiResponse.success(workbook), ctx);
    } catch (error) {
      if (error instanceof Error) {
        return withMetrics(ApiResponse.error(error.message), ctx);
      }
      return withMetrics(ApiResponse.error('Erro ao atualizar workbook'), ctx);
    }
  }
);

/**
 * DELETE /api/workbooks - Exclui um workbook
 */
export const DELETE = withMiddleware(
  [apiRateLimiter, metricsMiddleware, authMiddleware],
  async (req: NextRequest, ctx) => {
    try {
      // Extrair e validar ID
      const searchParams = req.nextUrl.searchParams;
      const id = searchParams.get('id');

      if (!id) {
        return withMetrics(ApiResponse.badRequest('ID do workbook é obrigatório'), ctx);
      }

      const result = workbookIdSchema.safeParse({ id });

      if (!result.success) {
        return withMetrics(ApiResponse.badRequest('ID de workbook inválido'), ctx);
      }

      // Excluir workbook
      await WorkbookService.deleteWorkbook(id, ctx.userId);

      // Invalidar cache relacionado a workbooks
      invalidateCache(`user-workbooks:${ctx.userId}`);

      // Registrar uso da API
      await recordApiUsage({
        userId: ctx.userId,
        endpoint: 'workbooks/delete',
        count: 1,
        workbookId: id,
      });

      return withMetrics(
        ApiResponse.success({ success: true, message: 'Workbook excluído com sucesso' }),
        ctx
      );
    } catch (error) {
      if (error instanceof Error) {
        return withMetrics(ApiResponse.error(error.message), ctx);
      }
      return withMetrics(ApiResponse.error('Erro ao excluir workbook'), ctx);
    }
  }
);
