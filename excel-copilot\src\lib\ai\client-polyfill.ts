/**
 * Polyfill para prevenir completamente a execução de código de IA no cliente
 * Este módulo substitui qualquer tentativa de usar APIs do Google no navegador
 */

import {
  isBlockedAIModule,
  isBlockedAPIDomain,
  AI_BLOCK_CLASSES,
  BLOCKED_AI_MODULES,
} from './constants';

// Função para criar módulo de bloqueio
function createBlockModule() {
  return {
    GoogleGenerativeAI: AI_BLOCK_CLASSES.GoogleGenerativeAI,
    VertexAI: AI_BLOCK_CLASSES.VertexAI,
    GenerativeModel: AI_BLOCK_CLASSES.GenerativeModel,
    default: {
      GoogleGenerativeAI: AI_BLOCK_CLASSES.GoogleGenerativeAI,
      VertexAI: AI_BLOCK_CLASSES.VertexAI,
      GenerativeModel: AI_BLOCK_CLASSES.GenerativeModel,
    },
  };
}

// Verificar se estamos no cliente
if (typeof window !== 'undefined') {
  console.log('[AI Client Polyfill] Inicializando proteções contra IA no cliente');

  // Interceptar tentativas de usar classes de IA usando classes de bloqueio
  (window as any).GoogleGenerativeAI = AI_BLOCK_CLASSES.GoogleGenerativeAI;
  (window as any).VertexAI = AI_BLOCK_CLASSES.VertexAI;
  (window as any).GenerativeModel = AI_BLOCK_CLASSES.GenerativeModel;

  // Interceptar módulos do Google
  const originalDefine = (window as any).define;
  if (originalDefine) {
    (window as any).define = function (
      name: string,
      deps: string[],
      factory: (...args: any[]) => any
    ) {
      if (name && isBlockedAIModule(name)) {
        console.warn(`[AI Client Polyfill] Bloqueado módulo ${name} no cliente`);
        return originalDefine(name, deps, () => createBlockModule());
      }
      return originalDefine.call(this, name, deps, factory);
    };
  }

  // Interceptar tentativas de definir módulos Google diretamente no window
  BLOCKED_AI_MODULES.forEach(moduleName => {
    try {
      Object.defineProperty(window, moduleName, {
        get: function () {
          console.warn(
            `[AI Client Polyfill] Tentativa de acessar ${moduleName} no cliente foi bloqueada`
          );
          return createBlockModule();
        },
        set: function (_value) {
          console.warn(
            `[AI Client Polyfill] Tentativa de definir ${moduleName} no cliente foi bloqueada`
          );
        },
        configurable: true,
        enumerable: false,
      });
    } catch {
      // Ignorar erros ao definir propriedades
    }
  });

  // Interceptar require se disponível
  const originalRequire = (window as any).require;
  if (originalRequire) {
    (window as any).require = function (moduleId: string, ...args: any[]) {
      if (isBlockedAIModule(moduleId)) {
        console.warn(`[AI Client Polyfill] Bloqueado require de ${moduleId} no cliente`);
        return createBlockModule();
      }
      return originalRequire.call(this, moduleId, ...args);
    };
  }

  // Interceptar import dinâmico
  const originalImport = (window as any).import;
  if (originalImport) {
    (window as any).import = function (moduleSpecifier: string, ...args: any[]) {
      if (isBlockedAIModule(moduleSpecifier)) {
        console.warn(
          `[AI Client Polyfill] Bloqueado import dinâmico de ${moduleSpecifier} no cliente`
        );
        return Promise.resolve(createBlockModule());
      }
      return originalImport.call(this, moduleSpecifier, ...args);
    };
  }

  // Interceptar fetch para APIs do Google (CONSOLIDADO AQUI)
  const originalFetch = window.fetch;
  window.fetch = function (input: RequestInfo | URL, init?: RequestInit) {
    const url = typeof input === 'string' ? input : input.toString();

    if (isBlockedAPIDomain(url)) {
      console.warn(`[AI Client Polyfill] Bloqueada chamada para API do Google: ${url}`);
      return Promise.reject(
        new Error('Chamadas diretas para APIs do Google não são permitidas no cliente')
      );
    }

    return originalFetch.call(this, input, init);
  };

  // Interceptar XMLHttpRequest (usando função centralizada)
  const OriginalXMLHttpRequest = window.XMLHttpRequest;
  window.XMLHttpRequest = class extends OriginalXMLHttpRequest {
    open(
      method: string,
      url: string | URL,
      async?: boolean,
      user?: string | null,
      password?: string | null
    ) {
      const urlStr = typeof url === 'string' ? url : url.toString();

      if (isBlockedAPIDomain(urlStr)) {
        console.warn(`[AI Client Polyfill] Bloqueada requisição XMLHttpRequest para: ${urlStr}`);
        throw new Error('Requisições diretas para APIs do Google não são permitidas no cliente');
      }

      return super.open(method, url, async ?? true, user, password);
    }
  };

  // Interceptar WebSocket se tentar conectar a APIs do Google
  const OriginalWebSocket = window.WebSocket;
  window.WebSocket = class extends OriginalWebSocket {
    constructor(url: string | URL, protocols?: string | string[]) {
      const urlStr = typeof url === 'string' ? url : url.toString();

      if (isBlockedAPIDomain(urlStr)) {
        console.warn(`[AI Client Polyfill] Bloqueada conexão WebSocket para: ${urlStr}`);
        throw new Error('Conexões WebSocket para APIs do Google não são permitidas no cliente');
      }

      super(url, protocols);
    }
  };

  // Interceptar tentativas de definir credenciais do Google
  Object.defineProperty(window, 'GOOGLE_APPLICATION_CREDENTIALS', {
    set: function (_value) {
      console.warn(
        '[AI Client Polyfill] Tentativa de definir GOOGLE_APPLICATION_CREDENTIALS no cliente foi bloqueada'
      );
    },
    get: function () {
      return undefined;
    },
  });

  // Interceptar tentativas de definir API keys
  Object.defineProperty(window, 'GOOGLE_AI_API_KEY', {
    set: function (_value) {
      console.warn(
        '[AI Client Polyfill] Tentativa de definir GOOGLE_AI_API_KEY no cliente foi bloqueada'
      );
    },
    get: function () {
      return undefined;
    },
  });

  console.log('[AI Client Polyfill] Proteções ativadas com sucesso');
}

export {}; // Tornar este arquivo um módulo
