/**
 * Testes unitários para o endpoint /api/vercel/deployments
 * Testa a funcionalidade de listagem de deployments do Vercel
 */

// Mock do VercelClient
const mockGetDeployments = jest.fn();

jest.mock('@/lib/vercel-integration', () => ({
  VercelClient: jest.fn().mockImplementation(() => ({
    getDeployments: mockGetDeployments,
  })),
}));

// Mock do logger
jest.mock('@/lib/logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
  },
}));

// Mock do ApiResponse
jest.mock('@/utils/api-response', () => ({
  ApiResponse: {
    success: jest.fn(data => ({
      json: () => Promise.resolve({ success: true, data }),
    })),
    error: jest.fn((message, code, status) => ({
      json: () =>
        Promise.resolve({
          success: false,
          error: { message, code },
          status,
        }),
    })),
  },
}));

// Importar após os mocks
import { GET } from '@/app/api/vercel/deployments/route';

describe('/api/vercel/deployments', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Mock das variáveis de ambiente
    process.env.VERCEL_API_TOKEN = 'test_token';
    process.env.VERCEL_PROJECT_ID = 'prj_test123';
    process.env.VERCEL_TEAM_ID = 'team_test123';
  });

  describe('GET', () => {
    it('deve retornar lista de deployments com sucesso', async () => {
      // Arrange
      const mockDeployments = [
        {
          uid: 'dpl_test123',
          name: 'excel-copilot',
          url: 'https://excel-copilot-git-main.vercel.app',
          state: 'READY',
          type: 'LAMBDAS',
          created: 1640995200000,
          building: false,
          ready: 1640995300000,
          target: 'production',
          creator: {
            uid: 'user_123',
            username: 'developer',
            email: '<EMAIL>',
          },
          meta: {
            githubCommitSha: 'abc123def456',
            githubCommitMessage: 'feat: add new feature',
            githubCommitAuthorName: 'Developer',
            githubCommitRef: 'main',
          },
        },
        {
          uid: 'dpl_test456',
          name: 'excel-copilot',
          url: 'https://excel-copilot-git-feature.vercel.app',
          state: 'BUILDING',
          type: 'LAMBDAS',
          created: 1640995400000,
          building: true,
          ready: null,
          target: 'preview',
          creator: {
            uid: 'user_123',
            username: 'developer',
            email: '<EMAIL>',
          },
          meta: {
            githubCommitSha: 'def456ghi789',
            githubCommitMessage: 'fix: bug correction',
            githubCommitAuthorName: 'Developer',
            githubCommitRef: 'feature-branch',
          },
        },
      ];

      mockGetDeployments.mockResolvedValue(mockDeployments);

      const request = { url: 'http://localhost:3000/api/vercel/deployments' } as any;

      // Act
      const response = await GET(request);
      const result = await response.json();

      // Assert
      expect(result.success).toBe(true);
      expect(result.data.deployments).toHaveLength(2);
      expect(result.data.deployments[0].id).toBe('dpl_test123');
      expect(result.data.deployments[0].state).toBe('READY');
      expect(result.data.deployments[1].state).toBe('BUILDING');
      expect(result.data.total).toBe(2);
    });

    it('deve aplicar filtros de estado corretamente', async () => {
      // Arrange
      const mockDeployments = [
        {
          uid: 'dpl_ready',
          name: 'excel-copilot',
          url: 'https://excel-copilot.vercel.app',
          state: 'READY',
          type: 'LAMBDAS',
          created: 1640995200000,
          building: false,
          ready: 1640995300000,
          target: 'production',
        },
      ];

      mockGetDeployments.mockResolvedValue(mockDeployments);

      const request = { url: 'http://localhost:3000/api/vercel/deployments?state=READY' } as any;

      // Act
      const response = await GET(request);
      const result = await response.json();

      // Assert
      expect(result.success).toBe(true);
      expect(result.data.deployments).toHaveLength(1);
      expect(result.data.deployments[0].state).toBe('READY');
      expect(result.data.filters.state).toBe('READY');
    });

    it('deve aplicar limite de resultados corretamente', async () => {
      // Arrange
      // Simular que o VercelClient já retorna apenas 3 deployments quando limit=3 é passado
      const mockDeployments = Array.from({ length: 3 }, (_, i) => ({
        uid: `dpl_test${i}`,
        name: 'excel-copilot',
        url: `https://excel-copilot-${i}.vercel.app`,
        state: 'READY' as const,
        type: 'LAMBDAS' as const,
        created: 1640995200000 + i * 1000,
        ready: 1640995300000 + i * 1000,
        projectId: 'prj_test123',
        target: 'production' as const,
      }));

      mockGetDeployments.mockResolvedValue(mockDeployments);

      const request = { url: 'http://localhost:3000/api/vercel/deployments?limit=3' } as any;

      // Act
      const response = await GET(request);
      const result = await response.json();

      // Assert
      expect(result.success).toBe(true);
      expect(result.data.deployments).toHaveLength(3);
      expect(result.data.filters.limit).toBe(3);
    });

    it('deve retornar erro quando API do Vercel falhar', async () => {
      // Arrange
      mockGetDeployments.mockRejectedValue(new Error('API Error'));

      const request = { url: 'http://localhost:3000/api/vercel/deployments' } as any;

      // Act
      const response = await GET(request);
      const result = await response.json();

      // Assert
      expect(result.success).toBe(false);
      expect(result.error.code).toBe('VERCEL_API_ERROR');
      expect(result.error.message).toContain('Erro ao conectar com Vercel');
    });

    it('deve retornar lista vazia quando não há deployments', async () => {
      // Arrange
      mockGetDeployments.mockResolvedValue([]);

      const request = { url: 'http://localhost:3000/api/vercel/deployments' } as any;

      // Act
      const response = await GET(request);
      const result = await response.json();

      // Assert
      expect(result.success).toBe(true);
      expect(result.data.deployments).toHaveLength(0);
      expect(result.data.total).toBe(0);
    });
  });
});
