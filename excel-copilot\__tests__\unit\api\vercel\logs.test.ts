/**
 * Testes unitários para o endpoint /api/vercel/logs
 * Testa a funcionalidade de obtenção de logs do Vercel
 */

// Mock do VercelMonitoringService
const mockGetFilteredLogs = jest.fn();

jest.mock('@/lib/vercel-integration', () => ({
  VercelMonitoringService: jest.fn().mockImplementation(() => ({
    getFilteredLogs: mockGetFilteredLogs,
  })),
}));

// Mock do logger
jest.mock('@/lib/logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
  },
}));

// Mock do ApiResponse
jest.mock('@/utils/api-response', () => ({
  ApiResponse: {
    success: jest.fn(data => ({
      json: () => Promise.resolve({ success: true, data }),
    })),
    error: jest.fn((message, code, status) => ({
      json: () =>
        Promise.resolve({
          success: false,
          error: { message, code },
          status,
        }),
    })),
  },
}));

// Importar após os mocks
import { GET } from '@/app/api/vercel/logs/route';

describe('/api/vercel/logs', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Mock das variáveis de ambiente
    process.env.VERCEL_API_TOKEN = 'test_token';
    process.env.VERCEL_PROJECT_ID = 'prj_test123';
    process.env.VERCEL_TEAM_ID = 'team_test123';
  });

  describe('GET', () => {
    it('deve retornar logs com sucesso', async () => {
      // Arrange
      const mockLogs = [
        {
          id: 'log_123',
          timestamp: 1640995200000,
          message: 'Request processed successfully',
          level: 'info',
          source: 'lambda',
          requestId: 'req_abc123',
          deploymentId: 'dpl_test123',
        },
        {
          id: 'log_456',
          timestamp: 1640995300000,
          message: 'Database connection established',
          level: 'info',
          source: 'lambda',
          requestId: 'req_def456',
          deploymentId: 'dpl_test123',
        },
      ];

      mockGetFilteredLogs.mockResolvedValue(mockLogs);

      const request = { url: 'http://localhost:3000/api/vercel/logs' } as any;

      // Act
      const response = await GET(request);
      const result = await response.json();

      // Assert
      expect(result.success).toBe(true);
      expect(result.data.logs).toHaveLength(2);
      expect(result.data.logs[0].message).toBe('Request processed successfully');
      expect(result.data.total).toBe(2);
    });

    it('deve retornar erro quando serviço falhar', async () => {
      // Arrange
      mockGetFilteredLogs.mockRejectedValue(new Error('Service Error'));

      const request = { url: 'http://localhost:3000/api/vercel/logs' } as any;

      // Act
      const response = await GET(request);
      const result = await response.json();

      // Assert
      expect(result.success).toBe(false);
      expect(result.error.code).toBe('VERCEL_API_ERROR');
      expect(result.error.message).toContain('Erro ao conectar com Vercel');
    });

    it('deve retornar lista vazia quando não há logs', async () => {
      // Arrange
      mockGetFilteredLogs.mockResolvedValue([]);

      const request = { url: 'http://localhost:3000/api/vercel/logs' } as any;

      // Act
      const response = await GET(request);
      const result = await response.json();

      // Assert
      expect(result.success).toBe(true);
      expect(result.data.logs).toHaveLength(0);
      expect(result.data.total).toBe(0);
    });
  });
});
