// Tipos para ambientes 3D usados em múltiplos componentes
export type Environment3DType =
  | 'sunset'
  | 'dawn'
  | 'night'
  | 'warehouse'
  | 'forest'
  | 'apartment'
  | 'studio'
  | 'city'
  | 'park'
  | 'lobby';

// Definição de ambientes com seus rótulos em português
export const availableEnvironments = [
  { value: 'city', label: 'Cidade' },
  { value: 'studio', label: 'Estúdio' },
  { value: 'night', label: 'Noite' },
  { value: 'sunset', label: 'Pôr do Sol' },
  { value: 'warehouse', label: 'Galp<PERSON>' },
  { value: 'forest', label: 'Floresta' },
  { value: 'apartment', label: 'Apartamento' },
  { value: 'park', label: 'Parque' },
  { value: 'lobby', label: 'Saguão' },
] as const;
