import {
  GeminiService,
  GeminiErrorType,
  GeminiServiceError,
  SendMessageOptions,
} from '@/server/ai/gemini-service';

/**
 * Uma implementação simplificada do StreamingTextResponse do Vercel AI SDK
 */
export class StreamingTextResponse extends Response {
  constructor(stream: ReadableStream<Uint8Array>, init?: ResponseInit) {
    const headers = {
      'Content-Type': 'text/plain; charset=utf-8',
      'X-Content-Type-Options': 'nosniff',
      ...init?.headers,
    };

    super(stream, {
      ...init,
      status: init?.status ?? 200,
      headers,
    });
  }
}

// Interface para o adaptador IA que unifica as implementações
export interface AIAdapterOptions {
  workbookId?: string;
  userId?: string;
  temperature?: number;
  timeout?: number;
  context?: {
    sheetData?: any;
    activeSheet?: string;
    selection?: string;
    headers?: string[];
    recentOperations?: string[];
  };
  systemPrompt?: string;
}

/**
 * Erro padronizado do adaptador de IA
 */
export class AIAdapterError extends Error {
  type: string;
  statusCode: number;
  recoverable: boolean;

  constructor(
    message: string,
    type: string,
    statusCode: number = 500,
    recoverable: boolean = false
  ) {
    super(message);
    this.name = 'AIAdapterError';
    this.type = type;
    this.statusCode = statusCode;
    this.recoverable = recoverable;
  }

  /**
   * Converte um GeminiServiceError para AIAdapterError
   */
  static fromGeminiError(error: GeminiServiceError): AIAdapterError {
    const errorMap: Record<GeminiErrorType, { type: string; statusCode: number }> = {
      [GeminiErrorType.TIMEOUT]: { type: 'timeout', statusCode: 504 },
      [GeminiErrorType.API_UNAVAILABLE]: { type: 'service_unavailable', statusCode: 503 },
      [GeminiErrorType.INVALID_REQUEST]: { type: 'bad_request', statusCode: 400 },
      [GeminiErrorType.CONTENT_FILTERED]: { type: 'content_filtered', statusCode: 422 },
      [GeminiErrorType.RATE_LIMITED]: { type: 'rate_limited', statusCode: 429 },
      [GeminiErrorType.INVALID_RESPONSE_FORMAT]: { type: 'bad_gateway', statusCode: 502 },
      [GeminiErrorType.CONTEXT_LIMIT_EXCEEDED]: { type: 'payload_too_large', statusCode: 413 },
      [GeminiErrorType.TOKEN_LIMIT_EXCEEDED]: { type: 'payload_too_large', statusCode: 413 },
      [GeminiErrorType.RETRY_FAILED]: { type: 'service_unavailable', statusCode: 503 },
      [GeminiErrorType.UNKNOWN]: { type: 'internal_error', statusCode: 500 },
    };

    const { type, statusCode } = errorMap[error.type] || errorMap[GeminiErrorType.UNKNOWN];
    return new AIAdapterError(error.message, type, statusCode, error.recoverable);
  }
}

/**
 * Converte um histórico de mensagens do formato Vercel AI para o formato GeminiService
 */
function convertVercelHistoryToGemini(messages: Array<{ role: string; content: string }>) {
  return messages.map(msg => ({
    role: msg.role === 'user' ? 'user' : 'assistant',
    content: msg.content,
  }));
}

/**
 * Implementação do AIStream inspirada no Vercel AI SDK, mas adaptada para nossas necessidades
 */
function AIStream(
  stream: ReadableStream<Uint8Array>,
  options?: {
    onFinal?: () => void;
    onCompletion?: (completion: string) => string;
  }
): ReadableStream<Uint8Array> {
  const encoder = new TextEncoder();
  const decoder = new TextDecoder();
  let fullText = '';

  return new ReadableStream({
    async start(controller) {
      const reader = stream.getReader();

      try {
        let done = false;
        while (!done) {
          const result = await reader.read();
          done = result.done;

          if (result.value) {
            const text = decoder.decode(result.value);
            fullText += text;
            controller.enqueue(result.value);
          }
        }

        // Processamento final da resposta completa
        if (options?.onCompletion) {
          try {
            const finalCompletion = options.onCompletion(fullText);
            if (finalCompletion !== fullText) {
              controller.enqueue(encoder.encode('\n' + finalCompletion));
            }
          } catch (err) {
            console.error('Erro no processamento final:', err);
          }
        }

        if (options?.onFinal) {
          options.onFinal();
        }

        controller.close();
      } catch (error) {
        controller.error(error);
      }
    },
  });
}

/**
 * Processa uma mensagem usando o GeminiService e retorna uma resposta compatível com o Vercel AI SDK
 */
export async function processAIMessage(
  messages: Array<{ role: string; content: string }>,
  options: AIAdapterOptions = {}
): Promise<Response> {
  try {
    // Obter a última mensagem do usuário
    const lastUserMessage = messages.filter(m => m.role === 'user').pop();
    if (!lastUserMessage) {
      throw new AIAdapterError('Nenhuma mensagem do usuário encontrada', 'bad_request', 400);
    }

    // Preparar histórico excluindo a última mensagem
    const historyMessages = messages.slice(0, -1);
    const geminiHistory = convertVercelHistoryToGemini(historyMessages);

    // Configurar opções para o GeminiService
    const geminiOptions: Record<string, unknown> = {};

    // Adicionar opções apenas se estiverem definidas
    if (options.temperature !== undefined) {
      geminiOptions.temperature = options.temperature;
    }
    if (options.timeout !== undefined) {
      geminiOptions.timeout = options.timeout;
    }
    if (options.userId !== undefined) {
      geminiOptions.userId = options.userId;
    }
    if (options.systemPrompt !== undefined) {
      geminiOptions.systemPrompt = options.systemPrompt;
    }

    // Configurar o contexto do Excel se existir
    if (options.context) {
      const excelContext: Record<string, unknown> = {};
      if (options.context.activeSheet !== undefined) {
        excelContext.activeSheet = options.context.activeSheet;
      }
      if (options.context.selection !== undefined) {
        excelContext.selection = options.context.selection;
      }
      if (options.context.headers !== undefined) {
        excelContext.headers = options.context.headers;
      }
      if (options.context.recentOperations !== undefined) {
        excelContext.recentOperations = options.context.recentOperations;
      }

      // Apenas adicionar o contexto se pelo menos uma propriedade for definida
      if (Object.keys(excelContext).length > 0) {
        geminiOptions.excelContext = excelContext;
      }
    }

    // Iniciar stream de resposta
    const geminiService = GeminiService.getInstance();

    // Usando o método de streaming para compatibilidade com Vercel AI SDK
    const stream = await geminiService.streamMessage(
      lastUserMessage.content,
      geminiHistory,
      geminiOptions as SendMessageOptions
    );

    // Converter stream do Gemini para formato Vercel AI
    const aiStream = AIStream(stream, {
      onFinal: () => {
        // Pode fazer logging ou outras operações quando o stream terminar
      },
      onCompletion: (completion: string) => {
        // Analisar a resposta final para extrair metadados
        try {
          // Verificar se a resposta contém JSON de operações do Excel
          if (completion.includes('"operations":')) {
            const match = completion.match(/\{[\s\S]*"operations"[\s\S]*\}/);
            if (match) {
              const operationsData = JSON.parse(match[0]);
              // Adicionar metadados ao final do stream
              return JSON.stringify({
                operationsExecuted: true,
                operationCount: operationsData.operations?.length || 0,
                dataUpdated: true,
                completion,
              });
            }
          }
        } catch (e) {
          // Ignorar erros de parsing e continuar
          console.error('Erro ao analisar resposta:', e);
        }

        return completion;
      },
    });

    // Retornar resposta de streaming
    return new StreamingTextResponse(aiStream);
  } catch (error) {
    console.error('Erro no adaptador AI:', error);

    // Converter erro para formato padronizado
    if (error instanceof GeminiServiceError) {
      const adaptedError = AIAdapterError.fromGeminiError(error);
      return new Response(
        JSON.stringify({
          error: adaptedError.message,
          type: adaptedError.type,
        }),
        { status: adaptedError.statusCode }
      );
    }

    // Erro genérico
    return new Response(
      JSON.stringify({
        error: error instanceof Error ? error.message : 'Erro desconhecido',
        type: 'internal_error',
      }),
      { status: 500 }
    );
  }
}

/**
 * Limpa o cache de AI para um usuário específico
 */
export function clearAICache(userId?: string): void {
  const geminiService = GeminiService.getInstance();
  geminiService.clearCache(userId);
}
