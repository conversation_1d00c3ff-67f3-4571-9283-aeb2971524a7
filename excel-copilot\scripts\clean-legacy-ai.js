#!/usr/bin/env node

/**
 * Script para limpar implementações de IA legadas no Excel Copilot
 *
 * Este script:
 * 1. Remove arquivos temporários com implementações antigas
 * 2. Atualiza importações para usar novas APIs
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Cores para output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
};

console.log(
  `${colors.blue}=== Iniciando limpeza de implementações de IA legadas ===${colors.reset}`
);

// Caminho base do projeto
const basePath = path.resolve(__dirname, '..');

// Arquivos legados para remover
const legacyFilesToRemove = [
  'temp/src/lib/ai/index.js',
  'temp/src/lib/ai/gemini-service.js',
  'temp/src/lib/ai/ExcelAIProcessor.js',
  'temp/src/lib/ai/prompts.js',
];

// Verifique e remova os arquivos legados
console.log(`\n${colors.yellow}Removendo arquivos legados...${colors.reset}`);
let removedCount = 0;

for (const file of legacyFilesToRemove) {
  const fullPath = path.join(basePath, file);

  if (fs.existsSync(fullPath)) {
    try {
      fs.unlinkSync(fullPath);
      console.log(`  ${colors.green}✓ Removido: ${file}${colors.reset}`);
      removedCount++;
    } catch (err) {
      console.error(`  ${colors.red}✗ Erro ao remover ${file}: ${err.message}${colors.reset}`);
    }
  } else {
    console.log(`  ${colors.yellow}⚠ Arquivo não encontrado: ${file}${colors.reset}`);
  }
}

console.log(`\n${colors.green}Removidos ${removedCount} arquivos legados.${colors.reset}`);

// Verificar importações para @google/generative-ai (biblioteca antiga)
console.log(`\n${colors.yellow}Verificando importações de bibliotecas antigas...${colors.reset}`);

try {
  const grepOutput = execSync(
    'grep -r --include="*.{ts,js,tsx,jsx}" "@google/generative-ai" src/',
    {
      cwd: basePath,
      encoding: 'utf8',
    }
  );

  if (grepOutput) {
    console.log(
      `${colors.red}Encontrados arquivos usando a biblioteca @google/generative-ai (obsoleta)${colors.reset}`
    );
    console.log(grepOutput);
    console.log(
      `${colors.yellow}Esses arquivos precisam ser atualizados manualmente para usar @google/genai.${colors.reset}`
    );
  } else {
    console.log(
      `${colors.green}Nenhuma referência à biblioteca @google/generative-ai encontrada no código fonte.${colors.reset}`
    );
  }
} catch (err) {
  // Não encontrou nenhuma ocorrência ou erro no grep
  if (err.status === 1 && !err.stderr) {
    console.log(
      `${colors.green}Nenhuma referência à biblioteca @google/generative-ai encontrada no código fonte.${colors.reset}`
    );
  } else {
    console.error(
      `${colors.red}Erro ao procurar por importações antigas: ${err.message}${colors.reset}`
    );
  }
}

// Criar o diretório docs se não existir
const docsDir = path.join(basePath, 'docs');
if (!fs.existsSync(docsDir)) {
  fs.mkdirSync(docsDir, { recursive: true });
}

console.log(`\n${colors.blue}=== Limpeza de IA legada concluída ===${colors.reset}`);
console.log(
  `${colors.magenta}Documentação de arquitetura disponível em: docs/ARQUITETURA_IA.md${colors.reset}`
);
console.log(`${colors.yellow}Próximos passos:${colors.reset}`);
console.log(`1. Verificar se os testes relacionados à IA estão passando`);
console.log(`2. Revisar integrações de IA em outros componentes`);
console.log(`3. Atualizar documentação adicional se necessário\n`);
