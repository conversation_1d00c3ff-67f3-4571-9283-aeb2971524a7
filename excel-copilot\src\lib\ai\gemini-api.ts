/**
 * Módulo de API do Gemini que é seguro para o browser
 * Este módulo é carregado dinamicamente apenas no lado do servidor
 * e usa uma implementação de mock no cliente para evitar problemas de build
 */

import { logger } from '@/lib/logger';

import { ExtendedExcelOperationType } from './gemini-service';

// Interface para resultado de análise
export interface AnalysisResult {
  insights: string[];
  suggestedOperations: string[];
  summary: string;
}

// Interface para fórmula gerada
export interface FormulaResult {
  formula: string;
  explanation: string;
  example?: string;
}

/**
 * Opções para processamento de comandos de IA
 */
export interface SendMessageOptions {
  /**
   * ID do usuário para logging e rate limiting
   */
  userId?: string;

  /**
   * Idioma preferido para resposta
   */
  language?: string;

  /**
   * Tipo de operação Excel sendo solicitada
   */
  operationType?: ExtendedExcelOperationType;

  /**
   * Timeout em milissegundos para a requisição
   */
  timeout?: number;

  /**
   * Se deve usar mock ao invés de fazer requisição real
   */
  useMock?: boolean;

  /**
   * Se deve preservar contexto histórico para conversa contínua
   */
  preserveContext?: boolean;

  /**
   * Dados adicionais específicos da operação
   */
  metadata?: Record<string, any>;

  /**
   * Temperatura para controlar a aleatoriedade da resposta (0.0-1.0)
   * Valores mais baixos = mais determinístico, valores mais altos = mais criativo
   */
  temperature?: number;

  /**
   * Ignorar cache e fazer uma nova requisição mesmo que haja resposta em cache
   */
  bypassCache?: boolean;

  /**
   * Configurações para estrutura da resposta
   */
  responseStructure?: {
    /**
     * Preferir respostas em formato JSON
     */
    preferJson?: boolean;

    /**
     * Preferir respostas mais concisas e diretas
     */
    preferConcise?: boolean;

    /**
     * Schema para validação de resposta JSON
     */
    jsonSchema?: Record<string, any>;
  };

  /**
   * Contexto específico do Excel
   */
  excelContext?: {
    /**
     * Nome da planilha ativa
     */
    activeSheet?: string;

    /**
     * Seleção atual na planilha
     */
    selection?: string;

    /**
     * Cabeçalhos disponíveis
     */
    headers?: string[];

    /**
     * Operações recentes
     */
    recentOperations?: string[];
  };

  /**
   * Prompt de sistema para direcionar o comportamento do modelo
   */
  systemPrompt?: string;
}

// Cliente da API Gemini para browser - implementação de mock
class GeminiApiClient {
  private static instance: GeminiApiClient;

  private constructor() {
    logger.info('GeminiApiClient inicializado no navegador (modo mock)');
  }

  /**
   * Obter instância singleton do cliente Gemini
   */
  public static getInstance(): GeminiApiClient {
    if (!GeminiApiClient.instance) {
      GeminiApiClient.instance = new GeminiApiClient();
    }
    return GeminiApiClient.instance;
  }

  /**
   * Enviar mensagem para o modelo de IA - versão mock para browser
   */
  async sendMessage(message: string, _options?: SendMessageOptions): Promise<string> {
    logger.info('Usando cliente mock do Gemini no navegador');

    // Simular tempo de processamento
    await new Promise(resolve => setTimeout(resolve, 500));

    return `Mock de resposta para: "${message}" (cliente navegador)`;
  }

  /**
   * Analisar dados do Excel - versão mock para browser
   */
  async analyzeExcelData(_data: any, _options?: SendMessageOptions): Promise<AnalysisResult> {
    logger.info('Usando cliente mock do Gemini para análise Excel no navegador');

    // Simular tempo de processamento
    await new Promise(resolve => setTimeout(resolve, 800));

    return {
      insights: ['Esta é uma resposta de mock para o navegador.'],
      suggestedOperations: ['Filtrar dados', 'Agrupar por categoria'],
      summary: 'Análise de dados mock para navegador.',
    };
  }

  /**
   * Gerar fórmula Excel - versão mock para browser
   */
  async generateExcelFormula(
    _description: string,
    _options?: SendMessageOptions
  ): Promise<FormulaResult> {
    logger.info('Usando cliente mock do Gemini para geração de fórmula no navegador');

    // Simular tempo de processamento
    await new Promise(resolve => setTimeout(resolve, 600));

    return {
      formula: '=SOMA(A1:A10)',
      explanation: 'Esta é uma fórmula mock para o navegador.',
      example: 'Exemplo: =SOMA(A1:A10) somará todos os valores na faixa A1:A10.',
    };
  }

  /**
   * Verificar se o serviço está saudável
   */
  async healthCheck(): Promise<boolean> {
    return true; // Mock sempre retorna saudável
  }

  /**
   * Finalizar o cliente e liberar recursos
   */
  async shutdown(): Promise<void> {
    logger.info('GeminiApiClient mock finalizado');
  }
}

// Export uma instância singleton
export const geminiApiClient = GeminiApiClient.getInstance();
