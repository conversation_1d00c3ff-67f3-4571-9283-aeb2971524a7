/**
 * Script de teste para verificar a integração Linear MCP
 * Execute com: node test-linear-integration.js
 */

// Simular variáveis de ambiente para teste
process.env.MCP_LINEAR_API_KEY = 'lin_api_test'; // Substitua pela sua chave real se quiser testar

async function testLinearIntegration() {
  console.log('🧪 Testando Linear MCP Integration...\n');

  try {
    // Importar dinamicamente o módulo
    const { LinearClient, LinearMonitoringService } = await import(
      './src/lib/linear-integration.js'
    );

    console.log('✅ Módulo Linear importado com sucesso');

    // Teste 1: Verificar se o cliente pode ser instanciado
    const client = new LinearClient();
    console.log('✅ LinearClient instanciado com sucesso');

    // Teste 2: Verificar se o serviço de monitoramento pode ser instanciado
    const service = new LinearMonitoringService();
    console.log('✅ LinearMonitoringService instanciado com sucesso');

    // Teste 3: Verificar health check (sem fazer chamada real)
    console.log('✅ Health check method disponível:', typeof client.checkHealth === 'function');

    // Teste 4: Verificar métodos principais
    const methods = [
      'getWorkspaceInfo',
      'getIssues',
      'getIssue',
      'createIssue',
      'updateIssue',
      'getTeams',
      'getProjects',
      'getWorkflowStates',
      'checkHealth',
    ];

    console.log('\n📋 Verificando métodos do LinearClient:');
    methods.forEach(method => {
      const exists = typeof client[method] === 'function';
      console.log(`${exists ? '✅' : '❌'} ${method}: ${exists ? 'OK' : 'MISSING'}`);
    });

    // Teste 5: Verificar métodos do serviço de monitoramento
    const serviceMethods = [
      'getWorkspaceSummary',
      'getExcelCopilotIssues',
      'createExcelCopilotIssue',
      'getDevelopmentMetrics',
    ];

    console.log('\n📊 Verificando métodos do LinearMonitoringService:');
    serviceMethods.forEach(method => {
      const exists = typeof service[method] === 'function';
      console.log(`${exists ? '✅' : '❌'} ${method}: ${exists ? 'OK' : 'MISSING'}`);
    });

    console.log('\n🎉 Todos os testes básicos passaram!');
    console.log('\n📝 Próximos passos:');
    console.log('1. Configure LINEAR_API_KEY no .env.local');
    console.log('2. Teste os endpoints via: curl http://localhost:3000/api/linear/status');
    console.log('3. Verifique health check via: curl http://localhost:3000/api/health');
  } catch (error) {
    console.error('❌ Erro durante o teste:', error.message);

    if (error.message.includes('Cannot resolve module')) {
      console.log('\n💡 Dica: Execute "npm run build" primeiro para compilar o TypeScript');
    }

    if (error.message.includes('LINEAR_API_KEY')) {
      console.log('\n💡 Dica: Configure LINEAR_API_KEY no .env.local para testes completos');
    }
  }
}

// Executar teste
testLinearIntegration().catch(console.error);
