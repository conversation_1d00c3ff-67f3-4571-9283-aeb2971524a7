/**
 * Logger estruturado para a aplicação
 * Substitui o uso direto de console.log com uma API mais estruturada e configurável
 */

import pino from 'pino';

// Tipos para o logger
export type LogLevel = 'trace' | 'debug' | 'info' | 'warn' | 'error' | 'fatal';

// Interface para metadados adicionais
export interface LogMetadata {
  [key: string]: any;
}

// Interface estendida para objetos de erro com metadados extras
export interface ExtendedError extends Error {
  [key: string]: any;
}

/**
 * Função utilitária para converter um valor desconhecido para Error | undefined
 * @param error Valor de erro de tipo desconhecido
 * @returns Valor convertido para Error ou undefined
 */
export function toError(error: unknown): Error | undefined {
  if (error instanceof Error) {
    return error;
  }
  if (error === null || error === undefined) {
    return undefined;
  }
  if (typeof error === 'string') {
    return new Error(error);
  }
  try {
    return new Error(JSON.stringify(error));
  } catch {
    return new Error('Unknown error');
  }
}

/**
 * Função utilitária para extrair propriedades de erro e metadados
 * @param error Valor de erro de qualquer tipo
 * @returns Objeto com erro normalizado e metadados extraídos
 */
export function extractErrorData(error: unknown): {
  normalizedError: Error | undefined;
  extractedMetadata: LogMetadata;
} {
  if (error === null || error === undefined) {
    return { normalizedError: undefined, extractedMetadata: {} };
  }

  // Caso seja um Error normal
  if (error instanceof Error) {
    // Extrair propriedades extras que não fazem parte do objeto Error padrão
    const standardErrorProps = ['name', 'message', 'stack'];
    const extractedMetadata: LogMetadata = {};

    // Capturar qualquer propriedade extra no objeto Error
    Object.keys(error).forEach(key => {
      if (!standardErrorProps.includes(key)) {
        extractedMetadata[key] = (error as any)[key];
      }
    });

    return {
      normalizedError: error,
      extractedMetadata,
    };
  }

  // Para objetos simples, convertemos para metadados
  if (typeof error === 'object' && error !== null) {
    return {
      normalizedError: toError(error),
      extractedMetadata: error as Record<string, any>,
    };
  }

  // Para outros tipos, convertemos para Error e não retornamos metadados
  return {
    normalizedError: toError(error),
    extractedMetadata: {},
  };
}

/**
 * Função para corrigir encoding de caracteres em mensagens de log
 * Converte caracteres com problemas de encoding para UTF-8 correto
 */
function _fixEncoding(message: string): string {
  if (!message || typeof message !== 'string') {
    return message;
  }

  try {
    // Corrigir caracteres comuns com problemas de encoding
    return message
      .replace(/Ã£/g, 'ã')
      .replace(/Ã¡/g, 'á')
      .replace(/Ã©/g, 'é')
      .replace(/Ã­/g, 'í')
      .replace(/Ã³/g, 'ó')
      .replace(/Ãº/g, 'ú')
      .replace(/Ã§/g, 'ç')
      .replace(/Ã /g, 'à')
      .replace(/Ã¢/g, 'â')
      .replace(/Ãª/g, 'ê')
      .replace(/Ã´/g, 'ô')
      .replace(/Ã¼/g, 'ü')
      .replace(/Ô£à/g, '✅')
      .replace(/­ƒöì/g, '🚀')
      .replace(/­ƒÄë/g, '📋')
      .replace(/Usu├írio/g, 'Usuário')
      .replace(/configura├º├úo/g, 'configuração')
      .replace(/autentica├º├úo/g, 'autenticação');
  } catch {
    // Se houver erro na correção, retornar a mensagem original
    return message;
  }
}

/**
 * Função utilitária para converter um valor desconhecido para LogMetadata | undefined
 * @param metadata Valor de metadados de tipo desconhecido
 * @returns Valor convertido para LogMetadata ou undefined
 */
export function toMetadata(metadata: unknown): LogMetadata | undefined {
  if (metadata === null || metadata === undefined) {
    return undefined;
  }

  if (typeof metadata === 'object' && metadata !== null) {
    return metadata as Record<string, any>;
  }

  return { value: metadata };
}

// Função segura para logging que evita erros em ambientes variados
export function safeConsoleLog(...args: any[]): void {
  try {
    if (typeof console !== 'undefined' && console.log) {
      console.log(...args);
    }
  } catch {
    // Silencia erros de console em ambientes restritos
  }
}

// Configurações do logger baseadas no ambiente
const isDevelopment = process.env.NODE_ENV === 'development';
const isTest = process.env.NODE_ENV === 'test';

// Configurações para diferentes ambientes
const config = {
  development: {
    level: 'debug',
    // Configuração otimizada para desenvolvimento com melhor formatação
    formatters: {
      level: (label: string) => {
        return { level: label };
      },
      log: (object: Record<string, unknown>) => {
        // Garantir que emojis e caracteres especiais sejam exibidos corretamente
        return object;
      },
    },
    // Configuração de serialização para melhor exibição
    serializers: {
      err: pino.stdSerializers.err,
      error: pino.stdSerializers.err,
    },
    // Configuração de timestamp mais legível
    timestamp: () =>
      `,"time":"${new Date().toLocaleString('pt-BR', {
        timeZone: 'America/Sao_Paulo',
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
      })}"`,
  },
  test: {
    level: 'error', // Em testes, só mostra erros por padrão
    enabled: process.env.DEBUG_LOGS === 'true', // Desabilita logs em testes, exceto quando explicitamente habilitado
  },
  production: {
    level: 'info', // Em produção, mostrar info, warn, error e fatal
    enabled: true,
    // Configuração otimizada para produção
    formatters: {
      level: (label: string) => {
        return { level: label };
      },
    },
    serializers: {
      err: pino.stdSerializers.err,
      error: pino.stdSerializers.err,
    },
  },
};

// Criar a instância do logger com configuração mais robusta
let pinoLogger: pino.Logger;
try {
  const loggerConfig = isDevelopment
    ? config.development
    : isTest
      ? config.test
      : config.production;

  // Em desenvolvimento, usar configuração simplificada para evitar worker thread issues
  if (isDevelopment) {
    pinoLogger = pino({
      level: 'debug',
      formatters: {
        level: (label: string) => ({ level: label }),
      },
      serializers: {
        err: pino.stdSerializers.err,
        error: pino.stdSerializers.err,
      },
      // Usar timestamp simples
      timestamp: pino.stdTimeFunctions.isoTime,
    });
  } else {
    pinoLogger = pino(loggerConfig);
  }
} catch (error) {
  // Fallback para logger básico se Pino falhar
  console.warn('Falha ao inicializar Pino logger, usando fallback:', error);
  pinoLogger = pino({
    level: 'info',
    formatters: {
      level: (label: string) => ({ level: label }),
    },
  });
}

// Wrapper de API simplificada para o logger
export const logger = {
  /**
   * Log de nível trace
   */
  trace: (message: string, metadata?: LogMetadata) => {
    pinoLogger.trace(metadata || {}, message);
  },

  /**
   * Log de nível debug
   */
  debug: (message: string, metadata?: LogMetadata) => {
    pinoLogger.debug(metadata || {}, message);
  },

  /**
   * Log de nível info
   */
  info: (message: string, metadata?: LogMetadata) => {
    pinoLogger.info(metadata || {}, message);
  },

  /**
   * Log de nível warn
   */
  warn: (message: string, metadata?: LogMetadata | unknown) => {
    // Se o segundo parâmetro for um erro, extrair como metadados
    if (metadata instanceof Error || (typeof metadata === 'object' && metadata !== null)) {
      const { extractedMetadata } = extractErrorData(metadata);
      pinoLogger.warn(extractedMetadata, message);
    } else {
      pinoLogger.warn(toMetadata(metadata) || {}, message);
    }
  },

  /**
   * Log de nível error
   * Aceita um erro de qualquer tipo e extrai metadados dele se necessário
   */
  error: (message: string, error?: unknown, metadata?: LogMetadata) => {
    const { normalizedError, extractedMetadata } = extractErrorData(error);

    const logData = {
      ...(metadata || {}),
      ...extractedMetadata,
      ...(normalizedError && {
        error: {
          message: normalizedError.message,
          stack: normalizedError.stack,
          name: normalizedError.name,
        },
      }),
    };

    pinoLogger.error(logData, message);
  },

  /**
   * Log de nível fatal
   * Aceita um erro de qualquer tipo e extrai metadados dele se necessário
   */
  fatal: (message: string, error?: unknown, metadata?: LogMetadata) => {
    const { normalizedError, extractedMetadata } = extractErrorData(error);

    const logData = {
      ...(metadata || {}),
      ...extractedMetadata,
      ...(normalizedError && {
        error: {
          message: normalizedError.message,
          stack: normalizedError.stack,
          name: normalizedError.name,
        },
      }),
    };

    pinoLogger.fatal(logData, message);
  },

  /**
   * Método para criar um logger com contexto
   * Útil para adicionar metadados consistentes a todos os logs de um módulo
   */
  createChild: (context: LogMetadata) => {
    const childLogger = pinoLogger.child(context);

    return {
      trace: (message: string, metadata?: LogMetadata) => {
        childLogger.trace(metadata || {}, message);
      },
      debug: (message: string, metadata?: LogMetadata) => {
        childLogger.debug(metadata || {}, message);
      },
      info: (message: string, metadata?: LogMetadata) => {
        childLogger.info(metadata || {}, message);
      },
      warn: (message: string, metadata?: LogMetadata | unknown) => {
        // Se o segundo parâmetro for um erro, extrair como metadados
        if (metadata instanceof Error || (typeof metadata === 'object' && metadata !== null)) {
          const { extractedMetadata } = extractErrorData(metadata);
          childLogger.warn(extractedMetadata, message);
        } else {
          childLogger.warn(toMetadata(metadata) || {}, message);
        }
      },
      error: (message: string, error?: unknown, metadata?: LogMetadata) => {
        const { normalizedError, extractedMetadata } = extractErrorData(error);

        const logData = {
          ...(metadata || {}),
          ...extractedMetadata,
          ...(normalizedError && {
            error: {
              message: normalizedError.message,
              stack: normalizedError.stack,
              name: normalizedError.name,
            },
          }),
        };

        childLogger.error(logData, message);
      },
      fatal: (message: string, error?: unknown, metadata?: LogMetadata) => {
        const { normalizedError, extractedMetadata } = extractErrorData(error);

        const logData = {
          ...(metadata || {}),
          ...extractedMetadata,
          ...(normalizedError && {
            error: {
              message: normalizedError.message,
              stack: normalizedError.stack,
              name: normalizedError.name,
            },
          }),
        };

        childLogger.fatal(logData, message);
      },
    };
  },

  // Mantemos o método "child" para compatibilidade com código existente
  child: function (context: LogMetadata) {
    return this.createChild(context);
  },
};

// ===== FUNÇÕES DE LOG SEGURAS PARA PRODUÇÃO =====

/**
 * Funções de log que são silenciadas em produção
 * Usadas para logs de inicialização e debug que não devem aparecer no console do navegador
 */

// Safe console functions que só logam em desenvolvimento (reutilizando isDevelopment)
export const safeConsoleError = isDevelopment ? console.error : () => {};
export const safeConsoleWarn = isDevelopment ? console.warn : () => {};
export const safeConsoleInfo = isDevelopment ? console.info : () => {};

/**
 * Logger seguro para inicialização de serviços
 * Silencia logs de inicialização em produção para manter o console limpo
 */
export const initLogger = {
  info: (message: string, ...args: unknown[]) => {
    if (isDevelopment) {
      console.log(`[INIT] ${message}`, ...args);
    }
  },
  warn: (message: string, ...args: unknown[]) => {
    if (isDevelopment) {
      console.warn(`[INIT] ${message}`, ...args);
    }
  },
  error: (message: string, ...args: unknown[]) => {
    // Erros sempre são mostrados, mesmo em produção
    console.error(`[INIT] ${message}`, ...args);
  },
  debug: (message: string, ...args: unknown[]) => {
    if (isDevelopment) {
      console.log(`[INIT DEBUG] ${message}`, ...args);
    }
  },
};

/**
 * Logger para serviços específicos que podem ser silenciados em produção
 */
export const serviceLogger = {
  environment: isDevelopment ? console.log : () => {},
  rateLimiters: isDevelopment ? console.log : () => {},
  telemetry: isDevelopment ? console.log : () => {},
  aiService: isDevelopment ? console.log : () => {},
  logger: isDevelopment ? console.log : () => {},
};

/**
 * Logger especializado para desenvolvimento com formatação melhorada
 * Exibe logs mais limpos e organizados no terminal
 */
export const devLogger = {
  /**
   * Log de inicialização com formatação especial
   */
  startup: (message: string, details?: Record<string, unknown>) => {
    if (!isDevelopment) return;

    const timestamp = new Date().toLocaleTimeString('pt-BR');
    console.log(`\n🚀 [${timestamp}] ${message}`);

    if (details) {
      Object.entries(details).forEach(([key, value]) => {
        console.log(`   ${key}: ${value}`);
      });
    }
  },

  /**
   * Log de configuração com formatação especial
   */
  config: (category: string, status: string, details?: string[]) => {
    if (!isDevelopment) return;

    const statusIcon = status === 'OK' ? '✅' : status === 'PARTIAL' ? '⚠️' : '❌';
    console.log(`${statusIcon} ${category}: ${status}`);

    if (details && details.length > 0) {
      details.forEach(detail => {
        console.log(`   ${detail}`);
      });
    }
  },

  /**
   * Log de seção com separador visual
   */
  section: (title: string) => {
    if (!isDevelopment) return;

    console.log(`\n${'='.repeat(50)}`);
    console.log(`📋 ${title.toUpperCase()}`);
    console.log(`${'='.repeat(50)}`);
  },

  /**
   * Log de progresso com indicador visual
   */
  progress: (current: number, total: number, item: string) => {
    if (!isDevelopment) return;

    const percentage = Math.round((current / total) * 100);
    const progressBar =
      '█'.repeat(Math.floor(percentage / 5)) + '░'.repeat(20 - Math.floor(percentage / 5));
    console.log(`[${progressBar}] ${percentage}% - ${item}`);
  },

  /**
   * Log de erro com formatação especial
   */
  error: (message: string, error?: Error) => {
    if (!isDevelopment) return;

    console.error(`\n🔴 ERRO: ${message}`);
    if (error) {
      console.error(`   Detalhes: ${error.message}`);
      if (error.stack) {
        console.error(`   Stack: ${error.stack.split('\n').slice(0, 3).join('\n')}`);
      }
    }
  },

  /**
   * Log de sucesso com formatação especial
   */
  success: (message: string, duration?: number) => {
    if (!isDevelopment) return;

    const durationText = duration ? ` (${duration}ms)` : '';
    console.log(`\n✅ SUCESSO: ${message}${durationText}`);
  },
};

export default logger;
