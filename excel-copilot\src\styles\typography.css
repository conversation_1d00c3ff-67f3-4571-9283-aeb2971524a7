/* Melhorar renderização de fontes */
h1,
h2,
h3,
h4,
h5,
h6 {
  @apply font-medium tracking-tight;
}

h1 {
  @apply text-4xl sm:text-5xl;
}

h2 {
  @apply text-3xl sm:text-4xl;
}

h3 {
  @apply text-2xl sm:text-3xl;
}

html {
  -webkit-text-size-adjust: 100%;
  text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  scroll-behavior: smooth;
  height: 100%;
}

/* Typography styles */
h1,
.h1 {
  font-size: var(--font-size-4xl);
  line-height: var(--line-height-tight);
  font-weight: 700;
  letter-spacing: -0.025em;
}

h2,
.h2 {
  font-size: var(--font-size-3xl);
  line-height: var(--line-height-tight);
  font-weight: 700;
  letter-spacing: -0.025em;
}

h3,
.h3 {
  font-size: var(--font-size-2xl);
  line-height: var(--line-height-tight);
  font-weight: 600;
}

h4,
.h4 {
  font-size: var(--font-size-xl);
  line-height: var(--line-height-tight);
  font-weight: 600;
}

h5,
.h5 {
  font-size: var(--font-size-lg);
  line-height: var(--line-height-normal);
  font-weight: 600;
}

h6,
.h6 {
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  font-weight: 600;
}

p {
  margin-bottom: 1em;
}

small {
  font-size: var(--font-size-xs);
}

/* Print styles */
@media print {
  html,
  body {
    background-color: white !important;
    color: black !important;
  }

  .no-print {
    display: none !important;
  }
}
