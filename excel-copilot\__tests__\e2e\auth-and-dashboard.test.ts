import { test, expect } from '@playwright/test';

// Configuração para autenticar antes dos testes
test.beforeEach(async ({ page }) => {
  // Simular autenticação bem-sucedida
  // Definir cookies/localstorage necessários para a sessão
  await page.context().addCookies([
    {
      name: 'next-auth.session-token',
      value: 'mock-session-token',
      domain: 'localhost',
      path: '/',
      httpOnly: true,
      secure: false,
    },
  ]);

  // Armazenar a informação do usuário no localStorage
  await page.evaluate(() => {
    localStorage.setItem(
      'user',
      JSON.stringify({
        id: 'test-user-id',
        name: 'Test User',
        email: '<EMAIL>',
      })
    );
  });
});

test.describe('Autenticação e Dashboard', () => {
  test('deve exibir o dashboard com planilhas do usuário', async ({ page }) => {
    // Navegar para a página do dashboard
    await page.goto('/dashboard');

    // Verificar se o usuário está autenticado corretamente
    await expect(page.getByText('Test User')).toBeVisible();

    // Verificar se o dashboard exibe elementos esperados
    await expect(page.getByRole('heading', { name: /Minhas Planilhas/i })).toBeVisible();
    await expect(page.getByText('Criar Nova Planilha')).toBeVisible();

    // Verificar se há planilhas listadas (assumindo que há pelo menos uma)
    await expect(page.locator('.workbook-card').first()).toBeVisible();
  });

  test('deve permitir criar uma nova planilha', async ({ page }) => {
    // Navegar para o dashboard
    await page.goto('/dashboard');

    // Clicar no botão para criar nova planilha
    await page.getByText('Criar Nova Planilha').click();

    // Verificar se o modal de criação aparece
    await expect(page.getByText('Nova Planilha')).toBeVisible();

    // Preencher o nome da planilha
    await page.getByLabel('Nome da Planilha').fill('Planilha de Teste E2E');

    // Clicar em criar
    await page.getByRole('button', { name: /Criar/i }).click();

    // Verificar se fomos redirecionados para a nova planilha
    await expect(page.url()).toContain('/workbook/');
    await expect(page.getByText('Planilha de Teste E2E')).toBeVisible();
  });

  test('deve permitir filtrar e buscar planilhas', async ({ page }) => {
    // Navegar para o dashboard
    await page.goto('/dashboard');

    // Verificar se a barra de busca existe
    await expect(page.getByPlaceholder('Buscar planilhas...')).toBeVisible();

    // Realizar uma busca
    await page.getByPlaceholder('Buscar planilhas...').fill('Vendas');

    // Verificar se os resultados são filtrados
    await expect(page.locator('.workbook-card').count()).toBeGreaterThan(0);

    // Verificar se algum resultado contém o termo buscado
    await expect(page.getByText(/Vendas/i).first()).toBeVisible();
  });

  test('deve permitir ordenar planilhas', async ({ page }) => {
    // Navegar para o dashboard
    await page.goto('/dashboard');

    // Clicar no seletor de ordenação
    await page.getByText('Ordenar por').click();

    // Selecionar "Mais recentes"
    await page.getByText('Mais recentes').click();

    // Verificar se a ordem foi aplicada (difícil testar visualmente, podemos verificar se não quebrou)
    await expect(page.locator('.workbook-card').first()).toBeVisible();

    // Selecionar outro tipo de ordenação
    await page.getByText('Ordenar por').click();
    await page.getByText('A-Z').click();

    // Verificar se a ordenação foi aplicada sem quebrar
    await expect(page.locator('.workbook-card').first()).toBeVisible();
  });
});
