// Excel Copilot Type Definitions
import { ExcelOperationType } from './global.d';

// DataWithMeta interface
export interface DataWithMeta<T = any> {
  data: T;
  meta: {
    headers?: Record<string, any>;
  };
}

// Chart Types - Consolidated
export enum ChartType {
  LINE = 'line',
  BAR = 'bar',
  COLUMN = 'column',
  AREA = 'area',
  SCATTER = 'scatter',
  PIE = 'pie',
  DONUT = 'donut',
  RADAR = 'radar',
  HEATMAP = 'heatmap',
  TREEMAP = 'treemap',
  BUBBLE = 'bubble',
  CANDLESTICK = 'candlestick',
  WATERFALL = 'waterfall',
  FUNNEL = 'funnel',
  GAUGE = 'gauge',
  BOXPLOT = 'boxplot',
  SANKEY = 'sankey',
  PARETO = 'pareto',
}

// Filter Operators
export enum FilterOperator {
  EQUALS = 'equals',
  NOT_EQUALS = 'notEquals',
  GREATER_THAN = 'greaterThan',
  LESS_THAN = 'lessThan',
  GREATER_THAN_OR_EQUAL = 'greaterThanOrEqual',
  LESS_THAN_OR_EQUAL = 'lessThanOrEqual',
  CONTAINS = 'contains',
  NOT_CONTAINS = 'notContains',
  BEGINS_WITH = 'beginsWith',
  ENDS_WITH = 'endsWith',
  BETWEEN = 'between',
  TOP_N = 'topN',
  BOTTOM_N = 'bottomN',
}

// Formula Operations
export interface FormulaOperation {
  formula: string;
  targetCell: string;
}

// Column Operations
export interface ColumnOperation {
  operation: string;
  columnName?: string;
  columnIndex?: number;
}

// Chart Operations
export interface ChartOperation {
  type: string;
  title?: string;
  data?: {
    xRange?: string;
    yRange?: string;
    range?: string;
  };
  options?: {
    showLegend?: boolean;
    colors?: string[];
    width?: number;
    height?: number;
  };
}

// Filter Operations
export interface FilterOperation {
  columnName?: string;
  columnIndex?: number;
  condition?: {
    operator?: string;
    value?: any;
    value2?: any;
  };
}

// Sort Operations
export interface SortOperation {
  columnName?: string;
  columnIndex?: number;
  direction?: 'asc' | 'desc';
}

// Table Operations
export interface TableOperation {
  range?: string;
  hasHeaders?: boolean;
  name?: string;
  style?: string;
}

// Cell Update Operations
export interface CellUpdateOperation {
  cell?: string;
  value?: any;
}

// Format Operations
export interface FormatOperation {
  range?: string;
  format?: {
    type?: string;
    style?: {
      bold?: boolean;
      italic?: boolean;
      underline?: boolean;
      color?: string;
      backgroundColor?: string;
      fontSize?: number;
      fontFamily?: string;
      horizontalAlignment?: string;
      verticalAlignment?: string;
    };
    numberFormat?: string;
    decimalPlaces?: number;
    currency?: string;
    date?: string;
    percentage?: boolean;
  };
}

// Pivot Table Operations
export interface PivotTableOperation {
  sourceRange?: string;
  destinationRange?: string;
  rowFields?: string[];
  columnFields?: string[];
  dataFields?: string[];
  filterFields?: string[];
  calculations?: {
    field?: string;
    function?: string;
    showAs?: string;
  }[];
  dateGrouping?: {
    field?: string;
    by?: string;
  }[];
}

// Conditional Format Operations
export interface ConditionalFormatOperation {
  type?: string;
  range?: string;
  condition?: string;
  cellValue?: {
    operator?: string;
    values?: any[];
    style?: FormatStyle;
  };
  colorScale?: {
    min?: { type?: string; color?: string; value?: any };
    mid?: { type?: string; color?: string; value?: any };
    max?: { type?: string; color?: string; value?: any };
  };
  dataBar?: {
    min?: { type?: string; value?: any };
    max?: { type?: string; value?: any };
    color?: string;
    gradient?: boolean;
    showValue?: boolean;
    border?: boolean;
    borderColor?: string;
  };
  iconSet?: {
    type?: string;
    reverse?: boolean;
    showValue?: boolean;
    thresholds?: { value?: any; type?: string }[];
  };
  topBottom?: {
    type?: string;
    value?: number;
    isPercent?: boolean;
    style?: FormatStyle;
  };
  textContains?: {
    text?: string;
    style?: FormatStyle;
  };
  dateOccurring?: {
    type?: string;
    style?: FormatStyle;
  };
  duplicateValues?: {
    type?: string;
    style?: FormatStyle;
  };
  formula?: {
    formula?: string;
    style?: FormatStyle;
  };
}

// Format Style
export interface FormatStyle {
  fill?: {
    type?: string;
    color?: string;
  };
  font?: {
    bold?: boolean;
    italic?: boolean;
    underline?: boolean;
    strikethrough?: boolean;
    color?: string;
    size?: number;
  };
  border?: {
    top?: { style?: string; color?: string };
    left?: { style?: string; color?: string };
    bottom?: { style?: string; color?: string };
    right?: { style?: string; color?: string };
  };
}

// Advanced Chart Operations
export interface AdvancedChartOperation {
  type?: string;
  sourceRange?: string;
  destinationRange?: string;
  title?: string;
  xAxis?: {
    title?: string;
    min?: number;
    max?: number;
    gridLines?: boolean;
  };
  yAxis?: {
    title?: string;
    min?: number;
    max?: number;
    gridLines?: boolean;
  };
  legend?: {
    show?: boolean;
    position?: string;
  };
  colors?: string[];
  animation?: boolean;
  stacked?: boolean;
}

// Health Checker Types
export interface HealthCheckResult {
  service: string;
  status: 'healthy' | 'unhealthy' | 'degraded';
  responseTime?: number;
  error?: string;
  details?: Record<string, any>;
}

// Logger Types
export enum LogLevel {
  DEBUG = 'debug',
  INFO = 'info',
  WARN = 'warn',
  ERROR = 'error',
}

export interface LoggerOptions {
  level?: LogLevel;
  timestamp?: boolean;
  prefix?: string;
}

// Rate Limiter Types
export interface RateLimitOptions {
  // Max requests allowed within the time window
  maxRequests: number;
  // Time window in seconds
  windowSizeInSeconds: number;
  // Optional patterns to match against the request path
  pathPatterns?: string[];
  // Optional function to get a unique identifier from the request
  getIdentifier?: (req: any) => string;
}

// Validation Types
export interface ValidationResult<T = any> {
  isValid: boolean;
  data?: T;
  errors?: string[];
  message?: string;
}

export interface ExtendedNextRequest {
  requestId?: string;
  validatedBody?: any;
  sanitizedQuery?: Record<string, any>;
  userSession?: any;
}

export type ApiHandler = (req: any, context: any) => Promise<any>;

// Chart Configuration Types
export interface AdvancedChartConfig {
  // Chart Type
  type: ChartType;
  sourceRange: string;
  destinationRange: string;
  title?: string;

  // Axes
  xAxis?: AxisConfig;
  yAxis?: AxisConfig;
  series?: SeriesConfig[];

  // Styling
  colors?: string[];
  theme?: string;
  animation?: boolean;

  // Components
  legend?: LegendConfig;
  grid?: GridConfig;
  paddingConfig?: {
    top?: number;
    right?: number;
    bottom?: number;
    left?: number;
  };

  // Additional features
  annotations?: AnnotationConfig[];
  subcharts?: boolean;
  responsive?: boolean;
  stacked?: boolean;
}

export interface AxisConfig {
  title?: string;
  min?: number;
  max?: number;
  tickFormat?: string;
  gridLines?: boolean;
  showLabels?: boolean;
  labelAngle?: number;
  labelTruncate?: number;
}

export interface SeriesConfig {
  name?: string;
  type?: string;
  color?: string;
  lineStyle?: string;
  markerType?: string;
  fillOpacity?: number;
  visible?: boolean;
  stack?: string;
}

export interface LegendConfig {
  show?: boolean;
  position?: string;
  orientation?: string;
  clickable?: boolean;
}

export interface GridConfig {
  x?: boolean;
  y?: boolean;
  color?: string;
  opacity?: number;
  dashed?: boolean;
}

export interface AnnotationConfig {
  type?: string;
  value?: any;
  text?: string;
  color?: string;
  position?: string;
}

// WebSocket Types
export enum WebSocketStatus {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  ERROR = 'error',
}

// Message types for WebSocket communication
export enum WebSocketMessageType {
  PING = 'ping',
  PONG = 'pong',
  CONNECT = 'connect',
  DISCONNECT = 'disconnect',
  OPERATION = 'operation',
  OPERATION_RESULT = 'operation_result',
  ERROR = 'error',
  GET_STATUS = 'get_status',
  STATUS = 'status',
  AUTH = 'auth',
}

export interface WebSocketMessage {
  id: string;
  type: WebSocketMessageType;
  timestamp?: number;
  method?: string;
  params?: any;
  payload?: any;
  error?: any;
}

// Excel Operation Base Interface
export interface ExcelOperation {
  type: ExcelOperationType;
  params?: any;
  data?: any;
  id?: string;
  description?: string;
}

// Application Config Interface
export interface AppConfig {
  darkMode?: boolean;
  language?: string;
  autoConnect?: boolean;
  wsUrl?: string;
  apiKey?: string;
}

// History Item Interface
export interface HistoryItem {
  id: string;
  timestamp: number;
  action: string;
  details?: any;
}

// Excel File Info Interface
export interface ExcelFileInfo {
  id: string;
  name?: string;
  path: string;
  sheets: SheetInfo[];
  lastModified: number;
}

// Sheet Info Interface
export interface SheetInfo {
  id: string;
  name?: string;
  visibleRange?: string;
  rowCount?: number;
  columnCount?: number;
}

// Operation Result Interface
export interface OperationResult<T = any> {
  success: boolean;
  message?: string;
  data?: T;
  error?: any;
}

// Excel Installation Info
export interface ExcelInstallInfo {
  installed: boolean;
  version?: string;
}

// Excel Cell Value Type
export type CellValue = string | number | boolean | Date | null;

// Service Metadata
export interface ServiceMetadata {
  name?: string;
  dependencies?: string[];
  initialized?: boolean;
  instance?: any;
}

export interface ServiceRegistry {
  [key: string]: ServiceMetadata;
}

// CSRF Token
export interface CSRFToken {
  token: string;
  expires: number;
}

// Rate Limiter Result
export interface RateLimiterOptions {
  // Time window in milliseconds
  windowMs: number;

  // Maximum number of requests in the time window
  maxRequests: number;

  // Error message when rate limit is exceeded
  message?: string;

  // Auto-increment counter even when blocked (for consistent window)
  autoIncrement?: boolean;
}

export interface RateLimiterResult {
  // Whether the request is rate limited
  limited: boolean;

  // Message to return to the client
  message?: string;

  // Number of requests remaining in the window
  remaining: number;

  // When the current window resets (Unix timestamp)
  resetAt: number;

  // Identifier used for this rate limit check
  identifier: string;
}

// Sanitization Result
export interface SanitizationResult<T = any> {
  isValid: boolean;
  sanitized?: T;
  originalValue?: any;
  errors?: string[];
}

// Metrics
export enum MetricType {
  COUNTER = 'counter',
  GAUGE = 'gauge',
  HISTOGRAM = 'histogram',
}

export interface Metric {
  name: string;
  type: MetricType;
  value: number;
  tags?: Record<string, string>;
  timestamp?: number;
}

export interface ErrorReport {
  message: string;
  stack?: string;
  context?: Record<string, any>;
  path?: string;
  timestamp?: number;
  userAgent?: string;
  severity?: string;
}

export interface TelemetryOptions {
  enabled?: boolean;
  sampleRate?: number;
  environment?: string;
  serviceName?: string;
}

// Workbook Data
export interface WorkbookData {
  id: string;
  name?: string;
  sheets: SheetData[];
  createdAt?: number;
  updatedAt?: number;
}

export interface SheetData {
  id: string;
  name?: string;
  workbookId: string;
  data: {
    headers?: string[];
    rows?: any[][];
    charts?: ChartData[];
    filtered?: boolean;
    filterCriteria?: {
      column?: string | number;
      operator?: string;
      value?: any;
      value2?: any;
    }[];
  };
  createdAt?: number;
  updatedAt?: number;
}

export interface ChartData {
  id: string;
  type: string;
  title?: string;
  xAxis?: {
    title?: string;
    range?: string;
  };
  yAxis?: {
    title?: string;
    range?: string;
  };
  series?: {
    name?: string;
    data?: any[];
  }[];
  options?: any;
  range?: string;
}

export interface WorkbookInfo {
  id: string;
  name?: string;
  path: string;
  sheets: WorksheetInfo[];
  lastModified: number;
}

export interface WorksheetInfo {
  id: string;
  name?: string;
  visibleRange?: string;
  rowCount?: number;
  columnCount?: number;
}

export interface WorksheetData {
  worksheetId: string;
  worksheetName: string;
  range: string;
  values: any[][];
  rowCount: number;
  columnCount: number;
  hasHeaders: boolean;
  headers?: string[];
}

export interface ChartInfo {
  id: string;
  name?: string;
  type: string;
  worksheetId: string;
  range: string;
}

export interface CreateChartOptions {
  name?: string;
  type: ChartType;
  worksheetId: string;
  range: string;
  title?: string;
  xAxisTitle?: string;
  yAxisTitle?: string;
  legendPosition?: string;
}

export interface DataAnalysisResult {
  type: string;
  data: any;
  visualizationOptions?: any;
}

// Chart Operation Types
export interface ChartOperationData {
  type: ChartType;
  title?: string;
  dataRange?: {
    columns?: string[];
    startRow?: number;
    endRow?: number;
  };
  options?: {
    showLegend?: boolean;
    xAxisTitle?: string;
    yAxisTitle?: string;
    colors?: string[];
  };
}

// Column Operation Types
export interface ColumnOperationData {
  column?: string;
  columnName?: string;
  columnIndex?: number;
  operation?: string;
  targetCell?: string;
  description?: string;
}

// Conditional Format Types
export interface ConditionalFormatRule {
  // Rule type
  type: string;
  range: string;

  // Cell value condition
  cellValue?: {
    operator: string;
    values: any[];
    style: FormatStyle;
  };

  // Color scale
  colorScale?: {
    min?: { type?: string; value?: any; color?: string };
    mid?: { type?: string; value?: any; color?: string };
    max?: { type?: string; value?: any; color?: string };
  };

  // Data bar
  dataBar?: {
    min?: { type?: string; value?: any };
    max?: { type?: string; value?: any };
    color?: string;
    gradient?: boolean;
    showValue?: boolean;
    negativeBarColor?: string;
    border?: boolean;
    borderColor?: string;
  };

  // Icon set
  iconSet?: {
    type?: string;
    reverse?: boolean;
    showValue?: boolean;
    thresholds?: {
      value?: any;
      type?: string;
    }[];
  };

  // Top/bottom
  topBottom?: {
    type?: string;
    value?: number;
    isPercent?: boolean;
    style?: FormatStyle;
  };

  // Text contains
  textContains?: {
    text?: string;
    style?: FormatStyle;
  };

  // Date occurring
  dateOccurring?: {
    type?: string;
    style?: FormatStyle;
  };

  // Duplicate values
  duplicateValues?: {
    type?: string;
    style?: FormatStyle;
  };

  // Formula
  formula?: {
    formula?: string;
    style?: FormatStyle;
  };
}

// Filter Operation Types
export interface FilterOperationData {
  column: string | number;
  operator: FilterOperator;
  value: any;
  value2?: any;
}

// Sort Operation Types
export interface SortOperationData {
  column: string | number;
  direction: 'asc' | 'desc';
}

// Formula Operation Types
export interface FormulaOperationData {
  cell: string;
  formula: string;
  description?: string;
}

// Pivot Table Config Type
export interface PivotTableConfig {
  // Ranges
  sourceRange: string;
  destinationRange: string;

  // Fields
  rowFields?: string[];
  columnFields?: string[];
  dataFields?: string[];
  filterFields?: string[];

  // Calculations
  calculations?: {
    field?: string;
    function?: string;
    showAs?: string;
  }[];

  // Layout options
  showHeaders?: boolean;
  compactLayout?: boolean;
  showRowTotals?: boolean;
  showColumnTotals?: boolean;

  // Grouping
  dateGrouping?: {
    field?: string;
    by?: string; // year, quarter, month, day
  }[];

  numberGrouping?: {
    field?: string;
    start?: number;
    end?: number;
    interval?: number;
  };
}
