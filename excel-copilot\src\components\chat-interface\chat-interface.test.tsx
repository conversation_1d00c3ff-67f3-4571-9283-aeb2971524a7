import { render } from '@testing-library/react';
import * as AIReact from 'ai/react';

import { ChatInterface } from './chat-interface';
import { EmptyState } from './empty-state';

// Mock dos hooks externos
jest.mock('ai/react', () => ({
  useChat: jest.fn(() => ({
    messages: [],
    input: '',
    handleInputChange: jest.fn(),
    handleSubmit: jest.fn(),
    setInput: jest.fn(),
    isLoading: false,
  })),
}));

jest.mock('@/components/providers/csrf-provider', () => ({
  useCSRF: jest.fn(() => ({ csrfToken: 'mock-token' })),
}));

jest.mock('@/contexts/LocaleContext', () => ({
  useLocale: () => ({
    t: function (key: string): string {
      return key;
    },
  }),
}));

jest.mock('@/hooks/useDesktopBridge', () => ({
  useDesktopBridge: jest.fn(() => ({ isExcelConnected: true })),
}));

jest.mock('@/components/ui/use-toast', () => ({
  useToast: jest.fn(() => ({ toast: jest.fn() })),
}));

// Mock para o componente EmptyState
jest.mock('./empty-state', () => ({
  EmptyState: jest.fn(() => <div data-testid="empty-state-mock">Empty State</div>),
}));

describe('ChatInterface', () => {
  it('should pass handleSuggestionClick to EmptyState correctly', () => {
    render(<ChatInterface workbookId="123" />);

    // Verificar se EmptyState foi chamado com a prop correta
    expect(EmptyState).toHaveBeenCalledWith(
      expect.objectContaining({
        onSuggestionClick: expect.any(Function),
      }),
      expect.anything()
    );
  });

  it('should handle suggestion clicks properly', () => {
    const useChatMock = AIReact.useChat as jest.Mock;
    const setInputMock = jest.fn();

    useChatMock.mockReturnValue({
      messages: [],
      input: '',
      handleInputChange: jest.fn(),
      handleSubmit: jest.fn(),
      setInput: setInputMock,
      isLoading: false,
    });

    render(<ChatInterface workbookId="123" />);

    // Obter a função handleSuggestionClick que foi passada para EmptyState
    const { onSuggestionClick } = (EmptyState as jest.Mock).mock.calls[0][0];

    // Testar a função com um objeto de sugestão
    onSuggestionClick({ text: 'Teste de sugestão' });

    // Verificar se setInput foi chamado com o texto correto
    expect(setInputMock).toHaveBeenCalledWith('Teste de sugestão');
  });
});
