/**
 * Componente para exibir gráficos gerados a partir de dados do Excel
 */
'use client';

import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  ChartOptions,
  ChartData,
} from 'chart.js';
import { Download, Bar<PERSON>hart2, Pie<PERSON><PERSON>, Line<PERSON>hart, Loader2 } from 'lucide-react';
import dynamic from 'next/dynamic';
import React, { useRef, useEffect, useState, useMemo } from 'react';
import { Bar, Line, Pie, Doughnut } from 'react-chartjs-2';

import { Card } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';

// Função temporária de useToast para evitar erro de compilação
const useToast = () => {
  return {
    toast: (_props: any) => {
      // Toast logged
    },
  };
};

// Importar Chart.js

// Registrar componentes do Chart.js
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend
);

// Import dinâmico do componente de gráfico
const _DynamicChartComponent = dynamic(
  () =>
    import('react-chartjs-2').then(mod => {
      return {
        default: ({ data, type, options, height, _width }: any) => {
          if (type === 'bar') return <mod.Bar data={data} options={options} height={height} />;
          if (type === 'line') return <mod.Line data={data} options={options} height={height} />;
          if (type === 'pie') return <mod.Pie data={data} options={options} height={height} />;
          return <div>Tipo de gráfico não suportado</div>;
        },
      };
    }),
  { ssr: false }
);

export type ChartType = 'bar' | 'line' | 'pie' | 'doughnut';

export interface ChartDisplayProps {
  data: {
    labels: string[];
    datasets: {
      label: string;
      data: number[];
      backgroundColor?: string | string[];
      borderColor?: string | string[];
      borderWidth?: number;
    }[];
  };
  type?: ChartType;
  title?: string;
  height?: number;
  showFormula?: boolean;
  formula?: string;
  className?: string;
}

/**
 * Componente para exibição de gráficos
 */
export function ChartDisplay({
  data,
  type = 'bar',
  title = 'Gráfico',
  height = 300,
  showFormula = false,
  formula = '',
  className = '',
}: ChartDisplayProps) {
  const { toast: _toast } = useToast();
  const chartRef = useRef<HTMLCanvasElement>(null);
  const [chartType, setChartType] = useState<ChartType>(type);
  const [isExporting, setIsExporting] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isClient, setIsClient] = useState(false);

  // Detectar lado do cliente para evitar erro de hidratação
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Definir temas para gráficos
  const isDarkMode = useMemo(() => {
    if (typeof window === 'undefined') return false;
    return document.documentElement.classList.contains('dark');
  }, []);

  const theme = {
    text: isDarkMode ? '#ffffff' : '#333333',
    grid: isDarkMode ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)',
    background: isDarkMode ? '#2D3748' : '#ffffff',
  };

  // Opções comuns para todos os tipos de gráficos
  const options: ChartOptions<any> = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top',
        labels: {
          color: theme.text,
        },
      },
      title: {
        display: !!title,
        text: title,
        color: theme.text,
        font: {
          size: 16,
          weight: 'bold',
        },
      },
      tooltip: {
        enabled: true,
        backgroundColor: isDarkMode ? 'rgba(0, 0, 0, 0.8)' : 'rgba(255, 255, 255, 0.8)',
        titleColor: isDarkMode ? '#fff' : '#000',
        bodyColor: isDarkMode ? '#fff' : '#000',
        borderColor: isDarkMode ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.2)',
        borderWidth: 1,
        padding: 10,
        cornerRadius: 6,
      },
    },
    scales:
      chartType !== 'pie' && chartType !== 'doughnut'
        ? {
            x: {
              grid: {
                color: theme.grid,
              },
              ticks: {
                color: theme.text,
              },
            },
            y: {
              grid: {
                color: theme.grid,
              },
              ticks: {
                color: theme.text,
              },
              beginAtZero: true,
            },
          }
        : undefined,
  };

  // Gerar cores diversificadas para datasets sem cores definidas
  const getDefaultColors = (count: number) => {
    const baseColors = [
      'rgba(54, 162, 235, 0.6)',
      'rgba(255, 99, 132, 0.6)',
      'rgba(75, 192, 192, 0.6)',
      'rgba(255, 206, 86, 0.6)',
      'rgba(153, 102, 255, 0.6)',
      'rgba(255, 159, 64, 0.6)',
      'rgba(199, 199, 199, 0.6)',
    ];

    // Replicar cores se necessário
    return Array.from({ length: count }, (_, i) => baseColors[i % baseColors.length]);
  };

  // Processar dados para garantir que todos os datasets tenham cores definidas
  const processedData: ChartData<any> = useMemo(() => {
    const processedDatasets = data.datasets.map(dataset => {
      // Se não houver backgroundColor definido, usar cores padrão
      if (!dataset.backgroundColor) {
        if (chartType === 'pie' || chartType === 'doughnut') {
          return {
            ...dataset,
            backgroundColor: getDefaultColors(dataset.data.length),
          };
        } else {
          return {
            ...dataset,
            backgroundColor: getDefaultColors(1)[0],
          };
        }
      }
      return dataset;
    });

    return {
      labels: data.labels,
      datasets: processedDatasets,
    };
  }, [data, chartType]);

  // Carregar gráfico
  useEffect(() => {
    // Simular tempo de carregamento para efeito de UI
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 800);

    return () => clearTimeout(timer);
  }, []);

  // Exportar gráfico para imagem
  const handleExportChart = async () => {
    if (!chartRef.current || isExporting) return;

    setIsExporting(true);

    try {
      const canvas = chartRef.current;
      const dataUrl = canvas.toDataURL('image/png');

      // Criar link para download
      const link = document.createElement('a');
      link.href = dataUrl;
      link.download = `${title.replace(/\s+/g, '_')}_${new Date().toISOString().slice(0, 10)}.png`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error('Erro ao exportar gráfico:', error);
    } finally {
      setIsExporting(false);
    }
  };

  // Renderizar o tipo de gráfico selecionado
  const renderChart = () => {
    if (isLoading) {
      return <Skeleton className="w-full h-full" />;
    }

    switch (chartType) {
      case 'bar':
        return <Bar data={processedData} options={options} ref={chartRef as any} />;
      case 'line':
        return <Line data={processedData} options={options} ref={chartRef as any} />;
      case 'pie':
        return <Pie data={processedData} options={options} ref={chartRef as any} />;
      case 'doughnut':
        return <Doughnut data={processedData} options={options} ref={chartRef as any} />;
      default:
        return <Bar data={processedData} options={options} ref={chartRef as any} />;
    }
  };

  if (!isClient) {
    return (
      <div
        style={{ height: height }}
        className="flex items-center justify-center bg-black/5 animate-pulse rounded-md"
      >
        Carregando gráfico...
      </div>
    );
  }

  // Verificar se os dados são válidos - agora usando safeData
  if (data.labels.length === 0 || data.datasets.length === 0) {
    return (
      <div className="p-4 bg-muted rounded-lg text-center">
        <p className="text-muted-foreground">
          Dados do gráfico não disponíveis ou em formato inválido
        </p>
      </div>
    );
  }

  return (
    <Card className={`p-4 ${className}`}>
      <div className="flex justify-between items-center mb-4">
        <div className="font-medium">{title}</div>

        <div className="flex items-center gap-2">
          <div className="flex items-center gap-2">
            <Label htmlFor="chart-type" className="sr-only">
              Tipo de Gráfico
            </Label>
            <Select value={chartType} onValueChange={value => setChartType(value as ChartType)}>
              <SelectTrigger className="h-8 w-[130px]">
                <SelectValue placeholder="Tipo de Gráfico" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="bar">
                  <div className="flex items-center gap-2">
                    <BarChart2 className="h-4 w-4" />
                    <span>Barras</span>
                  </div>
                </SelectItem>
                <SelectItem value="line">
                  <div className="flex items-center gap-2">
                    <LineChart className="h-4 w-4" />
                    <span>Linha</span>
                  </div>
                </SelectItem>
                <SelectItem value="pie">
                  <div className="flex items-center gap-2">
                    <PieChart className="h-4 w-4" />
                    <span>Pizza</span>
                  </div>
                </SelectItem>
                <SelectItem value="doughnut">
                  <div className="flex items-center gap-2">
                    <PieChart className="h-4 w-4" />
                    <span>Rosca</span>
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          <button
            onClick={() => handleExportChart()}
            disabled={isExporting || isLoading}
            className="h-8 w-8 inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background border border-input hover:bg-accent hover:text-accent-foreground"
            title="Exportar gráfico como imagem"
          >
            {isExporting ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Download className="h-4 w-4" />
            )}
          </button>
        </div>
      </div>

      <div style={{ height: `${height}px` }} className="relative">
        {renderChart()}
      </div>

      {showFormula && formula && (
        <div className="mt-3 pt-3 border-t text-xs text-muted-foreground">
          <span className="font-medium">Fórmula: </span>
          <code>{formula}</code>
        </div>
      )}
    </Card>
  );
}

export default ChartDisplay;
