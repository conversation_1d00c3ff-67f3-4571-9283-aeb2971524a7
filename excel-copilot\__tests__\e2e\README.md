# Testes End-to-End (E2E)

Este diretório contém testes E2E automatizados para o Excel Copilot, implementados com Playwright.

## Estrutura de Testes

Os testes estão organizados por funcionalidade:

- `basic.spec.ts`: Testes básicos da aplicação (carregamento da página inicial)
- `auth-and-dashboard.test.ts`: Testes de autenticação e funcionalidades do dashboard
- `upload-and-analyze.test.ts`: Testes de upload e análise de planilhas
- `upload-and-chat.spec.ts`: Testes de interação via chat após upload de planilha
- `workbook-editing.spec.ts`: Testes de edição de células e manipulação de planilhas
- `chart-interactions.spec.ts`: Testes de criação e interação com gráficos
- `ai-commands.spec.ts`: Testes de comandos avançados de IA e análise de dados
- `mobile-responsiveness.spec.ts`: Testes de responsividade em dispositivos móveis
- `desktop-bridge.spec.ts`: Testes de integração com a bridge de desktop

## Pré-requisitos

Para executar os testes:

1. Instale as dependências:

   ```bash
   npm install
   ```

2. Instale os navegadores do Playwright:
   ```bash
   npx playwright install
   ```

## Executando os Testes

### Executar todos os testes E2E

```bash
npm run test:e2e
```

### Executar testes específicos

```bash
npx playwright test auth-and-dashboard.test.ts
```

### Executar testes com interface visual

```bash
npx playwright test --ui
```

### Gerar relatório HTML dos testes

```bash
npx playwright test --reporter=html
```

## Estratégias de Teste

Os testes E2E seguem estas estratégias:

1. **Simulação de Autenticação**: Usamos cookies e localStorage para simular o estado autenticado
2. **Dados de Teste**: Criamos dados dinamicamente para testar funcionalidades
3. **Verificações Visuais**: Confirmamos elementos visuais e comportamento de UI
4. **Responsividade**: Testamos diferentes tamanhos de tela e dispositivos
5. **Mock de APIs**: Simulamos respostas de APIs (como a bridge de desktop)

## Boas Práticas

- Mantenha os testes independentes (não dependentes do estado de outros testes)
- Use seletores estáveis (data-testid, role, text)
- Configure timeouts razoáveis para operações assíncronas (10s máximo)
- Verifique tanto sucesso quanto falhas (casos de erro)
- Utilize a estrutura beforeEach para código de configuração compartilhado

## Problemas Conhecidos

- Os testes que dependem de interação com a IA podem ser mais lentos
- Testes de mobile podem ter falsos positivos devido a diferenças de renderização
- A bridge de desktop é mockada e não representa todas as condições reais

## CI/CD

Os testes E2E são executados na pipeline de CI em:

- Pull Requests
- Merges para main
- Deploys para produção
