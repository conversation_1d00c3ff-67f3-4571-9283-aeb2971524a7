import { User } from '@prisma/client';

/**
 * Tipo estendido para o usuário da sessão do NextAuth
 * Combina dados do Prisma com campos específicos da aplicação
 */
export interface SessionUser extends Omit<User, 'passwordHash' | 'salt'> {
  id: string;
  email: string;
  name?: string | null;
  image?: string | null;
  isAdmin?: boolean;
  isBanned?: boolean;
  plan?: string;
  stripeCustomerId?: string | null;
  usage?: {
    requests: number;
    cells: number;
    charts: number;
    advancedAI: number;
  };
}

declare module 'next-auth' {
  /**
   * Estender a interface Session para incluir o usuário tipado
   */
  interface Session {
    user: SessionUser;
    expires: string;
  }

  /**
   * Estender a interface User para compatibilidade
   */
  interface User {
    id: string;
    name?: string | null;
    email?: string | null;
    image?: string | null;
  }
}

declare module 'next-auth/jwt' {
  /**
   * Estender JWT para incluir informações do usuário
   */
  interface JWT {
    sub?: string;
    user?: SessionUser;
    iat?: number;
    exp?: number;
    jti?: string;
  }
}
