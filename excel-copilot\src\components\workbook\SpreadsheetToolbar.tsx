'use client';

import { Undo, Redo, Save, Loader2, KeyboardIcon, FullscreenIcon } from 'lucide-react';
import { memo } from 'react';

import { Button } from '@/components/ui/button';

interface SpreadsheetToolbarProps {
  // Undo/Redo
  canUndo: boolean;
  canRedo: boolean;
  onUndo: () => void;
  onRedo: () => void;

  // Save
  isSaving: boolean;
  onSave: () => void;
  readOnly?: boolean;

  // Other actions
  onShowKeyboardShortcuts: () => void;
  onToggleFullScreen: () => void;
  isFullScreen: boolean;
}

/**
 * Toolbar otimizada para o SpreadsheetEditor
 * Memoizada para evitar re-renders desnecessários
 */
export const SpreadsheetToolbar = memo<SpreadsheetToolbarProps>(
  ({
    canUndo,
    canRedo,
    onUndo,
    onRedo,
    isSaving,
    onSave,
    readOnly = false,
    onShowKeyboardShortcuts,
    onToggleFullScreen,
    isFullScreen,
  }) => {
    return (
      <div className="flex items-center gap-2 p-2 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        {/* Undo/Redo */}
        <div className="flex items-center gap-1">
          <Button
            onClick={onUndo}
            disabled={!canUndo}
            variant="ghost"
            size="sm"
            title="Desfazer (Ctrl+Z)"
          >
            <Undo className="h-4 w-4" />
          </Button>

          <Button
            onClick={onRedo}
            disabled={!canRedo}
            variant="ghost"
            size="sm"
            title="Refazer (Ctrl+Y)"
          >
            <Redo className="h-4 w-4" />
          </Button>
        </div>

        {/* Separator */}
        <div className="w-px h-6 bg-border" />

        {/* Save */}
        {!readOnly && (
          <Button
            onClick={onSave}
            disabled={isSaving}
            variant="ghost"
            size="sm"
            title="Salvar (Ctrl+S)"
          >
            {isSaving ? <Loader2 className="h-4 w-4 animate-spin" /> : <Save className="h-4 w-4" />}
            {isSaving ? 'Salvando...' : 'Salvar'}
          </Button>
        )}

        {/* Spacer */}
        <div className="flex-1" />

        {/* Right side actions */}
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={onShowKeyboardShortcuts}
            title="Atalhos de teclado"
          >
            <KeyboardIcon className="h-4 w-4" />
          </Button>

          <Button
            variant="ghost"
            size="sm"
            onClick={onToggleFullScreen}
            title={isFullScreen ? 'Sair da tela cheia' : 'Tela cheia'}
          >
            <FullscreenIcon className="h-4 w-4" />
          </Button>
        </div>
      </div>
    );
  }
);

SpreadsheetToolbar.displayName = 'SpreadsheetToolbar';
