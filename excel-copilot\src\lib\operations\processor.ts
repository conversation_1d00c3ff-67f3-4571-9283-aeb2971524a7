/**
 * Processador de operações para manipulação de dados Excel
 */

// Definição dos tipos de operação
export enum OperationType {
  FORMULA = 'FORMULA',
  CHART = 'CHART',
  TABLE = 'TABLE',
  FORMAT = 'FORMAT',
  COLUMN_OPERATION = 'COLUMN_OPERATION',
  ROW_OPERATION = 'ROW_OPERATION',
  CELL_OPERATION = 'CELL_OPERATION',
  MACRO = 'MACRO',
  FILTER = 'FILTER',
  SORT = 'SORT',
  PIVOT = 'PIVOT',
}

// Interface para operações
export interface Operation {
  type: string;
  data: any;
  description?: string;
}

/**
 * Extrai operações a partir de texto/JSON
 * @param input Texto ou objeto contendo operações
 * @returns Array de operações
 */
export function extractOperations(input: string | any): Operation[] {
  try {
    // Se for string, tentar converter para objeto
    if (typeof input === 'string') {
      try {
        const jsonMatch = input.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          input = JSON.parse(jsonMatch[0]);
        } else {
          // Texto sem JSON válido, retornar operação padrão
          return [
            {
              type: 'TABLE',
              data: { description: `Processando: "${input.substring(0, 100)}..."` },
            },
          ];
        }
      } catch (error) {
        // Erro ao analisar JSON, retornar operação padrão
        return [
          {
            type: 'TABLE',
            data: { description: `Não foi possível extrair operações do texto: ${error}` },
          },
        ];
      }
    }

    // Se temos um objeto com campo operations, extrair
    if (input && Array.isArray(input.operations)) {
      return input.operations;
    }

    // Se temos um array, retornar diretamente
    if (Array.isArray(input)) {
      return input;
    }

    // Caso ainda seja um objeto, tentar extrair como operação única
    if (input && typeof input === 'object' && input.type) {
      return [input];
    }

    // Fallback para operação vazia
    return [
      {
        type: 'TABLE',
        data: { description: 'Nenhuma operação encontrada nos dados fornecidos' },
      },
    ];
  } catch (error) {
    // Erro ao processar, retornar operação de erro
    return [
      {
        type: 'TABLE',
        data: { error: String(error) },
      },
    ];
  }
}
