/**
 * Tipos para Desktop Bridge
 * Integração com aplicações desktop (Excel nativo)
 */

// Status da conexão com o bridge
export type BridgeStatus = 'connected' | 'disconnected' | 'connecting' | 'error';

// Comando enviado para o bridge
export interface BridgeCommand {
  id: string;
  type: string;
  action: string;
  payload?: any;
}

// Resposta do bridge
export interface BridgeResponse {
  id: string;
  success: boolean;
  data?: any;
  error?: string;
}

// Estado do bridge
export interface BridgeState {
  status: BridgeStatus;
  connected: boolean;
  error: string | null;
  platform: string | null;
  version: string | null;
  lastActivity: number | null;
  _status?: string;
  _platform?: string;
}

// Ações do bridge
export interface BridgeActions {
  connect: () => Promise<boolean>;
  disconnect: () => Promise<boolean>;
  _disconnect?: () => Promise<boolean>;
}

// Resultado da operação
export interface OperationResult {
  success: boolean;
  data?: any;
  error?: string;
  message?: string;
  status?: string;
}

// Informações de arquivo do Excel
export interface ExcelFileInfo {
  name: string;
  path: string;
  size: number;
  lastModified: number;
  sheets?: string[];
  installed?: boolean;
}

// Diálogos
export interface ShowOpenDialogReturnValue {
  canceled: boolean;
  filePaths: string[];
}

export interface ShowSaveDialogReturnValue {
  canceled: boolean;
  filePath: string;
}
