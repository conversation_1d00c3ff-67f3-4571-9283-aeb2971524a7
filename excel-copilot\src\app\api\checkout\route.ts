import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import <PERSON><PERSON> from 'stripe';

import { logger } from '@/lib/logger';
import { stripe, getPriceIdFromPlan } from '@/lib/stripe';
import { prisma } from '@/server/db/client';

export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

export async function POST(req: NextRequest): Promise<NextResponse> {
  try {
    // Verificar se o Stripe está configurado
    if (!stripe) {
      return NextResponse.json({ error: 'Stripe não está configurado.' }, { status: 503 });
    }

    // Verificar autenticação
    const session = await getServerSession();
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Não autorizado. Faça login para continuar.' },
        { status: 401 }
      );
    }

    const userId = session.user.id;

    // Obter dados do corpo da requisição
    const { plan, successUrl, cancelUrl } = await req.json();

    if (!plan || !successUrl || !cancelUrl) {
      return NextResponse.json(
        { error: 'Dados incompletos. Por favor, forneça plano e URLs de redirecionamento.' },
        { status: 400 }
      );
    }

    // Obter o usuário do banco de dados
    const user = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      return NextResponse.json({ error: 'Usuário não encontrado.' }, { status: 404 });
    }

    // Verificar se já existe uma assinatura com stripeCustomerId para este usuário
    let stripeCustomerId: string;
    const existingSubscription = await prisma.subscription.findFirst({
      where: { userId },
      select: { stripeCustomerId: true },
    });

    if (existingSubscription?.stripeCustomerId) {
      stripeCustomerId = existingSubscription.stripeCustomerId;
    } else {
      // Criar um novo cliente Stripe
      const email = session.user.email || '';
      const name = session.user.name || '';

      const customer = await stripe.customers.create({
        email,
        name,
        metadata: { userId },
      } as Stripe.CustomerCreateParams);

      stripeCustomerId = customer.id;
    }

    // Obter o ID do preço com base no plano
    const priceId = getPriceIdFromPlan(plan);

    // Criar sessão de checkout
    const checkoutSession = await stripe.checkout.sessions.create({
      customer: stripeCustomerId,
      line_items: [
        {
          price: priceId,
          quantity: 1,
        },
      ],
      mode: 'subscription',
      success_url: successUrl,
      cancel_url: cancelUrl,
      allow_promotion_codes: true,
      billing_address_collection: 'auto',
      metadata: {
        userId: user.id,
        plan: plan,
      },
      payment_method_types: ['card'],
      locale: 'pt-BR',
      subscription_data: {
        metadata: {
          userId: user.id,
          plan: plan,
        },
      },
    });

    return NextResponse.json({ url: checkoutSession.url });
  } catch (error) {
    logger.error('[CHECKOUT_ERROR]', error);
    return NextResponse.json(
      {
        error: 'Erro ao processar o checkout. Por favor, tente novamente.',
        details: process.env.NODE_ENV === 'development' ? String(error) : undefined,
      },
      { status: 500 }
    );
  }
}
