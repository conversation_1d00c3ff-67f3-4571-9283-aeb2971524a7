/**
 * @jest-environment node
 */

import { GeminiService } from '@/server/ai/gemini-service';
import * as fs from 'fs';
import * as path from 'path';

// Definir as variáveis de ambiente necessárias para o Vertex AI
// Verificar se o arquivo de credenciais existe antes de executar os testes
const credentialsPath = path.join(process.cwd(), 'vertex-credentials.json');
const hasCredentials = fs.existsSync(credentialsPath);

if (hasCredentials) {
  // Configurar o caminho das credenciais para o teste
  process.env.GOOGLE_APPLICATION_CREDENTIALS = credentialsPath;

  // Ativar Vertex AI explicitamente
  process.env.VERTEX_AI_ENABLED = 'true';

  // Configurar o ID do projeto a partir do arquivo de credenciais
  try {
    const credentialsContent = fs.readFileSync(credentialsPath, 'utf8');
    const credentials = JSON.parse(credentialsContent);
    if (credentials.project_id) {
      process.env.VERTEX_AI_PROJECT_ID = credentials.project_id;
      console.log(`Usando projeto: ${credentials.project_id} para testes de integração`);
    }
  } catch (error) {
    console.error('Erro ao ler arquivo de credenciais:', error);
  }
}

// Definir timeout maior para chamadas de API
jest.setTimeout(30000);

// Pular testes se não houver credenciais
const testOrSkip = hasCredentials ? describe : describe.skip;

testOrSkip('Vertex AI Integration Tests', () => {
  let geminiService: GeminiService;
  // Armazenar streams e readers para limpeza
  const streamsToClean: ReadableStream<any>[] = [];
  const readersToClean: ReadableStreamDefaultReader<any>[] = [];

  beforeAll(() => {
    // Limpar instância existente (para garantir uma nova inicialização com as variáveis de ambiente atualizadas)
    jest.resetModules();
    // Criar serviço
    geminiService = GeminiService.getInstance();
    console.log('Serviço inicializado para testes de integração');
  });

  // Limpar recursos após cada teste
  afterEach(async () => {
    // Fechar todos os readers abertos
    for (const reader of readersToClean) {
      try {
        await reader.cancel();
      } catch (e) {
        // Ignorar erros ao limpar
      }
    }
    readersToClean.length = 0;
    streamsToClean.length = 0;
  });

  // Limpar todos os recursos após os testes
  afterAll(async () => {
    // Desligar o serviço GeminiService
    try {
      await geminiService.shutdown();
    } catch (e) {
      // Ignorar erros ao desligar
    }
  });

  // Verificar primeiro se a integração básica funciona
  describe('Status da Integração', () => {
    it('deve ter o Vertex AI inicializado corretamente', () => {
      // Checar se o serviço tem as propriedades internas esperadas
      const geminiServiceAny = geminiService as any;
      expect(geminiServiceAny.vertexAI).toBeDefined();
      expect(geminiServiceAny.vertexModel).toBeDefined();
    });
  });

  describe('Comunicação com o Vertex AI', () => {
    it('deve enviar uma mensagem e receber resposta', async () => {
      // Enviar uma mensagem simples para testar a API
      const response = await geminiService.sendMessage('Qual é a capital do Brasil?');

      // Verificar se a resposta contém algum texto válido
      expect(response).toBeTruthy();
      expect(typeof response).toBe('string');
      expect(response.length).toBeGreaterThan(5);

      // Verificar se a resposta menciona Brasília ou Brasil
      expect(response.toLowerCase()).toContain('brasil');
    });

    it('deve enviar mensagem com contexto de Excel', async () => {
      // Simular contexto de uma planilha Excel
      const options = {
        excelContext: {
          activeSheet: 'Planilha1',
          selection: 'A1:C10',
          headers: ['Nome', 'Idade', 'Cidade'],
          recentOperations: ['Ordenação por Idade'],
        },
      };

      // Enviar mensagem com contexto
      const response = await geminiService.sendMessage(
        'Como faço para calcular a média de idades?',
        [],
        options
      );

      // Verificar se a resposta é relevante para o contexto
      expect(response).toBeTruthy();
      expect(response.toLowerCase()).toMatch(/m[ée]dia|average/);
    });
  });

  describe('Streaming do Vertex AI', () => {
    it('deve criar um stream de resposta que retorna chunks', async () => {
      // Solicitar resposta em streaming
      const stream = await geminiService.streamMessage('Me dê 5 dicas para usar Excel melhor');

      // Verificar se o stream foi criado
      expect(stream).toBeInstanceOf(ReadableStream);

      // Registrar stream para limpeza
      streamsToClean.push(stream);

      // Ler o stream completo
      const reader = stream.getReader();
      readersToClean.push(reader);

      const decoder = new TextDecoder();
      let result = '';

      let chunkCount = 0;
      let done = false;

      // Limitar a quantidade de chunks lidos para evitar testes muito longos
      const MAX_CHUNKS = 5;

      while (!done && chunkCount < MAX_CHUNKS) {
        const chunk = await reader.read();
        done = chunk.done;
        if (chunk.value) {
          chunkCount++;
          result += decoder.decode(chunk.value);
        }
      }

      // Se não terminou a leitura natural, cancelar o reader
      if (!done) {
        await reader.cancel();
      }

      // Verificar se recebemos conteúdo significativo
      expect(chunkCount).toBeGreaterThan(0);
      expect(result.length).toBeGreaterThan(10);
    });
  });

  describe('Processamento de comandos Excel', () => {
    it('deve processar um comando de Excel e retornar JSON válido', async () => {
      // Enviar um comando para criar uma fórmula
      const response = await geminiService.processCommand('Calcule a soma das vendas na coluna B');

      // Verificar se a resposta pode ser interpretada como JSON
      expect(() => JSON.parse(response)).not.toThrow();

      // Analisar o JSON retornado
      const jsonResponse = JSON.parse(response);

      // Verificar a estrutura esperada
      expect(jsonResponse).toHaveProperty('operations');
      expect(Array.isArray(jsonResponse.operations)).toBe(true);

      // Verificar se pelo menos uma operação foi retornada
      expect(jsonResponse.operations.length).toBeGreaterThan(0);

      // Verificar se a operação está relacionada a fórmulas ou soma
      const hasRelevantOperation = jsonResponse.operations.some(
        (op: any) =>
          op.type === 'FORMULA' ||
          (op.data && (op.data.formula === 'SUM' || op.data.formula?.includes('SUM')))
      );

      expect(hasRelevantOperation).toBe(true);
    });
  });

  // Teste avançado para cenários específicos do Excel
  describe('Cenários avançados do Excel', () => {
    it('deve lidar com solicitações complexas do Excel', async () => {
      // Uma solicitação mais complexa relacionada ao Excel
      const command =
        'Crie uma tabela dinâmica que mostre vendas por região e por trimestre, com total geral';

      // Processar o comando
      const response = await geminiService.processCommand(command);

      // Verificar se a resposta é relacionada a tabelas dinâmicas
      const jsonResponse = JSON.parse(response);

      // Verificar se há operação de tabela dinâmica
      const hasPivotTableOperation = jsonResponse.operations.some(
        (op: any) => op.type === 'PIVOT_TABLE'
      );

      expect(hasPivotTableOperation).toBe(true);
    });
  });
});
