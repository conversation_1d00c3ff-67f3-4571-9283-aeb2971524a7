/**
 * Design Tokens do Excel Copilot
 *
 * Este arquivo centraliza todas as definições de design do aplicativo,
 * garantindo consistência visual em todos os componentes.
 */

// Espaçamentos padronizados (em pixels multiplicados por 4)
export const SPACING = {
  xs: 'space-y-1 space-x-1', // 4px
  sm: 'space-y-2 space-x-2', // 8px
  md: 'space-y-4 space-x-4', // 16px
  lg: 'space-y-6 space-x-6', // 24px
  xl: 'space-y-8 space-x-8', // 32px

  // Gaps para Flex e Grid
  gap: {
    xs: 'gap-1', // 4px
    sm: 'gap-2', // 8px
    md: 'gap-4', // 16px
    lg: 'gap-6', // 24px
    xl: 'gap-8', // 32px
  },

  // Paddings
  padding: {
    xs: 'p-1', // 4px
    sm: 'p-2', // 8px
    md: 'p-4', // 16px
    lg: 'p-6', // 24px
    xl: 'p-8', // 32px

    // Variações
    card: 'p-6',
    section: 'py-16 px-6',
    inputField: 'px-4 py-2',
  },

  // Margins
  margin: {
    xs: 'm-1',
    sm: 'm-2',
    md: 'm-4',
    lg: 'm-6',
    xl: 'm-8',

    bottom: {
      xs: 'mb-1',
      sm: 'mb-2',
      md: 'mb-4',
      lg: 'mb-6',
      xl: 'mb-8',
    },
  },
};

// Tamanhos de elementos
export const SIZES = {
  icon: {
    xs: 'w-3 h-3',
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6',
    xl: 'w-8 h-8',
  },

  // Botões e elementos interativos
  control: {
    sm: 'h-8',
    md: 'h-10',
    lg: 'h-12',
  },

  // Áreas de conteúdo
  container: {
    sm: 'max-w-screen-sm',
    md: 'max-w-screen-md',
    lg: 'max-w-screen-lg',
    xl: 'max-w-screen-xl',
    full: 'max-w-full',
  },

  // Altura de áreas de rolagem
  scroll: {
    sm: 'h-[200px]',
    md: 'h-[400px]',
    lg: 'h-[600px]',
  },
};

// Bordas e arredondamentos
export const BORDERS = {
  radius: {
    sm: 'rounded',
    md: 'rounded-md',
    lg: 'rounded-lg',
    xl: 'rounded-xl',
    full: 'rounded-full',
  },

  width: {
    none: 'border-0',
    thin: 'border',
    medium: 'border-2',
    thick: 'border-4',
  },

  // Estilos comuns
  card: 'border rounded-lg',
  input: 'border rounded-md',
  button: 'rounded-md',
};

// Animações e transições
export const ANIMATIONS = {
  transition: {
    fast: 'transition-all duration-150 ease-in-out',
    medium: 'transition-all duration-300 ease-in-out',
    slow: 'transition-all duration-500 ease-in-out',
  },

  hover: {
    scale: 'hover:scale-105',
    brightness: 'hover:brightness-110',
    opacity: 'hover:opacity-90',
  },

  // Animações específicas
  spin: 'animate-spin',
  pulse: 'animate-pulse',
  // Para animações mais complexas como fadeIn, usar o sistema de Framer Motion em @/lib/animations.ts
  loading: 'animate-pulse',
  shake: 'animate-[shake_0.82s_cubic-bezier(.36,.07,.19,.97)_both]',
};

// Tipogrofia
export const TYPOGRAPHY = {
  size: {
    xs: 'text-xs',
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg',
    xl: 'text-xl',
    '2xl': 'text-2xl',
    '3xl': 'text-3xl',
  },

  weight: {
    normal: 'font-normal',
    medium: 'font-medium',
    semibold: 'font-semibold',
    bold: 'font-bold',
  },

  // Estilos comuns
  heading: {
    h1: 'text-4xl md:text-5xl lg:text-6xl font-bold',
    h2: 'text-3xl md:text-4xl font-bold',
    h3: 'text-2xl font-semibold',
    h4: 'text-xl font-semibold',
  },

  body: {
    small: 'text-sm',
    default: 'text-base',
    large: 'text-lg',
  },

  // Variações específicas
  label: 'text-sm font-medium',
  caption: 'text-xs text-muted-foreground',
};

// Paletas de cores (usa as variáveis do Tailwind para compatibilidade com dark mode)
export const COLORS = {
  // Variantes semânticas para uso consistente
  intent: {
    primary: {
      base: 'bg-primary text-primary-foreground',
      hover: 'hover:bg-primary/90',
      light: 'bg-primary/10 text-primary',
    },
    secondary: {
      base: 'bg-secondary text-secondary-foreground',
      hover: 'hover:bg-secondary/90',
      light: 'bg-secondary/10 text-secondary',
    },
    accent: {
      base: 'bg-accent text-accent-foreground',
      hover: 'hover:bg-accent/90',
      light: 'bg-accent/20 text-accent-foreground',
    },
    destructive: {
      base: 'bg-destructive text-destructive-foreground',
      hover: 'hover:bg-destructive/90',
      light: 'bg-destructive/10 text-destructive',
    },
    success: {
      base: 'bg-green-600 text-white dark:bg-green-500',
      hover: 'hover:bg-green-700 dark:hover:bg-green-600',
      light: 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400',
    },
    warning: {
      base: 'bg-yellow-500 text-white dark:bg-yellow-600',
      hover: 'hover:bg-yellow-600 dark:hover:bg-yellow-700',
      light: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400',
    },
  },

  // Variações para fundos e texto
  background: {
    card: 'bg-card text-card-foreground',
    default: 'bg-background text-foreground',
    subtle: 'bg-muted text-muted-foreground',
    inverted: 'bg-foreground text-background',
  },

  // Variações de texto
  text: {
    primary: 'text-foreground',
    secondary: 'text-secondary-foreground',
    muted: 'text-muted-foreground',
    accent: 'text-accent-foreground',
    destructive: 'text-destructive',
  },

  // Outros elementos comuns
  border: 'border-border',
  focus: 'focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2',
  disabled: 'opacity-50 cursor-not-allowed',
};

// Sombras
export const SHADOWS = {
  sm: 'shadow-sm',
  md: 'shadow',
  lg: 'shadow-lg',
  xl: 'shadow-xl',

  // Variações para cartões e elementos elevados
  card: 'shadow',
  dropdown: 'shadow-lg',
  modal: 'shadow-xl',
};

// Layouts comuns (flexbox e grid)
export const LAYOUTS = {
  center: 'flex justify-center items-center',
  centerColumn: 'flex flex-col justify-center items-center',
  between: 'flex justify-between items-center',
  start: 'flex justify-start items-center',
  end: 'flex justify-end items-center',

  grid: {
    cols1: 'grid grid-cols-1',
    cols2: 'grid grid-cols-1 md:grid-cols-2',
    cols3: 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
    cols4: 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4',
  },
};

// Estilos pré-compostos para componentes comuns
export const COMPONENT_STYLES = {
  // Botões
  button: {
    base: `${BORDERS.button} font-medium ${ANIMATIONS.transition.fast}`,
    primary: `${COLORS.intent.primary.base} ${COLORS.intent.primary.hover}`,
    secondary: `${COLORS.intent.secondary.base} ${COLORS.intent.secondary.hover}`,
    destructive: `${COLORS.intent.destructive.base} ${COLORS.intent.destructive.hover}`,
    ghost: 'hover:bg-accent hover:text-accent-foreground',
    link: 'text-primary underline-offset-4 hover:underline',
  },

  // Inputs
  input: {
    base: `${BORDERS.input} ${SPACING.padding.inputField} w-full ${COLORS.focus}`,
    error: 'border-destructive focus-visible:ring-destructive',
  },

  // Cards - Deprecated
  // Use o componente Card de @/components/ui/card em vez destes estilos

  // Feedback
  feedback: {
    error: `${COLORS.intent.destructive.light} ${BORDERS.radius.md} ${SPACING.padding.sm}`,
    success: `${COLORS.intent.success.light} ${BORDERS.radius.md} ${SPACING.padding.sm}`,
    warning: `${COLORS.intent.warning.light} ${BORDERS.radius.md} ${SPACING.padding.sm}`,
    info: `${COLORS.intent.primary.light} ${BORDERS.radius.md} ${SPACING.padding.sm}`,
  },

  // Navegação
  nav: {
    item: `${SPACING.padding.sm} ${BORDERS.radius.md} ${ANIMATIONS.transition.fast} hover:bg-accent hover:text-accent-foreground`,
    active: `${SPACING.padding.sm} ${BORDERS.radius.md} bg-accent text-accent-foreground`,
  },
};
