export type Locale = 'pt-BR' | 'en-US';

// Configuração do i18n para a aplicação
export const i18nConfig = {
  defaultLocale: 'pt-BR' as Locale,
  locales: ['pt-BR', 'en-US'] as Locale[],
  domains: [
    {
      domain: 'excelcopilot.com.br',
      defaultLocale: 'pt-BR' as Locale,
    },
    {
      domain: 'excelcopilot.com',
      defaultLocale: 'en-US' as Locale,
    },
  ],
};

// Interface para definir a estrutura das mensagens de tradução
export interface Messages {
  common: {
    appName: string;
    poweredBy: string;
    loading: string;
    error: string;
    success: string;
  };
  nav: {
    home: string;
    dashboard: string;
    templates: string;
  };
  home: {
    title: string;
    subtitle: string;
    startNow: string;
    learnHow: string;
    recentWorkbooks: string;
    continueFrom: string;
    viewAll: string;
    askExcelCopilot: string;
    whyChoose: string;
    features: {
      intelligence: {
        title: string;
        description: string;
      };
      integration: {
        title: string;
        description: string;
      };
      zeroCode: {
        title: string;
        description: string;
      };
    };
  };
  chat: {
    title: string;
    subtitle: string;
    placeholder: string;
    send: string;
    examples: {
      createChart: string;
      calculate: string;
      filter: string;
    };
    emptyState: string;
    operationsExecuted: string;
  };
  upload: {
    title: string;
    subtitle: string;
    dragDrop: string;
    browse: string;
    orDivider: string;
  };
  theme: {
    toggle: string;
    light: string;
    dark: string;
    system: string;
  };
  footer: {
    tagline: string;
    terms: string;
    privacy: string;
    contact: string;
  };
  errors: {
    generic: string;
    network: string;
    auth: string;
    notFound: string;
    permission: string;
  };
}

const pt_BR: Messages = {
  common: {
    appName: 'Excel Copilot',
    poweredBy: 'Potencializado por IA',
    loading: 'Carregando...',
    error: 'Ocorreu um erro',
    success: 'Sucesso',
  },
  nav: {
    home: 'Início',
    dashboard: 'Painel',
    templates: 'Modelos',
  },
  home: {
    title: 'Excel Copilot',
    subtitle:
      'Transforme sua experiência com planilhas através de comandos em linguagem natural. O assistente inteligente que entende o que você precisa.',
    startNow: 'Iniciar agora',
    learnHow: 'Aprenda como usar',
    recentWorkbooks: 'Planilhas Recentes',
    continueFrom: 'Continue de onde parou',
    viewAll: 'Ver todas',
    askExcelCopilot: 'Pergunta para o Excel Copilot:',
    whyChoose: 'Por que escolher Excel Copilot?',
    features: {
      intelligence: {
        title: 'Inteligência Avançada',
        description: 'Entende comandos complexos e cria fórmulas automaticamente para você',
      },
      integration: {
        title: 'Integração Perfeita',
        description: 'Trabalha com seus arquivos Excel locais e na nuvem',
      },
      zeroCode: {
        title: 'Código Zero',
        description: 'Não precisa saber programação ou fórmulas complexas para análises avançadas',
      },
    },
  },
  chat: {
    title: 'Converse com sua planilha',
    subtitle: 'Use linguagem natural para analisar e manipular seus dados',
    placeholder: 'Digite um comando ou faça uma pergunta...',
    send: 'Enviar',
    examples: {
      createChart: 'Crie um gráfico de barras com vendas por região dos últimos 6 meses',
      calculate: 'Calcule a média da coluna B',
      filter: 'Filtre os dados onde o valor é maior que 1000',
    },
    emptyState: 'Inicie a conversa com um comando ou pergunta',
    operationsExecuted: 'Ações executadas:',
  },
  upload: {
    title: 'Comece com uma planilha',
    subtitle: 'Faça upload de uma planilha existente ou use um de nossos modelos',
    dragDrop: 'Arraste e solte arquivos aqui',
    browse: 'Buscar arquivos',
    orDivider: 'ou',
  },
  theme: {
    toggle: 'Mudar tema',
    light: 'Claro',
    dark: 'Escuro',
    system: 'Sistema',
  },
  footer: {
    tagline: 'Excel Copilot — Revolucionando a análise de dados com IA',
    terms: 'Termos',
    privacy: 'Privacidade',
    contact: 'Contato',
  },
  errors: {
    generic: 'Algo deu errado. Tente novamente mais tarde.',
    network: 'Erro de conexão. Verifique sua internet.',
    auth: 'Erro de autenticação. Faça login novamente.',
    notFound: 'Recurso não encontrado.',
    permission: 'Você não tem permissão para esta ação.',
  },
};

const en_US: Messages = {
  common: {
    appName: 'Excel Copilot',
    poweredBy: 'Powered by AI',
    loading: 'Loading...',
    error: 'An error occurred',
    success: 'Success',
  },
  nav: {
    home: 'Home',
    dashboard: 'Dashboard',
    templates: 'Templates',
  },
  home: {
    title: 'Excel Copilot',
    subtitle:
      'Transform your spreadsheet experience with natural language commands. The intelligent assistant that understands what you need.',
    startNow: 'Start now',
    learnHow: 'Learn how to use',
    recentWorkbooks: 'Recent Workbooks',
    continueFrom: 'Continue where you left off',
    viewAll: 'View all',
    askExcelCopilot: 'Ask Excel Copilot:',
    whyChoose: 'Why choose Excel Copilot?',
    features: {
      intelligence: {
        title: 'Advanced Intelligence',
        description: 'Understands complex commands and creates formulas automatically for you',
      },
      integration: {
        title: 'Seamless Integration',
        description: 'Works with your local and cloud Excel files',
      },
      zeroCode: {
        title: 'Zero Code',
        description: 'No need to know programming or complex formulas for advanced analysis',
      },
    },
  },
  chat: {
    title: 'Talk to your spreadsheet',
    subtitle: 'Use natural language to analyze and manipulate your data',
    placeholder: 'Type a command or ask a question...',
    send: 'Send',
    examples: {
      createChart: 'Create a bar chart with sales by region for the last 6 months',
      calculate: 'Calculate the average of column B',
      filter: 'Filter data where the value is greater than 1000',
    },
    emptyState: 'Start the conversation with a command or question',
    operationsExecuted: 'Operations executed:',
  },
  upload: {
    title: 'Start with a spreadsheet',
    subtitle: 'Upload an existing spreadsheet or use one of our templates',
    dragDrop: 'Drag and drop files here',
    browse: 'Browse files',
    orDivider: 'or',
  },
  theme: {
    toggle: 'Toggle theme',
    light: 'Light',
    dark: 'Dark',
    system: 'System',
  },
  footer: {
    tagline: 'Excel Copilot — Revolutionizing data analysis with AI',
    terms: 'Terms',
    privacy: 'Privacy',
    contact: 'Contact',
  },
  errors: {
    generic: 'Something went wrong. Please try again later.',
    network: 'Connection error. Check your internet.',
    auth: 'Authentication error. Please log in again.',
    notFound: 'Resource not found.',
    permission: "You don't have permission for this action.",
  },
};

// Mapeamento de locales para mensagens
const messages: Record<Locale, Messages> = {
  'pt-BR': pt_BR,
  'en-US': en_US,
};

// Hook de internacionalização
export const getMessages = (_locale: Locale = 'pt-BR'): Messages => {
  // Sempre retorna pt-BR para garantir que a interface esteja em português
  return messages['pt-BR'];
};
