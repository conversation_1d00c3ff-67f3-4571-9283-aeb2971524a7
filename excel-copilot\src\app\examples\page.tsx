import { WorkbookTemplatesServer } from '@/components/workbook-templates';

export default function TemplatesPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="text-center mb-10">
        <h1 className="text-3xl font-bold tracking-tight mb-4">Modelos e Exemplos</h1>
        <p className="text-muted-foreground max-w-2xl mx-auto">
          Comece rapidamente com nossos modelos e veja exemplos de comandos poderosos.
        </p>
      </div>

      <section className="py-8">
        <h2 className="text-2xl font-bold mb-6">Modelos Prontos</h2>
        <WorkbookTemplatesServer />
      </section>

      <section className="py-8">
        <h2 className="text-2xl font-bold mb-6">Exemplos de Comandos</h2>
        <div className="bg-card p-6 rounded-lg border shadow-sm">
          <p className="mb-4">Confira exemplos de comandos que você pode usar com nossa IA:</p>
          <ul className="list-disc pl-6 space-y-2">
            <li>Crie uma planilha de controle financeiro com categorias de gastos</li>
            <li>Gere um gráfico de barras com os dados das colunas A e B</li>
            <li>Some os valores da coluna Vendas</li>
            <li>Filtre os dados onde Preço é maior que 100</li>
            <li>Converta a planilha atual para formato de tabela</li>
            <li>Crie uma tabela dinâmica agrupando vendas por região</li>
            <li>Faça uma análise de correlação entre preço e demanda</li>
          </ul>
        </div>
      </section>
    </div>
  );
}
