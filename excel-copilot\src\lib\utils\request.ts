import { NextApiRequest } from 'next';

/**
 * Extrai o endereço IP do cliente a partir da requisição
 * Considera cabeçalhos de proxy como X-Forwarded-For
 */
export function getIpAddress(req: NextApiRequest): string | null {
  try {
    // Tentar obter IP do cabeçalho X-Forwarded-For (usado por muitos proxies)
    const forwarded = req.headers['x-forwarded-for'] as string;
    if (forwarded) {
      // O formato pode ser "client, proxy1, proxy2"
      // Pegamos o primeiro IP (do cliente)
      return safeArrayAccess(forwarded.split(','), 0)?.trim() || null;
    }

    // Tentar outras alternativas comuns
    const realIp = req.headers['x-real-ip'] as string;
    if (realIp) {
      return realIp;
    }

    // Usar o IP da conexão como último recurso
    return req.socket.remoteAddress || null;
  } catch (error) {
    console.error('Erro ao obter IP do cliente:', error);
    return null;
  }
}

// Implementação local para não depender de imports
export function safeArrayAccess<T>(arr: T[] | undefined | null, index: number): T | undefined {
  if (!arr || !Array.isArray(arr) || index < 0 || index >= arr.length) {
    return undefined;
  }
  return arr[index];
}

// Métodos de utilitário para chamadas de API
export const getApiBaseUrl = (): string => {
  // Função simplificada para obter a URL base da API
  return process.env.NEXT_PUBLIC_API_URL || '';
};
