/**
 * Implementações das funções utilitárias para tipos opcionais
 * Este arquivo contém as implementações das funções declaradas em optional-types.d.ts
 */

import type { NullableString } from '@/types/optional-types';

/**
 * Converte undefined para null em strings opcionais para compatibilidade com Prisma
 */
export function toNullableString(value: string | undefined): NullableString {
  return value === undefined ? null : value;
}

/**
 * Acesso seguro a um elemento de array por índice
 * @param arr O array para acessar
 * @param index O índice a acessar
 * @returns O elemento no índice ou undefined se o índice for inválido
 */
export function safeArrayAccess<T>(arr: T[] | undefined | null, index: number): T | undefined {
  if (!arr || !Array.isArray(arr) || index < 0 || index >= arr.length) {
    return undefined;
  }
  return arr[index];
}

/**
 * Acesso seguro a uma propriedade de objeto
 * @param obj O objeto para acessar
 * @param key A chave a acessar
 * @returns O valor da propriedade ou undefined se a chave não existir
 */
export function safeObjectAccess<T = any>(
  obj: Record<string, T> | undefined | null,
  key: string
): T | undefined {
  if (!obj || typeof obj !== 'object' || obj === null) {
    return undefined;
  }
  return obj[key];
}

/**
 * Acesso seguro a uma propriedade aninhada de objeto
 * @param obj O objeto para acessar
 * @param path Caminho de propriedades separado por pontos (ex: "user.address.street")
 * @returns O valor da propriedade aninhada ou undefined se qualquer parte do caminho não existir
 */
export function safeNestedAccess<T = any>(
  obj: Record<string, any> | undefined | null,
  path: string
): T | undefined {
  if (!obj || typeof obj !== 'object' || obj === null || !path) {
    return undefined;
  }

  const parts = path.split('.');
  let current: any = obj;

  for (const part of parts) {
    if (current === null || typeof current !== 'object' || !(part in current)) {
      return undefined;
    }
    current = current[part];
  }

  return current as T;
}

/**
 * Verifica se um valor é undefined ou null
 * @param value O valor a verificar
 * @returns true se o valor for undefined ou null
 */
export function isNullOrUndefined(value: unknown): value is null | undefined {
  return value === null || value === undefined;
}

/**
 * Fornece um valor padrão se o valor original for null ou undefined
 * @param value O valor original
 * @param defaultValue O valor padrão a retornar
 * @returns O valor original ou o valor padrão
 */
export function withDefault<T>(value: T | null | undefined, defaultValue: T): T {
  return isNullOrUndefined(value) ? defaultValue : value;
}
