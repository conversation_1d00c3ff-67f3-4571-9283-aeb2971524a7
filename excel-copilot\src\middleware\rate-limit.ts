import { NextRequest, NextResponse } from 'next/server';

import { logger } from '@/lib/logger';
import { ApiResponse } from '@/utils/api-response';

// Armazenamento simples em memória (em produção use Redis ou serviço externo)
const ipRequests = new Map<string, { count: number; resetAt: number }>();

/**
 * Limpa entradas expiradas do mapa de controle de taxa
 */
function cleanupExpiredEntries() {
  const now = Date.now();
  for (const [key, value] of ipRequests.entries()) {
    if (now > value.resetAt) {
      ipRequests.delete(key);
    }
  }
}

// Executar limpeza a cada 5 minutos
if (typeof setInterval !== 'undefined') {
  setInterval(cleanupExpiredEntries, 5 * 60 * 1000);
}

/**
 * Cria um middleware para limitação de taxa (rate limiting)
 * @param maxRequests Número máximo de requisições permitidas na janela de tempo
 * @param windowMs Janela de tempo em milissegundos
 * @param keyGenerator Função para gerar a chave de identificação (padrão: IP)
 * @returns Middleware de rate limiting
 */
export function createRateLimiter(
  maxRequests = 100,
  windowMs = 60 * 1000, // 1 minuto
  keyGenerator = (req: NextRequest) => req.ip || 'unknown'
) {
  return async (req: NextRequest, res: NextResponse) => {
    const key = keyGenerator(req);
    const now = Date.now();

    // Limpar entradas expiradas periodicamente (1% de chance para não impactar performance)
    if (Math.random() < 0.01) {
      cleanupExpiredEntries();
    }

    // Obter ou criar entrada para este IP/chave
    let record = ipRequests.get(key);
    if (!record || now > record.resetAt) {
      record = {
        count: 0,
        resetAt: now + windowMs,
      };
    }

    // Incrementar contador
    record.count += 1;
    ipRequests.set(key, record);

    // Adicionar headers com informações de rate limit
    res.headers.set('X-RateLimit-Limit', maxRequests.toString());
    res.headers.set('X-RateLimit-Remaining', Math.max(0, maxRequests - record.count).toString());
    res.headers.set('X-RateLimit-Reset', Math.ceil(record.resetAt / 1000).toString());

    // Verificar limite
    if (record.count > maxRequests) {
      logger.warn(`Rate limit excedido para ${key}`, {
        path: req.nextUrl.pathname,
        method: req.method,
        key,
        count: record.count,
        limit: maxRequests,
      });

      // Calcular segundos até reset
      const retryAfterSeconds = Math.ceil((record.resetAt - now) / 1000);

      return ApiResponse.tooManyRequests(
        'Muitas requisições. Tente novamente mais tarde.',
        retryAfterSeconds
      );
    }

    // Continuar para o próximo middleware ou handler
  };
}

// Rate limiters pré-configurados
export const apiRateLimiter = createRateLimiter(100, 60 * 1000); // 100 reqs/min para API geral
export const authRateLimiter = createRateLimiter(20, 60 * 1000); // 20 reqs/min para auth
export const aiRateLimiter = createRateLimiter(30, 60 * 1000); // 30 reqs/min para API de IA
