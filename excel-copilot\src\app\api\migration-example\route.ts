/**
 * Exemplo de migração de uma rota do Pages Router para o App Router
 *
 * Este arquivo demonstra três abordagens para migrar rotas:
 * 1. Implementação manual usando a nova API
 * 2. Uso do utilitário de migração automatizada
 * 3. Implementação híbrida
 */

import { NextRequest, NextResponse } from 'next/server';

// Tipos para compatibilidade com handler legado
interface LegacyRequest {
  method?: string;
  body?: unknown;
}

interface LegacyResponse {
  status: (code: number) => LegacyResponse;
  json: (data: unknown) => unknown;
}

// Simulação de um handler do Pages Router
// Em um cenário real, você importaria o handler de pages/api/
const _legacyHandler = async (req: LegacyRequest, res: LegacyResponse) => {
  if (req.method === 'GET') {
    return res.status(200).json({
      message: 'Dados obtidos com sucesso',
      approach: 'legacy',
      timestamp: new Date().toISOString(),
    });
  }

  if (req.method === 'POST') {
    const data = req.body;
    return res.status(201).json({
      message: 'Recurso criado com sucesso',
      data,
      approach: 'legacy',
      timestamp: new Date().toISOString(),
    });
  }

  return res.status(405).json({ error: 'Método não permitido' });
};

// Abordagem 1: Implementação manual no novo formato
export async function GET(_request: NextRequest) {
  // Implementação nativa do App Router
  return NextResponse.json({
    message: 'Dados obtidos com sucesso',
    approach: 'manual app router',
    timestamp: new Date().toISOString(),
  });
}

// Abordagem 2: Usando o utilitário de migração automática
// Descomente para usar esta abordagem (substitui a implementação manual acima)
/*
export const { GET, POST } = createAppRouteHandlers(legacyHandler);
*/

// Abordagem 3: Implementação híbrida - manual para alguns métodos, automatizada para outros
// Descomente para usar esta abordagem
/*
// POST automatizado a partir do handler legado
export const { POST } = createAppRouteHandlers({
  POST: legacyHandler
});

// GET implementado manualmente no novo formato
export async function GET(request: NextRequest) {
  return NextResponse.json({
    message: 'Dados obtidos com sucesso',
    approach: 'hybrid',
    timestamp: new Date().toISOString()
  });
}
*/
