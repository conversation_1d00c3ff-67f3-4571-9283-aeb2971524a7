#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Função para contar linhas de código
function countLines(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n');
    const totalLines = lines.length;
    
    // Contar linhas de código real (excluindo comentários e linhas vazias)
    let codeLines = 0;
    let commentLines = 0;
    let blankLines = 0;
    
    let inBlockComment = false;
    
    for (const line of lines) {
      const trimmed = line.trim();
      
      if (trimmed === '') {
        blankLines++;
      } else if (trimmed.startsWith('//') || trimmed.startsWith('*') || trimmed.startsWith('/*')) {
        commentLines++;
      } else if (trimmed.includes('/*')) {
        inBlockComment = true;
        commentLines++;
      } else if (trimmed.includes('*/')) {
        inBlockComment = false;
        commentLines++;
      } else if (inBlockComment) {
        commentLines++;
      } else {
        codeLines++;
      }
    }
    
    return {
      total: totalLines,
      code: codeLines,
      comments: commentLines,
      blank: blankLines
    };
  } catch (error) {
    return { total: 0, code: 0, comments: 0, blank: 0 };
  }
}

// Função para determinar complexidade baseada no tamanho do arquivo
function getComplexity(codeLines) {
  if (codeLines < 50) return 'BAIXA';
  if (codeLines < 150) return 'MÉDIA';
  if (codeLines < 300) return 'ALTA';
  return 'MUITO ALTA';
}

// Função para determinar importância do arquivo
function getImportance(filePath, codeLines) {
  const fileName = path.basename(filePath);
  const dirName = path.dirname(filePath);
  
  // CRÍTICO - Funcionalidade principal
  if (filePath.includes('layout.tsx') || 
      filePath.includes('page.tsx') ||
      filePath.includes('SpreadsheetEditor') ||
      filePath.includes('chat-interface') ||
      filePath.includes('dashboard/page') ||
      filePath.includes('workbook/[id]')) {
    return 'CRÍTICO';
  }
  
  // ALTO - Componentes importantes
  if (filePath.includes('components/ui/') ||
      filePath.includes('components/dashboard/') ||
      filePath.includes('components/workbook/') ||
      filePath.includes('hooks/') ||
      filePath.includes('lib/') ||
      codeLines > 200) {
    return 'ALTO';
  }
  
  // MÉDIO - Componentes de suporte
  if (filePath.includes('components/') ||
      filePath.includes('utils/') ||
      filePath.includes('types/') ||
      codeLines > 100) {
    return 'MÉDIO';
  }
  
  // BAIXO - Utilitários menores
  return 'BAIXO';
}

// Função para categorizar arquivos
function categorizeFile(filePath) {
  // Normalizar separadores de caminho
  const normalizedPath = filePath.replace(/\\/g, '/');

  if (normalizedPath.includes('/app/') && normalizedPath.endsWith('page.tsx')) return 'PÁGINAS';
  if (normalizedPath.includes('/app/layout.tsx')) return 'LAYOUT';
  if (normalizedPath.includes('/components/ui/')) return 'UI_COMPONENTS';
  if (normalizedPath.includes('/components/dashboard/')) return 'DASHBOARD_COMPONENTS';
  if (normalizedPath.includes('/components/workbook/')) return 'WORKBOOK_COMPONENTS';
  if (normalizedPath.includes('/components/chat-interface/')) return 'CHAT_COMPONENTS';
  if (normalizedPath.includes('/components/')) return 'COMPONENTS';
  if (normalizedPath.includes('/hooks/')) return 'HOOKS';
  if (normalizedPath.includes('/lib/')) return 'LIBRARIES';
  if (normalizedPath.includes('/utils/')) return 'UTILITIES';
  if (normalizedPath.includes('/types/')) return 'TYPES';
  if (normalizedPath.includes('/styles/')) return 'STYLES';
  if (normalizedPath.includes('/config/')) return 'CONFIG';
  return 'OTHER';
}

// Função principal para analisar arquivos
function analyzeFiles() {
  const srcDir = './src';
  const results = [];
  
  function walkDir(dir) {
    const files = fs.readdirSync(dir);
    
    for (const file of files) {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);
      
      if (stat.isDirectory()) {
        // Pular diretórios de API backend e testes
        if (!file.includes('api') && !file.includes('__tests__')) {
          walkDir(filePath);
        }
      } else if (file.match(/\.(tsx?|css)$/)) {
        const lines = countLines(filePath);
        const complexity = getComplexity(lines.code);
        const importance = getImportance(filePath, lines.code);
        const category = categorizeFile(filePath);
        
        results.push({
          path: filePath.replace('./src/', '').replace(/\\/g, '/'),
          category,
          importance,
          complexity,
          lines
        });
      }
    }
  }
  
  walkDir(srcDir);
  return results;
}

// Executar análise
console.log('🔍 Analisando estrutura frontend do Excel Copilot...\n');

const results = analyzeFiles();

// Ordenar por importância e depois por linhas de código
results.sort((a, b) => {
  const importanceOrder = { 'CRÍTICO': 4, 'ALTO': 3, 'MÉDIO': 2, 'BAIXO': 1 };
  const importanceDiff = importanceOrder[b.importance] - importanceOrder[a.importance];
  if (importanceDiff !== 0) return importanceDiff;
  return b.lines.code - a.lines.code;
});

// Gerar relatório
console.log('📊 RELATÓRIO DE ANÁLISE FRONTEND\n');
console.log('='.repeat(80));

// Estatísticas gerais
const totalFiles = results.length;
const totalLines = results.reduce((sum, file) => sum + file.lines.total, 0);
const totalCodeLines = results.reduce((sum, file) => sum + file.lines.code, 0);

console.log(`\n📈 ESTATÍSTICAS GERAIS:`);
console.log(`Total de arquivos frontend: ${totalFiles}`);
console.log(`Total de linhas: ${totalLines.toLocaleString()}`);
console.log(`Linhas de código: ${totalCodeLines.toLocaleString()}`);
console.log(`Média de linhas por arquivo: ${Math.round(totalCodeLines / totalFiles)}`);

// Distribuição por categoria
const categories = {};
results.forEach(file => {
  if (!categories[file.category]) {
    categories[file.category] = { count: 0, lines: 0 };
  }
  categories[file.category].count++;
  categories[file.category].lines += file.lines.code;
});

console.log(`\n📁 DISTRIBUIÇÃO POR CATEGORIA:`);
Object.entries(categories)
  .sort((a, b) => b[1].lines - a[1].lines)
  .forEach(([category, data]) => {
    console.log(`${category.padEnd(20)} | ${data.count.toString().padStart(3)} arquivos | ${data.lines.toString().padStart(6)} linhas`);
  });

// Distribuição por importância
const importance = {};
results.forEach(file => {
  if (!importance[file.importance]) {
    importance[file.importance] = { count: 0, lines: 0 };
  }
  importance[file.importance].count++;
  importance[file.importance].lines += file.lines.code;
});

console.log(`\n⭐ DISTRIBUIÇÃO POR IMPORTÂNCIA:`);
Object.entries(importance)
  .sort((a, b) => {
    const order = { 'CRÍTICO': 4, 'ALTO': 3, 'MÉDIO': 2, 'BAIXO': 1 };
    return order[b[0]] - order[a[0]];
  })
  .forEach(([level, data]) => {
    console.log(`${level.padEnd(8)} | ${data.count.toString().padStart(3)} arquivos | ${data.lines.toString().padStart(6)} linhas`);
  });

console.log(`\n📋 ARQUIVOS MAIS IMPORTANTES (Top 20):`);
console.log('-'.repeat(80));
console.log('ARQUIVO'.padEnd(50) + 'IMP'.padEnd(8) + 'COMP'.padEnd(12) + 'LINHAS');
console.log('-'.repeat(80));

results.slice(0, 20).forEach(file => {
  const fileName = file.path.length > 48 ? '...' + file.path.slice(-45) : file.path;
  console.log(
    fileName.padEnd(50) + 
    file.importance.padEnd(8) + 
    file.complexity.padEnd(12) + 
    file.lines.code.toString().padStart(6)
  );
});

// Salvar relatório detalhado em JSON
const reportData = {
  summary: {
    totalFiles,
    totalLines,
    totalCodeLines,
    averageLinesPerFile: Math.round(totalCodeLines / totalFiles)
  },
  categories,
  importance,
  files: results
};

fs.writeFileSync('frontend-analysis-report.json', JSON.stringify(reportData, null, 2));
console.log(`\n💾 Relatório detalhado salvo em: frontend-analysis-report.json`);
