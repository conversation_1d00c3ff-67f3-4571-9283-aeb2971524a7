# Guia de Utilização de Utilitários Tipados

Este guia explica como utilizar os novos utilitários criados para resolver problemas comuns de tipagem no projeto Excel Copilot.

## Problema: Erros de Tipagem com `unknown`

Um dos problemas mais comuns no projeto é o uso de variáveis do tipo `unknown` como argumentos para funções que esperam tipos específicos:

```typescript
// ❌ ERRADO - Causa erro de tipagem
logger.error('Mensagem de erro', errorObject);
// Erro: Argument of type 'unknown' is not assignable to parameter of type 'Error | undefined'
```

## Solução: Utilitários de Log Tipados

Os utilitários de log fornecem funções wrapper para o sistema de logging que lidam adequadamente com valores `unknown`:

```typescript
// ✅ CORRETO - Usa os utilitários para conversão de tipo
import { logError } from '@/utils';

try {
  // código que pode falhar
} catch (error) {
  logError('Mensagem de erro', error); // Tipagem segura
}
```

## Problema: Propriedades Personalizadas em Erros

Outro problema comum é a tentativa de adicionar propriedades contextuais a objetos `Error`, o que viola a tipagem estrita:

```typescript
// ❌ ERRADO - Property 'userId' does not exist on type 'Error'
error.userId = 'user-123';
```

## Solução: Classe `EnhancedError` e Funções Auxiliares

Use a classe `EnhancedError` ou as funções de criação de erro para adicionar contexto:

```typescript
// ✅ CORRETO - Usa erro tipado com contexto
import { createApiError } from '@/utils';

const error = createApiError('Falha na operação', 'API_ERROR', {
  userId: 'user-123',
  requestId: 'req-456',
});

// O contexto está disponível de forma segura
console.log(error.context.userId); // 'user-123'
```

## Problema: Acesso Inseguro a Arrays e Objetos

Problemas com acesso a índices inexistentes ou propriedades undefined:

```typescript
// ❌ ERRADO - Pode causar erro em runtime
const value = data[rowIndex][colIndex];
```

## Solução: Funções de Acesso Seguro

```typescript
// ✅ CORRETO - Acesso seguro com valor padrão
import { getCellValue } from '@/utils';

const value = getCellValue(data, rowIndex, colIndex, '');
// Retorna o valor ou '' se não existir
```

## Problema: Operações em Ranges do Excel

Manipulação de coordenadas de células e ranges sem tipagem adequada:

```typescript
// ❌ ERRADO - Parsing manual propenso a erros
const match = cellRef.match(/([A-Z]+)(\d+)/);
const col = match[1];
const row = parseInt(match[2], 10);
```

## Solução: Utilitários de Excel

```typescript
// ✅ CORRETO - Parsing seguro com tipagem
import { parseCellReference, parseRange } from '@/utils';

const { row, col } = parseCellReference('A1');
// Retorna { row: 0, col: 0 } com tipagem correta

const range = parseRange('A1:C3');
// Retorna coordenadas tipadas do range
```

## Como Implementar nos Arquivos Existentes

Para corrigir os erros de tipagem nos arquivos existentes, siga estes passos:

1. **Substitua chamadas diretas ao logger** por seus equivalentes tipados:

   ```typescript
   // Antes
   logger.error('Erro', error);

   // Depois
   import { logError } from '@/utils';
   logError('Erro', error);
   ```

2. **Substitua manipulação manual de erros** por funções de erro tipadas:

   ```typescript
   // Antes
   throw new Error(`Falha na API: ${errorMessage}`);

   // Depois
   import { createApiError } from '@/utils';
   throw createApiError(`Falha na API`, 'API_ERROR', { details: errorMessage });
   ```

3. **Use funções de acesso seguro** para arrays e objetos:

   ```typescript
   // Antes
   const part = parts[0];

   // Depois
   import { safeArrayAccess } from '@/utils';
   const part = safeArrayAccess(parts, 0) || '';
   ```

4. **Use utilitários de Excel** para operações com células e ranges:

   ```typescript
   // Antes
   const cellValue = data.rows[rowIndex][colIndex];

   // Depois
   import { getCellValue } from '@/utils';
   const cellValue = getCellValue(data.rows, rowIndex, colIndex, '');
   ```

## Lista de Utilitários Disponíveis

### Utilitários de Log

- `logInfo(message, metadata?)` - Versão tipada de logger.info
- `logWarn(message, metadata?)` - Versão tipada de logger.warn
- `logError(message, error?)` - Versão tipada de logger.error
- `logDebug(message, metadata?)` - Versão tipada de logger.debug
- `logFatal(message, error?)` - Versão tipada de logger.fatal

### Utilitários de Erro

- `createApiError(message, code, context?)` - Cria erro para APIs
- `createDatabaseError(message, originalError?, context?)` - Cria erro para BD
- `createExcelError(message, code?, context?)` - Cria erro para Excel
- `normalizeError(error, defaultMessage?)` - Converte qualquer erro para EnhancedError
- `extractErrorInfo(error)` - Extrai informações úteis de erros

### Utilitários de Excel

- `parseCellReference(cellRef)` - Converte "A1" para { row: 0, col: 0 }
- `parseRange(range)` - Converte "A1:C3" para coordenadas do range
- `getCellValue(matrix, row, col, defaultValue?)` - Acesso seguro a células
- `setCellValue(matrix, row, col, value)` - Define valor de célula com segurança
- `getRangeValues(matrix, range)` - Extrai valores de um range
- `hasCellReference(text)` - Verifica se um texto contém referência de célula
- `extractCellReference(text)` - Extrai primeira referência de célula em texto

## Exemplo de Uso

```typescript
import { logError, createExcelError, parseCellReference, getCellValue } from '@/utils';

function processCell(data: any[][], cellRef: string) {
  try {
    // Parsing seguro da referência
    const coords = parseCellReference(cellRef);

    // Acesso seguro à célula
    const value = getCellValue(data, coords.row, coords.col);

    return value;
  } catch (error) {
    // Erro tipado com contexto
    const excelError = createExcelError('Erro ao processar célula', 'CELL_PROCESSING_ERROR', {
      cellRef,
    });

    // Log tipado
    logError('Falha ao processar célula', excelError);
    return null;
  }
}
```
