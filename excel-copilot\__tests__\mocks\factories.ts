/**
 * Factories para criar dados de teste
 *
 * Este arquivo contém funções factory para criar objetos de teste
 * com dados aleatórios ou padronizados que podem ser usados em testes.
 */

import { ApiResult } from './types';

/**
 * Gera um ID aleatório para uso em testes
 */
const generateRandomId = (prefix = '') => `${prefix}${Math.random().toString(36).slice(2, 10)}`;

/**
 * Factory para criar um workbook de teste
 */
export const createMockWorkbook = (overrides = {}) => ({
  id: generateRandomId('wb-'),
  name: '<PERSON><PERSON><PERSON> de Teste',
  description: 'Descrição da planilha de teste',
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  sheets: [
    { id: 'sheet-1', name: 'Planilha 1' },
    { id: 'sheet-2', name: 'Planilha 2' },
  ],
  ...overrides,
});

/**
 * Factory para criar dados de planilha
 */
export const createMockSheetData = (rows = 5, cols = 3, withHeaders = true) => {
  const headers = ['Coluna A', 'Coluna B', 'Coluna C'].slice(0, cols);
  const data = [];

  if (withHeaders) {
    data.push([...headers]);
  }

  for (let i = 0; i < rows; i++) {
    const row = [];
    for (let j = 0; j < cols; j++) {
      // Gerar diferentes tipos de dados para as colunas
      if (j === 0) {
        row.push(`Item ${i + 1}`); // Texto
      } else if (j === 1) {
        row.push(Math.floor(Math.random() * 1000)); // Número
      } else {
        const date = new Date();
        date.setDate(date.getDate() - i);
        row.push(date.toISOString().split('T')[0]); // Data
      }
    }
    data.push(row);
  }

  return data;
};

/**
 * Factory para criar um usuário de teste
 */
export const createMockUser = (overrides = {}) => ({
  id: generateRandomId('user-'),
  name: 'Usuário Teste',
  email: '<EMAIL>',
  role: 'user',
  ...overrides,
});

/**
 * Factory para criar uma mensagem de chat
 */
export const createMockChatMessage = (role = 'user', content = '', overrides = {}) => ({
  id: generateRandomId('msg-'),
  role: role,
  content: content || `Mensagem de teste do ${role}`,
  timestamp: new Date().toISOString(),
  ...overrides,
});

/**
 * Factory para criar uma conversa de chat
 */
export const createMockChatHistory = (messageCount = 3, overrides = {}) => {
  const messages = [];

  for (let i = 0; i < messageCount; i++) {
    // Alternar entre mensagens do usuário e do assistente
    const role = i % 2 === 0 ? 'user' : 'assistant';
    messages.push(createMockChatMessage(role));
  }

  return {
    id: generateRandomId('chat-'),
    workbookId: generateRandomId('wb-'),
    messages,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    ...overrides,
  };
};

/**
 * Factory para criar uma resposta de API genérica
 */
export const createApiResponse = <T>(data: T, success = true, message?: string): ApiResult<T> => ({
  success,
  data,
  message: message || (success ? 'Operação realizada com sucesso' : 'Erro ao realizar operação'),
});

/**
 * Factory para criar dados para gráficos
 */
export const createMockChartData = (labels = 5, datasets = 1) => {
  const chartLabels = Array.from({ length: labels }, (_, i) => `Categoria ${i + 1}`);
  const chartDatasets = [];

  for (let i = 0; i < datasets; i++) {
    chartDatasets.push({
      label: `Série ${i + 1}`,
      data: Array.from({ length: labels }, () => Math.floor(Math.random() * 1000)),
      backgroundColor: `rgba(${Math.floor(Math.random() * 255)}, ${Math.floor(Math.random() * 255)}, ${Math.floor(Math.random() * 255)}, 0.5)`,
      borderColor: `rgba(${Math.floor(Math.random() * 255)}, ${Math.floor(Math.random() * 255)}, ${Math.floor(Math.random() * 255)}, 1)`,
    });
  }

  return {
    labels: chartLabels,
    datasets: chartDatasets,
  };
};
