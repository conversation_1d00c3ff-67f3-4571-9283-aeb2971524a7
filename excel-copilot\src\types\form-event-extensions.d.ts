/**
 * Extensões de tipos para eventos de formulário
 * Este arquivo adiciona suporte para misturar InputEvent e TextareaEvent
 */

// Reexportar tipos
import { FormInputEvent, FormTextareaEvent } from './form-events';

// Adicionar compatibilidade para interfaces de componentes do React
declare module 'react' {
  interface TextareaHTMLAttributes<T> {
    onChange?: ((e: FormInputEvent | FormTextareaEvent) => void) | undefined;
  }

  interface InputHTMLAttributes<T> {
    onChange?: ((e: FormInputEvent | FormTextareaEvent) => void) | undefined;
  }
}

// Utilidades para converter tipos de evento
export function asInputEvent(e: any): FormInputEvent {
  return e as FormInputEvent;
}

export function asTextareaEvent(e: any): FormTextareaEvent {
  return e as FormTextareaEvent;
}

// Helper type para uso universal de onChange
export type UniversalChangeHandler = (e: FormInputEvent | FormTextareaEvent) => void;
