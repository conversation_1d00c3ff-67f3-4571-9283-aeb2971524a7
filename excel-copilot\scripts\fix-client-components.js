#!/usr/bin/env node

/**
 * Script para verificar e corrigir problemas comuns em componentes do lado do cliente
 * Esta correção é necessária para resolver problemas de manipulação de eventos em componentes cliente
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

// Cores para mensagens de terminal
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
};

// Lista de problemas comuns a serem verificados
const issues = [
  {
    pattern: /['"]use client['"]\s*;?\s*[\s\S]*?export\s+const\s+metadata/,
    solution: 'Mover a exportação de metadata para um arquivo layout.tsx server-side',
    type: 'error',
    info: 'Erro comum de exportação de metadata em componente client-side',
  },
  {
    pattern: /useEffect\(\s*\(\)\s*=>\s*{\s*document\./,
    solution: 'Adicionar verificação typeof window !== "undefined" antes de acessar document',
    type: 'warning',
    info: 'Possível erro de acesso ao objeto document no lado do servidor',
  },
  {
    pattern: /"use client"[\s\S]*?export\s+async\s+function/,
    solution:
      'Remover async de funções exportadas em componentes client ou mover para um arquivo separado sem "use client"',
    type: 'error',
    info: 'Erro de declaração de função assíncrona exportada em componente client',
  },
  {
    pattern: /onClick={(\w+)(?!\s*\(.*\))}/,
    solution: 'Usar onClick={() => handleFunction()} em vez de onClick={handleFunction}',
    type: 'warning',
    info: 'Possível problema de referência de função em manipuladores de eventos',
  },
  {
    pattern: /"use client"[\s\S]*?const\s+router\s*=\s*useRouter\(\)/,
    solution:
      'Garantir que useRouter está sendo importado de next/navigation em componentes client',
    type: 'warning',
    info: 'Possível uso incorreto do hook useRouter',
  },
];

// Função principal
async function main() {
  console.log(
    `${colors.cyan}Excel Copilot - Verificação de Componentes Client-Side${colors.reset}`
  );
  console.log(
    `${colors.yellow}Este script irá verificar problemas comuns em componentes do lado do cliente${colors.reset}\n`
  );

  const rootDir = path.resolve(__dirname, '..');
  const srcDir = path.join(rootDir, 'src');

  // Verifica se o diretório src existe
  if (!fs.existsSync(srcDir)) {
    console.log(
      `${colors.red}Diretório src não encontrado. Verifique se você está executando este script na pasta raiz do projeto.${colors.reset}`
    );
    process.exit(1);
  }

  try {
    // Encontra todos os arquivos .tsx e .jsx
    const files = glob.sync('**/*.{tsx,jsx}', { cwd: srcDir });

    if (files.length === 0) {
      console.log(
        `${colors.yellow}Nenhum arquivo .tsx ou .jsx encontrado no diretório src.${colors.reset}`
      );
      process.exit(0);
    }

    console.log(
      `${colors.blue}Encontrados ${files.length} arquivos para verificação.${colors.reset}\n`
    );

    let errorsCount = 0;
    let warningsCount = 0;

    // Processa cada arquivo
    for (const file of files) {
      const filePath = path.join(srcDir, file);
      const content = fs.readFileSync(filePath, 'utf8');

      // Verifica se é um componente client-side
      const isClientComponent = content.includes('"use client"');

      let fileHasIssues = false;

      // Verifica cada problema conhecido
      for (const issue of issues) {
        if (issue.pattern.test(content)) {
          if (!fileHasIssues) {
            console.log(`\n${colors.yellow}Arquivo: ${file}${colors.reset}`);
            fileHasIssues = true;
          }

          const type = issue.type === 'error' ? colors.red : colors.yellow;
          console.log(`  ${type}[${issue.type.toUpperCase()}]${colors.reset} ${issue.info}`);
          console.log(`  ${colors.green}Solução:${colors.reset} ${issue.solution}`);

          if (issue.type === 'error') {
            errorsCount++;
          } else {
            warningsCount++;
          }
        }
      }

      // Verifica problema específico de mistura de server/client em um mesmo arquivo
      if (content.includes('"use client"') && content.includes('"use server"')) {
        if (!fileHasIssues) {
          console.log(`\n${colors.yellow}Arquivo: ${file}${colors.reset}`);
          fileHasIssues = true;
        }

        console.log(
          `  ${colors.red}[ERROR]${colors.reset} Mistura de "use client" e "use server" no mesmo arquivo`
        );
        console.log(`  ${colors.green}Solução:${colors.reset} Separar em arquivos diferentes`);
        errorsCount++;
      }
    }

    console.log(`\n${colors.blue}Verificação concluída.${colors.reset}`);
    console.log(`${colors.cyan}Resumo:${colors.reset}`);
    console.log(`  ${colors.red}Erros:${colors.reset} ${errorsCount}`);
    console.log(`  ${colors.yellow}Avisos:${colors.reset} ${warningsCount}`);

    if (errorsCount === 0 && warningsCount === 0) {
      console.log(
        `\n${colors.green}Nenhum problema encontrado nos componentes client-side!${colors.reset}`
      );
    } else {
      console.log(`\n${colors.cyan}Recomendação:${colors.reset}`);
      console.log(`  1. Revise os problemas encontrados e faça as correções necessárias`);
      console.log(
        `  2. Execute novamente este script para verificar se todos os problemas foram resolvidos`
      );
    }
  } catch (error) {
    console.error(`${colors.red}Erro ao processar os arquivos:${colors.reset}`, error);
    process.exit(1);
  }
}

// Executa a função principal
main().catch(error => {
  console.error(`${colors.red}Erro ao executar o script:${colors.reset}`, error);
  process.exit(1);
});
