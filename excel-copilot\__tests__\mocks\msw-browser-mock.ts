/**
 * Mock simples para msw/browser para testes
 * Isso evita erros relacionados ao Service Worker em ambiente de teste
 */

// Implementação simplificada para o módulo msw/browser
const mockMsw = require('./msw-mock');

module.exports = mockMsw;

export const setupWorker = (...handlers: any[]) => {
  return {
    start: () => Promise.resolve(),
    stop: () => Promise.resolve(),
    use: () => {},
    resetHandlers: () => {},
    restoreHandlers: () => {},
    listHandlers: () => handlers,
  };
};

export const worker = setupWorker();

export const http = {
  get: (url: string) => ({ url }),
  post: (url: string) => ({ url }),
  put: (url: string) => ({ url }),
  delete: (url: string) => ({ url }),
  patch: (url: string) => ({ url }),
};

export const HttpResponse = {
  json: (data: any) => ({ status: 200, data }),
  text: (text: string) => ({ status: 200, text }),
  error: () => ({ status: 500 }),
};

export default { worker, http, HttpResponse };
