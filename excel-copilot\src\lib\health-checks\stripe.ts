/**
 * 💳 STRIPE HEALTH CHECK - EXCEL COPILOT
 *
 * Verifica a conectividade e configuração do Stripe para pagamentos
 *
 * <AUTHOR> Copilot Team
 * @version 1.0.0
 */

import { BaseHealthCheck, HealthStatus, healthLogger } from '../health-checks';
// import { unifiedEnv } from '@/config/unified-environment';

// ============================================================================
// TIPOS E INTERFACES
// ============================================================================

interface StripeIssue {
  type: string;
  message: string;
  severity: string;
}

interface StripeConfig {
  enabled: boolean;
  status: string;
  credentials: {
    secretKey?: string;
    publishableKey?: string;
    webhookSecret?: string;
  };
}

interface StripeValidationResult {
  valid: boolean;
  issues: StripeIssue[];
}

interface StripeConnectivityResult {
  success: boolean;
  responseTime: number;
  keyType: string;
  warningsCount?: number;
  hasWarnings?: boolean;
  error?: string;
}

// ============================================================================
// STRIPE HEALTH CHECK
// ============================================================================

export class StripeHealthCheck extends BaseHealthCheck {
  constructor() {
    super('stripe');
  }

  protected async check(): Promise<{
    status: HealthStatus;
    details?: Record<string, string | number | boolean | undefined>;
  }> {
    try {
      // Verificar variáveis de ambiente diretamente
      const secretKey = process.env.STRIPE_SECRET_KEY;
      const publishableKey = process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY;
      const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;

      const issues: StripeIssue[] = [];

      // Verificar Secret Key
      if (!secretKey) {
        issues.push({
          type: 'missing_secret_key',
          message: 'Stripe secret key not configured',
          severity: 'critical',
        });
      } else if (!secretKey.startsWith('sk_')) {
        issues.push({
          type: 'invalid_secret_key_format',
          message: 'Stripe secret key format invalid',
          severity: 'critical',
        });
      }

      // Verificar Publishable Key
      if (!publishableKey) {
        issues.push({
          type: 'missing_publishable_key',
          message: 'Stripe publishable key not configured',
          severity: 'high',
        });
      }

      // Verificar Webhook Secret
      if (!webhookSecret) {
        issues.push({
          type: 'missing_webhook_secret',
          message: 'Stripe webhook secret not configured',
          severity: 'medium',
        });
      }

      let status: HealthStatus = 'healthy';

      if (issues.some(issue => issue.severity === 'critical')) {
        status = 'unhealthy';
      } else if (issues.some(issue => issue.severity === 'high')) {
        status = 'degraded';
      }

      const keyType = this.getStripeEnvironment(secretKey);

      healthLogger.info('Stripe health check completed', {
        status,
        keyType,
      });

      return {
        status,
        details: {
          message: 'Stripe system checked',
          keyType,
          environment: keyType,
          hasSecretKey: !!secretKey,
          hasPublishableKey: !!publishableKey,
          hasWebhookSecret: !!webhookSecret,
          issuesCount: issues.length,
          hasCriticalIssues: issues.some(issue => issue.severity === 'critical'),
        },
      };
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      const errorStack = error instanceof Error ? error.stack : undefined;

      healthLogger.error('Stripe health check failed', {
        error: errorMessage,
        stack: errorStack,
      });

      return {
        status: 'unhealthy',
        details: {
          message: 'Stripe health check failed',
          hasSecretKey: false,
          hasPublishableKey: false,
          hasWebhookSecret: false,
          error: errorMessage,
        },
      };
    }
  }

  /**
   * Valida a configuração do Stripe
   */
  private validateStripeConfig(stripeConfig: StripeConfig): StripeValidationResult {
    const issues: StripeIssue[] = [];

    // Verificar Secret Key
    const secretKey = stripeConfig.credentials.secretKey;
    if (!secretKey) {
      issues.push({
        type: 'missing_secret_key',
        message: 'Stripe secret key not configured',
        severity: 'critical',
      });
    } else {
      // Verificar formato da chave
      if (!secretKey.startsWith('sk_')) {
        issues.push({
          type: 'invalid_secret_key_format',
          message: 'Stripe secret key format invalid',
          severity: 'critical',
        });
      }

      // Verificar se está usando chave de teste em produção
      // TODO: Implementar quando unifiedEnv estiver disponível
      // const env = unifiedEnv.getConfig().NODE_ENV;
      const env = process.env.NODE_ENV;
      if (env === 'production' && secretKey.startsWith('sk_test_')) {
        issues.push({
          type: 'test_key_in_production',
          message: 'Using test Stripe key in production',
          severity: 'critical',
        });
      }
    }

    // Verificar Publishable Key
    const publishableKey = stripeConfig.credentials.publishableKey;
    if (!publishableKey) {
      issues.push({
        type: 'missing_publishable_key',
        message: 'Stripe publishable key not configured',
        severity: 'high',
      });
    } else if (!publishableKey.startsWith('pk_')) {
      issues.push({
        type: 'invalid_publishable_key_format',
        message: 'Stripe publishable key format invalid',
        severity: 'high',
      });
    }

    // Verificar Webhook Secret
    const webhookSecret = stripeConfig.credentials.webhookSecret;
    if (!webhookSecret) {
      issues.push({
        type: 'missing_webhook_secret',
        message: 'Stripe webhook secret not configured',
        severity: 'medium',
      });
    }

    return {
      valid: issues.filter(i => i.severity === 'critical').length === 0,
      issues,
    };
  }

  /**
   * Testa a conectividade com a API do Stripe
   */
  private async testStripeConnectivity(
    stripeConfig: StripeConfig
  ): Promise<StripeConnectivityResult> {
    const start = Date.now();

    try {
      const secretKey = stripeConfig.credentials.secretKey;
      const keyType = this.getStripeEnvironment(secretKey);

      // Em desenvolvimento, simular teste
      // TODO: Implementar quando unifiedEnv estiver disponível
      // if (unifiedEnv.getConfig().NODE_ENV === 'development') {
      if (process.env.NODE_ENV === 'development') {
        return await this.simulateStripeTest(keyType);
      }

      // Em produção, tentar conectar com Stripe real
      return await this.testRealStripe(secretKey || '', keyType);
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      return {
        success: false,
        responseTime: Date.now() - start,
        keyType: 'unknown',
        error: errorMessage,
      };
    }
  }

  /**
   * Simula teste do Stripe para desenvolvimento
   */
  private async simulateStripeTest(keyType: string): Promise<StripeConnectivityResult> {
    const start = Date.now();

    // Simular latência da API
    await new Promise(resolve => setTimeout(resolve, 150 + Math.random() * 200));

    const responseTime = Date.now() - start;
    const warnings: string[] = [];

    if (keyType === 'test') {
      warnings.push('Using Stripe test keys');
    }

    return {
      success: true,
      responseTime,
      keyType,
      warningsCount: warnings.length,
      hasWarnings: warnings.length > 0,
    };
  }

  /**
   * Testa Stripe real em produção
   */
  private async testRealStripe(
    secretKey: string,
    keyType: string
  ): Promise<StripeConnectivityResult> {
    const start = Date.now();

    try {
      // Tentar importar e usar o Stripe SDK
      // Por enquanto, simular até termos a implementação real

      // TODO: Implementar teste real com Stripe
      // const Stripe = await import('stripe');
      // const stripe = new Stripe.default(secretKey, {
      //   apiVersion: '2023-10-16',
      // });
      //
      // // Fazer uma chamada simples para verificar conectividade
      // await stripe.balance.retrieve();

      // Simular teste por enquanto
      await new Promise(resolve => setTimeout(resolve, 300));

      const responseTime = Date.now() - start;
      const warnings: string[] = [];

      if (keyType === 'test') {
        warnings.push('Using Stripe test environment');
      }

      return {
        success: true,
        responseTime,
        keyType,
        warningsCount: warnings.length,
        hasWarnings: warnings.length > 0,
      };
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      return {
        success: false,
        responseTime: Date.now() - start,
        keyType,
        error: errorMessage,
      };
    }
  }

  /**
   * Determina o ambiente do Stripe baseado na chave
   */
  private getStripeEnvironment(secretKey?: string): string {
    if (!secretKey) return 'unknown';

    if (secretKey.startsWith('sk_test_')) return 'test';
    if (secretKey.startsWith('sk_live_')) return 'live';

    return 'unknown';
  }
}

// ============================================================================
// FACTORY FUNCTION
// ============================================================================

/**
 * Cria uma instância do Stripe Health Check
 */
export function createStripeHealthCheck(): StripeHealthCheck {
  return new StripeHealthCheck();
}

// ============================================================================
// UTILITÁRIOS ESPECÍFICOS
// ============================================================================

/**
 * Verifica se o Stripe está configurado corretamente
 */
export function isStripeConfigured(): boolean {
  // TODO: Implementar quando unifiedEnv estiver disponível
  // const stripeConfig = unifiedEnv.getStripeConfig();
  // return stripeConfig.enabled && !!stripeConfig.credentials.secretKey;

  // Implementação temporária baseada em variáveis de ambiente
  const secretKey = process.env.STRIPE_SECRET_KEY;
  return !!secretKey;
}

/**
 * Obtém informações sobre a configuração do Stripe
 */
export function getStripeInfo() {
  // TODO: Implementar quando unifiedEnv estiver disponível
  // const stripeConfig = unifiedEnv.getStripeConfig();

  // Implementação temporária baseada em variáveis de ambiente
  const secretKey = process.env.STRIPE_SECRET_KEY;
  const publishableKey = process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY;
  const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;

  return {
    enabled: !!secretKey,
    status: 'configured',
    hasSecretKey: !!secretKey,
    hasPublishableKey: !!publishableKey,
    hasWebhookSecret: !!webhookSecret,
    environment: secretKey?.startsWith('sk_test_')
      ? 'test'
      : secretKey?.startsWith('sk_live_')
        ? 'live'
        : 'unknown',
  };
}
