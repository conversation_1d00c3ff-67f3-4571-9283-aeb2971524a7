/**
 * Utilitários para operações seguras com o banco de dados Prisma
 */
import { PrismaExtensions } from '../../types/prisma-extensions.d';

/**
 * Converte um objeto com propriedades potencialmente undefined para
 * um formato compatível com Prisma (que aceita null, mas não undefined).
 *
 * @param input Objeto com propriedades potencialmente undefined
 * @returns Objeto com undefined convertido para null
 */
export function toNullableInput<T extends Record<string, unknown>>(
  input: T
): Record<string, unknown> {
  if (!input) return {};

  const result: Record<string, unknown> = {};

  for (const key in input) {
    if (Object.prototype.hasOwnProperty.call(input, key)) {
      result[key] = input[key] === undefined ? null : input[key];
    }
  }

  return result;
}

/**
 * Prepara um objeto para criação de ApiUsage com tipagem segura
 */
export function createApiUsageInput(
  userId: string,
  endpoint: string,
  count: number,
  workbookId: string | undefined,
  billable: boolean
): PrismaExtensions.SafeApiUsageCreateInput {
  return {
    userId,
    endpoint,
    count,
    workbookId: workbookId || null,
    billable,
  };
}

/**
 * Prepara um objeto para criação de Workbook com tipagem segura
 */
export function createWorkbookInput(
  name: string,
  userId: string,
  description: string | undefined,
  sheets: { name: string; data: string }[]
): PrismaExtensions.SafeWorkbookCreateInput {
  return {
    name,
    userId,
    description: description || null,
    sheets: {
      create: sheets,
    },
  };
}

/**
 * Prepara um objeto para criação de ChatHistory com tipagem segura
 */
export function createChatHistoryInput(
  userId: string,
  message: string,
  response: string,
  workbookId: string | undefined
): PrismaExtensions.SafeChatHistoryCreateInput {
  return {
    userId,
    message,
    response,
    workbookId: workbookId || null,
  };
}

/**
 * Cria um objeto para cláusula where do Prisma com tipagem segura
 */
export function createWhereInput<T extends Record<string, unknown>>(
  input: T
): Record<string, unknown> {
  const where: Record<string, unknown> = {};

  for (const key in input) {
    if (Object.prototype.hasOwnProperty.call(input, key) && input[key] !== undefined) {
      where[key] = input[key];
    }
  }

  return where;
}

/**
 * Adiciona condições de data a um objeto where
 */
export function addDateCondition(
  where: Record<string, unknown>,
  field: string,
  startDate?: Date,
  endDate?: Date
): Record<string, unknown> {
  if (!where[field]) {
    where[field] = {};
  }

  if (startDate) {
    const dateRange = where[field] as import('@/types/global-types').DateRangeQuery[string];
    dateRange.gte = startDate;
  }

  if (endDate) {
    const dateRange = where[field] as import('@/types/global-types').DateRangeQuery[string];
    dateRange.lte = endDate;
  }

  return where;
}

/**
 * Converte um valor string | undefined para string | null
 * Compatível com exactOptionalPropertyTypes
 */
export function toNullableString(value: string | undefined): string | null {
  return value ?? null;
}

/**
 * Converte um valor para formato compatível com Prisma update
 * Transforma undefined em null para evitar erros de exactOptionalPropertyTypes
 */
export function toPrismaUpdateInput<T>(value: T | undefined): T | null {
  return value === undefined ? null : value;
}
