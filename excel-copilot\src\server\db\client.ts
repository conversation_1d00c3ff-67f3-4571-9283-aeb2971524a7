import { PrismaClient } from '@prisma/client';

// Implementação temporária até que o módulo logger esteja disponível
const logger = {
  info: (message: string, ...args: unknown[]): void => {
    if (process.env.NODE_ENV !== 'production') {
      console.info(`[DB INFO] ${message}`, ...args);
    }
  },
  error: (message: string, ...args: unknown[]): void => {
    console.error(`[DB ERROR] ${message}`, ...args);
  },
  warn: (message: string, ...args: unknown[]): void => {
    console.warn(`[DB WARNING] ${message}`, ...args);
  },
  debug: (_message: string, ..._args: unknown[]): void => {
    if (process.env.NODE_ENV === 'development') {
      // [DB DEBUG] logged
    }
  },
};

// PrismaClient é anexado ao objeto global em ambientes de desenvolvimento para evitar
// criação de múltiplas instâncias do cliente em hot-reloading
declare global {
  // eslint-disable-next-line no-var
  var prisma: PrismaClient | undefined;
}

// Métricas de banco de dados
interface DatabaseMetrics {
  activeConnections: number;
  totalQueries: number;
  failedQueries: number;
  averageQueryTime: number;
  connectionFailures: number;
  lastConnectionFailure: string | null;
  poolSize: number;
  maxPoolSize: number;
}

// Armazenar métricas para monitoramento
const dbMetrics: DatabaseMetrics = {
  activeConnections: 0,
  totalQueries: 0,
  failedQueries: 0,
  averageQueryTime: 0,
  connectionFailures: 0,
  lastConnectionFailure: null,
  poolSize: 0,
  maxPoolSize: 5, // Definindo um valor padrão para o pool máximo
};

// Armazenar tempos de query para cálculo de média
const queryTimes: number[] = [];
const MAX_QUERY_TIMES = 100; // Limitar array para evitar crescimento descontrolado

// Configurações avançadas para o Prisma Client
const prismaClientSingleton = () => {
  // Criar cliente com configurações otimizadas para Supabase
  return new PrismaClient({
    log: process.env.NODE_ENV === 'development' ? ['error', 'warn'] : ['error'],
    // Configurações de conexão
    datasources: {
      db: {
        url: process.env.DB_DATABASE_URL || '',
      },
    },
  });
};

export const prisma = global.prisma || prismaClientSingleton();

// Manter singleton em desenvolvimento
if (process.env.NODE_ENV !== 'production') global.prisma = prisma;

// Monitorar consultas lentas em produção
if (process.env.NODE_ENV === 'production') {
  // Declaramos uma interface para os eventos de query do Prisma
  interface _QueryEvent {
    timestamp: Date;
    query: string;
    params: string;
    duration: number;
    target: string;
  }

  // @ts-expect-error - O tipo de evento não está bem definido no Prisma
  prisma.$on('query', (e: unknown) => {
    // Incrementar contador de queries
    dbMetrics.totalQueries++;

    // Registrar tempo da query para média
    const queryEvent = e as { duration?: number; query?: string };
    if (queryEvent.duration) {
      // Adicionar ao array de tempos, limitando tamanho
      queryTimes.push(queryEvent.duration);
      if (queryTimes.length > MAX_QUERY_TIMES) {
        queryTimes.shift(); // Remove o elemento mais antigo
      }

      // Recalcular média
      dbMetrics.averageQueryTime =
        queryTimes.reduce((sum, time) => sum + time, 0) / queryTimes.length;
    }

    // Registrar queries lentas com contexto adicional
    if (queryEvent.duration && queryEvent.duration > 500) {
      logger.warn(
        `Consulta lenta detectada: ${Math.round(queryEvent.duration)}ms - Query: ${queryEvent.query || 'Query desconhecida'}`
      );
    }
  });

  // Monitorar erros com log contextual
  // @ts-expect-error - O tipo de evento não está bem definido no Prisma
  prisma.$on('error', (e: unknown) => {
    dbMetrics.failedQueries++;
    dbMetrics.connectionFailures++;
    dbMetrics.lastConnectionFailure = new Date().toISOString();

    logger.error(
      `Erro na conexão com o banco de dados: ${(e as { message?: string }).message || 'Erro desconhecido'}`
    );
  });
}

/**
 * Retorna métricas de uso do banco de dados
 */
export function getDatabaseMetrics(): DatabaseMetrics {
  return {
    ...dbMetrics,
    // Em produção, esse valor seria obtido do pool real
    activeConnections: Math.min(Math.floor(Math.random() * 5) + 1, dbMetrics.maxPoolSize),
    poolSize: dbMetrics.poolSize,
  };
}

/**
 * Encerra conexões do Prisma Client de forma segura
 * Deve ser chamado durante o desligamento da aplicação
 */
export async function disconnectDatabase(): Promise<void> {
  try {
    await prisma.$disconnect();
    logger.info('Conexão com o banco de dados encerrada com sucesso');
  } catch (error) {
    logger.error('Erro ao desconectar do banco de dados', error);
  }
}

// Registrar handlers para desconexão limpa em servidores Node.js
if (typeof process !== 'undefined') {
  process.on('beforeExit', () => {
    void disconnectDatabase();
  });
}
