#!/usr/bin/env node

/**
 * Script de teste para GitHub MCP Integration
 *
 * Este script verifica se a integração GitHub está funcionando corretamente
 * sem fazer chamadas reais para a API (para evitar rate limiting)
 */

async function testGitHubIntegration() {
  console.log('🐙 Testando GitHub MCP Integration...\n');

  try {
    // Verificar se o arquivo TypeScript existe
    const fs = await import('fs');
    const path = await import('path');

    const tsFile = path.join(process.cwd(), 'src/lib/github-integration.ts');
    if (!fs.existsSync(tsFile)) {
      throw new Error('Arquivo github-integration.ts não encontrado');
    }

    console.log('✅ Arquivo github-integration.ts encontrado');

    // Para teste, vamos apenas verificar a estrutura do arquivo
    const fileContent = fs.readFileSync(tsFile, 'utf8');

    // Verificar se as classes principais estão definidas
    const hasGitHubClient = fileContent.includes('export class GitHubClient');
    const hasGitHubMonitoringService = fileContent.includes('export class GitHubMonitoringService');

    console.log('✅ GitHubClient class:', hasGitHubClient ? 'ENCONTRADA' : 'NÃO ENCONTRADA');
    console.log(
      '✅ GitHubMonitoringService class:',
      hasGitHubMonitoringService ? 'ENCONTRADA' : 'NÃO ENCONTRADA'
    );

    if (!hasGitHubClient || !hasGitHubMonitoringService) {
      throw new Error('Classes principais não encontradas no arquivo');
    }

    console.log('✅ Módulo GitHub estrutura validada');

    // Verificar métodos principais no código
    const clientMethods = [
      'getRateLimit',
      'getRepositories',
      'getRepository',
      'getIssues',
      'getPullRequests',
      'getWorkflowRuns',
      'getAuthenticatedUser',
      'checkHealth',
    ];

    console.log('\n📊 Verificando métodos do GitHubClient no código:');
    clientMethods.forEach(method => {
      const exists = fileContent.includes(`async ${method}(`) || fileContent.includes(`${method}(`);
      console.log(`${exists ? '✅' : '❌'} ${method}: ${exists ? 'ENCONTRADO' : 'FALTANDO'}`);
    });

    // Verificar métodos do serviço de monitoramento
    const serviceMethods = ['getRepositoryDashboard', 'getCICDMetrics'];

    console.log('\n📊 Verificando métodos do GitHubMonitoringService no código:');
    serviceMethods.forEach(method => {
      const exists = fileContent.includes(`async ${method}(`) || fileContent.includes(`${method}(`);
      console.log(`${exists ? '✅' : '❌'} ${method}: ${exists ? 'ENCONTRADO' : 'FALTANDO'}`);
    });

    // Verificar configuração de ambiente
    console.log('\n🔧 Verificando configuração de ambiente:');
    const envVars = ['GITHUB_TOKEN', 'GITHUB_OWNER', 'GITHUB_REPO'];

    envVars.forEach(envVar => {
      const exists = !!process.env[envVar];
      console.log(
        `${exists ? '✅' : '⚠️'} ${envVar}: ${exists ? 'CONFIGURADO' : 'NÃO CONFIGURADO'}`
      );
    });

    console.log('\n🎉 Todos os testes básicos passaram!');
    console.log('\n📝 Próximos passos:');
    console.log('1. Configure GITHUB_TOKEN no .env.local');
    console.log('2. Configure GITHUB_OWNER e GITHUB_REPO (opcional)');
    console.log('3. Teste os endpoints via: curl http://localhost:3000/api/github/status');
    console.log('4. Verifique health check via: curl http://localhost:3000/api/health');

    // Verificar se os endpoints existem
    console.log('\n🌐 Verificando endpoints da API:');

    const endpoints = [
      'src/app/api/github/status/route.ts',
      'src/app/api/github/repositories/route.ts',
      'src/app/api/github/issues/route.ts',
      'src/app/api/github/workflows/route.ts',
    ];

    endpoints.forEach(endpoint => {
      const exists = fs.existsSync(path.join(process.cwd(), endpoint));
      console.log(`${exists ? '✅' : '❌'} ${endpoint}: ${exists ? 'EXISTE' : 'FALTANDO'}`);
    });

    // Verificar documentação
    console.log('\n📚 Verificando documentação:');
    const docs = ['GITHUB_MCP_INTEGRATION.md', 'README.md'];

    docs.forEach(doc => {
      const exists = fs.existsSync(path.join(process.cwd(), doc));
      console.log(`${exists ? '✅' : '❌'} ${doc}: ${exists ? 'EXISTE' : 'FALTANDO'}`);
    });

    console.log('\n🚀 GitHub MCP Integration está pronta para uso!');
  } catch (error) {
    console.error('❌ Erro durante os testes:', error);
    process.exit(1);
  }
}

// Executar testes
testGitHubIntegration().catch(console.error);
