import { Copy, Edit, Settings, Trash2 } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { toast } from 'sonner';

import { useFetchWithCSRF } from '@/components/providers/csrf-provider';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface WorkbookActionsProps {
  workbookId: string;
  onComplete?: () => void;
  allowDuplicate?: boolean;
  allowDelete?: boolean;
  buttonVariant?: 'ghost' | 'outline' | 'secondary';
  buttonSize?: 'default' | 'sm' | 'lg' | 'icon';
  onlyEdit?: boolean;
}

/**
 * Componente reutilizável para ações comuns de workbooks (abrir, duplicar, excluir)
 * Usado para evitar duplicação de código entre dashboard e outros componentes
 */
export function WorkbookActions({
  workbookId,
  onComplete,
  allowDuplicate = true,
  allowDelete = true,
  buttonVariant = 'ghost',
  buttonSize = 'icon',
  onlyEdit = false,
}: WorkbookActionsProps) {
  const router = useRouter();
  const { fetchWithCSRF } = useFetchWithCSRF();
  const [isDeleting, setIsDeleting] = useState(false);

  // Função para abrir uma planilha
  const handleOpenWorkbook = (e?: React.MouseEvent) => {
    if (e) e.stopPropagation();
    router.push(`/workbook/${workbookId}`);
    if (onComplete) onComplete();
  };

  // Função para duplicar uma planilha
  const handleDuplicateWorkbook = async (e?: React.MouseEvent) => {
    if (e) e.stopPropagation();

    try {
      const response = await fetchWithCSRF(`/api/workbooks/${workbookId}/duplicate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Erro ao duplicar planilha');
      }

      toast.success(`Planilha duplicada com sucesso!`);

      if (onComplete) onComplete();
    } catch (error) {
      console.error('Erro ao duplicar workbook:', error);
      toast.error('Não foi possível duplicar a planilha');
    }
  };

  // Função para excluir uma planilha
  const handleDeleteWorkbook = async (e?: React.MouseEvent) => {
    if (e) e.stopPropagation();

    try {
      setIsDeleting(true);

      const response = await fetchWithCSRF(`/api/workbooks/${workbookId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Erro ao excluir planilha');
      }

      toast.success('Planilha excluída com sucesso!');

      if (onComplete) onComplete();
    } catch (error) {
      console.error('Erro ao excluir workbook:', error);
      toast.error('Não foi possível excluir a planilha');
    } finally {
      setIsDeleting(false);
    }
  };

  // Se for apenas edição, retornar apenas o botão de editar
  if (onlyEdit) {
    return (
      <Button
        variant={buttonVariant}
        size={buttonSize}
        onClick={handleOpenWorkbook}
        aria-label="Editar planilha"
      >
        <Edit className="h-4 w-4 mr-2" />
        Editar
      </Button>
    );
  }

  // Retornar dropdown com todas as ações
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild onClick={e => e.stopPropagation()}>
        <Button variant={buttonVariant} size={buttonSize} aria-label="Opções da planilha">
          <Settings className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>Opções</DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={e => handleOpenWorkbook(e)}>
          <Edit className="h-4 w-4 mr-2" />
          Editar
        </DropdownMenuItem>

        {allowDuplicate && (
          <DropdownMenuItem onClick={e => handleDuplicateWorkbook(e)}>
            <Copy className="h-4 w-4 mr-2" />
            Duplicar
          </DropdownMenuItem>
        )}

        {allowDelete && (
          <>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              className="text-destructive focus:text-destructive"
              onClick={e => handleDeleteWorkbook(e)}
              disabled={isDeleting}
            >
              <Trash2 className="h-4 w-4 mr-2" />
              {isDeleting ? 'Excluindo...' : 'Excluir'}
            </DropdownMenuItem>
          </>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
