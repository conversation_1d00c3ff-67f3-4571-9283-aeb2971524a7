/**
 * @jest-environment jsdom
 */

import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { ThemeProvider } from 'next-themes';

// Definindo um componente responsivo de teste
const ResponsiveComponent = () => {
  return (
    <div>
      <div className="hidden sm:block" data-testid="desktop-only">
        Desktop Only
      </div>
      <div className="block sm:hidden" data-testid="mobile-only">
        Mobile Only
      </div>
      <div className="w-full md:w-1/2 lg:w-1/3" data-testid="responsive-width">
        Responsive Width
      </div>
      <div className="flex flex-col md:flex-row" data-testid="flex-container">
        <div className="p-2">Item 1</div>
        <div className="p-2">Item 2</div>
      </div>
      <div className="text-sm md:text-base lg:text-lg" data-testid="responsive-text">
        Texto Responsivo
      </div>
    </div>
  );
};

// Componente para testar dark/light mode
const ThemeComponent = () => {
  return (
    <div>
      <div className="bg-white dark:bg-gray-900" data-testid="theme-bg">
        <p className="text-black dark:text-white" data-testid="theme-text">
          Texto com tema
        </p>
      </div>
      <button
        data-testid="theme-toggle"
        onClick={() => {
          const html = document.documentElement;
          if (html.classList.contains('dark')) {
            html.classList.remove('dark');
            html.classList.add('light');
          } else {
            html.classList.remove('light');
            html.classList.add('dark');
          }
        }}
      >
        Toggle Theme
      </button>
    </div>
  );
};

// Mock da função matchMedia do window
const mockMatchMedia = (matches: boolean) => {
  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: jest.fn().mockImplementation(query => ({
      matches,
      media: query,
      onchange: null,
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
      dispatchEvent: jest.fn(),
    })),
  });
};

describe('Testes de Responsividade e CSS', () => {
  beforeEach(() => {
    // Resetar mocks e ambiente entre os testes
    jest.clearAllMocks();
    document.documentElement.className = 'light';
  });

  test('Componentes exibem layout correto em desktop', () => {
    // Simular tela de desktop (viewport maior)
    mockMatchMedia(true);

    render(<ResponsiveComponent />);

    // Em desktop:
    // - Elemento desktop-only deve estar visível
    // - Elemento mobile-only deve estar oculto
    expect(screen.getByTestId('desktop-only')).not.toHaveStyle({ display: 'none' });
    expect(screen.getByTestId('mobile-only')).toHaveClass('block sm:hidden');

    // Verificar classes responsivas aplicadas
    expect(screen.getByTestId('responsive-width')).toHaveClass('w-full md:w-1/2 lg:w-1/3');
    expect(screen.getByTestId('flex-container')).toHaveClass('flex flex-col md:flex-row');
    expect(screen.getByTestId('responsive-text')).toHaveClass('text-sm md:text-base lg:text-lg');
  });

  test('Componentes exibem layout correto em mobile', () => {
    // Simular tela de mobile (viewport menor)
    mockMatchMedia(false);

    render(<ResponsiveComponent />);

    // Em mobile:
    // - Elemento desktop-only deve estar oculto
    // - Elemento mobile-only deve estar visível
    expect(screen.getByTestId('desktop-only')).toHaveClass('hidden sm:block');
    expect(screen.getByTestId('mobile-only')).not.toHaveStyle({ display: 'none' });

    // Verificar classes responsivas aplicadas
    expect(screen.getByTestId('responsive-width')).toHaveClass('w-full md:w-1/2 lg:w-1/3');
    expect(screen.getByTestId('flex-container')).toHaveClass('flex flex-col md:flex-row');
    expect(screen.getByTestId('responsive-text')).toHaveClass('text-sm md:text-base lg:text-lg');
  });

  test('Tema claro/escuro muda corretamente', async () => {
    render(
      <ThemeProvider defaultTheme="light" enableSystem={false}>
        <ThemeComponent />
      </ThemeProvider>
    );

    // Verificar tema inicial (light)
    expect(document.documentElement.classList.contains('light')).toBeTruthy();

    // Clicar no botão para mudar o tema
    const user = userEvent.setup();
    await user.click(screen.getByTestId('theme-toggle'));

    // Verificar que o tema mudou para dark
    expect(document.documentElement.classList.contains('dark')).toBeTruthy();
  });

  test('Carregamento de fonte personalizada', () => {
    // Verificar se a fonte personalizada está definida no CSS
    const fontFamilyStyle = document.createElement('style');
    fontFamilyStyle.innerHTML = `
      :root {
        --font-sans: 'Inter', sans-serif;
      }
      body {
        font-family: var(--font-sans);
      }
    `;
    document.head.appendChild(fontFamilyStyle);

    // Renderizar um componente simples
    render(<div data-testid="font-test">Teste de Fonte</div>);

    // Verificar se a fonte foi aplicada
    const styles = window.getComputedStyle(document.body);
    expect(styles.fontFamily).toBeDefined();

    // Limpar
    document.head.removeChild(fontFamilyStyle);
  });

  test('Animações são carregadas corretamente', () => {
    // Verificar se as animações estão definidas no CSS
    const animationStyle = document.createElement('style');
    animationStyle.innerHTML = `
      .animate-fade-in {
        animation: fadeIn 0.5s ease-in-out;
      }
      @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
      }
    `;
    document.head.appendChild(animationStyle);

    // Renderizar um componente com animação
    render(
      <div data-testid="animation-test" className="animate-fade-in">
        Teste de Animação
      </div>
    );

    // Verificar se a classe de animação foi aplicada
    const element = screen.getByTestId('animation-test');
    expect(element).toHaveClass('animate-fade-in');

    // Limpar
    document.head.removeChild(animationStyle);
  });

  test('Layout de grid é aplicado corretamente', () => {
    // Renderizar componente com grid
    render(
      <div
        data-testid="grid-container"
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"
      >
        <div data-testid="grid-item-1">Item 1</div>
        <div data-testid="grid-item-2">Item 2</div>
        <div data-testid="grid-item-3">Item 3</div>
      </div>
    );

    // Verificar se as classes de grid foram aplicadas
    const gridContainer = screen.getByTestId('grid-container');
    expect(gridContainer).toHaveClass('grid');
    expect(gridContainer).toHaveClass('grid-cols-1');
    expect(gridContainer).toHaveClass('md:grid-cols-2');
    expect(gridContainer).toHaveClass('lg:grid-cols-3');
    expect(gridContainer).toHaveClass('gap-4');
  });
});
