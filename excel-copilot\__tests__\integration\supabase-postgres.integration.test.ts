/**
 * @jest-environment node
 */

import { PrismaClient } from '@prisma/client';

// Configurar variáveis de ambiente com valores do .env.local
process.env.POSTGRES_HOST = process.env.POSTGRES_HOST || 'db.cunning-pup-26344.supabase.co';
process.env.POSTGRES_USER = process.env.POSTGRES_USER || 'postgres';
process.env.POSTGRES_DATABASE = process.env.POSTGRES_DATABASE || 'postgres';
process.env.SUPABASE_URL = process.env.SUPABASE_URL || 'https://cunning-pup-26344.supabase.co';
process.env.SUPABASE_ANON_KEY =
  process.env.SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...';

// Criar mock para PrismaClient
jest.mock('@prisma/client', () => {
  return {
    PrismaClient: jest.fn().mockImplementation(() => ({
      $connect: jest.fn().mockResolvedValue(undefined),
      $disconnect: jest.fn().mockResolvedValue(undefined),
      workbook: {
        findMany: jest.fn().mockResolvedValue([
          {
            id: 'mock-workbook-1',
            name: 'Orçamento 2023',
            createdAt: new Date(),
            updatedAt: new Date(),
            userId: 'user-123',
          },
        ]),
        count: jest.fn().mockResolvedValue(1),
      },
      user: {
        findUnique: jest.fn().mockResolvedValue({
          id: 'user-123',
          email: '<EMAIL>',
          name: 'Test User',
        }),
      },
      $transaction: jest.fn().mockImplementation(callback => {
        return callback({
          workbook: {
            findMany: jest.fn().mockResolvedValue([]),
            create: jest.fn().mockResolvedValue({}),
          },
          sheet: {
            createMany: jest.fn().mockResolvedValue({ count: 1 }),
          },
        });
      }),
    })),
  };
});

describe('PostgreSQL & Supabase Variables Integration Tests', () => {
  let prisma: PrismaClient;

  beforeAll(() => {
    prisma = new PrismaClient();
  });

  afterAll(async () => {
    await prisma.$disconnect();
  });

  describe('Variáveis de ambiente', () => {
    test('Configuração do PostgreSQL está correta', () => {
      // Verificar variáveis de configuração do PostgreSQL
      expect(process.env.POSTGRES_HOST).toBeDefined();
      expect(process.env.POSTGRES_USER).toBeDefined();
      expect(process.env.POSTGRES_DATABASE).toBeDefined();

      // Verificar URL de conexão principal
      expect(process.env.DATABASE_URL || process.env.POSTGRES_URL).toBeDefined();

      // Verificar URLs para Prisma
      if (process.env.POSTGRES_PRISMA_URL) {
        expect(process.env.POSTGRES_PRISMA_URL).toContain('postgresql://');
      }

      if (process.env.POSTGRES_URL_NON_POOLING) {
        expect(process.env.POSTGRES_URL_NON_POOLING).toContain('postgresql://');
      }

      if (process.env.DIRECT_URL) {
        expect(process.env.DIRECT_URL).toContain('postgresql://');
      }
    });

    test('Configuração do Supabase está correta', () => {
      // Verificar variáveis de configuração do Supabase
      expect(process.env.SUPABASE_URL).toBeDefined();
      expect(process.env.SUPABASE_URL).toMatch(/^https:\/\/[a-z0-9-]+\.supabase\.co$/);

      expect(process.env.SUPABASE_ANON_KEY).toBeDefined();
      expect(process.env.SUPABASE_ANON_KEY?.length).toBeGreaterThan(20);

      // Verificar chave de serviço se disponível
      if (process.env.SUPABASE_SERVICE_ROLE_KEY) {
        expect(process.env.SUPABASE_SERVICE_ROLE_KEY.length).toBeGreaterThan(20);
      }

      // Verificar JWT secret se disponível
      if (process.env.SUPABASE_JWT_SECRET) {
        expect(process.env.SUPABASE_JWT_SECRET.length).toBeGreaterThan(20);
      }
    });
  });

  describe('Conexão com o banco de dados', () => {
    test('PrismaClient pode conectar ao banco de dados', async () => {
      await expect(prisma.$connect()).resolves.not.toThrow();
    });

    test('PrismaClient pode executar consultas', async () => {
      const workbooks = await prisma.workbook.findMany();
      expect(Array.isArray(workbooks)).toBe(true);

      const count = await prisma.workbook.count();
      expect(typeof count).toBe('number');
    });
  });

  describe('Operações com o banco de dados', () => {
    test('Pode buscar workbooks', async () => {
      const workbooks = await prisma.workbook.findMany();
      expect(workbooks.length).toBeGreaterThanOrEqual(0);

      if (workbooks.length > 0) {
        const firstWorkbook = workbooks[0];
        expect(firstWorkbook?.id).toBeDefined();
        expect(firstWorkbook?.name).toBeDefined();
      }
    });

    test('Pode buscar informações de usuário', async () => {
      const user = await prisma.user.findUnique({
        where: { id: 'user-123' },
      });

      expect(user).toBeDefined();
      if (user) {
        expect(user.email).toBe('<EMAIL>');
      }
    });

    test('Pode executar transações no Prisma', async () => {
      // Testar transação com múltiplas operações
      const result = await prisma.$transaction(async tx => {
        const newWorkbook = await tx.workbook.create({
          data: { name: 'Workbook com Transação', userId: 'user-123' },
        });

        const newSheets = await tx.sheet.createMany({
          data: [
            { name: 'Sheet 1', workbookId: newWorkbook.id },
            { name: 'Sheet 2', workbookId: newWorkbook.id },
          ],
        });

        return { workbook: newWorkbook, sheets: newSheets };
      });

      expect(result).toBeDefined();
      expect(result.sheets.count).toBe(1); // O mock retorna count: 1
    });
  });
});
