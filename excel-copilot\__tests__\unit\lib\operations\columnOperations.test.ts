import { executeColumnOperation, extractColumnOperations } from '@/lib/operations/columnOperations';
import type { ExcelOperation } from '@/types/index';

// Mock dados para testes
const mockSheetData = {
  headers: ['ID', 'Nome', 'Vendas', 'Data'],
  rows: [
    [1, '<PERSON>', 1500, '2023-01-15'],
    [2, '<PERSON>', 2800, '2023-01-20'],
    [3, 'Pedro', 950, '2023-01-25'],
    [4, 'Ana', 3200, '2023-01-30'],
    [5, '<PERSON>', 1100, '2023-02-05'],
  ],
};

describe('Operações de Coluna', () => {
  describe('extractColumnOperations', () => {
    test('deve extrair operação de soma corretamente', () => {
      const input = 'Some os valores da coluna Vendas';
      const operations = extractColumnOperations(input);

      expect(operations).toHaveLength(1);
      expect(operations[0]?.type).toBe('COLUMN_OPERATION');
      expect(operations[0]?.data?.operation).toBe('SUM');
      expect(operations[0]?.data?.columnName).toBe('Vendas');
    });

    test('deve extrair operação de média corretamente', () => {
      const input = 'Calcule a média da coluna Vendas';
      const operations = extractColumnOperations(input);

      expect(operations).toHaveLength(1);
      expect(operations[0]?.type).toBe('COLUMN_OPERATION');
      expect(operations[0]?.data?.operation).toBe('AVERAGE');
      expect(operations[0]?.data?.columnName).toBe('Vendas');
    });

    test('deve extrair operação de máximo corretamente', () => {
      const input = 'Encontre o valor máximo na coluna Vendas';
      const operations = extractColumnOperations(input);

      expect(operations).toHaveLength(1);
      expect(operations[0]?.type).toBe('COLUMN_OPERATION');
      expect(operations[0]?.data?.operation).toBe('MAX');
      expect(operations[0]?.data?.columnName).toBe('Vendas');
    });

    test('deve extrair operação de mínimo corretamente', () => {
      const input = 'Qual é o valor mínimo na coluna Vendas?';
      const operations = extractColumnOperations(input);

      expect(operations).toHaveLength(1);
      expect(operations[0]?.type).toBe('COLUMN_OPERATION');
      expect(operations[0]?.data?.operation).toBe('MIN');
      expect(operations[0]?.data?.columnName).toBe('Vendas');
    });

    test('deve extrair operação de contagem corretamente', () => {
      const input = 'Conte quantos valores existem na coluna Nome';
      const operations = extractColumnOperations(input);

      expect(operations).toHaveLength(1);
      expect(operations[0]?.type).toBe('COLUMN_OPERATION');
      expect(operations[0]?.data?.operation).toBe('COUNT');
      expect(operations[0]?.data?.columnName).toBe('Nome');
    });

    test('deve ignorar texto sem operações válidas', () => {
      const input = 'Mostre os dados da planilha';
      const operations = extractColumnOperations(input);

      expect(operations).toHaveLength(0);
    });

    test('deve extrair coluna pelo número quando especificado', () => {
      const input = 'Calcule a média da coluna 3';
      const operations = extractColumnOperations(input);

      expect(operations).toHaveLength(1);
      expect(operations[0]?.data?.operation).toBe('AVERAGE');
      expect(operations[0]?.data?.columnIndex).toBe(3);
    });

    test('deve extrair múltiplas operações quando presentes', () => {
      const input = 'Some a coluna Vendas e encontre o valor máximo da coluna Vendas';
      const operations = extractColumnOperations(input);

      expect(operations).toHaveLength(2);
      expect(operations[0]?.data?.operation).toBe('SUM');
      expect(operations[1]?.data?.operation).toBe('MAX');
    });
  });

  describe('executeColumnOperation', () => {
    test('deve calcular a soma de uma coluna por nome', async () => {
      const operation: ExcelOperation = {
        type: 'COLUMN_OPERATION',
        data: {
          operation: 'SUM',
          columnName: 'Vendas',
        },
      };

      const result = await executeColumnOperation(mockSheetData, operation);

      expect(result.updatedData).toEqual(mockSheetData); // Dados originais não devem ser alterados
      expect(result.resultSummary).toMatch(/9\.550/); // Soma de 1500 + 2800 + 950 + 3200 + 1100
    });

    test('deve calcular a média de uma coluna por nome', async () => {
      const operation: ExcelOperation = {
        type: 'COLUMN_OPERATION',
        data: {
          operation: 'AVERAGE',
          columnName: 'Vendas',
        },
      };

      const result = await executeColumnOperation(mockSheetData, operation);

      expect(result.resultSummary).toMatch(/1\.910/); // Média de (1500 + 2800 + 950 + 3200 + 1100) / 5
    });

    test('deve encontrar o valor máximo de uma coluna por nome', async () => {
      const operation: ExcelOperation = {
        type: 'COLUMN_OPERATION',
        data: {
          operation: 'MAX',
          columnName: 'Vendas',
        },
      };

      const result = await executeColumnOperation(mockSheetData, operation);

      expect(result.resultSummary).toMatch(/3\.200/);
    });

    test('deve encontrar o valor mínimo de uma coluna por nome', async () => {
      const operation: ExcelOperation = {
        type: 'COLUMN_OPERATION',
        data: {
          operation: 'MIN',
          columnName: 'Vendas',
        },
      };

      const result = await executeColumnOperation(mockSheetData, operation);

      expect(result.resultSummary).toMatch(/950/);
    });

    test('deve contar valores não vazios em uma coluna por nome', async () => {
      const operation: ExcelOperation = {
        type: 'COLUMN_OPERATION',
        data: {
          operation: 'COUNT',
          columnName: 'Nome',
        },
      };

      const result = await executeColumnOperation(mockSheetData, operation);

      expect(result.resultSummary).toContain('5');
    });

    test('deve calcular operações em uma coluna por índice', async () => {
      const operation: ExcelOperation = {
        type: 'COLUMN_OPERATION',
        data: {
          operation: 'SUM',
          columnIndex: 2, // Índice da coluna 'Vendas'
        },
      };

      const result = await executeColumnOperation(mockSheetData, operation);

      expect(result.resultSummary).toMatch(/9\.550/);
    });

    test('deve retornar mensagem de erro para coluna inexistente', async () => {
      const operation: ExcelOperation = {
        type: 'COLUMN_OPERATION',
        data: {
          operation: 'SUM',
          columnName: 'ColunaNaoExistente',
        },
      };

      try {
        await executeColumnOperation(mockSheetData, operation);
        fail('Deveria ter lançado um erro');
      } catch (error: unknown) {
        if (error instanceof Error) {
          expect(error.message).toContain('não encontrada');
        } else {
          fail('Erro não é uma instância de Error');
        }
      }
    });

    test('deve retornar mensagem de erro para operação inválida', async () => {
      const operation: ExcelOperation = {
        type: 'COLUMN_OPERATION',
        data: {
          operation: 'OPERACAO_INVALIDA' as any,
          columnName: 'Vendas',
        },
      };

      try {
        await executeColumnOperation(mockSheetData, operation);
        fail('Deveria ter lançado um erro');
      } catch (error: unknown) {
        if (error instanceof Error) {
          expect(error.message).toContain('não suportada');
        } else {
          fail('Erro não é uma instância de Error');
        }
      }
    });
  });
});
