'use client';

import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Analytics } from '@vercel/analytics/react';
import { SessionProvider } from 'next-auth/react';
import { ThemeProvider } from 'next-themes';
import { useState, Suspense, lazy, useEffect } from 'react';
import superjson from 'superjson';

import { AppInitializer } from '@/components/AppInitializer';
import { ClientOnly } from '@/components/ClientOnly';
import { initEnvValidation } from '@/lib/env-validator';
import { TRPCProvider } from '@/lib/trpc';

// Lazy load components that aren't needed immediately
const TourProvider = lazy(() =>
  import('@/components/user-onboarding/TourProvider').then(module => ({
    default: module.TourProvider,
  }))
);

// Lazy load additional components
// DesktopBridgeProvider removido - não é mais necessário
const CSRFProvider = lazy(() =>
  import('@/components/providers/csrf-provider').then(module => ({
    default: module.CSRFProvider,
  }))
);
const ToastProvider = lazy(() =>
  import('@/providers/toast-wrapper').then(module => ({
    default: module.ToastProvider,
  }))
);
const Toaster = lazy(() =>
  import('@/components/ui/toaster').then(module => ({
    default: module.Toaster,
  }))
);
const SonnerToaster = lazy(() =>
  import('sonner').then(module => ({
    default: module.Toaster,
  }))
);

// Iniciar validação de variáveis de ambiente no lado do servidor
if (typeof window === 'undefined') {
  initEnvValidation();
}

export function AppProviders({
  children,
  locale: _locale = 'pt-BR',
}: {
  children: React.ReactNode;
  locale?: string;
}) {
  const [mounted, setMounted] = useState(false);

  // Efeito para lidar com montagem do lado do cliente
  useEffect(() => {
    setMounted(true);
  }, []);

  // Create a new QueryClient instance for each provider render
  // This ensures isolated state between different users/sessions
  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            staleTime: 60 * 1000, // 1 minute
            cacheTime: 5 * 60 * 1000, // 5 minutes
            refetchOnWindowFocus: false,
            retry: 1,
            // Serialize/deserialize with superjson for improved type safety
            queryFn: ({ queryKey }) => {
              const data = superjson.stringify(queryKey);
              return data;
            },
          },
        },
      })
  );

  return (
    <SessionProvider>
      <ThemeProvider attribute="class" defaultTheme="system" enableSystem disableTransitionOnChange>
        <QueryClientProvider client={queryClient}>
          <TRPCProvider>
            {/*
              AppInitializer centraliza a inicialização da aplicação e
              mostra uma tela de carregamento enquanto os serviços são inicializados
            */}
            <ClientOnly
              fallback={
                <div className="flex h-screen w-full items-center justify-center bg-background">
                  <div className="text-center">Carregando...</div>
                </div>
              }
            >
              <AppInitializer>
                {/*
                Aplicamos lazy loading para todos os componentes que não são
                necessários para a renderização inicial da página
              */}
                <Suspense fallback={null}>
                  <CSRFProvider>
                    <Suspense fallback={null}>
                      <ToastProvider>
                        {/* Tour de onboarding - carregado sob demanda */}
                        <Suspense fallback={null}>
                          {mounted && <TourProvider>{children}</TourProvider>}
                        </Suspense>

                        {/* Analytics */}
                        <Analytics />
                      </ToastProvider>
                    </Suspense>
                  </CSRFProvider>
                </Suspense>

                {/* Toast notifications - carregadas quando necessárias */}
                <Suspense fallback={null}>
                  <Toaster />
                </Suspense>

                <Suspense fallback={null}>
                  <SonnerToaster
                    position="top-right"
                    toastOptions={{
                      style: {
                        fontSize: '0.875rem',
                      },
                      duration: 4000,
                    }}
                  />
                </Suspense>
              </AppInitializer>
            </ClientOnly>
          </TRPCProvider>
        </QueryClientProvider>
      </ThemeProvider>
    </SessionProvider>
  );
}

// Exportar como Providers para compatibilidade com layout.tsx
export { AppProviders as Providers };
