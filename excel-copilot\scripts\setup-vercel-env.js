#!/usr/bin/env node

/**
 * Script para configurar as variáveis de ambiente do .env.production no Vercel
 */

const https = require('https');

// Configurações do Vercel
const VERCEL_TOKEN = '************************';
const PROJECT_ID = 'prj_IQemY1bXbDdiiQmHDfRAYLUqMZIg';
const TEAM_ID = 'team_BLCIn3CF09teqBeBn8u0fLqp';

// Variáveis de ambiente do .env.production
const ENV_VARS = {
  // Configurações básicas
  NODE_ENV: 'production',
  APP_NAME: 'Excel Copilot',
  APP_VERSION: '1.0.0',
  APP_URL: 'https://excel-copilot-eight.vercel.app',

  // Autenticação - NextAuth
  AUTH_NEXTAUTH_SECRET: 'dW5jL4x7Q2tPaDZkVzFqc3pVTEhuMDdYZ0tLbldnRkxRV3hNeUJTRHJSWQ',
  AUTH_NEXTAUTH_URL: 'https://excel-copilot-eight.vercel.app',
  AUTH_SKIP_PROVIDERS: 'false',
  AUTH_GOOGLE_CLIENT_ID: '217111050148-1gocm6a0sa9jcrk8s08dubqn8n2001lv.apps.googleusercontent.com',
  AUTH_GOOGLE_CLIENT_SECRET: '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',
  AUTH_GITHUB_CLIENT_ID: '********************',
  AUTH_GITHUB_CLIENT_SECRET: '7c80b91c934dc9845a8ce7a362581d8ab45f2c3e',

  // Banco de dados - Supabase
  DB_DATABASE_URL:
    'postgresql://postgres.eliuoignzzxnjkcmmtml:<EMAIL>:6543/postgres?pgbouncer=true&connection_limit=1&pool_timeout=20',
  DB_DIRECT_URL:
    'postgresql://postgres.eliuoignzzxnjkcmmtml:<EMAIL>:5432/postgres',
  SUPABASE_URL: 'https://eliuoignzzxnjkcmmtml.supabase.co',
  NEXT_PUBLIC_SUPABASE_URL: 'https://eliuoignzzxnjkcmmtml.supabase.co',
  SUPABASE_ANON_KEY:
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.rMyGA-hjWQNxJDdLSi3gYtSi8Gg2TeDxAs8f2gx8Zdk',
  NEXT_PUBLIC_SUPABASE_ANON_KEY:
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.rMyGA-hjWQNxJDdLSi3gYtSi8Gg2TeDxAs8f2gx8Zdk',
  SUPABASE_SERVICE_ROLE_KEY:
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVsaXVvaWduenp4bmprY21tdG1sIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NjU0NTYxNCwiZXhwIjoyMDYyMTIxNjE0fQ.hHguPBu7OV6CJBSmwe3r7JwG1Ob__NWt-dWAnRsofP8',

  // Pagamentos - Stripe
  STRIPE_SECRET_KEY:
    '***********************************************************************************************************',
  NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY:
    'pk_live_51RGJ6nRrKLXtzZkMtpujgPAZR4MmRmQQrImSNrq6vdCLe6gfWulXfJDaDl1K2u3DKeKUegsXvzceFVi8xwnwroic00ER63lsVr',
  STRIPE_WEBHOOK_SECRET: 'whsec_U2oN7gw62XH6DKOsGNbuqtbCAYLMDx8U',
  NEXT_PUBLIC_STRIPE_PRICE_MONTHLY: 'price_1RJeZYRrKLXtzZkMf1SS2CRR',
  NEXT_PUBLIC_STRIPE_PRICE_ANNUAL: 'price_1RJecORrKLXtzZkMy1RSRpMV',

  // Inteligência Artificial - Vertex AI
  AI_ENABLED: 'true',
  AI_USE_MOCK: 'false',
  AI_VERTEX_PROJECT_ID: 'excel-copilot',
  AI_VERTEX_LOCATION: 'us-central1',
  AI_VERTEX_MODEL: 'gemini-2.0-flash-001',

  // Cache e Redis
  UPSTASH_REDIS_REST_URL: 'https://cunning-pup-26344.upstash.io',
  UPSTASH_REDIS_REST_TOKEN: 'AWboAAIjcDFkNjhiODgzNTEwMWE0MTQ5ODg0YTFhZDM3NjY5YTlmYXAxMA',
  AI_CACHE_SIZE: '200',
  AI_CACHE_TTL: '7200',
  EXCEL_CACHE_SIZE: '100',
  EXCEL_CACHE_TTL: '1800',
  CACHE_DEFAULT_TTL: '3600',

  // Segurança e validação
  DEV_FORCE_PRODUCTION: 'true',

  // Integrações MCP
  MCP_VERCEL_TOKEN: '************************',
  MCP_VERCEL_PROJECT_ID: 'prj_IQemY1bXbDdiiQmHDfRAYLUqMZIg',
  MCP_VERCEL_TEAM_ID: 'team_BLCIn3CF09teqBeBn8u0fLqp',
  MCP_LINEAR_API_KEY: '************************************************',
  MCP_GITHUB_TOKEN: '****************************************',

  // Monitoramento e observabilidade
  SENTRY_DSN:
    'https://<EMAIL>/4509435346223104',
  SENTRY_ORG: 'ngbprojectsentry',
  SENTRY_PROJECT: 'excel-copilot',
  VERCEL_BUILD_DATABASE_MIGRATION: 'false',
  VERCEL_OIDC_TOKEN:
    '***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************',
};

// Função para fazer requisições HTTP
function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = https.request(options, res => {
      let body = '';
      res.on('data', chunk => (body += chunk));
      res.on('end', () => {
        try {
          const result = JSON.parse(body);
          resolve({ status: res.statusCode, data: result });
        } catch (e) {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });

    req.on('error', reject);

    if (data) {
      req.write(JSON.stringify(data));
    }

    req.end();
  });
}

// Função para criar uma variável de ambiente
async function createEnvVar(key, value, target = ['production', 'preview', 'development']) {
  const options = {
    hostname: 'api.vercel.com',
    path: `/v9/projects/${PROJECT_ID}/env`,
    method: 'POST',
    headers: {
      Authorization: `Bearer ${VERCEL_TOKEN}`,
      'Content-Type': 'application/json',
    },
  };

  const data = {
    key,
    value,
    target,
    type: 'encrypted',
  };

  return makeRequest(options, data);
}

// Função principal
async function main() {
  try {
    console.log('🚀 Configurando variáveis de ambiente do .env.production no Vercel...');
    console.log(`📋 Total de variáveis a configurar: ${Object.keys(ENV_VARS).length}`);

    let successCount = 0;
    let errorCount = 0;

    for (const [key, value] of Object.entries(ENV_VARS)) {
      try {
        console.log(`   Configurando: ${key}`);
        const result = await createEnvVar(key, value);

        if (result.status === 200 || result.status === 201) {
          successCount++;
          console.log(`   ✅ ${key} configurada com sucesso`);
        } else {
          errorCount++;
          console.log(
            `   ❌ Erro ao configurar ${key}: ${result.status} - ${JSON.stringify(result.data)}`
          );
        }

        // Pequena pausa para evitar rate limiting
        await new Promise(resolve => setTimeout(resolve, 200));
      } catch (error) {
        errorCount++;
        console.log(`   ❌ Erro ao configurar ${key}: ${error.message}`);
      }
    }

    console.log(`\n🎉 Configuração concluída!`);
    console.log(`✅ Sucesso: ${successCount}/${Object.keys(ENV_VARS).length} variáveis`);
    console.log(`❌ Erros: ${errorCount}/${Object.keys(ENV_VARS).length} variáveis`);

    if (errorCount === 0) {
      console.log('\n🚀 Todas as variáveis foram configuradas com sucesso!');
      console.log('🔄 Agora você pode fazer deploy da aplicação no Vercel');
    }
  } catch (error) {
    console.error('❌ Erro durante a execução:', error.message);
    process.exit(1);
  }
}

// Executar script
if (require.main === module) {
  main();
}

module.exports = { main };
