/**
 * Excel sheet data structures and types
 * @module SheetTypes
 */

/**
 * Common value types that can appear in Excel cells
 */
export type CellValue = string | number | boolean | Date | null;

/**
 * Chart types supported by the application
 */
export type ChartType = 'bar' | 'line' | 'pie' | 'scatter' | 'area';

/**
 * Comparison operators available for filtering
 */
export type FilterOperator =
  | '='
  | '<>'
  | '<'
  | '<='
  | '>'
  | '>='
  | 'contains'
  | 'notContains'
  | 'startsWith'
  | 'endsWith'
  | 'between'
  | 'notBetween'
  | 'isEmpty'
  | 'isNotEmpty';

/**
 * Basic structure for sheet data
 */
export interface SheetDataFormat {
  headers?: string[];
  rows?: CellValue[][];
  charts?: ChartData[];
  filtered?: boolean;
  filterCriteria?: FilterCriteria;
}

/**
 * Filter criteria for filtering sheet data
 */
export interface FilterCriteria {
  column: string;
  operator: FilterOperator;
  value: CellValue;
  value2?: CellValue;
}

/**
 * Dataset configuration for charts
 */
export interface ChartDataset {
  label: string;
  data: number[];
  backgroundColor: string | string[];
  borderColor: string | string[];
  borderWidth: number;
}

/**
 * Chart configuration and data
 */
export interface ChartData {
  id: string;
  type: ChartType;
  title: string;
  labels: string[];
  datasets: ChartDataset[];
  options?: Record<string, unknown>;
}

/**
 * Table definition within a sheet
 */
export interface TableInfo {
  range: string;
  hasHeaders: boolean;
  name?: string;
  style?: string;
}

/**
 * Pivot table structure and configuration
 */
export interface PivotTableData {
  headers: string[];
  rows: CellValue[][];
  rowsField: string;
  columnsField: string;
  valuesField: string;
  aggregationFunction?: 'sum' | 'average' | 'count' | 'min' | 'max';
}

/**
 * Sheet with complete data structure
 */
export interface SheetWithData {
  id?: string;
  name: string;
  data: {
    headers?: string[];
    rows?: CellValue[][];
    charts?: ChartData[];
    filtered?: boolean;
    filterCriteria?: FilterCriteria;
    formatting?: Record<string, unknown>;
    isTable?: boolean;
    tables?: TableInfo[];
    pivotTable?: PivotTableData;
  };
  createdAt?: Date;
  updatedAt?: Date;
}

/**
 * Workbook containing multiple sheets
 */
export interface WorkbookWithSheets {
  id: string;
  name: string;
  description?: string;
  userId: string;
  sheets: SheetWithData[];
  isPublic: boolean;
  createdAt: Date;
  updatedAt: Date;
  lastAccessedAt?: Date;
  collaborators?: string[];
  version?: number;
}

/**
 * Information about an Excel file
 */
export interface ExcelFileInfo {
  name: string;
  path: string;
  sheetNames: string[];
  activeSheet?: string;
  lastModified: Date;
  size?: number;
  creator?: string;
}
