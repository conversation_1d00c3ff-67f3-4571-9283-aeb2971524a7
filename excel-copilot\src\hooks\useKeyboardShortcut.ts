import { useEffect, useCallback, useMemo, useRef } from 'react';

type _KeyType = string | string[];

export interface UseKeyboardShortcutOptions {
  metaKey?: boolean;
  ctrlKey?: boolean;
  altKey?: boolean;
  shiftKey?: boolean;
  exactMatch?: boolean;
  preventDefault?: boolean;
  enabled?: boolean;
}

/**
 * Hook para gerenciar atalhos de teclado
 * @param keys Array de teclas ou string única com a tecla do atalho
 * @param callback Função a ser executada quando o atalho for pressionado
 * @param options Opções adicionais para o listener
 */
export function useKeyboardShortcut(
  keys: string[] | string,
  callback: () => void,
  options: UseKeyboardShortcutOptions = {},
  _deps: unknown[] = []
) {
  const { enabled = true, preventDefault = true } = options;

  // Referência para verificar se o hook está ativo
  const callbackRef = useRef(callback);

  // Normalizar chaves para um array
  const normalizedKeys = useMemo(() => {
    if (typeof keys === 'string') {
      return [keys.toLowerCase()];
    }
    return keys.map(key => key.toLowerCase());
  }, [keys]);

  // Atualizar a referência quando a callback mudar
  useEffect(() => {
    callbackRef.current = callback;
  }, [callback]);

  // Verificar se todas as teclas do atalho estão pressionadas
  const checkKeysPressed = useCallback(
    (event: KeyboardEvent) => {
      // Ignorar se estamos em um input, textarea ou elemento editável
      if (
        document.activeElement instanceof HTMLInputElement ||
        document.activeElement instanceof HTMLTextAreaElement ||
        (document.activeElement?.getAttribute('contentEditable') === 'true' &&
          !normalizedKeys.includes('ctrl') &&
          !normalizedKeys.includes('meta'))
      ) {
        return false;
      }

      const keyMap: Record<string, boolean> = {
        ctrl: event.ctrlKey,
        shift: event.shiftKey,
        alt: event.altKey,
        meta: event.metaKey,
      };

      // Verificar cada tecla do atalho
      return normalizedKeys.every(key => {
        if (key in keyMap) {
          return keyMap[key];
        }
        // Para teclas normais, verificar event.key
        return event.key.toLowerCase() === key.toLowerCase();
      });
    },
    [normalizedKeys]
  );

  // Adicionar event listener quando o componente montar
  useEffect(() => {
    if (!enabled) return;

    const handleKeyDown = (event: KeyboardEvent) => {
      if (checkKeysPressed(event)) {
        if (preventDefault) {
          event.preventDefault();
        }
        callbackRef.current();
      }
    };

    window.addEventListener('keydown', handleKeyDown);

    // Remover o event listener quando o componente desmontar
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [checkKeysPressed, enabled, preventDefault]);
}
