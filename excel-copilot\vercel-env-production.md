# Configuração de Variáveis de Ambiente para Produção na Vercel

## ⚠️ CORREÇÕES NECESSÁRIAS

### 1. Variáveis que devem ser REMOVIDAS ou ALTERADAS:

```bash
# ❌ REMOVER - Conflito de ambiente
NODE_ENV="development"  # Deixar apenas "production"

# ❌ REMOVER - Não usar em produção
SKIP_AUTH_PROVIDERS="true"  # Deve ser "false" ou removido

# ❌ ALTERAR - URL incorreta
NEXTAUTH_URL="http://localhost:3000"  # Deve ser a URL de produção
```

### 2. Variáveis que devem ser ADICIONADAS/CORRIGIDAS:

```bash
# ✅ Ambiente correto
NODE_ENV="production"

# ✅ URL correta para produção
NEXTAUTH_URL="https://excel-copilot-eight.vercel.app"

# ✅ Autenticação em produção
SKIP_AUTH_PROVIDERS="false"

# ✅ Configurações de segurança
NEXT_PUBLIC_FORCE_PRODUCTION="true"
```

## 📋 CONFIGURAÇÃO COMPLETA RECOMENDADA

### Variáveis de Ambiente para Vercel (Produção):

```bash
# ======================================
# Configuração de Ambiente
# ======================================
NODE_ENV="production"
NEXT_PUBLIC_FORCE_PRODUCTION="true"

# ======================================
# Configuração NextAuth
# ======================================
NEXTAUTH_SECRET="dW5jL4x7Q2tPaDZkVzFqc3pVTEhuMDdYZ0tLbldnRkxRV3hNeUJTRHJSWQ"
NEXTAUTH_URL="https://excel-copilot-eight.vercel.app"

# ======================================
# Configuração de Autenticação
# ======================================
SKIP_AUTH_PROVIDERS="false"
GOOGLE_CLIENT_ID="217111050148-1gocm6a0sa9jcrk8s08dubqn8n2001lv.apps.googleusercontent.com"
GOOGLE_CLIENT_SECRET="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"
GITHUB_CLIENT_ID="********************"
GITHUB_CLIENT_SECRET="7c80b91c934dc9845a8ce7a362581d8ab45f2c3e"

# ======================================
# Configuração de Banco de Dados
# ======================================
DATABASE_URL="postgresql://postgres.eliuoignzzxnjkcmmtml:<EMAIL>:6543/postgres?pgbouncer=true&connection_limit=1&pool_timeout=20"
DIRECT_URL="postgresql://postgres.eliuoignzzxnjkcmmtml:<EMAIL>:5432/postgres"

# ======================================
# Configuração de Segurança
# ======================================
CSRF_SECRET="fZMaY9UO4bXdsFi7lp3gH6kRwEcV2qJ5"
NEXT_PUBLIC_DISABLE_CSRF="false"

# ======================================
# Configuração de IA (Vertex AI)
# ======================================
VERTEX_AI_ENABLED="true"
VERTEX_AI_PROJECT_ID="excel-copilot"
VERTEX_AI_LOCATION="us-central1"
VERTEX_AI_MODEL_NAME="gemini-2.0-flash-001"
VERTEX_AI_CREDENTIALS="[SEU_JSON_DE_CREDENCIAIS_AQUI]"

# ======================================
# Configuração de Pagamentos (Stripe)
# ======================================
STRIPE_SECRET_KEY="***********************************************************************************************************"
STRIPE_WEBHOOK_SECRET="whsec_U2oN7gw62XH6DKOsGNbuqtbCAYLMDx8U"
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY="pk_live_51RGJ6nRrKLXtzZkMtpujgPAZR4MmRmQQrImSNrq6vdCLe6gfWulXfJDaDl1K2u3DKeKUegsXvzceFVi8xwnwroic00ER63lsVr"
NEXT_PUBLIC_STRIPE_PRICE_MONTHLY="price_1RJeZYRrKLXtzZkMf1SS2CRR"
NEXT_PUBLIC_STRIPE_PRICE_ANNUAL="price_1RJecORrKLXtzZkMy1RSRpMV"

# ======================================
# Configuração de Cache (Redis)
# ======================================
UPSTASH_REDIS_REST_URL="https://cunning-pup-26344.upstash.io"
UPSTASH_REDIS_REST_TOKEN="AWboAAIjcDFkNjhiODgzNTEwMWE0MTQ5ODg0YTFhZDM3NjY5YTlmYXAxMA"

# ======================================
# Configuração da Aplicação
# ======================================
APP_NAME="Excel Copilot"
APP_VERSION="0.1.0"
APP_URL="https://excel-copilot-eight.vercel.app"
APP_DESCRIPTION="Seu assistente inteligente para Excel"
```

## 🚀 PASSOS PARA APLICAR AS CORREÇÕES:

### 1. Na Vercel Dashboard:

1. Acesse seu projeto Excel Copilot
2. Vá em Settings > Environment Variables
3. **REMOVA** as variáveis problemáticas:
   - `NODE_ENV="development"`
   - `SKIP_AUTH_PROVIDERS="true"`
   - `NEXTAUTH_URL="http://localhost:3000"`

### 2. **ADICIONE/CORRIJA** as variáveis:

- `NODE_ENV="production"`
- `SKIP_AUTH_PROVIDERS="false"`
- `NEXTAUTH_URL="https://excel-copilot-eight.vercel.app"`

### 3. **REDEPLOY** o projeto:

- Após alterar as variáveis, faça um novo deploy
- Ou force um redeploy na aba Deployments

## 🔍 VERIFICAÇÃO:

Após aplicar as correções, teste:

1. Acesse `https://excel-copilot-eight.vercel.app`
2. Verifique se não há mais erros 500 no console
3. Teste o login com Google/GitHub
4. Verifique se `/api/auth/session` retorna 200

## ⚡ CORREÇÕES APLICADAS NO CÓDIGO:

1. **Cookies NextAuth**: Removido `domain: '.vercel.app'` e `sameSite: 'none'`
2. **Provedores OAuth**: Garantido que em produção sempre use OAuth
3. **CSRF Token**: Corrigida geração de token
4. **TrustHost**: Adicionado `trustHost: true` para Vercel
