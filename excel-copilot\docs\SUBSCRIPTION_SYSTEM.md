# Sistema de Planos de Assinatura - Excel Copilot

## Visão Geral

O Excel Copilot implementa um sistema robusto de planos de assinatura com três níveis: **Free**, **Pro Monthly** e **Pro Annual**. Este documento detalha a implementação completa, incluindo as correções críticas implementadas.

## Planos Disponíveis

### 🆓 **Plano Free**

- **Preço**: Gratuito para sempre
- **Workbooks**: Máximo 5 por usuário
- **Células por planilha**: Máximo 1.000
- **Gráficos por planilha**: Máximo 1
- **Comandos avançados de IA**: ❌ Não disponível
- **API calls por mês**: 50
- **Rate limiting**: 30 requisições/minuto

### 💼 **Plano Pro Monthly**

- **Preço**: R$ 20/mês
- **Workbooks**: Ilimitado
- **Células por planilha**: Máximo 50.000
- **Gráficos por planilha**: Ilimitado
- **Comandos avançados de IA**: ✅ Disponível
- **API calls por mês**: 500
- **Rate limiting**: 120 requisições/minuto

### 💎 **Plano Pro Annual**

- **Preço**: R$ 200/ano (R$ 16,67/mês)
- **Workbooks**: Ilimitado
- **Células por planilha**: Ilimitado
- **Gráficos por planilha**: Ilimitado
- **Comandos avançados de IA**: ✅ Disponível
- **API calls por mês**: 1.000
- **Rate limiting**: 240 requisições/minuto

## Arquitetura do Sistema

### Estrutura de Dados

```typescript
// Modelo Subscription no Prisma
model Subscription {
  id                String   @id @default(cuid())
  userId            String
  user              User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  stripeCustomerId  String?
  stripeSubscriptionId String?  @unique
  stripePriceId     String?
  status            String   @default("active")
  plan              String
  cancelAtPeriodEnd Boolean  @default(false)
  currentPeriodStart DateTime?
  currentPeriodEnd   DateTime?
  apiCallsLimit     Int      @default(50)
  apiCallsUsed      Int      @default(0)
  payments          Payment[]
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
}
```

### Constantes de Planos

```typescript
// src/lib/stripe.ts
export const PLANS = {
  FREE: 'free',
  PRO_MONTHLY: 'pro_monthly',
  PRO_ANNUAL: 'pro_annual',
};

export const API_CALL_LIMITS = {
  [PLANS.FREE]: 50,
  [PLANS.PRO_MONTHLY]: 500,
  [PLANS.PRO_ANNUAL]: 1000,
};
```

## Correções Implementadas

### ✅ **1. Atribuição Automática de Plano Free**

**Problema**: Novos usuários não recebiam automaticamente uma assinatura Free.

**Solução**: Implementado callback `signIn` no NextAuth.js:

```typescript
// src/server/auth/options.ts
async signIn({ user, account, profile, isNewUser }) {
  if (isNewUser && user.id) {
    // Verificar se já existe uma assinatura
    const existingSubscription = await prisma.subscription.findFirst({
      where: { userId: user.id },
    });

    if (!existingSubscription) {
      // Criar assinatura Free automaticamente
      await prisma.subscription.create({
        data: {
          userId: user.id,
          plan: PLANS.FREE,
          status: 'active',
          apiCallsLimit: API_CALL_LIMITS[PLANS.FREE] || 50,
          apiCallsUsed: 0,
          currentPeriodStart: new Date(),
          cancelAtPeriodEnd: false,
        },
      });
    }
  }
  return true;
}
```

### ✅ **2. Script de Migração para Usuários Existentes**

**Arquivo**: `scripts/migrate-users-to-free-plan.ts`

**Funcionalidades**:

- Identifica usuários sem assinatura
- Cria assinaturas Free em lotes
- Validação de integridade
- Relatório detalhado de migração
- Prevenção de duplicações

**Uso**:

```bash
npm run migrate:free-plan
# ou
npm run migrate:free-plan:js
```

### ✅ **3. Validação de Integridade Aprimorada**

**Melhorias na função `getUserSubscriptionPlan`**:

- Verificação de existência do usuário
- Criação automática de assinatura Free se necessário
- Logs detalhados para monitoramento
- Fallbacks seguros em caso de erro

### ✅ **4. Rate Limiting Baseado em Plano**

**Arquivo**: `src/lib/middleware/plan-based-rate-limiter.ts`

**Funcionalidades**:

- Rate limiting específico por plano
- Diferentes janelas de tempo (minuto, hora, dia)
- Headers informativos de rate limiting
- Tratamento especial para usuários anônimos

### ✅ **5. Endpoint de Integridade Administrativa**

**Endpoint**: `/api/admin/subscription-integrity`

**Funcionalidades**:

- **GET**: Relatório de integridade completo
- **POST**: Correção automática de problemas
- Identificação de inconsistências
- Recomendações de correção

## Fluxo de Atribuição de Planos

### Para Novos Usuários

1. **Login/Registro** → NextAuth.js detecta `isNewUser: true`
2. **Callback signIn** → Verifica se já existe assinatura
3. **Criação Automática** → Cria assinatura Free se necessário
4. **Log de Auditoria** → Registra criação para monitoramento

### Para Usuários Existentes

1. **Verificação de Plano** → `getUserSubscriptionPlan(userId)`
2. **Busca Assinatura Ativa** → Prisma query com filtros de status
3. **Validação de Integridade** → Verifica se usuário existe
4. **Correção Automática** → Cria assinatura Free se necessário
5. **Cache de Plano** → Armazena resultado para performance

## Sistema de Verificações de Permissões

### Criação de Workbooks

```typescript
export async function canCreateWorkbook(userId: string) {
  const userPlan = await getUserSubscriptionPlan(userId);
  const limit = PLAN_LIMITS.MAX_WORKBOOKS[userPlan];
  const currentCount = await prisma.workbook.count({ where: { userId } });

  return {
    allowed: currentCount < limit,
    currentCount,
    limit,
  };
}
```

### Adição de Gráficos

```typescript
export async function canAddChart(userId: string, sheetId: string, currentChartCount: number) {
  const userPlan = await getUserSubscriptionPlan(userId);
  const limit = PLAN_LIMITS.MAX_CHARTS[userPlan];

  return {
    allowed: currentChartCount < limit,
    limit,
  };
}
```

## Middleware de Segurança

### Rate Limiting por Plano

```typescript
// src/middleware.ts
const needsPlanBasedLimiting = API_ROUTES_WITH_PLAN_LIMITS.some(route =>
  pathname.startsWith(route)
);

if (needsPlanBasedLimiting) {
  const planLimitResult = await planBasedRateLimiter(request, 'minute');
  if (planLimitResult) {
    return planLimitResult; // Retorna 429 se limite excedido
  }
}
```

### Rotas Protegidas

- `/dashboard` - Requer autenticação
- `/workbook` - Requer autenticação + verificação de plano
- `/api/workbooks` - Rate limiting baseado em plano
- `/api/ai` - Verificação de comandos avançados de IA

## Monitoramento e Logs

### Eventos Logados

- `[AUTH] Novo usuário detectado`
- `[AUTH] Assinatura Free criada automaticamente`
- `[SUBSCRIPTION_INTEGRITY] Usuário sem assinatura`
- `[SUBSCRIPTION_AUTO_FIX] Criando assinatura Free`
- `[RATE_LIMIT_EXCEEDED] Limite excedido`

### Métricas de Integridade

- Total de usuários
- Usuários com/sem assinatura
- Inconsistências detectadas
- Estatísticas por plano

## Scripts de Manutenção

### Verificar Integridade

```bash
npm run subscription:integrity
```

### Corrigir Problemas

```bash
npm run subscription:fix
```

### Executar Testes

```bash
npm run test:subscription
```

## Integração com Stripe

### Webhooks

- `checkout.session.completed` → Cria/atualiza assinatura
- `invoice.payment_succeeded` → Confirma pagamento
- `customer.subscription.updated` → Atualiza status
- `customer.subscription.deleted` → Cancela assinatura

### Fallbacks de Segurança

- Verificação de expiração de assinatura
- Validação de status do Stripe
- Logs de eventos de pagamento
- Recuperação automática de falhas

## Comandos Úteis

### Desenvolvimento

```bash
# Executar migração de usuários para plano Free
npm run migrate:free-plan

# Verificar integridade das assinaturas
npm run subscription:integrity

# Corrigir problemas automaticamente
npm run subscription:fix

# Executar testes do sistema de assinaturas
npm run test:subscription

# Verificar tipos TypeScript
npm run type-check
```

### Produção

```bash
# Migração segura em produção
NODE_ENV=production npm run migrate:free-plan

# Backup antes de migração
npm run db:backup

# Verificar saúde do banco
npm run health:db:prod
```

## Troubleshooting

### Problema: Usuário sem assinatura

**Sintomas**: Usuário não consegue criar workbooks, erro de plano não encontrado

**Solução**:

1. Verificar se o usuário existe: `SELECT * FROM "User" WHERE id = 'user-id'`
2. Executar correção automática: `npm run subscription:fix`
3. Verificar logs: `[SUBSCRIPTION_AUTO_FIX]`

### Problema: Rate limit excedido

**Sintomas**: Erro 429, headers `X-RateLimit-*`

**Solução**:

1. Verificar plano do usuário
2. Aguardar reset do rate limit
3. Considerar upgrade de plano

### Problema: Múltiplas assinaturas ativas

**Sintomas**: Relatório de integridade mostra duplicações

**Solução**:

1. Executar relatório: `npm run subscription:integrity`
2. Revisar manualmente as duplicações
3. Consolidar assinaturas via admin

## Próximos Passos

1. **Implementar dashboard administrativo** para monitoramento
2. **Adicionar métricas de uso** por plano
3. **Implementar alertas** para problemas de integridade
4. **Otimizar cache** de planos com Redis
5. **Adicionar testes E2E** para fluxo completo

---

**Documentação atualizada em**: Janeiro 2025
**Versão do sistema**: 1.0.0
**Status**: ✅ Implementado e testado
