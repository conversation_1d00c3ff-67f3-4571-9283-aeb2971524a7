import { NextResponse } from 'next/server';

import { logger } from '@/lib/logger';
import { WithOptionalFields } from '@/types/optional-types';

type ApiErrorResponse = {
  code: string;
  message: string;
  details?: unknown;
  timestamp: string;
};

type ApiSuccessResponse<T> = {
  data: T;
  meta?: Record<string, unknown>;
};

// Tipo para dados que podem vir do Prisma com propriedades opcionais
export type ApiCompatibleData<T> = WithOptionalFields<T>;

export const ApiResponse = {
  success<T>(data: T, meta?: Record<string, unknown>, status = 200) {
    const response: ApiSuccessResponse<T> = {
      data,
      ...(meta && { meta }),
    };
    return NextResponse.json(response, { status });
  },

  error(message: string, code = 'INTERNAL_ERROR', status = 500, details?: unknown) {
    const error: ApiErrorResponse = {
      code,
      message,
      timestamp: new Date().toISOString(),
      ...(details !== undefined && { details }),
    };

    logger.error(`API Error [${code}]: ${message}`, { details });
    return NextResponse.json(error, { status });
  },

  unauthorized(message = 'Não autorizado', details?: unknown) {
    return this.error(message, 'UNAUTHORIZED', 401, details);
  },

  badRequest(message: string, details?: unknown) {
    return this.error(message, 'BAD_REQUEST', 400, details);
  },

  notFound(message = 'Recurso não encontrado', details?: unknown) {
    return this.error(message, 'NOT_FOUND', 404, details);
  },

  forbidden(message = 'Acesso negado', details?: unknown) {
    return this.error(message, 'FORBIDDEN', 403, details);
  },

  tooManyRequests(
    message = 'Muitas requisições. Tente novamente mais tarde.',
    retryAfterSeconds?: number
  ) {
    const headers: HeadersInit = {};
    if (retryAfterSeconds) {
      headers['Retry-After'] = retryAfterSeconds.toString();
    }

    return NextResponse.json(
      {
        code: 'RATE_LIMIT_EXCEEDED',
        message,
        timestamp: new Date().toISOString(),
      },
      {
        status: 429,
        headers,
      }
    );
  },
};
