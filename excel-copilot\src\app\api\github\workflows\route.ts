import { NextRequest } from 'next/server';

import { GitHubClient, GitHubMonitoringService } from '@/lib/github-integration';
import { logger } from '@/lib/logger';
import { ApiResponse } from '@/utils/api-response';

// Forçar o modo dinâmico para essa rota
export const dynamic = 'force-dynamic';

/**
 * GET /api/github/workflows
 * Lista workflow runs e métricas de CI/CD
 */
export async function GET(request: NextRequest) {
  try {
    // Verificar se temos as credenciais necessárias
    const token = process.env.MCP_GITHUB_TOKEN;
    const defaultOwner = process.env.MCP_GITHUB_OWNER;
    const defaultRepo = process.env.MCP_GITHUB_REPO;

    if (!token) {
      return ApiResponse.error('GITHUB_TOKEN não configurado', 'GITHUB_NOT_CONFIGURED', 500);
    }

    // Obter parâmetros da query
    const { searchParams } = new URL(request.url);
    const owner = searchParams.get('owner') || defaultOwner;
    const repo = searchParams.get('repo') || defaultRepo;
    const workflow_id = searchParams.get('workflow_id');
    const actor = searchParams.get('actor');
    const branch = searchParams.get('branch');
    const event = searchParams.get('event');
    const status = searchParams.get('status') as 'queued' | 'in_progress' | 'completed';
    const per_page = parseInt(searchParams.get('per_page') || '30');
    const page = parseInt(searchParams.get('page') || '1');
    const includeMetrics = searchParams.get('include_metrics') === 'true';

    if (!owner || !repo) {
      return ApiResponse.badRequest('owner e repo são obrigatórios');
    }

    // Validar parâmetros
    if (per_page < 1 || per_page > 100) {
      return ApiResponse.badRequest('Parâmetro per_page deve estar entre 1 e 100');
    }

    if (page < 1) {
      return ApiResponse.badRequest('Parâmetro page deve ser maior que 0');
    }

    // Criar cliente GitHub
    const githubClient = new GitHubClient({ token });

    // Obter workflow runs
    const workflowRuns = await githubClient.getWorkflowRuns({
      owner,
      repo,
      ...(workflow_id && { workflow_id }),
      ...(actor && { actor }),
      ...(branch && { branch }),
      ...(event && { event }),
      ...(status && { status }),
      per_page,
      page,
    });

    // Formatar workflow runs
    const formattedWorkflowRuns = workflowRuns.workflowRuns.map(run => ({
      id: run.id,
      name: run.name,
      headBranch: run.head_branch,
      headSha: run.head_sha,
      status: run.status,
      conclusion: run.conclusion,
      workflowId: run.workflow_id,
      htmlUrl: run.html_url,
      createdAt: run.created_at,
      updatedAt: run.updated_at,
      runStartedAt: run.run_started_at,
      jobsUrl: run.jobs_url,
      logsUrl: run.logs_url,
      checkSuiteUrl: run.check_suite_url,
      artifactsUrl: run.artifacts_url,
      cancelUrl: run.cancel_url,
      rerunUrl: run.rerun_url,
      workflowUrl: run.workflow_url,
      pullRequests: run.pull_requests.map(pr => ({
        id: pr.id,
        number: pr.number,
        url: pr.url,
        head: {
          ref: pr.head.ref,
          sha: pr.head.sha,
        },
        base: {
          ref: pr.base.ref,
          sha: pr.base.sha,
        },
      })),
    }));

    let cicdMetrics = null;
    if (includeMetrics) {
      try {
        const githubService = new GitHubMonitoringService({ token });
        cicdMetrics = await githubService.getCICDMetrics(owner, repo);
      } catch (error) {
        logger.warn('Erro ao obter métricas de CI/CD:', error);
      }
    }

    const response = {
      repository: `${owner}/${repo}`,
      workflowRuns: formattedWorkflowRuns,
      pagination: {
        page,
        perPage: per_page,
        total: workflowRuns.total,
        hasNext: formattedWorkflowRuns.length === per_page,
      },
      filters: {
        workflowId: workflow_id,
        actor,
        branch,
        event,
        status,
      },
      metrics: cicdMetrics
        ? {
            totalRuns: cicdMetrics.totalRuns,
            successRate: cicdMetrics.successRate,
            averageDuration: cicdMetrics.averageDuration,
            recentFailures: cicdMetrics.recentFailures.length,
            trends: cicdMetrics.trendsLast30Days,
          }
        : null,
      timestamp: new Date().toISOString(),
    };

    logger.info('Workflow runs GitHub obtidos com sucesso', {
      repository: `${owner}/${repo}`,
      count: formattedWorkflowRuns.length,
      includeMetrics,
    });

    return ApiResponse.success(response);
  } catch (error) {
    logger.error('Erro ao obter workflow runs do GitHub', { error });

    if (error instanceof Error) {
      return ApiResponse.error(
        `Erro ao conectar com GitHub: ${error.message}`,
        'GITHUB_API_ERROR',
        500
      );
    }

    return ApiResponse.error('Erro interno do servidor', 'INTERNAL_ERROR', 500);
  }
}

/**
 * POST /api/github/workflows
 * Obtém métricas detalhadas de CI/CD
 */
export async function POST(request: NextRequest) {
  try {
    const token = process.env.MCP_GITHUB_TOKEN;

    if (!token) {
      return ApiResponse.error('GITHUB_TOKEN não configurado', 'GITHUB_NOT_CONFIGURED', 500);
    }

    // Obter dados do body
    const body = await request.json();
    const { owner, repo, days = 30 } = body;

    if (!owner || !repo) {
      return ApiResponse.badRequest('owner e repo são obrigatórios');
    }

    if (days < 1 || days > 90) {
      return ApiResponse.badRequest('Parâmetro days deve estar entre 1 e 90');
    }

    // Criar serviço de monitoramento
    const githubService = new GitHubMonitoringService({ token });

    // Obter métricas detalhadas
    const cicdMetrics = await githubService.getCICDMetrics(owner, repo);

    // Obter dashboard do repositório para contexto adicional
    const dashboard = await githubService.getRepositoryDashboard(owner, repo);

    const response = {
      repository: `${owner}/${repo}`,
      period: `${days} days`,
      metrics: {
        totalRuns: cicdMetrics.totalRuns,
        successRate: cicdMetrics.successRate,
        averageDuration: cicdMetrics.averageDuration,
        recentFailures: cicdMetrics.recentFailures.map(failure => ({
          id: failure.id,
          name: failure.name,
          headBranch: failure.head_branch,
          headSha: failure.head_sha,
          conclusion: failure.conclusion,
          htmlUrl: failure.html_url,
          createdAt: failure.created_at,
          updatedAt: failure.updated_at,
        })),
        trends: cicdMetrics.trendsLast30Days,
      },
      repository_health: dashboard.healthStatus,
      summary: {
        status: dashboard.healthStatus,
        openIssues: dashboard.openIssues,
        openPullRequests: dashboard.openPullRequests,
        lastUpdate: dashboard.repository.updated_at,
      },
      timestamp: new Date().toISOString(),
    };

    logger.info('Métricas detalhadas de CI/CD obtidas com sucesso', {
      repository: `${owner}/${repo}`,
      totalRuns: cicdMetrics.totalRuns,
      successRate: cicdMetrics.successRate,
    });

    return ApiResponse.success(response);
  } catch (error) {
    logger.error('Erro ao obter métricas de CI/CD do GitHub', { error });

    if (error instanceof Error) {
      return ApiResponse.error(
        `Erro ao conectar com GitHub: ${error.message}`,
        'GITHUB_API_ERROR',
        500
      );
    }

    return ApiResponse.error('Erro interno do servidor', 'INTERNAL_ERROR', 500);
  }
}
