#!/usr/bin/env ts-node
/**
 * Script para testar a persistência de dados no banco de dados
 *
 * Execução: ts-node scripts/test-persistence.ts
 */

import { PrismaClient } from '@prisma/client';
import { executeExcelOperations } from '../src/lib/excel';
import { ExcelAIProcessor } from '../src/lib/ai/ExcelAIProcessor';

// Cliente Prisma para acesso ao banco de dados
const prisma = new PrismaClient();

/**
 * Função principal
 */
async function main() {
  console.log('=== TESTE DE PERSISTÊNCIA NO BANCO DE DADOS ===\n');

  try {
    // 1. Verificar conexão com o banco de dados
    console.log('Verificando conexão com o banco de dados...');

    // Tentativa de contagem de registros
    const workbookCount = await prisma.workbook.count();
    console.log(`✅ Conexão OK. Existem ${workbookCount} workbooks no banco de dados.`);

    // 2. Criar workbook para testes se não existir
    console.log('\nCriando ou verificando workbook de teste...');

    const workbookName = 'Workbook de Teste de Persistência';
    let testWorkbook = await prisma.workbook.findFirst({
      where: {
        name: workbookName,
      },
      include: {
        sheets: true,
      },
    });

    if (!testWorkbook) {
      // Dados iniciais para teste
      const initialData = {
        headers: ['Produto', 'Categoria', 'Preço', 'Estoque', 'Vendas'],
        rows: [
          ['Produto A', 'Eletrônicos', 1500, 20, 8],
          ['Produto B', 'Informática', 800, 35, 15],
          ['Produto C', 'Eletrônicos', 2000, 15, 5],
          ['Produto D', 'Escritório', 50, 200, 120],
          ['Produto E', 'Informática', 1200, 40, 25],
        ],
      };

      testWorkbook = await prisma.workbook.create({
        data: {
          name: workbookName,
          userId: 'test-user',
          sheets: {
            create: {
              name: 'Produtos',
              data: JSON.stringify(initialData),
            },
          },
        },
        include: {
          sheets: true,
        },
      });

      console.log(`✅ Novo workbook criado com ID: ${testWorkbook.id}`);
    } else {
      console.log(`✅ Usando workbook existente com ID: ${testWorkbook.id}`);
    }

    // Verificar se o workbook tem pelo menos uma planilha
    if (!testWorkbook.sheets || testWorkbook.sheets.length === 0) {
      throw new Error('O workbook não tem nenhuma planilha');
    }

    // 3. Ler dados da planilha para testes
    const sheet = testWorkbook.sheets[0];
    if (!sheet) {
      throw new Error('Planilha não encontrada no workbook');
    }

    const sheetData = JSON.parse(sheet.data as string);
    console.log('\nDados da planilha:');
    console.log(`- Colunas: ${sheetData.headers.join(', ')}`);
    console.log(`- Linhas: ${sheetData.rows.length}`);

    // 4. Criar operações de teste
    console.log('\nCriando operações de teste...');

    // Inicializar processador de IA para gerar operações
    const aiProcessor = new ExcelAIProcessor(
      {
        activeSheet: sheet.name,
        headers: sheetData.headers,
        selection: 'A1',
        recentOperations: [],
      },
      true
    ); // true ativa o modo de teste

    // Processar alguns comandos de exemplo
    const testCommands = [
      'Ordenar produtos por preço de forma decrescente',
      'Calcular o valor total em estoque (preço * estoque)',
      'Adicionar coluna de receita (preço * vendas)',
      'Criar gráfico comparando vendas por categoria',
    ];

    let currentData = sheetData;

    for (const command of testCommands) {
      console.log(`\nProcessando comando: "${command}"`);

      // Gerar operações para o comando
      const result = await aiProcessor.processQuery(command);

      if (result.success && result.operations.length > 0) {
        console.log(`✅ Comando gerou ${result.operations.length} operação(ões)`);

        try {
          // Executar operações
          const execResult = await executeExcelOperations(currentData, result.operations);
          console.log(`✅ Operações executadas: ${execResult.resultSummary}`);

          // Atualizar dados para próxima operação
          currentData = execResult.updatedData;

          // Persistir no banco de dados
          await prisma.sheet.update({
            where: {
              id: sheet.id,
            },
            data: {
              data: JSON.stringify(currentData),
              updatedAt: new Date(),
            },
          });

          console.log('✅ Dados persistidos no banco de dados');

          // Verificar persistência
          const updatedSheet = await prisma.sheet.findUnique({
            where: {
              id: sheet.id,
            },
          });

          if (updatedSheet) {
            const updatedData = JSON.parse(updatedSheet.data as string);
            console.log('✅ Verificação de persistência OK');

            // Verificar se os headers foram preservados
            if (JSON.stringify(updatedData.headers) !== JSON.stringify(currentData.headers)) {
              console.log(
                '❌ Aviso: Os cabeçalhos podem ter sido alterados durante a persistência'
              );
            }

            // Verificar se o número de linhas está correto
            if (updatedData.rows.length !== currentData.rows.length) {
              console.log(
                '❌ Aviso: O número de linhas pode ter sido alterado durante a persistência'
              );
            }
          } else {
            console.log('❌ Não foi possível verificar a persistência, planilha não encontrada');
          }
        } catch (error) {
          console.error('❌ Erro ao executar operações:', error);
        }
      } else {
        console.log(`❌ Comando não gerou operações válidas: ${result.message}`);
      }
    }

    // 5. Criar e salvar histórico de operações
    console.log('\n=== TESTANDO HISTÓRICO DE OPERAÇÕES ===');

    try {
      // Usar ChatHistory no lugar de OperationHistory que não existe no schema
      await prisma.chatHistory.create({
        data: {
          userId: 'test-user',
          workbookId: testWorkbook.id,
          message: 'Teste de persistência de múltiplas operações',
          response: JSON.stringify({
            commands: testCommands,
            timestamp: new Date().toISOString(),
            results: 'Operações executadas com sucesso',
          }),
        },
      });

      console.log('✅ Histórico de operações salvo no banco de dados (via ChatHistory)');

      // Verificar histórico
      const history = await prisma.chatHistory.findMany({
        where: {
          workbookId: testWorkbook.id,
        },
        orderBy: {
          createdAt: 'desc',
        },
        take: 5,
      });

      console.log(`✅ Histórico recuperado: ${history.length} operações recentes`);

      for (const entry of history) {
        console.log(`- ${entry.createdAt}: ${entry.message}`);
      }
    } catch (error) {
      console.error('❌ Erro ao salvar histórico de operações:', error);
    }

    console.log('\n=== TESTE DE PERSISTÊNCIA CONCLUÍDO ===');
  } catch (error) {
    console.error('❌ Erro no teste de persistência:', error);
  } finally {
    // Fechar conexão com o banco de dados
    await prisma.$disconnect();
  }
}

// Executar função principal
main().catch(console.error);
