import { Loader2 } from 'lucide-react';

interface LoadingIndicatorProps {
  size?: 'sm' | 'md' | 'lg';
  message?: string;
}

/**
 * Componente de indicador de carregamento para uso com Suspense
 * Fornece feedback visual durante carregamento de componentes
 */
export function LoadingIndicator({
  size = 'md',
  message = 'Carregando...',
}: LoadingIndicatorProps) {
  // Mapeamento de tamanhos para valores em pixels
  const sizeMap = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-[100px] py-8 px-4">
      <Loader2 className={`${sizeMap[size]} animate-spin text-primary mb-3`} />
      {message && <p className="text-muted-foreground text-sm font-medium">{message}</p>}
    </div>
  );
}
