/**
 * Utilitários para tratamento de erros e conversão de tipos
 * Este arquivo fornece funções para trabalhar com erros de forma type-safe
 */

import { logger, LogMetadata } from '../logger';

/**
 * Converte um erro de tipo desconhecido para Error ou undefined.
 * Útil para garantir type-safety ao passar erros para funções que esperam Error.
 *
 * @param error Erro de tipo desconhecido
 * @returns O erro convertido para Error ou undefined se não for possível converter
 */
export function toError(error: unknown): Error | undefined {
  if (error instanceof Error) {
    return error;
  }
  if (error === null || error === undefined) {
    return undefined;
  }
  // Se for uma string, cria um novo Error com a mensagem
  if (typeof error === 'string') {
    return new Error(error);
  }
  // Para outros tipos, tenta criar um erro com a representação em string
  try {
    const errorMessage = JSON.stringify(error);
    return new Error(errorMessage);
  } catch {
    return new Error('Unknown error');
  }
}

/**
 * Converte um valor desconhecido para LogMetadata ou undefined.
 * Útil para garantir type-safety ao passar metadados para funções de log.
 *
 * @param data Dados de tipo desconhecido
 * @returns Os dados convertidos para LogMetadata ou undefined se não for possível converter
 */
export function toMetadata(data: unknown): LogMetadata | undefined {
  if (data === null || data === undefined) {
    return undefined;
  }
  if (typeof data === 'object') {
    return data as Record<string, unknown>;
  }
  // Para tipos primitivos, cria um objeto com uma propriedade "value"
  return { value: data };
}

/**
 * Registra um erro no sistema de log com tratamento adequado de tipos
 *
 * @param message Mensagem de erro
 * @param error Erro de tipo desconhecido
 * @param metadata Metadados adicionais opcionais
 */
export function logError(
  message: string,
  error: unknown,
  metadata?: Record<string, unknown>
): void {
  const errorObj = toError(error);
  const _combinedMetadata = metadata ? { ...metadata, originalError: errorObj } : undefined;
  logger.error(message, errorObj);
}

/**
 * Registra um aviso no sistema de log com tratamento adequado de tipos
 *
 * @param message Mensagem de aviso
 * @param data Dados adicionais de tipo desconhecido
 * @param metadata Metadados adicionais opcionais
 */
export function logWarning(
  message: string,
  data: unknown,
  metadata?: Record<string, unknown>
): void {
  const dataMetadata = toMetadata(data);
  const combinedMetadata = metadata ? { ...metadata, ...dataMetadata } : dataMetadata;
  logger.warn(message, combinedMetadata);
}
