// CORREÇÃO: Usar versão segura para o cliente
import { getGeminiServiceAPI } from '@/lib/ai/client-safe';

import { logger } from '../logger';

/**
 * NOTA: Existem alguns avisos de TypeScript relacionados a incompatibilidades
 * entre definições de tipos em diferentes arquivos. Isso ocorre porque o enum
 * ExcelOperationType está definido em mais de um lugar com valores ligeiramente
 * diferentes.
 *
 * Uma solução definitiva seria:
 * 1. Unificar todas as definições de ExcelOperationType em um único arquivo
 * 2. Atualizar todas as importações para usar essa definição unificada
 * 3. Garantir que todos os mapeamentos de string para enum usem os valores corretos
 *
 * Por enquanto, alguns desses erros são suprimidos com casts de tipo.
 */

// Logger específico para o processamento de IA
const _aiProcessorLogger = {
  info: (message: string, data?: unknown) => {
    logger.info(`[AI Processor] ${message}`, data as Record<string, any> | undefined);
  },
  warn: (message: string, data?: unknown) => {
    logger.warn(`[AI Processor] ${message}`, data as Record<string, any> | undefined);
  },
  error: (message: string, data?: unknown) => {
    logger.error(`[AI Processor] ${message}`, data instanceof Error ? data : undefined);
  },
  debug: (message: string, data?: unknown) => {
    logger.debug(`[AI Processor] ${message}`, data as Record<string, any> | undefined);
  },
};

// Interface para definir o contexto de processamento da IA
export interface AIProcessingContext {
  activeSheet?: string;
  headers?: string[];
  selection?: string;
  recentOperations?: string[];
}

// Interface para os resultados do processamento
export interface AIProcessingResult {
  operations: Array<{
    type: string;
    data: any;
    description?: string;
  }>;
  explanation?: string;
  error?: string;
  success: boolean;
  message?: string;
}

/**
 * Classe responsável por processar comandos em linguagem natural para operações Excel
 */
export class ExcelAIProcessor {
  private context: AIProcessingContext;
  private useRealAI: boolean;

  /**
   * Construtor da classe
   * @param context Contexto opcional para processamento
   * @param useRealAI Flag para usar IA real ou simulada
   */
  constructor(context: Partial<AIProcessingContext> = {}, useRealAI = true) {
    this.context = {
      activeSheet: context.activeSheet || 'Sheet1',
      headers: context.headers || [],
      selection: context.selection || 'A1',
      recentOperations: context.recentOperations || [],
    };

    this.useRealAI = useRealAI;
  }

  /**
   * Processa uma consulta de linguagem natural para operações Excel
   * @param query Consulta do usuário em linguagem natural
   * @returns Resultado do processamento com operações a serem executadas
   */
  async processQuery(query: string): Promise<AIProcessingResult> {
    try {
      // Pré-processar query
      const processedQuery = this.preprocessQuery(query);

      // Construir prompt para a IA com o contexto
      const prompt = this.buildPrompt(processedQuery);

      // CORREÇÃO: Usar versão segura do serviço
      const geminiService = await getGeminiServiceAPI();

      // Enviar para a IA
      const aiResponse = await geminiService.sendMessage(prompt, {
        context: JSON.stringify(this.context),
        useMock: !this.useRealAI,
      });

      // Processar resposta
      return this.parseAIResponse(aiResponse);
    } catch (error) {
      console.error('Erro ao processar query:', error);

      return {
        operations: [],
        error: `Erro ao processar: ${error instanceof Error ? error.message : String(error)}`,
        success: false,
        message: 'Falha ao processar query com IA',
      };
    }
  }

  /**
   * Pré-processa a query do usuário para melhorar resultados
   * @param query Query original
   * @returns Query pré-processada
   */
  private preprocessQuery(query: string): string {
    // Substituir abreviações comuns
    return query
      .replace(/\bform\./g, 'fórmula')
      .replace(/\bcol\./g, 'coluna')
      .replace(/\btab\./g, 'tabela')
      .replace(/\bgraf\./g, 'gráfico')
      .replace(/\bcel\./g, 'célula')
      .replace(/\bfunc\./g, 'função')
      .replace(/\bop\./g, 'operação')
      .replace(/\bval\./g, 'valor')
      .replace(/\bmed\./g, 'média');
  }

  /**
   * Constrói um prompt para a IA com base na query e contexto
   * @param query Query pré-processada
   * @returns Prompt para a IA
   */
  private buildPrompt(query: string): string {
    return `
    Analise o seguinte comando para Excel e retorne as operações necessárias em formato JSON:
    
    Comando: "${query}"
    
    Contexto da planilha:
    - Planilha ativa: ${this.context.activeSheet}
    - Seleção atual: ${this.context.selection}
    - Cabeçalhos: ${this.context.headers?.join(', ') || 'N/A'}
    
    Retorne APENAS um objeto JSON com a seguinte estrutura:
    {
      "operations": [
        {
          "type": "TIPO_OPERACAO", // FORMULA, CHART, TABLE, FORMAT, etc.
          "data": { ... }, // Dados específicos da operação
          "description": "Descrição" // Opcional, descrição da operação
        }
      ],
      "explanation": "Explicação do que foi feito" // Opcional
    }
    `;
  }

  /**
   * Analisa a resposta da IA e converte para o formato de resultado esperado
   * @param aiResponse Resposta bruta da IA
   * @returns Resultado processado
   */
  private parseAIResponse(aiResponse: string): AIProcessingResult {
    try {
      // Tentar extrair JSON da resposta
      const jsonMatch = aiResponse.match(/\{[\s\S]*\}/);

      if (jsonMatch) {
        const jsonStr = jsonMatch[0];
        const parsed = JSON.parse(jsonStr);

        // Validar formato básico
        if (!parsed.operations || !Array.isArray(parsed.operations)) {
          throw new Error('Formato de resposta inválido: operations não é um array');
        }

        return parsed;
      }

      // Se não encontrar JSON, retornar um formato básico
      return {
        operations: [
          {
            type: 'TABLE',
            data: { rawResponse: aiResponse },
            description: `Resposta em texto: ${aiResponse.substring(0, 100)}...`,
          },
        ],
        explanation: 'A resposta não pôde ser processada como JSON',
        success: true,
        message: 'Processamento parcial realizado',
      };
    } catch (error) {
      console.error('Erro ao analisar resposta da IA:', error);

      // Retornar resposta de fallback
      return {
        operations: [
          {
            type: 'TABLE',
            data: { error: true },
            description: `Processando: "${aiResponse.substring(0, 100)}..."`,
          },
        ],
        explanation: 'Erro ao processar JSON da resposta',
        error: String(error),
        success: false,
        message: 'Falha ao analisar resposta da IA',
      };
    }
  }
}
