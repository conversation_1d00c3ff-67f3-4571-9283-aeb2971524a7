import { useState, useEffect, createContext, useContext, ReactNode } from 'react';

import { Locale, Messages, getMessages, i18nConfig } from '@/config/i18n';

// Contexto para prover o i18n em toda a aplicação
interface I18nContextValue {
  locale: Locale;
  setLocale: (locale: Locale) => void;
  t: Messages;
  isLoading: boolean;
}

const I18nContext = createContext<I18nContextValue>({
  locale: i18nConfig.defaultLocale,
  setLocale: () => {},
  t: getMessages(i18nConfig.defaultLocale),
  isLoading: false,
});

// Provider do i18n para envolver a aplicação
export function I18nProvider({ children }: { children: ReactNode }): JSX.Element {
  // Sempre usar pt-BR como locale
  const [locale] = useState<Locale>('pt-BR');
  const [messages, setMessages] = useState<Messages>(getMessages('pt-BR'));
  const [isLoading, setIsLoading] = useState(false);

  const setLocale = (newLocale: Locale) => {
    // Ignorar qualquer tentativa de mudar o idioma
    // O locale será sempre pt-BR
    if (newLocale !== 'pt-BR') {
      return;
    }
  };

  // Efeito para atualizar as mensagens apenas na inicialização
  useEffect(() => {
    setIsLoading(true);

    // Forçar o idioma pt-BR
    document.documentElement.lang = 'pt-BR';

    // Configurar mensagens em português
    setMessages(getMessages('pt-BR'));
    setIsLoading(false);
  }, []);

  return (
    <I18nContext.Provider value={{ locale, setLocale, t: messages, isLoading }}>
      {children}
    </I18nContext.Provider>
  );
}

// Hook para usar o contexto i18n
export function useI18n(): I18nContextValue {
  return useContext(I18nContext);
}
