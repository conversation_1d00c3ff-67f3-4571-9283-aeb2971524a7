# Melhorias de Linting e Análise de Código

Este documento descreve as melhorias recomendadas para o sistema de linting e análise de código do projeto Excel Copilot.

## Instalação de Dependências

Execute o seguinte comando para instalar as dependências necessárias:

```bash
npm install --save-dev eslint-plugin-react-hooks eslint-plugin-import eslint-import-resolver-typescript eslint-plugin-sonarjs eslint-plugin-jsx-a11y eslint-plugin-prettier eslint-config-prettier
```

## Configuração do ESLint

Foi criado um `.eslintrc.json` mais robusto que inclui:

- Regras específicas para TypeScript
- Regras para React e React Hooks
- Ordenação de importações
- Detecção de código não utilizado
- Alertas para uso excessivo de `any` e `console.log`

## Plano de Implementação para Correção de Problemas

Devido ao grande número de problemas de linting detectados, recomendamos um plano de implementação gradual:

### Fase 1: Correção de Erros

Foco nos erros críticos que podem causar problemas em tempo de execução:

1. Caracteres de escape desnecessários (no-useless-escape)
2. Declarações léxicas em blocos case (no-case-declarations)
3. Múltiplas exportações com o mesmo nome (import/export)
4. Uso inseguro de tipos Function (no-unsafe-function-type)

### Fase 2: Correção de Estrutura de Importações

Resolver problemas relacionados à organização de importações:

1. Ordenação incorreta de importações (import/order)
2. Importações duplicadas (import/no-duplicates)
3. Uso de named imports como default imports (import/no-named-as-default)

### Fase 3: Correção de Tipagens TypeScript

Melhorar a tipagem em todo o projeto:

1. Uso excessivo de `any` (@typescript-eslint/no-explicit-any)
2. Variáveis não utilizadas (@typescript-eslint/no-unused-vars)
3. Preferir const para valores não reatribuídos (prefer-const)

### Fase 4: Limpeza Geral

Resolver problemas restantes:

1. Problemas de formatação (prettier/prettier)
2. Console statements não utilizados em produção (no-console)

## Adição de Husky para Git Hooks

Recomendamos configurar o Husky para executar o linting automaticamente antes de cada commit:

```bash
# Instalar Husky
npm install --save-dev husky lint-staged

# Configurar Husky
npx husky install
npm set-script prepare "husky install"
npx husky add .husky/pre-commit "npx lint-staged"
```

Adicione a seguinte configuração ao package.json:

```json
"lint-staged": {
  "*.{js,jsx,ts,tsx}": [
    "eslint --fix",
    "prettier --write"
  ]
}
```

## SonarQube/SonarCloud

Para projetos em crescimento, recomendamos configurar o SonarQube ou SonarCloud para análise contínua da qualidade do código:

1. Crie uma conta em [SonarCloud](https://sonarcloud.io/)
2. Configure um projeto para análise
3. Adicione o seguinte arquivo `sonar-project.properties` na raiz do projeto:

```properties
sonar.projectKey=excel-copilot
sonar.organization=your-organization

# Caminho para os arquivos fonte
sonar.sources=src
sonar.exclusions=**/*.test.ts,**/*.test.tsx,**/__tests__/**

# Cobertura de testes
sonar.javascript.lcov.reportPaths=coverage/lcov.info
```

4. Instale o plugin sonarjs para o ESLint:

```bash
npm install --save-dev eslint-plugin-sonarjs
```

5. Adicione ao arquivo `.eslintrc.json`:

```json
{
  "extends": [
    // ... outras extensões
    "plugin:sonarjs/recommended"
  ],
  "plugins": [
    // ... outros plugins
    "sonarjs"
  ]
}
```

## Scripts Adicionais Recomendados

Adicione estes scripts ao seu package.json:

```json
"scripts": {
  // ... scripts existentes
  "lint:full": "eslint --max-warnings=0 \"src/**/*.{ts,tsx}\"",
  "format": "prettier --write \"src/**/*.{ts,tsx,css,scss}\"",
  "check-format": "prettier --check \"src/**/*.{ts,tsx,css,scss}\"",
  "analyze": "npx @next/bundle-analyzer",
  "lint:fix:phase1": "eslint --fix --rule \"no-useless-escape: error, no-case-declarations: error\" \"src/**/*.{ts,tsx}\"",
  "lint:fix:phase2": "eslint --fix --rule \"import/order: error, import/no-duplicates: error\" \"src/**/*.{ts,tsx}\"",
  "lint:fix:phase3": "eslint --fix --rule \"@typescript-eslint/no-explicit-any: warn, @typescript-eslint/no-unused-vars: warn\" \"src/**/*.{ts,tsx}\""
}
```

## Ferramentas de Análise Adicionais

1. **TypeScript Project References**: Para projetos grandes, considere dividir a configuração do TypeScript usando Project References.

2. **Bundle Analyzer**: Analise o tamanho dos pacotes JavaScript:

```bash
npm install --save-dev @next/bundle-analyzer
```

3. **Lighthouse CI**: Integre testes de performance com Lighthouse:

```bash
npm install --save-dev @lhci/cli
```

## Dicas de Uso

1. Execute `npm run lint:fix:phase1`, `npm run lint:fix:phase2` e `npm run lint:fix:phase3` em sequência
2. Priorize a correção de warnings relacionados a tipagem TypeScript
3. Mantenha o número de `any` no código o mais baixo possível
4. Use comentários `// eslint-disable-next-line` apenas quando realmente necessário
