# Mock Server para Testes

Este diretório contém implementações de mocks para facilitar os testes de componentes e APIs do Excel Copilot sem depender de serviços externos reais.

## Server Mock (MSW)

O arquivo `server.ts` fornece uma implementação simplificada do Mock Service Worker (MSW) para possibilitar testes de endpoints HTTP sem depender da biblioteca MSW diretamente, evitando problemas de compatibilidade entre versões.

### Funcionalidades

- Registro e gerenciamento de handlers HTTP (GET, POST, PUT, DELETE)
- Simulação de respostas HTTP com status, corpo e headers
- API similar ao MSW para facilitar a migração
- Métodos para manipulação direta de requisições nos testes unitários

### Como Usar

#### 1. Importar o server e rest

```typescript
import { server, rest, ctx } from '../mocks/server';
```

#### 2. Configurar handlers para seus testes

```typescript
// No setup dos testes (beforeAll, beforeEach, etc.)
server.use(
  rest.get('/api/endpoint', (req, res, ctx) => {
    return res.status(200).json({
      success: true,
      data: {
        /* seus dados mockados */
      },
    });
  }),

  rest.post('/api/outro-endpoint', (req, res, ctx) => {
    // Acessar body da requisição
    const { id } = req.body;

    return res.status(201).json({
      success: true,
      id,
    });
  })
);
```

#### 3. Iniciar/Parar o server nos testes

```typescript
describe('Meus Testes', () => {
  beforeAll(() => {
    server.listen();
  });

  afterEach(() => {
    server.resetHandlers();
  });

  afterAll(() => {
    server.close();
  });

  // Seus testes aqui...
});
```

#### 4. Testar diretamente sem depender do cliente HTTP

O mock server também permite testar handlers diretamente, sem precisar de um cliente HTTP:

```typescript
test('deve retornar dados corretos', () => {
  // Configurar handlers
  setupHandlers();

  // Testar diretamente a resposta
  const response = server.handleRequest('/api/endpoint', 'GET');
  expect(response.status).toBe(200);
  expect(response.body.success).toBe(true);
});
```

## Mocks de Componentes

Além do server, este diretório contém mocks para os principais componentes da aplicação:

- `chart-display-mock.tsx`: Mock do componente de exibição de gráficos
- `chat-interface-mock.tsx`: Mock da interface de chat
- `command-examples-mock.tsx`: Mock dos exemplos de comandos
- `desktop-bridge-status-mock.tsx`: Mock do status da bridge desktop

### Exemplo Completo

Para ver um exemplo completo de uso do mock server, consulte o arquivo:
`__tests__/examples/mock-server-example.test.ts`

## Conjunto Completo de Ferramentas

A implementação agora inclui arquivos adicionais para facilitar os testes:

### Factories (`factories.ts`)

Funções factory para gerar dados mock consistentes e tipados:

```typescript
import { createMockWorkbook, createMockSheetData, createMockUser } from '../mocks/factories';

// Criar um workbook para teste
const workbook = createMockWorkbook({ name: 'Meu Workbook' });

// Criar dados de planilha
const sheetData = createMockSheetData(5, 3);

// Criar um usuário
const user = createMockUser({ role: 'admin' });
```

### Utilitários de Teste (`test-helpers.ts`)

Funções auxiliares para configurar e verificar testes:

```typescript
import {
  setupMockEndpoint,
  expectSuccessResponse,
  expectErrorResponse,
  callMockEndpoint,
} from '../utils/test-helpers';

// Configurar um endpoint
setupMockEndpoint('/api/test', 'GET', 200, { data: 'test' });

// Fazer uma chamada ao endpoint
const response = callMockEndpoint('/api/test', 'GET');

// Verificar resposta
expectSuccessResponse(response);
```

### Utilitários do Servidor (`server-test-utils.ts`)

Funções para configurar rapidamente handlers para cenários comuns:

```typescript
import {
  setupExcelMockHandlers,
  setupAuthMockHandlers,
  setupErrorMockHandlers,
  setupAllMockHandlers,
} from '../utils/server-test-utils';

// Configurar handlers para testes de Excel
setupExcelMockHandlers();

// Ou configurar todos os handlers de uma vez
setupAllMockHandlers();
```

### Configuração Global (`jest.setup.js`)

Arquivo de configuração global para Jest que:

- Inicializa o servidor mock para todos os testes
- Controla logs durante testes
- Configura valores fixos para datas
- Adiciona matchers personalizados ao Jest

## Como Utilizar

### Abordagem Recomendada

1. **Configuração global do servidor** em `jest.setup.js`
2. **Utilização de factories** para criar dados de teste
3. **Configuração de handlers** usando `server-test-utils.ts`
4. **Verificação de resposta** usando funções em `test-helpers.ts`

Veja um exemplo completo desta abordagem em:
`__tests__/examples/mock-server-usage.test.ts`

## Migração de Testes Existentes

Para migrar testes existentes que usam MSW diretamente:

1. Substitua importações do MSW por importações do nosso servidor mock:

   ```typescript
   // Antes
   import { setupServer } from 'msw/node';
   import { rest } from 'msw';

   // Depois
   import { server, rest } from '../../mocks/server';
   ```

2. Remova a instanciação do servidor:

   ```typescript
   // Antes
   const server = setupServer(...handlers);

   // Depois
   // Não é necessário instanciar - use o server importado diretamente
   ```

3. Use os tipos fornecidos para os handlers:

   ```typescript
   import { MockRequest, MockResponse, MockContext } from '../../mocks/types';

   rest.get('/api/endpoint', (req: MockRequest, res: MockResponse, ctx: MockContext) => {
     // Implementação do handler
   });
   ```

4. Considere usar os utilitários para simplificar seus testes:

   ```typescript
   import { setupMockEndpoint, expectSuccessResponse } from '../utils/test-helpers';

   // Antes
   server.use(
     rest.get('/api/test', (req, res, ctx) => {
       return res(ctx.status(200), ctx.json({ success: true }));
     })
   );

   // Depois
   setupMockEndpoint('/api/test', 'GET', 200, { success: true });
   ```

## Executando os Testes

Para executar os testes que utilizam o mock server, foram adicionados scripts específicos no `package.json`:

```bash
# Executar testes específicos do mock server
npm run test:mock-server

# Executar exemplos de uso do mock server
npm run test:mock-examples

# Executar todos os testes
npm test
```

## Funcionalidades Avançadas

### Extração de Parâmetros de Rota

O mock server suporta automaticamente a extração de parâmetros de rota, permitindo definir rotas como:

```typescript
rest.get('/api/users/:id', (req, res, ctx) => {
  const { id } = req.params;
  return res.status(200).json({ userId: id });
});
```

### Métodos HTTP Suportados

Além dos métodos básicos (GET, POST, PUT, DELETE), o mock server também suporta:

- PATCH
- HEAD
- OPTIONS

### Manipulação de Headers

```typescript
rest.get('/api/headers-test', (req, res, ctx) => {
  return res
    .status(200)
    .headers({
      'Content-Type': 'application/json',
      'X-Custom-Header': 'custom-value',
    })
    .json({ success: true });
});
```
