#!/usr/bin/env node

/**
 * Script para limpar o cache do Next.js e do Webpack
 * Útil para resolver problemas de compilação e cache
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';
import chalk from 'chalk';

// Diretórios e arquivos de cache
const cacheDirs = ['.next', 'node_modules/.cache', '.swc', '.vercel/cache'];

console.log(chalk.blue('🧹 Iniciando limpeza de cache do Excel Copilot...'));

// Verificar se o diretório atual tem o package.json do projeto
if (!fs.existsSync(path.resolve(process.cwd(), 'package.json'))) {
  console.error(
    chalk.red(
      '❌ Arquivo package.json não encontrado. Certifique-se de estar no diretório raiz do projeto.'
    )
  );
  process.exit(1);
}

// Limpar diretórios de cache
for (const dir of cacheDirs) {
  const dirPath = path.resolve(process.cwd(), dir);

  if (fs.existsSync(dirPath)) {
    try {
      console.log(chalk.gray(`Removendo ${dir}...`));
      fs.rmSync(dirPath, { recursive: true, force: true });
      console.log(chalk.green(`✅ ${dir} removido com sucesso.`));
    } catch (error) {
      console.error(chalk.yellow(`⚠️ Não foi possível remover ${dir}: ${error}`));
    }
  } else {
    console.log(chalk.gray(`📝 ${dir} não encontrado, ignorando.`));
  }
}

// Verificar e atualizar dependências importantes
console.log(chalk.blue('\n🔄 Verificando dependências...'));

try {
  console.log(chalk.gray('Executando npm install para reinstalar dependências...'));
  execSync('npm install', { stdio: 'inherit' });
  console.log(chalk.green('✅ Dependências reinstaladas com sucesso.'));
} catch (error) {
  console.error(chalk.red(`❌ Erro ao reinstalar dependências: ${error}`));
  process.exit(1);
}

// Reconstruir o projeto
console.log(chalk.blue('\n🏗️ Reconstruindo o projeto...'));

try {
  console.log(chalk.gray('Regenerando tipos do Prisma...'));
  execSync('npx prisma generate', { stdio: 'inherit' });
  console.log(chalk.green('✅ Tipos do Prisma regenerados.'));
} catch (error) {
  console.error(chalk.yellow(`⚠️ Aviso ao regenerar tipos do Prisma: ${error}`));
}

console.log(chalk.green('\n✨ Limpeza de cache concluída com sucesso!'));
console.log(chalk.blue('Para iniciar o projeto, execute: npm run dev'));
