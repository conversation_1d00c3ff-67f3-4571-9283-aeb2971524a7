#!/usr/bin/env node

/**
 * Script para testar as configurações MCP em produção
 * Verifica se os mocks foram desativados e as integrações estão funcionando
 */

// eslint-disable-next-line @typescript-eslint/no-require-imports
const fs = require('fs');
// eslint-disable-next-line @typescript-eslint/no-require-imports
const https = require('https');
// eslint-disable-next-line @typescript-eslint/no-require-imports
const path = require('path');

// Configurações
const BASE_URL = 'https://excel-copilot-eight.vercel.app';
const ENV_FILE = path.join(__dirname, '.env.local');

// Cores para output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m',
};

console.log(`${colors.bold}🚀 TESTE DE CONFIGURAÇÕES MCP EM PRODUÇÃO${colors.reset}\n`);

// Função para fazer requisições HTTPS
function makeRequest(url) {
  return new Promise((resolve, reject) => {
    https
      .get(url, res => {
        let data = '';
        res.on('data', chunk => (data += chunk));
        res.on('end', () => {
          try {
            resolve({
              status: res.statusCode,
              data: JSON.parse(data),
            });
          } catch (error) {
            resolve({
              status: res.statusCode,
              data: data,
              error: 'Invalid JSON',
            });
          }
        });
      })
      .on('error', reject);
  });
}

// Verificar configurações do .env.local
function checkEnvConfig() {
  console.log(`${colors.blue}📋 Verificando configurações do .env.local...${colors.reset}`);

  if (!fs.existsSync(ENV_FILE)) {
    console.log(`${colors.red}❌ Arquivo .env.local não encontrado${colors.reset}`);
    return false;
  }

  const envContent = fs.readFileSync(ENV_FILE, 'utf8');

  const checks = [
    { key: 'FORCE_GOOGLE_MOCKS', expected: 'false', description: 'Mocks Google desativados' },
    { key: 'USE_MOCK_AI', expected: 'false', description: 'Mock AI desativado' },
    {
      key: 'NEXT_PUBLIC_DISABLE_VERTEX_AI',
      expected: 'false',
      description: 'Vertex AI habilitado',
    },
    { key: 'VERTEX_AI_ENABLED', expected: 'true', description: 'Vertex AI ativo' },
    { key: 'NODE_ENV', expected: 'production', description: 'Ambiente de produção' },
    {
      key: 'VERCEL_API_TOKEN',
      expected: '************************',
      description: 'Token Vercel configurado',
    },
  ];

  let allPassed = true;

  checks.forEach(check => {
    const regex = new RegExp(`${check.key}="([^"]*)"`, 'i');
    const match = envContent.match(regex);

    if (match && match[1] === check.expected) {
      console.log(`${colors.green}✅ ${check.description}${colors.reset}`);
    } else {
      console.log(
        `${colors.red}❌ ${check.description} - Esperado: ${check.expected}, Encontrado: ${match ? match[1] : 'não encontrado'}${colors.reset}`
      );
      allPassed = false;
    }
  });

  return allPassed;
}

// Testar endpoints MCP
async function testMCPEndpoints() {
  console.log(`\n${colors.blue}🔍 Testando endpoints MCP...${colors.reset}`);

  const endpoints = [
    { name: 'Health Check Geral', url: `${BASE_URL}/api/health`, expected: 'healthy' },
    { name: 'Vercel MCP', url: `${BASE_URL}/api/vercel/status`, expected: 'success' },
    { name: 'Supabase MCP', url: `${BASE_URL}/api/supabase/status`, expected: 'success' },
    { name: 'Stripe MCP', url: `${BASE_URL}/api/stripe/status`, expected: 'success' },
    { name: 'Linear MCP', url: `${BASE_URL}/api/linear/status`, expected: 'success' },
    { name: 'GitHub MCP', url: `${BASE_URL}/api/github/status`, expected: 'success' },
  ];

  const results = [];

  for (const endpoint of endpoints) {
    try {
      console.log(`Testando ${endpoint.name}...`);
      const result = await makeRequest(endpoint.url);

      if (result.status === 200) {
        const isSuccess =
          result.data.status === endpoint.expected ||
          result.data.data?.status === endpoint.expected ||
          result.status === 'healthy';

        if (isSuccess) {
          console.log(`${colors.green}✅ ${endpoint.name} - OK${colors.reset}`);
          results.push({ name: endpoint.name, status: 'OK', details: result.data });
        } else {
          console.log(`${colors.yellow}⚠️ ${endpoint.name} - Resposta inesperada${colors.reset}`);
          results.push({ name: endpoint.name, status: 'WARNING', details: result.data });
        }
      } else {
        console.log(`${colors.red}❌ ${endpoint.name} - Status ${result.status}${colors.reset}`);
        results.push({ name: endpoint.name, status: 'ERROR', details: result.data });
      }
    } catch (error) {
      console.log(`${colors.red}❌ ${endpoint.name} - Erro: ${error.message}${colors.reset}`);
      results.push({ name: endpoint.name, status: 'ERROR', error: error.message });
    }
  }

  return results;
}

// Gerar relatório
function generateReport(envCheck, mcpResults) {
  console.log(`\n${colors.bold}📊 RELATÓRIO FINAL${colors.reset}`);
  console.log('='.repeat(50));

  // Status das configurações
  console.log(`\n${colors.blue}🔧 Configurações de Ambiente:${colors.reset}`);
  console.log(
    envCheck
      ? `${colors.green}✅ Todas as configurações corretas${colors.reset}`
      : `${colors.red}❌ Algumas configurações precisam ser corrigidas${colors.reset}`
  );

  // Status das integrações MCP
  console.log(`\n${colors.blue}🔗 Status das Integrações MCP:${colors.reset}`);

  const summary = {
    OK: 0,
    WARNING: 0,
    ERROR: 0,
  };

  mcpResults.forEach(result => {
    summary[result.status]++;
    const icon = result.status === 'OK' ? '✅' : result.status === 'WARNING' ? '⚠️' : '❌';
    const color =
      result.status === 'OK'
        ? colors.green
        : result.status === 'WARNING'
          ? colors.yellow
          : colors.red;
    console.log(`${color}${icon} ${result.name}${colors.reset}`);
  });

  console.log(`\n${colors.blue}📈 Resumo:${colors.reset}`);
  console.log(`✅ Funcionando: ${summary.OK}`);
  console.log(`⚠️ Avisos: ${summary.WARNING}`);
  console.log(`❌ Erros: ${summary.ERROR}`);

  // Recomendações
  console.log(`\n${colors.blue}💡 Próximos Passos:${colors.reset}`);

  if (!envCheck) {
    console.log(
      `${colors.yellow}1. Corrigir configurações do .env.local conforme indicado acima${colors.reset}`
    );
  }

  if (summary.ERROR > 0) {
    console.log(
      `${colors.yellow}2. Configurar tokens para integrações com erro (Linear/GitHub)${colors.reset}`
    );
    console.log(
      `${colors.yellow}3. Consultar CONFIGURACAO_MCP_PRODUCAO.md para instruções detalhadas${colors.reset}`
    );
  }

  if (summary.OK === mcpResults.length && envCheck) {
    console.log(
      `${colors.green}🎉 Todas as configurações estão corretas! Mocks desativados com sucesso.${colors.reset}`
    );
  }
}

// Executar testes
async function main() {
  try {
    const envCheck = checkEnvConfig();
    const mcpResults = await testMCPEndpoints();
    generateReport(envCheck, mcpResults);
  } catch (error) {
    console.error(`${colors.red}Erro durante os testes: ${error.message}${colors.reset}`);
    process.exit(1);
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  main();
}

module.exports = { checkEnvConfig, testMCPEndpoints, generateReport };
