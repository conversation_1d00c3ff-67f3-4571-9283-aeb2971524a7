// Import tipos base do projeto
import { ExcelOperationType, ExcelOperation as BaseExcelOperation } from '../../types/index';

/**
 * Re-export do enum ExcelOperationType para evitar conflitos
 * e garantir que estamos usando o mesmo enum em todo o código
 */
export { ExcelOperationType };

// Tipo estendido para as operações de planilha com type mais específico
export interface LocalExcelOperation extends Omit<BaseExcelOperation, 'type'> {
  type: ExcelOperationType;
  data?: any;
  params?: never;
}

// Por compatibilidade, reutilizamos o nome para manter o código externo funcionando
// Estendendo a interface para incluir propriedades específicas para operações de gráfico
export interface ExcelOperation extends BaseExcelOperation {
  // Propriedades para operações de gráfico
  chartType?: string;
  dataRange?: string;
  position?: string;
  title?: string;
  config?: {
    showLegend?: boolean;
    xAxisTitle?: string;
    yAxisTitle?: string;
    colors?: string[];
    [key: string]: any;
  };
}

/**
 * Converte uma operação do tipo base para o tipo local mais específico.
 * Isto garante que as operações tenham type do tipo correto (ExcelOperationType)
 * @param operation Operação no formato base
 * @returns Operação no formato local
 */
export function convertToLocalOperation(operation: BaseExcelOperation): LocalExcelOperation {
  return {
    ...operation,
    type: operation.type as unknown as ExcelOperationType,
  };
}

/**
 * Converte uma operação do tipo local para o tipo base.
 * @param operation Operação no formato local
 * @returns Operação no formato base
 */
export function convertToBaseOperation(operation: LocalExcelOperation): BaseExcelOperation {
  return {
    ...operation,
    type: operation.type as unknown as string,
  };
}

// Interface da resposta do parser de comandos
export interface CommandParserResult {
  operations: ExcelOperation[];
  error: string | null;
  message?: string;
  success: boolean;
}

// Interface para dados com metadados
export interface DataWithMeta<T = any> {
  data: T[];
  meta?: {
    headers?: string[];
    [key: string]: any;
  };
}

// Operações de Fórmula
export interface FormulaOperation {
  formula: string;
  targetCell: string;
}

// Operações de Coluna
export interface ColumnOperation {
  operation: 'SUM' | 'AVERAGE' | 'COUNT' | 'MAX' | 'MIN' | 'MEDIAN' | 'STDDEV';
  columnName?: string;
  columnIndex?: number;
}

// Operações de Gráfico
export interface ChartOperation {
  type: 'bar' | 'line' | 'pie' | 'area' | 'scatter' | 'column' | 'doughnut' | 'radar';
  title?: string;
  data: {
    xRange?: string;
    yRange?: string;
    range?: string;
  };
  options?: {
    showLegend?: boolean;
    colors?: string[];
    width?: number;
    height?: number;
  };
}

// Operações de Filtro
export interface FilterOperation {
  columnName?: string;
  columnIndex?: number;
  condition: {
    operator:
      | 'equals'
      | 'notEquals'
      | 'greaterThan'
      | 'lessThan'
      | 'greaterThanOrEqual'
      | 'lessThanOrEqual'
      | 'contains'
      | 'notContains'
      | 'startsWith'
      | 'endsWith'
      | 'between';
    value: any;
    value2?: any; // Para operações como 'between'
  };
}

// Operações de Ordenação
export interface SortOperation {
  columnName?: string;
  columnIndex?: number;
  direction: 'ascending' | 'descending';
}

// Operações de Tabela
export interface TableOperation {
  range: string;
  hasHeaders?: boolean;
  name?: string;
  style?: string;
}

// Operações de Atualização de Célula
export interface CellUpdateOperation {
  cell: string;
  value: any;
}

// Operações de Formatação
export interface FormatOperation {
  range: string;
  format: {
    type?: 'number' | 'currency' | 'percentage' | 'date' | 'text';
    style?: {
      bold?: boolean;
      italic?: boolean;
      underline?: boolean;
      color?: string;
      backgroundColor?: string;
      fontSize?: number;
      fontFamily?: string;
      horizontalAlignment?: 'left' | 'center' | 'right';
      verticalAlignment?: 'top' | 'middle' | 'bottom';
    };
    numberFormat?: string;
    decimalPlaces?: number;
    currency?: string;
  };
}

// Novas Operações

// Operações de Tabela Dinâmica
export interface PivotTableOperation {
  sourceRange: string;
  destinationRange?: string;
  rowFields: string[];
  columnFields: string[];
  dataFields: string[];
  filterFields?: string[];
  calculations?: {
    field: string;
    function:
      | 'sum'
      | 'count'
      | 'average'
      | 'max'
      | 'min'
      | 'product'
      | 'countNums'
      | 'stdDev'
      | 'stdDevp'
      | 'var'
      | 'varp';
    showAs?:
      | 'normal'
      | 'percentOfTotal'
      | 'percentOfRow'
      | 'percentOfColumn'
      | 'difference'
      | 'percentDifference'
      | 'runningTotal';
  }[];
  dateGrouping?: {
    field: string;
    by: 'years' | 'quarters' | 'months' | 'days';
  }[];
}

// Operações de Formatação Condicional
export interface ConditionalFormatOperation {
  type:
    | 'cellValue'
    | 'colorScale'
    | 'dataBar'
    | 'iconSet'
    | 'topBottom'
    | 'textContains'
    | 'dateOccurring'
    | 'duplicateValues'
    | 'formula';
  range: string;

  // Específico para cada tipo
  cellValue?: {
    operator:
      | 'equal'
      | 'notEqual'
      | 'greaterThan'
      | 'lessThan'
      | 'greaterThanOrEqual'
      | 'lessThanOrEqual'
      | 'between'
      | 'notBetween';
    values: string[];
    style: FormatStyle;
  };

  colorScale?: {
    min: { type: string; color: string; value?: any };
    mid?: { type: string; color: string; value?: any };
    max: { type: string; color: string; value?: any };
  };

  dataBar?: {
    min: { type: string; value?: any };
    max: { type: string; value?: any };
    color: string;
    gradient?: boolean;
    showValue?: boolean;
    border?: boolean;
    borderColor?: string;
  };

  iconSet?: {
    type: string;
    reverse?: boolean;
    showValue?: boolean;
    thresholds: { value: number; type: string }[];
  };

  topBottom?: {
    type: 'top' | 'bottom';
    value: number;
    isPercent?: boolean;
    style: FormatStyle;
  };

  textContains?: {
    text: string;
    style: FormatStyle;
  };

  dateOccurring?: {
    type: string;
    style: FormatStyle;
  };

  duplicateValues?: {
    type: 'duplicate' | 'unique';
    style: FormatStyle;
  };

  formula?: {
    formula: string;
    style: FormatStyle;
  };
}

// Estilo para formatação
export interface FormatStyle {
  fill?: {
    type: string;
    color: string;
  };
  font?: {
    bold?: boolean;
    italic?: boolean;
    underline?: boolean;
    strikethrough?: boolean;
    color?: string;
    size?: number;
  };
  border?: {
    top?: { style: string; color: string };
    left?: { style: string; color: string };
    bottom?: { style: string; color: string };
    right?: { style: string; color: string };
  };
}

// Operações de Gráficos Avançados
export interface AdvancedChartOperation {
  type: string;
  sourceRange: string;
  destinationRange?: string;
  title?: string;
  xAxis?: {
    title?: string;
    min?: number;
    max?: number;
    gridLines?: boolean;
  };
  yAxis?: {
    title?: string;
    min?: number;
    max?: number;
    gridLines?: boolean;
  };
  legend?: {
    show?: boolean;
    position?: string;
  };
  colors?: string[];
  animation?: boolean;
  stacked?: boolean;
}

// Outros tipos específicos de Excel que não estão no módulo principal
export interface ExcelResult {
  success: boolean;
  data?: any;
  error?: string;
}

// Re-exportar outros tipos conforme necessário
