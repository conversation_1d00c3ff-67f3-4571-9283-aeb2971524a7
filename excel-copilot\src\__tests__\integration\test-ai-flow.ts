import fs from 'fs';
import path from 'path';

import { PrismaClient } from '@prisma/client';
import { describe, test, expect, beforeAll, afterAll } from 'vitest';

import { ExcelAIProcessor } from '../../lib/ai/ExcelAIProcessor';
import { executeExcelOperations, createExcelFile } from '../../lib/excel';

// Inicializar cliente Prisma para testes de banco de dados
const prisma = new PrismaClient();

// Diretório temporário para arquivos de teste
const TEST_DIR = path.join(__dirname, '../../__tests__/temp');

// Criar diretório de testes se não existir
if (!fs.existsSync(TEST_DIR)) {
  fs.mkdirSync(TEST_DIR, { recursive: true });
}

describe('Fluxo Completo de IA para Excel', () => {
  let aiProcessor: ExcelAIProcessor;
  let testWorkbookId: string;

  // Configuração antes dos testes
  beforeAll(async () => {
    // Inicializar processador de IA com modo de teste ativado
    aiProcessor = new ExcelAIProcessor({}, true);

    // Criar um workbook de teste no banco de dados
    const testWorkbook = await prisma.workbook.create({
      data: {
        name: 'Workbook de Teste',
        userId: 'test-user',
        sheets: {
          create: {
            name: 'Planilha de Teste',
            data: JSON.stringify({
              headers: ['Produto', 'Preço', 'Quantidade', 'Total'],
              rows: [
                ['Produto A', 100, 2, 200],
                ['Produto B', 150, 3, 450],
                ['Produto C', 75, 5, 375],
              ],
            }),
          },
        },
      },
      include: {
        sheets: true,
      },
    });

    testWorkbookId = testWorkbook.id;

    console.log(`Workbook de teste criado com ID: ${testWorkbookId}`);
  });

  // Limpeza após os testes
  afterAll(async () => {
    // Remover workbook de teste
    await prisma.workbook.delete({
      where: {
        id: testWorkbookId,
      },
    });

    // Limpar arquivos temporários
    const files = fs.readdirSync(TEST_DIR);
    for (const file of files) {
      fs.unlinkSync(path.join(TEST_DIR, file));
    }

    // Fechar conexão com o banco de dados
    await prisma.$disconnect();
  });

  test('Deve processar comandos de IA e extrair operações Excel corretamente', async () => {
    // Comandos para testar
    const commands = [
      'Criar uma tabela com dados de vendas',
      'Calcular a soma total da coluna de preço',
      'Criar um gráfico de barras com produtos e totais',
      'Aplicar formatação de moeda na coluna de preço',
    ];

    for (const command of commands) {
      console.log(`\nTestando comando: "${command}"`);

      // Processar comando via IA
      const result = await aiProcessor.processQuery(command);

      console.log('Resultado:', JSON.stringify(result, null, 2));

      // Verificar se o processamento foi bem-sucedido
      expect(result.success).toBe(true);
      expect(result.operations.length).toBeGreaterThan(0);

      // Verificar se as operações têm os tipos esperados
      const operationTypes = result.operations.map(op => op.type);
      console.log('Tipos de operações:', operationTypes);

      // Verificar estrutura das operações
      for (const operation of result.operations) {
        expect(operation).toHaveProperty('type');
        expect(typeof operation.type).toBe('string');
      }
    }
  });

  test('Deve executar operações Excel e atualizar dados corretamente', async () => {
    // Dados iniciais da planilha
    const initialData = {
      headers: ['Produto', 'Preço', 'Quantidade', 'Total'],
      rows: [
        ['Produto A', 100, 2, 200],
        ['Produto B', 150, 3, 450],
        ['Produto C', 75, 5, 375],
      ],
    };

    // Comando para somar preços
    const command = 'Calcular a soma total da coluna Preço';

    // Processar comando via IA
    const result = await aiProcessor.processQuery(command);
    console.log('Operações geradas:', JSON.stringify(result.operations, null, 2));

    // Verificar se o processamento foi bem-sucedido
    expect(result.success).toBe(true);
    expect(result.operations.length).toBeGreaterThan(0);

    // Executar as operações nos dados
    const executionResult = await executeExcelOperations(initialData, result.operations);
    console.log('Resultado da execução:', JSON.stringify(executionResult, null, 2));

    // Verificar se os dados foram atualizados corretamente
    expect(executionResult.updatedData).toBeDefined();
    expect(executionResult.resultSummary.length).toBeGreaterThan(0);

    // Atualizar os dados no banco de dados
    const sheet = await prisma.sheet.findFirst({
      where: {
        workbookId: testWorkbookId,
      },
    });

    if (sheet) {
      await prisma.sheet.update({
        where: {
          id: sheet.id,
        },
        data: {
          data: JSON.stringify(executionResult.updatedData),
        },
      });

      console.log('Dados atualizados no banco de dados');
    }

    // Verificar se os dados foram persistidos corretamente
    const updatedSheet = await prisma.sheet.findFirst({
      where: {
        workbookId: testWorkbookId,
      },
    });

    expect(updatedSheet).toBeDefined();
    if (updatedSheet) {
      const sheetData = JSON.parse(updatedSheet.data as string);
      console.log('Dados persistidos:', sheetData);
      expect(sheetData).toEqual(executionResult.updatedData);
    }
  });

  test('Deve exportar dados para arquivo Excel corretamente', async () => {
    // Buscar dados do banco de dados
    const sheet = await prisma.sheet.findFirst({
      where: {
        workbookId: testWorkbookId,
      },
    });

    expect(sheet).toBeDefined();
    if (!sheet) return;

    const sheetData = JSON.parse(sheet.data as string);

    // Criar arquivo Excel
    const excelBlob = await createExcelFile(
      [
        {
          name: sheet.name,
          data: sheetData,
        },
      ],
      'Workbook de Teste'
    );

    expect(excelBlob).toBeDefined();
    expect(excelBlob instanceof Blob).toBe(true);

    // Salvar arquivo no sistema de arquivos (apenas para testes)
    const buffer = await excelBlob.arrayBuffer();
    const filePath = path.join(TEST_DIR, 'teste-exportacao.xlsx');
    fs.writeFileSync(filePath, Buffer.from(buffer));

    console.log(`Arquivo Excel salvo em: ${filePath}`);

    // Verificar se o arquivo foi criado corretamente
    expect(fs.existsSync(filePath)).toBe(true);
    const fileStats = fs.statSync(filePath);
    expect(fileStats.size).toBeGreaterThan(0);

    console.log(`Tamanho do arquivo: ${fileStats.size} bytes`);
  });
});
