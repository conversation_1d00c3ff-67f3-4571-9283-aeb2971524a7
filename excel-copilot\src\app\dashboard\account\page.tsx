'use client';

import { Loader2 } from 'lucide-react';
import { useSession } from 'next-auth/react';
import { useState, useEffect } from 'react';
import { toast } from 'sonner';

import { CustomerPortal } from '@/components/billing/customer-portal';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

export default function AccountPage() {
  const { data: session } = useSession();
  const [subscription, setSubscription] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchSubscription = async () => {
      try {
        const response = await fetch('/api/user/subscription');
        const data = await response.json();

        if (response.ok) {
          setSubscription(data.subscription);
        } else {
          // Se não tiver assinatura, cria um objeto de assinatura gratuita
          setSubscription({
            id: 'free',
            plan: 'free',
            status: 'active',
            currentPeriodEnd: null,
            apiCallsUsed: 0,
            apiCallsLimit: 50,
            cancelAtPeriodEnd: false,
          });
        }
      } catch {
        // Erro ao buscar assinatura - não expor detalhes em produção
        toast.error('Não foi possível carregar os dados da assinatura');
      } finally {
        setLoading(false);
      }
    };

    if (session) {
      fetchSubscription();
    }
  }, [session]);

  const handleCreatePortalSession = async () => {
    try {
      const response = await fetch('/api/billing/customer-portal', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          returnUrl: `${window.location.origin}/dashboard/account`,
        }),
      });

      const data = await response.json();
      if (!response.ok) {
        throw new Error(data.error || 'Erro ao acessar o portal');
      }

      return data.url;
    } catch {
      // Erro ao criar sessão do portal - não expor detalhes em produção
      toast.error('Não foi possível acessar o portal de gerenciamento');
      return null;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-[calc(100vh-200px)]">
        <div className="flex flex-col items-center gap-4">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <p className="text-muted-foreground">Carregando informações da conta...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-12 max-w-4xl">
      <h1 className="text-3xl font-bold mb-8">Sua Conta</h1>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        {/* Coluna lateral com informações do usuário */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Perfil</CardTitle>
              <CardDescription>Suas informações pessoais</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Nome</h3>
                  <p>{session?.user?.name || 'Não informado'}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground">Email</h3>
                  <p>{session?.user?.email || 'Não informado'}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Coluna principal com portal de assinatura */}
        <div className="md:col-span-2">
          <h2 className="text-xl font-semibold mb-4">Sua Assinatura</h2>
          {subscription && (
            <CustomerPortal
              subscription={subscription}
              onCreatePortalSession={handleCreatePortalSession}
            />
          )}
        </div>
      </div>
    </div>
  );
}
