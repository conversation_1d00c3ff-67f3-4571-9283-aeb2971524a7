/**
 * Gerenciador global de erros da aplicação
 *
 * Este módulo fornece funções para tratamento centralizado de erros,
 * evitando que erros não tratados quebrem a experiência do usuário.
 */

import { logger } from '@/lib/logger';

// Tipos de erros que tratamos especialmente
enum ErrorType {
  NETWORK = 'network',
  WEBSOCKET = 'websocket',
  BRIDGE = 'bridge',
  UI = 'ui',
  GENERAL = 'general',
}

interface ErrorHandlerOptions {
  silent?: boolean;
  retry?: boolean;
  maxRetries?: number;
}

// Configuração global
const config = {
  logErrors: true,
  silenceNonCritical: true,
  retryNetwork: true,
  maxRetries: 3,
};

// Mapa para tracking de retentativas
const _retryMap = new Map<string, number>();

// Definição de tipos para os erros
export type ErrorLevel = 'warn' | 'error' | 'fatal';

export interface ErrorOptions {
  level?: ErrorLevel;
  code?: string;
  context?: Record<string, unknown> | undefined;
  shouldReport?: boolean;
  originalError?: Error | undefined;
  userId?: string | undefined;
  sessionId?: string | undefined;
}

// Classe base para erros da aplicação
export class AppError extends Error {
  public readonly level: ErrorLevel;
  public readonly code: string;
  public readonly context: Record<string, unknown>;
  public readonly shouldReport: boolean;
  public readonly originalError?: Error | undefined;
  public readonly userId?: string | undefined;
  public readonly sessionId?: string | undefined;

  constructor(message: string, options: ErrorOptions = {}) {
    super(message);
    this.name = this.constructor.name;
    this.level = options.level || 'error';
    this.code = options.code || 'UNKNOWN_ERROR';
    this.context = options.context || {};
    this.shouldReport = options.shouldReport !== false;
    this.originalError = options.originalError;
    this.userId = options.userId;
    this.sessionId = options.sessionId;

    // Restaurar a cadeia de protótipos
    Object.setPrototypeOf(this, new.target.prototype);
  }

  // Serializa o erro para logs
  toJSON() {
    return {
      name: this.name,
      message: this.message,
      code: this.code,
      level: this.level,
      stack: this.stack,
      context: this.context,
      originalError: this.originalError
        ? {
            message: this.originalError.message,
            stack: this.originalError.stack,
          }
        : undefined,
      userId: this.userId,
      sessionId: this.sessionId,
    };
  }

  // Reporta o erro aos sistemas de logging
  report() {
    if (!this.shouldReport) return;

    const errorData = this.toJSON();

    // Usar o logger ao invés de console
    if (this.level === 'warn') {
      logger.warn('Aviso da aplicação', errorData);
    } else if (this.level === 'error') {
      logger.error('Erro da aplicação', this);
    } else if (this.level === 'fatal') {
      logger.fatal('Erro fatal da aplicação', this);
    }
  }
}

// Manipulador global de erros
export class ErrorHandler {
  static async handle(error: unknown, context?: Record<string, unknown>): Promise<void> {
    const appError = ErrorHandler.normalizeError(error, context);
    appError.report();
  }

  static normalizeError(error: unknown, context?: Record<string, unknown> | undefined): AppError {
    if (error instanceof AppError) {
      if (context) {
        // Criar um novo objeto para não modificar a propriedade readonly
        const updatedError = new AppError(error.message, {
          level: error.level,
          code: error.code,
          originalError: error.originalError,
          userId: error.userId,
          sessionId: error.sessionId,
          shouldReport: error.shouldReport,
          context: { ...error.context, ...context },
        });
        return updatedError;
      }
      return error;
    }

    if (error instanceof Error) {
      logger.debug('Normalizando erro nativo', { errorName: error.name });

      return new AppError(error.message, {
        originalError: error,
        context: context || undefined,
        code: error.name === 'Error' ? 'UNKNOWN_ERROR' : error.name,
      });
    }

    // Para erros que não são instâncias de Error
    if (typeof error === 'string') {
      return new AppError(error, { context: context || undefined });
    }

    try {
      const errorMsg = JSON.stringify(error);
      return new AppError(`Erro não estruturado: ${errorMsg}`, { context: context || undefined });
    } catch {
      return new AppError('Erro desconhecido não serializável', { context: context || undefined });
    }
  }

  // Método para manipular erros de API
  static async handleApiError(
    error: unknown,
    req: Request,
    errorId?: string
  ): Promise<{ message: string; code: string; id: string | undefined }> {
    const userId = this.getUserIdFromRequest(req);
    const sessionId = this.getSessionIdFromRequest(req);

    const appError = ErrorHandler.normalizeError(error, {
      url: req.url,
      method: req.method,
      userId,
      sessionId,
    });

    // Incluir IDs no contexto para rastreamento
    const updatedError = new AppError(appError.message, {
      level: appError.level,
      code: appError.code,
      context: appError.context,
      originalError: appError.originalError,
      shouldReport: appError.shouldReport,
      userId: userId || undefined,
      sessionId: sessionId || undefined,
    });

    updatedError.report();

    logger.debug('API Error', {
      errorId,
      code: updatedError.code,
      message: updatedError.message,
    });

    return {
      message: this.getSafeErrorMessage(updatedError),
      code: updatedError.code,
      id: errorId,
    };
  }

  // Obtém uma mensagem de erro segura para o cliente
  private static getSafeErrorMessage(error: AppError): string {
    // Em produção, não enviar mensagens detalhadas para o cliente
    if (process.env.NODE_ENV === 'production') {
      if (error.level === 'fatal' || error.code === 'INTERNAL_SERVER_ERROR') {
        return 'Ocorreu um erro inesperado no servidor.';
      }
    }

    return error.message;
  }

  // Extrai o ID do usuário do request
  private static getUserIdFromRequest(req: Request): string | undefined {
    try {
      // Implementação real depende da estrutura de autenticação
      const authHeader = req.headers.get('authorization');
      if (!authHeader) return undefined;

      // Extrair o ID do usuário a partir do token - implementação simplificada
      return 'user-id-extracted'; // Placeholder
    } catch (error) {
      logger.warn('Erro ao extrair ID do usuário', { error });
      return undefined;
    }
  }

  // Extrai o ID da sessão do request
  private static getSessionIdFromRequest(req: Request): string | undefined {
    try {
      return req.headers.get('x-session-id') || undefined;
    } catch (error) {
      logger.warn('Erro ao extrair ID da sessão', { error });
      return undefined;
    }
  }
}

/**
 * Registra tratamento global de erros não capturados
 */
export function setupGlobalErrorHandlers() {
  if (typeof window === 'undefined') return;

  // Erros síncronos não capturados
  window.addEventListener('error', event => {
    handleUncaughtError(event);

    // Se for um erro de recurso (ex: imagem não encontrada), silenciamos
    if (event.target && 'tagName' in event.target) {
      event.preventDefault();
    }
  });

  // Promessas rejeitadas não tratadas
  window.addEventListener('unhandledrejection', event => {
    handleUnhandledRejection(event);
    // Sempre prevenimos o erro padrão para garantir que não quebre a UI
    event.preventDefault();
  });

  console.info('✓ Tratamento global de erros inicializado');
}

/**
 * Trata erros não capturados
 */
function handleUncaughtError(event: ErrorEvent) {
  // Evitar looping com erros do próprio handler
  if (event.message?.includes('ErrorHandler')) return;

  const errorType = determineErrorType(event);

  if (config.silenceNonCritical && errorType !== ErrorType.GENERAL) {
    // Apenas logamos erros de recursos não críticos
    logger.warn(`Erro não crítico [${errorType}]: ${event.message}`, {
      filename: event.filename,
      line: event.lineno,
      column: event.colno,
    });
    return;
  }

  // Para erros gerais, usamos o handler completo
  const errorOptions: ErrorOptions = {
    level: errorType === ErrorType.GENERAL ? 'error' : 'warn',
    code: `UNCAUGHT_${errorType.toUpperCase()}`,
    context: {
      source: event.filename || 'unknown',
      line: event.lineno,
      column: event.colno,
    },
  };

  const appError = new AppError(event.message || 'Erro não capturado', errorOptions);
  ErrorHandler.handle(appError);

  // Se for erro de rede e temos retry habilitado
  if (errorType === ErrorType.NETWORK && config.retryNetwork) {
    retryOperation(event);
  }
}

/**
 * Trata rejeições de promises não capturadas
 */
function handleUnhandledRejection(event: PromiseRejectionEvent) {
  const reason = event.reason;

  // Verificar se o motivo é um erro estruturado
  if (reason instanceof Error) {
    logger.error('Rejeição de Promise não tratada:', reason);

    const errorOptions: ErrorOptions = {
      level: 'error',
      code: 'UNHANDLED_REJECTION',
      originalError: reason,
      context: {
        stack: reason.stack,
      },
    };

    const appError = new AppError(reason.message, errorOptions);
    ErrorHandler.handle(appError);
  } else {
    // Para rejeições que não são instâncias de Error
    const message =
      typeof reason === 'string'
        ? reason
        : typeof reason === 'object' && reason !== null
          ? JSON.stringify(reason)
          : 'Rejeição de Promise não tratada';

    const appError = new AppError(message, {
      level: 'error',
      code: 'UNHANDLED_REJECTION',
      context: { reason },
    });

    ErrorHandler.handle(appError);
  }
}

/**
 * Determina o tipo de erro com base em suas características
 */
function determineErrorType(error: Error | ErrorEvent | Event): ErrorType {
  if (error instanceof ErrorEvent) {
    // Verificar se é um erro de carregamento de recurso
    if (error.filename?.includes('.js') || error.filename?.includes('.ts')) {
      return ErrorType.GENERAL;
    }

    // Erro de carregamento de rede
    if (
      error.message?.includes('network') ||
      error.message?.includes('fetch') ||
      error.message?.includes('load')
    ) {
      return ErrorType.NETWORK;
    }

    // Verificar se é erro de conexão WebSocket
    if (error.message?.includes('WebSocket') || error.message?.includes('socket')) {
      return ErrorType.WEBSOCKET;
    }

    // Verificar se é erro de integração com desktop
    if (
      error.message?.includes('bridge') ||
      error.message?.includes('electron') ||
      error.message?.includes('desktop')
    ) {
      return ErrorType.BRIDGE;
    }
  }

  // Para erros que são instâncias de Error
  if (error instanceof Error) {
    const message = error.message.toLowerCase();
    if (message.includes('network') || message.includes('fetch')) {
      return ErrorType.NETWORK;
    }
    if (message.includes('websocket') || message.includes('socket')) {
      return ErrorType.WEBSOCKET;
    }
    if (message.includes('bridge') || message.includes('electron')) {
      return ErrorType.BRIDGE;
    }
    // Para erros específicos de UI
    if (message.includes('render') || message.includes('component') || message.includes('prop')) {
      return ErrorType.UI;
    }
  }

  return ErrorType.GENERAL;
}

/**
 * Função para tentar novamente operações que falharam (principalmente rede)
 */
function retryOperation(_error: ErrorEvent | Error) {
  // Implementação de retry pode ser adicionada aqui se necessário
}

/**
 * Envolve uma função com tratamento de erros
 */
export function withErrorHandling<T extends (...args: unknown[]) => unknown>(
  fn: T,
  options: ErrorHandlerOptions = {}
): (...args: Parameters<T>) => ReturnType<T> | Promise<ReturnType<T>> {
  return async (...args: Parameters<T>): Promise<ReturnType<T>> => {
    try {
      const result = await fn(...args);
      return result as ReturnType<T>;
    } catch (error) {
      // Somente logamos o erro se não estiver em modo silencioso
      if (!options.silent) {
        await ErrorHandler.handle(error);
      }

      // Rethrow para que o chamador possa tratar se necessário
      throw error;
    }
  };
}
