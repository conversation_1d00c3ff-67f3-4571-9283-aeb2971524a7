/**
 * Utilitários para testes
 *
 * Funções auxiliares para simplificar a configuração e verificação nos testes.
 */

import { server, rest, HttpResponse } from '../mocks/server';
import { MockRequest, MockResponse, MockContext } from '../mocks/types';

/**
 * Configura o servidor mock para responder a um endpoint específico
 * @param path Caminho do endpoint
 * @param method Método HTTP (GET, POST, etc)
 * @param statusCode Código de status HTTP
 * @param responseData Dados a serem retornados
 */
export const setupMockEndpoint = (
  path: string,
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'GET',
  statusCode = 200,
  responseData: any = { success: true }
) => {
  const methodFn = rest[method.toLowerCase() as keyof typeof rest];

  if (!methodFn) {
    throw new Error(`Método HTTP não suportado: ${method}`);
  }

  server.use(
    methodFn(path, () => {
      return HttpResponse.json(responseData, { status: statusCode });
    })
  );

  return { path, method, statusCode, responseData };
};

/**
 * Configura múltiplos endpoints no servidor mock
 * @param configs Array de configurações de endpoints
 */
export const setupMockEndpoints = (
  configs: Array<{
    path: string;
    method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
    statusCode?: number;
    responseData?: any;
  }>
) => {
  configs.forEach(config => {
    setupMockEndpoint(
      config.path,
      config.method || 'GET',
      config.statusCode || 200,
      config.responseData || { success: true }
    );
  });
};

/**
 * Verifica se uma resposta é de sucesso com os dados esperados
 * @param response Resposta a ser verificada
 * @param expectedData Dados esperados (opcional)
 */
export const expectSuccessResponse = (response: any, expectedData?: any) => {
  expect(response.status).toBe(200);
  expect(response.body.success).toBe(true);

  if (expectedData) {
    expect(response.body.data).toEqual(expectedData);
  }
};

/**
 * Verifica se uma resposta é de erro com a mensagem esperada
 * @param response Resposta a ser verificada
 * @param statusCode Código de status esperado
 * @param errorMessage Mensagem de erro esperada (opcional)
 */
export const expectErrorResponse = (response: any, statusCode = 400, errorMessage?: string) => {
  expect(response.status).toBe(statusCode);
  expect(response.body.success).toBe(false);

  if (errorMessage) {
    expect(response.body.error).toBe(errorMessage);
  }
};

/**
 * Simula uma chamada ao servidor mock e retorna a resposta
 * @param path Caminho do endpoint
 * @param method Método HTTP
 * @param body Corpo da requisição (para POST, PUT, etc)
 */
export const callMockEndpoint = (path: string, method: string = 'GET', body?: any): any => {
  // Criamos um mock simplificado que simula uma resposta do server.handleRequest
  return {
    status: 200,
    body: {
      success: true,
      data: body || {},
      method,
    },
    json: () => ({}),
  };
};

/**
 * Aguarda um tempo específico (útil para testes assíncronos)
 * @param ms Tempo em milissegundos
 */
export const wait = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

/**
 * Faz chamadas repetidas ao servidor mock até que uma condição seja atendida ou o timeout expire
 * @param path Caminho do endpoint
 * @param method Método HTTP
 * @param condition Função que verifica a condição de parada
 * @param maxAttempts Número máximo de tentativas
 * @param intervalMs Intervalo entre tentativas em milissegundos
 */
export const pollMockEndpoint = async (
  path: string,
  method: string = 'GET',
  condition: (response: any) => boolean,
  maxAttempts = 10,
  intervalMs = 100
) => {
  let attempts = 0;
  let lastResponse;

  while (attempts < maxAttempts) {
    attempts++;
    lastResponse = callMockEndpoint(path, method);

    if (condition(lastResponse)) {
      return lastResponse;
    }

    await wait(intervalMs);
  }

  throw new Error(`Condição não atendida após ${maxAttempts} tentativas: ${path}`);
};
