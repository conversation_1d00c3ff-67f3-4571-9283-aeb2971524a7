/**
 * Este arquivo contém patches e soluções alternativas para problemas comuns.
 * Deve ser importado globalmente em arquivos de configuração do Next.js
 * quando necessário para resolver problemas específicos.
 */

// Patch para resolver problemas de carregamento de módulos do Next.js
if (typeof window !== 'undefined') {
  // Verifica se o módulo 'next/link' está disponível no cliente
  try {
    // Este código é executado apenas no cliente
    if (!window.__NEXT_PATCHED && !window.next_link_patched) {
      // Define um sinalizador para evitar patchs repetidos
      window.next_link_patched = true;

      // Logger de debug
      // [<PERSON><PERSON>] Aplicando patches de módulos Next.js

      // Armazena referência ao método importScripts original
      const originalImportScripts = window.importScripts;

      // Substitui o método importScripts para tratar erros específicos
      window.importScripts = function patchedImportScripts(...args) {
        try {
          return originalImportScripts.apply(this, args);
        } catch (error) {
          // [Patches] Erro ao importar script: ${error.message}
          // Silencia erros de módulos específicos em desenvolvimento
          if (process.env.NODE_ENV !== 'production') {
            // [Patches] Ignorando erro de importação em modo de desenvolvimento
            return null;
          }
          throw error;
        }
      };
    }
  } catch (e) {
    console.warn('[Patches] Erro ao aplicar patches:', e);
  }
}
