/**
 * Endpoint administrativo para validar integridade das assinaturas
 * GET /api/admin/subscription-integrity
 */

export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';

import { logger } from '@/lib/logger';
import { PLANS, API_CALL_LIMITS } from '@/lib/stripe';
import { prisma } from '@/server/db/client';

interface IntegrityReport {
  summary: {
    totalUsers: number;
    usersWithSubscription: number;
    usersWithoutSubscription: number;
    inconsistencies: number;
  };
  issues: Array<{
    type:
      | 'missing_subscription'
      | 'invalid_plan'
      | 'expired_subscription'
      | 'duplicate_subscription';
    userId: string;
    email: string;
    details: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
  }>;
  recommendations: string[];
}

export async function GET(_request: NextRequest): Promise<NextResponse> {
  try {
    // Verificar autenticação e permissões de admin
    const session = await getServerSession();
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Não autorizado. Faça login para continuar.' },
        { status: 401 }
      );
    }

    // TODO: Implementar verificação de admin
    // Por enquanto, permitir apenas em desenvolvimento
    if (process.env.NODE_ENV === 'production') {
      return NextResponse.json(
        { error: 'Endpoint disponível apenas em desenvolvimento.' },
        { status: 403 }
      );
    }

    logger.info(
      `[SUBSCRIPTION_INTEGRITY] Iniciando verificação de integridade solicitada por ${session.user.email}`
    );

    const report: IntegrityReport = {
      summary: {
        totalUsers: 0,
        usersWithSubscription: 0,
        usersWithoutSubscription: 0,
        inconsistencies: 0,
      },
      issues: [],
      recommendations: [],
    };

    // 1. Buscar todos os usuários com suas assinaturas
    const users = await prisma.user.findMany({
      select: {
        id: true,
        email: true,
        name: true,
        createdAt: true,
        subscriptions: {
          select: {
            id: true,
            plan: true,
            status: true,
            apiCallsLimit: true,
            apiCallsUsed: true,
            currentPeriodEnd: true,
            createdAt: true,
          },
          orderBy: { createdAt: 'desc' },
        },
      },
    });

    report.summary.totalUsers = users.length;

    // 2. Analisar cada usuário
    for (const user of users) {
      const activeSubscriptions = user.subscriptions.filter(
        sub => sub.status === 'active' || sub.status === 'trialing'
      );

      // Verificar usuários sem assinatura
      if (user.subscriptions.length === 0) {
        report.summary.usersWithoutSubscription++;
        report.issues.push({
          type: 'missing_subscription',
          userId: user.id,
          email: user.email || 'Email não definido',
          details: 'Usuário não possui nenhuma assinatura registrada',
          severity: 'high',
        });
      } else {
        report.summary.usersWithSubscription++;

        // Verificar múltiplas assinaturas ativas
        if (activeSubscriptions.length > 1) {
          report.issues.push({
            type: 'duplicate_subscription',
            userId: user.id,
            email: user.email || 'Email não definido',
            details: `Usuário possui ${activeSubscriptions.length} assinaturas ativas`,
            severity: 'medium',
          });
        }

        // Verificar planos inválidos
        for (const subscription of user.subscriptions) {
          if (!Object.values(PLANS).includes(subscription.plan)) {
            report.issues.push({
              type: 'invalid_plan',
              userId: user.id,
              email: user.email || 'Email não definido',
              details: `Plano inválido: ${subscription.plan}`,
              severity: 'critical',
            });
          }

          // Verificar limites de API inconsistentes
          const expectedLimit = API_CALL_LIMITS[subscription.plan];
          if (expectedLimit && subscription.apiCallsLimit !== expectedLimit) {
            report.issues.push({
              type: 'invalid_plan',
              userId: user.id,
              email: user.email || 'Email não definido',
              details: `Limite de API inconsistente: esperado ${expectedLimit}, atual ${subscription.apiCallsLimit}`,
              severity: 'medium',
            });
          }

          // Verificar assinaturas expiradas mas ainda ativas
          if (
            subscription.currentPeriodEnd &&
            subscription.currentPeriodEnd < new Date() &&
            subscription.status === 'active'
          ) {
            report.issues.push({
              type: 'expired_subscription',
              userId: user.id,
              email: user.email || 'Email não definido',
              details: `Assinatura expirada em ${subscription.currentPeriodEnd.toISOString()} mas ainda ativa`,
              severity: 'high',
            });
          }
        }
      }
    }

    report.summary.inconsistencies = report.issues.length;

    // 3. Gerar recomendações
    if (report.summary.usersWithoutSubscription > 0) {
      report.recommendations.push(
        `Execute o script de migração para criar assinaturas Free para ${report.summary.usersWithoutSubscription} usuários sem assinatura.`
      );
    }

    const criticalIssues = report.issues.filter(issue => issue.severity === 'critical').length;
    if (criticalIssues > 0) {
      report.recommendations.push(
        `Corrija imediatamente ${criticalIssues} problemas críticos identificados.`
      );
    }

    const duplicateSubscriptions = report.issues.filter(
      issue => issue.type === 'duplicate_subscription'
    ).length;
    if (duplicateSubscriptions > 0) {
      report.recommendations.push(
        `Revise e consolide ${duplicateSubscriptions} usuários com múltiplas assinaturas ativas.`
      );
    }

    // 4. Estatísticas adicionais
    const planStats = await prisma.subscription.groupBy({
      by: ['plan', 'status'],
      _count: {
        plan: true,
      },
    });

    logger.info(`[SUBSCRIPTION_INTEGRITY] Verificação concluída:`, {
      totalUsers: report.summary.totalUsers,
      usersWithoutSubscription: report.summary.usersWithoutSubscription,
      inconsistencies: report.summary.inconsistencies,
      planStats,
    });

    return NextResponse.json({
      success: true,
      timestamp: new Date().toISOString(),
      report,
      planStats,
    });
  } catch (error) {
    logger.error('[SUBSCRIPTION_INTEGRITY_ERROR]', error);
    return NextResponse.json(
      {
        error: 'Erro ao verificar integridade das assinaturas.',
        details: process.env.NODE_ENV === 'development' ? String(error) : undefined,
      },
      { status: 500 }
    );
  }
}

/**
 * Endpoint para corrigir automaticamente problemas de integridade
 * POST /api/admin/subscription-integrity
 */
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    const session = await getServerSession();
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Não autorizado. Faça login para continuar.' },
        { status: 401 }
      );
    }

    if (process.env.NODE_ENV === 'production') {
      return NextResponse.json(
        { error: 'Correção automática disponível apenas em desenvolvimento.' },
        { status: 403 }
      );
    }

    const { action } = await request.json();

    if (action === 'fix_missing_subscriptions') {
      // Buscar usuários sem assinatura
      const usersWithoutSubscription = await prisma.user.findMany({
        where: {
          subscriptions: {
            none: {},
          },
        },
        select: {
          id: true,
          email: true,
        },
      });

      let fixed = 0;
      const errors: string[] = [];

      for (const user of usersWithoutSubscription) {
        try {
          await prisma.subscription.create({
            data: {
              userId: user.id,
              plan: PLANS.FREE,
              status: 'active',
              apiCallsLimit: API_CALL_LIMITS[PLANS.FREE] || 50,
              apiCallsUsed: 0,
              currentPeriodStart: new Date(),
              cancelAtPeriodEnd: false,
            },
          });
          fixed++;
        } catch (error) {
          errors.push(`Erro ao corrigir usuário ${user.email}: ${error}`);
        }
      }

      logger.info(`[SUBSCRIPTION_AUTO_FIX] Corrigidos ${fixed} usuários sem assinatura`);

      return NextResponse.json({
        success: true,
        message: `${fixed} assinaturas Free criadas automaticamente`,
        errors: errors.length > 0 ? errors : undefined,
      });
    }

    return NextResponse.json({ error: 'Ação não reconhecida.' }, { status: 400 });
  } catch (error) {
    logger.error('[SUBSCRIPTION_AUTO_FIX_ERROR]', error);
    return NextResponse.json(
      {
        error: 'Erro ao corrigir problemas de integridade.',
        details: process.env.NODE_ENV === 'development' ? String(error) : undefined,
      },
      { status: 500 }
    );
  }
}
