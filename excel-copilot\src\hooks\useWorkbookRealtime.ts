import { useSession } from 'next-auth/react';
import { useEffect, useRef, useState, useCallback } from 'react';

import {
  realtimeService,
  WorkbookChangePayload,
  CellChangeEvent,
  UserPresenceEvent,
} from '@/lib/supabase/realtime';

/**
 * Estado de presença de usuários
 */
export interface UserPresence {
  userId: string;
  userName: string;
  isOnline: boolean;
  lastSeen: string;
  cursor?:
    | {
        sheetId: string;
        cellAddress: string;
      }
    | undefined;
}

/**
 * Hook para gerenciar Real-time de workbooks
 */
export function useWorkbookRealtime(workbookId: string | null) {
  const { data: session } = useSession();
  const [isConnected, setIsConnected] = useState(false);
  const [onlineUsers, setOnlineUsers] = useState<Map<string, UserPresence>>(new Map());
  const [recentChanges, setRecentChanges] = useState<CellChangeEvent[]>([]);
  const subscribedWorkbookId = useRef<string | null>(null);

  // Callbacks para eventos
  const onWorkbookChange = useCallback((payload: WorkbookChangePayload) => {
    // Disparar eventos customizados ou atualizar estado global
    window.dispatchEvent(new CustomEvent('workbook-changed', { detail: payload }));
  }, []);

  const onSheetChange = useCallback((payload: WorkbookChangePayload) => {
    window.dispatchEvent(new CustomEvent('sheet-changed', { detail: payload }));
  }, []);

  const onCellChange = useCallback((event: CellChangeEvent) => {
    // Adicionar à lista de mudanças recentes
    setRecentChanges(prev => {
      const newChanges = [event, ...prev.slice(0, 49)]; // Manter apenas 50 mudanças
      return newChanges;
    });

    // Disparar evento customizado
    window.dispatchEvent(new CustomEvent('cell-changed', { detail: event }));
  }, []);

  const onPresenceChange = useCallback((event: UserPresenceEvent) => {
    setOnlineUsers(prev => {
      const newUsers = new Map(prev);

      if (event.isOnline) {
        newUsers.set(event.userId, {
          userId: event.userId,
          userName: event.userName,
          isOnline: true,
          lastSeen: event.lastSeen,
          cursor: event.cursor,
        });
      } else {
        const user = newUsers.get(event.userId);
        if (user) {
          newUsers.set(event.userId, {
            ...user,
            isOnline: false,
            lastSeen: event.lastSeen,
          });
        }
      }

      return newUsers;
    });

    // Disparar evento customizado
    window.dispatchEvent(new CustomEvent('user-presence-changed', { detail: event }));
  }, []);

  // Função para atualizar cursor do usuário
  const updateCursor = useCallback(
    async (sheetId: string, cellAddress: string) => {
      if (workbookId && session?.user) {
        try {
          await realtimeService.updateUserCursor(workbookId, { sheetId, cellAddress });
        } catch (error) {
          console.error('Erro ao atualizar cursor:', error);
        }
      }
    },
    [workbookId, session]
  );

  // Função para enviar mudança de célula
  const broadcastCellChange = useCallback(
    async (sheetId: string, cellAddress: string, value: unknown) => {
      if (workbookId && session?.user) {
        try {
          await realtimeService.broadcastCellChange(workbookId, {
            workbookId,
            sheetId,
            cellAddress,
            value,
            userId: session.user.id || session.user.email || 'unknown',
          });
        } catch (error) {
          console.error('Erro ao enviar mudança de célula:', error);
        }
      }
    },
    [workbookId, session]
  );

  // Efeito para gerenciar inscrições
  useEffect(() => {
    if (!workbookId || !session?.user) {
      // Limpar inscrições se não há workbook ou usuário
      if (subscribedWorkbookId.current) {
        realtimeService.unsubscribeFromWorkbook(subscribedWorkbookId.current);
        realtimeService.unsubscribeFromPresence(subscribedWorkbookId.current);
        subscribedWorkbookId.current = null;
        setIsConnected(false);
        setOnlineUsers(new Map());
      }
      return;
    }

    // Se já está inscrito no mesmo workbook, não fazer nada
    if (subscribedWorkbookId.current === workbookId) {
      return;
    }

    // Cancelar inscrição anterior se existir
    if (subscribedWorkbookId.current) {
      realtimeService.unsubscribeFromWorkbook(subscribedWorkbookId.current);
      realtimeService.unsubscribeFromPresence(subscribedWorkbookId.current);
    }

    // Inscrever-se no novo workbook
    try {
      // Inscrever-se em mudanças do workbook
      realtimeService.subscribeToWorkbook(workbookId, {
        onWorkbookChange,
        onSheetChange,
        onCellChange,
      });

      // Inscrever-se em presença de usuários
      realtimeService.subscribeToUserPresence(
        workbookId,
        {
          id: session.user.id || session.user.email || 'unknown',
          name: session.user.name || session.user.email || 'Usuário',
        },
        onPresenceChange
      );

      subscribedWorkbookId.current = workbookId;
      setIsConnected(true);
    } catch (error) {
      console.error('Erro ao conectar ao Real-time:', error);
      setIsConnected(false);
    }

    // Cleanup ao desmontar
    return () => {
      if (subscribedWorkbookId.current) {
        realtimeService.unsubscribeFromWorkbook(subscribedWorkbookId.current);
        realtimeService.unsubscribeFromPresence(subscribedWorkbookId.current);
        subscribedWorkbookId.current = null;
        setIsConnected(false);
        setOnlineUsers(new Map());
      }
    };
  }, [workbookId, session, onWorkbookChange, onSheetChange, onCellChange, onPresenceChange]);

  // Função para obter status de conexão
  const getConnectionStatus = useCallback(() => {
    return realtimeService.getConnectionStatus();
  }, []);

  // Função para reconectar
  const reconnect = useCallback(async () => {
    try {
      await realtimeService.reconnectAll();
      setIsConnected(true);
    } catch (error) {
      console.error('Erro ao reconectar:', error);
      setIsConnected(false);
    }
  }, []);

  return {
    // Estado
    isConnected,
    onlineUsers: Array.from(onlineUsers.values()),
    recentChanges,

    // Funções
    updateCursor,
    broadcastCellChange,
    getConnectionStatus,
    reconnect,

    // Utilitários
    isUserOnline: (userId: string) => onlineUsers.get(userId)?.isOnline || false,
    getUserCursor: (userId: string) => onlineUsers.get(userId)?.cursor,
    getOnlineCount: () => Array.from(onlineUsers.values()).filter(u => u.isOnline).length,
  };
}

/**
 * Hook simplificado para apenas presença de usuários
 */
export function useUserPresence(workbookId: string | null) {
  const { onlineUsers, isConnected, getOnlineCount } = useWorkbookRealtime(workbookId);

  return {
    onlineUsers,
    isConnected,
    onlineCount: getOnlineCount(),
  };
}

/**
 * Hook para escutar eventos específicos de Real-time
 */
export function useRealtimeEvents(_workbookId: string | null) {
  const [lastWorkbookChange, setLastWorkbookChange] = useState<WorkbookChangePayload | null>(null);
  const [lastSheetChange, setLastSheetChange] = useState<WorkbookChangePayload | null>(null);
  const [lastCellChange, setLastCellChange] = useState<CellChangeEvent | null>(null);

  useEffect(() => {
    const handleWorkbookChange = (event: CustomEvent<WorkbookChangePayload>) => {
      setLastWorkbookChange(event.detail);
    };

    const handleSheetChange = (event: CustomEvent<WorkbookChangePayload>) => {
      setLastSheetChange(event.detail);
    };

    const handleCellChange = (event: CustomEvent<CellChangeEvent>) => {
      setLastCellChange(event.detail);
    };

    window.addEventListener('workbook-changed', handleWorkbookChange as EventListener);
    window.addEventListener('sheet-changed', handleSheetChange as EventListener);
    window.addEventListener('cell-changed', handleCellChange as EventListener);

    return () => {
      window.removeEventListener('workbook-changed', handleWorkbookChange as EventListener);
      window.removeEventListener('sheet-changed', handleSheetChange as EventListener);
      window.removeEventListener('cell-changed', handleCellChange as EventListener);
    };
  }, []);

  return {
    lastWorkbookChange,
    lastSheetChange,
    lastCellChange,
  };
}
