import { Metadata } from 'next';
import React from 'react';

export const metadata: Metadata = {
  title: 'Termos de Uso | Excel Copilot',
  description: 'Termos de uso do Excel Copilot',
};

export default function TermsPage() {
  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <h1 className="text-3xl font-bold mb-6">Termos de Uso</h1>

      <div className="prose max-w-none">
        <p className="mb-4">Última atualização: {new Date().toLocaleDateString('pt-BR')}</p>

        <h2 className="text-2xl font-semibold mt-6 mb-3">1. Introdução</h2>
        <p>
          Bem-vindo ao Excel Copilot. Estes termos e condições descrevem as regras e regulamentos
          para o uso do website do Excel Copilot. Ao acessar este site, assumimos que você aceita
          estes termos e condições. Não continue a usar o Excel Copilot se você não concordar em
          aceitar todos os termos e condições estabelecidos nesta página.
        </p>

        <h2 className="text-2xl font-semibold mt-6 mb-3">2. Contas e Responsabilidades</h2>
        <p>
          Ao criar uma conta em nosso serviço, você é responsável por manter a segurança de sua
          conta e senha. Você é responsável por todas as atividades que ocorrem sob sua conta. O
          Excel Copilot não será responsável por quaisquer perdas ou danos decorrentes do seu não
          cumprimento desta obrigação de segurança.
        </p>

        <h2 className="text-2xl font-semibold mt-6 mb-3">3. Uso Aceitável</h2>
        <p>O uso do Excel Copilot está sujeito às seguintes condições:</p>
        <ul className="list-disc pl-6 mb-4">
          <li>
            Você não deve usar este site de forma que cause, ou possa causar, danos ao site ou
            comprometer a disponibilidade ou acessibilidade do Excel Copilot.
          </li>
          <li>Você não deve usar este site de forma ilegal, fraudulenta ou prejudicial.</li>
          <li>
            Você não deve usar este site para copiar, armazenar, hospedar, transmitir, enviar, usar,
            publicar ou distribuir qualquer material que consista (ou esteja vinculado a) qualquer
            spyware, vírus de computador, cavalo de Troia, worm, keystroke logger, rootkit ou outro
            software de computador malicioso.
          </li>
          <li>
            Você não deve conduzir nenhuma atividade de coleta sistemática ou automatizada de dados
            neste site ou em relação a este site sem o consentimento expresso por escrito do Excel
            Copilot.
          </li>
        </ul>

        <h2 className="text-2xl font-semibold mt-6 mb-3">4. Conteúdo do Usuário</h2>
        <p>
          Ao enviar conteúdo para o Excel Copilot, você concede ao Excel Copilot uma licença
          mundial, irrevogável, não exclusiva, isenta de royalties para usar, reproduzir, adaptar,
          publicar, traduzir e distribuir seu conteúdo em qualquer mídia existente ou futura. Você
          também concede ao Excel Copilot o direito de sublicenciar esses direitos e o direito de
          processar qualquer pessoa por violação desses direitos.
        </p>

        <h2 className="text-2xl font-semibold mt-6 mb-3">5. Pagamentos e Assinaturas</h2>
        <p>
          Para alguns dos nossos serviços, requeremos pagamento. Ao assinar um plano pago, você
          concorda com os seguintes termos:
        </p>
        <ul className="list-disc pl-6 mb-4">
          <li>O pagamento será cobrado de acordo com o plano escolhido (mensal ou anual).</li>
          <li>
            As assinaturas serão renovadas automaticamente, a menos que você cancele antes da
            próxima cobrança.
          </li>
          <li>
            Você pode cancelar sua assinatura a qualquer momento através da sua página de conta.
          </li>
          <li>Não oferecemos reembolsos para pagamentos já efetuados.</li>
        </ul>

        <h2 className="text-2xl font-semibold mt-6 mb-3">6. Limitação de Responsabilidade</h2>
        <p>
          Em nenhum caso o Excel Copilot, nem seus diretores, empregados, parceiros, agentes,
          fornecedores ou afiliados serão responsáveis por quaisquer danos indiretos, incidentais,
          especiais, consequenciais ou punitivos, incluindo, sem limitação, perda de lucros, dados,
          uso, boa vontade ou outras perdas intangíveis, resultantes do seu acesso ou uso ou
          incapacidade de acessar ou usar o serviço.
        </p>

        <h2 className="text-2xl font-semibold mt-6 mb-3">7. Indenização</h2>
        <p>
          Você concorda em defender, indenizar e isentar o Excel Copilot, seus contratados, e seus
          licenciadores, e seus respectivos diretores, funcionários e agentes de e contra quaisquer
          e todas as reivindicações, danos, obrigações, perdas, responsabilidades, custos ou
          dívidas, e despesas resultantes de: (i) seu uso e acesso aos serviços; (ii) sua violação
          de qualquer termo destes Termos; (iii) violação dos direitos de qualquer terceiro,
          incluindo, sem limitação, qualquer direito de propriedade intelectual, publicidade,
          confidencialidade, propriedade ou direito de privacidade.
        </p>

        <h2 className="text-2xl font-semibold mt-6 mb-3">8. Alterações nos Termos</h2>
        <p>
          Reservamo-nos o direito, a nosso critério, de modificar ou substituir estes termos a
          qualquer momento. É sua responsabilidade revisar estes termos periodicamente para
          alterações. Seu uso contínuo do site após a publicação de quaisquer alterações aos termos
          constitui a aceitação dessas alterações.
        </p>

        <h2 className="text-2xl font-semibold mt-6 mb-3">9. Lei Aplicável</h2>
        <p>
          Estes termos serão regidos e interpretados de acordo com as leis do Brasil, sem levar em
          conta seus princípios de conflito de leis.
        </p>

        <h2 className="text-2xl font-semibold mt-6 mb-3">10. Contato</h2>
        <p>
          Se você tiver alguma dúvida sobre estes Termos, entre em contato conosco em
          <EMAIL>.
        </p>
      </div>
    </div>
  );
}
