/**
 * 📊 HEALTH METRICS ENDPOINT
 *
 * GET /api/health/metrics - Obtém métricas de health checks
 * GET /api/health/metrics?service=database - Métricas de um serviço específico
 * GET /api/health/metrics?period=24 - Métricas de um período específico (horas)
 *
 * Fornece métricas de performance e disponibilidade dos health checks
 */

import { NextRequest, NextResponse } from 'next/server';

// import { metricsCollector } from '@/lib/monitoring/metrics-collector'; // Removido - não existe mais

// Mock metrics collector para compatibilidade
const metricsCollector = {
  getMetrics: () => ({ uptime: Date.now(), requests: 0, errors: 0 }),
  getServiceMetrics: () => ({ status: 'healthy', responseTime: 0 })
};

export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const service = url.searchParams.get('service');
    const period = parseInt(url.searchParams.get('period') || '1');
    const format = url.searchParams.get('format') || 'json';

    // Validar período
    if (period < 1 || period > 168) {
      // Máximo 1 semana
      return NextResponse.json(
        { error: 'Period must be between 1 and 168 hours' },
        { status: 400 }
      );
    }

    let response: {
      type: string;
      service?: string;
      period: string;
      data: unknown;
      timestamp: string;
      collector_stats?: unknown;
    };

    if (service) {
      // Métricas de um serviço específico
      const serviceMetrics = metricsCollector.getServiceMetrics(service, period);

      response = {
        type: 'service_metrics',
        service,
        period: `${period}h`,
        data: serviceMetrics,
        timestamp: new Date().toISOString(),
      };
    } else {
      // Métricas do sistema completo
      const systemMetrics = metricsCollector.getSystemMetrics(period);

      response = {
        type: 'system_metrics',
        period: `${period}h`,
        data: systemMetrics,
        timestamp: new Date().toISOString(),
      };
    }

    // Adicionar estatísticas do coletor
    response.collector_stats = metricsCollector.getStats();

    // Formato Prometheus (se solicitado)
    if (format === 'prometheus') {
      const prometheusMetrics = convertToPrometheus(response.data as Record<string, unknown>);
      return new Response(prometheusMetrics, {
        status: 200,
        headers: {
          'Content-Type': 'text/plain; charset=utf-8',
          'Cache-Control': 'no-cache, no-store, must-revalidate',
        },
      });
    }

    return NextResponse.json(response, {
      status: 200,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        Pragma: 'no-cache',
        Expires: '0',
      },
    });
  } catch (error: unknown) {
    return NextResponse.json(
      {
        error: 'Failed to retrieve health metrics',
        message: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      },
      {
        status: 500,
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
        },
      }
    );
  }
}

/**
 * Converte métricas para formato Prometheus
 */
function convertToPrometheus(data: Record<string, unknown>): string {
  let output = '';

  if (data.overall) {
    // Métricas do sistema
    const overall = data.overall as Record<string, unknown>;
    output += `# HELP excel_copilot_health_availability System health availability percentage\n`;
    output += `# TYPE excel_copilot_health_availability gauge\n`;
    output += `excel_copilot_health_availability ${overall.availability}\n\n`;

    output += `# HELP excel_copilot_health_response_time_ms Average health check response time in milliseconds\n`;
    output += `# TYPE excel_copilot_health_response_time_ms gauge\n`;
    output += `excel_copilot_health_response_time_ms ${overall.avgResponseTime}\n\n`;

    output += `# HELP excel_copilot_health_error_rate Health check error rate percentage\n`;
    output += `# TYPE excel_copilot_health_error_rate gauge\n`;
    output += `excel_copilot_health_error_rate ${overall.errorRate}\n\n`;

    output += `# HELP excel_copilot_health_services_total Total number of monitored services\n`;
    output += `# TYPE excel_copilot_health_services_total gauge\n`;
    output += `excel_copilot_health_services_total ${overall.totalServices}\n\n`;

    output += `# HELP excel_copilot_health_services_healthy Number of healthy services\n`;
    output += `# TYPE excel_copilot_health_services_healthy gauge\n`;
    output += `excel_copilot_health_services_healthy ${overall.healthyServices}\n\n`;

    // Métricas por serviço
    if (data.services) {
      const services = data.services as Record<string, unknown>[];
      services.forEach((service: Record<string, unknown>) => {
        const serviceName = service.service;

        output += `# HELP excel_copilot_health_service_availability Service health availability percentage\n`;
        output += `# TYPE excel_copilot_health_service_availability gauge\n`;
        const serviceMetrics = service.metrics as Record<string, unknown>;
        output += `excel_copilot_health_service_availability{service="${serviceName}"} ${serviceMetrics.availability}\n\n`;

        output += `# HELP excel_copilot_health_service_response_time_ms Service health check response time in milliseconds\n`;
        output += `# TYPE excel_copilot_health_service_response_time_ms gauge\n`;
        output += `excel_copilot_health_service_response_time_ms{service="${serviceName}"} ${serviceMetrics.avgResponseTime}\n\n`;

        output += `# HELP excel_copilot_health_service_error_rate Service health check error rate percentage\n`;
        output += `# TYPE excel_copilot_health_service_error_rate gauge\n`;
        output += `excel_copilot_health_service_error_rate{service="${serviceName}"} ${serviceMetrics.errorRate}\n\n`;
      });
    }
  } else if (data.service) {
    // Métricas de um serviço específico
    const serviceName = data.service;
    const metrics = data.metrics as Record<string, unknown>;

    output += `# HELP excel_copilot_health_service_availability Service health availability percentage\n`;
    output += `# TYPE excel_copilot_health_service_availability gauge\n`;
    output += `excel_copilot_health_service_availability{service="${serviceName}"} ${metrics.availability}\n\n`;

    output += `# HELP excel_copilot_health_service_response_time_ms Service health check response time metrics\n`;
    output += `# TYPE excel_copilot_health_service_response_time_ms gauge\n`;
    output += `excel_copilot_health_service_response_time_ms{service="${serviceName}",quantile="avg"} ${metrics.avgResponseTime}\n`;
    output += `excel_copilot_health_service_response_time_ms{service="${serviceName}",quantile="min"} ${metrics.minResponseTime}\n`;
    output += `excel_copilot_health_service_response_time_ms{service="${serviceName}",quantile="max"} ${metrics.maxResponseTime}\n`;
    output += `excel_copilot_health_service_response_time_ms{service="${serviceName}",quantile="p95"} ${metrics.p95ResponseTime}\n`;
    output += `excel_copilot_health_service_response_time_ms{service="${serviceName}",quantile="p99"} ${metrics.p99ResponseTime}\n\n`;

    output += `# HELP excel_copilot_health_service_checks_total Total number of health checks\n`;
    output += `# TYPE excel_copilot_health_service_checks_total counter\n`;
    output += `excel_copilot_health_service_checks_total{service="${serviceName}"} ${metrics.totalChecks}\n\n`;
  }

  return output;
}

// Permitir apenas GET
export async function POST() {
  return NextResponse.json({ error: 'Method not allowed' }, { status: 405 });
}

export async function PUT() {
  return NextResponse.json({ error: 'Method not allowed' }, { status: 405 });
}

export async function DELETE() {
  return NextResponse.json({ error: 'Method not allowed' }, { status: 405 });
}
