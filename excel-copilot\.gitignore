# Dependencies
node_modules/
.pnp
.pnp.js

# Testing
/coverage

# Next.js
/.next/
/out/

# Production
/build

# Misc
.DS_Store
*.pem

# Debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment files (SECURITY CRITICAL)
.env
.env*.local
.env.production
.env.development
.env.staging
.env.do.vercel
.env.verificacao
.env.backup
.env.old
*.env.backup
**/credentials.json
**/*-credentials.*

# Vercel
.vercel

# TypeScript
*.tsbuildinfo
next-env.d.ts

# Credentials and sensitive files
vertex-credentials.json
google-credentials.json
*.pem
*.key
*.crt

# Scripts with hardcoded credentials (SECURITY)
scripts/configure-vercel-env-final.js
scripts/fix-vercel-env.js
scripts/configure-vercel-env-critical.js
scripts/*-with-credentials.js

# Logs
logs/
*.log

# Sentry Config File
.env.sentry-build-plugin
