/* eslint-disable @typescript-eslint/no-require-imports */
/* eslint-disable no-console */
/* eslint-disable @typescript-eslint/no-unused-vars */
const { exec } = require('child_process');
const fs = require('fs');
const path = require('path');

// Processa o resultado do ESLint para encontrar arquivos com console.log
function processLintOutput(output) {
  const fileIssues = {};

  // Extrair informações do output
  const lines = output.split('\n');
  let currentFile = null;

  for (const line of lines) {
    // Verifica se é uma linha de arquivo
    if (line.startsWith('./')) {
      currentFile = line.split(':')[0].substring(2); // Remove './'
    } else if (
      currentFile &&
      line.includes('Warning:') &&
      line.includes('Unexpected console statement')
    ) {
      // Extrai o número da linha
      const match = line.match(/(\d+):(\d+)\s+Warning:/);
      if (match) {
        const [_, lineNum, colNum] = match;

        if (!fileIssues[currentFile]) {
          fileIssues[currentFile] = [];
        }

        fileIssues[currentFile].push({
          line: parseInt(lineNum),
          column: parseInt(colNum),
        });
      }
    }
  }

  return fileIssues;
}

// Cria o arquivo do logger se não existir
function ensureLoggerExists() {
  const loggerPath = path.join(process.cwd(), 'src/lib/logger.ts');

  // Se o logger já existe, não fazemos nada
  if (fs.existsSync(loggerPath)) {
    const content = fs.readFileSync(loggerPath, 'utf8');

    // Verifica se o logger já tem as funções que precisamos
    if (content.includes('safeConsoleLog') && content.includes('isDev')) {
      return;
    }

    // Adiciona as funções necessárias ao logger existente
    const newContent = `${content.trimEnd()}

// Environment detection
const isDev = process.env.NODE_ENV !== 'production';

// Safe console functions that only log in development
export const safeConsoleLog = isDev ? console.log : () => {};
export const safeConsoleError = isDev ? console.error : () => {};
export const safeConsoleWarn = isDev ? console.warn : () => {};
export const safeConsoleInfo = isDev ? console.info : () => {};
`;

    fs.writeFileSync(loggerPath, newContent);
    console.log('✓ Atualizado: src/lib/logger.ts');
  } else {
    // Cria o arquivo logger.ts
    const loggerContent = `/**
 * Logger seguro para o aplicativo que evita logs em produção
 */

// Environment detection
const isDev = process.env.NODE_ENV !== 'production';

// Safe console functions that only log in development
export const safeConsoleLog = isDev ? console.log : () => {};
export const safeConsoleError = isDev ? console.error : () => {};
export const safeConsoleWarn = isDev ? console.warn : () => {};
export const safeConsoleInfo = isDev ? console.info : () => {};

/**
 * Logger centralizado do aplicativo
 */
export const logger = {
  log: safeConsoleLog,
  error: safeConsoleError,
  warn: safeConsoleWarn,
  info: safeConsoleInfo,

  // Funções para medir performance
  time: isDev ? console.time : () => {},
  timeEnd: isDev ? console.timeEnd : () => {},

  // Versão que funciona em todos os ambientes e pode ser configurada
  debug: (message, ...args) => {
    if (isDev) {
      console.log(\`[DEBUG] \${message}\`, ...args);
    }
  }
};

export default logger;
`;

    fs.writeFileSync(loggerPath, loggerContent);
    console.log('✓ Criado: src/lib/logger.ts');
  }
}

// Substitui console.log por safeConsoleLog nos arquivos
function fixConsoleStatements(fileIssues) {
  // Primeiro garantimos que o logger existe
  ensureLoggerExists();

  for (const [filePath, issues] of Object.entries(fileIssues)) {
    try {
      const fullPath = path.join(process.cwd(), filePath);
      const content = fs.readFileSync(fullPath, 'utf8');

      // Verifique se já importamos o logger
      let hasLoggerImport =
        content.includes('import { safeConsoleLog') ||
        content.includes('import logger') ||
        content.includes('import { logger }');

      const lines = content.split('\n');
      let modified = false;

      // Ordenar problemas por linha em ordem decrescente
      issues.sort((a, b) => b.line - a.line);

      for (const issue of issues) {
        const line = lines[issue.line - 1];

        // Substitui console.log por safeConsoleLog
        if (line.includes('console.log')) {
          const newLine = line.replace(/console\.log/g, 'safeConsoleLog');
          lines[issue.line - 1] = newLine;
          modified = true;
        }
        // Substitui console.error por safeConsoleError
        else if (line.includes('console.error')) {
          const newLine = line.replace(/console\.error/g, 'safeConsoleError');
          lines[issue.line - 1] = newLine;
          modified = true;
        }
        // Substitui console.warn por safeConsoleWarn
        else if (line.includes('console.warn')) {
          const newLine = line.replace(/console\.warn/g, 'safeConsoleWarn');
          lines[issue.line - 1] = newLine;
          modified = true;
        }
        // Substitui console.info por safeConsoleInfo
        else if (line.includes('console.info')) {
          const newLine = line.replace(/console\.info/g, 'safeConsoleInfo');
          lines[issue.line - 1] = newLine;
          modified = true;
        }
      }

      // Adicionar import para o logger se necessário
      if (modified && !hasLoggerImport) {
        // Encontra a última linha de imports
        let lastImportIndex = -1;
        for (let i = 0; i < lines.length; i++) {
          if (lines[i].startsWith('import ')) {
            lastImportIndex = i;
          } else if (lastImportIndex >= 0 && lines[i].trim() === '') {
            // Encontrou uma linha em branco após imports
            break;
          }
        }

        if (lastImportIndex >= 0) {
          // Adiciona import após o último import existente
          lines.splice(
            lastImportIndex + 1,
            0,
            "import { safeConsoleLog, safeConsoleError, safeConsoleWarn, safeConsoleInfo } from '@/lib/logger';"
          );
        } else {
          // Adiciona no início do arquivo
          lines.unshift(
            "import { safeConsoleLog, safeConsoleError, safeConsoleWarn, safeConsoleInfo } from '@/lib/logger';"
          );
        }
        modified = true;
      }

      if (modified) {
        fs.writeFileSync(fullPath, lines.join('\n'), 'utf8');
        console.log(`✓ Corrigido: ${filePath}`);
      }
    } catch (error) {
      console.error(`Erro ao processar ${filePath}:`, error.message);
    }
  }
}

// Executa o lint e processa os resultados
exec('npm run lint', (error, stdout, stderr) => {
  if (error) {
    console.error(`Erro ao executar lint: ${error.message}`);
    return;
  }

  console.log('Processando resultados do lint para substituir console.log...');
  const fileIssues = processLintOutput(stdout);

  console.log(`Encontrados console statements em ${Object.keys(fileIssues).length} arquivos.`);
  fixConsoleStatements(fileIssues);

  console.log('Concluído! Execute npm run lint novamente para verificar as correções.');
});
