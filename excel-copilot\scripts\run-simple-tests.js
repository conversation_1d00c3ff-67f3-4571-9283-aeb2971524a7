/**
 * Script para executar todos os testes simples em sequência
 */

const { spawn } = require('child_process');
const path = require('path');

// Scripts a serem executados
const scripts = ['test-db-basic.js', 'test-chat.js', 'test-workbook.js'];

/**
 * Função para executar um script Node.js
 */
function executeScript(scriptName) {
  return new Promise(resolve => {
    console.log(`\n======================================================`);
    console.log(`EXECUTANDO: ${scriptName}`);
    console.log(`======================================================\n`);

    // Usar spawn em vez de execSync para melhor controle
    const nodeProcess = spawn('node', [`scripts/${scriptName}`], {
      stdio: 'inherit',
      shell: true,
    });

    // Quando o processo terminar
    nodeProcess.on('close', code => {
      if (code === 0) {
        console.log(`\n✅ Script ${scriptName} executado com sucesso.`);
        resolve(true);
      } else {
        console.log(`\n❌ Script ${scriptName} falhou com código ${code}.`);
        resolve(false);
      }
    });
  });
}

/**
 * Função principal para executar todos os scripts em sequência
 */
async function runAllTests() {
  console.log('=== INICIANDO TESTES SIMPLES DO EXCEL COPILOT ===\n');

  let successCount = 0;
  let failureCount = 0;

  for (let i = 0; i < scripts.length; i++) {
    const script = scripts[i];
    console.log(`\nExecutando teste ${i + 1}/${scripts.length}: ${script}`);

    const success = await executeScript(script);
    if (success) {
      successCount++;
    } else {
      failureCount++;
    }
  }

  // Resumo final
  console.log('\n======================================================');
  console.log('RESUMO DOS TESTES:');
  console.log(`Total de testes: ${scripts.length}`);
  console.log(`Testes bem-sucedidos: ${successCount}`);
  console.log(`Testes com falha: ${failureCount}`);
  console.log('======================================================');

  if (failureCount === 0) {
    console.log('\n🎉 TODOS OS TESTES CONCLUÍDOS COM SUCESSO! 🎉');
  } else {
    console.log(`\n⚠️ ${failureCount} TESTE(S) FALHOU(ARAM).`);
  }
}

// Executar a função principal
runAllTests().catch(error => {
  console.error('Erro ao executar testes:', error);
  process.exit(1);
});
