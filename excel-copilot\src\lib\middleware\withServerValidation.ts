/**
 * Middleware centralizado para validação no servidor
 *
 * Este módulo fornece funções para aplicar validação, autenticação,
 * rate limiting e outras verificações de segurança em rotas de API
 * de forma centralizada no servidor.
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { z } from 'zod';

// Resolvendo problemas de importação com mocks

// Definindo enum ErrorType localmente
enum ErrorType {
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  UNAUTHORIZED = 'UNAUTHORIZED',
  FORBIDDEN = 'FORBIDDEN',
  NOT_FOUND = 'NOT_FOUND',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
}

// Mock do ENV
const ENV = {
  FEATURES: {
    RATE_LIMITING: false,
    CSRF_PROTECTION: false,
  },
  IS_DEVELOPMENT: true,
  NEXT_PUBLIC_VERCEL_ENV: 'development',
};

// Mock do logger
const logger = {
  info: (message: string, ...args: any[]) => {},
  error: (message: string, ...args: any[]) => {},
  warn: (message: string, ...args: any[]) => {},
  debug: (message: string, ...args: any[]) => {},
};

// Mock do telemetry
const MetricType = {
  COUNTER: 'counter',
  GAUGE: 'gauge',
  HISTOGRAM: 'histogram',
};

const telemetry = {
  recordMetric: (name: string, value: number) => {
    return { name, value };
  },
};

// Mock das funções de segurança
interface SanitizationResult<T = unknown> {
  isValid: boolean;
  sanitized?: T;
  originalValue?: unknown;
  errors?: string[];
}

const validateAndSanitize = <T>(input: unknown, schema: any): SanitizationResult<T> => {
  return { isValid: true, sanitized: input as T };
};

const hasSuspiciousPatterns = (input: string): boolean => {
  return false;
};

const deepSanitizeObject = <T extends Record<string, unknown>>(obj: T): T => {
  return obj;
};

const getRateLimiter = () => ({
  limit: (req: any) => Promise.resolve({ success: true }),
});

const withServerCache = (handler: any) => handler;

const requireCSRFToken = (handler: any) => handler;

// Tipo de função manipuladora para rotas de API Next.js
type NextApiHandler = (req: NextRequest, context: any) => Promise<NextResponse> | NextResponse;

// Interface para resultado de validação
interface ValidationResult<T = any> {
  success: boolean;
  data?: T;
  error?: any;
}

/**
 * Configurações para o middleware de validação
 */
export interface ValidationOptions {
  // Se deve permitir campos extras além dos definidos no schema
  allowExtraFields?: boolean;
  // Se deve gerar telemetria sobre a validação
  trackMetrics?: boolean;
  // Mensagens de erro personalizadas
  customErrorMessages?: Record<string, string>;
}

/**
 * Middleware para validar dados de requisição com suporte a schemas Zod
 * @param schema Schema Zod para validação
 * @param options Opções de validação
 * @returns Middleware com validação
 */
export function withServerValidation<T>(schema: z.Schema<T>, options: ValidationOptions = {}) {
  return async function (handler: (req: NextRequest, data: T) => Promise<NextResponse>) {
    return async function (req: NextRequest) {
      const startTime = Date.now();
      const method = req.method;
      const url = req.url;

      try {
        let body: any;

        // Parse do corpo da requisição
        try {
          body = await req.json();
        } catch (error) {
          throw new Error('Formato de dados inválido. Esperado JSON válido.');
        }

        // Configurações de validação
        const validationOptions = {
          allowExtraFields: options.allowExtraFields ?? false,
        };

        // Utilizar sanitização e validação integrada
        const result: SanitizationResult<T> = validateAndSanitize(body, schema);

        if (!result.isValid) {
          const errorDetails = result.errors || ['Erro de validação não especificado'];

          throw new Error(`Dados inválidos na requisição: ${errorDetails.join(', ')}`);
        }

        // Registrar métricas de validação se solicitado
        if (options.trackMetrics) {
          const duration = Date.now() - startTime;
          telemetry.recordMetric('api.validation.success', 1);
        }

        // Executar o manipulador com os dados validados e sanitizados
        return handler(req, result.sanitized as T);
      } catch (error) {
        logger.error(`[Validation Error] ${method} ${url}: ${(error as Error).message}`, {
          url,
          method,
          error: error instanceof Error ? error.message : String(error),
          duration: Date.now() - startTime,
        });

        // Rastrear métricas de erro
        if (options.trackMetrics) {
          telemetry.recordMetric('api.validation.error', 1);
        }

        // Formatar resposta de erro
        if (error instanceof Error && 'type' in error) {
          return NextResponse.json(
            {
              success: false,
              error: {
                message: (error as Error).message,
                type: (error as any).type,
                details: (error as any).details,
              },
            },
            { status: 400 }
          );
        }

        return NextResponse.json(
          {
            success: false,
            error: {
              message: 'Erro de validação dos dados',
              type: ErrorType.VALIDATION_ERROR,
            },
          },
          { status: 400 }
        );
      }
    };
  };
}

/**
 * Middleware para aplicar validação de schema Zod com segurança
 *
 * @param handler Função manipuladora da rota
 * @param schema Schema Zod para validação
 * @param options Opções adicionais
 */
export function validateRequest<T>(
  handler: (req: NextRequest, data: T, context: any) => Promise<NextResponse> | NextResponse,
  schema: z.Schema<T>,
  options: {
    allowedMethods?: string[];
    requireAuth?: boolean;
    csrfProtection?: boolean;
    rateLimit?: {
      limiterName: string;
      limit?: number;
      window?: number;
    };
    sanitizeInput?: boolean;
    cacheResponse?: boolean;
    cacheTTL?: number;
  } = {}
): NextApiHandler {
  const {
    allowedMethods = ['POST', 'PUT', 'PATCH', 'DELETE'],
    requireAuth = true,
    csrfProtection = true,
    rateLimit,
    sanitizeInput = true,
    cacheResponse = false,
    cacheTTL = 300, // 5 minutos
  } = options;

  return async (req: NextRequest, context: any) => {
    try {
      const startTime = Date.now();

      // 1. Verificar método HTTP
      const method = req.method || 'GET';

      if (!allowedMethods.includes(method)) {
        return NextResponse.json(
          {
            error: 'Método não permitido',
            code: 'METHOD_NOT_ALLOWED',
          },
          { status: 405 }
        );
      }

      // 2. Verificar autenticação se necessário
      let userId = 'anonymous';

      if (requireAuth) {
        const session = await getServerSession();

        if (!session?.user) {
          return NextResponse.json(
            {
              error: 'Não autorizado. Faça login para continuar.',
              code: 'UNAUTHORIZED',
            },
            { status: 401 }
          );
        }

        userId = (session.user as any).id || 'anonymous';
      }

      // 3. Verificar CSRF para métodos de mutação
      if (csrfProtection && !['GET', 'HEAD', 'OPTIONS'].includes(method)) {
        try {
          const secret = process.env.SECURITY_CSRF_SECRET || 'default-csrf-secret';
          await requireCSRFToken(req);
        } catch (error) {
          logger.warn(`Falha na validação CSRF: ${method} ${req.nextUrl.pathname}`, { error });

          return NextResponse.json(
            {
              error: 'Token CSRF inválido ou ausente',
              code: 'CSRF_ERROR',
            },
            { status: 403 }
          );
        }
      }

      // 4. Aplicar rate limiting se configurado
      if (rateLimit) {
        const rateLimiter = getRateLimiter();

        if (rateLimiter) {
          try {
            const result = await rateLimiter.limit(req);

            if (!result.success) {
              logger.warn(`Rate limit excedido para usuário ${userId} em ${req.nextUrl.pathname}`);

              return NextResponse.json(
                {
                  error: 'Muitas requisições. Por favor, aguarde e tente novamente.',
                  code: 'RATE_LIMITED',
                },
                { status: 429 }
              );
            }
          } catch (error) {
            logger.error(`Erro ao verificar rate limit: ${rateLimit.limiterName}`, error);
          }
        }
      }

      // 5. Processar e validar o corpo da requisição
      let body: any;

      if (['POST', 'PUT', 'PATCH', 'DELETE'].includes(method)) {
        try {
          if (req.headers.get('content-type')?.includes('multipart/form-data')) {
            body = await req.formData();
          } else {
            const clonedReq = req.clone();
            body = await clonedReq.json();
          }
        } catch (error) {
          return NextResponse.json(
            {
              error: 'Corpo da requisição inválido',
              code: 'INVALID_REQUEST',
            },
            { status: 400 }
          );
        }

        // Sanitizar entradas para evitar XSS e injeções
        if (sanitizeInput && typeof body === 'object' && body !== null) {
          try {
            if (typeof body === 'string' && hasSuspiciousPatterns(body)) {
              logger.warn(`Detectado padrão suspeito na requisição: ${req.nextUrl.pathname}`, {
                userId,
                path: req.nextUrl.pathname,
              });

              return NextResponse.json(
                {
                  error: 'Detectados padrões suspeitos na requisição',
                  code: 'SUSPICIOUS_CONTENT',
                },
                { status: 400 }
              );
            }

            // Sanitizar o corpo para remover scripts e conteúdo malicioso
            if (body && typeof body === 'object') {
              body = deepSanitizeObject(body as Record<string, unknown>);
            }
          } catch (error) {
            logger.error(`Erro ao sanitizar entrada: ${req.nextUrl.pathname}`, error);
          }
        }

        // Validar com o schema Zod fornecido
        let validationResult: ValidationResult;

        try {
          const zodResult = schema.safeParse(body);

          if (!zodResult.success) {
            return NextResponse.json(
              {
                error: 'Validação falhou',
                details: zodResult.error.format(),
                code: 'VALIDATION_ERROR',
              },
              { status: 400 }
            );
          }

          // Usar dados validados e possivelmente transformados pelo Zod
          body = zodResult.data;
        } catch (error) {
          logger.error(`Erro na validação: ${req.nextUrl.pathname}`, error);

          return NextResponse.json(
            {
              error: 'Erro interno durante validação',
              code: 'INTERNAL_VALIDATION_ERROR',
            },
            { status: 500 }
          );
        }
      }

      // 6. Executar o handler com os dados validados
      const handlerFn = async () => {
        try {
          return await handler(req, body, context);
        } catch (error: any) {
          logger.error(`Erro ao processar requisição: ${req.nextUrl.pathname}`, error);

          const statusCode = error.status || error.statusCode || 500;
          const errorMessage =
            ENV.IS_DEVELOPMENT && error.message ? error.message : 'Erro interno do servidor';

          return NextResponse.json(
            {
              error: errorMessage,
              code: error.code || 'INTERNAL_SERVER_ERROR',
            },
            { status: statusCode }
          );
        }
      };

      // 7. Aplicar cache se configurado (apenas para GETs)
      if (cacheResponse && method === 'GET') {
        // Criar chave de cache baseada no URL e parâmetros
        const searchParams = new URL(req.url).searchParams.toString();
        const cacheKey = `api:${req.nextUrl.pathname}${searchParams ? '?' + searchParams : ''}`;

        try {
          // Usando withServerCache apenas com o handler (removido parâmetros extras)
          return await withServerCache(handlerFn);
        } catch (error) {
          // Se houver erro no cache, executar normalmente
          logger.warn(`Erro ao usar cache para: ${cacheKey}`, error);
          return await handlerFn();
        }
      } else {
        // Executar handler sem cache
        return await handlerFn();
      }
    } catch (error) {
      // Capturar erros não tratados
      logger.error(`Erro não tratado na request ${req.nextUrl.pathname}:`, error);

      return NextResponse.json(
        {
          error: 'Erro interno do servidor',
          code: 'INTERNAL_SERVER_ERROR',
        },
        { status: 500 }
      );
    }
  };
}

/**
 * Versão mais simples para validação de formulários
 */
export function validateForm<T>(
  handler: (req: NextRequest, data: T, context: any) => Promise<NextResponse> | NextResponse,
  schema: z.Schema<T>
): NextApiHandler {
  return validateRequest(handler, schema, {
    allowedMethods: ['POST'],
    requireAuth: true,
    csrfProtection: true,
    sanitizeInput: true,
  });
}

/**
 * Versão para endpoints públicos que não requerem autenticação
 */
export function validatePublicRequest<T extends Record<string, any>>(
  handler: (req: NextRequest, data: T, context: any) => Promise<NextResponse> | NextResponse,
  schema: z.ZodSchema<T>,
  options: {
    allowedMethods?: string[];
    csrfProtection?: boolean;
    cacheResponse?: boolean;
    cacheTTL?: number;
  } = {}
): NextApiHandler {
  return validateRequest(handler, schema, {
    ...options,
    requireAuth: false,
  });
}

/**
 * Helper para criar uma resposta de erro padronizada
 */
export function errorResponse(
  message: string,
  code: string = 'ERROR',
  status: number = 400,
  details?: any
): NextResponse {
  return NextResponse.json(
    {
      error: message,
      code,
      ...(details ? { details } : {}),
    },
    { status }
  );
}
