#!/usr/bin/env node

/**
 * Script de Build Rápido - Excel Copilot
 *
 * Otimizações implementadas:
 * - Limpeza seletiva de cache
 * - Configuração otimizada de memória
 * - Desabilitação temporária de features pesadas
 * - Monitoramento de progresso
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Cores simples para console
const colors = {
  blue: text => `\x1b[34m${text}\x1b[0m`,
  green: text => `\x1b[32m${text}\x1b[0m`,
  yellow: text => `\x1b[33m${text}\x1b[0m`,
  red: text => `\x1b[31m${text}\x1b[0m`,
  gray: text => `\x1b[90m${text}\x1b[0m`,
  bold: text => `\x1b[1m${text}\x1b[0m`,
};

console.log(colors.blue('🚀 Iniciando Build Rápido do Excel Copilot...\n'));

// Configurações otimizadas
const BUILD_CONFIG = {
  NODE_OPTIONS: '--max-old-space-size=8192 --max-semi-space-size=1024',
  DISABLE_SENTRY_UPLOAD: 'true', // Desabilita upload do Sentry em builds locais
  NEXT_TELEMETRY_DISABLED: '1', // Desabilita telemetria
  DISABLE_ESLINT: 'true', // Pula ESLint durante build
  DISABLE_TYPE_CHECK: 'true', // Pula verificação de tipos
};

function runCommand(command, description) {
  console.log(colors.yellow(`⏳ ${description}...`));
  const startTime = Date.now();

  try {
    execSync(command, {
      stdio: 'inherit',
      env: { ...process.env, ...BUILD_CONFIG },
    });

    const duration = ((Date.now() - startTime) / 1000).toFixed(1);
    console.log(colors.green(`✅ ${description} concluído em ${duration}s\n`));
  } catch (error) {
    console.error(colors.red(`❌ Erro em: ${description}`));
    console.error(error.message);
    process.exit(1);
  }
}

function cleanCache() {
  console.log(colors.yellow('🧹 Limpando cache seletivamente...'));

  const cachePaths = [
    '.next',
    'node_modules/.cache',
    '.swc', // Cache do SWC
  ];

  cachePaths.forEach(cachePath => {
    if (fs.existsSync(cachePath)) {
      try {
        execSync(`rimraf ${cachePath}`, { stdio: 'pipe' });
        console.log(colors.gray(`   Removido: ${cachePath}`));
      } catch (error) {
        console.log(colors.yellow(`   Aviso: Não foi possível remover ${cachePath}`));
      }
    }
  });

  console.log(colors.green('✅ Cache limpo\n'));
}

function checkOptimizations() {
  console.log(colors.blue('🔍 Verificando otimizações...'));

  // Verificar se SWC está habilitado
  const babelrcExists = fs.existsSync('.babelrc');
  if (babelrcExists) {
    console.log(colors.yellow('⚠️  Babel detectado - SWC será mais rápido'));
    console.log(colors.gray('   Considere renomear .babelrc para .babelrc.disabled'));
  } else {
    console.log(colors.green('✅ SWC habilitado (20x mais rápido que Babel)'));
  }

  // Verificar configuração de memória
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  const buildScript = packageJson.scripts.build;
  if (buildScript.includes('8192')) {
    console.log(colors.green('✅ Memória otimizada (8GB)'));
  } else {
    console.log(colors.yellow('⚠️  Memória pode ser otimizada'));
  }

  console.log();
}

// Executar build rápido
async function fastBuild() {
  const totalStartTime = Date.now();

  try {
    // 1. Verificar otimizações
    checkOptimizations();

    // 2. Limpeza seletiva
    cleanCache();

    // 3. Build otimizado
    console.log(colors.blue('🏗️  Iniciando build otimizado...\n'));

    const buildCommand = `npx cross-env ${Object.entries(BUILD_CONFIG)
      .map(([key, value]) => `${key}="${value}"`)
      .join(' ')} next build`;

    runCommand(buildCommand, 'Build do Next.js');

    // 4. Relatório final
    const totalDuration = ((Date.now() - totalStartTime) / 1000).toFixed(1);
    console.log(colors.bold(colors.green(`🎉 Build rápido concluído em ${totalDuration}s!`)));
    console.log(
      colors.gray('💡 Para builds ainda mais rápidos, considere usar: npm run build:dev\n')
    );
  } catch (error) {
    console.error(colors.red('❌ Falha no build rápido:'));
    console.error(error.message);
    process.exit(1);
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  fastBuild();
}

module.exports = { fastBuild };
