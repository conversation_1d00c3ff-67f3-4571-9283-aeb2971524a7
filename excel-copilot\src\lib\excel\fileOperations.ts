import * as ExcelJS from 'exceljs';

/**
 * Converte dados de planilha do formato JSON para um arquivo Excel
 * @param sheets Array de sheets com dados em formato JSON
 * @param workbookName Nome para o arquivo Excel
 * @returns Blob com o arquivo Excel
 */
export async function createExcelFile(
  sheets: { name: string; data: any }[],
  _workbookName: string
): Promise<Blob> {
  // Criar um novo workbook
  const workbook = new ExcelJS.Workbook();
  workbook.creator = 'Excel Copilot';
  workbook.lastModifiedBy = 'Excel Copilot';
  workbook.created = new Date();
  workbook.modified = new Date();

  // Processar cada sheet
  for (const sheetData of sheets) {
    // Criar uma nova planilha
    const worksheet = workbook.addWorksheet(sheetData.name);

    // Se não houver dados, criar uma planilha vazia
    if (!sheetData.data || Object.keys(sheetData.data).length === 0) {
      continue;
    }

    // Verificar formato dos dados
    if (Array.isArray(sheetData.data)) {
      // Formato de array de objetos (registros)
      if (sheetData.data.length > 0 && typeof sheetData.data[0] === 'object') {
        // Extrair cabeçalhos
        const headers = Object.keys(sheetData.data[0]);

        // Adicionar cabeçalhos
        worksheet.columns = headers.map(header => ({
          header,
          key: header,
          width: Math.max(header.length, 10),
        }));

        // Adicionar linhas
        worksheet.addRows(sheetData.data);
      }
    } else if (typeof sheetData.data === 'object') {
      // Formato de células ({A1: valor, B2: valor})
      Object.entries(sheetData.data).forEach(([cellRef, value]) => {
        worksheet.getCell(cellRef).value = value as ExcelJS.CellValue;
      });
    }

    // Estilizar cabeçalhos
    const firstRowValues = worksheet.getRow(1).values;
    if (firstRowValues && Array.isArray(firstRowValues) && firstRowValues.length > 1) {
      worksheet.getRow(1).font = { bold: true };
      worksheet.getRow(1).fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE6F0FF' },
      };
    }
  }

  // Gerar arquivo Excel
  const buffer = await workbook.xlsx.writeBuffer();
  return new Blob([buffer], {
    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  });
}

/**
 * Faz download de um arquivo Excel
 * @param blob Blob com o arquivo Excel
 * @param fileName Nome do arquivo
 */
export function downloadExcelFile(blob: Blob, fileName: string) {
  // Criar URL do blob
  const url = window.URL.createObjectURL(blob);

  // Criar elemento de link
  const a = document.createElement('a');
  a.href = url;
  a.download = fileName.endsWith('.xlsx') ? fileName : `${fileName}.xlsx`;

  // Adicionar ao documento e clicar
  document.body.appendChild(a);
  a.click();

  // Limpar
  window.URL.revokeObjectURL(url);
  document.body.removeChild(a);
}

/**
 * Converte arquivo Excel para estrutura de dados interna
 * @param file Arquivo Excel (.xlsx)
 * @returns Array de sheets com dados em formato JSON
 */
export async function parseExcelFile(file: File): Promise<{ name: string; data: any }[]> {
  try {
    // Criar novo workbook e carregar arquivo
    const workbook = new ExcelJS.Workbook();
    const arrayBuffer = await file.arrayBuffer();
    await workbook.xlsx.load(arrayBuffer);

    // Array para armazenar dados das planilhas
    const sheets: { name: string; data: any }[] = [];

    // Verificar se o workbook tem planilhas
    if (workbook.worksheets.length === 0) {
      throw new Error('Arquivo Excel não contém planilhas');
    }

    // Processar cada worksheet
    workbook.eachSheet(worksheet => {
      try {
        // Verificar se a planilha está vazia
        if (worksheet.rowCount === 0 || worksheet.columnCount === 0) {
          sheets.push({
            name: worksheet.name,
            data: {},
          });
          return;
        }

        // Detectar se a planilha tem cabeçalhos
        const firstRowValues = worksheet.getRow(1).values;
        const secondRowValues = worksheet.getRow(2).values;

        const hasHeaders =
          firstRowValues &&
          Array.isArray(firstRowValues) &&
          firstRowValues.length > 1 &&
          secondRowValues &&
          Array.isArray(secondRowValues) &&
          secondRowValues.length > 1;

        let sheetData: any;

        if (hasHeaders) {
          // Converter para array de objetos usando cabeçalhos
          const headers: string[] = [];
          worksheet.getRow(1).eachCell((cell, colNumber) => {
            headers[colNumber] = cell.value?.toString() || `Column${colNumber}`;
          });

          // Remover valores undefined do início do array
          while (headers[0] === undefined) {
            headers.shift();
          }

          // Verificar se há pelo menos um cabeçalho válido
          if (headers.length === 0) {
            sheets.push({
              name: worksheet.name,
              data: {},
            });
            return;
          }

          // Transformar linhas em objetos
          sheetData = [];
          worksheet.eachRow((row, rowNumber) => {
            if (rowNumber > 1) {
              // Pular cabeçalhos
              const rowData: Record<string, any> = {};
              row.eachCell((cell, colNumber) => {
                const header = headers[colNumber];
                if (header) {
                  rowData[header] = cell.value;
                }
              });
              if (Object.keys(rowData).length > 0) {
                sheetData.push(rowData);
              }
            }
          });
        } else {
          // Formato de células {A1: valor, B2: valor}
          sheetData = {};
          // Usar forEach em vez de eachCell para iterar pelas células
          const rows = worksheet.getRows(1, worksheet.rowCount) || [];
          rows.forEach(row => {
            if (row) {
              row.eachCell(cell => {
                const cellRef = cell.address;
                sheetData[cellRef] = cell.value;
              });
            }
          });
        }

        sheets.push({
          name: worksheet.name,
          data: sheetData,
        });
      } catch (error: any) {
        console.error(`Erro ao processar planilha ${worksheet.name}:`, error);
        sheets.push({
          name: worksheet.name,
          data: { error: `Erro ao processar: ${error.message}` },
        });
      }
    });

    return sheets;
  } catch (error: any) {
    console.error('Erro ao processar arquivo Excel:', error);
    throw new Error(`Falha ao processar arquivo Excel: ${error.message}`);
  }
}
