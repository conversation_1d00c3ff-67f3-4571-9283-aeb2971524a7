/**
 * Configuração global do MSW para os testes
 * Este arquivo centraliza a configuração do Mock Service Worker
 */
import { server } from './server';

// Configurar o servidor de mock antes de todos os testes
beforeAll(() => server.listen());

// Resetar todos os handlers depois de cada teste
// para que os testes não sejam afetados por mocks de outros testes
afterEach(() => server.resetHandlers());

// Fechar o servidor após os testes terminarem
afterAll(() => server.close());
