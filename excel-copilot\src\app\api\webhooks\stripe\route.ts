import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';

import { logger } from '@/lib/logger';
import {
  stripe,
  PLANS,
  API_CALL_LIMITS,
  normalizeSubscriptionStatus,
  STRIPE_WEBHOOK_SECRET,
} from '@/lib/stripe';
import { prisma } from '@/server/db/client';

// Configuração do segmento de rota (novo formato para Next.js 14)
export const dynamic = 'force-dynamic';
export const fetchCache = 'force-no-store';
export const runtime = 'nodejs';

// Importar a chave do webhook definida em stripe.ts

// Função para ler o raw body
async function getRawBody(req: NextRequest): Promise<Buffer> {
  const chunks: Uint8Array[] = [];
  const reader = req.body?.getReader();
  if (!reader) {
    throw new Error('Request body is empty');
  }

  let readDone = false;
  while (!readDone) {
    const { done, value } = await reader.read();
    if (done) {
      readDone = true;
    } else {
      chunks.push(value);
    }
  }

  return Buffer.concat(chunks);
}

export async function POST(req: NextRequest) {
  const startTime = Date.now();
  const ip = req.ip || req.headers.get('x-forwarded-for') || 'unknown';
  const userAgent = req.headers.get('user-agent') || 'unknown';

  try {
    // CORREÇÃO IMPORTANTE: Validação rigorosa de entrada
    const rawBody = await getRawBody(req);
    const signature = req.headers.get('stripe-signature');

    // Verificar se o corpo não está vazio
    if (!rawBody || rawBody.length === 0) {
      logger.warn('[WEBHOOK_SECURITY] Tentativa de acesso com corpo vazio', {
        ip,
        userAgent,
        timestamp: new Date().toISOString(),
      });
      return NextResponse.json({ error: 'Corpo da requisição vazio' }, { status: 400 });
    }

    if (!signature) {
      logger.warn('[WEBHOOK_SECURITY] Tentativa de acesso sem assinatura', {
        ip,
        userAgent,
        bodyLength: rawBody.length,
        timestamp: new Date().toISOString(),
      });
      return NextResponse.json({ error: 'No signature header' }, { status: 400 });
    }

    // Verificar webhook com assinatura do Stripe
    const webhookSecret = STRIPE_WEBHOOK_SECRET;
    if (!webhookSecret) {
      logger.error('[WEBHOOK_ERROR] Stripe webhook secret não configurado', {
        ip,
        timestamp: new Date().toISOString(),
      });
      return NextResponse.json({ error: 'Webhook não configurado' }, { status: 500 });
    }

    let event: Stripe.Event;
    try {
      if (!stripe) {
        throw new Error('Stripe não está configurado');
      }
      event = stripe.webhooks.constructEvent(rawBody, signature, webhookSecret);

      // Log de sucesso na validação
      logger.info('[WEBHOOK_SUCCESS] Assinatura validada com sucesso', {
        eventType: event.type,
        eventId: event.id,
        ip,
        timestamp: new Date().toISOString(),
      });
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';

      // CORREÇÃO IMPORTANTE: Log detalhado de tentativas de bypass
      logger.error('[WEBHOOK_SECURITY] Tentativa de bypass detectada - Assinatura inválida', {
        error: errorMessage,
        ip,
        userAgent,
        signature: signature.substring(0, 20) + '...', // Log parcial por segurança
        bodyLength: rawBody.length,
        timestamp: new Date().toISOString(),
        duration: Date.now() - startTime,
      });

      return NextResponse.json({ error: `Webhook Error: ${errorMessage}` }, { status: 400 });
    }

    // CORREÇÃO IMPORTANTE: Validação adicional - verificar se o evento é recente (previne replay attacks)
    const eventTime = new Date(event.created * 1000);
    const now = new Date();
    const timeDiff = now.getTime() - eventTime.getTime();
    const maxAge = 5 * 60 * 1000; // 5 minutos

    if (timeDiff > maxAge) {
      logger.warn('[WEBHOOK_SECURITY] Evento muito antigo detectado - possível replay attack', {
        eventId: event.id,
        eventType: event.type,
        eventTime: eventTime.toISOString(),
        currentTime: now.toISOString(),
        ageDiff: timeDiff,
        ip,
        userAgent,
      });
      return NextResponse.json({ error: 'Evento expirado' }, { status: 400 });
    }

    // Processar eventos
    switch (event.type) {
      case 'checkout.session.completed': {
        const session = event.data.object as Stripe.Checkout.Session;
        // Processar checkout concluído
        await handleCheckoutCompleted(session);

        // Cache será invalidado automaticamente após 30 minutos
        logger.info('Checkout completed - cache will auto-invalidate');
        break;
      }
      case 'invoice.paid': {
        const invoice = event.data.object as Stripe.Invoice;
        // Processar pagamento de fatura
        await handleInvoicePaid(invoice);
        break;
      }
      case 'customer.subscription.updated': {
        const subscription = event.data.object as Stripe.Subscription;
        // Processar atualização de assinatura
        await handleSubscriptionUpdated(subscription);

        // Cache será invalidado automaticamente após 30 minutos
        logger.info('Subscription updated - cache will auto-invalidate');
        break;
      }
      case 'customer.subscription.deleted': {
        const subscription = event.data.object as Stripe.Subscription;
        // Processar cancelamento de assinatura
        await handleSubscriptionDeleted(subscription);

        // Cache será invalidado automaticamente após 30 minutos
        logger.info('Subscription deleted - cache will auto-invalidate');
        break;
      }
      default:
        logger.info(`Evento não processado: ${event.type}`);
    }

    return NextResponse.json({ received: true });
  } catch (error) {
    logger.error('[WEBHOOK_ERROR]', error);
    return NextResponse.json({ error: 'Erro no webhook' }, { status: 500 });
  }
}

// Handler para checkout concluído
async function handleCheckoutCompleted(session: Stripe.Checkout.Session) {
  const userId = session.metadata?.userId;
  const plan = session.metadata?.plan;

  if (!userId || !plan) {
    logger.error('Checkout missing userId or plan:', session.id);
    return;
  }

  const subscriptionId = session.subscription as string;
  if (!subscriptionId) {
    logger.error('Checkout missing subscription ID:', session.id);
    return;
  }

  try {
    // Verificar se o Stripe está configurado
    if (!stripe) {
      logger.error('Stripe não está configurado para processar checkout');
      return;
    }

    // Obter detalhes da assinatura
    const stripeResponse = await stripe.subscriptions.retrieve(subscriptionId);

    // Cast para tipo completo com as propriedades esperadas
    // As propriedades current_period_start e current_period_end estão disponíveis na resposta
    // mas não estão adequadamente tipadas na definição do tipo Stripe.Subscription
    type SubscriptionWithTimestamps = Stripe.Subscription & {
      current_period_start: number;
      current_period_end: number;
    };

    // Primeiro convertemos para unknown para evitar erros de tipagem
    const subscription = stripeResponse as unknown as SubscriptionWithTimestamps;
    const normalizedStatus = normalizeSubscriptionStatus(subscription.status || 'canceled');

    // Criar ou atualizar assinatura no banco de dados
    await prisma.subscription.upsert({
      where: {
        stripeSubscriptionId: subscriptionId,
      },
      create: {
        userId,
        plan,
        status: normalizedStatus,
        stripeCustomerId: session.customer as string,
        stripeSubscriptionId: subscriptionId,
        stripePriceId: subscription.items.data[0]?.price?.id || '',
        // Se o priceId for undefined, usamos string vazia para evitar erro de tipagem
        currentPeriodStart: new Date(
          subscription.current_period_start ? subscription.current_period_start * 1000 : Date.now()
        ),
        currentPeriodEnd: new Date(
          subscription.current_period_end ? subscription.current_period_end * 1000 : Date.now()
        ),
        // Garantir que apiCallsLimit sempre seja um número
        apiCallsLimit:
          API_CALL_LIMITS[plan as keyof typeof API_CALL_LIMITS] ||
          API_CALL_LIMITS[PLANS.FREE] ||
          50,
        apiCallsUsed: 0,
      },
      update: {
        status: normalizedStatus,
        plan,
        currentPeriodStart: new Date(
          subscription.current_period_start ? subscription.current_period_start * 1000 : Date.now()
        ),
        currentPeriodEnd: new Date(
          subscription.current_period_end ? subscription.current_period_end * 1000 : Date.now()
        ),
        // Garantir que apiCallsLimit sempre seja um número
        apiCallsLimit:
          API_CALL_LIMITS[plan as keyof typeof API_CALL_LIMITS] ||
          API_CALL_LIMITS[PLANS.FREE] ||
          50,
      },
    });

    logger.info(`Assinatura criada/atualizada: ${subscriptionId} para usuário ${userId}`);
  } catch (error) {
    logger.error('Erro ao processar checkout:', error);
  }
}

// Interface para estender o tipo Invoice do Stripe com campos extras
interface StripeInvoiceExtended extends Stripe.Invoice {
  subscription?: string;
  payment_intent?: string;
}

// Handler para fatura paga
async function handleInvoicePaid(invoice: Stripe.Invoice) {
  const extendedInvoice = invoice as StripeInvoiceExtended;
  const subscriptionId = extendedInvoice.subscription;

  if (!subscriptionId) {
    logger.error('Fatura sem ID de assinatura:', invoice.id);
    return;
  }

  try {
    // Buscar a assinatura pelo ID do Stripe
    const subscription = await prisma.subscription.findUnique({
      where: { stripeSubscriptionId: subscriptionId },
    });

    if (!subscription) {
      logger.error(`Assinatura não encontrada: ${subscriptionId}`);
      return;
    }

    // Registrar pagamento
    await prisma.payment.create({
      data: {
        amount: invoice.amount_paid,
        currency: invoice.currency,
        stripePaymentId: extendedInvoice.payment_intent || null,
        stripeInvoiceId: invoice.id || null,
        status: 'succeeded',
        subscriptionId: subscription.id,
        metadata: JSON.stringify({
          billingReason: invoice.billing_reason,
          invoiceNumber: invoice.number,
        }),
      },
    });

    logger.info(`Pagamento registrado para assinatura ${subscriptionId}`);
  } catch (error) {
    logger.error('Erro ao processar pagamento de fatura:', error);
  }
}

// Interface para estender o tipo Subscription do Stripe com campos extras
interface StripeSubscriptionExtended extends Stripe.Subscription {
  current_period_start?: number;
  current_period_end?: number;
}

// Handler para atualização de assinatura
async function handleSubscriptionUpdated(subscription: Stripe.Subscription) {
  const extendedSubscription = subscription as StripeSubscriptionExtended;
  const subscriptionId = subscription.id;
  const normalizedStatus = normalizeSubscriptionStatus(subscription.status);

  try {
    // Atualizar status da assinatura
    const dbSubscription = await prisma.subscription.findUnique({
      where: { stripeSubscriptionId: subscriptionId },
    });

    if (!dbSubscription) {
      logger.error(`Assinatura não encontrada no banco: ${subscriptionId}`);
      return;
    }

    // Garantir que items.data[0] existe e tem uma propriedade price
    const priceId = subscription.items.data[0]?.price?.id;
    if (!priceId) {
      logger.error(`Assinatura sem price ID: ${subscriptionId}`);
      return;
    }

    await prisma.subscription.update({
      where: { id: dbSubscription.id },
      data: {
        status: normalizedStatus,
        stripePriceId: priceId,
        currentPeriodStart: new Date(
          extendedSubscription.current_period_start
            ? extendedSubscription.current_period_start * 1000
            : Date.now()
        ),
        currentPeriodEnd: new Date(
          extendedSubscription.current_period_end
            ? extendedSubscription.current_period_end * 1000
            : Date.now()
        ),
        cancelAtPeriodEnd: subscription.cancel_at_period_end,
      },
    });

    logger.info(`Assinatura atualizada: ${subscriptionId}`);
  } catch (error) {
    logger.error('Erro ao atualizar assinatura:', error);
  }
}

// Handler para cancelamento de assinatura
async function handleSubscriptionDeleted(subscription: Stripe.Subscription) {
  const subscriptionId = subscription.id;

  try {
    // Atualizar status da assinatura para cancelada
    const dbSubscription = await prisma.subscription.findUnique({
      where: { stripeSubscriptionId: subscriptionId },
    });

    if (!dbSubscription) {
      logger.error(`Assinatura não encontrada no banco: ${subscriptionId}`);
      return;
    }

    await prisma.subscription.update({
      where: { id: dbSubscription.id },
      data: {
        status: 'canceled',
        cancelAtPeriodEnd: false,
      },
    });

    logger.info(`Assinatura cancelada: ${subscriptionId}`);
  } catch (error) {
    logger.error('Erro ao cancelar assinatura:', error);
  }
}
