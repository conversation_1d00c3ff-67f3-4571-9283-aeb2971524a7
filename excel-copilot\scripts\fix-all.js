#!/usr/bin/env node

/**
 * Script principal para executar todas as correções
 * Este script coordena a execução de todos os scripts de correção individuais
 */

const { spawn } = require('child_process');
const path = require('path');
const readline = require('readline');

// Cores para mensagens de terminal
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
};

// Lista de scripts a serem executados
const scripts = [
  {
    name: 'Corrigir URL do banco de dados',
    script: 'fix-database-url.js',
    description: 'Corrige o formato da URL do banco de dados nos arquivos de ambiente',
  },
  {
    name: 'Atualizar configuração do Next.js',
    script: 'fix-nextjs-config.js',
    description: 'Atualiza a configuração do Next.js para resolver problemas de timeout',
  },
  {
    name: 'Corrigir handlers onClick',
    script: 'fix-onclick-handlers.js',
    description: 'Corrige automaticamente problemas de manipuladores onClick em componentes',
  },
  {
    name: 'Corrigir metadata em layouts',
    script: 'fix-layout-metadata.js',
    description: 'Corrige problemas de exportação de metadata em arquivos de layout',
  },
  {
    name: 'Verificar componentes client-side',
    script: 'fix-client-components.js',
    description: 'Verifica problemas comuns em componentes do lado do cliente',
  },
];

// Função para executar um script
function runScript(script) {
  return new Promise((resolve, reject) => {
    console.log(`\n${colors.blue}Executando: ${script.name}${colors.reset}`);
    console.log(`${colors.yellow}${script.description}${colors.reset}\n`);

    const scriptPath = path.join(__dirname, script.script);
    const child = spawn('node', [scriptPath], { stdio: 'inherit' });

    child.on('close', code => {
      if (code === 0) {
        console.log(`\n${colors.green}Script ${script.name} executado com sucesso.${colors.reset}`);
        resolve();
      } else {
        console.log(
          `\n${colors.red}Script ${script.name} falhou com código de saída ${code}.${colors.reset}`
        );
        reject(new Error(`Script ${script.name} falhou com código de saída ${code}`));
      }
    });

    child.on('error', error => {
      console.error(`${colors.red}Erro ao executar o script ${script.name}:${colors.reset}`, error);
      reject(error);
    });
  });
}

// Função principal
async function main() {
  console.log(`${colors.cyan}Excel Copilot - Correção Automática de Problemas${colors.reset}`);
  console.log(
    `${colors.yellow}Este script irá executar todas as correções disponíveis para resolver problemas conhecidos${colors.reset}\n`
  );

  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout,
  });

  // Lista os scripts disponíveis
  console.log(`${colors.cyan}Scripts disponíveis:${colors.reset}`);
  scripts.forEach((script, index) => {
    console.log(
      `  ${index + 1}. ${colors.green}${script.name}${colors.reset} - ${script.description}`
    );
  });

  // Pergunta quais scripts executar
  const answer = await new Promise(resolve => {
    rl.question(
      `\n${colors.yellow}Deseja executar todos os scripts? [S/n] ${colors.reset}`,
      answer => {
        resolve(answer.toLowerCase());
      }
    );
  });

  rl.close();

  if (answer === 'n') {
    // Implementação para executar scripts específicos poderia ser adicionada aqui
    console.log(`${colors.yellow}Execução cancelada.${colors.reset}`);
    process.exit(0);
  }

  // Executa todos os scripts em sequência
  console.log(`\n${colors.green}Iniciando execução de todos os scripts...${colors.reset}`);

  let successCount = 0;
  let failureCount = 0;

  for (const script of scripts) {
    try {
      await runScript(script);
      successCount++;
    } catch (error) {
      console.error(`${colors.red}Erro ao executar ${script.name}:${colors.reset}`, error.message);
      failureCount++;
    }
  }

  console.log(`\n${colors.cyan}Execução concluída.${colors.reset}`);
  console.log(`${colors.green}Scripts executados com sucesso: ${successCount}${colors.reset}`);

  if (failureCount > 0) {
    console.log(`${colors.red}Scripts com falha: ${failureCount}${colors.reset}`);
    console.log(
      `\n${colors.yellow}Recomendação: Execute manualmente os scripts que falharam para ver os erros detalhados.${colors.reset}`
    );
  } else {
    console.log(`\n${colors.green}Todos os scripts foram executados com sucesso!${colors.reset}`);
    console.log(`${colors.cyan}Próximos passos:${colors.reset}`);
    console.log(`  1. Verifique o documento CORRECOES_APLICADAS.md para mais detalhes`);
    console.log(
      `  2. Execute o comando 'npm run build' para verificar se os problemas foram resolvidos`
    );
    console.log(`  3. Se necessário, aplique as alterações no ambiente de produção da Vercel`);
  }
}

// Executa a função principal
main().catch(error => {
  console.error(`${colors.red}Erro ao executar o script:${colors.reset}`, error);
  process.exit(1);
});
