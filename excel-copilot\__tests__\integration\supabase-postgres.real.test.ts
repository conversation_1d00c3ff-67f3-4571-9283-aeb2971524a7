/**
 * @jest-environment node
 */

import { PrismaClient } from '@prisma/client';
import { createClient } from '@supabase/supabase-js';
import * as dotenv from 'dotenv';
import * as path from 'path';

// Carregar variáveis de ambiente do arquivo .env.local
dotenv.config({ path: path.resolve(process.cwd(), '.env.local') });

// Este teste usa as credenciais reais, então só será executado se as variáveis estiverem definidas
const hasConfig =
  typeof process.env.DATABASE_URL === 'string' &&
  typeof process.env.SUPABASE_URL === 'string' &&
  typeof process.env.SUPABASE_ANON_KEY === 'string';

// Executar testes apenas se a configuração existir
const testOrSkip = hasConfig ? describe : describe.skip;

testOrSkip('Supabase & PostgreSQL Real Integration Tests', () => {
  let prisma: PrismaClient;
  let supabase: ReturnType<typeof createClient>;

  beforeAll(() => {
    // Criar cliente Prisma real com a conexão do banco de dados
    prisma = new PrismaClient({
      datasources: {
        db: {
          url: process.env.DATABASE_URL || 'placeholder-url',
        },
      },
    });

    // Criar cliente Supabase real
    supabase = createClient(
      process.env.SUPABASE_URL as string,
      process.env.SUPABASE_ANON_KEY as string
    );
  });

  afterAll(async () => {
    // Desconectar cliente Prisma
    await prisma.$disconnect();
  });

  describe('Verificação de Conexão com PostgreSQL', () => {
    test('Prisma pode conectar ao banco de dados', async () => {
      // Testar a conexão do Prisma com o banco de dados real
      await expect(prisma.$connect()).resolves.not.toThrow();
    });
  });

  describe('Conexão com Supabase', () => {
    test('Supabase Auth está disponível', async () => {
      // Verificar se podemos acessar o serviço de autenticação
      const { data, error } = await supabase.auth.getSession();

      // Podemos não ter uma sessão, mas o serviço deve estar acessível
      expect(error).toBeNull();
      expect(data).toBeDefined();
    });
  });

  describe('Operações de Banco de Dados', () => {
    // Este teste assume que existem workbooks no banco, mas só verifica a estrutura
    test('Pode consultar estrutura da tabela workbooks', async () => {
      try {
        // Consultar metadados da tabela (sem modificar dados)
        const result = await prisma.$queryRaw`
          SELECT column_name, data_type 
          FROM information_schema.columns 
          WHERE table_name = 'Workbook'
        `;

        // Verificar se os resultados têm uma estrutura válida
        expect(Array.isArray(result)).toBeTruthy();

        // Verificar se temos informações de colunas
        if (Array.isArray(result) && result.length > 0) {
          expect(result[0] as any).toHaveProperty('column_name');
          expect(result[0] as any).toHaveProperty('data_type');
        }
      } catch (error) {
        // Caso haja erro na consulta, vamos reportar para diagnosticar
        console.error('Erro ao consultar estrutura da tabela Workbook:', error);
        throw error;
      }
    });

    // Teste seguro que só verifica as tabelas existentes
    test('Pode listar tabelas no banco de dados', async () => {
      try {
        const result = await prisma.$queryRaw`
          SELECT table_name 
          FROM information_schema.tables 
          WHERE table_schema = 'public'
        `;

        expect(Array.isArray(result)).toBeTruthy();

        // Logar as tabelas para diagnostico (sem expor dados)
        const tableNames = Array.isArray(result)
          ? result.map((r: any) => r.table_name).join(', ')
          : '';
        console.log(`Tabelas encontradas: ${tableNames}`);

        // Verificar se temos algumas tabelas esperadas
        const tables = Array.isArray(result)
          ? result.map((r: any) => r.table_name.toLowerCase())
          : [];

        // Verificar tabelas comuns em aplicações NextAuth + Prisma
        expect(tables).toEqual(
          expect.arrayContaining([
            expect.stringMatching(/user/i),
            expect.stringMatching(/session/i),
          ])
        );
      } catch (error) {
        console.error('Erro ao listar tabelas:', error);
        throw error;
      }
    });
  });

  describe('Funcionalidades do Supabase', () => {
    test('Pode acessar o bucket de storage (sem modificar dados)', async () => {
      // Este teste apenas verifica se podemos listar buckets, sem criar/modificar
      const { data, error } = await supabase.storage.listBuckets();

      expect(error).toBeNull();
      expect(data).toBeDefined();
      expect(Array.isArray(data)).toBeTruthy();

      // Logar os buckets encontrados para diagnóstico
      if (data && data.length > 0) {
        const bucketNames = data.map(b => b.name).join(', ');
        console.log(`Buckets encontrados: ${bucketNames}`);
      }
    });

    test('Pode executar consulta RPC (sem modificar dados)', async () => {
      // Este teste tenta executar uma função RPC no Supabase
      // Usamos uma consulta simples que não altera dados
      try {
        const { data, error } = await supabase.rpc('version');

        if (error) {
          // Se a função específica não existir, vamos só verificar que o serviço responde
          console.log('Função RPC "version" não encontrada. Testando outra abordagem...');

          // Usar query básica para verificar acesso
          const { data: queryData, error: queryError } = await supabase
            .from('_prisma_migrations')
            .select('id')
            .limit(1);

          expect(queryError).toBeNull();
          expect(queryData).toBeDefined();
        } else {
          expect(data).toBeDefined();
        }
      } catch (error) {
        // Se der erro, pode ser que não tenhamos acesso a funções RPC específicas
        console.log('Erro ao chamar função RPC:', error);
        // Não falhar o teste por isso, apenas logar
      }
    });
  });
});
