import { NextRequest, NextResponse } from 'next/server';

import { logger } from '@/lib/logger';

// Contador global para métricas de requisições
const metrics = {
  totalRequests: 0,
  successfulRequests: 0,
  failedRequests: 0,
  responseTimeTotal: 0,
  requestsByPath: new Map<string, number>(),
  requestsByMethod: new Map<string, number>(),
  requestsByStatusCode: new Map<string, number>(),
};

/**
 * Middleware para coletar métricas de requisições API
 */
export async function metricsMiddleware(
  req: NextRequest,
  res: NextResponse,
  context: Record<string, any>
) {
  const startTime = Date.now();
  const path = req.nextUrl.pathname;
  const method = req.method;

  // Incrementar contadores globais
  metrics.totalRequests += 1;

  // Incrementar contadores por path e método
  metrics.requestsByPath.set(path, (metrics.requestsByPath.get(path) || 0) + 1);
  metrics.requestsByMethod.set(method, (metrics.requestsByMethod.get(method) || 0) + 1);

  // Armazenar tempo de início no contexto
  context.requestStartTime = startTime;

  // Armazenar função para registrar métricas no contexto
  // para que os handlers possam chamar quando finalizar a requisição
  context.recordMetrics = (statusCode: number) => {
    const endTime = Date.now();
    const duration = endTime - startTime;

    // Atualizar métricas com base no status da resposta
    if (statusCode < 400) {
      metrics.successfulRequests += 1;
    } else {
      metrics.failedRequests += 1;
    }

    // Atualizar métricas por código de status
    const statusCodeKey = statusCode.toString();
    metrics.requestsByStatusCode.set(
      statusCodeKey,
      (metrics.requestsByStatusCode.get(statusCodeKey) || 0) + 1
    );

    // Acumular tempo de resposta para cálculo de média
    metrics.responseTimeTotal += duration;

    // Registrar log da requisição
    logger.debug(`${method} ${path} - ${statusCode} - ${duration}ms`, {
      path,
      method,
      statusCode,
      duration,
      userId: context.userId,
    });

    return {
      path,
      method,
      statusCode,
      duration,
      userId: context.userId,
    };
  };

  // Continuar para o próximo middleware ou handler
}

/**
 * Wrapper para registrar métricas ao final de uma resposta
 * Esta função deve ser chamada pelos handlers após obter resposta do serviço
 */
export function withMetrics(response: NextResponse, context: Record<string, any>): NextResponse {
  if (context.recordMetrics && typeof context.recordMetrics === 'function') {
    context.recordMetrics(response.status);
  }
  return response;
}

/**
 * Obter métricas coletadas pelo middleware
 */
export function getApiMetrics() {
  return {
    totalRequests: metrics.totalRequests,
    successfulRequests: metrics.successfulRequests,
    failedRequests: metrics.failedRequests,
    averageResponseTime:
      metrics.totalRequests > 0 ? metrics.responseTimeTotal / metrics.totalRequests : 0,
    requestsByPath: Object.fromEntries(metrics.requestsByPath),
    requestsByMethod: Object.fromEntries(metrics.requestsByMethod),
    requestsByStatusCode: Object.fromEntries(metrics.requestsByStatusCode),
    timestamp: new Date().toISOString(),
  };
}
