/**
 * 🔒 VALIDADOR DE SEGURANÇA - EXCEL COPILOT
 *
 * Sistema rigoroso de validação de ambiente para prevenir exposição de credenciais
 * e garantir configuração segura em todos os ambientes.
 *
 * @version 1.0.0
 * <AUTHOR> Copilot Security Team
 */

interface SecurityValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  criticalIssues: string[];
}

interface CredentialPattern {
  name: string;
  pattern: RegExp;
  severity: 'critical' | 'high' | 'medium';
  description: string;
}

/**
 * Padrões de credenciais conhecidas que NÃO devem estar expostas
 */
const DANGEROUS_CREDENTIAL_PATTERNS: CredentialPattern[] = [
  {
    name: 'Stripe Live Secret Key',
    pattern: /sk_live_[a-zA-Z0-9]{99}/,
    severity: 'critical',
    description: 'Chave secreta LIVE do Stripe detectada',
  },
  {
    name: 'Google OAuth Client Secret',
    pattern: /GOCSPX-[a-zA-Z0-9_-]{28}/,
    severity: 'critical',
    description: 'Client Secret do Google OAuth detectado',
  },
  {
    name: 'GitHub Personal Access Token',
    pattern: /ghp_[a-zA-Z0-9]{36}/,
    severity: 'critical',
    description: 'Token de acesso pessoal do GitHub detectado',
  },
  {
    name: 'Linear API Key',
    pattern: /lin_api_[a-zA-Z0-9]{32}/,
    severity: 'high',
    description: 'Chave de API do Linear detectada',
  },
  {
    name: 'Supabase Service Role Key',
    pattern: /eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9\.[a-zA-Z0-9_-]+\.[a-zA-Z0-9_-]+/,
    severity: 'critical',
    description: 'Chave de service role do Supabase detectada',
  },
  {
    name: 'Vercel API Token',
    pattern: /[a-zA-Z0-9]{24}/,
    severity: 'medium',
    description: 'Possível token da API do Vercel detectado',
  },
];

/**
 * Variáveis obrigatórias por ambiente
 */
const REQUIRED_VARIABLES = {
  production: ['AUTH_NEXTAUTH_SECRET', 'AUTH_NEXTAUTH_URL', 'DB_DATABASE_URL', 'NODE_ENV'],
  development: ['AUTH_NEXTAUTH_SECRET', 'AUTH_NEXTAUTH_URL', 'DB_DATABASE_URL'],
  test: ['DB_DATABASE_URL'],
};

/**
 * Valida se há credenciais expostas no ambiente
 */
export function validateCredentialSecurity(): SecurityValidationResult {
  const result: SecurityValidationResult = {
    isValid: true,
    errors: [],
    warnings: [],
    criticalIssues: [],
  };

  // Verificar todas as variáveis de ambiente
  for (const [key, value] of Object.entries(process.env)) {
    if (!value) continue;

    // Verificar padrões perigosos
    for (const pattern of DANGEROUS_CREDENTIAL_PATTERNS) {
      if (pattern.pattern.test(value)) {
        const message = `🚨 CRÍTICO: ${pattern.description} encontrada na variável ${key}`;

        if (pattern.severity === 'critical') {
          result.criticalIssues.push(message);
          result.isValid = false;
        } else if (pattern.severity === 'high') {
          result.errors.push(message);
          result.isValid = false;
        } else {
          result.warnings.push(message);
        }
      }
    }

    // Verificar se há placeholders não substituídos
    if (value.includes('[') && value.includes(']')) {
      result.warnings.push(`⚠️ Placeholder não substituído na variável ${key}: ${value}`);
    }

    // Verificar credenciais hardcoded suspeitas
    if (value.length > 50 && /^[a-zA-Z0-9+/=]+$/.test(value)) {
      result.warnings.push(
        `⚠️ Possível credencial hardcoded na variável ${key} (string longa base64)`
      );
    }
  }

  return result;
}

/**
 * Valida se todas as variáveis obrigatórias estão presentes
 */
export function validateRequiredVariables(): SecurityValidationResult {
  const result: SecurityValidationResult = {
    isValid: true,
    errors: [],
    warnings: [],
    criticalIssues: [],
  };

  const env = process.env.NODE_ENV || 'development';
  const required = REQUIRED_VARIABLES[env as keyof typeof REQUIRED_VARIABLES] || [];

  for (const variable of required) {
    if (!process.env[variable]) {
      result.errors.push(`❌ Variável obrigatória ausente: ${variable}`);
      result.isValid = false;
    }
  }

  return result;
}

/**
 * Valida a força da chave NEXTAUTH_SECRET
 */
export function validateNextAuthSecret(): SecurityValidationResult {
  const result: SecurityValidationResult = {
    isValid: true,
    errors: [],
    warnings: [],
    criticalIssues: [],
  };

  const secret = process.env.AUTH_NEXTAUTH_SECRET;
  if (!secret) {
    result.criticalIssues.push('🚨 CRÍTICO: NEXTAUTH_SECRET não configurado');
    result.isValid = false;
    return result;
  }

  // Verificar comprimento mínimo
  if (secret.length < 32) {
    result.errors.push('❌ NEXTAUTH_SECRET deve ter pelo menos 32 caracteres');
    result.isValid = false;
  }

  // Verificar se não é um valor padrão/exemplo
  const defaultValues = [
    'excel-copilot-secret-key-development',
    'your-secret-key-here',
    'change-me',
    'default-secret',
  ];

  if (defaultValues.includes(secret)) {
    result.criticalIssues.push('🚨 CRÍTICO: NEXTAUTH_SECRET usando valor padrão inseguro');
    result.isValid = false;
  }

  // Verificar complexidade
  const hasUppercase = /[A-Z]/.test(secret);
  const hasLowercase = /[a-z]/.test(secret);
  const hasNumbers = /[0-9]/.test(secret);
  const hasSpecialChars = /[^a-zA-Z0-9]/.test(secret);

  const complexityScore = [hasUppercase, hasLowercase, hasNumbers, hasSpecialChars].filter(
    Boolean
  ).length;

  if (complexityScore < 3) {
    result.warnings.push(
      '⚠️ NEXTAUTH_SECRET poderia ser mais complexo (use maiúsculas, minúsculas, números e símbolos)'
    );
  }

  return result;
}

/**
 * Executa validação completa de segurança
 */
export function runSecurityValidation(): SecurityValidationResult {
  const credentialValidation = validateCredentialSecurity();
  const requiredValidation = validateRequiredVariables();
  const secretValidation = validateNextAuthSecret();

  const combinedResult: SecurityValidationResult = {
    isValid: credentialValidation.isValid && requiredValidation.isValid && secretValidation.isValid,
    errors: [
      ...credentialValidation.errors,
      ...requiredValidation.errors,
      ...secretValidation.errors,
    ],
    warnings: [
      ...credentialValidation.warnings,
      ...requiredValidation.warnings,
      ...secretValidation.warnings,
    ],
    criticalIssues: [
      ...credentialValidation.criticalIssues,
      ...requiredValidation.criticalIssues,
      ...secretValidation.criticalIssues,
    ],
  };

  return combinedResult;
}

/**
 * Gera relatório de segurança formatado
 */
export function generateSecurityReport(): string {
  const validation = runSecurityValidation();
  let report = '\n🔒 RELATÓRIO DE SEGURANÇA - EXCEL COPILOT\n';
  report += '================================================\n\n';

  if (validation.isValid) {
    report += '✅ STATUS: SEGURO\n';
    report += 'Nenhum problema crítico de segurança detectado.\n\n';
  } else {
    report += '🚨 STATUS: INSEGURO\n';
    report += 'Problemas de segurança detectados que precisam ser corrigidos.\n\n';
  }

  if (validation.criticalIssues.length > 0) {
    report += '🚨 PROBLEMAS CRÍTICOS:\n';
    validation.criticalIssues.forEach(issue => {
      report += `   ${issue}\n`;
    });
    report += '\n';
  }

  if (validation.errors.length > 0) {
    report += '❌ ERROS:\n';
    validation.errors.forEach(error => {
      report += `   ${error}\n`;
    });
    report += '\n';
  }

  if (validation.warnings.length > 0) {
    report += '⚠️ AVISOS:\n';
    validation.warnings.forEach(warning => {
      report += `   ${warning}\n`;
    });
    report += '\n';
  }

  report += '================================================\n';
  return report;
}

/**
 * Middleware para validação automática no startup
 */
export function initializeSecurityValidation(): void {
  // Executar apenas em desenvolvimento e produção
  if (process.env.NODE_ENV === 'test') return;

  const validation = runSecurityValidation();
  if (!validation.isValid) {
    console.error(generateSecurityReport());
    // Em produção, falhar completamente se houver problemas críticos
    if (process.env.NODE_ENV === 'production' && validation.criticalIssues.length > 0) {
      // eslint-disable-next-line no-console
      console.error(
        '🚨 FALHA CRÍTICA DE SEGURANÇA: Aplicação não pode iniciar com credenciais expostas'
      );
      process.exit(1);
    }
  } else {
    // eslint-disable-next-line no-console
    console.log('✅ Validação de segurança passou - ambiente configurado corretamente');
  }
}
