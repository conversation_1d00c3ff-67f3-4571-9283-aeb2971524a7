# 🔐 **SISTEMA DE MONITORAMENTO OAUTH - EXCEL COPILOT**

## 📋 **VISÃO GERAL**

O Excel Copilot possui um sistema completo de monitoramento e segurança para autenticação OAuth, implementando:

- ✅ **Rate Limiting** específico para endpoints OAuth
- ✅ **Logging estruturado** de eventos de autenticação
- ✅ **Sistema de alertas** para atividades suspeitas
- ✅ **APIs de monitoramento** com métricas em tempo real
- ✅ **Health checks** para providers OAuth
- ✅ **Dashboard de segurança** integrado

---

## 🏗️ **ARQUITETURA DO SISTEMA**

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Middleware    │───▶│  Rate Limiting   │───▶│   Auth Logger   │
│   (OAuth)       │    │   (IP-based)     │    │  (Structured)   │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│ Event Processor │───▶│  Alert System   │───▶│  Notifications  │
│   (Real-time)   │    │   (Rules-based)  │    │   (Webhook)     │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│ Monitoring API  │───▶│   Health Check   │───▶│   Dashboard     │
│  (Metrics)      │    │   (Providers)    │    │   (Admin UI)    │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

---

## 🔧 **COMPONENTES PRINCIPAIS**

### **1. Rate Limiting OAuth (`oauth-rate-limiter.ts`)**

**Funcionalidades:**

- Rate limiting específico por tipo de endpoint OAuth
- Bloqueio automático de IPs suspeitos
- Configurações diferenciadas por severidade

**Configurações:**

```typescript
oauth_signin: {
  windowMs: 15 * 60 * 1000,    // 15 minutos
  maxAttempts: 5,              // 5 tentativas
  blockDurationMs: 30 * 60 * 1000  // 30 min bloqueio
}

oauth_callback: {
  windowMs: 5 * 60 * 1000,     // 5 minutos
  maxAttempts: 10,             // 10 tentativas
  blockDurationMs: 15 * 60 * 1000  // 15 min bloqueio
}

oauth_error: {
  windowMs: 10 * 60 * 1000,    // 10 minutos
  maxAttempts: 3,              // 3 tentativas
  blockDurationMs: 60 * 60 * 1000  // 1 hora bloqueio
}
```

### **2. Auth Logger (`auth-logger.ts`)**

**Eventos Monitorados:**

- `LOGIN_ATTEMPT` - Tentativas de login
- `LOGIN_SUCCESS` - Logins bem-sucedidos
- `LOGIN_FAILURE` - Falhas de login
- `OAUTH_REDIRECT` - Redirecionamentos OAuth
- `OAUTH_CALLBACK` - Callbacks OAuth
- `OAUTH_SUCCESS` - OAuth bem-sucedido
- `OAUTH_ERROR` - Erros OAuth
- `SESSION_CREATED` - Criação de sessão
- `LOGOUT_SUCCESS` - Logout bem-sucedido
- `SUSPICIOUS_ACTIVITY` - Atividade suspeita
- `RATE_LIMIT_EXCEEDED` - Rate limit excedido

### **3. Sistema de Alertas (`auth-alerts.ts`)**

**Regras de Alerta:**

1. **Múltiplas tentativas falhadas** (HIGH)

   - 5+ falhas do mesmo IP em 10 minutos
   - Cooldown: 30 minutos

2. **Erros frequentes do provider** (MEDIUM)

   - 3+ erros do mesmo provider em 15 minutos
   - Cooldown: 20 minutos

3. **Padrões suspeitos de login** (HIGH)

   - Logins de múltiplos IPs para o mesmo usuário
   - Cooldown: 1 hora

4. **Abuso de rate limiting** (CRITICAL)

   - 3+ eventos de rate limit em 5 minutos
   - Cooldown: 15 minutos

5. **Erros de configuração** (CRITICAL)
   - Qualquer erro de configuração OAuth
   - Cooldown: 1 hora

---

## 🚀 **APIS DE MONITORAMENTO**

### **1. API Principal (`/api/monitoring/auth`)**

**Endpoints:**

```bash
# Visão geral do sistema
GET /api/monitoring/auth

# Estatísticas detalhadas
GET /api/monitoring/auth?action=stats

# Alertas ativos
GET /api/monitoring/auth?action=alerts

# Rate limiting
GET /api/monitoring/auth?action=rate-limits

# Reconhecer alerta
POST /api/monitoring/auth
{
  "action": "acknowledge_alert",
  "alertId": "alert_123"
}
```

### **2. Health Check (`/api/monitoring/health/oauth`)**

**Endpoints:**

```bash
# Health check geral
GET /api/monitoring/health/oauth

# Provider específico
GET /api/monitoring/health/oauth?provider=google

# Detalhado
GET /api/monitoring/health/oauth?detailed=true
```

**Verificações:**

- ✅ Configuração de credenciais
- ✅ Conectividade com providers
- ✅ Tempo de resposta
- ✅ Validação de URLs de callback

---

## 📊 **MÉTRICAS E DASHBOARD**

### **Métricas Principais:**

- **Taxa de sucesso OAuth** por provider
- **Tempo de resposta** dos providers
- **Número de IPs bloqueados**
- **Alertas ativos** por severidade
- **Eventos por minuto/hora**

### **Status de Saúde:**

- 🟢 **Healthy** - Tudo funcionando
- 🟡 **Degraded** - Problemas menores
- 🔴 **Unhealthy** - Problemas críticos

---

## 🔔 **SISTEMA DE NOTIFICAÇÕES**

### **Canais Suportados:**

- 📧 **Email** (configurável)
- 💬 **Slack/Discord** (webhook)
- 📱 **SMS** (Twilio)
- 📊 **Sistemas de monitoramento** (DataDog, New Relic)

### **Configuração de Webhook:**

```bash
# Variável de ambiente
ALERT_WEBHOOK_URL="https://hooks.slack.com/services/..."

# Payload enviado
{
  "text": "🚨 AUTH ALERT: Múltiplas tentativas falhadas",
  "severity": "high",
  "details": {
    "ruleName": "multiple_failed_logins",
    "timestamp": "2024-01-15T10:30:00Z"
  }
}
```

---

## ⚙️ **CONFIGURAÇÃO E DEPLOYMENT**

### **Variáveis de Ambiente:**

```bash
# Alertas
ALERT_WEBHOOK_URL="https://hooks.slack.com/..."
ALERT_EMAIL_ENABLED="true"
ALERT_EMAIL_TO="<EMAIL>"

# Rate Limiting
OAUTH_RATE_LIMIT_ENABLED="true"
OAUTH_RATE_LIMIT_STRICT="false"

# Logging
AUTH_LOGGING_LEVEL="info"
AUTH_LOGGING_ENABLED="true"
```

### **Produção vs Desenvolvimento:**

**Desenvolvimento:**

- Rate limits mais permissivos
- Logs detalhados
- Alertas desabilitados

**Produção:**

- Rate limits rigorosos
- Logs estruturados
- Alertas ativos
- Notificações habilitadas

---

## 🛡️ **SEGURANÇA E PRIVACIDADE**

### **Proteção de Dados:**

- ❌ **Não logamos senhas** ou tokens completos
- ✅ **Hash de IPs** em produção (opcional)
- ✅ **Retenção limitada** de logs (24h padrão)
- ✅ **Sanitização** de dados sensíveis

### **Compliance:**

- ✅ **LGPD/GDPR** compliant
- ✅ **Logs auditáveis**
- ✅ **Retenção configurável**
- ✅ **Anonização automática**

---

## 🔧 **MANUTENÇÃO E TROUBLESHOOTING**

### **Comandos Úteis:**

```bash
# Verificar saúde do sistema
curl /api/monitoring/health/oauth

# Ver alertas ativos
curl /api/monitoring/auth?action=alerts

# Estatísticas de rate limiting
curl /api/monitoring/auth?action=rate-limits
```

### **Limpeza Automática:**

- **Eventos antigos** removidos após 24h
- **Alertas reconhecidos** removidos após 24h
- **Rate limit entries** expiram automaticamente

### **Troubleshooting Comum:**

1. **Rate limit muito restritivo:**

   - Ajustar `maxAttempts` nas configurações
   - Verificar se não há bot/crawler

2. **Alertas falsos positivos:**

   - Ajustar regras de alerta
   - Aumentar cooldown

3. **Provider não reachable:**
   - Verificar conectividade de rede
   - Validar configurações de firewall

---

## 📈 **ROADMAP E MELHORIAS**

### **Próximas Funcionalidades:**

- 🔄 **Machine Learning** para detecção de anomalias
- 📊 **Dashboard visual** com gráficos
- 🔗 **Integração com SIEM**
- 📱 **App mobile** para alertas
- 🤖 **Auto-remediation** de problemas

### **Otimizações:**

- 🚀 **Redis** para rate limiting distribuído
- 📊 **InfluxDB** para métricas de série temporal
- 🔍 **Elasticsearch** para logs avançados
- 📈 **Grafana** para visualização

---

## 🆘 **SUPORTE E CONTATO**

Para questões sobre o sistema de monitoramento:

- 📧 **Email:** <EMAIL>
- 💬 **Slack:** #security-alerts
- 📖 **Docs:** `/docs/monitoring`
- 🐛 **Issues:** GitHub Issues

**Status Page:** https://status.excel-copilot.com
