'use client';

import { ReactNode } from 'react';
import { toast as reactHotToast } from 'react-hot-toast';

import { ToastAction } from '@/components/ui/toast';
import { Toaster as RadixToaster } from '@/components/ui/toaster';
import { useToast as useRadixToast } from '@/components/ui/use-toast';

// Interface unificada para toasts
export interface ToastOptions {
  title?: string | null;
  description?: string | null;
  variant?: 'default' | 'destructive' | 'success' | 'warning';
  duration?: number;
  id?: string | number | null;
  action?: {
    label: string;
    onClick: () => void;
  } | null;
}

// Hook unificado para toast
export function useToast() {
  const radixToast = useRadixToast();

  // Função unificada para mostrar toasts
  const showToast = (options: ToastOptions) => {
    if (options.variant === 'destructive') {
      // Use react-hot-toast para toasts de erro
      return reactHotToast.error(options.title || '');
    } else if (options.variant === 'success') {
      // Use react-hot-toast para toasts de sucesso
      return reactHotToast.success(options.title || '');
    } else if (options.variant === 'warning') {
      // Use react-hot-toast para alertas - como não há warning específico, use toast normal
      return reactHotToast(options.title || '', { icon: '⚠️' });
    } else {
      // Use radix para toasts comuns para manter compatibilidade com componentes existentes
      return radixToast.toast({
        title: options.title || undefined,
        description: options.description || undefined,
        variant: options.variant || 'default',
        duration: options.duration || 3000,
        // Só adicionar action se existir
        ...(options.action && {
          action: (
            <ToastAction altText={options.action.label} onClick={options.action.onClick}>
              {options.action.label}
            </ToastAction>
          ),
        }),
      });
    }
  };

  return {
    toast: showToast,
    radixToast,
  };
}

// Componente que fornece ambos os toasters
export function ToastProvider({ children }: { children: ReactNode }) {
  return (
    <>
      {children}
      <RadixToaster />
    </>
  );
}

// Re-exportar toast para compatibilidade
export const toast = reactHotToast;
