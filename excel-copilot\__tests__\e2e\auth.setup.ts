import { test as setup, expect } from '@playwright/test';
import path from 'path';

const authFile = path.join(__dirname, '.auth/user.json');

/**
 * Setup de autenticação para testes E2E
 * Este arquivo executa antes de todos os testes para configurar a sessão do usuário
 */
setup('authenticate', async ({ page }) => {
  console.log('🔐 Iniciando setup de autenticação...');

  // Navegar para a página de login
  await page.goto('/auth/signin');
  
  // Aguardar a página carregar completamente
  await page.waitForLoadState('networkidle');
  
  // Verificar se estamos na página de login
  await expect(page).toHaveTitle(/Excel Copilot/);
  
  // Verificar se há um formulário de login ou botões de OAuth
  const hasEmailLogin = await page.locator('input[type="email"]').isVisible();
  const hasGoogleLogin = await page.locator('button:has-text("Google")').isVisible();
  const hasGitHubLogin = await page.locator('button:has-text("GitHub")').isVisible();
  
  console.log('📋 Métodos de login disponíveis:', {
    email: hasEmailLogin,
    google: hasGoogleLogin,
    github: hasGitHubLogin,
  });

  // Estratégia 1: Login com email/senha (se disponível)
  if (hasEmailLogin) {
    console.log('📧 Tentando login com email...');
    
    const testEmail = process.env.TEST_USER_EMAIL || '<EMAIL>';
    const testPassword = process.env.TEST_USER_PASSWORD || 'password123';
    
    await page.fill('input[type="email"]', testEmail);
    await page.fill('input[type="password"]', testPassword);
    
    // Clicar no botão de login
    await page.click('button[type="submit"]');
    
    // Aguardar redirecionamento
    await page.waitForURL('/dashboard', { timeout: 30000 });
    
  } else if (hasGoogleLogin && process.env.TEST_GOOGLE_EMAIL) {
    console.log('🔍 Tentando login com Google...');
    
    // Clicar no botão do Google
    await page.click('button:has-text("Google")');
    
    // Aguardar popup do Google
    const popup = await page.waitForEvent('popup');
    
    // Preencher credenciais do Google
    await popup.fill('input[type="email"]', process.env.TEST_GOOGLE_EMAIL);
    await popup.click('button:has-text("Next")');
    
    await popup.fill('input[type="password"]', process.env.TEST_GOOGLE_PASSWORD || '');
    await popup.click('button:has-text("Next")');
    
    // Aguardar fechamento do popup e redirecionamento
    await popup.waitForEvent('close');
    await page.waitForURL('/dashboard', { timeout: 30000 });
    
  } else {
    console.log('🤖 Criando sessão mock para testes...');
    
    // Estratégia fallback: criar sessão mock via API
    await page.goto('/api/auth/test-login', {
      waitUntil: 'networkidle',
    });
    
    // Verificar se a API de teste está disponível
    const response = await page.textContent('body');
    if (response?.includes('error')) {
      throw new Error('API de teste não disponível. Configure TEST_USER_EMAIL e TEST_USER_PASSWORD.');
    }
    
    // Navegar para o dashboard
    await page.goto('/dashboard');
  }

  // Verificar se o login foi bem-sucedido
  await expect(page).toHaveURL('/dashboard');
  
  // Aguardar elementos do dashboard carregarem
  await expect(page.locator('h1')).toContainText(/Bem-vindo|Dashboard/);
  
  // Verificar se há dados do usuário
  const userMenu = page.locator('[data-testid="user-menu"]');
  if (await userMenu.isVisible()) {
    await expect(userMenu).toBeVisible();
  }
  
  console.log('✅ Autenticação bem-sucedida!');
  
  // Salvar estado da sessão
  await page.context().storageState({ path: authFile });
  
  console.log('💾 Estado da sessão salvo em:', authFile);
});

/**
 * Setup adicional para limpar dados de teste
 */
setup('cleanup test data', async ({ page }) => {
  console.log('🧹 Limpando dados de teste...');
  
  // Navegar para o dashboard
  await page.goto('/dashboard');
  
  // Aguardar carregamento
  await page.waitForLoadState('networkidle');
  
  // Buscar por planilhas de teste e removê-las
  const testWorkbooks = page.locator('[data-testid="workbook-item"]').filter({
    hasText: /test|teste|e2e/i,
  });
  
  const count = await testWorkbooks.count();
  console.log(`🗑️ Encontradas ${count} planilhas de teste para limpar`);
  
  for (let i = 0; i < count; i++) {
    const workbook = testWorkbooks.nth(i);
    
    // Abrir menu de ações
    await workbook.locator('[data-testid="workbook-actions"]').click();
    
    // Clicar em excluir
    await page.locator('[data-testid="delete-workbook"]').click();
    
    // Confirmar exclusão
    await page.locator('[data-testid="confirm-delete"]').click();
    
    // Aguardar exclusão
    await page.waitForTimeout(1000);
  }
  
  console.log('✅ Limpeza concluída');
});

/**
 * Verificação de saúde do sistema antes dos testes
 */
setup('health check', async ({ page }) => {
  console.log('🏥 Verificando saúde do sistema...');
  
  // Verificar se a aplicação está respondendo
  const response = await page.goto('/api/health');
  expect(response?.status()).toBe(200);
  
  // Verificar se o banco de dados está acessível
  await page.goto('/api/health/db');
  const dbHealth = await page.textContent('body');
  expect(dbHealth).toContain('healthy');
  
  // Verificar se o Redis está funcionando (se configurado)
  try {
    await page.goto('/api/cache/stats');
    const cacheStats = await page.textContent('body');
    if (cacheStats?.includes('connected')) {
      console.log('✅ Cache Redis funcionando');
    }
  } catch (error) {
    console.log('⚠️ Cache Redis não configurado ou indisponível');
  }
  
  console.log('✅ Sistema saudável');
});
