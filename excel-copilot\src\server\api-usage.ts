import { logger } from '@/lib/logger';
import { prisma } from '@/server/db/client';
import { createApiUsageInput, createWhereInput, addDateCondition } from '@/server/db/utils';

/**
 * Interface para registro de uso da API
 */
interface ApiUsageInput {
  userId: string;
  endpoint: string;
  count?: number;
  workbookId?: string;
  billable?: boolean;
}

/**
 * Registra o uso da API por um usuário
 * Importante para monitorar limites de planos e cobrança
 */
export async function recordApiUsage(data: ApiUsageInput): Promise<void> {
  try {
    const apiUsageData = createApiUsageInput(
      data.userId,
      data.endpoint,
      data.count ?? 1,
      data.workbookId,
      data.billable ?? true
    );

    await prisma.apiUsage.create({
      data: apiUsageData,
    });
  } catch (error) {
    // Log error but don't throw - não deve interromper a execução
    logger.error('Erro ao registrar uso da API', {
      userId: data.userId,
      endpoint: data.endpoint,
      error,
    });
  }
}

/**
 * Obtém o uso total da API por um usuário em um período
 */
export async function getUserApiUsage(userId: string, startDate?: Date, endDate?: Date) {
  try {
    // Criar objeto where com tipagem segura
    const baseWhere = createWhereInput({
      userId,
      billable: true,
    });

    // Adicionar condições de data se necessário
    const where = addDateCondition(baseWhere, 'createdAt', startDate, endDate);

    // Agrupar por endpoint e contar
    const usage = await prisma.apiUsage.groupBy({
      by: ['endpoint'],
      where,
      _sum: {
        count: true,
      },
      orderBy: {
        _sum: {
          count: 'desc',
        },
      },
    });

    // Contagem total
    const totalCount = await prisma.apiUsage.aggregate({
      where,
      _sum: {
        count: true,
      },
    });

    return {
      total: totalCount._sum?.count || 0,
      byEndpoint: usage.map(item => ({
        endpoint: item.endpoint,
        count: item._sum?.count || 0,
      })),
    };
  } catch (error) {
    logger.error('Erro ao buscar uso da API', { userId, error });
    throw new Error('Falha ao buscar estatísticas de uso da API');
  }
}

/**
 * Verifica se o usuário atingiu o limite de uso da API
 * @returns true se o limite foi atingido, false caso contrário
 */
export async function hasReachedApiLimit(userId: string): Promise<boolean> {
  try {
    // Obter plano do usuário
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        subscriptions: true,
      },
    });

    if (!user) {
      return true; // Se usuário não foi encontrado, considerar como limite atingido
    }

    // Obter limite baseado no plano de assinatura
    let apiLimit = 50; // Limite padrão para usuários gratuitos

    // Verificar se há uma assinatura ativa
    const activeSubscription = user.subscriptions?.find(sub => sub.status === 'active');

    if (activeSubscription) {
      const planLimits: Record<string, number> = {
        free: 50,
        starter: 1000,
        pro: 5000,
        business: 20000,
        enterprise: Infinity,
      };

      apiLimit = planLimits[activeSubscription.plan] || apiLimit;
    }

    // Data de início do período atual (30 dias)
    const periodStart = new Date();
    periodStart.setDate(periodStart.getDate() - 30);

    // Contar uso no período
    const usage = await prisma.apiUsage.aggregate({
      where: {
        userId,
        billable: true,
        createdAt: {
          gte: periodStart,
        },
      },
      _sum: {
        count: true,
      },
    });

    const totalUsage = usage._sum.count || 0;

    return totalUsage >= apiLimit;
  } catch (error) {
    logger.error('Erro ao verificar limite de API', { userId, error });
    return true; // Em caso de erro, limitar o uso por segurança
  }
}
