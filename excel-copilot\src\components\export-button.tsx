/**
 * Componente de botão de exportação para dados Excel
 */
'use client';

import { Download } from 'lucide-react';

import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useExcelFile } from '@/hooks/useExcelFile';

interface ExportButtonProps {
  workbookId: string;
  workbookName: string;
  sheets: {
    name: string;
    data: any;
  }[];
  variant?: 'default' | 'outline' | 'ghost';
  size?: 'default' | 'sm' | 'lg' | 'icon';
}

/**
 * Botão para exportar planilha para arquivo Excel
 */
export function ExportButton({
  workbookId,
  workbookName,
  sheets,
  variant = 'outline',
  size = 'sm',
}: ExportButtonProps) {
  const { exportExcel, isLoading } = useExcelFile();

  const handleExportExcel = async () => {
    await exportExcel(sheets, workbookName, 'xlsx', {
      trackAnalytics: true,
      workbookId,
    });
  };

  const handleExportCSV = async () => {
    await exportExcel(sheets, workbookName, 'csv', {
      trackAnalytics: true,
      workbookId,
    });
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant={variant}
          size={size}
          disabled={!sheets || sheets.length === 0 || isLoading}
          className="bg-green-600 hover:bg-green-700 text-white flex items-center gap-2"
        >
          {isLoading ? (
            <>
              <div className="h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
              Exportando...
            </>
          ) : (
            <>
              <Download className="h-4 w-4" />
              Exportar
            </>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={() => handleExportExcel()}>
          <Download className="h-4 w-4 mr-2" />
          <span>Exportar como Excel (.xlsx)</span>
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => handleExportCSV()}>
          <Download className="h-4 w-4 mr-2" />
          <span>Exportar como CSV (.csv)</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
