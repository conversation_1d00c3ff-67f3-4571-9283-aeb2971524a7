'use client';

import { <PERSON>, UserPlus, MessageSquare, History, Eye, Share2 } from 'lucide-react';
import { useSession } from 'next-auth/react';
import { useState, useEffect } from 'react';
import { toast } from 'sonner';

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Switch } from '@/components/ui/switch';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { useSocket } from '@/hooks/useSocket';

import { Input } from '../ui/input';

// Interface para colaboradores ativos
interface Collaborator {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  color?: string;
  position?: { row: number; col: number };
  lastActive: Date;
  status: 'active' | 'idle' | 'offline';
}

// Interface para histórico de alterações
interface ChangeHistoryItem {
  id: string;
  userId: string;
  userName: string;
  timestamp: Date;
  action: string;
  details: string;
}

// Props para o componente
interface CollaborationPanelProps {
  workbookId: string;
  readOnly?: boolean;
  onShare?: (email: string, permission: string) => Promise<void>;
}

// Array de cores para diferenciar colaboradores
const COLLABORATOR_COLORS = [
  '#F87171',
  '#60A5FA',
  '#34D399',
  '#FBBF24',
  '#A78BFA',
  '#F472B6',
  '#4ADE80',
  '#FB923C',
  '#38BDF8',
  '#A3E635',
];

/**
 * Componente de Painel de Colaboração
 * Gerencia a visualização e interação com colaboradores em tempo real
 */
export function CollaborationPanel({
  workbookId,
  readOnly = false,
  onShare,
}: CollaborationPanelProps) {
  useSession();
  const [activeTab, setActiveTab] = useState('collaborators');
  const [collaborators, setCollaborators] = useState<Collaborator[]>([]);
  const [changeHistory, setChangeHistory] = useState<ChangeHistoryItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showOffline, setShowOffline] = useState(false);
  const [shareEmail, setShareEmail] = useState('');
  const [sharePermission, setSharePermission] = useState('view');
  const [isSharing, setIsSharing] = useState(false);
  const [isRealTimeSyncEnabled, setIsRealTimeSyncEnabled] = useState(true);

  // Hook de socket para comunicação em tempo real
  const { socket, isConnected, connect, disconnect } = useSocket(workbookId);

  // Inicializar socket quando componente for montado
  useEffect(() => {
    if (isRealTimeSyncEnabled) {
      connect();
    } else {
      disconnect();
    }

    return () => {
      disconnect();
    };
  }, [isRealTimeSyncEnabled, connect, disconnect]);

  // Carregar colaboradores e histórico ao montar componente
  useEffect(() => {
    const loadCollaborationData = async () => {
      setIsLoading(true);
      try {
        // Carregar colaboradores
        const collaboratorsResponse = await fetch(`/api/workbooks/${workbookId}/collaborators`);
        if (collaboratorsResponse.ok) {
          const data = await collaboratorsResponse.json();
          setCollaborators(
            data.collaborators.map((collab: Collaborator, index: number) => ({
              ...collab,
              color: COLLABORATOR_COLORS[index % COLLABORATOR_COLORS.length],
              lastActive: new Date(collab.lastActive),
            }))
          );
        }

        // Carregar histórico de alterações
        const historyResponse = await fetch(`/api/workbooks/${workbookId}/history`);
        if (historyResponse.ok) {
          const data = await historyResponse.json();
          setChangeHistory(
            data.history.map((item: any) => ({
              ...item,
              timestamp: new Date(item.timestamp),
            }))
          );
        }
      } catch (error) {
        console.error('Erro ao carregar dados de colaboração:', error);
        toast.error('Não foi possível carregar dados de colaboração');
      } finally {
        setIsLoading(false);
      }
    };

    loadCollaborationData();
  }, [workbookId]);

  // Escutar eventos de socket para atualizações em tempo real
  useEffect(() => {
    if (!socket) return;

    // Listener para colaborador entrando
    socket.on('collaborator_joined', (collaborator: any) => {
      if (!collaborator || typeof collaborator !== 'object') return;

      setCollaborators(prev => {
        if (!Array.isArray(prev)) return [collaborator];

        // Verificar se colaborador já existe
        const existingIndex = prev.findIndex(c => c && c.id === collaborator.id);
        if (existingIndex >= 0) {
          // Atualizar colaborador existente
          const updated = [...prev];
          updated[existingIndex] = {
            status: 'active',
            lastActive: new Date(),
            id: collaborator.id || `user-${Date.now()}`, // Garante que sempre temos um id
            name: collaborator.name || 'Usuário',
            email: collaborator.email || '',
            avatar: collaborator.avatar,
            color: collaborator.color,
            position: collaborator.position,
          };
          return updated;
        } else {
          // Adicionar novo colaborador
          return [
            ...prev,
            {
              ...collaborator,
              color: COLLABORATOR_COLORS[prev.length % COLLABORATOR_COLORS.length],
              lastActive: new Date(),
              status: 'active',
            },
          ];
        }
      });

      toast.info(`${collaborator.name || 'Usuário'} entrou na planilha`);
    });

    // Listener para colaborador saindo
    socket.on('collaborator_left', (collaboratorId: string) => {
      if (typeof collaboratorId !== 'string') return;

      setCollaborators(prev => {
        if (!Array.isArray(prev)) return prev;

        return prev.map(c => {
          if (c && c.id === collaboratorId) {
            return {
              ...c,
              status: 'offline',
              lastActive: new Date(),
            };
          }
          return c;
        });
      });
    });

    // Listener para atualização de posição de colaborador
    socket.on(
      'cursor_position',
      ({ userId, position }: { userId: string; position: { row: number; col: number } }) => {
        if (!userId || !position || typeof position !== 'object') return;

        setCollaborators(prev => {
          if (!Array.isArray(prev)) return prev;

          return prev.map(c => {
            if (c && c.id === userId) {
              return {
                ...c,
                position,
                lastActive: new Date(),
                status: 'active',
              };
            }
            return c;
          });
        });
      }
    );

    // Listener para alterações na planilha
    socket.on('cell_changed', (changeData: any) => {
      // Adicionar à lista de histórico
      const historyItem: ChangeHistoryItem = {
        id: Date.now().toString(),
        userId: changeData.userId,
        userName: changeData.userName || 'Usuário',
        timestamp: new Date(),
        action: 'Editou célula',
        details: `${changeData.cell}: "${changeData.value}"`,
      };

      setChangeHistory(prev => [historyItem, ...prev].slice(0, 100));
    });

    // Limpeza ao desmontar
    return () => {
      socket.off('collaborator_joined');
      socket.off('collaborator_left');
      socket.off('cursor_position');
      socket.off('cell_changed');
    };
  }, [socket]);

  // Filtrar colaboradores para exibição
  const displayCollaborators = showOffline
    ? collaborators
    : collaborators.filter(c => c.status !== 'offline');

  // Ordenar por status (ativos primeiro) e depois por nome
  const sortedCollaborators = [...displayCollaborators].sort((a, b) => {
    if (a.status === 'active' && b.status !== 'active') return -1;
    if (a.status !== 'active' && b.status === 'active') return 1;
    return a.name.localeCompare(b.name);
  });

  // Handler para compartilhar planilha
  const handleShare = async () => {
    if (!shareEmail || !sharePermission) {
      toast.error('E-mail e permissão são obrigatórios');
      return;
    }

    setIsSharing(true);
    try {
      if (onShare && typeof onShare === 'function') {
        await onShare(shareEmail, sharePermission);
        toast.success(`Planilha compartilhada com ${shareEmail}`);
        setShareEmail('');
      }
    } catch (error) {
      console.error('Erro ao compartilhar planilha:', error);
      toast.error('Não foi possível compartilhar a planilha');
    } finally {
      setIsSharing(false);
    }
  };

  // Handler para alternar sincronização em tempo real
  const handleToggleRealTimeSync = () => {
    const newState = !isRealTimeSyncEnabled;
    setIsRealTimeSyncEnabled(newState);

    if (newState) {
      toast.info('Sincronização em tempo real ativada');
      connect();
    } else {
      toast.info('Sincronização em tempo real desativada');
      disconnect();
    }
  };

  // Formatador para tempo relativo
  const formatRelativeTime = (date: Date) => {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMinutes = Math.floor(diffMs / 60000);

    if (diffMinutes < 1) return 'agora';
    if (diffMinutes < 60) return `${diffMinutes}m atrás`;

    const diffHours = Math.floor(diffMinutes / 60);
    if (diffHours < 24) return `${diffHours}h atrás`;

    const diffDays = Math.floor(diffHours / 24);
    return `${diffDays}d atrás`;
  };

  return (
    <Card className="w-full">
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg flex items-center">
            <Users className="h-5 w-5 mr-2" />
            Colaboração
          </CardTitle>
          <div className="flex items-center space-x-2">
            <div className="flex items-center space-x-1">
              <Switch
                id="sync-toggle"
                checked={isRealTimeSyncEnabled}
                onCheckedChange={handleToggleRealTimeSync}
              />
              <Label htmlFor="sync-toggle" className="text-xs">
                Sincronização
              </Label>
            </div>
            <Badge variant={isConnected ? 'success' : 'outline'} className="h-6">
              {isConnected ? 'Conectado' : 'Desconectado'}
            </Badge>
          </div>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="w-full">
            <TabsTrigger value="collaborators" className="flex-1">
              <Users className="h-4 w-4 mr-1" />
              <span className="hidden sm:inline">Colaboradores</span>
            </TabsTrigger>
            <TabsTrigger value="history" className="flex-1">
              <History className="h-4 w-4 mr-1" />
              <span className="hidden sm:inline">Histórico</span>
            </TabsTrigger>
            <TabsTrigger value="share" className="flex-1">
              <Share2 className="h-4 w-4 mr-1" />
              <span className="hidden sm:inline">Compartilhar</span>
            </TabsTrigger>
          </TabsList>

          {/* Aba de Colaboradores */}
          <TabsContent value="collaborators" className="mt-2">
            <div className="mb-2 flex justify-between items-center">
              <div className="text-sm text-muted-foreground">
                {displayCollaborators.length} colaboradores
              </div>
              <div className="flex items-center space-x-1">
                <Label htmlFor="show-offline" className="text-xs">
                  Mostrar offline
                </Label>
                <Switch id="show-offline" checked={showOffline} onCheckedChange={setShowOffline} />
              </div>
            </div>

            <ScrollArea className="h-[200px]">
              {isLoading ? (
                <div className="flex justify-center items-center h-full">
                  <span className="text-sm text-muted-foreground">Carregando...</span>
                </div>
              ) : sortedCollaborators.length > 0 ? (
                <ul className="space-y-2">
                  {sortedCollaborators.map(collaborator => (
                    <li
                      key={collaborator.id}
                      className="flex items-center justify-between p-2 rounded-md hover:bg-muted"
                    >
                      <div className="flex items-center">
                        <div className="relative" style={{ borderColor: collaborator.color }}>
                          <Avatar className="h-8 w-8">
                            <AvatarImage src={collaborator.avatar} alt={collaborator.name} />
                            <AvatarFallback style={{ backgroundColor: collaborator.color }}>
                              {collaborator.name.substring(0, 2).toUpperCase()}
                            </AvatarFallback>
                          </Avatar>
                          <div
                            className={`absolute bottom-0 right-0 h-2 w-2 rounded-full ${
                              collaborator.status === 'active'
                                ? 'bg-green-500'
                                : collaborator.status === 'idle'
                                  ? 'bg-amber-500'
                                  : 'bg-gray-400'
                            }`}
                          />
                        </div>
                        <div className="ml-2 flex flex-col">
                          <span className="text-sm font-medium">{collaborator.name}</span>
                          <span className="text-xs text-muted-foreground truncate max-w-[120px]">
                            {collaborator.email}
                          </span>
                        </div>
                      </div>
                      <div className="flex items-center">
                        {collaborator.position && (
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger>
                                <Badge variant="outline" className="text-xs">
                                  {`${String.fromCharCode(65 + collaborator.position.col)}${collaborator.position.row + 1}`}
                                </Badge>
                              </TooltipTrigger>
                              <TooltipContent>Posição atual do cursor</TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        )}
                        <span className="text-xs text-muted-foreground ml-2">
                          {formatRelativeTime(collaborator.lastActive)}
                        </span>
                      </div>
                    </li>
                  ))}
                </ul>
              ) : (
                <div className="flex justify-center items-center h-full">
                  <span className="text-sm text-muted-foreground">
                    Nenhum colaborador no momento
                  </span>
                </div>
              )}
            </ScrollArea>
          </TabsContent>

          {/* Aba de Histórico */}
          <TabsContent value="history" className="mt-2">
            <ScrollArea className="h-[200px]">
              {isLoading ? (
                <div className="flex justify-center items-center h-full">
                  <span className="text-sm text-muted-foreground">Carregando...</span>
                </div>
              ) : changeHistory.length > 0 ? (
                <ul className="space-y-2">
                  {changeHistory.map(item => (
                    <li key={item.id} className="flex items-center p-2 rounded-md hover:bg-muted">
                      <div className="flex-1">
                        <div className="flex items-center">
                          <span className="text-sm font-medium">{item.userName}</span>
                          <Badge variant="outline" className="ml-2 text-xs">
                            {item.action}
                          </Badge>
                        </div>
                        <div className="flex items-center mt-1">
                          <span className="text-xs text-muted-foreground">
                            {formatRelativeTime(item.timestamp)}
                          </span>
                          <span className="text-xs ml-2">{item.details}</span>
                        </div>
                      </div>
                    </li>
                  ))}
                </ul>
              ) : (
                <div className="flex justify-center items-center h-full">
                  <span className="text-sm text-muted-foreground">
                    Nenhuma alteração registrada
                  </span>
                </div>
              )}
            </ScrollArea>
          </TabsContent>

          {/* Aba de Compartilhamento */}
          <TabsContent value="share" className="mt-2">
            <div className="space-y-4">
              <div className="grid gap-2">
                <Label htmlFor="email">E-mail para compartilhar</Label>
                <Input
                  id="email"
                  placeholder="<EMAIL>"
                  type="email"
                  value={shareEmail}
                  onChange={e => setShareEmail(e.target.value)}
                  disabled={readOnly || isSharing}
                />
              </div>

              <div className="grid gap-2">
                <Label>Permissão</Label>
                <div className="flex space-x-2">
                  <Button
                    variant={sharePermission === 'view' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setSharePermission('view')}
                    disabled={readOnly || isSharing}
                    className="flex-1"
                  >
                    <Eye className="h-4 w-4 mr-1" />
                    Visualizar
                  </Button>
                  <Button
                    variant={sharePermission === 'edit' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setSharePermission('edit')}
                    disabled={readOnly || isSharing}
                    className="flex-1"
                  >
                    <MessageSquare className="h-4 w-4 mr-1" />
                    Editar
                  </Button>
                </div>
              </div>

              <Button
                onClick={() => handleShare()}
                disabled={readOnly || isSharing || !shareEmail}
                className="w-full"
              >
                {isSharing ? (
                  <>Compartilhando...</>
                ) : (
                  <>
                    <UserPlus className="h-4 w-4 mr-2" />
                    Compartilhar Planilha
                  </>
                )}
              </Button>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
