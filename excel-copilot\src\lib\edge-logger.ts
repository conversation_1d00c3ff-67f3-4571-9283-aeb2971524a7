/**
 * Logger simplificado para Edge Runtime
 * Compatível com Vercel Edge Functions
 */

interface LogLevel {
  ERROR: 'error';
  WARN: 'warn';
  INFO: 'info';
  DEBUG: 'debug';
}

const LOG_LEVELS: LogLevel = {
  ERROR: 'error',
  WARN: 'warn',
  INFO: 'info',
  DEBUG: 'debug',
};

class EdgeLogger {
  private isDevelopment = process.env.NODE_ENV === 'development';
  private isProduction = process.env.NODE_ENV === 'production';

  private formatMessage(level: string, message: string, ...args: unknown[]): string {
    const timestamp = new Date().toISOString();
    const prefix = `[${timestamp}] [${level.toUpperCase()}]`;

    if (args.length > 0) {
      return `${prefix} ${message} ${JSON.stringify(args)}`;
    }

    return `${prefix} ${message}`;
  }

  error(message: string, ...args: unknown[]): void {
    const formatted = this.formatMessage(LOG_LEVELS.ERROR, message, ...args);
    // eslint-disable-next-line no-console
    console.error(formatted);
  }

  warn(message: string, ...args: unknown[]): void {
    const formatted = this.formatMessage(LOG_LEVELS.WARN, message, ...args);
    console.warn(formatted);
  }

  info(message: string, ...args: unknown[]): void {
    if (this.isDevelopment || !this.isProduction) {
      const formatted = this.formatMessage(LOG_LEVELS.INFO, message, ...args);
      console.info(formatted);
    }
  }

  debug(message: string, ...args: unknown[]): void {
    if (this.isDevelopment) {
      const formatted = this.formatMessage(LOG_LEVELS.DEBUG, message, ...args);
      // eslint-disable-next-line no-console
      console.debug(formatted);
    }
  }

  // Métodos de compatibilidade com o logger original
  log(message: string, ...args: unknown[]): void {
    this.info(message, ...args);
  }

  // Método para logs estruturados
  logStructured(level: keyof LogLevel, message: string, data?: Record<string, unknown>): void {
    const logData = {
      timestamp: new Date().toISOString(),
      level: level.toLowerCase(),
      message,
      ...(data && { data }),
    };

    switch (level) {
      case 'ERROR':
        // eslint-disable-next-line no-console
        console.error(JSON.stringify(logData));
        break;
      case 'WARN':
        console.warn(JSON.stringify(logData));
        break;
      case 'INFO':
        if (this.isDevelopment || !this.isProduction) {
          console.info(JSON.stringify(logData));
        }
        break;
      case 'DEBUG':
        if (this.isDevelopment) {
          // eslint-disable-next-line no-console
          console.debug(JSON.stringify(logData));
        }
        break;
    }
  }
}

// Instância singleton
const edgeLogger = new EdgeLogger();

// Export para compatibilidade com o logger original
export const logger = edgeLogger;

// Export da classe para casos específicos
export { EdgeLogger };

// Export default
export default edgeLogger;
