# ======================================
# TESTE - Excel Copilot (TEMPLATE)
# ======================================
# Este é um template para configuração de ambiente de teste
# Copie este arquivo para .env.test e configure suas credenciais de teste
# ======================================

# ============================================================================
# CONFIGURAÇÕES BÁSICAS
# ============================================================================
NODE_ENV="test"
APP_NAME="Excel Copilot"
APP_VERSION="1.0.0"
APP_URL="http://localhost:3000"
NEXT_PUBLIC_APP_URL="http://localhost:3000"

# ============================================================================
# AUTENTICAÇÃO - NEXTAUTH (OBRIGATÓRIAS)
# ============================================================================
# Gerar com: openssl rand -base64 32
AUTH_NEXTAUTH_SECRET="[GERAR_CHAVE_SEGURA_32_CHARS_PARA_DEV]"
AUTH_NEXTAUTH_URL="http://localhost:3000"
AUTH_SKIP_PROVIDERS="false"

# OAuth Providers (OPCIONAIS para desenvolvimento)
# GOOGLE_CLIENT_ID="[SEU_GOOGLE_CLIENT_ID_DEV]"
# GOOGLE_CLIENT_SECRET="[SEU_GOOGLE_CLIENT_SECRET_DEV]"
# GITHUB_CLIENT_ID="[SEU_GITHUB_CLIENT_ID_DEV]"
# GITHUB_CLIENT_SECRET="[SEU_GITHUB_CLIENT_SECRET_DEV]"

# ============================================================================
# BANCO DE DADOS - DESENVOLVIMENTO
# ============================================================================
# Banco de dados para testes (isolado)
DB_DATABASE_URL="postgresql://postgres:password@localhost:5432/excel_copilot_test"
# DIRECT_URL="postgresql://postgres:password@localhost:5432/excel_copilot_test"

# Opção 2: Supabase (desenvolvimento)
# DATABASE_URL="[SUA_DATABASE_URL_SUPABASE_DEV]"
# DIRECT_URL="[SUA_DIRECT_URL_SUPABASE_DEV]"
# SUPABASE_URL="[SUA_SUPABASE_URL_DEV]"
# NEXT_PUBLIC_SUPABASE_URL="[SUA_SUPABASE_URL_DEV]"
# SUPABASE_ANON_KEY="[SUA_SUPABASE_ANON_KEY_DEV]"
# NEXT_PUBLIC_SUPABASE_ANON_KEY="[SUA_SUPABASE_ANON_KEY_DEV]"
# SUPABASE_SERVICE_ROLE_KEY="[SUA_SUPABASE_SERVICE_ROLE_KEY_DEV]"

# ============================================================================
# PAGAMENTOS - STRIPE (TESTE)
# ============================================================================
# Use sempre chaves de TESTE em desenvolvimento
# STRIPE_SECRET_KEY="sk_test_[SUA_STRIPE_SECRET_KEY_TEST]"
# NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY="pk_test_[SUA_STRIPE_PUBLISHABLE_KEY_TEST]"
# STRIPE_WEBHOOK_SECRET="whsec_[SEU_STRIPE_WEBHOOK_SECRET_TEST]"

# ============================================================================
# INTELIGÊNCIA ARTIFICIAL - DESENVOLVIMENTO
# ============================================================================
# Recomendado: usar mocks em desenvolvimento
AI_ENABLED="true"
AI_USE_MOCK="true"
# VERTEX_AI_PROJECT_ID="[SEU_VERTEX_AI_PROJECT_ID]"
# VERTEX_AI_LOCATION="us-central1"
# VERTEX_AI_MODEL_NAME="gemini-2.0-flash-001"

# ============================================================================
# CACHE E REDIS (OPCIONAL)
# ============================================================================
# Para desenvolvimento, cache pode ser desabilitado ou usar Redis local
# UPSTASH_REDIS_REST_URL="[SUA_UPSTASH_REDIS_URL_DEV]"
# UPSTASH_REDIS_REST_TOKEN="[SEU_UPSTASH_REDIS_TOKEN_DEV]"
AI_CACHE_SIZE="50"
AI_CACHE_TTL="3600"
EXCEL_CACHE_SIZE="25"
EXCEL_CACHE_TTL="900"
CACHE_DEFAULT_TTL="1800"

# ============================================================================
# INTEGRAÇÕES MCP (OPCIONAL) - NOMENCLATURA PADRONIZADA
# ============================================================================
# Para desenvolvimento, MCPs podem usar tokens de teste ou serem desabilitadas
# MCP_VERCEL_TOKEN="[SEU_VERCEL_API_TOKEN_DEV]"
# MCP_VERCEL_PROJECT_ID="[SEU_VERCEL_PROJECT_ID_DEV]"
# MCP_VERCEL_TEAM_ID="[SEU_VERCEL_TEAM_ID_DEV]"
# MCP_LINEAR_API_KEY="[SUA_LINEAR_API_KEY_DEV]"
# MCP_GITHUB_TOKEN="[SEU_GITHUB_TOKEN_DEV]"
# MCP_GITHUB_OWNER="[SEU_GITHUB_USERNAME_DEV]"
# MCP_GITHUB_REPO="[SEU_REPO_NAME_DEV]"

# ============================================================================
# DESENVOLVIMENTO E DEBUG
# ============================================================================
# Configurações específicas para desenvolvimento
DEV_LOG_LEVEL="debug"
DEV_DISABLE_VALIDATION="false"
DEV_FORCE_PRODUCTION="false"

# ============================================================================
# MONITORAMENTO (OPCIONAL)
# ============================================================================
# Para desenvolvimento, monitoramento pode ser desabilitado
# SENTRY_DSN="[SEU_SENTRY_DSN_DEV]"
# SENTRY_ORG="[SUA_SENTRY_ORG]"
# SENTRY_PROJECT="[SEU_SENTRY_PROJECT_DEV]"

# ============================================================================
# INSTRUÇÕES DE CONFIGURAÇÃO PARA TESTES
# ============================================================================
# 1. Copie este arquivo para .env.test
# 2. Substitua os placeholders [PLACEHOLDER] por valores de teste
# 3. Use sempre banco de dados isolado para testes
# 4. Use sempre credenciais de TESTE
# 5. Mantenha AI_USE_MOCK="true" para testes rápidos
# 6. Configure NODE_ENV="test" obrigatoriamente
# ============================================================================
