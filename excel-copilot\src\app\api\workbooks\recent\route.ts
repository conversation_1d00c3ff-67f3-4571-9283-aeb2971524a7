// Forçar o modo dinâmico para essa rota
export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

import { ENV } from '@/config/unified-environment';
import { getCurrentUserId } from '@/lib/auth';
import { logger } from '@/lib/logger';
import { prisma } from '@/server/db/client';

// Schema para validação dos parâmetros de paginação
const paginationSchema = z.object({
  limit: z.coerce.number().int().min(1).max(50).default(10),
  page: z.coerce.number().int().min(0).default(0),
});

// Tipos para resposta da API
interface _WorkbookResponse {
  id: string;
  name: string;
  createdAt: Date;
  updatedAt: Date;
  lastAccessedAt: Date;
  sheets: { id: string }[];
}

/**
 * GET /api/workbooks/recent
 * Listar as planilhas recentemente acessadas pelo usuário
 */
export async function GET(request: NextRequest) {
  try {
    // Extrair e validar parâmetros de paginação
    const searchParams = request.nextUrl.searchParams;
    const paginationResult = paginationSchema.safeParse({
      limit: searchParams.get('limit'),
      page: searchParams.get('page'),
    });

    // Valores padrão se a validação falhar
    const { limit, page } = paginationResult.success
      ? paginationResult.data
      : { limit: 10, page: 0 };

    // Verificar autenticação e obter ID do usuário
    const userId = await getCurrentUserId();

    if (!userId) {
      if (ENV.IS_DEVELOPMENT && ENV.FEATURES?.SKIP_AUTH_PROVIDERS) {
        logger.warn('Permitindo acesso sem autenticação em modo de desenvolvimento');

        // Retornar planilhas mock para desenvolvimento
        return NextResponse.json({
          workbooks: [
            {
              id: 'dev-1',
              name: 'Planilha de Demonstração Recente 1',
              createdAt: new Date(),
              updatedAt: new Date(),
              lastAccessedAt: new Date(),
              sheets: [{ id: 'sheet-1' }],
            },
            {
              id: 'dev-2',
              name: 'Planilha de Demonstração Recente 2',
              createdAt: new Date(),
              updatedAt: new Date(),
              lastAccessedAt: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 dia atrás
              sheets: [{ id: 'sheet-2' }],
            },
          ],
          pagination: {
            limit,
            page,
            totalItems: 2,
            totalPages: 1,
            hasMore: false,
          },
        });
      }

      return NextResponse.json(
        {
          error: 'Não autorizado',
          details: 'Usuário não autenticado ou sessão inválida',
        },
        { status: 401 }
      );
    }

    // Obter contagem total de workbooks para paginação
    const totalItems = await prisma.workbook.count({
      where: { userId: userId },
    });

    const totalPages = Math.ceil(totalItems / limit);
    const hasMore = page < totalPages - 1;

    // Como alternativa ao modelo WorkbookAccess que pode não existir ainda,
    // vamos buscar os workbooks mais recentemente atualizados do usuário
    const recentWorkbooks = await prisma.workbook.findMany({
      where: {
        userId: userId,
      },
      orderBy: {
        updatedAt: 'desc',
      },
      take: limit,
      skip: page * limit,
      include: {
        sheets: {
          select: {
            id: true,
          },
        },
      },
    });

    // Transformar os resultados para o formato esperado
    const formattedWorkbooks = recentWorkbooks.map(workbook => ({
      id: workbook.id,
      name: workbook.name,
      createdAt: workbook.createdAt,
      updatedAt: workbook.updatedAt,
      lastAccessedAt: workbook.updatedAt, // Usar updatedAt como proxy para lastAccessedAt
      sheets: workbook.sheets,
    }));

    // Incluir metadados de paginação na resposta
    return NextResponse.json({
      workbooks: formattedWorkbooks,
      pagination: {
        limit,
        page,
        totalItems,
        totalPages,
        hasMore,
      },
    });
  } catch (error) {
    logger.error('Erro ao listar workbooks recentes:', error);
    return NextResponse.json(
      {
        error: 'Erro ao buscar planilhas recentes',
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 }
    );
  }
}
