import { useState, useCallback } from 'react';
import { toast } from 'sonner';

interface StorageFile {
  path: string;
  size: number;
  name: string;
  type: string;
  uploadedAt: string;
}

interface UseSupabaseStorageReturn {
  files: StorageFile[];
  isLoading: boolean;
  isUploading: boolean;
  uploadFile: (file: File, workbookId: string) => Promise<StorageFile | null>;
  deleteFile: (filePath: string, workbookId: string) => Promise<boolean>;
  listFiles: (workbookId: string) => Promise<StorageFile[]>;
  downloadFile: (filePath: string, fileName: string) => Promise<void>;
  getStorageStats: (
    workbookId: string
  ) => Promise<{ totalFiles: number; totalSize: number } | null>;
}

/**
 * Hook para gerenciar arquivos no Supabase Storage via API routes
 */
export function useSupabaseStorage(): UseSupabaseStorageReturn {
  const [files, setFiles] = useState<StorageFile[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isUploading, setIsUploading] = useState(false);

  /**
   * Upload de arquivo para o Supabase Storage
   */
  const uploadFile = useCallback(
    async (file: File, workbookId: string): Promise<StorageFile | null> => {
      if (!file || !workbookId) {
        toast.error('Arquivo ou ID do workbook não fornecido');
        return null;
      }

      setIsUploading(true);

      try {
        const formData = new FormData();
        formData.append('file', file);

        const response = await fetch(`/api/workbooks/${workbookId}/storage`, {
          method: 'POST',
          body: formData,
        });

        const result = await response.json();

        if (!response.ok) {
          throw new Error(result.error || 'Erro no upload');
        }

        if (result.success) {
          toast.success('Arquivo enviado com sucesso!', {
            description: `${file.name} (${Math.round(file.size / 1024)}KB)`,
          });

          // Atualizar lista de arquivos
          await listFiles(workbookId);

          return result.file;
        } else {
          throw new Error(result.error || 'Falha no upload');
        }
      } catch (error) {
        console.error('Erro no upload:', error);
        toast.error('Erro no upload', {
          description: error instanceof Error ? error.message : 'Erro desconhecido',
        });
        return null;
      } finally {
        setIsUploading(false);
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    []
  );

  /**
   * Listar arquivos do workbook
   */
  const listFiles = useCallback(async (workbookId: string): Promise<StorageFile[]> => {
    if (!workbookId) {
      return [];
    }

    setIsLoading(true);

    try {
      const response = await fetch(`/api/workbooks/${workbookId}/storage`);
      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Erro ao listar arquivos');
      }

      if (result.success) {
        setFiles(result.files || []);
        return result.files || [];
      } else {
        throw new Error(result.error || 'Falha ao listar arquivos');
      }
    } catch (error) {
      console.error('Erro ao listar arquivos:', error);
      toast.error('Erro ao carregar arquivos', {
        description: error instanceof Error ? error.message : 'Erro desconhecido',
      });
      return [];
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Deletar arquivo do Storage
   */
  const deleteFile = useCallback(
    async (filePath: string, workbookId: string): Promise<boolean> => {
      if (!filePath || !workbookId) {
        toast.error('Caminho do arquivo ou ID do workbook não fornecido');
        return false;
      }

      try {
        const response = await fetch(
          `/api/workbooks/${workbookId}/storage?path=${encodeURIComponent(filePath)}`,
          {
            method: 'DELETE',
          }
        );

        const result = await response.json();

        if (!response.ok) {
          throw new Error(result.error || 'Erro ao deletar arquivo');
        }

        if (result.success) {
          toast.success('Arquivo deletado com sucesso!');

          // Atualizar lista de arquivos
          await listFiles(workbookId);

          return true;
        } else {
          throw new Error(result.error || 'Falha ao deletar arquivo');
        }
      } catch (error) {
        console.error('Erro ao deletar arquivo:', error);
        toast.error('Erro ao deletar arquivo', {
          description: error instanceof Error ? error.message : 'Erro desconhecido',
        });
        return false;
      }
    },
    [listFiles]
  );

  /**
   * Download de arquivo (usando URL assinada)
   */
  const downloadFile = useCallback(async (filePath: string, fileName: string): Promise<void> => {
    if (!filePath || !fileName) {
      toast.error('Caminho ou nome do arquivo não fornecido');
      return;
    }

    try {
      toast.loading('Preparando download...', { id: 'download' });

      // Aqui você pode implementar a lógica para obter URL assinada
      // Por enquanto, vamos usar uma abordagem simples
      const response = await fetch(`/api/storage/download?path=${encodeURIComponent(filePath)}`);

      if (!response.ok) {
        throw new Error('Erro ao baixar arquivo');
      }

      const blob = await response.blob();

      // Criar URL temporária e fazer download
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = fileName;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      toast.success('Download concluído!', { id: 'download' });
    } catch (error) {
      console.error('Erro no download:', error);
      toast.error('Erro no download', {
        id: 'download',
        description: error instanceof Error ? error.message : 'Erro desconhecido',
      });
    }
  }, []);

  /**
   * Obter estatísticas de uso do Storage
   */
  const getStorageStats = useCallback(
    async (workbookId: string): Promise<{ totalFiles: number; totalSize: number } | null> => {
      try {
        const fileList = await listFiles(workbookId);

        const stats = {
          totalFiles: fileList.length,
          totalSize: fileList.reduce((total, file) => total + file.size, 0),
        };

        return stats;
      } catch (error) {
        console.error('Erro ao obter estatísticas:', error);
        return null;
      }
    },
    [listFiles]
  );

  return {
    files,
    isLoading,
    isUploading,
    uploadFile,
    deleteFile,
    listFiles,
    downloadFile,
    getStorageStats,
  };
}
