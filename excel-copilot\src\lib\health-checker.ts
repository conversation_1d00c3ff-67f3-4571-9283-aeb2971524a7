/**
 * Serviço de verificação de saúde para dependências externas
 *
 * Este módulo fornece métodos para verificar a disponibilidade e saúde
 * de serviços externos dos quais a aplicação depende.
 */

import { ENV } from '@/config/unified-environment';
import { getRateLimiter } from '@/lib/app-initializer';
import { logger } from '@/lib/logger';

// Verificar se estamos no lado do servidor
const isServer = typeof window === 'undefined';

// Tipos para verificação de saúde
export type HealthStatus = 'healthy' | 'degraded' | 'failing' | 'unhealthy';

export interface HealthCheckResult {
  service: string;
  status: HealthStatus;
  responseTime: number;
  error: string | undefined;
  details?: Record<string, unknown>;
  message?: string;
  data?: Record<string, unknown>;
}

/**
 * Realiza uma verificação de saúde no banco de dados
 */
export async function checkDatabaseHealth(): Promise<HealthCheckResult> {
  const startTime = Date.now();
  let status: 'healthy' | 'degraded' | 'failing' = 'failing';
  let error: string | undefined;
  let prismaClient = null;

  if (!isServer) {
    // No cliente, retorna verificação simulada para evitar erros
    return {
      service: 'database',
      status: 'healthy',
      responseTime: 0,
      error: undefined,
      details: {
        type: 'mysql',
        provider: 'planetscale',
        mode: 'client-side-mock',
      },
    };
  }

  try {
    // Implementar timeout para verificação de banco de dados
    const timeout = ENV.TIMEOUTS.HEALTH_CHECK.EXTERNAL_DEPS;
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(
        () => reject(new Error(`Database health check timeout após ${timeout}ms`)),
        timeout
      );
    });

    // Importação dinâmica do prisma apenas quando estiver no servidor
    const { prisma } = await import('@/server/db/client');
    prismaClient = prisma;

    // Executar uma consulta simples para verificar conectividade com timeout
    await Promise.race([prisma.$queryRaw`SELECT 1 as health`, timeoutPromise]);

    status = 'healthy';
  } catch (e) {
    error = e instanceof Error ? e.message : 'Erro desconhecido ao conectar com o banco de dados';
    logger.error('Falha na verificação de saúde do banco de dados:', e);
  } finally {
    // Garantir que conexões específicas de consulta sejam liberadas
    if (prismaClient) {
      try {
        await prismaClient.$disconnect();
      } catch (disconnectError) {
        logger.error('Erro ao desconectar do banco de dados após health check:', disconnectError);
      }
    }
  }

  const responseTime = Date.now() - startTime;

  // Status degradado se a resposta for muito lenta (50% do timeout configurado)
  const slowThreshold = ENV.TIMEOUTS.HEALTH_CHECK.EXTERNAL_DEPS * 0.5;
  if (status === 'healthy' && responseTime > slowThreshold) {
    status = 'degraded';
    logger.warn(
      `Banco de dados com resposta lenta: ${responseTime}ms (threshold: ${slowThreshold}ms)`
    );
  }

  return {
    service: 'database',
    status,
    responseTime,
    error,
    details: {
      type: 'mysql',
      provider: 'planetscale',
    },
  };
}

/**
 * Verifica a saúde da API de IA (Google Vertex AI)
 */
export async function checkAIServiceHealth(): Promise<HealthCheckResult> {
  const startTime = Date.now();
  let status: 'healthy' | 'degraded' | 'failing' = 'failing';
  let error: string | undefined;
  const _controller: AbortController | null = null;
  const _response: Response | null = null;

  // Se estamos em modo mock ou sem configuração Vertex AI, retornar saudável sem fazer check
  if (ENV.FEATURES.USE_MOCK_AI || !ENV.VERTEX_AI.ENABLED) {
    return {
      service: 'ai-service',
      status: 'healthy',
      responseTime: 0,
      error: undefined,
      details: {
        mode: ENV.FEATURES.USE_MOCK_AI ? 'mock' : 'vertex-ai-disabled',
        provider: 'vertex-ai',
      },
    };
  }

  try {
    // Verificar as configurações do Vertex AI
    const projectId = ENV.VERTEX_AI.PROJECT_ID;
    const location = ENV.VERTEX_AI.LOCATION;

    if (!projectId || !location) {
      throw new Error('Configurações do Vertex AI incompletas');
    }

    // Em desenvolvimento, simulamos disponibilidade com timeout configurável
    if (ENV.IS_DEVELOPMENT) {
      const timeout = ENV.TIMEOUTS.HEALTH_CHECK.EXTERNAL_DEPS;
      const simulationTime = Math.min(100, timeout / 10); // Máximo 100ms ou 10% do timeout
      await new Promise(resolve => setTimeout(resolve, simulationTime));
      status = 'healthy';
    } else {
      // Em produção, verificamos se o arquivo de credenciais existe
      try {
        // Importação dinâmica apenas quando estiver no servidor
        if (typeof window === 'undefined') {
          const fs = await import('fs');
          const path = await import('path');

          // Verificar se o arquivo de credenciais existe
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          const credentialsPath =
            ENV.VERTEX_AI.CREDENTIALS_PATH || path.join(process.cwd(), 'vertex-credentials.json');

          if (fs.existsSync(credentialsPath)) {
            status = 'healthy';
          } else {
            status = 'degraded';
            error = 'Arquivo de credenciais do Vertex AI não encontrado';
          }
        } else {
          // No cliente, consideramos saudável para evitar erros
          status = 'healthy';
        }
      } catch (fsError) {
        status = 'degraded';
        error = `Erro ao verificar arquivo de credenciais: ${fsError instanceof Error ? fsError.message : 'Erro desconhecido'}`;
      }
    }
  } catch (e) {
    error = e instanceof Error ? e.message : 'Erro desconhecido na API de IA';
    logger.error('Falha na verificação de saúde da API de IA:', e);
  }

  const responseTime = Date.now() - startTime;

  // Status degradado se a resposta for muito lenta (30% do timeout configurado)
  const slowThreshold = ENV.TIMEOUTS.HEALTH_CHECK.EXTERNAL_DEPS * 0.3;
  if (status === 'healthy' && responseTime > slowThreshold) {
    status = 'degraded';
    logger.warn(`API de IA com resposta lenta: ${responseTime}ms (threshold: ${slowThreshold}ms)`);
  }

  return {
    service: 'ai-service',
    status,
    responseTime,
    error,
    details: {
      provider: 'vertex-ai',
      model: ENV.VERTEX_AI.MODEL_NAME || 'gemini-1.5-pro',
      projectId: ENV.VERTEX_AI.PROJECT_ID,
      location: ENV.VERTEX_AI.LOCATION,
    },
  };
}

/**
 * Verifica a saúde do serviço de autenticação
 */
export async function checkAuthServiceHealth(): Promise<HealthCheckResult> {
  const startTime = Date.now();
  let status: 'healthy' | 'degraded' | 'failing' = 'failing';
  let error: string | undefined;
  let prismaClient = null;

  if (!isServer) {
    // No cliente, retorna verificação simulada para evitar erros
    return {
      service: 'auth-service',
      status: 'healthy',
      responseTime: 0,
      error: undefined,
      details: {
        provider: 'next-auth',
        adapters: ['google', 'github'],
        mode: 'client-side-mock',
      },
    };
  }

  try {
    // Importação dinâmica do prisma apenas quando estiver no servidor
    const { prisma } = await import('@/server/db/client');
    prismaClient = prisma;

    // Verificar se as variáveis de ambiente necessárias estão configuradas
    const requiredVars = ['NEXTAUTH_SECRET', 'NEXTAUTH_URL'];

    const missingVars = requiredVars.filter(name => !process.env[name]);

    if (missingVars.length > 0) {
      throw new Error(`Variáveis de ambiente ausentes: ${missingVars.join(', ')}`);
    }

    // Verificar banco de dados de autenticação com uma consulta simples
    const _userCount = await prisma.user.count();
    status = 'healthy';
  } catch (e) {
    error = e instanceof Error ? e.message : 'Erro desconhecido no serviço de autenticação';
    logger.error('Falha na verificação de saúde do serviço de autenticação:', e);
  } finally {
    // Garantir que a conexão seja fechada
    if (prismaClient) {
      try {
        await prismaClient.$disconnect();
      } catch (disconnectError) {
        logger.error(
          'Erro ao desconectar do banco de dados após auth health check:',
          disconnectError
        );
      }
    }
  }

  const responseTime = Date.now() - startTime;

  return {
    service: 'auth-service',
    status,
    responseTime,
    error,
    details: {
      provider: 'next-auth',
      adapters: ['google', 'github'],
    },
  };
}

/**
 * Verifica o estado atual dos rate limiters
 */
export async function checkRateLimitersHealth(): Promise<HealthCheckResult> {
  const startTime = Date.now();
  let status: HealthStatus = 'healthy';
  let message = 'Rate limiters funcionando normalmente';
  const error: string | undefined = undefined;
  const _error: Error | null = null;
  const limiters: {
    chat: ReturnType<typeof getRateLimiter> | null;
    excel: ReturnType<typeof getRateLimiter> | null;
    api: ReturnType<typeof getRateLimiter> | null;
  } = { chat: null, excel: null, api: null };

  try {
    limiters.chat = getRateLimiter('chat');
    limiters.excel = getRateLimiter('excel');
    limiters.api = getRateLimiter('api');

    if (!limiters.chat || !limiters.excel || !limiters.api) {
      return {
        service: 'rate-limiters',
        status: 'unhealthy',
        message: 'Um ou mais rate limiters não estão inicializados',
        responseTime: Date.now() - startTime,
        error: 'Rate limiters não inicializados corretamente',
      };
    }

    // Coletar diagnósticos de cada rate limiter
    const chatDiag = limiters.chat.getDiagnostics();
    const excelDiag = limiters.excel.getDiagnostics();
    const apiDiag = limiters.api.getDiagnostics();

    // Verificar se algum rate limiter está próximo do limite
    const thresholdPercentage = 0.9; // 90% de utilização é considerado alerta

    if (
      chatDiag.totalEntries > chatDiag.maxEntries * thresholdPercentage ||
      excelDiag.totalEntries > excelDiag.maxEntries * thresholdPercentage ||
      apiDiag.totalEntries > apiDiag.maxEntries * thresholdPercentage
    ) {
      status = 'degraded';
      message = 'Um ou mais rate limiters estão próximos da capacidade máxima';

      // Registrar alerta no log
      logger.warn('Rate limiters com alta utilização de memória', {
        chat: `${chatDiag.totalEntries}/${chatDiag.maxEntries} (${Math.round((chatDiag.totalEntries / chatDiag.maxEntries) * 100)}%)`,
        excel: `${excelDiag.totalEntries}/${excelDiag.maxEntries} (${Math.round((excelDiag.totalEntries / excelDiag.maxEntries) * 100)}%)`,
        api: `${apiDiag.totalEntries}/${apiDiag.maxEntries} (${Math.round((apiDiag.totalEntries / apiDiag.maxEntries) * 100)}%)`,
      });
    }

    return {
      service: 'rate-limiters',
      status,
      message,
      responseTime: Date.now() - startTime,
      error,
      data: {
        chat: chatDiag,
        excel: excelDiag,
        api: apiDiag,
      },
    };
  } catch (e) {
    const errorMessage = e instanceof Error ? e.message : String(e);
    return {
      service: 'rate-limiters',
      status: 'unhealthy',
      message: 'Erro ao verificar rate limiters',
      responseTime: Date.now() - startTime,
      error: errorMessage,
    };
  } finally {
    // O GC vai cuidar da limpeza dos recursos normalmente
    // Não há métodos específicos para liberação manual de recursos nos rate limiters
    logger.debug('Concluindo health check de rate limiters');
  }
}

/**
 * Verifica a saúde das integrações MCP
 */
export async function checkMCPIntegrationsHealth(): Promise<HealthCheckResult> {
  const startTime = Date.now();
  let status: HealthStatus = 'healthy';
  let message = 'Integrações MCP funcionando normalmente';
  let error: string | undefined;
  const mcpResults: Record<
    string,
    {
      name: string;
      status: string;
      details?: unknown;
      error?: string;
    }
  > = {};

  if (!isServer) {
    // No cliente, retorna verificação simulada
    return {
      service: 'mcp-integrations',
      status: 'healthy',
      responseTime: 0,
      error: undefined,
      details: {
        mode: 'client-side-mock',
        integrations: ['vercel', 'linear', 'github', 'supabase'],
      },
    };
  }

  try {
    // Importar dinamicamente as integrações MCP
    const [
      { VercelMonitoringService },
      { LinearMonitoringService },
      { GitHubMonitoringService },
      { SupabaseMonitoringService },
    ] = await Promise.all([
      import('./vercel-integration'),
      import('./linear-integration'),
      import('./github-integration'),
      import('./supabase-integration'),
    ]);

    // Verificar cada integração MCP
    const mcpChecks = await Promise.allSettled([
      // Vercel MCP
      (async () => {
        try {
          if (!ENV.VERCEL_API_TOKEN) {
            return {
              name: 'vercel',
              status: 'failing',
              error: 'VERCEL_API_TOKEN não configurado',
            };
          }
          const vercelService = new VercelMonitoringService(
            ENV.VERCEL_API_TOKEN,
            ENV.VERCEL_TEAM_ID || undefined,
            ENV.VERCEL_PROJECT_ID || undefined
          );
          const vercelStatus = await vercelService.getProjectStatus();
          return { name: 'vercel', status: vercelStatus.status, details: vercelStatus };
        } catch (e) {
          return {
            name: 'vercel',
            status: 'failing',
            error: e instanceof Error ? e.message : 'Erro desconhecido',
          };
        }
      })(),

      // Linear MCP
      (async () => {
        try {
          // Verifica se temos API key ou se a integração MCP está disponível
          const { isMCPAvailable } = await import('@/lib/mcp-tools');

          if (!ENV.LINEAR_API_KEY && !isMCPAvailable()) {
            return {
              name: 'linear',
              status: 'failing',
              error: 'LINEAR_API_KEY não configurado e MCP não disponível',
            };
          }

          const linearService = new LinearMonitoringService(ENV.LINEAR_API_KEY);
          const linearStatus = await linearService.getWorkspaceSummary();
          return {
            name: 'linear',
            status: 'healthy',
            details: {
              ...linearStatus,
              usingMCP: !ENV.LINEAR_API_KEY || ENV.LINEAR_API_KEY === 'mcp_integration_enabled',
            },
          };
        } catch (e) {
          return {
            name: 'linear',
            status: 'failing',
            error: e instanceof Error ? e.message : 'Erro desconhecido',
          };
        }
      })(),

      // GitHub MCP
      (async () => {
        try {
          if (!ENV.GITHUB_TOKEN) {
            return {
              name: 'github',
              status: 'failing',
              error: 'GITHUB_TOKEN não configurado',
            };
          }
          const githubService = new GitHubMonitoringService({
            token: ENV.GITHUB_TOKEN,
            ...(ENV.GITHUB_OWNER && { owner: ENV.GITHUB_OWNER }),
            ...(ENV.GITHUB_REPO && { repo: ENV.GITHUB_REPO }),
          });
          const githubStatus = await githubService.getRepositoryDashboard();
          return { name: 'github', status: githubStatus.healthStatus, details: githubStatus };
        } catch (e) {
          return {
            name: 'github',
            status: 'failing',
            error: e instanceof Error ? e.message : 'Erro desconhecido',
          };
        }
      })(),

      // Supabase MCP
      (async () => {
        try {
          const supabaseService = new SupabaseMonitoringService();
          const supabaseStatus = await supabaseService.getProjectStatus();
          return { name: 'supabase', status: supabaseStatus.status, details: supabaseStatus };
        } catch (e) {
          return {
            name: 'supabase',
            status: 'failing',
            error: e instanceof Error ? e.message : 'Erro desconhecido',
          };
        }
      })(),
    ]);

    // Processar resultados
    let healthyCount = 0;
    let degradedCount = 0;
    let failingCount = 0;

    mcpChecks.forEach((result, index) => {
      const integrationNames = ['vercel', 'linear', 'github', 'supabase'];
      const integrationName = integrationNames[index];

      if (!integrationName) return; // Skip se não há nome de integração

      if (result.status === 'fulfilled') {
        const mcpResult = result.value;
        mcpResults[integrationName] = mcpResult;

        switch (mcpResult.status) {
          case 'healthy':
            healthyCount++;
            break;
          case 'degraded':
            degradedCount++;
            break;
          case 'failing':
          case 'down':
            failingCount++;
            break;
        }
      } else {
        mcpResults[integrationName] = {
          name: integrationName,
          status: 'failing',
          error: result.reason?.message || 'Erro desconhecido',
        };
        failingCount++;
      }
    });

    // Determinar status geral
    if (failingCount > 2) {
      status = 'failing';
      message = `${failingCount} integrações MCP com falha`;
    } else if (failingCount > 0 || degradedCount > 1) {
      status = 'degraded';
      message = `${failingCount} falhas, ${degradedCount} degradadas`;
    } else if (degradedCount > 0) {
      status = 'degraded';
      message = `${degradedCount} integrações degradadas`;
    }

    logger.info('Verificação MCP concluída', {
      healthy: healthyCount,
      degraded: degradedCount,
      failing: failingCount,
    });
  } catch (e) {
    error = e instanceof Error ? e.message : 'Erro desconhecido na verificação MCP';
    status = 'failing';
    message = 'Erro ao verificar integrações MCP';
    logger.error('Falha na verificação de saúde das integrações MCP:', e);
  }

  return {
    service: 'mcp-integrations',
    status,
    message,
    responseTime: Date.now() - startTime,
    error,
    details: {
      integrations: mcpResults,
      summary: {
        total: 4,
        healthy: Object.values(mcpResults).filter(r => r.status === 'healthy').length,
        degraded: Object.values(mcpResults).filter(r => r.status === 'degraded').length,
        failing: Object.values(mcpResults).filter(r => ['failing', 'down'].includes(r.status))
          .length,
      },
    },
  };
}

/**
 * Verifica a saúde de serviços externos (usado apenas em produção)
 */
export async function checkExternalDependenciesHealth(): Promise<HealthCheckResult> {
  const startTime = Date.now();
  let controller: AbortController | null = null;
  let response: Response | null = null;
  const error: string | undefined = undefined;

  try {
    // Aqui seria implementada a verificação de dependências externas
    // como CDNs, serviços de terceiros, etc.

    // Exemplo: verificar conexão com um serviço externo
    if (ENV.IS_PRODUCTION) {
      controller = new AbortController();
      const signal = controller.signal;
      const timeout = ENV.TIMEOUTS.HEALTH_CHECK.EXTERNAL_DEPS;
      const timeoutId = setTimeout(() => controller?.abort(), timeout);

      try {
        // Exemplo de verificação de um serviço externo
        response = await fetch('https://api.external-service.com/health', {
          signal,
        });

        clearTimeout(timeoutId);
      } catch (fetchError) {
        if (fetchError instanceof Error && fetchError.name !== 'AbortError') {
          throw fetchError;
        }
      }
    }

    // Por enquanto retornamos como saudável
    return {
      service: 'external-dependencies',
      status: 'healthy',
      message: 'Dependências externas funcionando normalmente',
      responseTime: Date.now() - startTime,
      error,
    };
  } catch (e) {
    const errorMessage = e instanceof Error ? e.message : String(e);
    return {
      service: 'external-dependencies',
      status: 'degraded',
      message: 'Erro ao verificar dependências externas',
      responseTime: Date.now() - startTime,
      error: errorMessage,
    };
  } finally {
    // Garantir que controladores e respostas HTTP sejam liberados
    if (controller) {
      try {
        controller.abort();
      } catch (abortError) {
        logger.error('Erro ao abortar conexão HTTP em external dependency check:', abortError);
      }
    }

    if (response && !response.bodyUsed) {
      try {
        response.body?.cancel();
      } catch (cancelError) {
        logger.error(
          'Erro ao cancelar body de resposta em external dependency check:',
          cancelError
        );
      }
    }
  }
}

/**
 * Executa todas as verificações de saúde de serviços externos
 */
export async function checkAllServicesHealth(): Promise<HealthCheckResult[]> {
  const results: HealthCheckResult[] = [];
  const promises: Promise<HealthCheckResult>[] = [];

  // Iniciar verificações em paralelo
  promises.push(checkDatabaseHealth());
  promises.push(checkAIServiceHealth());
  promises.push(checkRateLimitersHealth());
  promises.push(checkMCPIntegrationsHealth());

  // Se estiver em produção, verificar recursos adicionais
  if (ENV.IS_PRODUCTION) {
    promises.push(checkExternalDependenciesHealth());
  }

  // Aguardar todas as verificações e capturar erros individualmente
  for (const promise of promises) {
    try {
      const result = await promise;
      results.push(result);
    } catch (e) {
      logger.error('Erro não tratado ao executar verificação de saúde:', e);
      // Adicionar resultado de falha para este serviço
      results.push({
        service: 'unknown-service',
        status: 'failing',
        error: e instanceof Error ? e.message : String(e),
        message: 'Erro não tratado durante verificação de saúde',
        responseTime: 0,
      });
    }
  }

  return results;
}

/**
 * Verifica se todos os serviços críticos estão saudáveis
 */
export function isSystemHealthy(results: HealthCheckResult[]): boolean {
  const criticalServices = ['database', 'auth-service'];

  // Sistema é considerado saudável se todos os serviços críticos
  // estão saudáveis ou degradados (mas não falhos)
  return criticalServices.every(serviceName => {
    const service = results.find(s => s.service === serviceName);
    return service && service.status !== 'failing';
  });
}
