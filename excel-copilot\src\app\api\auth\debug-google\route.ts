import { NextResponse } from 'next/server';

export const dynamic = 'force-dynamic';

export async function GET() {
  try {
    // Obter as configurações do ambiente
    const clientId = process.env.AUTH_GOOGLE_CLIENT_ID;
    const redirectUri = `${process.env.AUTH_NEXTAUTH_URL}/api/auth/callback/google`;

    // Verifique se as variáveis de ambiente estão definidas
    if (!clientId || !redirectUri) {
      return NextResponse.json(
        {
          error: 'Configuração incompleta',
          message: 'Configurações OAuth ausentes',
        },
        { status: 500 }
      );
    }

    // Montar URL de autorização Google diretamente
    const scopes = ['profile', 'email'].map(scope => encodeURIComponent(scope)).join('%20');

    // Gera um estado simples
    const state = Math.random().toString(36).substring(2, 15);

    // Construir a URL de autorização
    const googleAuthUrl =
      `https://accounts.google.com/o/oauth2/v2/auth?` +
      `client_id=${encodeURIComponent(clientId)}` +
      `&redirect_uri=${encodeURIComponent(redirectUri)}` +
      `&response_type=code` +
      `&scope=${scopes}` +
      `&state=${state}` +
      `&prompt=consent` +
      `&access_type=offline`;

    // Redirecionar para a URL de autorização
    return NextResponse.redirect(googleAuthUrl);
  } catch {
    return NextResponse.json(
      {
        error: 'Erro interno',
        message: 'Ocorreu um erro ao iniciar autenticação Google',
      },
      { status: 500 }
    );
  }
}
