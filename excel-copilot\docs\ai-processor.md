# Excel AI Processor

O Excel AI Processor é um sistema avançado de processamento de linguagem natural que converte comandos em texto livre em operações estruturadas do Excel.

## Visão Geral

O sistema foi projetado para interpretar instruções complexas dos usuários e transformá-las em operações executáveis de planilha. O processador utiliza técnicas de NLP (Processamento de Linguagem Natural) para entender a intenção do usuário e extrair entidades relevantes da consulta.

## Principais Recursos

- **Análise de intenção**: Identifica o tipo de operação que o usuário deseja realizar (fórmula, gráfico, filtro, etc.)
- **Extração de entidades**: Reconhece células, intervalos, colunas, valores e outros elementos relevantes na consulta
- **Sensível ao contexto**: Utiliza o contexto da planilha atual (seleção, cabeçalhos, etc.) para processar consultas mais naturais
- **Processamento robusto**: Mecanismo de fallback para o sistema legado quando necessário
- **Normalização de consultas**: Lida com abreviações e variações na linguagem do usuário

## Tipos de Operações Suportadas

O processador suporta os seguintes tipos de operações:

1. **Fórmulas**: Aplicação de fórmulas em células ou intervalos

   - Ex: "Aplique a fórmula =SOMA(A1:A10) na célula B1"
   - Ex: "Use a função MÉDIA para o intervalo C5:C15 na célula D5"

2. **Operações de coluna**: Cálculos em colunas inteiras

   - Ex: "Some os valores da coluna Vendas"
   - Ex: "Calcule a média da coluna B"

3. **Gráficos**: Criação e modificação de gráficos

   - Ex: "Crie um gráfico de barras usando os dados A1:B10"
   - Ex: "Adicione um gráfico de pizza para visualizar as vendas por região"

4. **Filtros e Ordenação**: Manipulação de dados tabulares

   - Ex: "Filtre a coluna Preço onde valor > 1000"
   - Ex: "Ordene a coluna Nome em ordem alfabética"

5. **Tabelas Dinâmicas**: Criação e configuração de tabelas dinâmicas

   - Ex: "Crie uma tabela dinâmica com Região nas linhas e Produto nas colunas"

6. **Formatação Condicional**: Aplicação de regras visuais aos dados

   - Ex: "Destaque em vermelho os valores menores que zero na coluna Lucro"

7. **Visualizações Avançadas**: Criação de visualizações especializadas

   - Ex: "Crie um mapa de calor para os dados de vendas mensais"
   - Ex: "Adicione mini gráficos (sparklines) para mostrar tendências em cada linha"

8. **Análise de Dados**: Operações analíticas complexas
   - Ex: "Faça uma previsão de vendas para os próximos 3 meses baseado nos dados históricos"
   - Ex: "Calcule a correlação entre as colunas Preço e Demanda"

## Arquitetura

O sistema é composto por vários componentes que trabalham em conjunto:

```
┌─────────────────┐     ┌───────────────────┐     ┌──────────────────┐
│                 │     │                   │     │                  │
│  Pré-Processor  │────▶│  Intent Analyzer  │────▶│ Entity Extractor │
│                 │     │                   │     │                  │
└─────────────────┘     └───────────────────┘     └──────────────────┘
         │                                                 │
         │                                                 ▼
┌─────────────────┐                              ┌──────────────────┐
│                 │                              │                  │
│  Context Store  │◀─────────────────────────────│ Operation Builder│
│                 │                              │                  │
└─────────────────┘                              └──────────────────┘
         │                                                 │
         ▼                                                 ▼
┌─────────────────┐                              ┌──────────────────┐
│                 │                              │                  │
│ Fallback System │◀─────────────────────────────│  Result Composer │
│                 │                              │                  │
└─────────────────┘                              └──────────────────┘
```

## Uso no Código

```typescript
import { createExcelAIProcessor } from '@/lib/ai';

// Criar uma instância do processador
const processor = createExcelAIProcessor();

// Opcionalmente, configurar o contexto
processor.updateContext({
  spreadsheetContext: {
    activeSheet: 'Vendas',
    selection: 'A1:B10',
    headers: ['Produto', 'Valor', 'Quantidade'],
  },
});

// Processar uma consulta
const result = await processor.processQuery('Calcule a soma da coluna Valor');

// Verificar o resultado
if (result.operations.length > 0) {
  console.log(`Operações encontradas: ${result.operations.length}`);

  // Executar as operações na planilha
  await executeExcelOperations(result.operations);
} else {
  console.error(`Erro: ${result.error}`);
}
```

## Extensão do Sistema

O sistema foi projetado para ser facilmente extensível. Para adicionar suporte a novos tipos de operações:

1. Adicione padrões de reconhecimento de intenção no método `identifyIntent()`
2. Implemente a extração específica no método correspondente (ex: `extractDataAnalysisOperations()`)
3. Atualize os testes unitários para validar o novo comportamento

## Integração com LLMs (Opcional)

O processador pode ser integrado com modelos de linguagem de grande escala (LLMs) para aumentar suas capacidades:

```typescript
// Exemplo de integração com LLM (pseudocódigo)
async function enhanceWithLLM(query: string, context: any) {
  const llmResponse = await callExternalLLM({
    prompt: `Analise a seguinte consulta Excel: "${query}"`,
    context: context,
  });

  return {
    enhancedQuery: llmResponse.enhancedQuery,
    structuredOperations: llmResponse.operations,
  };
}
```

## Considerações Futuras

- Implementação de aprendizado contínuo baseado em feedback do usuário
- Suporte a operações mais complexas e customizadas
- Integração com recursos de IA generativa para sugerir análises proativas
