import { test, expect, devices } from '@playwright/test';

// Configuração para dispositivo mobile fora do describe
test.use({
  ...devices['iPhone 12'],
});

test.describe('Responsividade Mobile', () => {
  // Testes com dispositivo mobile

  test('deve ter layout adaptado para mobile na página inicial', async ({ page }) => {
    // Navegar para a página inicial
    await page.goto('/');

    // Verificar se a página carregou corretamente
    await expect(page.getByText('Excel Copilot')).toBeVisible();

    // Verificar se os elementos de UI estão adaptados para mobile
    // Os tabs devem estar um abaixo do outro em vez de lado a lado
    await expect(page.getByRole('tab', { name: 'Chat' })).toBeVisible();
    await expect(page.getByRole('tab', { name: 'Upload de Planilha' })).toBeVisible();

    // Verificar margin/padding reduzido para economizar espaço
    const mainContainer = page.locator('main');
    const padding = await mainContainer.evaluate(el => {
      const style = window.getComputedStyle(el);
      return style.padding;
    });

    // Padding em mobile deve ser menor que em desktop
    expect(padding).not.toContain('24px');
  });

  test('deve ter layout adaptado para mobile no dashboard', async ({ page }) => {
    // Simular autenticação
    await page.context().addCookies([
      {
        name: 'next-auth.session-token',
        value: 'mock-session-token',
        domain: 'localhost',
        path: '/',
        httpOnly: true,
        secure: false,
      },
    ]);

    // Configurar dados do usuário
    await page.evaluate(() => {
      localStorage.setItem(
        'user',
        JSON.stringify({
          id: 'test-user-id',
          name: 'Test User',
          email: '<EMAIL>',
        })
      );
    });

    // Navegar para o dashboard
    await page.goto('/dashboard');

    // Verificar se a página carregou corretamente
    await expect(page.getByText('Minhas Planilhas')).toBeVisible();

    // Verificar se cards de planilhas estão em uma coluna única
    const workbookCards = page.locator('.workbook-card');
    const firstCardBox = await workbookCards.first().boundingBox();
    const secondCardBox = await workbookCards.nth(1).boundingBox();

    if (firstCardBox && secondCardBox) {
      // Em mobile, os cards devem estar empilhados (um abaixo do outro)
      // então o segundo card deve ter Y maior que o primeiro
      expect(secondCardBox.y).toBeGreaterThan(firstCardBox.y + firstCardBox.height - 5);
    }
  });

  test('deve ter interface de planilha adaptada para mobile', async ({ page }) => {
    // Simular autenticação
    await page.context().addCookies([
      {
        name: 'next-auth.session-token',
        value: 'mock-session-token',
        domain: 'localhost',
        path: '/',
        httpOnly: true,
        secure: false,
      },
    ]);

    // Configurar dados do usuário
    await page.evaluate(() => {
      localStorage.setItem(
        'user',
        JSON.stringify({
          id: 'test-user-id',
          name: 'Test User',
          email: '<EMAIL>',
        })
      );
    });

    // Navegar para uma planilha existente (assumindo que existe)
    await page.goto('/dashboard');
    await page.locator('.workbook-card').first().click();

    // Verificar se estamos na página de workbook
    await expect(page.url()).toContain('/workbook/');

    // Verificar se a planilha está visível e adaptada
    await expect(page.locator('.excel-grid')).toBeVisible();

    // Em mobile, deve haver controles para scroll horizontal/zoom
    // ou a tabela deve ter layout responsivo
    const excelGridWidth = await page.locator('.excel-grid').evaluate(el => el.clientWidth);
    const pageWidth = await page.evaluate(() => window.innerWidth);

    // A grade Excel não deve ultrapassar a largura da tela
    expect(excelGridWidth).toBeLessThanOrEqual(pageWidth);
  });

  test('deve ter chat adaptado para mobile', async ({ page }) => {
    // Simular autenticação
    await page.context().addCookies([
      {
        name: 'next-auth.session-token',
        value: 'mock-session-token',
        domain: 'localhost',
        path: '/',
        httpOnly: true,
        secure: false,
      },
    ]);

    // Configurar dados do usuário
    await page.evaluate(() => {
      localStorage.setItem(
        'user',
        JSON.stringify({
          id: 'test-user-id',
          name: 'Test User',
          email: '<EMAIL>',
        })
      );
    });

    // Navegar para uma planilha existente
    await page.goto('/dashboard');
    await page.locator('.workbook-card').first().click();

    // Navegar para a aba de chat
    await page.getByRole('tab', { name: 'Chat' }).click();

    // Verificar se a interface de chat está visível
    await expect(page.getByPlaceholder('Digite sua mensagem...')).toBeVisible();

    // Verificar se o input de mensagem está adaptado à largura da tela
    const chatInputWidth = await page
      .getByPlaceholder('Digite sua mensagem...')
      .evaluate(el => el.clientWidth);
    const pageWidth = await page.evaluate(() => window.innerWidth);

    // O input não deve ser maior que a largura da página
    expect(chatInputWidth).toBeLessThanOrEqual(pageWidth);

    // Verificar se o botão de enviar está posicionado corretamente
    await expect(page.getByRole('button', { name: 'Enviar' })).toBeVisible();
  });

  test('deve ter menu colapsado em mobile', async ({ page }) => {
    // Simular autenticação
    await page.context().addCookies([
      {
        name: 'next-auth.session-token',
        value: 'mock-session-token',
        domain: 'localhost',
        path: '/',
        httpOnly: true,
        secure: false,
      },
    ]);

    // Navegar para o dashboard
    await page.goto('/dashboard');

    // Verificar se há um botão de menu hamburger
    await expect(page.locator('.mobile-menu-button, [aria-label="Menu"]')).toBeVisible();

    // Clicar no botão de menu
    await page.locator('.mobile-menu-button, [aria-label="Menu"]').click();

    // Verificar se o menu lateral aparece
    await expect(page.locator('.mobile-nav-menu, .sidebar')).toBeVisible();

    // Verificar se contém os links esperados
    await expect(page.getByText('Dashboard')).toBeVisible();
    await expect(page.getByText('Perfil')).toBeVisible();
  });
});
