import { Message } from 'ai';

/**
 * Interface para mensagens de IA estendidas com metadados de operações Excel
 */
export interface ExtendedAIMessage extends Message {
  operationsExecuted?: boolean;
  operationCount?: number;
  dataUpdated?: boolean;
  operationId?: string;
  operationResult?: unknown;
}

/**
 * Interface para as props do componente ChatInterface
 */
export interface ChatInterfaceProps {
  /**
   * ID da planilha atual, se aplicável
   */
  workbookId?: string;

  /**
   * Callback chamado quando dados da planilha são atualizados
   */
  onDataUpdated?: () => void;
}

/**
 * Interface para a referência exposta pelo componente ChatInterface
 */
export interface ChatInterfaceHandle {
  /**
   * Define o valor do input de texto
   */
  setInputValue: (value: string) => void;

  /**
   * Submete a mensagem atual
   */
  submitMessage: () => void;
}

export interface ChatMessageProps {
  message: ExtendedAIMessage;
  isLast?: boolean;
  isProcessing?: boolean;
  isExcelConnected?: boolean;
}

export interface EmptyStateProps {
  showSuggestions?: boolean;
  suggestions?: Array<{ text: string; icon?: React.ReactNode; category?: string }>;
  onSuggestionClick?: (suggestion: { text: string; icon?: React.ReactNode }) => void;
  isExcelConnected?: boolean;
}
