/**
 * @jest-environment jsdom
 */

import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { server } from '../../mocks/server';
import CommandExamplesMock from '../../mocks/command-examples-mock';

// Mock do componente real
jest.mock('@/components/command-examples', () => ({
  CommandExamples: CommandExamplesMock,
}));

describe('CommandExamples Component', () => {
  beforeAll(() => {
    server.listen();
  });

  afterEach(() => {
    server.resetHandlers();
  });

  afterAll(() => {
    server.close();
  });

  test('renderiza as categorias de comandos', () => {
    render(<CommandExamplesMock />);

    // Verificar se as categorias estão sendo exibidas
    expect(screen.getByTestId('category-matematica')).toBeInTheDocument();
    expect(screen.getByTestId('category-formatacao')).toBeInTheDocument();
    expect(screen.getByTestId('category-graficos')).toBeInTheDocument();

    // Verificar se o título está presente
    expect(screen.getByText('Exemplos de Comandos')).toBeInTheDocument();
  });

  test('renderiza exemplos de comandos para uma categoria', () => {
    render(<CommandExamplesMock />);

    // Por padrão, a categoria "Análise Matemática" é selecionada
    expect(screen.getByText('Análise Matemática')).toBeInTheDocument();

    // Verificar se os comandos da categoria estão visíveis
    expect(screen.getByText('Some os valores da coluna B')).toBeInTheDocument();

    // Clicar em outra categoria
    fireEvent.click(screen.getByTestId('category-formatacao'));

    // Verificar se os comandos da nova categoria são exibidos
    expect(screen.getByText('Aplique negrito na linha de cabeçalho')).toBeInTheDocument();
  });

  test('chama a função onSelect quando um comando é clicado', () => {
    const onSelectMock = jest.fn();
    render(<CommandExamplesMock onSelect={onSelectMock} />);

    // Clicar em um comando
    fireEvent.click(screen.getByText('Some os valores da coluna B'));

    // Verificar se a função foi chamada com o comando correto
    expect(onSelectMock).toHaveBeenCalledWith('Some os valores da coluna B');
  });
});
