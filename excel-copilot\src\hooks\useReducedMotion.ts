import { useState, useEffect } from 'react';

/**
 * Hook que detecta se o usuário prefere movimento reduzido.
 * Utiliza a media query 'prefers-reduced-motion' e atualiza
 * ao vivo se a preferência do usuário mudar.
 *
 * @returns {boolean} true se o usuário prefere movimento reduzido, false caso contrário
 */
export function useReducedMotion(): boolean {
  // Definir estado inicial baseado na preferência do usuário
  const [prefersReducedMotion, setPrefersReducedMotion] = useState<boolean>(() => {
    // No servidor, assumimos false como padrão
    if (typeof window === 'undefined') return false;

    // Checar a preferência atual do usuário
    return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
  });

  useEffect(() => {
    // Não executar no servidor
    if (typeof window === 'undefined') return;

    // Criar o media query matcher
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');

    // Handler para atualizar o estado quando a preferência mudar
    const handleMediaChange = (event: MediaQueryListEvent) => {
      setPrefersReducedMotion(event.matches);
    };

    // Adicionar listener para mudanças na preferência
    // Usando a API moderna para compatibilidade com navegadores modernos
    if (mediaQuery.addEventListener) {
      mediaQuery.addEventListener('change', handleMediaChange);
    } else {
      // Fallback para navegadores antigos (Safari)
      mediaQuery.addListener(handleMediaChange);
    }

    // Limpar listener quando o componente for desmontado
    return () => {
      if (mediaQuery.removeEventListener) {
        mediaQuery.removeEventListener('change', handleMediaChange);
      } else {
        // Fallback para navegadores antigos
        mediaQuery.removeListener(handleMediaChange);
      }
    };
  }, []);

  return prefersReducedMotion;
}
