/**
 * Utilitários de segurança para autenticação
 * Implementa validação de redirecionamentos e outras medidas de segurança
 */

import { logger } from '@/lib/logger';

/**
 * URLs permitidas para redirecionamento após login
 */
const ALLOWED_REDIRECT_PATHS = [
  '/',
  '/dashboard',
  '/workbook',
  '/account',
  '/profile',
  '/settings',
  '/pricing',
  '/examples',
  '/templates',
];

/**
 * Domínios permitidos para redirecionamento
 */
const ALLOWED_DOMAINS = ['localhost', 'excel-copilot-eight.vercel.app', 'excel-copilot.vercel.app'];

/**
 * Cria uma URL de redirecionamento segura
 * Valida contra ataques de redirecionamento aberto
 */
export function createSecureRedirectUrl(url: string, baseUrl: string): string {
  try {
    // Se a URL for relativa, considerar como segura se estiver na lista permitida
    if (url.startsWith('/')) {
      const isAllowed = ALLOWED_REDIRECT_PATHS.some(
        path => url === path || url.startsWith(`${path}/`) || url.startsWith(`${path}?`)
      );

      if (isAllowed) {
        logger.debug('Redirecionamento seguro (path relativo)', { url, baseUrl });
        return url.startsWith(baseUrl) ? url : `${baseUrl}${url}`;
      } else {
        logger.warn('Tentativa de redirecionamento para path não permitido', { url, baseUrl });
        return baseUrl + '/dashboard';
      }
    }

    // Parse da URL completa
    const parsedUrl = new URL(url);
    const _parsedBaseUrl = new URL(baseUrl);

    // Verificar se o domínio está na lista permitida
    const isDomainAllowed = ALLOWED_DOMAINS.some(domain => {
      return (
        parsedUrl.hostname === domain ||
        parsedUrl.hostname.endsWith(`.${domain}`) ||
        (domain === 'localhost' && parsedUrl.hostname.startsWith('localhost'))
      );
    });

    if (!isDomainAllowed) {
      logger.warn('Tentativa de redirecionamento para domínio não permitido', {
        url,
        hostname: parsedUrl.hostname,
        baseUrl,
        allowedDomains: ALLOWED_DOMAINS,
      });
      return baseUrl + '/dashboard';
    }

    // Verificar se o protocolo é seguro
    if (parsedUrl.protocol !== 'http:' && parsedUrl.protocol !== 'https:') {
      logger.warn('Tentativa de redirecionamento com protocolo inseguro', {
        url,
        protocol: parsedUrl.protocol,
        baseUrl,
      });
      return baseUrl + '/dashboard';
    }

    // Em produção, forçar HTTPS
    if (process.env.NODE_ENV === 'production' && parsedUrl.protocol === 'http:') {
      logger.warn('Forçando HTTPS em produção', { url, baseUrl });
      parsedUrl.protocol = 'https:';
    }

    // Verificar se o path está permitido
    const isPathAllowed = ALLOWED_REDIRECT_PATHS.some(
      path =>
        parsedUrl.pathname === path ||
        parsedUrl.pathname.startsWith(`${path}/`) ||
        parsedUrl.pathname.startsWith(`${path}?`)
    );

    if (!isPathAllowed) {
      logger.warn('Tentativa de redirecionamento para path não permitido', {
        url,
        pathname: parsedUrl.pathname,
        baseUrl,
      });
      return baseUrl + '/dashboard';
    }

    const secureUrl = parsedUrl.toString();
    logger.debug('Redirecionamento seguro validado', {
      originalUrl: url,
      secureUrl,
      baseUrl,
    });

    return secureUrl;
  } catch (error) {
    logger.error('Erro ao validar URL de redirecionamento', {
      url,
      baseUrl,
      error: error instanceof Error ? error.message : 'Erro desconhecido',
    });

    // Fallback seguro
    return baseUrl + '/dashboard';
  }
}

/**
 * Valida se uma URL de callback é segura
 */
export function isValidCallbackUrl(callbackUrl: string, baseUrl: string): boolean {
  try {
    const secureUrl = createSecureRedirectUrl(callbackUrl, baseUrl);
    return secureUrl === callbackUrl || secureUrl === `${baseUrl}${callbackUrl}`;
  } catch {
    return false;
  }
}

/**
 * Sanitiza parâmetros de URL para prevenir ataques
 */
export function sanitizeUrlParams(params: Record<string, string>): Record<string, string> {
  const sanitized: Record<string, string> = {};

  for (const [key, value] of Object.entries(params)) {
    // Remover caracteres perigosos
    const sanitizedKey = key.replace(/[<>"'&]/g, '');
    const sanitizedValue = value.replace(/[<>"'&]/g, '');

    // Limitar tamanho
    if (sanitizedKey.length <= 50 && sanitizedValue.length <= 500) {
      sanitized[sanitizedKey] = sanitizedValue;
    } else {
      logger.warn('Parâmetro de URL muito longo ignorado', { key, valueLength: value.length });
    }
  }

  return sanitized;
}

/**
 * Gera um token CSRF seguro
 */
export async function generateCSRFToken(): Promise<string> {
  const array = new Uint8Array(32);
  if (typeof crypto !== 'undefined' && crypto.getRandomValues) {
    crypto.getRandomValues(array);
  } else {
    // Fallback para Node.js
    const crypto = await import('crypto');
    const buffer = crypto.randomBytes(32);
    array.set(buffer);
  }

  return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
}

/**
 * Valida um token CSRF
 */
export function validateCSRFToken(token: string, expectedToken: string): boolean {
  if (!token || !expectedToken) {
    return false;
  }

  if (token.length !== expectedToken.length) {
    return false;
  }

  // Comparação de tempo constante para prevenir ataques de timing
  let result = 0;
  for (let i = 0; i < token.length; i++) {
    result |= token.charCodeAt(i) ^ expectedToken.charCodeAt(i);
  }

  return result === 0;
}

/**
 * Extrai informações seguras do User Agent
 */
export function extractSafeUserAgent(userAgent?: string): string {
  if (!userAgent) {
    return 'Unknown';
  }

  // Limitar tamanho e remover informações sensíveis
  const cleaned = userAgent
    .substring(0, 200)
    .replace(/[<>"'&]/g, '')
    .replace(/\b\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\b/g, '[IP]') // Remover IPs
    .replace(/\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g, '[EMAIL]'); // Remover emails

  return cleaned || 'Unknown';
}

interface RequestWithHeaders {
  headers?: Record<string, string | string[]> | Headers;
  connection?: { remoteAddress?: string };
  socket?: { remoteAddress?: string };
}

/**
 * Extrai IP de forma segura
 */
export function extractSafeIP(request: RequestWithHeaders): string {
  const headers = request.headers;
  const forwarded =
    headers instanceof Headers ? headers.get('x-forwarded-for') : headers?.['x-forwarded-for'];
  const realIP = headers instanceof Headers ? headers.get('x-real-ip') : headers?.['x-real-ip'];
  const remoteAddress = request.connection?.remoteAddress || request.socket?.remoteAddress;

  const forwardedIP = Array.isArray(forwarded) ? forwarded[0] : forwarded;
  const realIPString = Array.isArray(realIP) ? realIP[0] : realIP;
  const ip = forwardedIP?.split(',')[0] || realIPString || remoteAddress || 'unknown';

  // Validar se é um IP válido
  const ipRegex =
    /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
  const ipv6Regex = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/;

  if (ipRegex.test(ip) || ipv6Regex.test(ip)) {
    return ip;
  }

  return 'unknown';
}

/**
 * Configurações de rate limiting para autenticação
 */
export const AUTH_RATE_LIMITS = {
  LOGIN_ATTEMPTS: {
    windowMs: 15 * 60 * 1000, // 15 minutos
    max: 5, // 5 tentativas por IP
  },
  OAUTH_REQUESTS: {
    windowMs: 5 * 60 * 1000, // 5 minutos
    max: 10, // 10 requests OAuth por IP
  },
  PASSWORD_RESET: {
    windowMs: 60 * 60 * 1000, // 1 hora
    max: 3, // 3 tentativas de reset por IP
  },
} as const;
