#!/usr/bin/env tsx

/**
 * Script de migração para criar assinaturas Free para usuários existentes
 * que não possuem registro na tabela Subscription
 *
 * Uso: npx tsx scripts/migrate-users-to-free-plan.ts
 */

import { config } from 'dotenv';
import { resolve } from 'path';
import { PrismaClient } from '@prisma/client';

// Carregar variáveis de ambiente
config({ path: resolve(process.cwd(), '.env.local') });

// Constantes locais para evitar dependência do Stripe
const PLANS = {
  FREE: 'free',
  PRO_MONTHLY: 'pro_monthly',
  PRO_ANNUAL: 'pro_annual',
};

const API_CALL_LIMITS = {
  [PLANS.FREE]: 50,
  [PLANS.PRO_MONTHLY]: 500,
  [PLANS.PRO_ANNUAL]: 1000,
};

const prisma = new PrismaClient();

interface MigrationStats {
  totalUsers: number;
  usersWithSubscription: number;
  usersWithoutSubscription: number;
  migratedUsers: number;
  errors: number;
  errorDetails: Array<{ userId: string; email: string; error: string }>;
}

/**
 * Executa a migração de usuários para plano Free
 */
async function migrateUsersToFreePlan(): Promise<MigrationStats> {
  const stats: MigrationStats = {
    totalUsers: 0,
    usersWithSubscription: 0,
    usersWithoutSubscription: 0,
    migratedUsers: 0,
    errors: 0,
    errorDetails: [],
  };

  try {
    console.log('🚀 Iniciando migração de usuários para plano Free...\n');

    // Buscar todos os usuários
    const allUsers = await prisma.user.findMany({
      select: {
        id: true,
        email: true,
        name: true,
        createdAt: true,
        subscriptions: {
          select: {
            id: true,
            plan: true,
            status: true,
          },
        },
      },
    });

    stats.totalUsers = allUsers.length;
    console.log(`📊 Total de usuários encontrados: ${stats.totalUsers}`);

    // Identificar usuários sem assinatura
    const usersWithoutSubscription = allUsers.filter(user => user.subscriptions.length === 0);
    const usersWithSubscription = allUsers.filter(user => user.subscriptions.length > 0);

    stats.usersWithSubscription = usersWithSubscription.length;
    stats.usersWithoutSubscription = usersWithoutSubscription.length;

    console.log(`✅ Usuários com assinatura: ${stats.usersWithSubscription}`);
    console.log(`⚠️  Usuários sem assinatura: ${stats.usersWithoutSubscription}`);

    if (stats.usersWithoutSubscription === 0) {
      console.log('\n🎉 Todos os usuários já possuem assinatura! Nenhuma migração necessária.');
      return stats;
    }

    console.log('\n📝 Usuários que serão migrados:');
    usersWithoutSubscription.forEach((user, index) => {
      console.log(`  ${index + 1}. ${user.email || 'Email não definido'} (ID: ${user.id})`);
    });

    // Confirmar migração
    console.log('\n⚡ Iniciando criação de assinaturas Free...');

    // Migrar usuários em lotes para melhor performance
    const batchSize = 10;
    for (let i = 0; i < usersWithoutSubscription.length; i += batchSize) {
      const batch = usersWithoutSubscription.slice(i, i + batchSize);

      console.log(
        `\n📦 Processando lote ${Math.floor(i / batchSize) + 1}/${Math.ceil(usersWithoutSubscription.length / batchSize)}...`
      );

      for (const user of batch) {
        try {
          // Verificar novamente se o usuário não possui assinatura (prevenção de race condition)
          const existingSubscription = await prisma.subscription.findFirst({
            where: { userId: user.id },
          });

          if (existingSubscription) {
            console.log(`  ⏭️  Usuário ${user.email} já possui assinatura, pulando...`);
            continue;
          }

          // Criar assinatura Free
          const freeSubscription = await prisma.subscription.create({
            data: {
              userId: user.id,
              plan: PLANS.FREE,
              status: 'active',
              apiCallsLimit: API_CALL_LIMITS[PLANS.FREE] || 50,
              apiCallsUsed: 0,
              currentPeriodStart: new Date(),
              cancelAtPeriodEnd: false,
              // Não definir currentPeriodEnd para plano Free (ilimitado)
            },
          });

          stats.migratedUsers++;
          console.log(`  ✅ Assinatura Free criada para ${user.email || user.id}:`);
          console.log(`     - Subscription ID: ${freeSubscription.id}`);
          console.log(`     - API Calls Limit: ${freeSubscription.apiCallsLimit}`);
        } catch (error) {
          stats.errors++;
          const errorMessage = error instanceof Error ? error.message : String(error);
          stats.errorDetails.push({
            userId: user.id,
            email: user.email || 'Email não definido',
            error: errorMessage,
          });

          console.error(`  ❌ Erro ao migrar usuário ${user.email || user.id}:`, errorMessage);
        }
      }

      // Pequena pausa entre lotes para não sobrecarregar o banco
      if (i + batchSize < usersWithoutSubscription.length) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    return stats;
  } catch (error) {
    console.error('💥 Erro crítico durante a migração:', error);
    throw error;
  }
}

/**
 * Valida a integridade dos dados após a migração
 */
async function validateMigration(): Promise<void> {
  console.log('\n🔍 Validando integridade dos dados após migração...');

  // Verificar se ainda existem usuários sem assinatura
  const usersWithoutSubscription = await prisma.user.count({
    where: {
      subscriptions: {
        none: {},
      },
    },
  });

  if (usersWithoutSubscription === 0) {
    console.log('✅ Validação bem-sucedida: Todos os usuários possuem assinatura!');
  } else {
    console.warn(`⚠️  Ainda existem ${usersWithoutSubscription} usuários sem assinatura.`);
  }

  // Verificar estatísticas de planos
  const planStats = await prisma.subscription.groupBy({
    by: ['plan'],
    _count: {
      plan: true,
    },
  });

  console.log('\n📊 Estatísticas de planos após migração:');
  planStats.forEach(stat => {
    console.log(`  - ${stat.plan}: ${stat._count.plan} usuários`);
  });
}

/**
 * Função principal
 */
async function main(): Promise<void> {
  try {
    const startTime = Date.now();

    // Executar migração
    const stats = await migrateUsersToFreePlan();

    // Validar resultados
    await validateMigration();

    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;

    // Relatório final
    console.log('\n' + '='.repeat(60));
    console.log('📋 RELATÓRIO FINAL DA MIGRAÇÃO');
    console.log('='.repeat(60));
    console.log(`⏱️  Tempo de execução: ${duration.toFixed(2)}s`);
    console.log(`👥 Total de usuários: ${stats.totalUsers}`);
    console.log(`✅ Usuários já com assinatura: ${stats.usersWithSubscription}`);
    console.log(`🆕 Usuários migrados: ${stats.migratedUsers}`);
    console.log(`❌ Erros: ${stats.errors}`);

    if (stats.errors > 0) {
      console.log('\n🚨 DETALHES DOS ERROS:');
      stats.errorDetails.forEach((error, index) => {
        console.log(`  ${index + 1}. ${error.email} (${error.userId}): ${error.error}`);
      });
    }

    if (stats.migratedUsers > 0) {
      console.log('\n🎉 Migração concluída com sucesso!');
      console.log(`   ${stats.migratedUsers} usuários agora possuem assinatura Free.`);
    }
  } catch (error) {
    console.error('\n💥 Falha na migração:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Executar script se chamado diretamente
if (require.main === module) {
  main().catch(console.error);
}

export { migrateUsersToFreePlan, validateMigration };
