/**
 * Sanitização de dados específicos para arquivos Excel
 * Módulo separado da sanitização geral para melhor organização
 */

/**
 * Função para sanitizar dados do Excel
 * @param sheetData Dados da planilha a serem sanitizados
 * @returns Objeto com dados sanitizados e relatório de segurança
 */
export function sanitizeExcelData(sheetData: unknown): {
  sanitizedData: unknown;
  securityReport: {
    hasDangerousFormulas: boolean;
    formulasRejected: number;
    details: Array<{
      rowIndex: number;
      columnName: string;
      reason: string;
    }>;
  };
} {
  // Cópia do objeto para não modificar o original
  const sanitizedData = JSON.parse(JSON.stringify(sheetData));

  // Inicializar relatório de segurança
  const securityReport = {
    hasDangerousFormulas: false,
    formulasRejected: 0,
    details: [] as Array<{
      rowIndex: number;
      columnName: string;
      reason: string;
    }>,
  };

  // Aqui seria implementada a lógica real de sanitização
  // Procurando por fórmulas perigosas, macros, etc.
  // Por enquanto, apenas retornamos os dados sem modificação

  return {
    sanitizedData,
    securityReport,
  };
}

/**
 * Verifica se uma fórmula contém comandos potencialmente perigosos
 * @param formula Fórmula para verificar
 * @returns true se a fórmula for perigosa
 */
function _hasDangerousFormula(formula: string): boolean {
  if (!formula) return false;

  // Lista de padrões perigosos em fórmulas Excel
  const dangerousPatterns = [
    /=.*EXEC\(/i,
    /=.*CMD\(/i,
    /=.*SHELL\(/i,
    /=.*RUN\(/i,
    /=.*CALL\(/i,
    /=.*SYSTEM\(/i,
    /=.*DDE\(/i,
    /=.*DDEEXEC\(/i,
    /=.*XLM\./i,
    /=.*HYPERLINK\(".*javascript:/i,
    /=.*MSExcel\./i,
    /=.*Application\./i,
    /=.*VBA\./i,
    /=.*IMPORTFROMWEB\(/i,
  ];

  return dangerousPatterns.some(pattern => pattern.test(formula));
}

/**
 * Sanitiza valores de texto para remover conteúdo potencialmente perigoso
 * @param value Valor para sanitizar
 * @returns Valor sanitizado
 */
function _sanitizeValue(value: string): string {
  if (!value || typeof value !== 'string') return value;

  // Remover tags HTML
  let sanitized = value.replace(/<[^>]*>/g, '');

  // Remover scripts
  sanitized = sanitized.replace(/javascript:/gi, 'blocked:');
  sanitized = sanitized.replace(/data:/gi, 'blocked:');

  // Remover caracteres de controle (usando método alternativo para evitar no-control-regex)
  const controlCharsRegex = new RegExp(`[\\u0000-\\u001F\\u007F]`, 'g');
  sanitized = sanitized.replace(controlCharsRegex, '');

  return sanitized;
}

// Also export as default for flexibility
export default sanitizeExcelData;
