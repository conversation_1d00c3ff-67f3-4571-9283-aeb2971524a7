'use client';

import { loadStripe, Stripe } from '@stripe/stripe-js';
import { useEffect, useState, createContext, useContext, ReactNode } from 'react';

import { getStripePublicKey } from '@/lib/stripe';

// Contexto do Stripe
interface StripeContextType {
  stripe: Stripe | null;
  isLoading: boolean;
  error: Error | null;
}

const StripeContext = createContext<StripeContextType>({
  stripe: null,
  isLoading: true,
  error: null,
});

// Hook para usar o Stripe em componentes
export const useStripe = () => useContext(StripeContext);

interface StripeProviderProps {
  children: ReactNode;
}

export default function StripeScriptProvider({ children }: StripeProviderProps) {
  const [stripe, setStripe] = useState<Stripe | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    // Carregar o Stripe apenas no lado do cliente
    if (typeof window === 'undefined') return;

    async function initStripe() {
      try {
        setIsLoading(true);
        setError(null);

        // Obter a chave pública do Stripe
        const publishableKey = getStripePublicKey();

        if (!publishableKey) {
          throw new Error(
            'Chave pública do Stripe não encontrada. Verifique a variável NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY'
          );
        }

        if (!publishableKey.startsWith('pk_')) {
          throw new Error('Chave pública do Stripe inválida. Deve começar com "pk_"');
        }

        // Carregar o Stripe com a chave pública
        const stripeInstance = await loadStripe(publishableKey);

        if (!stripeInstance) {
          throw new Error(
            'Falha ao carregar o Stripe. Possíveis causas: CSP bloqueando js.stripe.com, chave inválida, ou problemas de rede'
          );
        }

        setStripe(stripeInstance);
        setError(null);
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : 'Erro desconhecido ao carregar Stripe';
        setError(err instanceof Error ? err : new Error(errorMessage));
      } finally {
        setIsLoading(false);
      }
    }

    initStripe();
  }, []);

  // Mostrar erro se houver problema na inicialização
  if (error && !isLoading) {
    return (
      <StripeContext.Provider value={{ stripe: null, isLoading: false, error }}>
        <div className="rounded-lg border border-red-200 bg-red-50 p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">
                Erro ao carregar sistema de pagamento
              </h3>
              <div className="mt-2 text-sm text-red-700">
                <p>{error.message}</p>
                <p className="mt-1 text-xs">
                  Tente recarregar a página. Se o problema persistir, entre em contato com o
                  suporte.
                </p>
              </div>
            </div>
          </div>
        </div>
        {children}
      </StripeContext.Provider>
    );
  }

  return (
    <StripeContext.Provider value={{ stripe, isLoading, error }}>{children}</StripeContext.Provider>
  );
}
