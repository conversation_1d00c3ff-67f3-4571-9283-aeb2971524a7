/**
 * Monitoramento de Core Web Vitals e Performance
 */

import { onCLS, onINP, onFCP, onLCP, onTTFB } from 'web-vitals';
import * as Sentry from '@sentry/nextjs';

// Interface para métricas de performance
interface PerformanceMetric {
  name: string;
  value: number;
  rating: 'good' | 'needs-improvement' | 'poor';
  delta: number;
  id: string;
  navigationType: string;
}

// Thresholds para classificação das métricas
const THRESHOLDS = {
  CLS: { good: 0.1, poor: 0.25 },
  FID: { good: 100, poor: 300 },
  FCP: { good: 1800, poor: 3000 },
  LCP: { good: 2500, poor: 4000 },
  TTFB: { good: 800, poor: 1800 },
};

// Função para classificar a métrica
function getRating(name: string, value: number): 'good' | 'needs-improvement' | 'poor' {
  const threshold = THRESHOLDS[name as keyof typeof THRESHOLDS];
  if (!threshold) return 'good';

  if (value <= threshold.good) return 'good';
  if (value <= threshold.poor) return 'needs-improvement';
  return 'poor';
}

// Função para enviar métricas para o Sentry
function sendToSentry(metric: PerformanceMetric) {
  // Adicionar como breadcrumb
  Sentry.addBreadcrumb({
    message: `Web Vital: ${metric.name}`,
    level: metric.rating === 'poor' ? 'warning' : 'info',
    data: {
      value: metric.value,
      rating: metric.rating,
      delta: metric.delta,
      id: metric.id,
      navigationType: metric.navigationType,
    },
    category: 'web-vitals',
  });

  // Se a métrica for ruim, capturar como evento
  if (metric.rating === 'poor') {
    Sentry.captureMessage(`Poor Web Vital: ${metric.name} = ${metric.value}`, 'warning');
  }
}

// Função para enviar métricas para analytics customizado
function sendToAnalytics(metric: PerformanceMetric) {
  // Enviar para Google Analytics se disponível
  if (typeof window !== 'undefined' && (window as any).gtag) {
    (window as any).gtag('event', metric.name, {
      event_category: 'Web Vitals',
      event_label: metric.id,
      value: Math.round(metric.name === 'CLS' ? metric.value * 1000 : metric.value),
      custom_map: {
        metric_rating: metric.rating,
        metric_delta: metric.delta,
      },
    });
  }

  // Enviar para endpoint customizado
  if (process.env.NODE_ENV === 'production') {
    fetch('/api/analytics/vitals', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        name: metric.name,
        value: metric.value,
        rating: metric.rating,
        delta: metric.delta,
        id: metric.id,
        navigationType: metric.navigationType,
        url: window.location.href,
        userAgent: navigator.userAgent,
        timestamp: Date.now(),
      }),
    }).catch(error => {
      console.warn('Failed to send web vitals to analytics:', error);
    });
  }
}

// Função principal para processar métricas
function handleMetric(metric: any) {
  const performanceMetric: PerformanceMetric = {
    name: metric.name,
    value: metric.value,
    rating: getRating(metric.name, metric.value),
    delta: metric.delta,
    id: metric.id,
    navigationType: metric.navigationType || 'unknown',
  };

  // Log local em desenvolvimento
  if (process.env.NODE_ENV === 'development') {
    console.log('Web Vital:', performanceMetric);
  }

  // Enviar para Sentry
  sendToSentry(performanceMetric);

  // Enviar para analytics
  sendToAnalytics(performanceMetric);
}

// Função para inicializar o monitoramento de Web Vitals
export function initWebVitals() {
  if (typeof window === 'undefined') return;

  try {
    // Cumulative Layout Shift
    onCLS(handleMetric);

    // Interaction to Next Paint (substitui FID)
    onINP(handleMetric);

    // First Contentful Paint
    onFCP(handleMetric);

    // Largest Contentful Paint
    onLCP(handleMetric);

    // Time to First Byte
    onTTFB(handleMetric);

    console.log('Web Vitals monitoring initialized');
  } catch (error) {
    console.warn('Failed to initialize Web Vitals monitoring:', error);
  }
}

// Função para monitorar performance customizada
export function measurePerformance<T>(name: string, fn: () => T | Promise<T>): T | Promise<T> {
  const startTime = performance.now();

  const finish = (result: T) => {
    const duration = performance.now() - startTime;

    // Log da performance
    const metric = {
      name: `custom_${name}`,
      value: duration,
      rating: duration > 1000 ? 'poor' : duration > 500 ? 'needs-improvement' : 'good',
      delta: duration,
      id: `${name}_${Date.now()}`,
      navigationType: 'custom',
    } as PerformanceMetric;

    handleMetric(metric);

    return result;
  };

  try {
    const result = fn();

    if (result instanceof Promise) {
      return result.then(finish).catch(error => {
        finish(error);
        throw error;
      });
    }

    return finish(result);
  } catch (error) {
    finish(error as T);
    throw error;
  }
}

// Hook para React components
export function usePerformanceMonitoring() {
  return {
    measurePerformance,
    startMeasurement: (name: string) => {
      const startTime = performance.now();
      return {
        finish: () => {
          const duration = performance.now() - startTime;
          handleMetric({
            name: `component_${name}`,
            value: duration,
            rating: getRating('custom', duration),
            delta: duration,
            id: `${name}_${Date.now()}`,
            navigationType: 'component',
          });
        },
      };
    },
  };
}

// Monitoramento de recursos
export function monitorResourceLoading() {
  if (typeof window === 'undefined') return;

  // Monitorar carregamento de recursos
  const observer = new PerformanceObserver(list => {
    for (const entry of list.getEntries()) {
      if (entry.entryType === 'resource') {
        const resource = entry as PerformanceResourceTiming;

        // Apenas monitorar recursos importantes
        if (
          resource.name.includes('.js') ||
          resource.name.includes('.css') ||
          resource.name.includes('/api/')
        ) {
          const duration = resource.responseEnd - resource.requestStart;

          if (duration > 2000) {
            // Recursos que demoram mais de 2s
            Sentry.addBreadcrumb({
              message: `Slow resource loading: ${resource.name}`,
              level: 'warning',
              data: {
                duration,
                size: resource.transferSize,
                type: resource.initiatorType,
              },
              category: 'performance',
            });
          }
        }
      }
    }
  });

  try {
    observer.observe({ entryTypes: ['resource'] });
  } catch (error) {
    console.warn('Failed to observe resource loading:', error);
  }
}

// Exportar função de inicialização completa
export function initPerformanceMonitoring() {
  initWebVitals();
  monitorResourceLoading();

  // Monitorar erros de JavaScript
  window.addEventListener('error', event => {
    Sentry.captureException(event.error, {
      tags: {
        component: 'global-error-handler',
      },
      extra: {
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
      },
    });
  });

  // Monitorar promises rejeitadas
  window.addEventListener('unhandledrejection', event => {
    Sentry.captureException(event.reason, {
      tags: {
        component: 'unhandled-promise-rejection',
      },
    });
  });
}

export default {
  initWebVitals,
  initPerformanceMonitoring,
  measurePerformance,
  usePerformanceMonitoring,
  monitorResourceLoading,
};
