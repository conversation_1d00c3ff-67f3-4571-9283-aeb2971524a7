'use client';

import { Check, X, CreditCard, Calendar, Zap, AlertCircle, Sparkles, Shield } from 'lucide-react';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { useState, useEffect } from 'react';
import { toast } from 'sonner';

import { useStripe } from '@/components/billing/stripe-script-provider';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { PLANS } from '@/lib/stripe';
import { cn } from '@/lib/utils';

interface PricingFeature {
  text: string;
  free: boolean | string;
  pro_monthly: boolean | string;
  pro_annual: boolean | string;
}

const features: PricingFeature[] = [
  {
    text: 'Número de planilhas',
    free: '5 planilhas',
    pro_monthly: 'Ilimitadas',
    pro_annual: 'Ilimitadas',
  },
  {
    text: 'Células por planilha',
    free: '1.000 células',
    pro_monthly: '50.000 células',
    pro_annual: 'Ilimitadas',
  },
  {
    text: 'Comandos de IA',
    free: 'Básicos',
    pro_monthly: 'Todos',
    pro_annual: 'Todos',
  },
  {
    text: 'Chamadas de API por mês',
    free: '50',
    pro_monthly: '500',
    pro_annual: '1.000',
  },
  {
    text: 'Limite de complexidade',
    free: '1.000 tokens',
    pro_monthly: '4.000 tokens',
    pro_annual: '8.000 tokens',
  },
  {
    text: 'Gráficos',
    free: '1 por planilha',
    pro_monthly: 'Ilimitados',
    pro_annual: 'Ilimitados',
  },
  {
    text: 'Análise preditiva com IA',
    free: false,
    pro_monthly: true,
    pro_annual: true,
  },
  {
    text: 'Exportação para Excel',
    free: true,
    pro_monthly: true,
    pro_annual: true,
  },
  {
    text: 'Chamadas excedentes',
    free: false,
    pro_monthly: 'R$ 0,10 cada',
    pro_annual: 'R$ 0,08 cada',
  },
  {
    text: 'Suporte prioritário',
    free: false,
    pro_monthly: true,
    pro_annual: true,
  },
];

export default function PricingPage() {
  const [billingPeriod, setBillingPeriod] = useState<'monthly' | 'annual'>('monthly');
  const [isLoading, setIsLoading] = useState<{ [key: string]: boolean }>({});
  const [currentSubscription, setCurrentSubscription] = useState<string | null>(null);
  const [isSubscriptionLoading, setIsSubscriptionLoading] = useState(true);

  const router = useRouter();
  const { status } = useSession();
  const searchParams = useSearchParams();
  const { stripe, isLoading: isStripeLoading, error: stripeError } = useStripe();

  const errorParam = searchParams?.get('error');
  const checkoutStatus = searchParams?.get('checkout');
  const plan = searchParams?.get('plan');
  const action = searchParams?.get('action');

  // Mostrar erro do Stripe se ocorrer
  useEffect(() => {
    if (stripeError) {
      console.error('Erro ao carregar Stripe:', stripeError);
      toast.error(
        'Erro ao carregar o sistema de pagamento. Por favor, tente novamente mais tarde.'
      );
    }
  }, [stripeError]);

  // Verificar assinatura atual do usuário ao carregar e quando o status de autenticação mudar
  useEffect(() => {
    const checkSubscription = async () => {
      if (status === 'authenticated') {
        try {
          setIsSubscriptionLoading(true);
          const response = await fetch('/api/user/subscription');

          if (response.ok) {
            const data = await response.json();
            setCurrentSubscription(data.subscription?.plan || PLANS.FREE);
          } else {
            // Se houver erro, assumimos plano gratuito
            setCurrentSubscription(PLANS.FREE);
          }
        } catch (error) {
          console.error('Erro ao verificar assinatura:', error);
          setCurrentSubscription(PLANS.FREE);
        } finally {
          setIsSubscriptionLoading(false);
        }
      } else if (status === 'unauthenticated') {
        setCurrentSubscription(null);
        setIsSubscriptionLoading(false);
      }
    };

    checkSubscription();
  }, [status]);

  // Verificar ações pendentes nos parâmetros da URL após login
  useEffect(() => {
    // Não fazer nada se o usuário não estiver autenticado
    if (status !== 'authenticated') return;

    // Verificar ações pendentes de assinatura
    if (action === 'trial' && !isLoading.trial) {
      // Iniciar trial
      handleTrialSelect();
    } else if (action === 'subscribe' && plan && !isLoading[plan]) {
      // Assinar plano específico
      handlePlanSelect(plan);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [status, action, plan, isLoading]);

  // Mostrar mensagens com base nos parâmetros da URL
  useEffect(() => {
    // Mensagem de erro de trial
    if (errorParam === 'trial-already-used') {
      toast.error(
        'Você já utilizou seu período de avaliação gratuito. Por favor, escolha um plano para continuar.'
      );
    }

    // Mensagem após checkout
    if (checkoutStatus === 'success') {
      toast.success('Assinatura concluída com sucesso! Aproveite o Excel Copilot Pro.');
    } else if (checkoutStatus === 'cancelled') {
      toast.info('Checkout cancelado. Você pode assinar a qualquer momento.');
    }
  }, [errorParam, checkoutStatus]);

  // Verificar se usuário já possui este plano
  const userHasPlan = (plan: string) => {
    return currentSubscription === plan;
  };

  const handlePlanSelect = async (plan: string) => {
    // Não fazer nada se usuário já tiver este plano
    if (userHasPlan(plan)) {
      toast.info('Você já está inscrito neste plano.');
      return;
    }

    // Se for o plano grátis e o usuário estiver logado
    if (plan === PLANS.FREE && status === 'authenticated') {
      if (currentSubscription !== PLANS.FREE) {
        toast.info('Você será redirecionado para cancelar sua assinatura atual.');
        router.push('/dashboard/account?action=downgrade');
      } else {
        toast.success('Você já está no plano gratuito!');
      }
      return;
    }

    // Verificar autenticação
    if (status !== 'authenticated') {
      // Redirecionar para login e salvar a intenção de assinar nos parâmetros
      const callbackUrl = `/pricing?action=subscribe&plan=${plan}`;
      router.push(`/auth/signin?callbackUrl=${encodeURIComponent(callbackUrl)}`);
      return;
    }

    // Verificar se o Stripe está carregado
    if (isStripeLoading || !stripe) {
      toast.error(
        'O sistema de pagamento está sendo carregado. Por favor, aguarde um momento e tente novamente.'
      );
      return;
    }

    // Iniciar checkout para usuário autenticado
    setIsLoading(prev => ({ ...prev, [plan]: true }));

    try {
      // Iniciar checkout do Stripe
      const response = await fetch('/api/checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          plan,
          successUrl: `${window.location.origin}/dashboard?checkout=success`,
          cancelUrl: `${window.location.origin}/pricing?checkout=cancelled`,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Erro ao iniciar checkout');
      }

      const data = await response.json();

      // Validar URL de checkout
      if (!data.url || !data.url.startsWith('https://')) {
        throw new Error('URL de checkout inválida');
      }

      // Redirecionar para a página de checkout do Stripe
      window.location.href = data.url;
    } catch (error) {
      console.error('Erro ao processar assinatura:', error);
      toast.error('Erro ao iniciar o checkout. Por favor, tente novamente mais tarde.');
    } finally {
      setIsLoading(prev => ({ ...prev, [plan]: false }));
    }
  };

  const handleTrialSelect = async () => {
    // Verificar autenticação
    if (status !== 'authenticated') {
      // Redirecionar para login e salvar a intenção de iniciar trial nos parâmetros
      const callbackUrl = `/pricing?action=trial`;
      router.push(`/auth/signin?callbackUrl=${encodeURIComponent(callbackUrl)}`);
      return;
    }

    setIsLoading(prev => ({ ...prev, trial: true }));

    try {
      // Usar a navegação direta para a rota GET do trial
      router.push('/api/checkout/trial');
    } catch (error) {
      console.error('Erro ao iniciar período de avaliação:', error);
      toast.error('Erro ao iniciar o período de avaliação. Por favor, tente novamente.');
      setIsLoading(prev => ({ ...prev, trial: false }));
    }
  };

  const getPlanButtonText = (plan: string) => {
    if (isLoading[plan]) {
      return 'Processando...';
    }

    if (userHasPlan(plan)) {
      return 'Plano Atual';
    }

    if (plan === PLANS.FREE) {
      return 'Começar Grátis';
    }

    return 'Assinar Pro';
  };

  return (
    <div className="container max-w-5xl mx-auto px-3 py-8">
      {/* Header */}
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold tracking-tight mb-2">Planos e Preços</h1>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          Escolha o plano ideal para suas necessidades e desbloqueie todo o potencial do Excel
          Copilot.
        </p>
      </div>

      {/* Status do usuário - mostrar apenas quando carregando */}
      {isSubscriptionLoading && (
        <div className="flex justify-center mb-4">
          <div className="inline-flex items-center gap-2 px-3 py-1 bg-muted rounded-md text-sm">
            <div className="h-3 w-3 rounded-full bg-primary animate-pulse"></div>
            <span>Verificando assinatura...</span>
          </div>
        </div>
      )}

      {/* Trial Banner */}
      {(!currentSubscription || currentSubscription === PLANS.FREE) && (
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/30 dark:to-indigo-950/30 border border-blue-100 dark:border-blue-800/50 rounded-lg p-3 mb-8 flex flex-col md:flex-row items-center justify-between gap-3">
          <div className="flex items-center gap-3">
            <div className="bg-blue-100 dark:bg-blue-900/50 p-2 rounded-full flex-shrink-0">
              <Sparkles className="h-5 w-5 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <h3 className="font-medium">Experimente grátis por 7 dias</h3>
              <p className="text-sm text-muted-foreground">
                Acesso a todos os recursos do plano Pro sem compromisso.
              </p>
            </div>
          </div>
          <Button
            onClick={() => handleTrialSelect()}
            className="bg-blue-600 hover:bg-blue-700 text-white w-full md:w-auto"
            disabled={isLoading.trial || isSubscriptionLoading}
            size="default"
          >
            {isLoading.trial ? 'Processando...' : 'Iniciar Avaliação'}
          </Button>
        </div>
      )}

      {/* Alerta de erro */}
      {errorParam === 'trial-already-used' && (
        <Alert variant="destructive" className="mb-6 text-sm">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Período de avaliação já utilizado</AlertTitle>
          <AlertDescription>
            Você já utilizou seu período de avaliação gratuito. Por favor, escolha um plano para
            continuar.
          </AlertDescription>
        </Alert>
      )}

      {/* Alerta de sucesso após checkout */}
      {checkoutStatus === 'success' && (
        <Alert
          variant="default"
          className="mb-6 text-sm bg-green-50 dark:bg-green-950/20 border-green-200 dark:border-green-900/50"
        >
          <Check className="h-4 w-4 text-green-600 dark:text-green-400" />
          <AlertTitle>Assinatura ativada</AlertTitle>
          <AlertDescription>
            Sua assinatura foi ativada com sucesso. Aproveite todos os recursos do Excel Copilot
            Pro!
          </AlertDescription>
        </Alert>
      )}

      {/* Seletor de período */}
      <div className="flex justify-center mb-6">
        <div className="bg-muted inline-flex rounded-lg p-1 shadow-sm">
          <button
            onClick={() => setBillingPeriod('monthly')}
            className={cn(
              'px-5 py-1.5 rounded-md flex items-center gap-1.5 transition-colors text-sm',
              billingPeriod === 'monthly'
                ? 'bg-background text-foreground shadow'
                : 'text-muted-foreground hover:text-foreground'
            )}
          >
            <Calendar className="h-3.5 w-3.5" />
            <span>Mensal</span>
          </button>
          <button
            onClick={() => setBillingPeriod('annual')}
            className={cn(
              'px-5 py-1.5 rounded-md flex items-center gap-1.5 transition-colors text-sm relative',
              billingPeriod === 'annual'
                ? 'bg-background text-foreground shadow'
                : 'text-muted-foreground hover:text-foreground'
            )}
          >
            <Zap className="h-3.5 w-3.5" />
            <span>Anual</span>
            <span className="absolute -top-2 -right-2 bg-primary text-primary-foreground text-xs px-1.5 py-0.5 rounded-full text-[10px]">
              -17%
            </span>
          </button>
        </div>
      </div>

      {/* Cards de preços */}
      <div className="grid md:grid-cols-12 gap-4">
        {/* Plano Free */}
        <Card
          className={cn(
            'md:col-span-5 border-muted-foreground/20 shadow-sm',
            currentSubscription === PLANS.FREE && 'border-primary border-2'
          )}
        >
          <CardHeader className="pb-3 pt-4 px-4">
            <div className="flex items-center mb-1">
              <Shield className="h-4 w-4 text-muted-foreground mr-1.5" />
              <CardTitle className="text-xl">Free</CardTitle>
              {currentSubscription === PLANS.FREE && (
                <span className="ml-auto text-xs font-medium px-1.5 py-0.5 bg-primary/10 text-primary rounded-full">
                  Atual
                </span>
              )}
            </div>
            <CardDescription className="text-sm">Para uso pessoal e experimentação</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4 px-4 pb-4">
            <div>
              <div className="text-3xl font-bold">R$ 0</div>
              <p className="text-sm text-muted-foreground">Gratuito para sempre</p>
            </div>
            <Button
              onClick={() => handlePlanSelect(PLANS.FREE)}
              variant={userHasPlan(PLANS.FREE) ? 'outline' : 'default'}
              className="w-full py-1.5 text-sm"
              size="sm"
              disabled={isSubscriptionLoading || userHasPlan(PLANS.FREE)}
            >
              {getPlanButtonText(PLANS.FREE)}
            </Button>
            <div>
              <h4 className="text-xs font-medium mb-2">Inclui:</h4>
              <ul className="space-y-1.5 text-sm">
                {features.map(feature => (
                  <li key={feature.text} className="flex items-start gap-2">
                    {feature.free ? (
                      <>
                        <Check className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                        <span className="text-sm">
                          {typeof feature.free === 'string' ? feature.free : feature.text}
                        </span>
                      </>
                    ) : (
                      <>
                        <X className="h-4 w-4 text-muted-foreground mt-0.5 flex-shrink-0" />
                        <span className="text-sm text-muted-foreground">{feature.text}</span>
                      </>
                    )}
                  </li>
                ))}
              </ul>
            </div>
          </CardContent>
        </Card>

        {/* Plano Pro */}
        <Card
          className={cn(
            'md:col-span-7 border-primary/50 relative shadow-sm',
            (currentSubscription === PLANS.PRO_MONTHLY ||
              currentSubscription === PLANS.PRO_ANNUAL) &&
              'border-primary border-2'
          )}
        >
          <div className="absolute top-0 right-0 bg-primary text-primary-foreground px-2 py-0.5 rounded-bl-lg rounded-tr-md">
            <span className="text-[10px] font-semibold">RECOMENDADO</span>
          </div>
          <CardHeader className="pb-3 pt-4 px-4">
            <div className="flex items-center mb-1">
              <Shield className="h-4 w-4 text-primary mr-1.5" />
              <CardTitle className="text-xl">Excel Copilot Pro</CardTitle>
              {(currentSubscription === PLANS.PRO_MONTHLY ||
                currentSubscription === PLANS.PRO_ANNUAL) && (
                <span className="ml-auto text-xs font-medium px-1.5 py-0.5 bg-primary/10 text-primary rounded-full">
                  Atual
                </span>
              )}
            </div>
            <CardDescription className="text-sm">Para profissionais e empresas</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4 px-4 pb-4">
            <div className="grid md:grid-cols-2 gap-4">
              <div>
                <div className="text-3xl font-bold">
                  {billingPeriod === 'monthly' ? 'R$ 20' : 'R$ 16,67'}
                  <span className="text-sm font-normal text-muted-foreground ml-1">/mês</span>
                </div>
                <p className="text-xs text-muted-foreground">
                  {billingPeriod === 'monthly'
                    ? 'Cobrado mensalmente'
                    : 'Cobrado R$ 200 anualmente'}
                </p>
              </div>
              <div className="flex flex-col justify-center">
                <Button
                  onClick={() =>
                    handlePlanSelect(
                      billingPeriod === 'monthly' ? PLANS.PRO_MONTHLY : PLANS.PRO_ANNUAL
                    )
                  }
                  className="w-full py-1.5 text-sm"
                  size="sm"
                  disabled={
                    isLoading[PLANS.PRO_MONTHLY] ||
                    isLoading[PLANS.PRO_ANNUAL] ||
                    isSubscriptionLoading ||
                    (billingPeriod === 'monthly' && userHasPlan(PLANS.PRO_MONTHLY)) ||
                    (billingPeriod === 'annual' && userHasPlan(PLANS.PRO_ANNUAL))
                  }
                  variant={
                    (billingPeriod === 'monthly' && userHasPlan(PLANS.PRO_MONTHLY)) ||
                    (billingPeriod === 'annual' && userHasPlan(PLANS.PRO_ANNUAL))
                      ? 'outline'
                      : 'default'
                  }
                >
                  {isLoading[PLANS.PRO_MONTHLY] || isLoading[PLANS.PRO_ANNUAL] ? (
                    <>Processando...</>
                  ) : (billingPeriod === 'monthly' && userHasPlan(PLANS.PRO_MONTHLY)) ||
                    (billingPeriod === 'annual' && userHasPlan(PLANS.PRO_ANNUAL)) ? (
                    <>Plano Atual</>
                  ) : (
                    <>
                      <CreditCard className="mr-1.5 h-4 w-4" />
                      Assinar Pro
                    </>
                  )}
                </Button>
              </div>
            </div>

            <div>
              <h4 className="text-xs font-medium mb-2">Tudo do Free, mais:</h4>
              <ul className="grid md:grid-cols-2 gap-x-4 gap-y-1.5 text-sm">
                {features.map(feature => {
                  const proValue =
                    billingPeriod === 'monthly' ? feature.pro_monthly : feature.pro_annual;

                  if (!proValue) return null;

                  return (
                    <li key={feature.text} className="flex items-start gap-2">
                      <Check className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                      <span className="text-sm">
                        {typeof proValue === 'string' ? proValue : feature.text}
                      </span>
                    </li>
                  );
                })}
              </ul>
            </div>

            <div className="mt-2 pt-2 border-t border-border">
              <div className="flex items-start gap-2 bg-blue-50/50 dark:bg-blue-950/20 p-2 rounded-lg">
                <Sparkles className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                <div className="text-sm">
                  <span className="font-medium">Bônus:</span> Acesso antecipado a novos recursos
                </div>
              </div>
            </div>
          </CardContent>
          <CardFooter className="bg-muted/50 px-4 py-2">
            <div className="flex items-center justify-between w-full text-xs">
              <div className="flex items-center gap-1.5">
                <AlertCircle className="h-3.5 w-3.5 text-muted-foreground" />
                <span>Cancele a qualquer momento</span>
              </div>
              {billingPeriod === 'annual' && (
                <div className="text-primary font-medium">Economize 17%</div>
              )}
            </div>
          </CardFooter>
        </Card>
      </div>

      {/* FAQ */}
      <div className="mt-12">
        <h2 className="text-xl font-bold mb-6 text-center">Perguntas Frequentes</h2>
        <div className="grid md:grid-cols-2 gap-x-6 gap-y-4 max-w-4xl mx-auto">
          <div className="space-y-1 bg-muted/30 p-3 rounded-lg">
            <h3 className="font-medium text-sm">Posso mudar de plano depois?</h3>
            <p className="text-xs text-muted-foreground">
              Sim, você pode atualizar ou fazer downgrade do seu plano a qualquer momento através do
              seu painel de conta.
            </p>
          </div>
          <div className="space-y-1 bg-muted/30 p-3 rounded-lg">
            <h3 className="font-medium text-sm">Existe um período de avaliação gratuita?</h3>
            <p className="text-xs text-muted-foreground">
              Sim! Oferecemos 7 dias de avaliação gratuita do plano Pro. Você pode cancelar a
              qualquer momento durante esse período.
            </p>
          </div>
          <div className="space-y-1 bg-muted/30 p-3 rounded-lg">
            <h3 className="font-medium text-sm">Como funciona o limite de chamadas de API?</h3>
            <p className="text-xs text-muted-foreground">
              Cada plano tem um número máximo de solicitações que você pode fazer à IA por mês.
              Estas são resetadas no início de cada ciclo de faturamento.
            </p>
          </div>
          <div className="space-y-1 bg-muted/30 p-3 rounded-lg">
            <h3 className="font-medium text-sm">O que acontece se eu exceder o limite?</h3>
            <p className="text-xs text-muted-foreground">
              No plano Free, você precisará esperar até o próximo mês. No Pro, será cobrado um valor
              adicional por cada chamada excedente.
            </p>
          </div>
          <div className="space-y-1 bg-muted/30 p-3 rounded-lg">
            <h3 className="font-medium text-sm">Preciso fornecer cartão para o trial?</h3>
            <p className="text-xs text-muted-foreground">
              Sim, pedimos os dados do cartão para ativar o período de avaliação, mas você não será
              cobrado se cancelar antes do término dos 7 dias.
            </p>
          </div>
          <div className="space-y-1 bg-muted/30 p-3 rounded-lg">
            <h3 className="font-medium text-sm">Como eu cancelo antes do fim do trial?</h3>
            <p className="text-xs text-muted-foreground">
              No seu painel da conta, você terá acesso ao portal de gerenciamento onde pode cancelar
              facilmente sua assinatura.
            </p>
          </div>
        </div>

        <div className="mt-6 text-center">
          <Link href="/contact">
            <Button variant="outline" size="sm" className="gap-1.5 text-sm">
              <AlertCircle className="h-3.5 w-3.5" />
              Ainda tem perguntas? Entre em contato
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
}
