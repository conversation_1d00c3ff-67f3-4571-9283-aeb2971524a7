/**
 * Inicialização da API e validação de ambiente
 */
import { ENV } from '@/config/unified-environment';
import { logger } from '@/lib/logger';

/**
 * Valida o ambiente de execução e inicializa serviços necessários
 * @returns Status da inicialização
 */
export async function initializeApi() {
  try {
    // Validação do ambiente
    const envValidation = ENV.validate();

    if (!envValidation.valid && ENV.IS_PRODUCTION) {
      const missingVars = envValidation.missing.join(', ');
      logger.error(
        `Inicialização falhou: Variáveis de ambiente obrigatórias não configuradas: ${missingVars}`
      );
      return {
        success: false,
        message: 'Configuração de ambiente incompleta',
        details: envValidation,
      };
    }

    if (envValidation.missing.length > 0 && !ENV.IS_PRODUCTION) {
      logger.warn(
        `Inicialização com warnings: Variáveis de ambiente ausentes: ${envValidation.missing.join(', ')}`
      );
    }

    // Inicialização de serviços críticos - usar initLogger para silenciar em produção
    const { initLogger } = await import('@/lib/logger');
    initLogger.info(`API inicializada em ambiente: ${ENV.NODE_ENV}`);

    // Configuração de timeouts globais
    if (ENV.TIMEOUTS.API_CALL) {
      initLogger.info(`API timeout configurado: ${ENV.TIMEOUTS.API_CALL}ms`);
    }

    // Feature Flags
    if (ENV.FEATURES.USE_MOCK_AI) {
      initLogger.info('Modo de IA simulada (mock) ativado');
    }

    if (ENV.FEATURES.SKIP_AUTH_PROVIDERS) {
      // Warnings de segurança sempre aparecem, mesmo em produção
      logger.warn('AVISO: Autenticação simplificada ativada (não use em produção)');
    }

    return {
      success: true,
      message: 'API inicializada com sucesso',
      environment: ENV.NODE_ENV,
    };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
    logger.error(`Falha na inicialização da API: ${errorMessage}`, { error });

    return {
      success: false,
      message: 'Falha na inicialização da API',
      error: errorMessage,
    };
  }
}
