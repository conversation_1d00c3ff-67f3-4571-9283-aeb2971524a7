/**
 * <PERSON>ck para MSW (Mock Service Worker) usado nos testes
 * Fornece implementações simplificadas de http, setupServer e outras funcionalidades do MSW
 */

// Interface de resposta
export interface MockResponse {
  status: number;
  body: any;
}

// Interface de handler
export interface MockHandler {
  (req: any, res: any, ctx: any): MockResponse;
}

// Tipos de métodos HTTP
export type HttpMethod = 'get' | 'post' | 'put' | 'delete' | 'patch';

// Tipo para requisição HTTP
export interface RequestHandler {
  url: string;
  handler: (params: any) => Promise<any> | any;
}

// Setup server - aceita os handlers e retorna uma interface compatível com MSW
export const setupServer = (...handlers: any[]) => {
  return {
    listen: () => {},
    close: () => {},
    resetHandlers: () => {},
    use: (...newHandlers: any[]) => {},
    events: {
      on: () => {},
      removeListener: () => {},
    },
    handlers,
  };
};

// Implementação básica de http
export const http = {
  get: (url: string, handler?: any) => ({ url, method: 'get', handler }),
  post: (url: string, handler?: any) => ({ url, method: 'post', handler }),
  put: (url: string, handler?: any) => ({ url, method: 'put', handler }),
  delete: (url: string, handler?: any) => ({ url, method: 'delete', handler }),
  patch: (url: string, handler?: any) => ({ url, method: 'patch', handler }),
};

// Classe para simular respostas HTTP
export class HttpResponse {
  static json(data: any, init?: { status?: number }) {
    return { body: data, status: init?.status || 200 };
  }

  static text(content: string, init?: { status?: number }) {
    return { body: content, status: init?.status || 200 };
  }

  static error(init?: { status?: number }) {
    return { status: init?.status || 500 };
  }

  constructor(body: any, init?: { status?: number }) {
    return { body, status: init?.status || 200 };
  }
}

// Re-exportar como CommonJS module
module.exports = {
  setupServer,
  http,
  HttpResponse,
};
