/**
 * 🚨 SISTEMA DE MONITORAMENTO DE AUTENTICAÇÃO
 *
 * Monitora e alerta sobre falhas de autenticação OAuth
 */

import { logger } from './logger';

/**
 * Interface para o Sentry Scope
 */
interface SentryScope {
  setTag: (key: string, value: string) => void;
  setLevel: (level: string) => void;
  setContext: (key: string, context: Record<string, unknown>) => void;
}

/**
 * Tipos de eventos de autenticação
 */
export enum AuthEventType {
  LOGIN_SUCCESS = 'LOGIN_SUCCESS',
  LOGIN_FAILURE = 'LOGIN_FAILURE',
  OAUTH_ERROR = 'OAUTH_ERROR',
  SESSION_EXPIRED = 'SESSION_EXPIRED',
  SUSPICIOUS_ACTIVITY = 'SUSPICIOUS_ACTIVITY',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
  INVALID_TOKEN = 'INVALID_TOKEN',
  PROVIDER_ERROR = 'PROVIDER_ERROR',
}

/**
 * Severidade do alerta
 */
export enum AlertSeverity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL',
}

/**
 * Evento de autenticação
 */
export interface AuthEvent {
  type: AuthEventType;
  timestamp: number;
  userId?: string;
  email?: string;
  ip: string;
  userAgent: string;
  provider?: string;
  error?: string;
  metadata?: Record<string, string | number | boolean | undefined>;
}

/**
 * Configuração de alertas
 */
export interface AlertConfig {
  enabled: boolean;
  thresholds: {
    failuresPerMinute: number;
    failuresPerHour: number;
    suspiciousActivityWindow: number; // ms
  };
  notifications: {
    email: boolean;
    slack: boolean;
    sentry: boolean;
  };
}

/**
 * Monitor de autenticação
 */
class AuthMonitor {
  private events: AuthEvent[] = [];
  private readonly MAX_EVENTS = 10000; // Manter apenas os últimos 10k eventos
  private readonly CLEANUP_INTERVAL = 300000; // 5 minutos

  private config: AlertConfig = {
    enabled: process.env.NODE_ENV === 'production',
    thresholds: {
      failuresPerMinute: 10,
      failuresPerHour: 100,
      suspiciousActivityWindow: 300000, // 5 minutos
    },
    notifications: {
      email: false, // Implementar quando necessário
      slack: false, // Implementar quando necessário
      sentry: true,
    },
  };

  constructor() {
    // Limpeza periódica de eventos antigos
    setInterval(() => {
      this.cleanupOldEvents();
    }, this.CLEANUP_INTERVAL);
  }

  /**
   * Registra um evento de autenticação
   */
  recordEvent(event: Omit<AuthEvent, 'timestamp'>): void {
    const fullEvent: AuthEvent = {
      ...event,
      timestamp: Date.now(),
    };

    this.events.push(fullEvent);

    // Log estruturado
    logger.info('Auth event recorded', {
      type: event.type,
      userId: event.userId,
      email: event.email,
      ip: event.ip,
      provider: event.provider,
      error: event.error,
    });

    // Verifica se precisa disparar alertas
    this.checkForAlerts(fullEvent);

    // Limita o tamanho do array
    if (this.events.length > this.MAX_EVENTS) {
      this.events = this.events.slice(-this.MAX_EVENTS);
    }
  }

  /**
   * Verifica se deve disparar alertas
   */
  private checkForAlerts(event: AuthEvent): void {
    if (!this.config.enabled) return;

    const now = Date.now();
    const oneMinuteAgo = now - 60000;
    const oneHourAgo = now - 3600000;

    // Conta falhas recentes
    const recentFailures = this.events.filter(
      e =>
        e.timestamp >= oneMinuteAgo &&
        (e.type === AuthEventType.LOGIN_FAILURE ||
          e.type === AuthEventType.OAUTH_ERROR ||
          e.type === AuthEventType.PROVIDER_ERROR)
    );

    const hourlyFailures = this.events.filter(
      e =>
        e.timestamp >= oneHourAgo &&
        (e.type === AuthEventType.LOGIN_FAILURE ||
          e.type === AuthEventType.OAUTH_ERROR ||
          e.type === AuthEventType.PROVIDER_ERROR)
    );

    // Alerta por muitas falhas por minuto
    if (recentFailures.length >= this.config.thresholds.failuresPerMinute) {
      this.sendAlert({
        severity: AlertSeverity.HIGH,
        title: 'Alto número de falhas de autenticação',
        message: `${recentFailures.length} falhas de autenticação no último minuto`,
        details: {
          failuresPerMinute: recentFailures.length,
          threshold: this.config.thresholds.failuresPerMinute,
          recentEvents: recentFailures.slice(-5), // Últimas 5 falhas
        },
      });
    }

    // Alerta por muitas falhas por hora
    if (hourlyFailures.length >= this.config.thresholds.failuresPerHour) {
      this.sendAlert({
        severity: AlertSeverity.MEDIUM,
        title: 'Padrão de falhas de autenticação detectado',
        message: `${hourlyFailures.length} falhas de autenticação na última hora`,
        details: {
          failuresPerHour: hourlyFailures.length,
          threshold: this.config.thresholds.failuresPerHour,
        },
      });
    }

    // Detecta atividade suspeita (mesmo IP, múltiplas falhas)
    if (event.type === AuthEventType.LOGIN_FAILURE) {
      this.checkSuspiciousActivity(event);
    }

    // Alerta crítico para erros de provider
    if (event.type === AuthEventType.PROVIDER_ERROR) {
      this.sendAlert({
        severity: AlertSeverity.CRITICAL,
        title: 'Erro no provedor de autenticação',
        message: `Erro no provedor ${event.provider}: ${event.error}`,
        details: {
          provider: event.provider,
          error: event.error,
          timestamp: event.timestamp,
        },
      });
    }
  }

  /**
   * Detecta atividade suspeita
   */
  private checkSuspiciousActivity(event: AuthEvent): void {
    const windowStart = event.timestamp - this.config.thresholds.suspiciousActivityWindow;

    // Conta falhas do mesmo IP na janela de tempo
    const ipFailures = this.events.filter(
      e => e.ip === event.ip && e.timestamp >= windowStart && e.type === AuthEventType.LOGIN_FAILURE
    );

    // Se muitas falhas do mesmo IP
    if (ipFailures.length >= 5) {
      this.sendAlert({
        severity: AlertSeverity.HIGH,
        title: 'Atividade suspeita detectada',
        message: `${ipFailures.length} tentativas de login falharam do IP ${event.ip}`,
        details: {
          ip: event.ip,
          failures: ipFailures.length,
          timeWindow: this.config.thresholds.suspiciousActivityWindow / 1000 / 60, // em minutos
          userAgent: event.userAgent,
        },
      });

      // Marca como atividade suspeita
      this.recordEvent({
        type: AuthEventType.SUSPICIOUS_ACTIVITY,
        ip: event.ip,
        userAgent: event.userAgent,
        metadata: {
          failureCount: ipFailures.length,
          timeWindow: this.config.thresholds.suspiciousActivityWindow,
        },
      });
    }
  }

  /**
   * Envia alerta
   */
  private sendAlert(alert: {
    severity: AlertSeverity;
    title: string;
    message: string;
    details: Record<string, unknown>;
  }): void {
    // Log do alerta
    logger.warn('Auth security alert', {
      severity: alert.severity,
      title: alert.title,
      message: alert.message,
      details: alert.details,
    });

    // Envio para Sentry se habilitado
    if (this.config.notifications.sentry) {
      this.sendToSentry(alert);
    }

    // TODO: Implementar notificações por email/Slack quando necessário
  }

  /**
   * Envia alerta para Sentry
   */
  private sendToSentry(alert: {
    severity: AlertSeverity;
    title: string;
    message: string;
    details: Record<string, unknown>;
  }): void {
    try {
      // Se Sentry estiver configurado
      if (typeof window !== 'undefined' && 'Sentry' in window) {
        const windowWithSentry = window as typeof window & {
          Sentry: {
            withScope: (callback: (scope: SentryScope) => void) => void;
            captureMessage: (message: string, level: string) => void;
          };
        };

        windowWithSentry.Sentry.withScope((scope: SentryScope) => {
          scope.setTag('component', 'auth-monitor');
          scope.setLevel(this.severityToSentryLevel(alert.severity));
          scope.setContext('alert_details', alert.details);

          windowWithSentry.Sentry.captureMessage(alert.title, 'warning');
        });
      }
    } catch (error) {
      logger.error('Failed to send alert to Sentry', { error });
    }
  }

  /**
   * Converte severidade para nível do Sentry
   */
  private severityToSentryLevel(severity: AlertSeverity): string {
    switch (severity) {
      case AlertSeverity.LOW:
        return 'info';
      case AlertSeverity.MEDIUM:
        return 'warning';
      case AlertSeverity.HIGH:
        return 'error';
      case AlertSeverity.CRITICAL:
        return 'fatal';
      default:
        return 'warning';
    }
  }

  /**
   * Remove eventos antigos
   */
  private cleanupOldEvents(): void {
    const oneDayAgo = Date.now() - 86400000; // 24 horas
    const initialLength = this.events.length;

    this.events = this.events.filter(event => event.timestamp >= oneDayAgo);

    const removed = initialLength - this.events.length;
    if (removed > 0) {
      logger.debug(`Cleaned up ${removed} old auth events`);
    }
  }

  /**
   * Obtém estatísticas de autenticação
   */
  getStats(timeWindow: number = 3600000): {
    totalEvents: number;
    successfulLogins: number;
    failedLogins: number;
    oauthErrors: number;
    suspiciousActivity: number;
    topFailureReasons: Array<{ reason: string; count: number }>;
    topFailureIPs: Array<{ ip: string; count: number }>;
  } {
    const windowStart = Date.now() - timeWindow;
    const recentEvents = this.events.filter(e => e.timestamp >= windowStart);

    const successfulLogins = recentEvents.filter(
      e => e.type === AuthEventType.LOGIN_SUCCESS
    ).length;
    const failedLogins = recentEvents.filter(e => e.type === AuthEventType.LOGIN_FAILURE).length;
    const oauthErrors = recentEvents.filter(e => e.type === AuthEventType.OAUTH_ERROR).length;
    const suspiciousActivity = recentEvents.filter(
      e => e.type === AuthEventType.SUSPICIOUS_ACTIVITY
    ).length;

    // Top failure reasons
    const failureReasons = new Map<string, number>();
    recentEvents
      .filter(e => e.error)
      .forEach(e => {
        const reason = e.error || 'Unknown';
        failureReasons.set(reason, (failureReasons.get(reason) || 0) + 1);
      });

    // Top failure IPs
    const failureIPs = new Map<string, number>();
    recentEvents
      .filter(e => e.type === AuthEventType.LOGIN_FAILURE)
      .forEach(e => {
        failureIPs.set(e.ip, (failureIPs.get(e.ip) || 0) + 1);
      });

    return {
      totalEvents: recentEvents.length,
      successfulLogins,
      failedLogins,
      oauthErrors,
      suspiciousActivity,
      topFailureReasons: Array.from(failureReasons.entries())
        .map(([reason, count]) => ({ reason, count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 5),
      topFailureIPs: Array.from(failureIPs.entries())
        .map(([ip, count]) => ({ ip, count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 5),
    };
  }

  /**
   * Atualiza configuração
   */
  updateConfig(newConfig: Partial<AlertConfig>): void {
    this.config = { ...this.config, ...newConfig };
    logger.info('Auth monitor configuration updated', { config: this.config });
  }
}

// Instância global do monitor
export const authMonitor = new AuthMonitor();

/**
 * Helpers para registrar eventos comuns
 */
export const recordAuthEvent = {
  loginSuccess: (
    userId: string,
    email: string,
    ip: string,
    userAgent: string,
    provider: string
  ) => {
    authMonitor.recordEvent({
      type: AuthEventType.LOGIN_SUCCESS,
      userId,
      email,
      ip,
      userAgent,
      provider,
    });
  },

  loginFailure: (email: string, ip: string, userAgent: string, provider: string, error: string) => {
    authMonitor.recordEvent({
      type: AuthEventType.LOGIN_FAILURE,
      email,
      ip,
      userAgent,
      provider,
      error,
    });
  },

  oauthError: (ip: string, userAgent: string, provider: string, error: string) => {
    authMonitor.recordEvent({
      type: AuthEventType.OAUTH_ERROR,
      ip,
      userAgent,
      provider,
      error,
    });
  },

  sessionExpired: (userId: string, ip: string, userAgent: string) => {
    authMonitor.recordEvent({
      type: AuthEventType.SESSION_EXPIRED,
      userId,
      ip,
      userAgent,
    });
  },

  rateLimitExceeded: (ip: string, userAgent: string) => {
    authMonitor.recordEvent({
      type: AuthEventType.RATE_LIMIT_EXCEEDED,
      ip,
      userAgent,
    });
  },

  invalidToken: (ip: string, userAgent: string, error: string) => {
    authMonitor.recordEvent({
      type: AuthEventType.INVALID_TOKEN,
      ip,
      userAgent,
      error,
    });
  },

  providerError: (provider: string, ip: string, userAgent: string, error: string) => {
    authMonitor.recordEvent({
      type: AuthEventType.PROVIDER_ERROR,
      provider,
      ip,
      userAgent,
      error,
    });
  },
};
