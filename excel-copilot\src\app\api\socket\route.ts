import { Server as NetServer } from 'http';

import { NextRequest, NextResponse } from 'next/server';
import { Server as SocketIOServer, Socket } from 'socket.io';

import { ActiveCollaborator } from '@/lib/collaboration/store';
import { logger } from '@/lib/logger';
import { prisma } from '@/server/db/client';

// Forçar o modo dinâmico para essa rota
export const dynamic = 'force-dynamic';

// Definição do tipo para handshake.auth
interface SocketAuth {
  workbookId: string;
  userId: string;
  userName: string;
  userEmail?: string;
}

// Store para manter dados em memória
class CollaborationStore {
  private static instance: CollaborationStore;
  private activeCollaborators: Map<string, ActiveCollaborator[]> = new Map();
  private userSockets: Map<string, string> = new Map(); // userId -> socketId
  private socketUsers: Map<string, string> = new Map(); // socketId -> userId
  private workbookSockets: Map<string, Set<string>> = new Map(); // workbookId -> Set<socketId>

  private constructor() {}

  public static getInstance(): CollaborationStore {
    if (!CollaborationStore.instance) {
      CollaborationStore.instance = new CollaborationStore();
    }
    return CollaborationStore.instance;
  }

  // Adiciona um colaborador ativo
  public addCollaborator(workbookId: string, collaborator: ActiveCollaborator): void {
    // Obter lista atual ou criar nova
    const collaborators = this.activeCollaborators.get(workbookId) || [];

    // Verificar se já existe
    const existingIndex = collaborators.findIndex(c => c.id === collaborator.id);
    if (existingIndex >= 0) {
      // Atualizar existente
      collaborators[existingIndex] = collaborator;
    } else {
      // Adicionar novo
      collaborators.push(collaborator);
    }

    // Atualizar maps
    this.activeCollaborators.set(workbookId, collaborators);
    this.userSockets.set(collaborator.id, collaborator.socket);
    this.socketUsers.set(collaborator.socket, collaborator.id);

    // Adicionar à lista de sockets da planilha
    const workbookSocketsSet = this.workbookSockets.get(workbookId) || new Set();
    workbookSocketsSet.add(collaborator.socket);
    this.workbookSockets.set(workbookId, workbookSocketsSet);
  }

  // Remove um colaborador
  public removeCollaborator(socketId: string): string[] {
    const userId = this.socketUsers.get(socketId);
    if (!userId) return [];

    const affectedWorkbooks: string[] = [];

    // Remover de todas as planilhas
    this.activeCollaborators.forEach((collaborators, workbookId) => {
      const index = collaborators.findIndex(c => c.id === userId);
      if (index >= 0) {
        collaborators.splice(index, 1);
        this.activeCollaborators.set(workbookId, collaborators);
        affectedWorkbooks.push(workbookId);

        // Remover da lista de sockets da planilha
        const workbookSocketsSet = this.workbookSockets.get(workbookId);
        if (workbookSocketsSet) {
          workbookSocketsSet.delete(socketId);
          if (workbookSocketsSet.size === 0) {
            this.workbookSockets.delete(workbookId);
          } else {
            this.workbookSockets.set(workbookId, workbookSocketsSet);
          }
        }
      }
    });

    // Remover dos maps
    this.userSockets.delete(userId);
    this.socketUsers.delete(socketId);

    return affectedWorkbooks;
  }

  // Obtém colaboradores de uma planilha
  public getCollaborators(workbookId: string): ActiveCollaborator[] {
    return this.activeCollaborators.get(workbookId) || [];
  }

  // Atualiza posição do cursor
  public updateCollaboratorPosition(
    socketId: string,
    position: { row: number; col: number }
  ): { userId: string; workbookIds: string[] } | null {
    const userId = this.socketUsers.get(socketId);
    if (!userId) return null;

    const updatedWorkbooks: string[] = [];

    // Atualizar em todas as planilhas
    this.activeCollaborators.forEach((collaborators, workbookId) => {
      const collaborator = collaborators.find(c => c.id === userId);
      if (collaborator) {
        collaborator.position = position;
        collaborator.lastActive = new Date();
        collaborator.status = 'active';
        updatedWorkbooks.push(workbookId);
      }
    });

    return { userId, workbookIds: updatedWorkbooks };
  }

  // Obtém sockets de uma planilha
  public getWorkbookSockets(workbookId: string): string[] {
    const sockets = this.workbookSockets.get(workbookId);
    return sockets ? Array.from(sockets) : [];
  }
}

// Variável para armazenar a instância do socket.io
let io: SocketIOServer | null = null;

// Handler para rota de API
export async function GET(req: NextRequest, res: NextResponse) {
  try {
    // Verificar se o servidor já está inicializado
    if (!io) {
      // Obter o NetServer subjacente com tipo correto
      const resWithSocket = res as unknown as { socket: { server: NetServer } };
      const server: NetServer = resWithSocket.socket.server;

      // Inicializar o Socket.IO
      io = new SocketIOServer(server, {
        path: '/api/socket',
        cors: {
          origin: process.env.AUTH_NEXTAUTH_URL || '*',
          methods: ['GET', 'POST'],
          credentials: true,
        },
      });

      const store = CollaborationStore.getInstance();

      // Configurar manipuladores de eventos
      io.on('connection', async (socket: Socket) => {
        // Obter informações do usuário e planilha
        const auth = socket.handshake.auth as SocketAuth;
        const { workbookId, userId, userName, userEmail } = auth;

        if (!workbookId || !userId) {
          socket.emit('error', 'Informações de autenticação incompletas');
          socket.disconnect();
          return;
        }

        // Verificar permissão para acessar a planilha
        try {
          // Usando o modelo de compartilhamento correto
          const workbook = await prisma.workbook.findFirst({
            where: {
              id: workbookId,
              OR: [
                { userId },
                {
                  shares: {
                    some: {
                      sharedWithUserId: userId,
                    },
                  },
                },
              ],
            },
          });

          if (!workbook) {
            socket.emit('error', 'Sem permissão para acessar esta planilha');
            socket.disconnect();
            return;
          }
        } catch (error) {
          logger.error('Erro ao verificar permissão de planilha:', error);
          socket.emit('error', 'Erro ao verificar permissão');
          socket.disconnect();
          return;
        }

        logger.info(`Usuário ${userName} (${userId}) conectado à planilha ${workbookId}`);

        // Juntar-se à sala da planilha
        socket.join(`workbook:${workbookId}`);

        // Adicionar colaborador à lista
        const collaborator: ActiveCollaborator = {
          id: userId,
          name: userName || 'Usuário',
          email: userEmail || '',
          socket: socket.id,
          status: 'active',
          lastActive: new Date(),
        };

        store.addCollaborator(workbookId, collaborator);

        // Notificar outros usuários
        socket.to(`workbook:${workbookId}`).emit('collaborator_joined', {
          id: userId,
          name: userName,
          status: 'active',
        });

        // Enviar lista de colaboradores atuais para o usuário
        const collaborators = store.getCollaborators(workbookId);
        socket.emit('collaborators', collaborators);

        // Manipular posição do cursor
        socket.on('cursor_position', (position: { row: number; col: number }) => {
          const update = store.updateCollaboratorPosition(socket.id, position);
          if (update) {
            const { userId, workbookIds } = update;
            workbookIds.forEach(wbId => {
              socket.to(`workbook:${wbId}`).emit('cursor_position', {
                userId,
                position,
                timestamp: Date.now(),
              });
            });
          }
        });

        // Manipular alterações em células
        socket.on('cell_changed', async (data: Record<string, unknown>) => {
          // Processar alteração de célula
          const eventData = {
            ...data,
            userId,
            userName,
            timestamp: Date.now(),
          };

          // Transmitir para outros usuários
          socket.to(`workbook:${workbookId}`).emit('cell_changed', eventData);

          // TODO: Persistir alterações no banco de dados se necessário
        });

        // Manipular desconexão
        socket.on('disconnect', () => {
          // Remover colaborador e notificar outros usuários
          const affectedWorkbooks = store.removeCollaborator(socket.id);

          affectedWorkbooks.forEach(wbId => {
            socket.to(`workbook:${wbId}`).emit('collaborator_left', userId);
          });

          logger.info(`Usuário ${userName} (${userId}) desconectado`);
        });
      });
    }

    return new NextResponse('Socket.IO server is running', { status: 200 });
  } catch (error) {
    logger.error('Erro ao inicializar Socket.IO', error);
    return new NextResponse('Failed to start Socket.IO server', { status: 500 });
  }
}
