# Dependências e diretórios de build
node_modules/
.next/
out/
build/
dist/
coverage/

# Arquivos de configuração
next.config.js
postcss.config.js
tailwind.config.js
fix-lint.js

# Arquivos gerados
prisma/schema.prisma
prisma/migrations/
public/

# Arquivos de teste e exemplos
**/__tests__/*
**/*.test.ts
**/*.test.tsx
**/*.test.js
**/*.test.jsx
**/*.spec.ts
**/*.spec.tsx
**/*.spec.js
**/*.spec.jsx
**/__mocks__/*

# Scripts de utilitários
scripts/
fix-*.js
create-env.js
jest.setup.js
jest.config.js
jest.integration.config.js
jest.env.js

# Desktop bridge
desktop-bridge/

# Monitoring
monitoring/

# Temporary files
temp/

# Documentation
docs/
*.md

# Environment files
.env*
env.example

# Database files
*.db
*.sqlite
*.sqlite3

# Logs
logs/
*.log

# Build artifacts
public/sw.js
public/sw.js.map
public/workbox-*.js
public/workbox-*.js.map

# Prisma seed files
prisma/seed.js
prisma/seed.ts

# Configuration files
babel.config.js.disabled
playwright.config.ts
tailwind.config.ts