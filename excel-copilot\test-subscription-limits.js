#!/usr/bin/env node

/**
 * Script de Auditoria Técnica - Sistema de Privilégios Excel Copilot
 * Verifica funcionalidades reais de limitação por plano
 */

import fs from 'fs';
import path from 'path';

console.log('🔍 AUDITORIA TÉCNICA - SISTEMA DE PRIVILÉGIOS EXCEL COPILOT');
console.log('===========================================================\n');

// ============================================================================
// 1. VERIFICAÇÃO DE FUNCIONALIDADE REAL
// ============================================================================

console.log('📋 1. VERIFICAÇÃO DE FUNCIONALIDADE REAL');
console.log('==========================================\n');

// Verificar se as funções de limitação existem e estão implementadas
const limitsFile = 'src/lib/subscription-limits.ts';
if (fs.existsSync(limitsFile)) {
  const content = fs.readFileSync(limitsFile, 'utf8');

  console.log('✅ Arquivo subscription-limits.ts encontrado');

  // Verificar funções críticas
  const criticalFunctions = [
    'canCreateWorkbook',
    'canAddCells',
    'canAddChart',
    'canUseAdvancedAI',
    'getUserSubscriptionPlan',
  ];

  criticalFunctions.forEach(func => {
    if (content.includes(`export async function ${func}`)) {
      console.log(`  ✅ ${func} - Implementada`);
    } else {
      console.log(`  ❌ ${func} - NÃO ENCONTRADA`);
    }
  });

  // Verificar constantes de limites
  if (content.includes('PLAN_LIMITS')) {
    console.log('  ✅ PLAN_LIMITS - Constantes definidas');

    // Extrair limites para análise
    const limitsMatch = content.match(/export const PLAN_LIMITS = \{([\s\S]*?)\};/);
    if (limitsMatch) {
      console.log('  📊 Limites por plano detectados:');

      if (content.includes('MAX_WORKBOOKS')) console.log('    - MAX_WORKBOOKS ✅');
      if (content.includes('MAX_CELLS')) console.log('    - MAX_CELLS ✅');
      if (content.includes('MAX_CHARTS')) console.log('    - MAX_CHARTS ✅');
      if (content.includes('ADVANCED_AI_COMMANDS')) console.log('    - ADVANCED_AI_COMMANDS ✅');
      if (content.includes('RATE_LIMITS')) console.log('    - RATE_LIMITS ✅');
    }
  } else {
    console.log('  ❌ PLAN_LIMITS - Constantes não encontradas');
  }
} else {
  console.log('❌ Arquivo subscription-limits.ts NÃO ENCONTRADO');
}

// ============================================================================
// 2. ANÁLISE DE SEGURANÇA E PREVENÇÃO DE BYPASS
// ============================================================================

console.log('\n🔒 2. ANÁLISE DE SEGURANÇA E PREVENÇÃO DE BYPASS');
console.log('=================================================\n');

// Verificar APIs que implementam verificação de planos
const apiRoutes = [
  'src/app/api/workbook/save/route.ts',
  'src/app/api/chat/route.ts',
  'src/app/api/workbooks/route.ts',
];

let securityScore = 0;
let totalChecks = 0;

apiRoutes.forEach(route => {
  if (fs.existsSync(route)) {
    const content = fs.readFileSync(route, 'utf8');
    console.log(`📁 ${path.basename(route)}`);

    totalChecks += 4;

    // Verificar se usa verificação server-side
    if (
      content.includes('canCreateWorkbook') ||
      content.includes('canUseAdvancedAI') ||
      content.includes('canAddChart')
    ) {
      console.log('  ✅ Verificação server-side implementada');
      securityScore++;
    } else {
      console.log('  ⚠️  Verificação server-side não detectada');
    }

    // Verificar autenticação
    if (content.includes('getServerSession') || content.includes('session?.user')) {
      console.log('  ✅ Verificação de autenticação presente');
      securityScore++;
    } else {
      console.log('  ❌ Verificação de autenticação ausente');
    }

    // Verificar validação de entrada
    if (content.includes('await req.json()') && content.includes('if (!')) {
      console.log('  ✅ Validação de entrada implementada');
      securityScore++;
    } else {
      console.log('  ⚠️  Validação de entrada limitada');
    }

    // Verificar tratamento de erro
    if (content.includes('try {') && content.includes('catch')) {
      console.log('  ✅ Tratamento de erro implementado');
      securityScore++;
    } else {
      console.log('  ⚠️  Tratamento de erro limitado');
    }

    console.log('');
  }
});

// ============================================================================
// 3. SISTEMA DE CONTAGEM E MÉTRICAS
// ============================================================================

console.log('📊 3. SISTEMA DE CONTAGEM E MÉTRICAS');
console.log('====================================\n');

// Verificar se existe sistema de contagem
const prismaSchema = 'prisma/schema.prisma';
if (fs.existsSync(prismaSchema)) {
  const content = fs.readFileSync(prismaSchema, 'utf8');
  console.log('✅ Schema Prisma encontrado');

  // Verificar modelos relacionados a assinaturas
  if (content.includes('model Subscription')) {
    console.log('  ✅ Modelo Subscription definido');

    if (content.includes('apiCallsUsed')) {
      console.log('    - apiCallsUsed: Contador de API calls ✅');
    }
    if (content.includes('apiCallsLimit')) {
      console.log('    - apiCallsLimit: Limite de API calls ✅');
    }
    if (content.includes('plan')) {
      console.log('    - plan: Tipo de plano ✅');
    }
  }

  if (content.includes('model Workbook')) {
    console.log('  ✅ Modelo Workbook definido');
  }
} else {
  console.log('❌ Schema Prisma não encontrado');
}

// ============================================================================
// 4. VERIFICAÇÃO DE MIDDLEWARE E RATE LIMITING
// ============================================================================

console.log('\n⚡ 4. VERIFICAÇÃO DE MIDDLEWARE E RATE LIMITING');
console.log('===============================================\n');

const middlewareFile = 'src/lib/middleware/plan-based-rate-limiter.ts';
if (fs.existsSync(middlewareFile)) {
  const content = fs.readFileSync(middlewareFile, 'utf8');
  console.log('✅ Middleware de rate limiting encontrado');

  // Verificar configurações por plano
  if (content.includes('RATE_LIMIT_CONFIG')) {
    console.log('  ✅ Configuração de rate limiting por plano');

    if (content.includes('requestsPerMinute')) {
      console.log('    - Rate limiting por minuto ✅');
    }
    if (content.includes('requestsPerHour')) {
      console.log('    - Rate limiting por hora ✅');
    }
    if (content.includes('requestsPerDay')) {
      console.log('    - Rate limiting por dia ✅');
    }
  }

  // Verificar cache de rate limiting
  if (content.includes('rateLimitCache')) {
    console.log('  ✅ Cache de rate limiting implementado');
  }
} else {
  console.log('❌ Middleware de rate limiting não encontrado');
}

// ============================================================================
// 5. RELATÓRIO DE SEGURANÇA
// ============================================================================

console.log('\n🛡️  5. RELATÓRIO DE SEGURANÇA');
console.log('==============================\n');

const securityPercentage = Math.round((securityScore / totalChecks) * 100);

console.log(`📈 Score de Segurança: ${securityScore}/${totalChecks} (${securityPercentage}%)`);

if (securityPercentage >= 80) {
  console.log('🟢 NÍVEL DE SEGURANÇA: ALTO');
} else if (securityPercentage >= 60) {
  console.log('🟡 NÍVEL DE SEGURANÇA: MÉDIO');
} else {
  console.log('🔴 NÍVEL DE SEGURANÇA: BAIXO');
}

console.log('\n📋 VULNERABILIDADES IDENTIFICADAS:');

// Verificar possíveis vulnerabilidades
const vulnerabilities = [];

// Verificar se existe bypass via frontend
const frontendFiles = ['src/components/workbook/SpreadsheetEditor.tsx', 'src/app/pricing/page.tsx'];

frontendFiles.forEach(file => {
  if (fs.existsSync(file)) {
    const content = fs.readFileSync(file, 'utf8');

    // Verificar se há verificações apenas no frontend
    if (content.includes('userHasPlan') && !content.includes('server')) {
      vulnerabilities.push({
        file: path.basename(file),
        type: 'Client-side only validation',
        risk: 'MÉDIO',
        description: 'Verificação de plano apenas no frontend pode ser contornada',
      });
    }
  }
});

if (vulnerabilities.length === 0) {
  console.log('✅ Nenhuma vulnerabilidade crítica identificada');
} else {
  vulnerabilities.forEach((vuln, index) => {
    console.log(`\n${index + 1}. ${vuln.file}`);
    console.log(`   Tipo: ${vuln.type}`);
    console.log(`   Risco: ${vuln.risk}`);
    console.log(`   Descrição: ${vuln.description}`);
  });
}

console.log('\n✅ AUDITORIA TÉCNICA CONCLUÍDA');
console.log('==============================');
