{"mcpServers": {"vercel": {"command": "node", "args": ["src/lib/vercel-integration.js"], "env": {"VERCEL_API_TOKEN": "seu-token-vercel", "VERCEL_PROJECT_ID": "seu-project-id", "VERCEL_TEAM_ID": "seu-team-id"}, "description": "Vercel MCP Integration para deploy e monitoramento", "status": "active", "endpoints": ["/api/vercel/status", "/api/vercel/deployments", "/api/vercel/projects"]}, "linear": {"command": "node", "args": ["src/lib/linear-integration.js"], "env": {"LINEAR_API_KEY": "lin_api_seu-token-linear"}, "description": "Linear MCP Integration para gestão de issues e projetos", "status": "implemented", "endpoints": ["/api/linear/status", "/api/linear/issues", "/api/linear/teams"]}, "github": {"command": "node", "args": ["src/lib/github-integration.js"], "env": {"GITHUB_TOKEN": "seu-token-github", "GITHUB_OWNER": "seu-usuario-ou-organizacao", "GITHUB_REPO": "nome-do-repositorio"}, "description": "GitHub MCP Integration para repositórios, issues, PRs e CI/CD", "status": "implemented", "endpoints": ["/api/github/status", "/api/github/repositories", "/api/github/issues", "/api/github/workflows"]}, "supabase": {"command": "node", "args": ["src/lib/supabase-integration.js"], "env": {"SUPABASE_URL": "sua-url-supabase", "SUPABASE_ANON_KEY": "sua-chave-anonima", "SUPABASE_SERVICE_ROLE_KEY": "sua-chave-service-role"}, "description": "Supabase MCP Integration para banco de dados e storage", "status": "active", "endpoints": ["/api/supabase/status", "/api/supabase/tables", "/api/supabase/storage"]}, "stripe": {"command": "node", "args": ["src/lib/stripe-integration.js"], "env": {"STRIPE_SECRET_KEY": "sk_test_seu-token-stripe", "STRIPE_WEBHOOK_SECRET": "whsec_seu-webhook-secret"}, "description": "Stripe MCP Integration para monitoramento de pagamentos, assinaturas e receita", "status": "active", "endpoints": ["/api/stripe/status", "/api/stripe/customers", "/api/stripe/subscriptions", "/api/stripe/payments"]}}, "configuration": {"project": {"name": "Excel Copilot", "version": "0.1.0", "description": "SaaS de planilhas colaborativas com IA integrada"}, "integrations": {"active": ["vercel", "linear", "github", "supabase", "stripe"], "implemented": ["vercel", "linear", "github", "supabase", "stripe"], "real_tokens": ["linear", "github"], "production_ready": true, "planned": []}, "healthChecks": {"enabled": true, "interval": 300000, "timeout": 10000, "endpoints": ["/api/health", "/api/vercel/status", "/api/linear/status", "/api/github/status", "/api/supabase/status", "/api/stripe/status"]}, "monitoring": {"enabled": true, "services": ["vercel", "linear", "github", "supabase", "stripe", "database", "ai-service"], "alerts": {"email": "<EMAIL>", "webhook": "https://hooks.slack.com/services/..."}}}, "documentation": {"vercel": "VERCEL_MCP_INTEGRATION.md", "linear": "LINEAR_MCP_INTEGRATION.md", "github": "docs/GITHUB_MCP_INTEGRATION.md", "supabase": "docs/SUPABASE_MCP_INTEGRATION.md", "stripe": "docs/STRIPE_MCP_INTEGRATION.md"}, "development": {"setup": {"requirements": ["Node.js 18+", "Next.js 14+", "TypeScript 5+", "Prisma ORM"], "environment": {"required": ["DATABASE_URL", "NEXTAUTH_SECRET", "NEXTAUTH_URL", "LINEAR_API_KEY", "VERCEL_API_TOKEN"], "optional": ["GITHUB_PERSONAL_ACCESS_TOKEN", "SUPABASE_URL", "SUPABASE_ANON_KEY"]}}, "testing": {"healthChecks": "npm run test:health", "integrations": "npm run test:mcp", "e2e": "npm run test:e2e"}}, "production": {"deployment": {"platform": "Vercel", "database": "Supabase PostgreSQL", "monitoring": "Built-in health checks + Vercel Analytics"}, "security": {"apiKeys": "Environment variables only", "rateLimiting": "Enabled for all MCP endpoints", "cors": "Configured for production domains"}}}