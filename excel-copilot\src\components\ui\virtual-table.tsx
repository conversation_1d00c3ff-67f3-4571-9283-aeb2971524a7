'use client';

import { useEffect, useRef, useState } from 'react';

import { cn } from '@/lib/utils';

export interface VirtualTableProps {
  data: any[][];
  headers?: string[];
  rowHeight?: number;
  visibleRows?: number;
  className?: string;
  cellClassName?: string;
  headerClassName?: string;
  onRowClick?: (rowIndex: number) => void;
}

// Interface para item virtual (sem dependência externa)
interface VirtualItem {
  index: number;
  start: number;
  size: number;
  key: string | number;
}

/**
 * Tabela com virtualização para renderizar eficientemente grandes conjuntos de dados
 * Implementação manual de virtualização otimizada para performance
 */
export function VirtualTable({
  data,
  headers,
  rowHeight = 40,
  visibleRows = 15,
  className,
  cellClassName,
  headerClassName,
  onRowClick,
}: VirtualTableProps) {
  const parentRef = useRef<HTMLDivElement>(null);
  const [scrollTop, setScrollTop] = useState(0);
  const [_containerHeight, setContainerHeight] = useState(0);

  // Total de linhas no conjunto de dados
  const totalRows = data.length;
  // Altura total da tabela se todas as linhas fossem renderizadas
  const fullHeight = totalRows * rowHeight;
  // Altura do container de visualização
  const viewportHeight = visibleRows * rowHeight;
  // Número total de colunas com base na primeira linha (ou no cabeçalho, se disponível)
  const totalColumns = headers?.length || data[0]?.length || 0;

  // Calcular quais linhas devem ser renderizadas com base na posição de rolagem
  const startIndex = Math.max(0, Math.floor(scrollTop / rowHeight) - 2); // 2 linhas extras antes
  const endIndex = Math.min(totalRows, Math.ceil((scrollTop + viewportHeight) / rowHeight) + 2); // 2 linhas extras depois

  // Criar itens virtuais
  const virtualRows: VirtualItem[] = [];
  for (let index = startIndex; index < endIndex; index++) {
    virtualRows.push({
      index,
      start: index * rowHeight,
      size: rowHeight,
      key: index,
    });
  }

  // Manipulador de eventos de rolagem
  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(e.currentTarget.scrollTop);
  };

  // Atualizar a altura do contêiner quando o componente montar
  useEffect(() => {
    if (parentRef.current) {
      setContainerHeight(parentRef.current.clientHeight);

      // Capturar o ref atual em uma variável para uso no cleanup
      const currentParentRef = parentRef.current;

      // Adicionar resistência de rolagem para melhor performance
      const resizeObserver = new ResizeObserver(() => {
        if (currentParentRef) {
          setContainerHeight(currentParentRef.clientHeight);
        }
      });

      resizeObserver.observe(currentParentRef);

      return () => {
        resizeObserver.unobserve(currentParentRef);
      };
    }
  }, []);

  return (
    <div
      ref={parentRef}
      className={cn('border rounded-md overflow-auto', className)}
      style={{ height: `${viewportHeight}px` }}
      onScroll={handleScroll}
      role="region"
      aria-label="Tabela de dados com rolagem virtual"
      tabIndex={0}
    >
      {/* Container com altura total para possibilitar scrolling */}
      <div
        style={{
          height: `${fullHeight}px`,
          width: '100%',
          position: 'relative',
        }}
      >
        <table className="w-full">
          {headers && (
            <thead className="sticky top-0 bg-muted z-10">
              <tr>
                {headers.map((header, i) => (
                  <th
                    key={i}
                    className={cn(
                      'px-4 py-2 text-left text-sm font-medium text-foreground border-b',
                      headerClassName
                    )}
                    scope="col"
                    id={`header-${i}`}
                  >
                    {header}
                  </th>
                ))}
              </tr>
            </thead>
          )}
          <tbody>
            {virtualRows.map((virtualRow: VirtualItem) => {
              const rowIndex = virtualRow.index;
              const row = data[rowIndex];

              if (!row) return null;

              return (
                <tr
                  key={virtualRow.key}
                  className={cn(
                    'hover:bg-accent/50 transition-colors',
                    onRowClick && 'cursor-pointer'
                  )}
                  onClick={() => onRowClick?.(rowIndex)}
                  aria-rowindex={rowIndex + 1}
                  style={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    width: '100%',
                    height: `${virtualRow.size}px`,
                    transform: `translateY(${virtualRow.start}px)`,
                  }}
                >
                  {Array.from({ length: totalColumns }).map((_, colIdx) => (
                    <td
                      key={colIdx}
                      className={cn(
                        'px-4 border-b h-10 text-sm text-foreground',
                        colIdx === 0 && 'font-medium',
                        cellClassName
                      )}
                      aria-colindex={colIdx + 1}
                      headers={headers ? `header-${colIdx}` : undefined}
                    >
                      {row[colIdx]}
                    </td>
                  ))}
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
    </div>
  );
}
