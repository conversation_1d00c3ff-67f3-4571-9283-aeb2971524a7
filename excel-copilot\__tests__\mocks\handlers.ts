/**
 * Handlers para o servidor mock
 * Define os interceptadores para as solicitações HTTP
 */

import { http, HttpResponse } from './msw-mock';

// Mock data
const users = [
  { id: '1', name: 'Test User', email: '<EMAIL>' },
  { id: '2', name: 'Admin User', email: '<EMAIL>' },
];

const workbooks = [
  { id: '1', name: 'Sales Report', userId: '1', sheets: [] },
  { id: '2', name: 'Budget Planner', userId: '1', sheets: [] },
];

// Array de handlers padrão
const handlers = [
  // Health check básico
  http.get('/api/health', () => {
    return HttpResponse.json({
      status: 'healthy',
      version: '1.0.0',
      services: {
        database: { connected: true },
        auth: { configured: true },
      },
      timestamp: new Date().toISOString(),
    });
  }),

  // Simular API de autenticação
  http.post('/api/auth/callback/credentials', () => {
    return HttpResponse.json({
      user: { id: '1', name: 'Test User', email: '<EMAIL>' },
      expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
    });
  }),

  // Simular API de session
  http.get('/api/auth/session', () => {
    return HttpResponse.json({
      user: { id: '1', name: 'Test User', email: '<EMAIL>' },
      expires: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
    });
  }),

  // Simular API de workbooks
  http.get('/api/workbooks', () => {
    return HttpResponse.json(workbooks);
  }),

  // Simular API de usuários
  http.get('/api/users', () => {
    return HttpResponse.json(users);
  }),

  // API genérica para testes
  http.get('/api/test', () => {
    return HttpResponse.json({ data: 'Test data', message: 'Success' });
  }),
];

module.exports = handlers;
