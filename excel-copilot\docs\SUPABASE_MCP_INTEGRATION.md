# 🗄️ Supabase MCP Integration - Excel Copilot

Integração completa com Supabase via Model Context Protocol (MCP) para monitoramento de banco de dados, storage e real-time.

## 📋 Visão Geral

O Supabase MCP permite monitorar e gerenciar:

- **Database**: Tabelas, índices, performance e estatísticas
- **Storage**: Buckets, objetos, uso de espaço e análises
- **Real-time**: Conexões ativas, canais e métricas
- **Project**: Status geral, configurações e saúde

## 🚀 Configuração

### Variáveis de Ambiente

```bash
# Supabase Configuration
SUPABASE_URL=https://seu-projeto.supabase.co
SUPABASE_ANON_KEY=sua-chave-anonima
SUPABASE_SERVICE_ROLE_KEY=sua-chave-service-role
```

### Estrutura de Arquivos

```
src/lib/supabase-integration.ts    # Cliente e serviços MCP
src/app/api/supabase/
├── status/route.ts                # Status geral do projeto
├── tables/route.ts                # Informações de tabelas
└── storage/route.ts               # Gestão de storage
```

## 📡 Endpoints Disponíveis

### 🏥 Status Geral

```http
GET /api/supabase/status
POST /api/supabase/status  # Verificação forçada
```

**Resposta:**

```json
{
  "status": "healthy|degraded|down",
  "message": "Status message",
  "project": {
    "id": "project-id",
    "name": "project-name",
    "status": "ACTIVE_HEALTHY",
    "database_version": "15.1"
  },
  "services": {
    "database": true,
    "storage": true,
    "realtime": true
  },
  "metrics": {
    "database": {
      "tableCount": 12,
      "totalSize": "45.2 MB",
      "connectionCount": 5
    },
    "storage": {
      "bucketCount": 3,
      "totalObjects": 1250,
      "totalSize": "2.1 GB"
    },
    "realtime": {
      "activeConnections": 8,
      "channelsCount": 15
    }
  }
}
```

### 🗄️ Tabelas do Banco

```http
GET /api/supabase/tables?details=true&limit=50&sort=size
POST /api/supabase/tables
```

**Parâmetros GET:**

- `details`: Incluir detalhes completos (RLS, estatísticas)
- `limit`: Número máximo de tabelas (padrão: 50)
- `sort`: Ordenação (`size`, `name`, `rows`)

**Resposta:**

```json
{
  "summary": {
    "totalTables": 12,
    "totalSize": "45.2 MB",
    "largestTable": {
      "name": "documents",
      "size": "15.8 MB",
      "schema": "public"
    }
  },
  "tables": [
    {
      "name": "users",
      "schema": "public",
      "size": "2.1 MB",
      "bytes": 2097152,
      "rls_enabled": true,
      "row_count": 1500
    }
  ]
}
```

**POST Actions:**

```json
{
  "action": "analyze",
  "tableName": "users"
}
```

### 📦 Storage e Buckets

```http
GET /api/supabase/storage
GET /api/supabase/storage?bucket=avatars&objects=true
POST /api/supabase/storage
```

**Parâmetros GET:**

- `bucket`: Nome do bucket específico
- `objects`: Incluir lista de objetos
- `path`: Caminho dentro do bucket

**Resposta (resumo geral):**

```json
{
  "summary": {
    "totalBuckets": 3,
    "publicBuckets": 1,
    "privateBuckets": 2,
    "totalObjects": 1250,
    "totalSize": "2.1 GB"
  },
  "buckets": [
    {
      "name": "avatars",
      "public": true,
      "objectCount": 450,
      "totalSize": "125.5 MB",
      "file_size_limit": 5242880
    }
  ]
}
```

**POST Actions:**

```json
{
  "action": "analyze_bucket",
  "bucketName": "avatars"
}
```

## 🔧 Classes e Métodos

### SupabaseClient

Cliente base para interação com APIs do Supabase:

```typescript
const client = new SupabaseClient({
  serviceRoleKey: 'sua-chave',
  projectUrl: 'https://projeto.supabase.co',
});

// Métodos disponíveis
await client.getProjectInfo();
await client.getTables();
await client.getStorageBuckets();
await client.getStorageObjects('bucket-name');
await client.getProjectMetrics();
await client.checkHealth();
```

### SupabaseMonitoringService

Serviço de alto nível para monitoramento:

```typescript
const service = new SupabaseMonitoringService();

// Status e métricas
await service.getProjectStatus();
await service.getPerformanceMetrics();

// Database
await service.getDatabaseSummary();

// Storage
await service.getStorageSummary();
await service.getBucketContents('bucket-name');
```

## 📊 Métricas e Análises

### Database Analytics

- **Tamanho das tabelas** por bytes e rows
- **Performance** (sequential vs index scans)
- **Segurança** (RLS enabled/disabled)
- **Recomendações** automáticas

### Storage Analytics

- **Distribuição de tipos** de arquivo
- **Uso de espaço** por bucket
- **Arquivos mais antigos/novos/maiores**
- **Recomendações** de otimização

### Recomendações Automáticas

O sistema gera recomendações baseadas em:

**Segurança:**

- Tabelas sem RLS habilitado
- Buckets sem restrições de MIME type

**Performance:**

- Tabelas com muitos sequential scans
- Necessidade de VACUUM por dead rows

**Custo:**

- Buckets com muito espaço usado
- Arquivos antigos para arquivamento

## 🔍 Health Monitoring

### Verificações Automáticas

- ✅ Conectividade com database
- ✅ Acesso ao storage
- ✅ Status do real-time
- ✅ Configuração de credenciais

### Integração com Health Checker

```typescript
import { checkMCPIntegrationsHealth } from '@/lib/health-checker';

const health = await checkMCPIntegrationsHealth();
// Inclui verificação do Supabase MCP
```

## 🛠️ Troubleshooting

### Problemas Comuns

**1. Credenciais não configuradas**

```
Error: Supabase service role key não configurada
```

- Verificar `SUPABASE_SERVICE_ROLE_KEY` no `.env`

**2. Project ref inválido**

```
Error: Supabase project ref não pode ser extraído da URL
```

- Verificar formato da `SUPABASE_URL`

**3. Permissões insuficientes**

```
Error: Supabase Management API error: 403
```

- Verificar se a service role key tem permissões adequadas

### Debug Mode

Para debug detalhado, use:

```typescript
// Verificação forçada com detalhes
const response = await fetch('/api/supabase/status', {
  method: 'POST',
});
```

## 📈 Exemplos de Uso

### Monitoramento de Tabelas

```typescript
// Obter tabelas maiores que 10MB
const tables = await fetch('/api/supabase/tables?sort=size&limit=10');

// Analisar tabela específica
const analysis = await fetch('/api/supabase/tables', {
  method: 'POST',
  body: JSON.stringify({
    action: 'analyze',
    tableName: 'documents',
  }),
});
```

### Gestão de Storage

```typescript
// Listar todos os buckets
const buckets = await fetch('/api/supabase/storage');

// Analisar bucket específico
const bucketAnalysis = await fetch('/api/supabase/storage', {
  method: 'POST',
  body: JSON.stringify({
    action: 'analyze_bucket',
    bucketName: 'avatars',
  }),
});
```

## 🔄 Cache e Performance

- **Cache TTL**: 5 minutos para dados não críticos
- **Rate Limiting**: Integrado com sistema global
- **Timeout**: 30 segundos para operações longas
- **Retry Logic**: 3 tentativas com backoff exponencial

## 🚀 Próximos Passos

- [ ] **Real-time Metrics**: Métricas em tempo real via WebSocket
- [ ] **Alertas**: Sistema de alertas para problemas críticos
- [ ] **Backup Monitoring**: Monitoramento de backups automáticos
- [ ] **Query Analytics**: Análise de queries lentas
- [ ] **Cost Optimization**: Sugestões de otimização de custos
