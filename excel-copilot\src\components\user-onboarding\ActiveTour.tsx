'use client';

import React from 'react';

import { useTour, getTours, TourStep as TourStepType } from './TourProvider';
import { TourStep } from './TourStep';

export function ActiveTour() {
  const { currentTour, currentStep, nextStep, prevStep, endTour, isTourActive } = useTour();

  if (!isTourActive || !currentTour) return null;

  // Obter os passos do tour atual
  const tours = getTours();
  const currentTourSteps = tours[currentTour];

  // Verificar se temos passos e se o índice atual é válido
  if (!currentTourSteps || currentStep >= currentTourSteps.length) return null;

  const step = currentTourSteps[currentStep];

  // Verificar se há um passo definido
  if (!step) return null;

  // Verificar se é o primeiro ou último passo
  const isFirstStep = currentStep === 0;
  const isLastStep = currentStep === currentTourSteps.length - 1;

  // Garantir que o passo está no formato correto
  const typedStep: TourStepType = {
    target: step.target,
    title: step.title,
    content: step.content,
    placement: (step.placement || 'bottom') as 'top' | 'right' | 'bottom' | 'left',
    // action é opcional, então só incluímos se existir
  };

  return (
    <TourStep
      step={typedStep}
      isFirst={isFirstStep}
      isLast={isLastStep}
      onNext={nextStep}
      onPrev={prevStep}
      onClose={endTour}
    />
  );
}
