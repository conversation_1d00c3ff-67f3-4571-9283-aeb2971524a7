{"extends": "./tsconfig.json", "compilerOptions": {"jsx": "react-jsx", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "isolatedModules": true, "noEmit": true, "resolveJsonModule": true, "skipLibCheck": true, "strict": true}, "include": ["src/**/*.ts", "src/**/*.tsx", "__tests__/**/*.ts", "__tests__/**/*.tsx", "jest.setup.js", "jest.env.js"], "exclude": ["node_modules", "__tests__/e2e/**/*"]}