/**
 * Script para encontrar e sugerir correções para testes que estão usando
 * resultados de Promises sem await.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔍 Procurando por testes que usam Promises sem await...');

// Diretórios a serem verificados
const testDirs = ['__tests__'];

// Padrões comuns a serem buscados
const patterns = [
  // Padrão para acessar propriedades de um resultado de Promise sem await
  'expect\\s*\\(\\s*\\w+\\.\\w+\\s*\\)',
  // Padrão para operações em propriedades de Promises sem await
  '\\w+\\.operations\\[\\d+\\]',
  '\\w+\\.success',
];

// Armazenar resultados por arquivo
const results = {};

// Buscar por erros no código
try {
  for (const dir of testDirs) {
    for (const pattern of patterns) {
      try {
        const output = execSync(`npx grep-cli --glob "**/*.{ts,tsx}" "${pattern}" ${dir}`, {
          encoding: 'utf8',
        }).trim();

        if (output) {
          const lines = output.split('\n').filter(Boolean);

          for (const line of lines) {
            // Extrair nome do arquivo
            const match = line.match(/^([^:]+):(\d+):/);
            if (match) {
              const [_, file, lineNum] = match;

              if (!results[file]) {
                results[file] = [];
              }

              results[file].push({
                line: parseInt(lineNum),
                text: line.substring(match[0].length).trim(),
              });
            }
          }
        }
      } catch (e) {
        // grep-cli retorna status code 1 quando não encontra correspondências
        if (e.status !== 1) {
          console.error(`Erro ao buscar padrão ${pattern} em ${dir}:`, e.message);
        }
      }
    }
  }
} catch (e) {
  console.error('Erro ao executar busca:', e.message);
}

// Verificar cada arquivo que pode ter problemas e sugerir correções
for (const [file, issues] of Object.entries(results)) {
  if (issues.length > 0) {
    console.log(`\n📄 Arquivo: ${file}`);
    console.log('   Possíveis problemas de Promise sem await:');

    // Ordenar as linhas
    issues.sort((a, b) => a.line - b.line);

    // Ler o conteúdo do arquivo para análise
    const content = fs.readFileSync(file, 'utf-8').split('\n');

    for (const issue of issues) {
      const line = content[issue.line - 1];

      // Verificar se a linha tem algum await antes do resultado
      if (
        !line.includes('await') &&
        !line.includes('.then(') &&
        /expect|assert/.test(line) &&
        /\.[\w\[\]]+/.test(line)
      ) {
        console.log(`   - Linha ${issue.line}: ${issue.text}`);

        // Encontrar a variável que pode ser um resultado de Promise
        const varMatch = line.match(/expect\s*\(\s*(\w+)\.[\w\[\]]+/);
        if (varMatch && varMatch[1]) {
          const varName = varMatch[1];

          // Verificar as declarações anteriores para ver se é um resultado de uma função que pode retornar Promise
          let foundDeclaration = false;
          for (let i = issue.line - 2; i >= Math.max(0, issue.line - 20); i--) {
            if (
              content[i].includes(`const ${varName} =`) ||
              content[i].includes(`let ${varName} =`)
            ) {
              const declLine = content[i];

              if (
                declLine.includes('parse') ||
                declLine.includes('execute') ||
                declLine.includes('create') ||
                declLine.includes('get') ||
                declLine.includes('fetch') ||
                declLine.includes('load')
              ) {
                foundDeclaration = true;

                // Sugerir correção
                if (!declLine.includes('await')) {
                  console.log(`     ℹ️ Possível correção na linha ${i + 1}:`);
                  console.log(`        Atual: ${declLine.trim()}`);
                  console.log(
                    `        Sugerido: const ${varName} = await ${declLine.split('=')[1].trim()}`
                  );
                }

                break;
              }
            }
          }

          // Se não encontramos uma declaração, podemos sugerir um fix genérico na linha atual
          if (!foundDeclaration) {
            console.log(`     ℹ️ Possível correção:`);
            console.log(`        Atual: ${line.trim()}`);
            console.log(
              `        Sugerido 1: expect((await ${varName})` + line.split(`${varName}`)[1].trim()
            );
            console.log(
              `        Sugerido 2: Usar a função createAsyncTestParserResult do src/lib/excel/testUtils.ts`
            );
          }
        }
      }
    }
  }
}

// Verificar quantidade total de problemas
const totalIssues = Object.values(results).reduce((sum, issues) => sum + issues.length, 0);

if (totalIssues > 0) {
  console.log(
    `\n🔴 Encontrados ${totalIssues} possíveis problemas em ${Object.keys(results).length} arquivos.`
  );
  console.log('\n💡 Sugestão geral:');
  console.log('1. Use sempre "await" ao chamar funções que retornam Promises em testes');
  console.log('2. Importe e use as funções utilitárias de src/lib/excel/testUtils.ts:');
  console.log('   - createTestParserResult: Para criar resultados de parser para testes síncronos');
  console.log(
    '   - createAsyncTestParserResult: Para criar resultados de parser para testes assíncronos'
  );
  console.log('   - createTestOperation: Para criar operações com o tipo correto');
} else {
  console.log('✅ Nenhum problema óbvio de Promise sem await encontrado em testes.');
}

// Exemplo de como corrigir manualmente
console.log('\n📋 Exemplo de correção manual:');
console.log(`
// Antes
const result = parseAICommandToExcelOperations("crie um gráfico");
expect(result.success).toBe(true);

// Depois (opção 1 - usando await)
const result = await parseAICommandToExcelOperations("crie um gráfico");
expect(result.success).toBe(true);

// Depois (opção 2 - usando utilitário)
import { createAsyncTestParserResult } from '@/lib/excel/testUtils';

// Mock para simular a função que retorna Promise
jest.spyOn(parserModule, 'parseAICommandToExcelOperations').mockImplementation(() => 
  createAsyncTestParserResult(true, [{ type: ExcelOperationType.CHART, data: { type: 'BAR' } }])
);
`);

console.log('\n📋 Para executar este script novamente:');
console.log('node scripts/fix-test-await.js');
