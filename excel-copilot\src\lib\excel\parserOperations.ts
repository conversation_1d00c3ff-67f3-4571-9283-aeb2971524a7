import { ExcelOperationType } from '../../types/index';

import { ExcelOperation, CommandParserResult as BaseCommandParserResult } from './types';

// Stub para o logger se não existir
const logger = {
  warn: (message: string) => console.warn(message),
  debug: (message: string) => console.debug(message),
  error: (message: string) => console.error(message),
};

// Stub para normalizeCommand se não existir
const _normalizeCommand = (command: string) => command;

// Estenda a interface para incluir a propriedade success
export interface CommandParserResult extends Omit<BaseCommandParserResult, 'error'> {
  success: boolean;
  error: string | null;
  message: string;
}

// Comandos comuns e suas operações pré-definidas para garantir funcionamento do MVP
const _PREDEFINED_COMMANDS: Record<string, any> = {
  // Comandos para somar
  'some os valores da coluna': {
    type: 'FORMULA',
    data: {
      formula: 'SUM',
      range: 'A1:A10', // Será ajustado dinamicamente
      resultCell: 'A11',
    },
  },

  // Comandos para criar tabela
  'crie uma tabela': {
    type: 'TABLE_OPERATION',
    data: {
      type: 'CREATE',
      headers: ['Item', 'Quantidade', 'Valor', 'Total'],
      rows: [
        ['Produto A', '10', '50', '=B2*C2'],
        ['Produto B', '5', '100', '=B3*C3'],
        ['Produto C', '8', '75', '=B4*C4'],
        ['Produto D', '12', '25', '=B5*C5'],
      ],
    },
  },

  // Comandos para gráficos
  'crie um gráfico': {
    type: 'CHART',
    data: {
      type: 'BAR',
      dataX: 'A2:A5',
      dataY: 'D2:D5',
      title: 'Valores por Item',
    },
  },

  // Comandos para análise
  'calcule a média': {
    type: 'FORMULA',
    data: {
      formula: 'AVERAGE',
      range: 'C2:C5', // Será ajustado
      resultCell: 'C6',
    },
  },
};

/**
 * Normaliza uma fórmula para garantir o formato adequado
 * @param formula Fórmula a ser normalizada
 * @returns Fórmula normalizada
 */
function normalizeFormula(formula: string): string {
  // Se a fórmula não começar com "=", adicionar
  if (formula && !formula.trim().startsWith('=')) {
    // Verificar se é uma função conhecida que precisa de "="
    const knownFunctions = [
      'SUM',
      'AVERAGE',
      'COUNT',
      'MAX',
      'MIN',
      'IF',
      'VLOOKUP',
      'HLOOKUP',
      'CONCATENATE',
    ];
    let needsEquals = false;

    for (const func of knownFunctions) {
      if (formula.toUpperCase().includes(func)) {
        needsEquals = true;
        break;
      }
    }

    if (needsEquals || /[+\-*/]/.test(formula)) {
      return `=${formula.trim()}`;
    }
  }

  return formula.trim();
}

/**
 * Extrai o título do gráfico do comando de texto
 * @param text Texto do comando
 * @returns Título extraído ou título padrão
 */
function extractChartTitle(text: string): string {
  // Tentar extrair o título usando regex
  const titleRegex = /(?:com|usando|e)?\s+(?:o\s+)?título\s+(?:de|como)?\s+["'](.+?)["']/i;
  const match = text.match(titleRegex);

  if (match && match[1]) {
    return match[1].trim();
  }

  // Tentar extrair o título após "título"
  const simpleTitleRegex = /título\s+(.+?)(?:\s+(?:com|usando|e|para)|$)/i;
  const simpleMatch = text.match(simpleTitleRegex);

  if (simpleMatch && simpleMatch[1]) {
    return simpleMatch[1].trim();
  }

  // Título padrão baseado no tipo de gráfico
  for (const chartType of ['barra', 'linha', 'pizza', 'dispersão', 'área']) {
    if (text.toLowerCase().includes(chartType)) {
      return `Gráfico de ${chartType}`;
    }
  }

  // Título padrão
  return 'Gráfico de dados';
}

/**
 * Extrai operações de fórmula de um texto
 * @param text Texto para analisar
 * @returns Array de operações de fórmula
 */
export function extractFormulaOperations(text: string): ExcelOperation[] {
  if (!text || typeof text !== 'string') {
    logger.warn('extractFormulaOperations: texto inválido ou vazio');
    return [];
  }

  const operations: ExcelOperation[] = [];

  // Regex aprimorada para fórmulas com melhor captura de padrões
  const formulaRegexes = [
    // Padrão: "aplicar fórmula =X na célula Y"
    /(?:aplicar|inserir|usar)\s+(?:a\s+)?fórmula\s+([=A-Z0-9+\-*/()s]+)\s+(?:na|em|para)\s+(?:célula|células|range|intervalo)?\s*([A-Z]+[0-9]+(?::[A-Z]+[0-9]+)?)/i,

    // Padrão: "calcular X em Y"
    /calcular\s+([^,;]+)\s+(?:na|em|para)\s+(?:célula|células|range|intervalo)?\s*([A-Z]+[0-9]+(?::[A-Z]+[0-9]+)?)/i,

    // Padrão: "inserir X em Y"
    /inserir\s+([^,;]+)\s+(?:na|em|para)\s+(?:célula|células|range|intervalo)?\s*([A-Z]+[0-9]+(?::[A-Z]+[0-9]+)?)/i,
  ];

  // Tentar cada regex até encontrar uma correspondência
  for (const regex of formulaRegexes) {
    const match = text.match(regex);

    if (match && match[1] && match[2]) {
      const formula = match[1].trim();
      const range = match[2].trim();

      // Verificar se o range tem formato válido
      if (!/^[A-Z]+[0-9]+(?::[A-Z]+[0-9]+)?$/.test(range)) {
        logger.warn(`extractFormulaOperations: range inválido "${range}" ignorado`);
        continue;
      }

      // Verificar se a fórmula precisa do prefixo "="
      const normalizedFormula = normalizeFormula(formula);

      // Verificar se a fórmula parece ser potencialmente perigosa
      const dangerousPatterns = [
        /EXEC\(/i,
        /SHELL\(/i,
        /RUN\(/i,
        /CALL\(/i,
        /SYSTEM\(/i,
        /\bURL\(/i,
        /\bHTTP/i,
        /\bFTP/i,
        /\bIMPORT/i,
        /\bEVAL\(/i,
      ];

      if (dangerousPatterns.some(pattern => pattern.test(normalizedFormula))) {
        logger.warn(
          `extractFormulaOperations: fórmula potencialmente insegura "${normalizedFormula}" ignorada`
        );
        continue;
      }

      operations.push({
        type: ExcelOperationType.FORMULA,
        data: {
          formula: normalizedFormula,
          range,
        },
      });

      // Registrar sucesso no log
      logger.debug(
        `extractFormulaOperations: fórmula "${normalizedFormula}" extraída para o range "${range}"`
      );
    }
  }

  return operations;
}

/**
 * Extrai operações de filtro de um texto
 * @param text Texto para analisar
 * @returns Array de operações de filtro
 */
export function extractFilterOperations(text: string): ExcelOperation[] {
  const operations: ExcelOperation[] = [];

  // Regex para filtros
  const filterRegex =
    /filtrar\s+(?:a\s+)?coluna\s+([A-Z]+|"[^"]+"|'[^']+')\s+(?:para|onde)\s+([^,;]+)/i;
  const match = text.match(filterRegex);

  if (match && match[1] && match[2]) {
    const column = match[1].replace(/["']/g, '').trim();
    const condition = match[2].trim().toLowerCase();

    // Identificar operador e valor
    let operator = 'EQUALS';
    let value: string | number = '';

    if (condition.includes('igual a')) {
      operator = 'EQUALS';
      value = condition.split('igual a')[1]?.trim() || '';
    } else if (condition.includes('maior que')) {
      operator = 'GREATER_THAN';
      value = condition.split('maior que')[1]?.trim() || '';
    } else if (condition.includes('menor que')) {
      operator = 'LESS_THAN';
      value = condition.split('menor que')[1]?.trim() || '';
    } else if (condition.includes('contém')) {
      operator = 'CONTAINS';
      value = condition.split('contém')[1]?.trim() || '';
    }

    // Tentar converter para número se possível
    const numValue = parseFloat(value.toString());
    if (!isNaN(numValue)) {
      value = numValue;
    } else {
      // Remover aspas
      value = value.toString().replace(/["']/g, '');
    }

    operations.push({
      type: ExcelOperationType.FILTER,
      data: {
        column,
        operator,
        value,
      },
    });
  }

  return operations;
}

/**
 * Extrai operações de ordenação de um texto
 * @param text Texto para analisar
 * @returns Array de operações de ordenação
 */
export function extractSortOperations(text: string): ExcelOperation[] {
  const operations: ExcelOperation[] = [];

  // Regex para ordenação
  const sortRegex =
    /ordenar\s+(?:a\s+)?(?:coluna|dados)\s+([A-Z]+|"[^"]+"|'[^']+')\s+(?:em\s+)?(crescente|decrescente)/i;
  const match = text.match(sortRegex);

  if (match && match[1] && match[2]) {
    const column = match[1].replace(/["']/g, '').trim();
    const direction = match[2].toLowerCase() === 'crescente' ? 'ASC' : 'DESC';

    operations.push({
      type: ExcelOperationType.SORT,
      data: {
        column,
        direction,
      },
    });
  }

  return operations;
}

/**
 * Extrai operações de gráfico de um texto
 * @param text Texto para analisar
 * @returns Array de operações de gráfico
 */
export function extractChartOperations(text: string): ExcelOperation[] {
  if (!text || typeof text !== 'string') {
    logger.warn('extractChartOperations: texto inválido ou vazio');
    return [];
  }

  const operations: ExcelOperation[] = [];

  // Regex aprimorada para gráficos com diferentes padrões de comando
  const chartRegexes = [
    // Padrão principal: "criar gráfico de tipo X com dados em Y"
    /criar\s+(?:um\s+)?gráfico\s+(?:de\s+)?(barra|linha|pizza|dispersão|área)\s+(?:com|usando)\s+(?:os\s+)(?:dados|valores)(?:\s+da|\s+no|\s+em)?\s+(?:região|range|intervalo)?\s*([A-Z]+[0-9]+:[A-Z]+[0-9]+)/i,

    // Padrão alternativo: "gerar gráfico tipo X para dados Y"
    /gerar\s+(?:um\s+)?gráfico\s+(?:do\s+tipo\s+)?(barra|linha|pizza|dispersão|área)\s+(?:para|com)\s+(?:os\s+)?dados\s+(?:em|de|do)\s+([A-Z]+[0-9]+:[A-Z]+[0-9]+)/i,

    // Padrão simples: "mostrar gráfico de Y"
    /mostrar\s+(?:um\s+)?gráfico\s+(?:de\s+)?(barra|linha|pizza|dispersão|área)\s+(?:para|dos?)\s+(?:dados|valores)\s+(?:em|de)\s+([A-Z]+[0-9]+:[A-Z]+[0-9]+)/i,
  ];

  // Tentar cada regex até encontrar uma correspondência
  for (const regex of chartRegexes) {
    const match = text.match(regex);

    if (match && match[1] && match[2]) {
      const chartType = match[1].toLowerCase();
      const range = match[2].trim();

      // Validar o range
      if (!/^[A-Z]+[0-9]+:[A-Z]+[0-9]+$/.test(range)) {
        logger.warn(`extractChartOperations: range inválido "${range}" ignorado`);
        continue;
      }

      // Verificar se o tipo é suportado
      const typeMap: Record<string, string> = {
        barra: 'BAR',
        linha: 'LINE',
        pizza: 'PIE',
        dispersão: 'SCATTER',
        área: 'AREA',
      };

      if (!typeMap[chartType]) {
        logger.warn(`extractChartOperations: tipo de gráfico não suportado "${chartType}"`);
        continue;
      }

      operations.push({
        type: ExcelOperationType.CHART,
        data: {
          type: typeMap[chartType],
          range,
          title: extractChartTitle(text),
        },
      });

      // Registrar sucesso no log
      logger.debug(
        `extractChartOperations: gráfico "${typeMap[chartType]}" extraído para o range "${range}"`
      );
    }
  }

  return operations;
}
