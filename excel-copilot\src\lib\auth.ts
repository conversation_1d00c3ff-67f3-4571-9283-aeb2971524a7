import { getServerSession } from 'next-auth';

import { authOptions } from '@/server/auth/options';

/**
 * Função utilitária para obter a sessão do servidor
 * Centraliza a configuração de autenticação
 */
export async function getSession() {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  return await getServerSession(authOptions as any);
}

/**
 * Função para verificar se o usuário está autenticado
 */
export async function isAuthenticated(): Promise<boolean> {
  const session = await getSession();
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  return !!(session as any)?.user;
}

/**
 * Função para obter o usuário atual
 */
export async function getCurrentUser() {
  const session = await getSession();
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  return (session as any)?.user || null;
}

/**
 * Função para obter o ID do usuário atual
 */
export async function getCurrentUserId(): Promise<string | null> {
  const session = await getSession();
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  return (session as any)?.user?.id || null;
}

/**
 * Re-exportar authOptions para compatibilidade
 */
export { authOptions } from '@/server/auth/options';
