// Tipos opcionais utilizados nas operações do Excel
export type Optional<T> = T | undefined | null;

/**
 * Tipo utilitário que permite undefined como valor para propriedades opcionais
 * Resolve problemas com exactOptionalPropertyTypes: true
 */
export type WithOptionalFields<T> = {
  [K in keyof T]: T[K] extends undefined ? T[K] | undefined : T[K];
};

export type OptionalNumber = Optional<number>;
export type OptionalString = Optional<string>;
export type OptionalBoolean = Optional<boolean>;

export type NullableString = string | null;

export type OptionalArray<T> = Optional<T[]>;
export type OptionalRecord<K extends keyof any, T> = Optional<Record<K, T>>;

export interface OptionalRange {
  startRow?: number;
  startCol?: number;
  endRow?: number;
  endCol?: number;
}

export interface OptionalFormatting {
  bold?: boolean;
  italic?: boolean;
  underline?: boolean;
  fontSize?: number;
  fontColor?: string;
  backgroundColor?: string;
  horizontalAlignment?: 'left' | 'center' | 'right';
  verticalAlignment?: 'top' | 'middle' | 'bottom';
}

export type OptionalCallback<T = void> = Optional<(result: T) => void>;
