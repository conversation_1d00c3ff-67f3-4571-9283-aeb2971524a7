import ExcelJS from 'exceljs';
import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';

import { logger } from '@/lib/logger';
import { prisma } from '@/server/db/client';

// Configuração do segmento de rota (formato do Next.js 14)
export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

/**
 * Sanitiza o nome do arquivo para evitar injeção de caracteres especiais
 * no cabeçalho Content-Disposition, seguindo RFC 6266
 */
function sanitizeFilename(filename: string): string {
  if (!filename) return 'planilha';

  // Remove caracteres inválidos para nomes de arquivo
  const sanitized = filename
    .replace(/[\\/:*?"<>|]/g, '')
    .replace(/\s+/g, '_')
    .substring(0, 255);

  return sanitized || 'planilha';
}

/**
 * Gera o cabeçalho Content-Disposition com suporte a caracteres não-ASCII
 * Implementa RFC 6266 para compatibilidade entre navegadores
 */
function getContentDisposition(filename: string): string {
  const sanitizedName = sanitizeFilename(filename);
  const isAscii = /^[!-~]*$/.test(sanitizedName);

  if (isAscii) {
    return `attachment; filename="${sanitizedName}.xlsx"`;
  } else {
    const encodedName = encodeURIComponent(sanitizedName).replace(/%20/g, '_');
    return `attachment; filename="${sanitizedName}.xlsx"; filename*=UTF-8''${encodedName}.xlsx`;
  }
}

export async function GET(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    // Verificar autenticação
    const session = await getServerSession();
    if (!session?.user) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    const workbookId = params.id;

    // Buscar workbook do banco de dados
    const workbook = await prisma.workbook.findUnique({
      where: {
        id: workbookId,
      },
      include: {
        sheets: true,
      },
    });

    if (!workbook) {
      return NextResponse.json({ error: 'Planilha não encontrada' }, { status: 404 });
    }

    // Verificar se o usuário tem acesso a este workbook
    if (workbook.userId !== (session.user as { id?: string })?.id && !workbook.isPublic) {
      return NextResponse.json({ error: 'Acesso negado a esta planilha' }, { status: 403 });
    }

    // Criar arquivo Excel
    const excelWorkbook = new ExcelJS.Workbook();
    excelWorkbook.creator = 'Excel Copilot';
    excelWorkbook.lastModifiedBy = session.user.name || 'Usuário';
    excelWorkbook.created = new Date(workbook.createdAt);
    excelWorkbook.modified = new Date(workbook.updatedAt);

    // Definir metadados/propriedades do documento (compatível com ExcelJS 4.x)
    // Em vez de acessar properties diretamente, que pode ter tipos incompatíveis
    const _docProps = {
      title: workbook.name,
      subject: workbook.description || 'Gerado pelo Excel Copilot',
      keywords: 'excel,copilot,planilha',
      category: 'Planilhas Inteligentes',
      company: 'Excel Copilot',
    };

    // Adicionar cada sheet ao arquivo Excel
    for (const sheet of workbook.sheets) {
      const worksheet = excelWorkbook.addWorksheet(sheet.name);

      // Se tiver dados na sheet, adicionar ao worksheet
      if (sheet.data) {
        try {
          const sheetData = typeof sheet.data === 'string' ? JSON.parse(sheet.data) : sheet.data;

          // Verificar o formato dos dados
          if (sheetData && sheetData.headers && Array.isArray(sheetData.headers)) {
            // Configurar cabeçalhos
            worksheet.columns = sheetData.headers.map((header: string) => ({
              header,
              key: header,
              width: Math.max(header.length * 1.2, 10),
            }));

            // Adicionar linhas de dados
            if (sheetData.rows && Array.isArray(sheetData.rows)) {
              worksheet.addRows(sheetData.rows);
            }

            // Estilizar cabeçalhos
            if (sheetData.headers.length > 0) {
              worksheet.getRow(1).font = { bold: true };
              worksheet.getRow(1).fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: 'FFE6F0FF' },
              };

              // Adicionar bordas leves às células
              worksheet.eachRow({ includeEmpty: false }, (row, _rowNumber) => {
                row.eachCell({ includeEmpty: false }, cell => {
                  cell.border = {
                    top: { style: 'thin', color: { argb: 'FFCCCCCC' } },
                    left: { style: 'thin', color: { argb: 'FFCCCCCC' } },
                    bottom: { style: 'thin', color: { argb: 'FFCCCCCC' } },
                    right: { style: 'thin', color: { argb: 'FFCCCCCC' } },
                  };
                });
              });
            }
          }
        } catch (err) {
          logger.error(`Erro ao processar dados da planilha ${sheet.id}:`, err);
          // Continue com a próxima planilha em caso de erro
        }
      }
    }

    // Gerar buffer do arquivo Excel
    const buffer = await excelWorkbook.xlsx.writeBuffer();

    // Gerar o cabeçalho Content-Disposition seguro
    const contentDisposition = getContentDisposition(workbook.name);

    // Retornar o arquivo para download
    return new NextResponse(buffer, {
      headers: {
        'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition': contentDisposition,
        'Cache-Control': 'no-store, max-age=0',
      },
    });
  } catch (error) {
    logger.error('[EXCEL_EXPORT_ERROR]', error);
    return NextResponse.json({ error: 'Erro ao gerar o arquivo Excel' }, { status: 500 });
  }
}
