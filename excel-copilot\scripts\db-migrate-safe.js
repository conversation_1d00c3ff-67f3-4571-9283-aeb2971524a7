const { execSync } = require('child_process');
const { PrismaClient } = require('@prisma/client');
const fs = require('fs');
const path = require('path');

/**
 * Script para realizar migrações de banco de dados de forma segura
 * com verificações prévias, backups e recuperação em caso de falha
 */
async function safeMigration() {
  console.log('\n🔍 INICIANDO MIGRAÇÃO SEGURA DO BANCO DE DADOS\n');

  // Verificar ambiente
  const isProduction = process.env.NODE_ENV === 'production';
  if (isProduction) {
    console.log('⚠️  ATENÇÃO: Executando em ambiente de PRODUÇÃO');
  } else {
    console.log('ℹ️  Executando em ambiente de desenvolvimento/teste');
  }

  // Diretório para backups
  const backupDir = path.join(__dirname, '../backups');
  if (!fs.existsSync(backupDir)) {
    fs.mkdirSync(backupDir, { recursive: true });
    console.log(`✅ Diretório de backups criado: ${backupDir}`);
  }

  const prisma = new PrismaClient();
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const backupFilename = path.join(backupDir, `db-backup-${timestamp}.sql`);

  try {
    // Verificar conexão
    console.log('🔄 Verificando conexão com o banco de dados...');
    await prisma.$queryRaw`SELECT 1`;
    console.log('✅ Conexão com banco de dados estabelecida');

    // Verificar se há operações críticas em andamento
    console.log('🔄 Verificando atividade no sistema...');
    const activeUsers = await prisma.session.count({
      where: {
        expires: {
          gt: new Date(),
        },
      },
    });
    console.log(`ℹ️  Usuários com sessões ativas: ${activeUsers}`);

    if (activeUsers > 100 && isProduction) {
      console.warn(
        '⚠️  ALERTA: Muitos usuários ativos. Considere agendar a migração para um horário de menor tráfego.'
      );
      const shouldContinue = await promptContinue();
      if (!shouldContinue) {
        console.log('❌ Migração cancelada pelo usuário');
        process.exit(0);
      }
    }

    // Criar backup antes da migração
    console.log(`🔄 Criando backup do banco de dados em ${backupFilename}...`);

    try {
      // Determinar qual comando de backup usar com base no provider
      const databaseUrl = process.env.DB_DATABASE_URL || '';

      if (databaseUrl.includes('postgresql://') || databaseUrl.includes('postgres://')) {
        // PostgreSQL backup
        execSync(`pg_dump "${process.env.DB_DATABASE_URL}" -f "${backupFilename}"`);
      } else if (databaseUrl.includes('mysql://')) {
        // MySQL backup
        execSync(
          `mysqldump --host="${getHostFromUrl(databaseUrl)}" --user="${getUserFromUrl(databaseUrl)}" --password="${getPasswordFromUrl(databaseUrl)}" "${getDatabaseFromUrl(databaseUrl)}" > "${backupFilename}"`
        );
      } else {
        throw new Error('Provedor de banco de dados não suportado para backup automatizado');
      }

      console.log('✅ Backup criado com sucesso');
    } catch (backupError) {
      console.error('⚠️  Não foi possível criar backup automatizado:', backupError.message);
      console.log('ℹ️  Tentando método alternativo via Prisma...');

      // Salvar estrutura do banco via Prisma (não é um backup completo, mas pode ajudar)
      fs.writeFileSync(
        backupFilename.replace('.sql', '-schema.json'),
        JSON.stringify(await prisma.$queryRaw`SELECT * FROM information_schema.tables`, null, 2)
      );

      const shouldContinue = await promptContinue(
        'Não foi possível criar um backup completo. Deseja continuar mesmo assim?'
      );
      if (!shouldContinue) {
        console.log('❌ Migração cancelada pelo usuário');
        process.exit(0);
      }
    }

    // Aplicar migração
    console.log('🔄 Aplicando migração...');
    execSync('npx prisma migrate deploy', { stdio: 'inherit' });

    console.log('\n✅ Migração aplicada com sucesso!');

    // Verificar integridade após migração
    console.log('🔄 Verificando integridade do banco após migração...');
    await prisma.$queryRaw`SELECT 1`;
    console.log('✅ Banco de dados operacional após migração');
  } catch (error) {
    console.error('\n❌ ERRO DURANTE MIGRAÇÃO:', error.message);

    if (fs.existsSync(backupFilename)) {
      console.log('\n⚠️  TENTANDO RESTAURAR BACKUP...');

      try {
        // Determinar qual comando de restauração usar
        const databaseUrl = process.env.DB_DATABASE_URL || '';

        if (databaseUrl.includes('postgresql://') || databaseUrl.includes('postgres://')) {
          // PostgreSQL restore
          execSync(`psql "${process.env.DB_DATABASE_URL}" < "${backupFilename}"`);
        } else if (databaseUrl.includes('mysql://')) {
          // MySQL restore
          execSync(
            `mysql --host="${getHostFromUrl(databaseUrl)}" --user="${getUserFromUrl(databaseUrl)}" --password="${getPasswordFromUrl(databaseUrl)}" "${getDatabaseFromUrl(databaseUrl)}" < "${backupFilename}"`
          );
        }

        console.log(
          '✅ Restauração de backup concluída. O banco foi revertido ao estado anterior à migração'
        );
      } catch (restoreError) {
        console.error('❌ ERRO AO RESTAURAR BACKUP:', restoreError.message);
        console.error(
          '⚠️  ATENÇÃO: O banco pode estar em estado inconsistente. Restauração manual pode ser necessária.'
        );
      }
    } else {
      console.error('❌ Não foi possível encontrar o arquivo de backup para restauração');
    }

    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

/**
 * Função auxiliar para prompt de confirmação
 */
async function promptContinue(message = 'Deseja continuar?') {
  // Implementação simplificada, em um ambiente real usaria um pacote como 'inquirer'
  return new Promise(resolve => {
    const { stdin, stdout } = process;
    stdout.write(`${message} (s/N): `);

    stdin.once('data', data => {
      const input = data.toString().trim().toLowerCase();
      resolve(input === 's' || input === 'sim' || input === 'y' || input === 'yes');
      if (!process.stdin.isTTY) {
        stdin.pause();
      }
    });
  });
}

/**
 * Funções auxiliares para extrair componentes da URL de conexão
 */
function getHostFromUrl(url) {
  const matches = url.match(/@([^:]+):/);
  return matches ? matches[1] : '';
}

function getUserFromUrl(url) {
  const matches = url.match(/\/\/([^:]+):/);
  return matches ? matches[1] : '';
}

function getPasswordFromUrl(url) {
  const matches = url.match(/:([^@]+)@/);
  return matches ? matches[1] : '';
}

function getDatabaseFromUrl(url) {
  const matches = url.match(/\/([^?]+)(\?|$)/);
  return matches ? matches[1] : '';
}

// Executar a migração segura
safeMigration().catch(error => {
  console.error('Falha na migração:', error);
  process.exit(1);
});
