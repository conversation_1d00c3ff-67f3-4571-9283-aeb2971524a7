// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Testes de Regressão Visual Componentes de UI Básicos Button com variant e size renderiza corretamente 1`] = `
<div>
  <button
    variant="destructive"
  >
    Botão Pequeno Destrutivo
  </button>
</div>
`;

exports[`Testes de Regressão Visual Componentes de UI Básicos Button renderiza corretamente 1`] = `
<div>
  <button>
    Botão de Teste
  </button>
</div>
`;

exports[`Testes de Regressão Visual Componentes de UI Básicos Card com título renderiza corretamente 1`] = `
<div>
  <div
    class="rounded-xl border shadow-sm p-6 border-border bg-card"
  >
    <div
      class="mb-4 flex flex-col space-y-1.5"
    >
      <h3
        class="text-xl font-semibold leading-none tracking-tight"
      >
        Título do Card
      </h3>
    </div>
    <div
      class="card-content"
    >
      <p>
        Conteúdo do card para teste
      </p>
    </div>
  </div>
</div>
`;

exports[`Testes de Regressão Visual Componentes de UI Básicos Input com Label renderiza corretamente 1`] = `
<div>
  <div>
    <label
      class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
      for="email"
    >
      Email
    </label>
    <input
      id="email"
      placeholder="Digite seu email"
      type="email"
    />
  </div>
</div>
`;

exports[`Testes de Regressão Visual Componentes de UI Básicos Input renderiza corretamente 1`] = `
<div>
  <input
    placeholder="Digite aqui..."
  />
</div>
`;

exports[`Testes de Regressão Visual Componentes de UI Básicos Tabs renderizam corretamente 1`] = `
<div>
  <div
    data-orientation="horizontal"
    dir="ltr"
  >
    <div
      aria-orientation="horizontal"
      class="inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground"
      data-orientation="horizontal"
      role="tablist"
      style="outline: none;"
      tabindex="0"
    >
      <button
        aria-controls="radix-:r0:-content-tab1"
        aria-selected="true"
        class="inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm"
        data-orientation="horizontal"
        data-radix-collection-item=""
        data-state="active"
        id="radix-:r0:-trigger-tab1"
        role="tab"
        tabindex="-1"
        type="button"
      >
        Aba 1
      </button>
      <button
        aria-controls="radix-:r0:-content-tab2"
        aria-selected="false"
        class="inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm"
        data-orientation="horizontal"
        data-radix-collection-item=""
        data-state="inactive"
        id="radix-:r0:-trigger-tab2"
        role="tab"
        tabindex="-1"
        type="button"
      >
        Aba 2
      </button>
    </div>
    <div
      aria-labelledby="radix-:r0:-trigger-tab1"
      class="mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
      data-orientation="horizontal"
      data-state="active"
      id="radix-:r0:-content-tab1"
      role="tabpanel"
      style="animation-duration: 0s;"
      tabindex="0"
    >
      Conteúdo da Aba 1
    </div>
    <div
      aria-labelledby="radix-:r0:-trigger-tab2"
      class="mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
      data-orientation="horizontal"
      data-state="inactive"
      hidden=""
      id="radix-:r0:-content-tab2"
      role="tabpanel"
      tabindex="0"
    />
  </div>
</div>
`;

exports[`Testes de Regressão Visual Responsividade Componentes são responsivos em viewport mobile: mobile-viewport 1`] = `
<div>
  <div
    style="width: 100%;"
  >
    <div
      class="rounded-xl border shadow-sm p-6 border-border bg-card w-full"
    >
      <div
        class="mb-4 flex flex-col space-y-1.5"
      >
        <h3
          class="text-xl font-semibold leading-none tracking-tight"
        >
          Card Responsivo
        </h3>
      </div>
      <div
        class="card-content"
      >
        <input
          placeholder="Input responsivo"
        />
        <button
          class="mt-4 w-full"
        >
          Botão Responsivo
        </button>
      </div>
    </div>
  </div>
</div>
`;
