/**
 * Vercel Integration para Excel Copilot
 * Integração com Vercel API para monitoramento de deployments, logs e métricas
 */

import { logger } from '@/lib/logger';

// Cache simples para evitar muitas requisições
const cache = new Map<string, { data: unknown; timestamp: number }>();
const CACHE_TTL = 5 * 60 * 1000; // 5 minutos

function _getCachedData<T>(key: string): T | null {
  const cached = cache.get(key);
  if (cached && Date.now() - cached.timestamp < CACHE_TTL) {
    return cached.data as T;
  }
  return null;
}

function _setCachedData<T>(key: string, data: T): void {
  cache.set(key, { data, timestamp: Date.now() });
}

// Tipos para a API do Vercel
export interface VercelDeployment {
  uid: string;
  name: string;
  url: string;
  state: 'BUILDING' | 'ERROR' | 'INITIALIZING' | 'QUEUED' | 'READY' | 'CANCELED';
  type: 'LAMBDAS';
  created: number;
  buildingAt?: number;
  ready?: number | null;
  alias?: string[];
  aliasAssigned?: number;
  meta?: Record<string, unknown>;
  target?: 'production' | 'staging' | 'preview' | 'development';
  source?: 'cli' | 'git' | 'import';
  projectId?: string;
  inspectorUrl?: string;
  creator?: {
    uid: string;
    username: string;
    email: string;
  };
}

export interface VercelLog {
  id?: string;
  timestamp: number;
  message: string;
  level: 'debug' | 'info' | 'warn' | 'error';
  source: 'build' | 'static' | 'lambda' | 'edge';
  deploymentId: string;
  requestId?: string;
  region?: string;
  statusCode?: number;
  duration?: number;
}

export interface VercelMetrics {
  timestamp?: number;
  requests?: number;
  requests24h?: number;
  bandwidth?: number;
  bandwidth24h?: number;
  duration?: number;
  errors?: number;
  errors24h?: number;
  errorRate?: number;
  avgResponseTime?: number;
  uniqueVisitors24h?: number;
  p95ResponseTime?: number;
  p99ResponseTime?: number;
  cacheHits?: number;
  cacheMisses?: number;
  cache?: {
    hits: number;
    misses: number;
    ratio: number;
  };
}

export interface VercelProject {
  id: string;
  name: string;
  accountId?: string;
  createdAt: number;
  updatedAt: number;
  framework: string;
  devCommand?: string;
  buildCommand?: string;
  outputDirectory?: string;
  rootDirectory?: string;
  directoryListing?: boolean;
  description?: string;
  repository?: {
    type: string;
    url: string;
  };
  settings?: {
    buildCommand?: string;
    outputDirectory?: string;
    installCommand?: string;
  };
  targets?: Record<
    string,
    {
      id: string;
      url: string;
      state: string;
      createdAt: number;
    }
  >;
  env: Array<{
    id?: string;
    key: string;
    value: string;
    target?: string[];
    type?: 'system' | 'secret' | 'encrypted' | 'plain';
    createdAt?: number;
    updatedAt?: number;
  }>;
}

/**
 * Cliente para integração com Vercel API
 */
export class VercelClient {
  private readonly apiToken: string;
  private readonly teamId?: string | undefined;
  private readonly projectId?: string | undefined;
  private readonly baseUrl = 'https://api.vercel.com';

  constructor(options: { apiToken: string; teamId?: string; projectId?: string }) {
    this.apiToken = options.apiToken;
    this.teamId = options.teamId || undefined;
    this.projectId = options.projectId || undefined;
  }

  /**
   * Faz uma requisição para a API do Vercel
   */
  private async makeRequest<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`;
    const headers: Record<string, string> = {
      Authorization: `Bearer ${this.apiToken}`,
      'Content-Type': 'application/json',
    };

    // Adicionar headers customizados se fornecidos
    if (options.headers) {
      Object.assign(headers, options.headers);
    }

    if (this.teamId) {
      headers['X-Vercel-Team-Id'] = this.teamId;
    }

    try {
      const response = await fetch(url, {
        ...options,
        headers,
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Vercel API error: ${response.status} - ${errorText}`);
      }

      return await response.json();
    } catch (error) {
      logger.error('Erro na requisição Vercel API', { endpoint, error });
      throw error;
    }
  }

  /**
   * Lista deployments do projeto
   */
  async getDeployments(limit = 20): Promise<VercelDeployment[]> {
    const endpoint = this.projectId
      ? `/v6/deployments?projectId=${this.projectId}&limit=${limit}`
      : `/v6/deployments?limit=${limit}`;

    const response = await this.makeRequest<{ deployments: VercelDeployment[] }>(endpoint);
    return response.deployments;
  }

  /**
   * Obtém detalhes de um deployment específico
   */
  async getDeployment(deploymentId: string): Promise<VercelDeployment> {
    const endpoint = `/v13/deployments/${deploymentId}`;
    return await this.makeRequest<VercelDeployment>(endpoint);
  }

  /**
   * Obtém logs de um deployment
   */
  async getDeploymentLogs(
    deploymentId: string,
    options: {
      since?: number;
      until?: number;
      limit?: number;
      direction?: 'forward' | 'backward';
    } = {}
  ): Promise<VercelLog[]> {
    try {
      const params = new URLSearchParams();
      if (options.since) params.append('since', options.since.toString());
      if (options.until) params.append('until', options.until.toString());
      if (options.limit) params.append('limit', options.limit.toString());
      if (options.direction) params.append('direction', options.direction);

      const endpoint = `/v2/deployments/${deploymentId}/events?${params.toString()}`;
      const response = await this.makeRequest<{ events?: VercelLog[] }>(endpoint);

      // A API do Vercel retorna os logs em uma propriedade 'events'
      return response.events || [];
    } catch (error) {
      logger.error(`Erro ao obter logs do deployment ${deploymentId}`, error);
      return [];
    }
  }

  /**
   * Obtém informações do projeto
   */
  async getProject(projectId?: string): Promise<VercelProject> {
    const id = projectId || this.projectId;
    if (!id) {
      throw new Error('Project ID é obrigatório');
    }

    const endpoint = `/v9/projects/${id}`;
    return await this.makeRequest<VercelProject>(endpoint);
  }

  /**
   * Obtém métricas de analytics
   */
  async getAnalytics(
    options: {
      since?: number;
      until?: number;
      granularity?: 'day' | 'hour' | 'minute';
    } = {}
  ): Promise<VercelMetrics[]> {
    if (!this.projectId) {
      throw new Error('Project ID é obrigatório para analytics');
    }

    const params = new URLSearchParams();
    if (options.since) params.append('since', options.since.toString());
    if (options.until) params.append('until', options.until.toString());
    if (options.granularity) params.append('granularity', options.granularity);

    const endpoint = `/v1/analytics?projectId=${this.projectId}&${params.toString()}`;
    const response = await this.makeRequest<{ metrics: VercelMetrics[] }>(endpoint);
    return response.metrics;
  }

  /**
   * Obtém variáveis de ambiente do projeto
   */
  async getEnvironmentVariables(): Promise<VercelProject['env']> {
    if (!this.projectId) {
      throw new Error('Project ID é obrigatório');
    }

    const endpoint = `/v9/projects/${this.projectId}/env`;
    const response = await this.makeRequest<{ envs: VercelProject['env'] }>(endpoint);
    return response.envs;
  }

  /**
   * Cria ou atualiza uma variável de ambiente
   */
  async setEnvironmentVariable(
    key: string,
    value: string,
    target: ('production' | 'preview' | 'development')[] = ['production', 'preview'],
    type: 'system' | 'secret' | 'encrypted' | 'plain' = 'encrypted'
  ): Promise<{ success: boolean; message: string }> {
    if (!this.projectId) {
      throw new Error('Project ID é obrigatório');
    }

    try {
      const endpoint = `/v9/projects/${this.projectId}/env`;

      await this.makeRequest(endpoint, {
        method: 'POST',
        body: JSON.stringify({
          key,
          value,
          target,
          type,
        }),
      });

      return {
        success: true,
        message: `Variável ${key} configurada com sucesso`,
      };
    } catch (error) {
      logger.error(`Erro ao configurar variável ${key}`, error);
      return {
        success: false,
        message: `Erro ao configurar ${key}: ${error instanceof Error ? error.message : 'Erro desconhecido'}`,
      };
    }
  }

  /**
   * Remove uma variável de ambiente
   */
  async deleteEnvironmentVariable(envId: string): Promise<{ success: boolean; message: string }> {
    if (!this.projectId) {
      throw new Error('Project ID é obrigatório');
    }

    try {
      const endpoint = `/v9/projects/${this.projectId}/env/${envId}`;

      await this.makeRequest(endpoint, {
        method: 'DELETE',
      });

      return {
        success: true,
        message: `Variável removida com sucesso`,
      };
    } catch (error) {
      logger.error(`Erro ao remover variável ${envId}`, error);
      return {
        success: false,
        message: `Erro ao remover variável: ${error instanceof Error ? error.message : 'Erro desconhecido'}`,
      };
    }
  }

  /**
   * Configura múltiplas variáveis de ambiente de uma vez
   */
  async setMultipleEnvironmentVariables(
    variables: Array<{
      key: string;
      value: string;
      target?: ('production' | 'preview' | 'development')[];
      type?: 'system' | 'secret' | 'encrypted' | 'plain';
    }>
  ): Promise<{
    success: boolean;
    results: Array<{ key: string; success: boolean; message: string }>;
    summary: { configured: number; failed: number; skipped: number };
  }> {
    const results: Array<{ key: string; success: boolean; message: string }> = [];
    let configured = 0;
    let failed = 0;
    let skipped = 0;

    // Verificar variáveis existentes
    const existingVars = await this.getEnvironmentVariables();
    const existingKeys = existingVars.map(env => env.key);

    for (const variable of variables) {
      try {
        // Verificar se já existe
        if (existingKeys.includes(variable.key)) {
          results.push({
            key: variable.key,
            success: true,
            message: 'Já existe (pulado)',
          });
          skipped++;
          continue;
        }

        // Configurar variável
        const result = await this.setEnvironmentVariable(
          variable.key,
          variable.value,
          variable.target,
          variable.type
        );

        results.push({
          key: variable.key,
          success: result.success,
          message: result.message,
        });

        if (result.success) {
          configured++;
        } else {
          failed++;
        }

        // Delay para evitar rate limiting
        await new Promise(resolve => setTimeout(resolve, 200));
      } catch (error) {
        results.push({
          key: variable.key,
          success: false,
          message: `Erro: ${error instanceof Error ? error.message : 'Erro desconhecido'}`,
        });
        failed++;
      }
    }

    return {
      success: failed === 0,
      results,
      summary: { configured, failed, skipped },
    };
  }

  /**
   * Obtém status de build de um deployment
   */
  async getBuildStatus(deploymentId: string): Promise<{
    status: string;
    logs: string[];
    duration?: number | undefined;
  }> {
    const deployment = await this.getDeployment(deploymentId);
    const logs = await this.getDeploymentLogs(deploymentId, { limit: 100 });

    const buildLogs = logs.filter(log => log.source === 'build').map(log => log.message);

    const duration =
      deployment.ready && deployment.buildingAt
        ? deployment.ready - deployment.buildingAt
        : undefined;

    return {
      status: deployment.state,
      logs: buildLogs,
      duration,
    };
  }

  /**
   * Verifica se há erros recentes nos deployments
   */
  async checkRecentErrors(hours = 24): Promise<{
    hasErrors: boolean;
    errorCount: number;
    errors: Array<{
      deploymentId: string;
      timestamp: number;
      message: string;
      url?: string;
    }>;
  }> {
    const since = Date.now() - hours * 60 * 60 * 1000;
    const deployments = await this.getDeployments(50);

    const errorDeployments = deployments.filter(d => d.state === 'ERROR' && d.created > since);

    const errors = [];
    for (const deployment of errorDeployments) {
      try {
        const logs = await this.getDeploymentLogs(deployment.uid, {
          limit: 10,
          direction: 'backward',
        });

        const errorLogs = logs.filter(log => log.level === 'error');
        if (errorLogs.length > 0 && errorLogs[0]) {
          errors.push({
            deploymentId: deployment.uid,
            timestamp: deployment.created,
            message: errorLogs[0].message,
            url: deployment.url,
          });
        }
      } catch (error) {
        logger.warn(`Erro ao obter logs do deployment ${deployment.uid}`, error);
      }
    }

    return {
      hasErrors: errors.length > 0,
      errorCount: errors.length,
      errors,
    };
  }
}

/**
 * Serviço de monitoramento Vercel para Excel Copilot
 */
export class VercelMonitoringService {
  private client: VercelClient;

  constructor(apiToken: string, teamId?: string, projectId?: string) {
    this.client = new VercelClient({
      apiToken,
      ...(teamId && { teamId }),
      ...(projectId && { projectId }),
    });
  }

  /**
   * Obtém status geral do projeto
   */
  async getProjectStatus(): Promise<{
    status: 'healthy' | 'degraded' | 'down';
    lastDeployment: VercelDeployment | null;
    recentErrors: number;
    uptime: number;
    message: string;
  }> {
    try {
      const deployments = await this.client.getDeployments(10);
      const lastDeployment = deployments[0] || null;

      const errorCheck = await this.client.checkRecentErrors(24);

      let status: 'healthy' | 'degraded' | 'down' = 'healthy';
      let message = 'Sistema funcionando normalmente';

      if (!lastDeployment) {
        status = 'down';
        message = 'Nenhum deployment encontrado';
      } else if (lastDeployment.state === 'ERROR') {
        status = 'down';
        message = 'Último deployment falhou';
      } else if (errorCheck.errorCount > 3) {
        status = 'degraded';
        message = `${errorCheck.errorCount} erros nas últimas 24h`;
      } else if (lastDeployment.state === 'BUILDING') {
        status = 'degraded';
        message = 'Deploy em andamento';
      }

      // Calcular uptime baseado no último deployment bem-sucedido
      const uptime =
        lastDeployment && lastDeployment.state === 'READY'
          ? Date.now() - lastDeployment.created
          : 0;

      return {
        status,
        lastDeployment,
        recentErrors: errorCheck.errorCount,
        uptime,
        message,
      };
    } catch (error) {
      logger.error('Erro ao obter status do projeto Vercel', error);
      return {
        status: 'down',
        lastDeployment: null,
        recentErrors: 0,
        uptime: 0,
        message: 'Erro ao conectar com Vercel API',
      };
    }
  }

  /**
   * Obtém métricas de performance das últimas 24h
   */
  async getPerformanceMetrics(): Promise<{
    requests: number;
    errors: number;
    errorRate: number;
    averageResponseTime: number;
    bandwidth: number;
    cacheHitRate: number;
  }> {
    try {
      const since = Date.now() - 24 * 60 * 60 * 1000; // 24h atrás
      const metrics = await this.client.getAnalytics({
        since,
        granularity: 'hour',
      });

      const totals = metrics.reduce(
        (acc, metric) => ({
          requests: (acc.requests || 0) + (metric.requests || 0),
          errors: (acc.errors || 0) + (metric.errors || 0),
          bandwidth: (acc.bandwidth || 0) + (metric.bandwidth || 0),
          duration: (acc.duration || 0) + (metric.duration || 0),
          cacheHits: (acc.cacheHits || 0) + (metric.cache?.hits || 0),
          cacheMisses: (acc.cacheMisses || 0) + (metric.cache?.misses || 0),
        }),
        { requests: 0, errors: 0, bandwidth: 0, duration: 0, cacheHits: 0, cacheMisses: 0 }
      );

      const errorRate =
        (totals.requests || 0) > 0 ? ((totals.errors || 0) / (totals.requests || 1)) * 100 : 0;
      const averageResponseTime =
        (totals.requests || 0) > 0 ? (totals.duration || 0) / (totals.requests || 1) : 0;
      const totalCacheRequests = (totals.cacheHits || 0) + (totals.cacheMisses || 0);
      const cacheHitRate =
        totalCacheRequests > 0 ? ((totals.cacheHits || 0) / totalCacheRequests) * 100 : 0;

      return {
        requests: totals.requests || 0,
        errors: totals.errors || 0,
        errorRate: Math.round(errorRate * 100) / 100,
        averageResponseTime: Math.round(averageResponseTime),
        bandwidth: totals.bandwidth || 0,
        cacheHitRate: Math.round(cacheHitRate * 100) / 100,
      };
    } catch (error) {
      logger.error('Erro ao obter métricas de performance', error);
      return {
        requests: 0,
        errors: 0,
        errorRate: 0,
        averageResponseTime: 0,
        bandwidth: 0,
        cacheHitRate: 0,
      };
    }
  }

  /**
   * Obtém logs recentes filtrados por critérios
   */
  async getFilteredLogs(
    options: {
      level?: 'info' | 'warn' | 'error';
      source?: 'build' | 'static' | 'lambda' | 'edge';
      limit?: number;
      search?: string;
    } = {}
  ): Promise<VercelLog[]> {
    try {
      const deployments = await this.client.getDeployments(5);
      const allLogs: VercelLog[] = [];

      for (const deployment of deployments) {
        try {
          const logs = await this.client.getDeploymentLogs(deployment.uid, {
            limit: options.limit || 50,
            direction: 'backward',
          });
          allLogs.push(...logs);
        } catch (error) {
          logger.warn(`Erro ao obter logs do deployment ${deployment.uid}`, error);
        }
      }

      // Filtrar logs
      let filteredLogs = allLogs;

      if (options.level) {
        filteredLogs = filteredLogs.filter(log => log.level === options.level);
      }

      if (options.source) {
        filteredLogs = filteredLogs.filter(log => log.source === options.source);
      }

      if (options.search) {
        const searchTerm = options.search.toLowerCase();
        filteredLogs = filteredLogs.filter(log => log.message.toLowerCase().includes(searchTerm));
      }

      // Ordenar por timestamp (mais recente primeiro)
      filteredLogs.sort((a, b) => b.timestamp - a.timestamp);

      return filteredLogs.slice(0, options.limit || 50);
    } catch (error) {
      logger.error('Erro ao obter logs filtrados', error);
      return [];
    }
  }
}
