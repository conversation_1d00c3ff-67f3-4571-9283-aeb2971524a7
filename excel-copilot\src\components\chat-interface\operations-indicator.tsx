'use client';

import { motion } from 'framer-motion';
import { Check } from 'lucide-react';

import { SIZES, TYPOGRAPHY, COLORS } from '@/lib/design-tokens';
import { cn } from '@/lib/utils';

// Componente para o indicador de operação aplicada
export function OperationsAppliedIndicator() {
  return (
    <motion.div
      initial={{ opacity: 0, y: 5 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: 0.2 }}
      className={cn('mt-1', TYPOGRAPHY.size.xs, COLORS.intent.success.base, 'flex items-center')}
    >
      <Check className={cn(SIZES.icon.xs, 'mr-1')} aria-hidden="true" />
      <span>Operações aplicadas à planilha</span>
    </motion.div>
  );
}
