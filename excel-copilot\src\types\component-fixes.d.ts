/**
 * Correções específicas para problemas de tipagem de componentes
 * Este arquivo resolve problemas específicos com componentes React e JSX
 */

import React from 'react';

// Declaração de módulo para corrigir problemas com Input
declare module '@/components/ui/input' {
  interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
    variant?: 'default' | 'outline' | 'ghost' | 'error';
    inputSize?: 'sm' | 'md' | 'lg';
    fieldSize?: 'sm' | 'md' | 'lg';
    wrapperClassName?: string;
  }

  const Input: React.ForwardRefExoticComponent<InputProps & React.RefAttributes<HTMLInputElement>>;

  export { Input };
  export default Input;
}

// Declaração de módulo para corrigir problemas com ErrorMessage
declare module '@/components/ui/error-message' {
  type ErrorType = 'error' | 'warning' | 'info' | 'success';

  interface ErrorMessageProps {
    type?: ErrorType;
    message: string;
    description?: string | undefined;
    className?: string;
    iconClassName?: string;
    _iconClassName?: string;
    _icon?: React.ReactNode;
  }

  const ErrorMessage: React.ForwardRefExoticComponent<
    ErrorMessageProps & React.RefAttributes<HTMLDivElement>
  >;

  export { ErrorMessage };
  export default ErrorMessage;
}

// Declaração global para resolver problemas com JSX
declare global {
  namespace JSX {
    interface IntrinsicElements {
      [elemName: string]: any;
    }
  }
}
