#!/usr/bin/env node

/**
 * Script para validar o arquivo .env.production
 * Análise abrangente de implementação e compatibilidade com Vercel
 */

/* eslint-disable @typescript-eslint/no-require-imports */
/* eslint-disable no-console */

const fs = require('fs');
const path = require('path');

// Carregar variáveis do .env.production
const envPath = path.join(__dirname, '.env.production');

if (!fs.existsSync(envPath)) {
  console.error('❌ Arquivo .env.production não encontrado');
  process.exit(1);
}

// Ler e parsear o arquivo
const envContent = fs.readFileSync(envPath, 'utf8');
const envVars = {};

envContent.split('\n').forEach(line => {
  const trimmed = line.trim();
  if (trimmed && !trimmed.startsWith('#')) {
    const [key, ...valueParts] = trimmed.split('=');
    if (key && valueParts.length > 0) {
      envVars[key] = valueParts.join('=').replace(/^["']|["']$/g, '');
    }
  }
});

console.log('🔍 ANÁLISE ABRANGENTE DO .env.production\n');

// Validações básicas
const requiredVars = [
  'NODE_ENV',
  'AUTH_NEXTAUTH_SECRET',
  'AUTH_NEXTAUTH_URL',
  'DB_DATABASE_URL',
  'SUPABASE_URL',
  'STRIPE_SECRET_KEY',
];

let allValid = true;

console.log('✅ VARIÁVEIS OBRIGATÓRIAS:');
requiredVars.forEach(varName => {
  if (envVars[varName]) {
    console.log(`  ✅ ${varName}: Configurada`);
  } else {
    console.log(`  ❌ ${varName}: AUSENTE`);
    allValid = false;
  }
});

console.log('\n🔗 INTEGRAÇÕES MCP:');
const mcpVars = [
  'MCP_VERCEL_TOKEN',
  'MCP_VERCEL_PROJECT_ID',
  'MCP_VERCEL_TEAM_ID',
  'MCP_LINEAR_API_KEY',
  'MCP_GITHUB_TOKEN',
];

mcpVars.forEach(varName => {
  if (envVars[varName]) {
    console.log(`  ✅ ${varName}: Configurada`);
  } else {
    console.log(`  ⚠️  ${varName}: Ausente (opcional)`);
  }
});

console.log('\n🤖 INTELIGÊNCIA ARTIFICIAL:');
const aiVars = ['AI_ENABLED', 'AI_USE_MOCK', 'AI_VERTEX_PROJECT_ID'];
aiVars.forEach(varName => {
  if (envVars[varName]) {
    console.log(`  ✅ ${varName}: ${envVars[varName]}`);
  } else {
    console.log(`  ⚠️  ${varName}: Não configurada`);
  }
});

console.log('\n🔒 VALIDAÇÕES DE SEGURANÇA:');

// Verificar NODE_ENV
if (envVars.NODE_ENV === 'production') {
  console.log('  ✅ NODE_ENV: production (correto)');
} else {
  console.log('  ❌ NODE_ENV: Deve ser "production"');
  allValid = false;
}

// Verificar URLs de produção
if (envVars.AUTH_NEXTAUTH_URL && !envVars.AUTH_NEXTAUTH_URL.includes('localhost')) {
  console.log('  ✅ AUTH_NEXTAUTH_URL: URL de produção');
} else {
  console.log('  ❌ AUTH_NEXTAUTH_URL: Não pode ser localhost em produção');
  allValid = false;
}

// Verificar força do secret
if (envVars.AUTH_NEXTAUTH_SECRET && envVars.AUTH_NEXTAUTH_SECRET.length >= 32) {
  console.log(
    `  ✅ AUTH_NEXTAUTH_SECRET: ${envVars.AUTH_NEXTAUTH_SECRET.length} caracteres (seguro)`
  );
} else {
  console.log('  ⚠️  AUTH_NEXTAUTH_SECRET: Deveria ter pelo menos 32 caracteres');
}

// Verificar providers OAuth
const hasOAuth = envVars.AUTH_GOOGLE_CLIENT_ID || envVars.AUTH_GITHUB_CLIENT_ID;
if (hasOAuth) {
  console.log('  ✅ OAuth: Pelo menos um provider configurado');
} else {
  console.log('  ❌ OAuth: Pelo menos um provider é obrigatório em produção');
  allValid = false;
}

// Verificar Stripe LIVE
if (envVars.STRIPE_SECRET_KEY && envVars.STRIPE_SECRET_KEY.startsWith('sk_live_')) {
  console.log('  ✅ Stripe: Chaves LIVE de produção');
} else if (envVars.STRIPE_SECRET_KEY && envVars.STRIPE_SECRET_KEY.startsWith('sk_test_')) {
  console.log('  ⚠️  Stripe: Usando chaves de teste (não recomendado para produção)');
} else {
  console.log('  ❌ Stripe: Chave secreta não configurada');
}

console.log('\n' + '='.repeat(60));

if (allValid) {
  console.log('🎉 VALIDAÇÃO CONCLUÍDA: Todas as configurações obrigatórias estão corretas!');
  console.log('\n📋 PRÓXIMOS PASSOS:');
  console.log('1. Configure essas variáveis no Vercel: vercel env add < .env.production');
  console.log('2. Verifique se o arquivo está no .gitignore');
  console.log('3. Teste as integrações após o deploy');
} else {
  console.log('❌ VALIDAÇÃO FALHOU: Corrija os problemas identificados acima');
  process.exit(1);
}
