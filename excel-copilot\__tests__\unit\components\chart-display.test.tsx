/**
 * @jest-environment jsdom
 */

import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { server } from '../../mocks/server';
import { ChartDisplay } from '../../mocks/chart-display-mock';

// Mock o componente real com nossa versão simulada
jest.mock('@/components/chart-display', () => ({
  ChartDisplay: ChartDisplay,
}));

describe('ChartDisplay Component', () => {
  beforeAll(() => {
    server.listen();
  });

  afterEach(() => {
    server.resetHandlers();
  });

  afterAll(() => {
    server.close();
  });

  // Dados de exemplo para os testes
  const mockData = {
    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May'],
    datasets: [
      {
        label: 'Sales 2023',
        data: [650, 590, 800, 810, 560],
        backgroundColor: ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF'],
        borderColor: '#7e57c2',
        borderWidth: 1,
      },
    ],
  };

  test('renders with correct data', () => {
    render(<ChartDisplay chartData={mockData} />);

    // Verificar se o rótulo do dataset aparece no documento
    expect(screen.getByText('Sales 2023')).toBeInTheDocument();

    // Verificar se os labels do eixo X são renderizados
    mockData.labels.forEach(label => {
      expect(screen.getByText(label)).toBeInTheDocument();
    });
  });

  test('renders with custom height', () => {
    const customHeight = 400;
    render(<ChartDisplay chartData={mockData} height={customHeight} />);

    const container = screen.getByTestId('chart-display');
    expect(container).toHaveStyle({ height: `${customHeight}px` });
  });

  test('renders with custom width', () => {
    const customWidth = 800;
    render(<ChartDisplay chartData={mockData} width={customWidth} />);

    const container = screen.getByTestId('chart-display');
    expect(container).toHaveStyle({ width: `${customWidth}px` });
  });

  test('applies custom className', () => {
    render(<ChartDisplay chartData={mockData} className="custom-chart" />);

    const container = screen.getByTestId('chart-display');
    expect(container).toHaveClass('custom-chart');
  });

  test('shows error message when data is insufficient', () => {
    const emptyData = {
      labels: [],
      datasets: [],
    };

    render(<ChartDisplay chartData={emptyData} />);
    expect(screen.getByText(/dados insuficientes/i)).toBeInTheDocument();
  });

  test('renders chart type correctly', () => {
    render(<ChartDisplay chartData={mockData} chartType="bar" />);

    // Verificar se o gráfico está presente
    expect(screen.getByTestId('chart-display')).toBeInTheDocument();

    // Verificar se o label do dataset está presente
    expect(screen.getByText(mockData.datasets[0]?.label || '')).toBeInTheDocument();
  });
});
