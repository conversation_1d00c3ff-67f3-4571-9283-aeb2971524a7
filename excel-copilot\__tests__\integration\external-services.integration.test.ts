/**
 * @jest-environment node
 */

import axios from 'axios';
import * as fs from 'fs';
import * as path from 'path';
import { ENV } from '@/config/unified-environment';

// Função para verificar se uma URL está online
async function checkUrl(url: string, timeout = 5000): Promise<boolean> {
  try {
    await axios.get(url, {
      timeout,
      validateStatus: () => true, // Aceitar qualquer status para confirmar que o serviço está online
    });
    return true;
  } catch (error) {
    return false;
  }
}

// Verificar se um arquivo existe
function fileExists(filePath: string): boolean {
  try {
    return fs.existsSync(filePath);
  } catch {
    return false;
  }
}

describe('Integração com Serviços Externos', () => {
  // Verificar se estamos em um ambiente de CI
  const isCI = process.env.CI === 'true';

  // Verificar credenciais do Vertex AI
  const hasVertexCredentials = fileExists(path.join(process.cwd(), 'vertex-credentials.json'));

  // Definir quais testes devem ser pulados em CI
  const testIfNotCI = isCI ? test.skip : test;

  describe('Vertex AI', () => {
    // Testes básicos de configuração
    test('verifica configuração básica do Vertex AI', () => {
      // Pular o teste se estiver utilizando mock
      if (ENV.FEATURES.USE_MOCK_AI) {
        return;
      }

      // Verificar se as configurações existem
      if (hasVertexCredentials) {
        expect(true).toBe(true); // Arquivo de credenciais existe
      } else {
        // Se não tem arquivo de credenciais, deve ter variáveis de ambiente
        expect(process.env.VERTEX_AI_PROJECT_ID).toBeDefined();
        expect(process.env.VERTEX_AI_LOCATION).toBeDefined();
      }
    });

    // Teste de conectividade - apenas em ambientes não-CI
    testIfNotCI(
      'verifica conectividade com Vertex AI',
      async () => {
        // Pular o teste se estiver utilizando mock
        if (ENV.FEATURES.USE_MOCK_AI) {
          return;
        }

        // Pular o teste se não tivermos credenciais
        if (!hasVertexCredentials && !process.env.VERTEX_AI_PROJECT_ID) {
          return;
        }

        // Verificar conectividade com o Vertex AI
        // Vamos verificar se o endpoint está acessível
        const apiUrl = `https://${process.env.VERTEX_AI_LOCATION || 'us-central1'}-aiplatform.googleapis.com/v1/projects`;
        const isOnline = await checkUrl(apiUrl);

        expect(isOnline).toBe(true);
      },
      10000
    ); // Timeout maior para testes de rede
  });

  describe('OAuth Providers', () => {
    test('verifica configuração do Google OAuth', () => {
      // Pular se SKIP_AUTH_PROVIDERS está ativado
      if (process.env.SKIP_AUTH_PROVIDERS === 'true') {
        return;
      }

      expect(process.env.GOOGLE_CLIENT_ID).toBeDefined();
      expect(process.env.GOOGLE_CLIENT_SECRET).toBeDefined();

      // Verificar formato do Client ID
      if (process.env.GOOGLE_CLIENT_ID) {
        expect(process.env.GOOGLE_CLIENT_ID).toMatch(
          /^[0-9]+-[a-z0-9]+\.apps\.googleusercontent\.com$/
        );
      }
    });

    test('verifica configuração do GitHub OAuth', () => {
      // Pular se SKIP_AUTH_PROVIDERS está ativado
      if (process.env.SKIP_AUTH_PROVIDERS === 'true') {
        return;
      }

      expect(process.env.GITHUB_CLIENT_ID).toBeDefined();
      expect(process.env.GITHUB_CLIENT_SECRET).toBeDefined();
    });

    // Teste de conectividade - apenas em ambientes não-CI
    testIfNotCI(
      'verifica conectividade com servidores OAuth',
      async () => {
        // Verificar conectividade com Google
        const googleAuthUrl = 'https://accounts.google.com/.well-known/openid-configuration';
        const googleIsOnline = await checkUrl(googleAuthUrl);

        // Verificar conectividade com GitHub
        const githubAuthUrl = 'https://github.com/login/oauth/authorize';
        const githubIsOnline = await checkUrl(githubAuthUrl);

        expect(googleIsOnline).toBe(true);
        expect(githubIsOnline).toBe(true);
      },
      10000
    ); // Timeout maior para testes de rede
  });

  describe('Stripe (Pagamentos)', () => {
    test('verifica configuração do Stripe', () => {
      // Em produção, Stripe deve estar configurado
      if (process.env.NODE_ENV === 'production') {
        expect(process.env.STRIPE_SECRET_KEY).toBeDefined();
        expect(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY).toBeDefined();
        expect(process.env.STRIPE_WEBHOOK_SECRET).toBeDefined();

        // Verificar formatos das chaves
        if (process.env.STRIPE_SECRET_KEY) {
          expect(process.env.STRIPE_SECRET_KEY).toMatch(/^(sk_test_|sk_live_)/);
        }

        if (process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY) {
          expect(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY).toMatch(/^(pk_test_|pk_live_)/);
        }
      }
    });

    // Teste de conectividade - apenas em ambientes não-CI
    testIfNotCI(
      'verifica conectividade com Stripe',
      async () => {
        // Pular teste se não temos chaves do Stripe
        if (!process.env.STRIPE_SECRET_KEY) {
          return;
        }

        // Verificar conectividade com Stripe
        const stripeApiUrl = 'https://api.stripe.com/v1/health';
        const isOnline = await checkUrl(stripeApiUrl);

        expect(isOnline).toBe(true);
      },
      10000
    ); // Timeout maior para testes de rede
  });

  describe('Banco de Dados', () => {
    test('verifica configuração do banco de dados', () => {
      expect(process.env.DATABASE_URL).toBeDefined();

      if (process.env.DATABASE_URL) {
        // Verificar formato da URL de banco de dados
        expect(process.env.DATABASE_URL).toMatch(/^(mysql|postgresql):\/\//);
      }
    });
  });
});
