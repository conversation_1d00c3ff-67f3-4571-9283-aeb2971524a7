# Arquitetura de IA do Excel Copilot

Este documento descreve a arquitetura unificada de integração com Inteligência Artificial no Excel Copilot, detalhando os componentes, fluxos de dados e responsabilidades.

## Visão Geral da Arquitetura

O Excel Copilot utiliza uma arquitetura de IA em camadas que integra o Vercel AI SDK com o serviço personalizado Google Gemini. Esta abordagem combina a facilidade de uso e recursos avançados do Vercel AI SDK com o controle e personalização do nosso serviço Gemini.

```
┌─────────────────┐     ┌───────────────┐     ┌───────────────────┐
│  Componentes UI │ ──▶ │ Vercel AI SDK │ ──▶ │ AI Adapter Layer  │
└─────────────────┘     └───────────────┘     └───────────────────┘
                                                       │
┌─────────────────┐                           ┌───────────────────┐
│  Excel Context  │ ◀─────────────────────── │ GeminiService     │
└─────────────────┘                           └───────────────────┘
                                                       │
                                              ┌───────────────────┐
                                              │ Google Gemini API │
                                              └───────────────────┘
```

## Componentes Principais

### 1. Camada de Interface (UI)

- **ChatInterface**: Componente React que utiliza o hook `useChat` do Vercel AI SDK para gerenciar o estado da conversa.
- Responsabilidades:
  - Gerenciar o estado do chat (mensagens, input, carregamento)
  - Fornecer interface para o usuário interagir com a IA
  - Exibir respostas e atualizações em tempo real

### 2. Camada de Adaptação (AI Adapter)

- **AIAdapter**: Ponte entre o Vercel AI SDK e o GeminiService
- Localização: `src/lib/ai/ai-adapter.ts`
- Responsabilidades:
  - Converter mensagens do formato Vercel AI para o formato Gemini
  - Normalizar tratamento de erros
  - Extrair metadados específicos do Excel das respostas
  - Fornecer streaming de respostas compatível com o Vercel AI SDK

### 3. Camada de Serviço (GeminiService)

- **GeminiService**: Serviço singleton para comunicação com a API do Google Gemini
- Localização: `src/server/ai/gemini-service.ts`
- Responsabilidades:
  - Gerenciar conexão com a API do Google Gemini
  - Implementar cache e otimizações
  - Fornecer mecanismos de fallback e mock para desenvolvimento
  - Processamento avançado de contexto específico do Excel

### 4. APIs

- **API de Chat**: Endpoint compatível com o Vercel AI SDK
- Localização: `src/app/api/chat/route.ts`
- Responsabilidades:
  - Validar requisições
  - Enriquecer contexto com dados de planilhas
  - Processar mensagens via AIAdapter
  - Streaming de respostas

## Fluxo de Dados

1. O usuário envia uma mensagem pelo `ChatInterface`
2. O hook `useChat` do Vercel AI faz uma requisição à API de chat
3. A API de chat enriquece o contexto com dados de planilhas
4. O `AIAdapter` converte a mensagem para o formato compatível com o `GeminiService`
5. O `GeminiService` processa a mensagem e gera uma resposta via Google Gemini
6. A resposta é convertida em um stream para o Vercel AI SDK
7. O `ChatInterface` recebe e exibe a resposta em tempo real

## Tratamento de Erros

Implementamos um sistema unificado de tratamento de erros que:

1. Define tipos de erro consistentes (`GeminiErrorType` e `AIAdapterError`)
2. Mapeia erros da API Gemini para o formato do Vercel AI SDK
3. Fornece mensagens de erro amigáveis para o usuário
4. Registra erros para monitoramento e depuração

## Desempenho e Otimizações

- **Streaming de Respostas**: Exibição progressiva para melhor experiência do usuário
- **Cache Multinível**:
  - Cache no servidor (GeminiService)
  - Cache no cliente (Vercel AI SDK)
- **Tratamento de Contexto**: Processo otimizado para inclusão de dados de planilhas

## Configurações e Personalização

As configurações do sistema de IA são gerenciadas através de:

1. Variáveis de ambiente para chaves de API e endpoints
2. Configurações de usuário para temperatura de geração
3. Sistema de fallback para desenvolvimento e testes

## Segurança

- Validação de entrada para evitar injeção de prompts
- Filtragem de conteúdo prejudicial
- Mecanismos de limitação de taxa
- Sanitização de respostas da IA

## Evolução Futura

A arquitetura foi projetada para permitir:

1. Troca simples do provedor de IA (substituir Google Gemini por outra solução)
2. Adição de recursos de análise avançada
3. Integração com sistemas de feedback do usuário para melhoria contínua
4. Implementação de fine-tuning específico para operações Excel
