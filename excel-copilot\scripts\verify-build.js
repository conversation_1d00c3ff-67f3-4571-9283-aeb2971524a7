#!/usr/bin/env node

/**
 * Script para verificar e diagnosticar problemas de build
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Cores para output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

console.log(
  `${colors.cyan}🔍 Verificando configuração de build do Excel Copilot...${colors.reset}\n`
);

// Função para verificar se arquivo existe
function checkFile(filePath, description) {
  const exists = fs.existsSync(filePath);
  const status = exists ? `${colors.green}✅` : `${colors.red}❌`;
  console.log(`${status} ${description}: ${filePath}${colors.reset}`);
  return exists;
}

// Função para executar comando e capturar output
function safeExec(command, description) {
  try {
    console.log(`${colors.blue}🔄 ${description}...${colors.reset}`);
    const output = execSync(command, { encoding: 'utf8', stdio: 'pipe' });
    console.log(`${colors.green}✅ ${description} - OK${colors.reset}`);
    return { success: true, output };
  } catch (error) {
    console.log(`${colors.red}❌ ${description} - ERRO${colors.reset}`);
    console.log(`${colors.yellow}   ${error.message}${colors.reset}`);
    return { success: false, error: error.message };
  }
}

// Verificações de arquivos essenciais
console.log(`${colors.magenta}📁 Verificando arquivos de configuração...${colors.reset}`);
const configFiles = [
  ['package.json', 'Configuração do projeto'],
  ['next.config.js', 'Configuração do Next.js'],
  ['tsconfig.json', 'Configuração do TypeScript'],
  ['.eslintrc.json', 'Configuração do ESLint'],
  ['.babelrc', 'Configuração do Babel'],
  ['tailwind.config.ts', 'Configuração do Tailwind'],
];

let allFilesExist = true;
configFiles.forEach(([file, desc]) => {
  if (!checkFile(file, desc)) {
    allFilesExist = false;
  }
});

// Verificar dependências críticas
console.log(`\n${colors.magenta}📦 Verificando dependências críticas...${colors.reset}`);
const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
const criticalDeps = ['next', 'react', 'react-dom', 'typescript', '@types/node', '@types/react'];

criticalDeps.forEach(dep => {
  const hasInDeps = packageJson.dependencies && packageJson.dependencies[dep];
  const hasInDevDeps = packageJson.devDependencies && packageJson.devDependencies[dep];
  const exists = hasInDeps || hasInDevDeps;
  const status = exists ? `${colors.green}✅` : `${colors.red}❌`;
  const version = hasInDeps
    ? packageJson.dependencies[dep]
    : hasInDevDeps
      ? packageJson.devDependencies[dep]
      : 'N/A';
  console.log(`${status} ${dep}: ${version}${colors.reset}`);
});

// Verificar estrutura de diretórios
console.log(`\n${colors.magenta}🗂️  Verificando estrutura de diretórios...${colors.reset}`);
const requiredDirs = ['src', 'src/app', 'src/components', 'src/lib', 'public', 'prisma'];

requiredDirs.forEach(dir => {
  checkFile(dir, `Diretório ${dir}`);
});

// Executar verificações de build
console.log(`\n${colors.magenta}🔧 Executando verificações de build...${colors.reset}`);

// Verificação de tipos TypeScript
const typeCheck = safeExec('npx tsc --noEmit --skipLibCheck', 'Verificação de tipos TypeScript');

// Verificação de lint
const lintCheck = safeExec('npm run lint -- --max-warnings 0', 'Verificação de linting');

// Verificação de build (dry run)
console.log(`\n${colors.magenta}🏗️  Testando build...${colors.reset}`);
const buildCheck = safeExec('npm run build', 'Build do projeto');

// Resumo final
console.log(`\n${colors.cyan}📊 Resumo da verificação:${colors.reset}`);
console.log(
  `   Arquivos de configuração: ${allFilesExist ? `${colors.green}OK` : `${colors.red}PROBLEMAS`}${colors.reset}`
);
console.log(
  `   Verificação de tipos: ${typeCheck.success ? `${colors.green}OK` : `${colors.red}PROBLEMAS`}${colors.reset}`
);
console.log(
  `   Verificação de lint: ${lintCheck.success ? `${colors.green}OK` : `${colors.yellow}AVISOS`}${colors.reset}`
);
console.log(
  `   Build do projeto: ${buildCheck.success ? `${colors.green}OK` : `${colors.red}PROBLEMAS`}${colors.reset}`
);

if (!allFilesExist || !typeCheck.success || !buildCheck.success) {
  console.log(`\n${colors.yellow}💡 Sugestões para correção:${colors.reset}`);
  console.log(`   1. Execute 'npm run clean:all' para limpar caches`);
  console.log(`   2. Execute 'npm install' para reinstalar dependências`);
  console.log(`   3. Verifique os erros específicos acima`);
  console.log(`   4. Execute 'npm run verify-build' novamente`);
  process.exit(1);
} else {
  console.log(
    `\n${colors.green}🎉 Todas as verificações passaram! O projeto está pronto para build.${colors.reset}`
  );
}
