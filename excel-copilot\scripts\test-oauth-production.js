#!/usr/bin/env node

/**
 * Script para testar e diagnosticar problemas de OAuth em produção
 */

const https = require('https');
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m',
  bold: '\x1b[1m',
};

console.log(`${colors.bold}${colors.blue}🔐 Teste de OAuth em Produção${colors.reset}\n`);

const baseUrl = 'https://excel-copilot-eight.vercel.app';

// Função para fazer requisições HTTP
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const req = https.request(
      url,
      {
        method: 'GET',
        ...options,
        headers: {
          'User-Agent': 'OAuth-Test-Script/1.0',
          ...options.headers,
        },
      },
      res => {
        let data = '';
        res.on('data', chunk => (data += chunk));
        res.on('end', () => {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            body: data,
          });
        });
      }
    );

    req.on('error', reject);
    req.end();
  });
}

async function testOAuth() {
  console.log(`${colors.cyan}📋 1. Testando providers OAuth...${colors.reset}`);

  try {
    const providersResponse = await makeRequest(`${baseUrl}/api/auth/providers`);

    if (providersResponse.statusCode === 200) {
      const providers = JSON.parse(providersResponse.body);
      console.log(`${colors.green}✅ Providers configurados:${colors.reset}`);

      Object.keys(providers).forEach(provider => {
        console.log(`   - ${provider}: ${providers[provider].name}`);
      });
    } else {
      console.log(
        `${colors.red}❌ Erro ao buscar providers: ${providersResponse.statusCode}${colors.reset}`
      );
    }
  } catch (error) {
    console.log(`${colors.red}❌ Erro na requisição de providers: ${error.message}${colors.reset}`);
  }

  console.log(`\n${colors.cyan}📋 2. Testando Google OAuth...${colors.reset}`);

  try {
    const googleResponse = await makeRequest(`${baseUrl}/api/auth/signin/google`, {
      redirect: 'manual',
    });

    console.log(`Status: ${googleResponse.statusCode}`);

    if (googleResponse.headers.location) {
      const location = googleResponse.headers.location;
      console.log(`Redirecionamento: ${location}`);

      if (location.includes('error=google')) {
        console.log(`${colors.red}❌ Erro no Google OAuth detectado${colors.reset}`);
      } else if (location.includes('accounts.google.com')) {
        console.log(`${colors.green}✅ Redirecionamento para Google funcionando${colors.reset}`);
      } else {
        console.log(`${colors.yellow}⚠️  Redirecionamento inesperado${colors.reset}`);
      }
    }
  } catch (error) {
    console.log(`${colors.red}❌ Erro no teste Google: ${error.message}${colors.reset}`);
  }

  console.log(`\n${colors.cyan}📋 3. Testando GitHub OAuth...${colors.reset}`);

  try {
    const githubResponse = await makeRequest(`${baseUrl}/api/auth/signin/github`, {
      redirect: 'manual',
    });

    console.log(`Status: ${githubResponse.statusCode}`);

    if (githubResponse.headers.location) {
      const location = githubResponse.headers.location;
      console.log(`Redirecionamento: ${location}`);

      if (location.includes('error=github')) {
        console.log(`${colors.red}❌ Erro no GitHub OAuth detectado${colors.reset}`);
      } else if (location.includes('github.com')) {
        console.log(`${colors.green}✅ Redirecionamento para GitHub funcionando${colors.reset}`);
      } else {
        console.log(`${colors.yellow}⚠️  Redirecionamento inesperado${colors.reset}`);
      }
    }
  } catch (error) {
    console.log(`${colors.red}❌ Erro no teste GitHub: ${error.message}${colors.reset}`);
  }

  console.log(`\n${colors.cyan}📋 4. Testando configuração de ambiente...${colors.reset}`);

  try {
    const envResponse = await makeRequest(`${baseUrl}/api/auth/check-env`);

    if (envResponse.statusCode === 200) {
      const envData = JSON.parse(envResponse.body);
      console.log(`${colors.green}✅ Configuração de ambiente:${colors.reset}`);

      Object.entries(envData).forEach(([key, value]) => {
        const status = value ? '✅' : '❌';
        console.log(`   ${status} ${key}: ${value ? 'Configurado' : 'Ausente'}`);
      });
    } else {
      console.log(
        `${colors.red}❌ Erro ao verificar ambiente: ${envResponse.statusCode}${colors.reset}`
      );
    }
  } catch (error) {
    console.log(`${colors.red}❌ Erro na verificação de ambiente: ${error.message}${colors.reset}`);
  }

  console.log(`\n${colors.cyan}📋 5. Testando sessão...${colors.reset}`);

  try {
    const sessionResponse = await makeRequest(`${baseUrl}/api/auth/session`);

    if (sessionResponse.statusCode === 200) {
      const session = JSON.parse(sessionResponse.body);

      if (Object.keys(session).length === 0) {
        console.log(`${colors.yellow}⚠️  Nenhuma sessão ativa (esperado)${colors.reset}`);
      } else {
        console.log(`${colors.green}✅ Sessão ativa encontrada${colors.reset}`);
        console.log(`   Usuário: ${session.user?.email || 'N/A'}`);
      }
    } else {
      console.log(
        `${colors.red}❌ Erro ao verificar sessão: ${sessionResponse.statusCode}${colors.reset}`
      );
    }
  } catch (error) {
    console.log(`${colors.red}❌ Erro na verificação de sessão: ${error.message}${colors.reset}`);
  }

  console.log(`\n${colors.bold}${colors.magenta}🎯 Diagnóstico e Recomendações:${colors.reset}\n`);

  console.log(`${colors.cyan}Possíveis causas dos erros OAuth:${colors.reset}`);
  console.log(`1. URLs de callback incorretas no Google/GitHub Console`);
  console.log(`2. Client ID/Secret inválidos ou expirados`);
  console.log(`3. Domínio não autorizado nos providers`);
  console.log(`4. Configuração de CORS ou CSP bloqueando`);

  console.log(`\n${colors.cyan}URLs de callback que devem estar configuradas:${colors.reset}`);
  console.log(`Google: ${baseUrl}/api/auth/callback/google`);
  console.log(`GitHub: ${baseUrl}/api/auth/callback/github`);

  console.log(`\n${colors.cyan}Próximos passos:${colors.reset}`);
  console.log(`1. Verificar configuração no Google Console`);
  console.log(`2. Verificar configuração no GitHub Apps`);
  console.log(`3. Testar URLs de callback manualmente`);
  console.log(`4. Verificar logs da Vercel para erros específicos`);

  console.log(`\n${colors.bold}${colors.green}✅ Teste concluído!${colors.reset}`);
}

// Executar teste
testOAuth().catch(console.error);
