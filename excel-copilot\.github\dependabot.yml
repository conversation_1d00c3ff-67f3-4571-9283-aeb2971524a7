version: 2
updates:
  # Manter dependências npm atualizadas
  - package-ecosystem: 'npm'
    directory: '/'
    schedule:
      interval: 'weekly'
    open-pull-requests-limit: 10
    versioning-strategy: increase
    labels:
      - 'dependencies'
    commit-message:
      prefix: 'deps'
      include: 'scope'
    groups:
      production-dependencies:
        dependency-type: 'production'
        update-types:
          - 'minor'
          - 'patch'
      development-dependencies:
        dependency-type: 'development'
        update-types:
          - 'minor'
          - 'patch'
    ignore:
      # Ignorar atualizações major que podem quebrar a compatibilidade
      - dependency-name: '*'
        update-types: ['major']

  # Manter dependências GitHub Actions atualizadas
  - package-ecosystem: 'github-actions'
    directory: '/'
    schedule:
      interval: 'monthly'
    open-pull-requests-limit: 5
    labels:
      - 'dependencies'
      - 'github-actions'
