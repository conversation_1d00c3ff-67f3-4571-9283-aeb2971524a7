#!/usr/bin/env node

/**
 * Script para corrigir erros <PERSON> (React Server Components) em produção
 * Aplica correções sistemáticas para resolver problemas de 404 e hydratação
 */

const fs = require('fs');
const path = require('path');

// Cores para output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m',
  bold: '\x1b[1m',
};

console.log(
  `${colors.cyan}${colors.bold}🔧 CORREÇÃO DE ERROS RSC - EXCEL COPILOT${colors.reset}\n`
);

let hasErrors = false;
const results = [];
const fixes = [];

/**
 * Verificar se o middleware está configurado corretamente para RSC
 */
function checkMiddlewareRSC() {
  console.log(`${colors.blue}📋 Verificando configuração do middleware para RSC...${colors.reset}`);

  const middlewarePath = path.join(__dirname, '../src/middleware.ts');

  if (!fs.existsSync(middlewarePath)) {
    results.push(`${colors.red}❌ Middleware não encontrado${colors.reset}`);
    hasErrors = true;
    return;
  }

  const content = fs.readFileSync(middlewarePath, 'utf8');

  // Verificar se RSC está excluído do matcher
  if (content.includes('\\?_rsc=')) {
    results.push(`${colors.green}✅ Middleware configurado para excluir RSC${colors.reset}`);
  } else {
    results.push(`${colors.red}❌ Middleware não exclui requisições RSC${colors.reset}`);
    fixes.push('Atualizar matcher do middleware para excluir _rsc');
    hasErrors = true;
  }
}

/**
 * Verificar se o supressor de erros RSC está implementado
 */
function checkRSCErrorSuppressor() {
  console.log(`${colors.blue}📋 Verificando supressor de erros RSC...${colors.reset}`);

  const suppressorPath = path.join(__dirname, '../src/components/rsc-error-suppressor.tsx');
  const layoutPath = path.join(__dirname, '../src/app/layout.tsx');

  if (!fs.existsSync(suppressorPath)) {
    results.push(`${colors.red}❌ Supressor de erros RSC não encontrado${colors.reset}`);
    fixes.push('Criar componente RSCErrorSuppressor');
    hasErrors = true;
    return;
  }

  if (!fs.existsSync(layoutPath)) {
    results.push(`${colors.red}❌ Layout principal não encontrado${colors.reset}`);
    hasErrors = true;
    return;
  }

  const layoutContent = fs.readFileSync(layoutPath, 'utf8');

  if (layoutContent.includes('RSCErrorSuppressor')) {
    results.push(`${colors.green}✅ Supressor de erros RSC implementado no layout${colors.reset}`);
  } else {
    results.push(`${colors.red}❌ Supressor de erros RSC não adicionado ao layout${colors.reset}`);
    fixes.push('Adicionar RSCErrorSuppressor ao layout principal');
    hasErrors = true;
  }
}

/**
 * Verificar configuração do Next.js para RSC
 */
function checkNextConfigRSC() {
  console.log(`${colors.blue}📋 Verificando configuração Next.js para RSC...${colors.reset}`);

  const configPath = path.join(__dirname, '../next.config.js');

  if (!fs.existsSync(configPath)) {
    results.push(`${colors.red}❌ next.config.js não encontrado${colors.reset}`);
    hasErrors = true;
    return;
  }

  const content = fs.readFileSync(configPath, 'utf8');

  // Verificar se pacotes Google foram removidos da transpilação
  if (content.includes('@google-cloud/aiplatform') || content.includes('@google-cloud/vertexai')) {
    results.push(
      `${colors.red}❌ Pacotes Google ainda na transpilação (podem causar erros RSC)${colors.reset}`
    );
    fixes.push('Remover pacotes Google da transpilação');
    hasErrors = true;
  } else {
    results.push(`${colors.green}✅ Pacotes Google removidos da transpilação${colors.reset}`);
  }

  // Verificar configurações experimentais
  if (content.includes('experimental:')) {
    results.push(`${colors.green}✅ Configurações experimentais presentes${colors.reset}`);
  } else {
    results.push(`${colors.yellow}⚠️ Configurações experimentais não encontradas${colors.reset}`);
    fixes.push('Adicionar configurações experimentais para melhor performance RSC');
  }
}

/**
 * Verificar se há problemas de hydratação no layout
 */
function checkHydrationIssues() {
  console.log(`${colors.blue}📋 Verificando problemas de hidratação...${colors.reset}`);

  const layoutPath = path.join(__dirname, '../src/app/layout.tsx');

  if (!fs.existsSync(layoutPath)) {
    results.push(`${colors.red}❌ Layout principal não encontrado${colors.reset}`);
    hasErrors = true;
    return;
  }

  const content = fs.readFileSync(layoutPath, 'utf8');

  // Verificar se há suppressHydrationWarning
  if (content.includes('suppressHydrationWarning')) {
    results.push(`${colors.green}✅ suppressHydrationWarning configurado${colors.reset}`);
  } else {
    results.push(`${colors.yellow}⚠️ suppressHydrationWarning não encontrado${colors.reset}`);
    fixes.push('Adicionar suppressHydrationWarning ao html tag');
  }

  // Verificar se há uso de Suspense
  if (content.includes('Suspense')) {
    results.push(`${colors.green}✅ Suspense implementado para lazy loading${colors.reset}`);
  } else {
    results.push(`${colors.yellow}⚠️ Suspense não encontrado${colors.reset}`);
    fixes.push('Implementar Suspense para componentes lazy');
  }
}

/**
 * Aplicar correções automáticas
 */
function applyAutomaticFixes() {
  console.log(`${colors.blue}🔧 Aplicando correções automáticas...${colors.reset}`);

  let fixesApplied = 0;

  // Correção 1: Atualizar suppressHydrationWarning se necessário
  const layoutPath = path.join(__dirname, '../src/app/layout.tsx');
  if (fs.existsSync(layoutPath)) {
    let layoutContent = fs.readFileSync(layoutPath, 'utf8');

    if (!layoutContent.includes('suppressHydrationWarning')) {
      layoutContent = layoutContent.replace(
        '<html lang="pt-BR"',
        '<html lang="pt-BR" suppressHydrationWarning'
      );

      fs.writeFileSync(layoutPath, layoutContent);
      console.log(`${colors.green}✅ suppressHydrationWarning adicionado ao layout${colors.reset}`);
      fixesApplied++;
    }
  }

  console.log(
    `${colors.green}🎉 ${fixesApplied} correções aplicadas automaticamente${colors.reset}`
  );
}

/**
 * Função principal
 */
function main() {
  console.log(`${colors.blue}🔍 Iniciando diagnóstico de erros RSC...${colors.reset}\n`);

  checkMiddlewareRSC();
  checkRSCErrorSuppressor();
  checkNextConfigRSC();
  checkHydrationIssues();

  console.log(`\n${colors.cyan}📊 RESULTADOS DO DIAGNÓSTICO:${colors.reset}\n`);

  results.forEach(result => {
    console.log(result);
  });

  if (fixes.length > 0) {
    console.log(`\n${colors.yellow}🛠️ CORREÇÕES SUGERIDAS:${colors.reset}\n`);
    fixes.forEach((fix, index) => {
      console.log(`${index + 1}. ${fix}`);
    });
  }

  // Aplicar correções automáticas
  if (hasErrors) {
    console.log(`\n${colors.blue}🔧 Aplicando correções automáticas...${colors.reset}\n`);
    applyAutomaticFixes();
  }

  console.log(`\n${colors.cyan}📋 RESUMO:${colors.reset}`);
  if (hasErrors) {
    console.log(
      `${colors.red}❌ Problemas encontrados que podem estar causando erros RSC${colors.reset}`
    );
    console.log(
      `${colors.blue}💡 Execute novamente após aplicar as correções sugeridas${colors.reset}`
    );
  } else {
    console.log(`${colors.green}✅ Configuração RSC parece estar correta${colors.reset}`);
    console.log(
      `${colors.blue}💡 Se os erros persistirem, verifique as variáveis de ambiente na Vercel${colors.reset}`
    );
  }

  process.exit(hasErrors ? 1 : 0);
}

// Executar script
main();
