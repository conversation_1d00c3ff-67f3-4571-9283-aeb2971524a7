# ======================================
# PRODUÇÃO - Excel Copilot (TEMPLATE)
# ======================================
# Este é um template para configuração de produção
# SUBSTITUA todos os placeholders por suas credenciais reais
# NÃO commite este arquivo com credenciais reais

# ======================================
# Configurações Básicas de Produção
# ======================================
NODE_ENV="production"
DEV_FORCE_PRODUCTION="true"
DEV_DISABLE_VALIDATION="false"

# ======================================
# NextAuth.js - Autenticação
# ======================================
# Gere uma chave secreta: openssl rand -base64 32
AUTH_NEXTAUTH_SECRET="your-production-nextauth-secret-here"
AUTH_NEXTAUTH_URL="https://your-domain.vercel.app"

# ======================================
# OAuth Providers - CONFIGURE SUAS CREDENCIAIS REAIS
# ======================================
# Google OAuth - https://console.cloud.google.com/apis/credentials
AUTH_GOOGLE_CLIENT_ID="your-google-client-id-here"
AUTH_GOOGLE_CLIENT_SECRET="your-google-client-secret-here"

# GitHub OAuth - https://github.com/settings/developers
AUTH_GITHUB_CLIENT_ID="your-github-client-id-here"
AUTH_GITHUB_CLIENT_SECRET="your-github-client-secret-here"

# ======================================
# Banco de Dados - Supabase Produção
# ======================================
DB_DATABASE_URL="your-supabase-production-database-url-here"
DB_DIRECT_URL="your-supabase-production-direct-url-here"
SUPABASE_URL="your-supabase-production-url-here"
SUPABASE_ANON_KEY="your-supabase-production-anon-key-here"
SUPABASE_SERVICE_ROLE_KEY="your-supabase-production-service-role-key-here"
NEXT_PUBLIC_SUPABASE_URL="your-supabase-production-url-here"
NEXT_PUBLIC_SUPABASE_ANON_KEY="your-supabase-production-anon-key-here"

# ======================================
# Vertex AI - Google Cloud Produção
# ======================================
AI_ENABLED="true"
AI_VERTEX_PROJECT_ID="your-vertex-ai-project-id"
AI_VERTEX_LOCATION="us-central1"
AI_VERTEX_MODEL="gemini-2.0-flash-001"
AI_ENABLED="false"

# ======================================
# Stripe - Pagamentos Produção (LIVE KEYS)
# ======================================
STRIPE_SECRET_KEY="your-stripe-live-secret-key"
STRIPE_WEBHOOK_SECRET="your-stripe-live-webhook-secret"
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY="your-stripe-live-publishable-key"
NEXT_PUBLIC_STRIPE_PRICE_MONTHLY="your-stripe-live-monthly-price-id"
NEXT_PUBLIC_STRIPE_PRICE_ANNUAL="your-stripe-live-annual-price-id"

# ======================================
# MCP Integrations - Produção (NOMENCLATURA PADRONIZADA)
# ======================================
# Vercel MCP
MCP_VERCEL_TOKEN="your-vercel-api-token"
MCP_VERCEL_PROJECT_ID="your-vercel-project-id"
MCP_VERCEL_TEAM_ID="your-vercel-team-id"

# Linear MCP
MCP_LINEAR_API_KEY="your-linear-api-key"

# GitHub MCP
MCP_GITHUB_TOKEN="your-github-token"
MCP_GITHUB_OWNER="your-github-username"
MCP_GITHUB_REPO="your-repo-name"

# ======================================
# Configurações da Aplicação
# ======================================
APP_NAME="Excel Copilot"
APP_VERSION="1.0.0"
APP_URL="https://your-domain.vercel.app"
NEXT_PUBLIC_APP_URL="https://your-domain.vercel.app"

# ======================================
# Configurações de Segurança
# ======================================
SECURITY_CSRF_SECRET="your-csrf-secret-here"
# REMOVIDO: NEXT_PUBLIC_DISABLE_CSRF="false"

# ======================================
# Feature Flags - Produção
# ======================================
AI_USE_MOCK="false"
NEXT_PUBLIC_USE_MOCK_AI="false"
AI_USE_MOCK="false"
AUTH_SKIP_PROVIDERS="false"

# ======================================
# Configurações de Cache
# ======================================
AI_CACHE_SIZE="200"
AI_CACHE_TTL="7200"
EXCEL_CACHE_SIZE="100"
EXCEL_CACHE_TTL="1800"
CACHE_DEFAULT_TTL="3600"
