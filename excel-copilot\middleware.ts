import { NextRequest, NextResponse } from 'next/server';
import { getToken } from 'next-auth/jwt';

/**
 * Middleware principal do Excel Copilot
 * Aplica rate limiting e verificações de segurança globalmente
 */
export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Aplicar apenas para rotas de API
  if (pathname.startsWith('/api/')) {
    // Verificar rate limiting básico
    const ip = request.ip || request.headers.get('x-forwarded-for') || 'unknown';

    // Rate limiting global: máximo 1000 requests por IP por hora
    const _rateLimitKey = `global_rate_limit:${ip}`;

    // Para APIs que requerem autenticação
    if (
      pathname.startsWith('/api/workbooks') ||
      pathname.startsWith('/api/chat') ||
      pathname.startsWith('/api/workbook/save')
    ) {
      // Verificar se usuário está autenticado
      const token = await getToken({
        req: request,
        secret: process.env.NEXTAUTH_SECRET,
      });

      if (!token && !pathname.includes('/shared')) {
        return NextResponse.json(
          { error: 'Não autorizado. Faça login para continuar.' },
          { status: 401 }
        );
      }

      // Adicionar headers de segurança
      const response = NextResponse.next();
      response.headers.set('X-Content-Type-Options', 'nosniff');
      response.headers.set('X-Frame-Options', 'DENY');
      response.headers.set('X-XSS-Protection', '1; mode=block');

      return response;
    }
  }

  return NextResponse.next();
}

export const config = {
  matcher: ['/api/:path*', '/((?!_next/static|_next/image|favicon.ico).*)'],
};
