/**
 * Endpoint de health check para autenticação
 * Verifica se todas as configurações estão corretas
 */

import { NextRequest, NextResponse } from 'next/server';

import { ENV } from '@/config/unified-environment';
import { getAuthHealthReport } from '@/lib/auth/validation';
import { logger } from '@/lib/logger';

export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

/**
 * GET /api/auth/health
 * Retorna o status de saúde da configuração de autenticação
 */
export async function GET(request: NextRequest) {
  try {
    logger.info('🔍 Verificação de saúde da autenticação solicitada', {
      ip: request.ip,
      userAgent: request.headers.get('user-agent'),
      timestamp: new Date().toISOString(),
    });

    // Gerar relatório de saúde
    const healthReport = getAuthHealthReport();

    // Determinar status HTTP baseado na saúde geral
    const statusCode = healthReport.overall ? 200 : 503;

    // Preparar resposta (remover informações sensíveis em produção)
    const response = {
      status: healthReport.overall ? 'healthy' : 'unhealthy',
      timestamp: healthReport.timestamp,
      environment: ENV.NODE_ENV,
      checks: {
        environment: {
          status: healthReport.environment.isValid ? 'pass' : 'fail',
          errors: healthReport.environment.errors.length,
          warnings: healthReport.environment.warnings.length,
          // Não expor detalhes dos erros em produção
          details: ENV.IS_PRODUCTION
            ? undefined
            : {
                errors: healthReport.environment.errors,
                warnings: healthReport.environment.warnings,
              },
        },
        providers: {
          google: {
            status: healthReport.providers.google ? 'pass' : 'fail',
            configured: Boolean(
              process.env.AUTH_GOOGLE_CLIENT_ID && process.env.AUTH_GOOGLE_CLIENT_SECRET
            ),
          },
          github: {
            status: healthReport.providers.github ? 'pass' : 'fail',
            configured: Boolean(
              process.env.AUTH_GITHUB_CLIENT_ID && process.env.AUTH_GITHUB_CLIENT_SECRET
            ),
          },
        },
        database: {
          status: process.env.DB_DATABASE_URL ? 'pass' : 'fail',
          configured: Boolean(process.env.DB_DATABASE_URL),
        },
        nextauth: {
          status:
            process.env.AUTH_NEXTAUTH_SECRET && process.env.AUTH_NEXTAUTH_URL ? 'pass' : 'fail',
          secret_configured: Boolean(process.env.AUTH_NEXTAUTH_SECRET),
          url_configured: Boolean(process.env.AUTH_NEXTAUTH_URL),
        },
      },
    };

    // Log do resultado
    if (healthReport.overall) {
      logger.info('✅ Health check de autenticação: SAUDÁVEL', {
        statusCode,
        checks: Object.keys(response.checks).length,
      });
    } else {
      logger.warn('⚠️ Health check de autenticação: PROBLEMAS DETECTADOS', {
        statusCode,
        environmentErrors: healthReport.environment.errors.length,
        environmentWarnings: healthReport.environment.warnings.length,
        googleProvider: healthReport.providers.google,
        githubProvider: healthReport.providers.github,
      });
    }

    return NextResponse.json(response, { status: statusCode });
  } catch (error) {
    logger.error('❌ Erro no health check de autenticação', {
      error: error instanceof Error ? error.message : 'Erro desconhecido',
      stack: error instanceof Error ? error.stack : undefined,
    });

    return NextResponse.json(
      {
        status: 'error',
        message: 'Erro interno no health check',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/auth/health
 * Executa verificação detalhada com validação forçada
 */
export async function POST(request: NextRequest) {
  try {
    logger.info('🔍 Verificação detalhada de autenticação solicitada', {
      ip: request.ip,
      userAgent: request.headers.get('user-agent'),
      timestamp: new Date().toISOString(),
    });

    // Executar validações mais profundas
    const healthReport = getAuthHealthReport();

    // Verificações adicionais
    const additionalChecks = {
      urls: {
        nextauth_url_valid: isValidUrl(process.env.AUTH_NEXTAUTH_URL),
        nextauth_url_https:
          process.env.AUTH_NEXTAUTH_URL?.startsWith('https://') || ENV.IS_DEVELOPMENT,
      },
      secrets: {
        nextauth_secret_length: (process.env.AUTH_NEXTAUTH_SECRET?.length || 0) >= 32,
        google_client_secret_length: (process.env.AUTH_GOOGLE_CLIENT_SECRET?.length || 0) >= 20,
        github_client_secret_length: (process.env.AUTH_GITHUB_CLIENT_SECRET?.length || 0) >= 30,
      },
      format: {
        google_client_id_format:
          process.env.AUTH_GOOGLE_CLIENT_ID?.includes('.apps.googleusercontent.com') || false,
        github_client_id_format: (process.env.AUTH_GITHUB_CLIENT_ID?.length || 0) >= 16,
      },
    };

    const allChecksPass =
      healthReport.overall &&
      Object.values(additionalChecks.urls).every(Boolean) &&
      Object.values(additionalChecks.secrets).every(Boolean) &&
      Object.values(additionalChecks.format).every(Boolean);

    const response = {
      status: allChecksPass ? 'healthy' : 'unhealthy',
      timestamp: new Date().toISOString(),
      environment: ENV.NODE_ENV,
      basic_checks: {
        environment: healthReport.environment.isValid,
        google_provider: healthReport.providers.google,
        github_provider: healthReport.providers.github,
      },
      detailed_checks: ENV.IS_PRODUCTION ? undefined : additionalChecks,
      recommendations: generateRecommendations(healthReport, additionalChecks),
    };

    const statusCode = allChecksPass ? 200 : 503;

    logger.info(
      allChecksPass ? '✅ Verificação detalhada: APROVADA' : '⚠️ Verificação detalhada: PROBLEMAS',
      {
        statusCode,
        basicChecks: Object.values(response.basic_checks).filter(Boolean).length,
        recommendations: response.recommendations.length,
      }
    );

    return NextResponse.json(response, { status: statusCode });
  } catch (error) {
    logger.error('❌ Erro na verificação detalhada de autenticação', {
      error: error instanceof Error ? error.message : 'Erro desconhecido',
      stack: error instanceof Error ? error.stack : undefined,
    });

    return NextResponse.json(
      {
        status: 'error',
        message: 'Erro interno na verificação detalhada',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

/**
 * Valida se uma string é uma URL válida
 */
function isValidUrl(url?: string): boolean {
  if (!url) return false;
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

interface HealthReport {
  environment: { isValid: boolean };
  providers: { google: boolean; github: boolean };
}

interface AdditionalChecks {
  urls: { nextauth_url_https: boolean };
  secrets: { nextauth_secret_length: boolean };
  format: { google_client_id_format: boolean };
}

/**
 * Gera recomendações baseadas nos resultados dos checks
 */
function generateRecommendations(
  healthReport: HealthReport,
  additionalChecks: AdditionalChecks
): string[] {
  const recommendations: string[] = [];

  if (!healthReport.environment.isValid) {
    recommendations.push('Configure todas as variáveis de ambiente obrigatórias');
  }

  if (!healthReport.providers.google) {
    recommendations.push('Verifique as credenciais do Google OAuth');
  }

  if (!healthReport.providers.github) {
    recommendations.push('Verifique as credenciais do GitHub OAuth');
  }

  if (!additionalChecks.urls.nextauth_url_https && ENV.IS_PRODUCTION) {
    recommendations.push('Use HTTPS para NEXTAUTH_URL em produção');
  }

  if (!additionalChecks.secrets.nextauth_secret_length) {
    recommendations.push('NEXTAUTH_SECRET deve ter pelo menos 32 caracteres');
  }

  if (!additionalChecks.format.google_client_id_format) {
    recommendations.push('GOOGLE_CLIENT_ID deve terminar com .apps.googleusercontent.com');
  }

  return recommendations;
}
