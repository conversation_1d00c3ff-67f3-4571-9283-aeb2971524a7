import { test, expect } from '@playwright/test';
import path from 'path';
import fs from 'fs';
import os from 'os';

// Função utilitária para criar um arquivo Excel de teste
async function createTestExcelFile(): Promise<string> {
  const ExcelJS = require('exceljs');
  const workbook = new ExcelJS.Workbook();
  const sheet = workbook.addWorksheet('Planilha1');

  // Adicionar cabeçalhos
  sheet.columns = [
    { header: 'Nome', key: 'nome' },
    { header: 'Valor', key: 'valor' },
    { header: 'Data', key: 'data' },
  ];

  // Adicionar dados
  sheet.addRow({ nome: 'João', valor: 100, data: new Date(2023, 0, 1) });
  sheet.addRow({ nome: 'Maria', valor: 200, data: new Date(2023, 0, 2) });
  sheet.addRow({ nome: 'Pedro', valor: 300, data: new Date(2023, 0, 3) });
  sheet.addRow({ nome: 'Ana', valor: 400, data: new Date(2023, 0, 4) });
  sheet.addRow({ nome: 'Carlos', valor: 500, data: new Date(2023, 0, 5) });

  // Criar arquivo temporário
  const tmpDir = os.tmpdir();
  const filePath = path.join(tmpDir, 'test-excel-file.xlsx');

  await workbook.xlsx.writeFile(filePath);
  return filePath;
}

test.describe('Fluxo de Upload e Chat', () => {
  let excelFilePath: string;

  test.beforeAll(async () => {
    excelFilePath = await createTestExcelFile();
  });

  test.afterAll(async () => {
    // Limpar arquivo temporário
    if (excelFilePath && fs.existsSync(excelFilePath)) {
      fs.unlinkSync(excelFilePath);
    }
  });

  test('Deve permitir upload de planilha e interação por chat', async ({ page }) => {
    // 1. Acessar a página inicial
    await page.goto('/');

    // 2. Verificar se a página carregou corretamente
    await expect(page.getByText('Excel Copilot')).toBeVisible();

    // 3. Navegar para a aba de upload
    await page.getByRole('tab', { name: 'Upload de Planilha' }).click();

    // 4. Fazer upload do arquivo
    await page.setInputFiles('input[type="file"]', excelFilePath);
    await page.getByRole('button', { name: 'Iniciar Upload' }).click();

    // 5. Esperar o redirecionamento para a página da planilha
    await page.waitForURL(/\/workbook\/*/);

    // 6. Verificar se a planilha foi carregada corretamente
    await expect(page.getByText('Planilha1')).toBeVisible();
    await expect(page.getByText('Valor')).toBeVisible();

    // 7. Interagir com o chat
    await page.getByRole('tab', { name: 'Chat' }).click();

    // 8. Enviar uma mensagem para calcular a soma
    await page.getByPlaceholder('Digite sua mensagem...').fill('Some os valores da coluna Valor');
    await page.getByRole('button', { name: 'Enviar' }).click();

    // 9. Verificar a resposta
    await expect(page.getByText(/Soma da coluna Valor/)).toBeVisible({ timeout: 10000 });
    await expect(page.getByText('1500')).toBeVisible();

    // 10. Verificar se uma notificação de sucesso aparece
    await expect(page.getByText('Operações executadas com sucesso')).toBeVisible();

    // 11. Voltar para a visualização de dados e verificar se os dados foram atualizados
    await page.getByRole('tab', { name: 'Dados' }).click();
    await expect(page.locator('.excel-grid')).toBeVisible();
  });

  test('Deve lidar com erros nos comandos de chat', async ({ page }) => {
    // 1. Acessar a página inicial e fazer login
    await page.goto('/');

    // 2. Navegar para a aba de chat
    await page.getByRole('tab', { name: 'Chat' }).click();

    // 3. Enviar um comando inválido
    await page.getByPlaceholder('Digite sua mensagem...').fill('Comando que não existe no Excel');
    await page.getByRole('button', { name: 'Enviar' }).click();

    // 4. Verificar a resposta com erro ou sugestão
    await expect(page.getByText(/Não foi possível identificar/)).toBeVisible({ timeout: 10000 });
  });
});

test.describe('Navegação e Interface', () => {
  test('Deve navegar entre diferentes seções da aplicação', async ({ page }) => {
    // 1. Acessar a página inicial
    await page.goto('/');

    // 2. Verificar elementos de UI
    await expect(page.getByText('Excel Copilot')).toBeVisible();
    await expect(page.getByRole('tab', { name: 'Chat' })).toBeVisible();
    await expect(page.getByRole('tab', { name: 'Upload de Planilha' })).toBeVisible();

    // 3. Testar navegação para dashboard
    // Primeiro, verificar se há um link para o dashboard
    const dashboardLink = page.getByRole('link', { name: 'Ver todas as planilhas' });
    if (await dashboardLink.isVisible()) {
      await dashboardLink.click();
      await expect(page.url()).toContain('/dashboard');
      await expect(page.getByText('Minhas Planilhas')).toBeVisible();
    } else {
      // Se não houver planilhas, verificamos a interface inicial apenas
      await expect(page.getByText('Interaja com suas planilhas')).toBeVisible();
    }

    // 4. Verificar responsividade em tela menor
    await page.setViewportSize({ width: 480, height: 800 });
    await page.goto('/');
    await expect(page.getByText('Excel Copilot')).toBeVisible();

    // A interface deve se adaptar à tela menor
    await expect(page.getByRole('tab', { name: 'Chat' })).toBeVisible();
  });
});
