/**
 * Centralizador de serviços do sistema
 *
 * Esta classe gerencia o acesso a serviços singleton em toda a aplicação,
 * fornecendo um ponto central para registro e recuperação de serviços,
 * com suporte a dependências entre serviços para inicialização ordenada.
 * A inicialização é delegada ao app-initializer.ts.
 */

// Criando um logger básico local para evitar dependências com erro
const logger = {
  info: (..._args: unknown[]) => {
    if (process.env.NODE_ENV !== 'production') {
      // Service info logged
    }
  },
  warn: (..._args: unknown[]) => {
    if (process.env.NODE_ENV !== 'production') {
      // Service warning logged
    }
  },
  error: (..._args: unknown[]) => {
    if (process.env.NODE_ENV !== 'production') {
      // Service error logged
    }
  },
  debug: (..._args: unknown[]) => {
    if (process.env.NODE_ENV !== 'production') {
      // Service debug logged
    }
  },
};

// Tipo para metadados de serviço
export interface ServiceMetadata {
  name: string;
  dependencies: string[];
  initialized: boolean;
  instance: unknown;
  priority?: ServicePriority;
  initStartTime?: number | undefined;
  initEndTime?: number | undefined;
  initDuration?: number | undefined;
}

// Prioridade de inicialização do serviço
export enum ServicePriority {
  CRITICAL = 0, // Serviços essenciais para renderização e funcionalidade básica
  HIGH = 1, // Serviços importantes, mas não críticos
  MEDIUM = 2, // Serviços que podem ser carregados depois
  LOW = 3, // Serviços pesados ou raramente usados
  LAZY = 4, // Serviços carregados apenas sob demanda
}

// Registro central de serviços
interface ServiceRegistry {
  [key: string]: ServiceMetadata;
}

export class ServiceManager {
  private static instance: ServiceManager;
  private services: ServiceRegistry = {};
  private initialized: boolean = false;
  private initializationStartTime: number = 0;
  private initializationEndTime: number = 0;

  // Privado para garantir singleton
  private constructor() {}

  /**
   * Obtém a instância do ServiceManager
   */
  public static getInstance(): ServiceManager {
    if (!ServiceManager.instance) {
      ServiceManager.instance = new ServiceManager();
    }
    return ServiceManager.instance;
  }

  /**
   * Registra um serviço no gerenciador
   * @param name Nome do serviço
   * @param service Instância do serviço
   * @param dependencies Array de nomes de serviços dos quais este depende
   * @param priority Prioridade de inicialização (padrão: MEDIUM)
   * @returns ServiceManager
   */
  public registerService<T>(
    name: string,
    service: T,
    dependencies: string[] = [],
    priority: ServicePriority = ServicePriority.MEDIUM
  ): ServiceManager {
    if (this.services[name] && process.env.NODE_ENV !== 'test') {
      logger.warn(`Serviço '${name}' já estava registrado e será substituído.`);
    }

    this.services[name] = {
      name,
      dependencies,
      initialized: false,
      instance: service,
      priority,
    };

    return this;
  }

  /**
   * Obtém um serviço pelo nome
   * @param name Nome do serviço
   * @returns Instância do serviço ou null se não encontrado
   */
  public getService<T>(name: string): T | null {
    const serviceMetadata = this.services[name];
    return serviceMetadata ? (serviceMetadata.instance as T) : null;
  }

  /**
   * Verifica se o ServiceManager foi inicializado
   */
  public isInitialized(): boolean {
    return this.initialized;
  }

  /**
   * Define o estado de inicialização
   * @param state Estado de inicialização
   */
  public setInitialized(state: boolean): void {
    this.initialized = state;

    if (state) {
      this.initializationEndTime = performance.now();
      const duration = this.initializationEndTime - this.initializationStartTime;
      logger.info(`Inicialização completa do ServiceManager em ${duration.toFixed(2)}ms`);
    } else {
      this.initializationStartTime = performance.now();
    }
  }

  /**
   * Verifica se um serviço específico está inicializado
   * @param name Nome do serviço
   */
  public isServiceInitialized(name: string): boolean {
    return !!this.services[name]?.initialized;
  }

  /**
   * Define o estado de inicialização de um serviço específico
   * @param name Nome do serviço
   * @param state Estado de inicialização
   */
  public setServiceInitialized(name: string, state: boolean): void {
    if (this.services[name]) {
      const now = performance.now();

      if (state && !this.services[name].initialized) {
        // Serviço sendo inicializado pela primeira vez
        this.services[name].initEndTime = now;

        if (this.services[name].initStartTime) {
          const duration = now - this.services[name].initStartTime;
          this.services[name].initDuration = duration;

          // Log apenas se a inicialização demorou mais que 5ms
          if (duration > 5) {
            logger.debug(`Serviço '${name}' inicializado em ${duration.toFixed(2)}ms`);
          }
        }
      } else if (!state && this.services[name].initialized) {
        // Reiniciando um serviço
        this.services[name].initStartTime = now;
        this.services[name].initEndTime = undefined;
        this.services[name].initDuration = undefined;
      }

      this.services[name].initialized = state;
    } else {
      logger.warn(`Tentativa de marcar serviço não registrado '${name}' como inicializado`);
    }
  }

  /**
   * Marca o início da inicialização de um serviço
   * Útil para medir o tempo de inicialização de serviços assíncronos
   * @param name Nome do serviço
   */
  public markServiceInitializationStart(name: string): void {
    if (this.services[name]) {
      this.services[name].initStartTime = performance.now();
    }
  }

  /**
   * Obtém serviços por prioridade
   * @param priority Prioridade desejada
   * @returns Lista de nomes de serviços com a prioridade especificada
   */
  public getServicesByPriority(priority: ServicePriority): string[] {
    return Object.keys(this.services).filter(name => {
      const service = this.services[name];
      return service && service.priority === priority;
    });
  }

  /**
   * Obtém o tempo total de inicialização do sistema (em ms)
   */
  public getTotalInitializationTime(): number {
    if (!this.initialized || !this.initializationEndTime || !this.initializationStartTime) {
      return 0;
    }
    return this.initializationEndTime - this.initializationStartTime;
  }

  /**
   * Obtém todas as dependências de um serviço, incluindo as transitivas
   * @param serviceName Nome do serviço
   * @param visited Conjunto auxiliar para evitar loops infinitos
   * @returns Array de nomes de serviços dos quais este depende
   */
  public getAllDependencies(serviceName: string, visited: Set<string> = new Set()): string[] {
    const service = this.services[serviceName];
    if (!service) return [];

    if (visited.has(serviceName)) {
      logger.warn(`Dependência circular detectada para o serviço '${serviceName}'`);
      return [];
    }

    visited.add(serviceName);
    const allDeps = [...service.dependencies];

    for (const dep of service.dependencies) {
      const transitiveDeps = this.getAllDependencies(dep, new Set(visited));
      for (const transDep of transitiveDeps) {
        if (!allDeps.includes(transDep)) {
          allDeps.push(transDep);
        }
      }
    }

    return allDeps;
  }

  /**
   * Calcula a ordem de inicialização dos serviços baseada em suas dependências
   * @param priorityFilter Opcional: filtra serviços apenas da prioridade especificada
   * @returns Array de nomes de serviços na ordem correta de inicialização
   */
  public getInitializationOrder(priorityFilter?: ServicePriority): string[] {
    const visited = new Set<string>();
    const order: string[] = [];

    const visit = (serviceName: string) => {
      if (visited.has(serviceName)) return;
      visited.add(serviceName);

      const service = this.services[serviceName];
      if (!service) return;

      // Pular serviços que não correspondem ao filtro de prioridade
      if (priorityFilter !== undefined && service.priority !== priorityFilter) {
        return;
      }

      for (const dep of service.dependencies) {
        // Ao visitar dependências, incluímos mesmo que estejam fora do filtro de prioridade
        visit(dep);
      }

      // Só adicionar à ordem final se corresponder ao filtro de prioridade
      if (priorityFilter === undefined || service.priority === priorityFilter) {
        order.push(serviceName);
      }
    };

    // Inicializa todos os serviços registrados
    Object.keys(this.services).forEach(visit);

    return order;
  }

  /**
   * Retorna a ordem de finalização dos serviços (inversa da inicialização)
   * para garantir que serviços dependentes sejam encerrados antes de suas dependências
   * @returns Array de nomes de serviços na ordem correta de finalização
   */
  public getShutdownOrder(): string[] {
    // A ordem de shutdown é o inverso da ordem de inicialização
    return this.getInitializationOrder().reverse();
  }

  /**
   * Finaliza todos os serviços registrados de forma ordenada
   * Chama o método shutdown/dispose/close em cada serviço quando disponível
   * @returns Promessa que resolve quando todos os serviços forem finalizados
   */
  public async shutdown(): Promise<void> {
    logger.info('Iniciando finalização ordenada dos serviços...');

    const shutdownOrder = this.getShutdownOrder();
    const errors: { service: string; error: unknown }[] = [];

    for (const serviceName of shutdownOrder) {
      try {
        const service = this.getService(serviceName);
        if (!service) continue;

        logger.debug(`Finalizando serviço: ${serviceName}`);

        // Tenta chamar um dos métodos de finalização conhecidos
        // Usa verificação de tipo e interface genérica para evitar erros de typescript
        // Define interfaces para diferentes tipos de serviços que podem ser desligados
        interface ShutdownService {
          shutdown(): Promise<void> | void;
        }
        interface DisposeService {
          dispose(): Promise<void> | void;
        }
        interface CloseService {
          close(): Promise<void> | void;
        }
        interface TerminateService {
          terminate(): Promise<void> | void;
        }
        interface DestroyService {
          destroy(): Promise<void> | void;
        }

        // Type guards para verificar o tipo de serviço usando expressões de função em vez de declarações
        const isShutdownService = (s: unknown): s is ShutdownService => {
          return Boolean(
            s &&
              typeof (s as import('@/types/global-types').ServiceWithShutdown).shutdown ===
                'function'
          );
        };

        const isDisposeService = (s: unknown): s is DisposeService => {
          return Boolean(
            s &&
              typeof (s as import('@/types/global-types').ServiceWithDispose).dispose === 'function'
          );
        };

        const isCloseService = (s: unknown): s is CloseService => {
          return Boolean(
            s && typeof (s as import('@/types/global-types').ServiceWithClose).close === 'function'
          );
        };

        const isTerminateService = (s: unknown): s is TerminateService => {
          return Boolean(
            s &&
              typeof (s as import('@/types/global-types').ServiceWithTerminate).terminate ===
                'function'
          );
        };

        const isDestroyService = (s: unknown): s is DestroyService => {
          return Boolean(
            s &&
              typeof (s as import('@/types/global-types').ServiceWithDestroy).destroy === 'function'
          );
        };

        // Usar type guards para verificar e chamar método adequado
        if (isShutdownService(service)) {
          await service.shutdown();
        } else if (isDisposeService(service)) {
          await service.dispose();
        } else if (isCloseService(service)) {
          await service.close();
        } else if (isTerminateService(service)) {
          await service.terminate();
        } else if (isDestroyService(service)) {
          await service.destroy();
        } else {
          // Se o serviço for um Map/Set com clear, tenta chamar
          if (service instanceof Map || service instanceof Set) {
            service.clear();
          }
          // Se for um timer, limpa
          else if (service && typeof service === 'object' && 'clearInterval' in globalThis) {
            // Interface para serviços com timer interno
            interface TimerService {
              _timer?: NodeJS.Timeout;
            }

            // Verifica se o serviço tem um timer interno - usando expressão de função
            const hasTimer = (s: unknown): s is TimerService => {
              return Boolean(s && typeof s === 'object' && '_timer' in s);
            };

            if (hasTimer(service) && service._timer) {
              clearInterval(service._timer);
            }
          }
        }

        // Marca o serviço como não inicializado
        this.setServiceInitialized(serviceName, false);
      } catch (error) {
        const errorMsg = error instanceof Error ? error.message : String(error);
        logger.error(`Erro ao finalizar serviço '${serviceName}': ${errorMsg}`, error);
        errors.push({ service: serviceName, error });
      }
    }

    // Marca o ServiceManager como não inicializado
    this.initialized = false;

    if (errors.length > 0) {
      logger.warn(`Finalização de serviços concluída com ${errors.length} erros`);
    } else {
      logger.info('Todos os serviços finalizados com sucesso');
    }
  }

  /**
   * Reinicia todos os serviços (útil para testes)
   * @param safe Se true, apenas reinicia em ambientes não produtivos
   */
  public reset(safe: boolean = true): void {
    if (safe && process.env.NODE_ENV === 'production') {
      logger.warn('Tentativa de reset do ServiceManager em produção bloqueada.');
      return;
    }

    // Limpa todos os serviços
    this.services = {};
    this.initialized = false;
  }

  /**
   * Lista todos os serviços registrados e suas dependências
   * @returns Array de metadados dos serviços
   */
  public listServices(): ServiceMetadata[] {
    return Object.values(this.services);
  }

  /**
   * Obtém métricas de inicialização
   * @returns Objeto com métricas
   */
  public getInitializationMetrics() {
    const services = this.listServices();
    const totalTime = this.getTotalInitializationTime();
    const initializedCount = services.filter(s => s.initialized).length;

    return {
      totalServices: services.length,
      initializedServices: initializedCount,
      uninitializedServices: services.length - initializedCount,
      totalInitTime: totalTime,
      serviceMetrics: services.map(s => ({
        name: s.name,
        initialized: s.initialized,
        initTime: s.initDuration || 0,
        priority: s.priority,
      })),
    };
  }
}

/**
 * Função de ajuda para acessar rapidamente o gerenciador de serviços
 */
export function getServiceManager(): ServiceManager {
  return ServiceManager.getInstance();
}

/**
 * Função de ajuda para acessar rapidamente um serviço específico
 */
export function getService<T>(name: string): T | null {
  return getServiceManager().getService<T>(name);
}
