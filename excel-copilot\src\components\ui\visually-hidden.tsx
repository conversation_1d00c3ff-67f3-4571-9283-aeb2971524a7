import { ReactNode } from 'react';

import { cn } from '@/lib/utils';

interface VisuallyHiddenProps extends React.HTMLAttributes<HTMLSpanElement> {
  children: ReactNode;
}

/**
 * Um componente que esconde conteúdo visualmente, mas mantém acessível para leitores de tela
 */
export function VisuallyHidden({ children, className, ...props }: VisuallyHiddenProps) {
  return (
    <span className={cn('sr-only', className)} {...props}>
      {children}
    </span>
  );
}
