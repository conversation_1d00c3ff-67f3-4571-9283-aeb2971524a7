import { NextRequest, NextResponse } from 'next/server';

import { logger } from '@/lib/logger';
import { VercelMonitoringService } from '@/lib/vercel-integration';
import { ApiResponse } from '@/utils/api-response';

// Configurar rota como dinâmica
export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

/**
 * GET /api/vercel/logs
 * Obtém logs filtrados do Vercel
 */
export async function GET(request: NextRequest) {
  try {
    // Verificar se temos as credenciais necessárias
    const apiToken = process.env.MCP_VERCEL_TOKEN;
    const projectId = process.env.MCP_VERCEL_PROJECT_ID;
    const teamId = process.env.MCP_VERCEL_TEAM_ID;

    if (!apiToken) {
      return ApiResponse.error('VERCEL_API_TOKEN não configurado', 'VERCEL_CONFIG_ERROR', 500);
    }

    // Obter parâmetros da query
    const { searchParams } = new URL(request.url);
    const level = searchParams.get('level') as 'info' | 'warn' | 'error' | null;
    const source = searchParams.get('source') as 'build' | 'static' | 'lambda' | 'edge' | null;
    const limit = parseInt(searchParams.get('limit') || '50');
    const search = searchParams.get('search');

    // Validar parâmetros
    if (level && !['info', 'warn', 'error'].includes(level)) {
      return ApiResponse.error(
        'Parâmetro level inválido. Use: info, warn, error',
        'INVALID_PARAMETER',
        400
      );
    }

    if (source && !['build', 'static', 'lambda', 'edge'].includes(source)) {
      return ApiResponse.error(
        'Parâmetro source inválido. Use: build, static, lambda, edge',
        'INVALID_PARAMETER',
        400
      );
    }

    if (limit < 1 || limit > 200) {
      return ApiResponse.error(
        'Parâmetro limit deve estar entre 1 e 200',
        'INVALID_PARAMETER',
        400
      );
    }

    // Criar instância do serviço de monitoramento
    const vercelService = new VercelMonitoringService(apiToken, teamId, projectId);

    // Obter logs filtrados
    const logs = await vercelService.getFilteredLogs({
      ...(level && { level }),
      ...(source && { source }),
      limit,
      ...(search && { search }),
    });

    // Formatar logs para resposta
    const formattedLogs = logs.map(log => ({
      timestamp: new Date(log.timestamp).toISOString(),
      level: log.level,
      source: log.source,
      message: log.message,
      deploymentId: log.deploymentId,
      requestId: log.requestId,
      region: log.region,
    }));

    const response = {
      logs: formattedLogs,
      total: formattedLogs.length,
      filters: {
        level: level || 'all',
        source: source || 'all',
        search: search || null,
        limit,
      },
      timestamp: new Date().toISOString(),
    };

    logger.info('Logs Vercel obtidos com sucesso', {
      count: formattedLogs.length,
      level,
      source,
    });

    return ApiResponse.success(response);
  } catch (error) {
    logger.error('Erro ao obter logs do Vercel', { error });

    if (error instanceof Error) {
      return ApiResponse.error(
        `Erro ao conectar com Vercel: ${error.message}`,
        'VERCEL_API_ERROR',
        500
      );
    }

    return ApiResponse.error('Erro interno do servidor', 'INTERNAL_ERROR', 500);
  }
}

/**
 * POST /api/vercel/logs
 * Busca logs com filtros mais avançados
 */
export async function POST(request: NextRequest) {
  try {
    const apiToken = process.env.MCP_VERCEL_TOKEN;
    const projectId = process.env.MCP_VERCEL_PROJECT_ID;
    const teamId = process.env.MCP_VERCEL_TEAM_ID;

    if (!apiToken) {
      return ApiResponse.error('VERCEL_API_TOKEN não configurado', 'VERCEL_CONFIG_ERROR', 500);
    }

    // Obter filtros do body
    const body = await request.json();
    const { level, source, limit = 50, search, deploymentId, since, until } = body;

    // Validar parâmetros
    if (level && !['info', 'warn', 'error'].includes(level)) {
      return ApiResponse.error('Parâmetro level inválido', 'INVALID_PARAMETER', 400);
    }

    if (source && !['build', 'static', 'lambda', 'edge'].includes(source)) {
      return ApiResponse.error('Parâmetro source inválido', 'INVALID_PARAMETER', 400);
    }

    if (limit < 1 || limit > 500) {
      return ApiResponse.error(
        'Parâmetro limit deve estar entre 1 e 500',
        'INVALID_PARAMETER',
        400
      );
    }

    const vercelService = new VercelMonitoringService(apiToken, teamId, projectId);

    let logs;

    if (deploymentId) {
      // Buscar logs de um deployment específico
      const client = vercelService['client']; // Acessar cliente privado
      logs = await client.getDeploymentLogs(deploymentId, {
        ...(since && { since: new Date(since).getTime() }),
        ...(until && { until: new Date(until).getTime() }),
        limit,
      });

      // Aplicar filtros adicionais
      if (level) {
        logs = logs.filter(log => log.level === level);
      }
      if (source) {
        logs = logs.filter(log => log.source === source);
      }
      if (search) {
        const searchTerm = search.toLowerCase();
        logs = logs.filter(log => log.message.toLowerCase().includes(searchTerm));
      }
    } else {
      // Buscar logs gerais
      logs = await vercelService.getFilteredLogs({
        level,
        source,
        limit,
        search,
      });
    }

    // Formatar logs para resposta
    const formattedLogs = logs.map(log => ({
      timestamp: new Date(log.timestamp).toISOString(),
      level: log.level,
      source: log.source,
      message: log.message,
      deploymentId: log.deploymentId,
      requestId: log.requestId,
      region: log.region,
    }));

    const response = {
      logs: formattedLogs,
      total: formattedLogs.length,
      filters: {
        level: level || 'all',
        source: source || 'all',
        search: search || null,
        deploymentId: deploymentId || null,
        limit,
        since: since || null,
        until: until || null,
      },
      timestamp: new Date().toISOString(),
    };

    return ApiResponse.success(response);
  } catch (error) {
    logger.error('Erro na busca avançada de logs do Vercel', { error });
    return ApiResponse.error('Erro ao buscar logs', 'VERCEL_LOGS_ERROR', 500);
  }
}
