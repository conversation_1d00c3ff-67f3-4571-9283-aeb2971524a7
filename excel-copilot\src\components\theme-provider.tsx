'use client';

import { ThemeProvider as NextThemeProvider } from 'next-themes';
import { createContext, useContext, useEffect, useState } from 'react';

// Definindo o tipo localmente com tipos mais precisos
type ThemeProviderProps = {
  attribute?: 'class' | 'data-theme' | 'data-mode';
  defaultTheme?: string;
  enableSystem?: boolean;
  disableTransitionOnChange?: boolean;
  storageKey?: string;
  forcedTheme?: string;
  themes?: string[];
  children?: React.ReactNode;
};

const ThemeProviderContext = createContext({ isDark: false, setIsDark: (_value: boolean) => {} });

export function ThemeProvider({
  children,
  defaultTheme = 'system',
  enableSystem = true,
  ...props
}: ThemeProviderProps) {
  const [isDark, setIsDark] = useState(false);

  useEffect(() => {
    const isDarkMode = document.documentElement.classList.contains('dark');
    setIsDark(isDarkMode);
  }, []);

  return (
    <ThemeProviderContext.Provider value={{ isDark, setIsDark }}>
      <NextThemeProvider
        attribute="class"
        defaultTheme={defaultTheme}
        enableSystem={enableSystem}
        {...props}
      >
        {children}
      </NextThemeProvider>
    </ThemeProviderContext.Provider>
  );
}

export const useTheme = () => {
  const context = useContext(ThemeProviderContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};
