/**
 * Utilitário para detecção de padrões suspeitos em strings
 * que podem indicar tentativas de ataque ou injeção
 */

// Expressões regulares para detectar padrões suspeitos
const SUSPICIOUS_PATTERNS = [
  // SQL Injection
  /(\b(select|insert|update|delete|drop|alter|create)\b.*\b(from|into|table|database|values)\b)/i,
  // JavaScript Injection
  /<script\b[^>]*>.*?<\/script>/i,
  /\bon\w+\s*=\s*["']?[^"']*["']?/i,
  // XSS básico
  /<[^>]*\s+on\w+\s*=[^>]*>/i,
  // Command Injection
  /[;&|`\\]/,
  // Path Traversal
  /\.\.\//,
  /\.\.\\/,
  // Ataque de Prototype Pollution
  /__(proto|defineGetter|defineSetter|lookupGetter|lookupSetter)__/,
  // NoSQL Injection (MongoDB)
  /\$(?:ne|gt|lt|gte|lte|in|nin|or|and|regex|where|exists|type|mod|all|size|elemMatch)/i,
  // HTML elements suspeitos
  /<(iframe|object|embed|base|link|meta|img\s+[^>]*\s+onerror)/i,
];

/**
 * Verifica se uma string contém padrões potencialmente maliciosos
 * @param value String para analisar
 * @returns true se contém padrão suspeito, false caso contrário
 */
export function hasSuspiciousPatterns(value: string | null | undefined): boolean {
  if (!value || typeof value !== 'string') return false;
  // Verificar contra padrões conhecidos
  return SUSPICIOUS_PATTERNS.some(pattern => pattern.test(value));
}

/**
 * Verifica se um objeto contém valores com padrões suspeitos
 * @param obj Objeto para verificar
 * @returns true se algum valor contém padrão suspeito
 */
export function hasObjectWithSuspiciousValues(obj: Record<string, unknown>): boolean {
  if (!obj || typeof obj !== 'object') return false;
  for (const key in obj) {
    const value = obj[key];
    if (typeof value === 'string' && hasSuspiciousPatterns(value)) {
      return true;
    }
    if (value && typeof value === 'object') {
      if (hasObjectWithSuspiciousValues(value as Record<string, unknown>)) {
        return true;
      }
    }
  }
  return false;
}

/**
 * Sanitiza um objeto removendo valores com padrões suspeitos
 * @param obj Objeto para sanitizar
 * @returns Objeto limpo ou null se tiver muitos padrões suspeitos
 */
export function sanitizeObjectValues(obj: Record<string, unknown>): Record<string, unknown> | null {
  if (!obj || typeof obj !== 'object') return null;

  const sanitized: Record<string, unknown> = {};
  let suspiciousCount = 0;

  for (const key in obj) {
    const value = obj[key];
    if (typeof value === 'string') {
      if (hasSuspiciousPatterns(value)) {
        suspiciousCount++;
        continue; // Pular este valor
      }
      sanitized[key] = value;
    } else if (value && typeof value === 'object') {
      const sanitizedNested = sanitizeObjectValues(value as Record<string, unknown>);
      if (sanitizedNested) {
        sanitized[key] = sanitizedNested;
      } else {
        suspiciousCount++;
      }
    } else {
      sanitized[key] = value;
    }
  }
  // Se muitos valores suspeitos foram encontrados, pode ser um ataque
  if (suspiciousCount > 3) {
    return null;
  }
  return sanitized;
}
