import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';

import { prisma } from '@/server/db/client';
import { SessionUser } from '@/types/next-auth';

/**
 * POST /api/workbooks/[id]/duplicate
 * Duplicar uma planilha existente
 */
export async function POST(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const workbookId = params.id;

    if (!workbookId) {
      return NextResponse.json({ error: 'ID da planilha é obrigatório' }, { status: 400 });
    }

    // Verificar autenticação
    const session = await getServerSession();

    if (!session || !session.user) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    // Obter o ID do usuário da sessão
    const userId = (session.user as SessionUser).id;

    // Buscar a planilha a ser duplicada
    const sourceWorkbook = await prisma.workbook.findUnique({
      where: {
        id: workbookId,
      },
      include: {
        sheets: true,
      },
    });

    if (!sourceWorkbook) {
      return NextResponse.json({ error: 'Planilha não encontrada' }, { status: 404 });
    }

    // Verificar se o usuário tem acesso à planilha (deve ser dono ou pública)
    if (sourceWorkbook.userId !== userId && !sourceWorkbook.isPublic) {
      return NextResponse.json({ error: 'Acesso negado' }, { status: 403 });
    }

    // Criar nova planilha (cópia)
    const newWorkbook = await prisma.workbook.create({
      data: {
        name: `${sourceWorkbook.name} (cópia)`,
        description: sourceWorkbook.description,
        userId: userId,
        isPublic: false, // A cópia sempre começa como privada
      },
    });

    // Duplicar todas as sheets
    const newSheets = [];
    for (const sheet of sourceWorkbook.sheets) {
      const newSheet = await prisma.sheet.create({
        data: {
          name: sheet.name,
          workbookId: newWorkbook.id,
          data: sheet.data,
        },
      });
      newSheets.push(newSheet);
    }

    return NextResponse.json(
      {
        workbook: {
          ...newWorkbook,
          sheets: newSheets,
        },
      },
      { status: 201 }
    );
  } catch (error) {
    console.error('Erro ao duplicar workbook:', error);
    return NextResponse.json({ error: 'Erro ao duplicar planilha' }, { status: 500 });
  }
}
