#!/usr/bin/env node

/**
 * Script para corrigir problemas de formatação com Prettier
 * Executa o Prettier em todos os arquivos do projeto
 */

const { execSync } = require('child_process');

// Função para colorir texto
const colors = {
  blue: text => `\x1b[34m${text}\x1b[0m`,
  green: text => `\x1b[32m${text}\x1b[0m`,
  yellow: text => `\x1b[33m${text}\x1b[0m`,
  cyan: text => `\x1b[36m${text}\x1b[0m`,
  red: text => `\x1b[31m${text}\x1b[0m`,
};

// Lista de diretórios a serem corrigidos
const directories = [
  'src/app/**/*.{ts,tsx}',
  'src/components/**/*.{ts,tsx}',
  'src/hooks/**/*.{ts,tsx}',
  'src/lib/**/*.{ts,tsx}',
  'src/server/**/*.{ts,tsx}',
  'src/types/**/*.{ts,d.ts}',
];

console.log(colors.blue('🔍 Iniciando correção de formatação com Prettier...'));

// Corrigir diretórios individualmente
for (const directory of directories) {
  console.log(colors.cyan(`🔧 Executando Prettier em ${directory}...`));
  try {
    execSync(`npx prettier --write ${directory}`, { stdio: 'inherit' });
    console.log(colors.green(`✅ Formatação concluída para ${directory}`));
  } catch (error) {
    console.log(colors.red(`❌ Erro ao formatar ${directory}: ${error.message}`));
  }
}

console.log(colors.green('\n✨ Processo de formatação concluído!'));
console.log(
  colors.yellow('⚠️ Verifique se ainda existem problemas de formatação executando npm run lint')
);
