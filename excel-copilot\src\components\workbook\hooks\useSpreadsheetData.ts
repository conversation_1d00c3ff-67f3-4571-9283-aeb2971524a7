import { useCallback, useEffect } from 'react';
import { toast } from 'sonner';

import { useExcelOperations } from '@/hooks/useExcelOperations';
import { useWorkbookRealtime } from '@/hooks/useWorkbookRealtime';
import { logger } from '@/lib/logger';

import { useSpreadsheetContext, SpreadsheetData } from '../SpreadsheetContext';

interface UseSpreadsheetDataProps {
  workbookId: string;
  onSave?: (data: SpreadsheetData) => Promise<void>;
  initialCommand?: string | null;
}

/**
 * Hook para gerenciar dados da planilha e integrações
 */
export function useSpreadsheetData({ workbookId, onSave, initialCommand }: UseSpreadsheetDataProps) {
  const { state, actions } = useSpreadsheetContext();

  // Hook de operações Excel
  const {
    processExcelCommand,
    isProcessing: isExcelProcessing,
    lastModifiedCells,
  } = useExcelOperations({
    onDataChange: actions.setData,
    onAddHistory: actions.addToHistory,
  });

  // Hook de Real-time para colaboração
  const {
    isConnected: isRealtimeConnected,
    updateCursor,
    broadcastCellChange,
  } = useWorkbookRealtime(workbookId);

  // Função para salvar spreadsheet
  const saveSpreadsheet = useCallback(async () => {
    if (state.readOnly) return;

    try {
      actions.updateUI({ isSaving: true });

      // Se temos uma função onSave personalizada
      if (onSave) {
        await onSave(state.data);
        toast.success('Planilha salva com sucesso');
        return;
      }

      // Implementação padrão de salvamento
      const response = await fetch(`/api/workbooks/${workbookId}/sheets`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: state.data.name || 'Sem nome',
          data: JSON.stringify(state.data),
        }),
      });

      if (!response.ok) {
        throw new Error('Erro ao salvar planilha');
      }

      toast.success('Planilha salva com sucesso');
    } catch (error) {
      logger.error('SpreadsheetEditor: Erro ao salvar planilha', error, {
        workbookId,
        spreadsheetName: state.data.name,
        readOnly: state.readOnly,
      });
      toast.error('Erro ao salvar planilha');
    } finally {
      actions.updateUI({ isSaving: false });
    }
  }, [workbookId, state.data, state.readOnly, onSave, actions]);

  // Função otimizada para mudanças de célula com Real-time
  const handleCellChange = useCallback(
    async (rowIndex: number, colIndex: number, value: string) => {
      if (state.readOnly || typeof rowIndex !== 'number' || typeof colIndex !== 'number') return;
      if (rowIndex < 0 || colIndex < 0) return;

      // Usar a ação do contexto para atualizar a célula
      actions.updateCellValue(rowIndex, colIndex, value);

      // Broadcast da mudança para outros usuários via Real-time
      if (isRealtimeConnected) {
        try {
          const cellAddress = `${String.fromCharCode(65 + colIndex)}${rowIndex + 1}`;
          await broadcastCellChange('sheet1', cellAddress, value);
          await updateCursor('sheet1', cellAddress);
        } catch (error) {
          console.error('Erro ao enviar mudança via Real-time:', error);
        }
      }
    },
    [state.readOnly, actions, isRealtimeConnected, broadcastCellChange, updateCursor]
  );

  // Manejar operações em lote vindas do AI
  const handleOperations = useCallback(
    (operations: any[]) => {
      if (!operations || !Array.isArray(operations) || operations.length === 0) return;

      actions.addToHistory(state.data);

      const newData = { ...state.data };
      newData.headers = Array.isArray(state.data.headers) ? [...state.data.headers] : [];
      newData.rows = Array.isArray(state.data.rows) ? [...state.data.rows] : [];

      // Lista para rastrear células modificadas
      const modifiedCells: Array<{ row: number; col: number }> = [];

      operations.forEach(op => {
        if (!op || typeof op !== 'object') return;

        if (
          op.type === 'cell_update' &&
          typeof op.row === 'number' &&
          typeof op.col === 'number'
        ) {
          // Garantir que a linha existe
          if (!Array.isArray(newData.rows[op.row])) {
            newData.rows[op.row] = Array(newData.headers.length).fill('');
          }

          // Verificar se o índice é válido
          if (
            op.row >= 0 &&
            op.col >= 0 &&
            Array.isArray(newData.headers) &&
            op.col < newData.headers.length &&
            Array.isArray(newData.rows) &&
            newData.rows[op.row] !== undefined &&
            Array.isArray(newData.rows[op.row])
          ) {
            try {
              if (newData.rows[op.row] && typeof op.col === 'number') {
                (newData.rows[op.row] as any[])[op.col] = op.value;
                modifiedCells.push({ row: op.row, col: op.col });
              }
            } catch (err) {
              logger.error('SpreadsheetEditor: Erro ao atualizar célula', err, {
                row: op.row,
                col: op.col,
                operation: op.type,
                workbookId,
              });
            }
          }
        } else if (op.type === 'add_row') {
          const newRow = Array(newData.headers.length).fill('');
          newData.rows.push(newRow);
        } else if (
          op.type === 'add_column' &&
          Array.isArray(newData.headers) &&
          Array.isArray(newData.rows)
        ) {
          newData.headers.push(op.name || `Coluna ${newData.headers.length + 1}`);
          newData.rows.forEach((row, idx) => {
            if (Array.isArray(row)) {
              newData.rows[idx] = [...row, ''];
            } else {
              newData.rows[idx] = Array(newData.headers.length).fill('');
            }
          });
        }
      });

      // Atualizar dados e célula modificada
      actions.setData(newData);
      if (modifiedCells.length > 0) {
        actions.setLastModifiedCell(modifiedCells[0] || null);
      }
    },
    [state.data, actions, workbookId]
  );

  // Processar comando inicial se fornecido
  useEffect(() => {
    if (initialCommand && !isExcelProcessing) {
      const timer = setTimeout(() => {
        if (processExcelCommand) {
          processExcelCommand(initialCommand, state.data);
        }
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [initialCommand, processExcelCommand, isExcelProcessing, state.data]);

  return {
    // Estado
    data: state.data,
    history: state.history,
    historyIndex: state.historyIndex,
    lastModifiedCell: state.lastModifiedCell,
    isSaving: state.ui.isSaving,
    readOnly: state.readOnly,

    // Ações
    saveSpreadsheet,
    handleCellChange,
    handleOperations,
    undo: actions.undo,
    redo: actions.redo,
    addColumn: actions.addColumn,
    addRow: actions.addRow,
    removeRow: actions.removeRow,
    removeColumn: actions.removeColumn,

    // Estados de integração
    isExcelProcessing,
    lastModifiedCells,
    isRealtimeConnected,

    // Utilitários
    canUndo: state.historyIndex > 0,
    canRedo: state.historyIndex < state.history.length - 1,
  };
}
