/**
 * Script extremamente simples para testar apenas a conexão básica com o banco de dados
 */

const { PrismaClient } = require('@prisma/client');

// Inicializar cliente Prisma
const prisma = new PrismaClient();

/**
 * Função principal
 */
async function main() {
  console.log('=== TESTE BÁSICO DE BANCO DE DADOS ===\n');

  try {
    // 1. Verificar conexão com o banco de dados
    console.log('Verificando conexão com o banco de dados...');

    // Tentativa de contagem de registros
    const workbookCount = await prisma.workbook.count();
    console.log(`✅ Conexão OK. Existem ${workbookCount} workbooks no banco de dados.`);

    // 2. Listar IDs de workbooks existentes
    console.log('\nListando IDs de workbooks existentes:');

    const workbooks = await prisma.workbook.findMany({
      select: {
        id: true,
        name: true,
        createdAt: true,
      },
      take: 5, // Limitar a 5 registros
    });

    workbooks.forEach((workbook, index) => {
      console.log(
        `  ${index + 1}. ID: ${workbook.id} | Nome: ${workbook.name} | Criado: ${workbook.createdAt}`
      );
    });

    // 3. Verificar se existe histórico de chat
    const chatCount = await prisma.chatHistory.count();
    console.log(`\nHistórico de chat: ${chatCount} mensagens encontradas`);

    console.log('\n✅ TESTE BÁSICO CONCLUÍDO COM SUCESSO');
  } catch (error) {
    console.error('❌ Erro no teste:', error);
  } finally {
    // Fechar conexão com o banco de dados
    await prisma.$disconnect();
    console.log('\nConexão com o banco de dados fechada');
  }
}

// Executar função principal
main().catch(error => {
  console.error('❌ Erro fatal:', error);
  process.exit(1);
});
