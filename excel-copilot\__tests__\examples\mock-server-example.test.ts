/**
 * @jest-environment node
 */

// Exemplo de teste usando nosso servidor mock aprimorado
import { server, rest, ctx } from '../mocks/server';
import { MockRequest, MockResponse, MockContext } from '../mocks/types';

// Configurar handlers de exemplo para testes
const setupExampleHandlers = () => {
  server.use(
    // Handler para simular recuperação de dados do Excel
    rest.get('/api/excel/data', (req: MockRequest, res: MockResponse, ctx: MockContext) => {
      return ctx.status(200).json({
        success: true,
        data: [
          ['Nome', 'Idade', 'Cidade'],
          ['<PERSON>', 28, 'São Paulo'],
          ['Maria', 32, 'Rio de Janeiro'],
          ['Pedro', 45, 'Belo Horizonte'],
        ],
      });
    }),

    // Handler para simular processamento de uma fórmula
    rest.post('/api/excel/calculate', (req: MockRequest, res: MockResponse, ctx: MockContext) => {
      const { formula } = req.body || {};

      // Simular cálculo de soma
      if (formula && formula.includes('SUM')) {
        return ctx.status(200).json({
          success: true,
          result: 105,
        });
      }

      return ctx.status(400).json({
        success: false,
        error: 'Fórmula inválida',
      });
    })
  );
};

describe('Mock Server Example', () => {
  beforeAll(() => {
    server.listen();
  });

  afterEach(() => {
    server.resetHandlers();
  });

  afterAll(() => {
    server.close();
  });

  test('deve retornar dados do Excel corretamente', () => {
    // Configurar handlers para este teste
    setupExampleHandlers();

    // Simular uma requisição
    const response = server.handleRequest('/api/excel/data', 'GET');

    // Verificar a resposta
    expect(response.status).toBe(200);
    expect(response.body.data).toHaveLength(4); // 1 linha de cabeçalho + 3 linhas de dados
    expect(response.body.success).toBe(true);
  });

  test('deve processar uma fórmula válida', () => {
    // Configurar handlers para este teste
    setupExampleHandlers();

    // Simular uma requisição com corpo
    const response = server.handleRequest('/api/excel/calculate', 'POST', {
      formula: 'SUM(A1:A10)',
    });

    // Verificar a resposta
    expect(response.status).toBe(200);
    expect(response.body.result).toBe(105);
  });

  test('deve rejeitar uma fórmula inválida', () => {
    // Configurar handlers para este teste
    setupExampleHandlers();

    // Simular uma requisição com corpo
    const response = server.handleRequest('/api/excel/calculate', 'POST', { formula: 'INVALID()' });

    // Verificar a resposta
    expect(response.status).toBe(400);
    expect(response.body.success).toBe(false);
    expect(response.body.error).toBe('Fórmula inválida');
  });

  test('deve retornar 404 para rotas não encontradas', () => {
    // Configurar handlers para este teste
    setupExampleHandlers();

    // Simular uma requisição para uma rota que não existe
    const response = server.handleRequest('/api/route-that-doesnt-exist', 'GET');

    // Verificar a resposta
    expect(response.status).toBe(404);
    expect(response.body.error).toBe('Not found');
  });
});
