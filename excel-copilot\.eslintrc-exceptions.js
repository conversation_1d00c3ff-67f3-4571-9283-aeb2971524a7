/**
 * Configurações de exceções para o ESLint
 * Este arquivo define regras de exceção para diretórios específicos
 * onde determinadas regras são desativadas
 */

module.exports = {
  // Arquivos relacionados a operações de Excel podem usar 'any'
  excelFiles: {
    files: [
      'src/lib/excel/**/*.ts',
      'src/lib/excel/**/*.tsx',
      'src/components/workbook/**/*.tsx',
      'src/hooks/useExcel*.ts',
    ],
    rules: {
      '@typescript-eslint/no-explicit-any': 'off',
    },
  },

  // Arquivos de APIs podem usar 'any' para lidar com dados externos
  apiFiles: {
    files: ['src/app/api/**/*.ts', 'src/server/**/*.ts'],
    rules: {
      '@typescript-eslint/no-explicit-any': 'off',
    },
  },

  // Arquivos de integrações com serviços externos podem usar 'any'
  integrationFiles: {
    files: ['src/lib/ai/**/*.ts', 'src/server/ai/**/*.ts', 'src/lib/bridge/**/*.ts'],
    rules: {
      '@typescript-eslint/no-explicit-any': 'off',
      'no-console': 'off',
    },
  },

  // Arquivos de utilitários podem usar 'any'
  utilFiles: {
    files: ['src/utils/**/*.ts'],
    rules: {
      '@typescript-eslint/no-explicit-any': 'off',
    },
  },

  // Arquivos de logs e telemetria podem usar console
  loggingFiles: {
    files: ['src/lib/logger.ts', 'src/lib/telemetry.ts', 'src/lib/error-handler.ts'],
    rules: {
      'no-console': 'off',
    },
  },

  // Arquivos de teste não precisam preocupar com unused vars e any
  testFiles: {
    files: ['**/__tests__/**/*', '**/*.test.ts', '**/*.test.tsx', '**/*.spec.ts', '**/*.spec.tsx'],
    rules: {
      '@typescript-eslint/no-explicit-any': 'off',
      '@typescript-eslint/no-unused-vars': 'off',
      'no-console': 'off',
    },
  },

  // Arquivos de definição de tipos podem usar any
  typeFiles: {
    files: ['src/types/**/*.ts', 'src/types/**/*.d.ts'],
    rules: {
      '@typescript-eslint/no-explicit-any': 'off',
      '@typescript-eslint/no-unused-vars': 'off',
    },
  },
};
