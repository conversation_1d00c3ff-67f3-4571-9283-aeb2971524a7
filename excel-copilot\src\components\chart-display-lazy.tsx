'use client';

import { Suspense, lazy } from 'react';

import { ChartDisplayProps } from './chart-display';

// Componente de loading
const ChartLoading = () => (
  <div className="flex items-center justify-center h-[300px] bg-muted rounded-lg">
    <div className="animate-spin h-8 w-8 border-2 border-primary border-t-transparent rounded-full"></div>
    <span className="ml-2">Carregando gráfico...</span>
  </div>
);

// Importação dinâmica do componente chart-display
const ChartDisplayComponent = lazy(() =>
  import('./chart-display').then(mod => ({
    default: mod.ChartDisplay,
  }))
);

// Componente wrapper que implementa lazy loading
export function ChartDisplayLazy(props: ChartDisplayProps) {
  return (
    <Suspense fallback={<ChartLoading />}>
      <ChartDisplayComponent {...props} />
    </Suspense>
  );
}

export default ChartDisplayLazy;
