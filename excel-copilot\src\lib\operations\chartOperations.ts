import { safeArrayAccess } from '@/utils';
import { extractGroup } from '@/utils/regex-utils';

import { ExcelOperation } from '../excel/types';
import { logger } from '../logger';
import { canAddChart } from '../subscription-limits';

/**
 * Tipos de gráficos suportados
 */
export type ChartType =
  | 'bar'
  | 'column'
  | 'line'
  | 'pie'
  | 'scatter'
  | 'area'
  | 'doughnut'
  | 'radar'
  | 'bubble';

/**
 * Interface para dados de operação de gráfico
 */
export interface ChartOperationData {
  type: ChartType;
  title?: string;
  dataRange: {
    columns: string[]; // Pode ser nomes de coluna ou letras (A, B, etc.)
    startRow?: number;
    endRow?: number;
  };
  options?: {
    showLegend?: boolean;
    xAxisTitle?: string;
    yAxisTitle?: string;
    colors?: string[];
  };
}

/**
 * Executa uma operação de gráfico
 * @param sheetData Dados da planilha
 * @param operation Operação a ser executada
 * @returns Dados atualizados e resumo da operação
 */
export async function executeChartOperation(
  sheetData: any,
  operation: ExcelOperation,
  userId?: string,
  sheetId?: string
): Promise<{ updatedData: any; resultSummary: string }> {
  try {
    // Verificar se já existem gráficos
    const existingCharts = Array.isArray(sheetData.charts) ? sheetData.charts : [];

    // Se houver userId e sheetId, verificar limite de gráficos
    if (userId && sheetId) {
      const limitCheck = await canAddChart(userId, sheetId, existingCharts.length);
      if (!limitCheck.allowed) {
        throw new Error(limitCheck.message || `Limite de gráficos excedido para seu plano.`);
      }
    }

    // Clonar dados para não modificar o original
    const updatedData = { ...sheetData };

    // Inicializar array de gráficos se não existir
    if (!updatedData.charts) {
      updatedData.charts = [];
    }

    // Criar o novo gráfico
    const newChart = {
      id: `chart_${Date.now()}`,
      type: operation.chartType || 'column',
      dataRange: operation.dataRange,
      position: operation.position || 'auto',
      title: operation.title || `Gráfico de ${operation.chartType || 'coluna'}`,
      config: operation.config || {},
    };

    // Adicionar o novo gráfico
    updatedData.charts.push(newChart);

    return {
      updatedData,
      resultSummary: `Gráfico de ${operation.chartType} criado com dados de ${operation.dataRange}`,
    };
  } catch (error) {
    logger.error('[CHART_OPERATION_ERROR]', { operation, error });
    throw error instanceof Error ? error : new Error('Erro ao executar operação de gráfico');
  }
}

/**
 * Extrai dados para um gráfico a partir de ranges da planilha
 * @param sheetData Dados da planilha
 * @param xRange Range para eixo X
 * @param yRange Range para eixo Y
 * @returns Dados formatados para o gráfico
 */
function _extractDataForChart(sheetData: any, xRange: string, yRange: string): any {
  // Extrair coordenadas dos ranges
  const xCoords = parseRange(xRange);
  const yCoords = parseRange(yRange);

  // Extrair valores dos dados para os eixos
  const labels = extractRangeValues(sheetData, xCoords);
  const datasets = [
    {
      data: extractRangeValues(sheetData, yCoords),
      backgroundColor: generateDefaultColors(yCoords.endRow - yCoords.startRow + 1),
      borderColor: generateDefaultColors(yCoords.endRow - yCoords.startRow + 1, 1),
    },
  ];

  return {
    labels,
    datasets,
  };
}

/**
 * Extrai valores de um range de células
 * @param sheetData Dados da planilha
 * @param rangeCoords Coordenadas do range
 * @returns Array de valores extraídos
 */
function extractRangeValues(
  sheetData: any,
  rangeCoords: { startRow: number; startCol: number; endRow: number; endCol: number }
): any[] {
  const { startRow, startCol, endRow, endCol } = rangeCoords;
  const values = [];

  // Para simplicidade, vamos assumir que é uma coluna ou linha (não uma matriz)
  if (startCol === endCol) {
    // Extrair coluna
    for (let row = startRow - 1; row < endRow; row++) {
      if (row < sheetData.rows.length) {
        const value = sheetData.rows[row][startCol - 1];
        // Converter para número se possível
        values.push(isNaN(Number(value)) ? value : Number(value));
      }
    }
  } else if (startRow === endRow) {
    // Extrair linha
    if (startRow - 1 < sheetData.rows.length) {
      const row = sheetData.rows[startRow - 1];
      for (let col = startCol - 1; col < endCol; col++) {
        if (col < row.length) {
          const value = row[col];
          // Converter para número se possível
          values.push(isNaN(Number(value)) ? value : Number(value));
        }
      }
    }
  } else {
    // Caso seja uma matriz, pegamos a primeira linha ou coluna por simplicidade
    for (let row = startRow - 1; row < endRow; row++) {
      if (row < sheetData.rows.length) {
        const value = sheetData.rows[row][startCol - 1];
        // Converter para número se possível
        values.push(isNaN(Number(value)) ? value : Number(value));
      }
    }
  }

  return values;
}

/**
 * Converte range (ex: "A1:C3") para índices de linha e coluna
 * @param range Range de células (ex: "A1:C3")
 * @returns Índices de linha e coluna (1-indexed)
 */
function parseRange(range: string): {
  startRow: number;
  startCol: number;
  endRow: number;
  endCol: number;
} {
  // Dividir o range em células de início e fim
  const parts = range.split(':');
  if (parts.length !== 2) {
    throw new Error(`Range inválido: ${range}`);
  }

  // Converter cada célula para índices
  const start = parseCellReference(safeArrayAccess(parts, 0) || '');
  const end = parseCellReference(safeArrayAccess(parts, 1) || '');

  return {
    startRow: start.row,
    startCol: start.col,
    endRow: end.row,
    endCol: end.col,
  };
}

/**
 * Converte referência de célula (ex: "A1") para índices de linha e coluna
 * @param cellRef Referência de célula (ex: "A1")
 * @returns Índices de linha e coluna (1-indexed)
 */
function parseCellReference(cellRef: string): { row: number; col: number } {
  // Extrair parte alfabética (coluna) e numérica (linha)
  const match = cellRef.match(/([A-Za-z]+)([0-9]+)/);
  if (!match) {
    throw new Error(`Referência de célula inválida: ${cellRef}`);
  }

  const colStr = extractGroup(match, 1);
  const rowStr = extractGroup(match, 2);

  // Converter coluna de alfabética para numérica (A=1, B=2, ...)
  let colNum = 0;
  for (let i = 0; i < colStr.length; i++) {
    colNum = colNum * 26 + (colStr.charCodeAt(i) - 64);
  }

  // Converter linha para número
  const rowNum = parseInt(rowStr, 10);

  return { row: rowNum, col: colNum };
}

/**
 * Gera cores padrão para gráficos
 * @param count Número de cores necessárias
 * @param opacity Opacidade das cores (0-1)
 * @returns Array de cores em formato rgba
 */
function generateDefaultColors(count: number, opacity: number = 0.7): string[] {
  const baseColors = [
    'rgba(255, 99, 132, $opacity)',
    'rgba(54, 162, 235, $opacity)',
    'rgba(255, 206, 86, $opacity)',
    'rgba(75, 192, 192, $opacity)',
    'rgba(153, 102, 255, $opacity)',
    'rgba(255, 159, 64, $opacity)',
    'rgba(199, 199, 199, $opacity)',
    'rgba(83, 102, 255, $opacity)',
    'rgba(40, 159, 193, $opacity)',
    'rgba(210, 105, 30, $opacity)',
  ];

  // Verificar se temos pelo menos uma cor base
  if (baseColors.length === 0) {
    // Retornar uma cor padrão se a lista base estiver vazia
    return Array(count).fill('rgba(128, 128, 128, ' + opacity + ')');
  }

  // Substituir $opacity pelo valor real
  const colorsWithOpacity = baseColors.map(color => color.replace('$opacity', opacity.toString()));

  // Repetir cores se precisarmos de mais do que temos na base
  const result: string[] = [];
  for (let i = 0; i < count; i++) {
    const colorIndex = i % colorsWithOpacity.length;
    // Usar cor padrão caso a cor esteja indefinida
    const defaultColor = 'rgba(128, 128, 128, ' + opacity + ')';
    result.push(colorsWithOpacity[colorIndex] || defaultColor);
  }

  return result;
}

/**
 * Extrai operações relacionadas a gráficos da resposta da IA
 */
export function extractChartOperations(text: string): ExcelOperation[] {
  const operations: ExcelOperation[] = [];
  const chartRegexes = [
    // Corresponde a 'criar um gráfico de [tipo] com dados de [range]'
    /criar\s+(um\s+)?gráfico\s+de\s+(\w+)(?:\s+usando|\s+com)?\s+(?:os\s+)?dados\s+(?:d[aeo]s?\s+)?(?:células\s+)?([A-Z]\d+:[A-Z]\d+|[A-Z]\d+:\w+\d+)/gi,
    // Corresponde a 'adicionar gráfico [tipo] para [range]'
    /adicionar\s+(um\s+)?gráfico\s+(?:de\s+)?(\w+)(?:\s+para|\s+com)?\s+(?:os\s+)?dados\s+(?:d[aeo]s?\s+)?(?:células\s+)?([A-Z]\d+:[A-Z]\d+|[A-Z]\d+:\w+\d+)/gi,
    // Corresponde a 'inserir gráfico [tipo] baseado em [range]'
    /inserir\s+(um\s+)?gráfico\s+(?:de\s+)?(\w+)(?:\s+baseado|\s+com\s+base)(?:\s+em|\s+n[aeo]s?)?\s+(?:os\s+)?dados\s+(?:d[aeo]s?\s+)?(?:células\s+)?([A-Z]\d+:[A-Z]\d+|[A-Z]\d+:\w+\d+)/gi,
  ];

  for (const regex of chartRegexes) {
    let match;
    // Buscar todas as ocorrências no texto
    while ((match = regex.exec(text)) !== null) {
      const chartTypeText = extractGroup(match, 2, 'column');
      const chartType = mapChartType(chartTypeText.toLowerCase());
      const range = extractGroup(match, 3, '');

      operations.push({
        type: 'chart',
        chartType,
        dataRange: range,
        position: 'auto', // Posicionamento automático
        title: `Gráfico de ${chartType}`,
      });
    }
  }

  return operations;
}

/**
 * Mapeia o tipo de gráfico da linguagem natural para tipos conhecidos
 */
function mapChartType(naturalType: string): string {
  const typeMap: Record<string, string> = {
    barra: 'bar',
    barras: 'bar',
    coluna: 'column',
    colunas: 'column',
    linha: 'line',
    linhas: 'line',
    pizza: 'pie',
    torta: 'pie',
    dispersão: 'scatter',
    área: 'area',
    radar: 'radar',
    bolhas: 'bubble',
    donut: 'doughnut',
    rosca: 'doughnut',
  };

  return typeMap[naturalType] || 'column'; // Padrão para coluna se não for reconhecido
}
