/**
 * @jest-environment jsdom
 */

import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { AllProviders } from '../utils/test-providers';

// Import dos componentes específicos para teste
// Nota: Os componentes reais serão importados quando estiverem disponíveis
// Aqui estamos criando mocks para os testes

// Mocks para os componentes de planilha, dashboard e chat
const MockWorkbookComponent = () => (
  <div data-testid="workbook-component">
    <div data-testid="excel-grid" className="grid grid-cols-12 gap-1">
      {Array.from({ length: 10 }).map((_, rowIdx) => (
        <div key={`row-${rowIdx}`} className="flex">
          {Array.from({ length: 5 }).map((_, colIdx) => (
            <div key={`cell-${rowIdx}-${colIdx}`} className="border p-2 min-w-[80px]">
              {`Célula ${rowIdx}${colIdx}`}
            </div>
          ))}
        </div>
      ))}
    </div>
    <div data-testid="toolbar" className="flex gap-2 p-2">
      <button>Formatação</button>
      <button>Fórmulas</button>
      <button>Gráficos</button>
    </div>
  </div>
);

const MockChatInterface = () => (
  <div data-testid="chat-interface" className="flex flex-col h-[400px]">
    <div data-testid="chat-messages" className="flex-1 overflow-y-auto p-4">
      <div className="mb-4">
        <div data-testid="ai-message" className="bg-blue-100 p-3 rounded-lg">
          Olá! Como posso ajudar com seus dados hoje?
        </div>
      </div>
    </div>
    <div data-testid="chat-input-area" className="border-t p-2">
      <form className="flex gap-2">
        <input
          type="text"
          data-testid="chat-input"
          className="flex-1 p-2 border rounded"
          placeholder="Digite uma instrução..."
        />
        <button type="submit" data-testid="send-button">
          Enviar
        </button>
      </form>
    </div>
  </div>
);

const MockDashboard = () => (
  <div data-testid="dashboard" className="p-4">
    <h1 className="text-2xl font-bold mb-4">Suas Planilhas</h1>
    <div
      data-testid="workbook-grid"
      className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"
    >
      {Array.from({ length: 3 }).map((_, idx) => (
        <div key={idx} data-testid={`workbook-card-${idx}`} className="border p-4 rounded-lg">
          <h3 className="font-medium">Planilha {idx + 1}</h3>
          <p className="text-gray-500">Editado há {idx + 1} dias</p>
        </div>
      ))}
    </div>
  </div>
);

// mockSession é importado de test-providers

// AllProviders é importado de test-providers

describe('Inicialização dos Componentes Principais', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('Editor de Planilha inicializa corretamente', async () => {
    render(
      <AllProviders>
        <MockWorkbookComponent />
      </AllProviders>
    );

    // Verificar se o componente principal foi renderizado
    expect(screen.getByTestId('workbook-component')).toBeInTheDocument();

    // Verificar se o grid de células foi renderizado
    const grid = screen.getByTestId('excel-grid');
    expect(grid).toBeInTheDocument();

    // Verificar se a toolbar foi carregada
    const toolbar = screen.getByTestId('toolbar');
    expect(toolbar).toBeInTheDocument();
    expect(toolbar.children.length).toBe(3); // 3 botões

    // Verificar se as células foram renderizadas (total de 50 células: 10 linhas x 5 colunas)
    expect(screen.getAllByText(/Célula \d\d/)).toHaveLength(50);
  });

  test('Interface de Chat inicializa e responde a interações', async () => {
    render(
      <AllProviders>
        <MockChatInterface />
      </AllProviders>
    );

    // Verificar se a interface de chat foi renderizada
    expect(screen.getByTestId('chat-interface')).toBeInTheDocument();

    // Verificar se a área de mensagens foi renderizada
    expect(screen.getByTestId('chat-messages')).toBeInTheDocument();

    // Verificar se existe uma mensagem inicial da IA
    expect(screen.getByTestId('ai-message')).toBeInTheDocument();
    expect(screen.getByText('Olá! Como posso ajudar com seus dados hoje?')).toBeInTheDocument();

    // Verificar se a área de input foi renderizada
    expect(screen.getByTestId('chat-input-area')).toBeInTheDocument();

    // Verificar se é possível interagir com o input
    const chatInput = screen.getByTestId('chat-input');
    const sendButton = screen.getByTestId('send-button');

    expect(chatInput).toBeInTheDocument();
    expect(sendButton).toBeInTheDocument();

    // Simular entrada de texto
    const user = userEvent.setup();
    await user.type(chatInput, 'Calcular média da coluna A');

    // Verificar se o texto foi inserido
    expect(chatInput).toHaveValue('Calcular média da coluna A');
  });

  test('Dashboard inicializa e exibe lista de planilhas', () => {
    render(
      <AllProviders>
        <MockDashboard />
      </AllProviders>
    );

    // Verificar se o dashboard foi renderizado
    expect(screen.getByTestId('dashboard')).toBeInTheDocument();

    // Verificar se o título está presente
    expect(screen.getByText('Suas Planilhas')).toBeInTheDocument();

    // Verificar se os cards de planilhas foram renderizados
    expect(screen.getByTestId('workbook-grid')).toBeInTheDocument();

    // Verificar se existem 3 cards de planilhas
    const cards = [
      screen.getByTestId('workbook-card-0'),
      screen.getByTestId('workbook-card-1'),
      screen.getByTestId('workbook-card-2'),
    ];

    cards.forEach(card => {
      expect(card).toBeInTheDocument();
    });

    // Verificar se os textos dos cards estão corretos
    expect(screen.getByText('Planilha 1')).toBeInTheDocument();
    expect(screen.getByText('Planilha 2')).toBeInTheDocument();
    expect(screen.getByText('Planilha 3')).toBeInTheDocument();
  });

  test('Componentes se adaptam ao tamanho da tela', () => {
    // Mock para testar responsividade
    const originalInnerWidth = window.innerWidth;

    try {
      // Simular tela menor
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 480, // tamanho de tela de celular
      });
      window.dispatchEvent(new Event('resize'));

      render(
        <AllProviders>
          <MockDashboard />
        </AllProviders>
      );

      // No modo mobile, esperamos que a grid seja de uma coluna
      const gridElement = screen.getByTestId('workbook-grid');
      expect(gridElement).toHaveClass('grid-cols-1');

      // Simular tela maior
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 1200, // tamanho de tela grande
      });
      window.dispatchEvent(new Event('resize'));

      // Não podemos verificar a mudança efetiva no tamanho da tela
      // em testes de unidade, mas podemos verificar se a classe está presente
      expect(gridElement).toHaveClass('lg:grid-cols-3');
    } finally {
      // Restaurar o tamanho original da janela
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: originalInnerWidth,
      });
      window.dispatchEvent(new Event('resize'));
    }
  });
});
