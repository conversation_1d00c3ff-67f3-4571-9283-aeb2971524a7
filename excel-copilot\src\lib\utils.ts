import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

import { validateEnvironment } from './env-validator';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// Formatação de data para exibição
export function formatDate(date: Date | string): string {
  if (!date) return '';

  const d = typeof date === 'string' ? new Date(date) : date;

  return new Intl.DateTimeFormat('pt-BR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(d);
}

// Converter uma string em título (primeira letra de cada palavra maiúscula)
export function toTitleCase(str: string): string {
  return str
    .toLowerCase()
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

// Verificar se um objeto é vazio
export function isObjectEmpty(obj: Record<string, unknown>): boolean {
  return Object.keys(obj).length === 0;
}

// Formatar número como moeda brasileira
export function formatCurrency(value: number): string {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL',
  }).format(value);
}

/**
 * Extrai o valor de uma célula do Excel para exibição
 * @param cell Valor da célula que pode estar em diferentes formatos
 * @returns O valor formatado para exibição
 */
export function extractCellValue(value: unknown): unknown {
  if (!value) return '';

  // Se o valor tiver uma propriedade 'result', extraí-la
  if (value && typeof value === 'object' && 'result' in value) {
    return value.result;
  }

  return value;
}

// Detecta se um array de valores parece ser numérico para gráficos
export function isNumericDataArray(values: unknown[]): boolean {
  const numValues = values.filter(val => val !== null && val !== undefined && !isNaN(Number(val)));
  return numValues.length > 0 && numValues.length >= values.length * 0.7; // 70% dos valores são numéricos
}

// Gera uma cor aleatória para gráficos
export function getRandomColor(opacity = 1): string {
  const r = Math.floor(Math.random() * 256);
  const g = Math.floor(Math.random() * 256);
  const b = Math.floor(Math.random() * 256);
  return `rgba(${r}, ${g}, ${b}, ${opacity})`;
}

// Gera um array de cores para gráficos
export function generateColors(count: number, opacity = 1): string[] {
  return Array.from({ length: count }, () => getRandomColor(opacity));
}

// Converter objeto para string JSON de forma segura
export function safeStringify(obj: unknown): string {
  // Lidar com ciclicidade
  const cache = new Set();

  return JSON.stringify(
    obj,
    (key, value) => {
      if (typeof value === 'object' && value !== null) {
        if (cache.has(value)) {
          return '[Circular]';
        }
        cache.add(value);
      }
      return value;
    },
    2
  );
}

// Gerar um ID aleatório para uso em componentes
export function generateId(prefix: string = 'id'): string {
  return `${prefix}-${Math.random().toString(36).substring(2, 9)}`;
}

// Re-exportar a função de validação de ambiente centralizada
export { validateEnvironment as validateEnv };

/**
 * Verifica se o contraste entre duas cores está dentro dos padrões de acessibilidade WCAG
 * @param foreground Cor de texto (formato hex: #RRGGBB)
 * @param background Cor de fundo (formato hex: #RRGGBB)
 * @returns true se o contraste for adequado (>=4.5:1 para WCAG AA)
 */
export function hasAdequateContrast(foreground: string, background: string): boolean {
  // Converte as cores para RGB
  const fgRGB = hexToRGB(foreground);
  const bgRGB = hexToRGB(background);

  // Se alguma das cores não for válida, retorna false
  if (!fgRGB || !bgRGB) return false;

  // Calcula a luminância relativa de cada cor
  const fgLuminance = relativeLuminance(fgRGB.r, fgRGB.g, fgRGB.b);
  const bgLuminance = relativeLuminance(bgRGB.r, bgRGB.g, bgRGB.b);

  // Calcula a razão de contraste
  const lighterLum = Math.max(fgLuminance, bgLuminance);
  const darkerLum = Math.min(fgLuminance, bgLuminance);
  const contrastRatio = (lighterLum + 0.05) / (darkerLum + 0.05);

  // WCAG AA requer contraste mínimo de 4.5:1 para texto normal
  return contrastRatio >= 4.5;
}

/**
 * Converte uma cor hexadecimal em RGB
 */
function hexToRGB(hex: string): { r: number; g: number; b: number } | null {
  // Remove o '#' se existir
  const cleanHex = hex.replace(/^#/, '');

  // Verifica se é um formato de cor válido
  if (!/^([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(cleanHex)) {
    return null;
  }

  // Se for uma cor de 3 dígitos, expande para 6
  const processedHex =
    cleanHex.length === 3
      ? cleanHex.charAt(0) +
        cleanHex.charAt(0) +
        cleanHex.charAt(1) +
        cleanHex.charAt(1) +
        cleanHex.charAt(2) +
        cleanHex.charAt(2)
      : cleanHex;

  // Converte para RGB
  const r = parseInt(processedHex.substring(0, 2), 16) / 255;
  const g = parseInt(processedHex.substring(2, 4), 16) / 255;
  const b = parseInt(processedHex.substring(4, 6), 16) / 255;

  return { r, g, b };
}

/**
 * Calcula a luminância relativa de uma cor RGB conforme recomendação WCAG
 */
function relativeLuminance(r: number, g: number, b: number): number {
  // Ajusta os valores conforme fórmula de luminância sRGB
  const adjustValue = (val: number): number => {
    return val <= 0.03928 ? val / 12.92 : Math.pow((val + 0.055) / 1.055, 2.4);
  };

  const rLinear = adjustValue(r);
  const gLinear = adjustValue(g);
  const bLinear = adjustValue(b);

  // Fórmula de luminância relativa
  return 0.2126 * rLinear + 0.7152 * gLinear + 0.0722 * bLinear;
}

/**
 * Converte letras de coluna do Excel (A, B, C...) para índices numéricos (0, 1, 2...)
 */
export function columnLetterToIndex(column: string): number {
  let index = 0;
  for (let i = 0; i < column.length; i++) {
    index = index * 26 + column.charCodeAt(i) - 64;
  }
  return index - 1;
}

/**
 * Converte índice numérico para letra de coluna do Excel (0 -> A, 1 -> B, etc.)
 */
export function indexToColumnLetter(index: number): string {
  let temp = index + 1;
  let letter = '';

  while (temp > 0) {
    const mod = (temp - 1) % 26;
    letter = String.fromCharCode(65 + mod) + letter;
    temp = Math.floor((temp - mod) / 26);
  }

  return letter;
}

/**
 * Normaliza um texto removendo acentos e colocando em minúsculas
 * @param text Texto para normalizar
 * @returns Texto normalizado
 */
export function normalizeCommand(text: string): string {
  if (!text) return '';

  // Converter para minúsculas
  const lowerCase = text.toLowerCase();

  // Remover acentos
  return lowerCase.normalize('NFD').replace(/[\u0300-\u036f]/g, '');
}

/**
 * Gera um ID único curto para uso em componentes e tracking
 * @returns String ID único
 */
export function nanoid(): string {
  // Gera um ID de 10 caracteres alfanuméricos
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';

  // Gerar 10 caracteres aleatórios
  for (let i = 0; i < 10; i++) {
    result += characters.charAt(Math.floor(Math.random() * characters.length));
  }

  return result;
}
