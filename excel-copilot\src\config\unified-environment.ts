/**
 * 🔧 CONFIGURAÇÃO UNIFICADA DE AMBIENTE - EXCEL COPILOT
 *
 * Este arquivo centraliza TODA a configuração de ambiente da aplicação,
 * eliminando conflitos e inconsistências entre diferentes arquivos.
 *
 * PRINCÍPIOS:
 * 1. Uma única fonte da verdade para configurações
 * 2. Validação rigorosa com mensagens específicas
 * 3. Hierarquia clara de precedência
 * 4. Fallbacks seguros e previsíveis
 * 5. Tipos TypeScript para todas as configurações
 *
 * <AUTHOR> Copilot Team
 * @version 2.0.0
 */

import { z } from 'zod';

// ============================================================================
// TIPOS E INTERFACES
// ============================================================================

/**
 * Tipos de ambiente suportados
 */
export type Environment = 'development' | 'production' | 'test';

/**
 * Níveis de log disponíveis
 */
export type LogLevel = 'debug' | 'info' | 'warn' | 'error';

/**
 * Providers de autenticação suportados
 */
export type AuthProvider = 'google' | 'github';

/**
 * Status de configuração de um serviço
 */
export type ServiceStatus = 'enabled' | 'disabled' | 'mock' | 'fallback';

/**
 * Interface para resultado de validação
 */
export interface ValidationResult {
  valid: boolean;
  errors: string[];
  warnings: string[];
  missing: string[];
  conflicts: string[];
}

/**
 * Interface para configuração de um serviço externo
 */
export interface ServiceConfig {
  enabled: boolean;
  status: ServiceStatus;
  credentials: Record<string, string>;
  endpoints?: Record<string, string>;
  timeouts?: Record<string, number>;
}

// ============================================================================
// SCHEMAS DE VALIDAÇÃO ZOD
// ============================================================================

/**
 * Schema para validação de URLs
 */
const urlSchema = z.string().url('Deve ser uma URL válida');

/**
 * Schema para validação de tokens/secrets
 */
const secretSchema = z.string().min(8, 'Deve ter pelo menos 8 caracteres');

/**
 * Schema para validação de IDs
 */
const idSchema = z.string().min(1, 'Não pode estar vazio');

/**
 * Schema principal de configuração
 */
const environmentSchema = z.object({
  // Configurações básicas
  NODE_ENV: z.enum(['development', 'production', 'test']),
  APP_NAME: z.string().default('Excel Copilot'),
  APP_VERSION: z.string().default('1.0.0'),
  APP_URL: urlSchema,

  // Autenticação
  AUTH_NEXTAUTH_SECRET: secretSchema,
  AUTH_NEXTAUTH_URL: urlSchema,
  AUTH_GOOGLE_CLIENT_ID: idSchema.optional(),
  AUTH_GOOGLE_CLIENT_SECRET: secretSchema.optional(),
  AUTH_GITHUB_CLIENT_ID: idSchema.optional(),
  AUTH_GITHUB_CLIENT_SECRET: secretSchema.optional(),
  AUTH_SKIP_PROVIDERS: z.boolean().default(false),

  // Banco de dados
  DB_DATABASE_URL: z.string().min(1, 'URL do banco é obrigatória'),
  DB_DIRECT_URL: z.string().optional(),
  DB_PROVIDER: z.enum(['postgresql', 'sqlite']).default('postgresql'),

  // Inteligência Artificial
  AI_ENABLED: z.boolean().default(true),
  AI_USE_MOCK: z.boolean().default(false),
  AI_VERTEX_PROJECT_ID: z.string().optional(),
  AI_VERTEX_LOCATION: z.string().default('us-central1'),
  AI_VERTEX_MODEL: z.string().default('gemini-2.0-flash-001'),

  // Stripe (Pagamentos)
  STRIPE_ENABLED: z.boolean().default(true),
  STRIPE_SECRET_KEY: z.string().optional(),
  STRIPE_WEBHOOK_SECRET: z.string().optional(),
  STRIPE_PUBLISHABLE_KEY: z.string().optional(),

  // Supabase
  SUPABASE_URL: urlSchema.optional(),
  SUPABASE_ANON_KEY: z.string().optional(),
  SUPABASE_SERVICE_ROLE_KEY: z.string().optional(),

  // MCPs (Model Context Protocol)
  MCP_VERCEL_TOKEN: z.string().optional(),
  MCP_VERCEL_PROJECT_ID: z.string().optional(),
  MCP_VERCEL_TEAM_ID: z.string().optional(),
  MCP_LINEAR_API_KEY: z.string().optional(),
  MCP_GITHUB_TOKEN: z.string().optional(),

  // Configurações de desenvolvimento
  DEV_DISABLE_VALIDATION: z.boolean().default(false),
  DEV_FORCE_PRODUCTION: z.boolean().default(false),
  DEV_LOG_LEVEL: z.enum(['debug', 'info', 'warn', 'error']).default('info'),

  // Configurações de segurança
  SECURITY_CSRF_SECRET: z.string().optional(),
  SECURITY_RATE_LIMIT_ENABLED: z.boolean().default(true),
  SECURITY_CORS_ORIGINS: z.string().optional(),
});

// ============================================================================
// UTILITÁRIOS DE CONFIGURAÇÃO
// ============================================================================

/**
 * Obtém uma variável de ambiente com fallback
 */
function getEnvVar(key: string, fallback?: string): string | undefined {
  return process.env[key] || fallback;
}

/**
 * Converte string para boolean de forma segura
 */
function parseBoolean(value: string | undefined, defaultValue = false): boolean {
  if (!value) return defaultValue;
  return ['true', '1', 'yes', 'on'].includes(value.toLowerCase());
}

/**
 * Detecta se estamos rodando no Vercel
 */
function _isVercelEnvironment(): boolean {
  return process.env.VERCEL === '1' || process.env.VERCEL_ENV !== undefined;
}

/**
 * Detecta se estamos no lado do servidor
 */
function isServerSide(): boolean {
  return typeof window === 'undefined';
}

// ============================================================================
// CONFIGURAÇÃO UNIFICADA
// ============================================================================

/**
 * Classe principal para gerenciar configuração unificada
 */
export class UnifiedEnvironment {
  private static instance: UnifiedEnvironment;
  private config: z.infer<typeof environmentSchema>;
  private validationResult: ValidationResult;
  private initialized = false;

  private constructor() {
    this.config = this.loadConfiguration();
    this.validationResult = this.validateConfiguration();
  }

  /**
   * Singleton pattern - garante uma única instância
   */
  public static getInstance(): UnifiedEnvironment {
    if (!UnifiedEnvironment.instance) {
      UnifiedEnvironment.instance = new UnifiedEnvironment();
    }
    return UnifiedEnvironment.instance;
  }

  /**
   * Carrega configuração de variáveis de ambiente
   */
  private loadConfiguration(): z.infer<typeof environmentSchema> {
    const rawConfig = {
      // Configurações básicas
      NODE_ENV: getEnvVar('NODE_ENV', 'development') as Environment,
      APP_NAME: getEnvVar('APP_NAME', 'Excel Copilot'),
      APP_VERSION: getEnvVar('APP_VERSION', '1.0.0'),
      APP_URL: getEnvVar('NEXT_PUBLIC_APP_URL') || getEnvVar('APP_URL', 'http://localhost:3000'),

      // Autenticação - PADRONIZAÇÃO COM PREFIXO AUTH_
      AUTH_NEXTAUTH_SECRET: getEnvVar('AUTH_NEXTAUTH_SECRET'),
      AUTH_NEXTAUTH_URL: getEnvVar('AUTH_NEXTAUTH_URL'),
      AUTH_GOOGLE_CLIENT_ID: getEnvVar('AUTH_GOOGLE_CLIENT_ID'),
      AUTH_GOOGLE_CLIENT_SECRET: getEnvVar('AUTH_GOOGLE_CLIENT_SECRET'),
      AUTH_GITHUB_CLIENT_ID: getEnvVar('AUTH_GITHUB_CLIENT_ID'),
      AUTH_GITHUB_CLIENT_SECRET: getEnvVar('AUTH_GITHUB_CLIENT_SECRET'),
      AUTH_SKIP_PROVIDERS: parseBoolean(getEnvVar('AUTH_SKIP_PROVIDERS')),

      // Banco de dados - PADRONIZAÇÃO COM PREFIXO DB_
      DB_DATABASE_URL: getEnvVar('DB_DATABASE_URL'),
      DB_DIRECT_URL: getEnvVar('DB_DIRECT_URL'),
      DB_PROVIDER: getEnvVar('DB_PROVIDER', 'postgresql'),

      // IA - LÓGICA UNIFICADA (elimina conflitos)
      AI_ENABLED: this.resolveAIConfiguration(),
      AI_USE_MOCK: this.resolveAIMockConfiguration(),
      AI_VERTEX_PROJECT_ID: getEnvVar('AI_VERTEX_PROJECT_ID'),
      AI_VERTEX_LOCATION: getEnvVar('AI_VERTEX_LOCATION', 'us-central1'),
      AI_VERTEX_MODEL: getEnvVar('AI_VERTEX_MODEL', 'gemini-2.0-flash-001'),

      // Stripe - PADRONIZAÇÃO COM PREFIXO STRIPE_
      STRIPE_ENABLED: !parseBoolean(getEnvVar('DISABLE_STRIPE')),
      STRIPE_SECRET_KEY: getEnvVar('STRIPE_SECRET_KEY'),
      STRIPE_WEBHOOK_SECRET: getEnvVar('STRIPE_WEBHOOK_SECRET'),
      STRIPE_PUBLISHABLE_KEY: getEnvVar('NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY'),

      // Supabase
      SUPABASE_URL: getEnvVar('SUPABASE_URL') || getEnvVar('NEXT_PUBLIC_SUPABASE_URL'),
      SUPABASE_ANON_KEY:
        getEnvVar('SUPABASE_ANON_KEY') || getEnvVar('NEXT_PUBLIC_SUPABASE_ANON_KEY'),
      SUPABASE_SERVICE_ROLE_KEY: getEnvVar('SUPABASE_SERVICE_ROLE_KEY'),

      // MCPs - PADRONIZAÇÃO COM PREFIXO MCP_
      MCP_VERCEL_TOKEN: getEnvVar('MCP_VERCEL_TOKEN'),
      MCP_VERCEL_PROJECT_ID: getEnvVar('MCP_VERCEL_PROJECT_ID'),
      MCP_VERCEL_TEAM_ID: getEnvVar('MCP_VERCEL_TEAM_ID'),
      MCP_LINEAR_API_KEY: getEnvVar('MCP_LINEAR_API_KEY'),
      MCP_GITHUB_TOKEN: getEnvVar('MCP_GITHUB_TOKEN'),

      // Desenvolvimento
      DEV_DISABLE_VALIDATION: parseBoolean(getEnvVar('DEV_DISABLE_VALIDATION')),
      DEV_FORCE_PRODUCTION: parseBoolean(getEnvVar('DEV_FORCE_PRODUCTION')),
      DEV_LOG_LEVEL: getEnvVar('DEV_LOG_LEVEL', 'info') as LogLevel,

      // Segurança
      SECURITY_CSRF_SECRET: getEnvVar('SECURITY_CSRF_SECRET'),
      SECURITY_RATE_LIMIT_ENABLED: !parseBoolean(getEnvVar('SECURITY_RATE_LIMIT_ENABLED')),
      SECURITY_CORS_ORIGINS: getEnvVar('SECURITY_CORS_ORIGINS'),
    };

    return rawConfig as z.infer<typeof environmentSchema>;
  }

  /**
   * RESOLVE CONFLITOS DE CONFIGURAÇÃO DE IA
   *
   * Hierarquia de precedência (do maior para o menor):
   * 1. NEXT_PUBLIC_DISABLE_VERTEX_AI=true → AI_ENABLED=false
   * 2. FORCE_GOOGLE_MOCKS=true → AI_USE_MOCK=true
   * 3. USE_MOCK_AI=true → AI_USE_MOCK=true
   * 4. VERTEX_AI_ENABLED=false → AI_ENABLED=false
   * 5. Ausência de VERTEX_AI_PROJECT_ID → AI_USE_MOCK=true
   */
  private resolveAIConfiguration(): boolean {
    // Prioridade 1: Desabilitação explícita
    if (parseBoolean(getEnvVar('AI_ENABLED'))) {
      return false;
    }

    // Prioridade 2: Configuração explícita de habilitação
    const vertexEnabled = getEnvVar('AI_ENABLED');
    if (vertexEnabled !== undefined) {
      return parseBoolean(vertexEnabled, true);
    }

    // Prioridade 3: Se tem projeto configurado, habilitar
    const hasProjectId = getEnvVar('AI_VERTEX_PROJECT_ID');
    if (hasProjectId) {
      return true;
    }

    // Fallback: Habilitado por padrão em produção, desabilitado em desenvolvimento
    const nodeEnv = getEnvVar('NODE_ENV', 'development');
    return nodeEnv === 'production';
  }

  /**
   * RESOLVE CONFLITOS DE MOCK DE IA
   *
   * Hierarquia de precedência:
   * 1. FORCE_GOOGLE_MOCKS=true → sempre mock
   * 2. USE_MOCK_AI=true → sempre mock
   * 3. AI_ENABLED=false → sempre mock
   * 4. Ausência de credenciais → mock
   * 5. Ambiente de desenvolvimento → mock por padrão
   */
  private resolveAIMockConfiguration(): boolean {
    // Prioridade 1: Forçar mocks
    if (parseBoolean(getEnvVar('AI_USE_MOCK'))) {
      return true;
    }

    // Prioridade 2: Mock explicitamente habilitado
    if (parseBoolean(getEnvVar('AI_USE_MOCK'))) {
      return true;
    }

    // Prioridade 3: Se IA está desabilitada, usar mock
    if (!this.resolveAIConfiguration()) {
      return true;
    }

    // Prioridade 4: Se não tem credenciais, usar mock
    const hasCredentials = getEnvVar('AI_VERTEX_PROJECT_ID') || getEnvVar('VERTEX_AI_CREDENTIALS');
    if (!hasCredentials) {
      return true;
    }

    // Prioridade 5: Em desenvolvimento, usar mock por padrão
    const nodeEnv = getEnvVar('NODE_ENV', 'development');
    return nodeEnv === 'development';
  }

  /**
   * Valida toda a configuração carregada
   */
  private validateConfiguration(): ValidationResult {
    const result: ValidationResult = {
      valid: true,
      errors: [],
      warnings: [],
      missing: [],
      conflicts: [],
    };

    try {
      // Validação com Zod
      const _parsed = environmentSchema.parse(this.config);

      // Validações específicas por ambiente
      this.validateByEnvironment(result);

      // Validações de dependências
      this.validateDependencies(result);

      // Validações de conflitos
      this.validateConflicts(result);

      // Validações de segurança
      this.validateSecurity(result);
    } catch (error) {
      if (error instanceof z.ZodError) {
        result.errors.push(...error.errors.map(e => `${e.path.join('.')}: ${e.message}`));
      } else {
        result.errors.push(`Erro de validação: ${error}`);
      }
    }

    result.valid = result.errors.length === 0;
    return result;
  }

  /**
   * Validações específicas por ambiente
   */
  private validateByEnvironment(result: ValidationResult): void {
    const env = this.config.NODE_ENV;

    if (env === 'production') {
      // Em produção, certas variáveis são obrigatórias
      const requiredInProduction = ['AUTH_NEXTAUTH_SECRET', 'AUTH_NEXTAUTH_URL', 'DB_DATABASE_URL'];

      for (const key of requiredInProduction) {
        if (!this.config[key as keyof typeof this.config]) {
          result.errors.push(`${key} é obrigatória em produção`);
        }
      }

      // Em produção, deve ter pelo menos um provider OAuth
      if (!this.config.AUTH_GOOGLE_CLIENT_ID && !this.config.AUTH_GITHUB_CLIENT_ID) {
        result.errors.push('Pelo menos um provider OAuth deve estar configurado em produção');
      }

      // Em produção, não deve pular validação
      if (this.config.DEV_DISABLE_VALIDATION) {
        result.warnings.push('DISABLE_ENV_VALIDATION não deveria estar ativo em produção');
      }
    }

    if (env === 'development') {
      // Em desenvolvimento, avisar sobre configurações ausentes
      if (!this.config.AUTH_GOOGLE_CLIENT_ID && !this.config.AUTH_GITHUB_CLIENT_ID) {
        result.warnings.push('Nenhum provider OAuth configurado - usando modo de desenvolvimento');
      }
    }
  }

  /**
   * Validações de dependências entre serviços
   */
  private validateDependencies(result: ValidationResult): void {
    // Se Stripe está habilitado, deve ter as chaves
    if (this.config.STRIPE_ENABLED) {
      if (!this.config.STRIPE_SECRET_KEY) {
        result.errors.push('STRIPE_SECRET_KEY é obrigatória quando Stripe está habilitado');
      }
      if (!this.config.STRIPE_PUBLISHABLE_KEY) {
        result.errors.push('STRIPE_PUBLISHABLE_KEY é obrigatória quando Stripe está habilitado');
      }
    }

    // Se IA está habilitada e não é mock, deve ter credenciais
    if (this.config.AI_ENABLED && !this.config.AI_USE_MOCK) {
      if (!this.config.AI_VERTEX_PROJECT_ID) {
        result.warnings.push('AI habilitada sem VERTEX_AI_PROJECT_ID - usando modo mock');
      }
    }

    // Se Supabase está sendo usado, deve ter URL e chave
    if (this.config.DB_DATABASE_URL?.includes('supabase')) {
      if (!this.config.SUPABASE_URL) {
        result.warnings.push('Usando Supabase mas SUPABASE_URL não configurada');
      }
      if (!this.config.SUPABASE_ANON_KEY) {
        result.warnings.push('Usando Supabase mas SUPABASE_ANON_KEY não configurada');
      }
    }
  }

  /**
   * Detecta conflitos na configuração
   */
  private validateConflicts(result: ValidationResult): void {
    // Conflito: IA habilitada mas forçando mocks
    if (this.config.AI_ENABLED && parseBoolean(getEnvVar('AI_USE_MOCK'))) {
      result.conflicts.push('AI_ENABLED=true mas FORCE_GOOGLE_MOCKS=true - usando mocks');
    }

    // Conflito: Múltiplas configurações de mock
    const mockFlags = [
      getEnvVar('AI_USE_MOCK'),
      getEnvVar('AI_USE_MOCK'),
      getEnvVar('AI_ENABLED'),
    ].filter(Boolean);

    if (mockFlags.length > 1) {
      result.conflicts.push(
        'Múltiplas flags de mock configuradas - usando hierarquia de precedência'
      );
    }

    // Conflito: Ambiente de produção com configurações de desenvolvimento
    if (this.config.NODE_ENV === 'production') {
      if (this.config.AUTH_SKIP_PROVIDERS) {
        result.conflicts.push(
          'SKIP_AUTH_PROVIDERS=true em produção - pode causar problemas de autenticação'
        );
      }
    }
  }

  /**
   * Validações de segurança
   */
  private validateSecurity(result: ValidationResult): void {
    // Verificar força do NEXTAUTH_SECRET
    if (this.config.AUTH_NEXTAUTH_SECRET && this.config.AUTH_NEXTAUTH_SECRET.length < 32) {
      result.warnings.push(
        'NEXTAUTH_SECRET deveria ter pelo menos 32 caracteres para máxima segurança'
      );
    }

    // Verificar URLs de produção
    if (this.config.NODE_ENV === 'production') {
      if (this.config.AUTH_NEXTAUTH_URL?.includes('localhost')) {
        result.errors.push('NEXTAUTH_URL não pode ser localhost em produção');
      }
      if (this.config.APP_URL?.includes('localhost')) {
        result.errors.push('APP_URL não pode ser localhost em produção');
      }
    }

    // Verificar CORS em produção
    if (this.config.NODE_ENV === 'production' && !this.config.SECURITY_CORS_ORIGINS) {
      result.warnings.push(
        'CORS_ORIGINS não configurado em produção - pode causar problemas de segurança'
      );
    }
  }

  // ============================================================================
  // MÉTODOS PÚBLICOS
  // ============================================================================

  /**
   * Obtém toda a configuração validada
   */
  public getConfig(): z.infer<typeof environmentSchema> {
    return { ...this.config };
  }

  /**
   * Obtém resultado da validação
   */
  public getValidationResult(): ValidationResult {
    return { ...this.validationResult };
  }

  /**
   * Verifica se a configuração é válida
   */
  public isValid(): boolean {
    return this.validationResult.valid;
  }

  /**
   * Obtém configuração específica por categoria
   */
  public getAuthConfig(): ServiceConfig {
    return {
      enabled: !this.config.AUTH_SKIP_PROVIDERS,
      status: this.config.AUTH_SKIP_PROVIDERS ? 'disabled' : 'enabled',
      credentials: {
        nextAuthSecret: this.config.AUTH_NEXTAUTH_SECRET || '',
        nextAuthUrl: this.config.AUTH_NEXTAUTH_URL || '',
        googleClientId: this.config.AUTH_GOOGLE_CLIENT_ID || '',
        googleClientSecret: this.config.AUTH_GOOGLE_CLIENT_SECRET || '',
        githubClientId: this.config.AUTH_GITHUB_CLIENT_ID || '',
        githubClientSecret: this.config.AUTH_GITHUB_CLIENT_SECRET || '',
      },
    };
  }

  /**
   * Obtém configuração de IA com lógica unificada
   */
  public getAIConfig(): ServiceConfig {
    const enabled = this.config.AI_ENABLED;
    const useMock = this.config.AI_USE_MOCK;

    let status: ServiceStatus = 'enabled';
    if (!enabled) status = 'disabled';
    else if (useMock) status = 'mock';

    return {
      enabled,
      status,
      credentials: {
        projectId: this.config.AI_VERTEX_PROJECT_ID || '',
        location: this.config.AI_VERTEX_LOCATION,
        model: this.config.AI_VERTEX_MODEL,
      },
    };
  }

  /**
   * Obtém configuração do banco de dados
   */
  public getDatabaseConfig(): ServiceConfig {
    return {
      enabled: !!this.config.DB_DATABASE_URL,
      status: this.config.DB_DATABASE_URL ? 'enabled' : 'disabled',
      credentials: {
        databaseUrl: this.config.DB_DATABASE_URL || '',
        directUrl: this.config.DB_DIRECT_URL || '',
        provider: this.config.DB_PROVIDER,
      },
    };
  }

  /**
   * Obtém configuração do Stripe
   */
  public getStripeConfig(): ServiceConfig {
    return {
      enabled: this.config.STRIPE_ENABLED,
      status: this.config.STRIPE_ENABLED ? 'enabled' : 'disabled',
      credentials: {
        secretKey: this.config.STRIPE_SECRET_KEY || '',
        webhookSecret: this.config.STRIPE_WEBHOOK_SECRET || '',
        publishableKey: this.config.STRIPE_PUBLISHABLE_KEY || '',
      },
    };
  }

  /**
   * Obtém configuração das MCPs
   */
  public getMCPConfig(): Record<string, ServiceConfig> {
    return {
      vercel: {
        enabled: !!this.config.MCP_VERCEL_TOKEN,
        status: this.config.MCP_VERCEL_TOKEN ? 'enabled' : 'disabled',
        credentials: {
          token: this.config.MCP_VERCEL_TOKEN || '',
          projectId: this.config.MCP_VERCEL_PROJECT_ID || '',
          teamId: this.config.MCP_VERCEL_TEAM_ID || '',
        },
      },
      linear: {
        enabled: !!this.config.MCP_LINEAR_API_KEY,
        status: this.config.MCP_LINEAR_API_KEY ? 'enabled' : 'disabled',
        credentials: {
          apiKey: this.config.MCP_LINEAR_API_KEY || '',
        },
      },
      github: {
        enabled: !!this.config.MCP_GITHUB_TOKEN,
        status: this.config.MCP_GITHUB_TOKEN ? 'enabled' : 'disabled',
        credentials: {
          token: this.config.MCP_GITHUB_TOKEN || '',
        },
      },
    };
  }

  /**
   * Força revalidação da configuração
   */
  public revalidate(): ValidationResult {
    this.config = this.loadConfiguration();
    this.validationResult = this.validateConfiguration();
    return this.getValidationResult();
  }

  /**
   * Gera relatório detalhado da configuração
   */
  public generateReport(): string {
    const result = this.validationResult;
    const config = this.config;

    let report = '🔧 RELATÓRIO DE CONFIGURAÇÃO - EXCEL COPILOT\n';
    report += '='.repeat(60) + '\n\n';

    // Status geral
    report += `✅ Status Geral: ${result.valid ? 'VÁLIDA' : 'INVÁLIDA'}\n`;
    report += `🌍 Ambiente: ${config.NODE_ENV}\n`;
    report += `📱 Aplicação: ${config.APP_NAME} v${config.APP_VERSION}\n`;
    report += `🔗 URL: ${config.APP_URL}\n\n`;

    // Serviços
    report += '📋 STATUS DOS SERVIÇOS:\n';
    report += '-'.repeat(30) + '\n';

    const authConfig = this.getAuthConfig();
    report += `🔐 Autenticação: ${authConfig.status.toUpperCase()}\n`;

    const aiConfig = this.getAIConfig();
    report += `🤖 Inteligência Artificial: ${aiConfig.status.toUpperCase()}\n`;

    const dbConfig = this.getDatabaseConfig();
    report += `🗄️  Banco de Dados: ${dbConfig.status.toUpperCase()}\n`;

    const stripeConfig = this.getStripeConfig();
    report += `💳 Stripe: ${stripeConfig.status.toUpperCase()}\n`;

    const mcpConfigs = this.getMCPConfig();
    report += `🔌 Vercel MCP: ${mcpConfigs.vercel?.status?.toUpperCase() || 'DISABLED'}\n`;
    report += `🔌 Linear MCP: ${mcpConfigs.linear?.status?.toUpperCase() || 'DISABLED'}\n`;
    report += `🔌 GitHub MCP: ${mcpConfigs.github?.status?.toUpperCase() || 'DISABLED'}\n\n`;

    // Erros
    if (result.errors.length > 0) {
      report += '❌ ERROS:\n';
      result.errors.forEach(error => (report += `  • ${error}\n`));
      report += '\n';
    }

    // Avisos
    if (result.warnings.length > 0) {
      report += '⚠️  AVISOS:\n';
      result.warnings.forEach(warning => (report += `  • ${warning}\n`));
      report += '\n';
    }

    // Conflitos
    if (result.conflicts.length > 0) {
      report += '🔄 CONFLITOS RESOLVIDOS:\n';
      result.conflicts.forEach(conflict => (report += `  • ${conflict}\n`));
      report += '\n';
    }

    return report;
  }
}

// ============================================================================
// INSTÂNCIA GLOBAL E EXPORTS
// ============================================================================

/**
 * Instância global da configuração unificada
 */
export const unifiedEnv = UnifiedEnvironment.getInstance();

/**
 * Configuração compatível com o sistema antigo (para migração gradual)
 */
export const ENV = {
  NODE_ENV: unifiedEnv.getConfig().NODE_ENV,
  IS_DEVELOPMENT: unifiedEnv.getConfig().NODE_ENV === 'development',
  IS_PRODUCTION: unifiedEnv.getConfig().NODE_ENV === 'production',
  IS_TEST: unifiedEnv.getConfig().NODE_ENV === 'test',
  IS_SERVER: isServerSide(),

  // Configurações básicas
  APP: {
    NAME: unifiedEnv.getConfig().APP_NAME,
    VERSION: unifiedEnv.getConfig().APP_VERSION,
    URL: unifiedEnv.getConfig().APP_URL,
  },

  // Autenticação
  NEXTAUTH_SECRET: unifiedEnv.getConfig().AUTH_NEXTAUTH_SECRET,
  NEXTAUTH_URL: unifiedEnv.getConfig().AUTH_NEXTAUTH_URL,

  // APIs
  API_KEYS: {
    GOOGLE_CLIENT_ID: unifiedEnv.getConfig().AUTH_GOOGLE_CLIENT_ID || '',
    GOOGLE_CLIENT_SECRET: unifiedEnv.getConfig().AUTH_GOOGLE_CLIENT_SECRET || '',
    GITHUB_CLIENT_ID: unifiedEnv.getConfig().AUTH_GITHUB_CLIENT_ID || '',
    GITHUB_CLIENT_SECRET: unifiedEnv.getConfig().AUTH_GITHUB_CLIENT_SECRET || '',
  },

  // Banco de dados
  DATABASE_URL: unifiedEnv.getConfig().DB_DATABASE_URL,

  // IA (com lógica unificada)
  VERTEX_AI: {
    ENABLED: unifiedEnv.getAIConfig().enabled,
    PROJECT_ID: unifiedEnv.getConfig().AI_VERTEX_PROJECT_ID || '',
    LOCATION: unifiedEnv.getConfig().AI_VERTEX_LOCATION,
    MODEL_NAME: unifiedEnv.getConfig().AI_VERTEX_MODEL,
    CREDENTIALS_PATH:
      process.env.VERTEX_AI_CREDENTIALS_PATH || process.env.GOOGLE_APPLICATION_CREDENTIALS,
  },

  // Timeouts (configurações de timeout)
  TIMEOUTS: {
    API_CALL: 30000, // 30 segundos
    HEALTH_CHECK: {
      DATABASE: 5000, // 5 segundos
      AI_SERVICE: 10000, // 10 segundos
      EXTERNAL_DEPS: 15000, // 15 segundos
    },
  },

  // Cache (configurações de cache)
  CACHE: {
    DEFAULT_TTL: 300, // 5 minutos
    EXCEL_CACHE_SIZE: 50,
    EXCEL_CACHE_TTL: 1800, // 30 minutos
    AI_CACHE_SIZE: 200,
    AI_CACHE_TTL: 86400, // 24 horas
  },

  // Limits (configurações de limites)
  LIMITS: {
    API_RATE_LIMIT: unifiedEnv.getConfig().NODE_ENV === 'production' ? 60 : 120,
  },

  // Features (com lógica unificada)
  FEATURES: {
    USE_MOCK_AI: unifiedEnv.getAIConfig().status === 'mock',
    SKIP_AUTH_PROVIDERS: unifiedEnv.getConfig().AUTH_SKIP_PROVIDERS,
    TELEMETRY_SAMPLE_RATE: 0.1, // 10% por padrão
    ENABLE_REALTIME_COLLABORATION: true, // Habilitado por padrão
    ENABLE_DESKTOP_INTEGRATION: true, // Habilitado para produção
    ENABLE_STRIPE_INTEGRATION: unifiedEnv.getStripeConfig().enabled,
  },

  // MCPs - Propriedades de compatibilidade
  VERCEL_API_TOKEN: unifiedEnv.getConfig().MCP_VERCEL_TOKEN,
  VERCEL_PROJECT_ID: unifiedEnv.getConfig().MCP_VERCEL_PROJECT_ID,
  VERCEL_TEAM_ID: unifiedEnv.getConfig().MCP_VERCEL_TEAM_ID,
  LINEAR_API_KEY: unifiedEnv.getConfig().MCP_LINEAR_API_KEY,
  GITHUB_TOKEN: unifiedEnv.getConfig().MCP_GITHUB_TOKEN,
  GITHUB_OWNER: process.env.GITHUB_OWNER || process.env.MCP_GITHUB_OWNER,
  GITHUB_REPO: process.env.GITHUB_REPO || process.env.MCP_GITHUB_REPO || 'excel-copilot',
  SUPABASE_SERVICE_ROLE_KEY: unifiedEnv.getConfig().SUPABASE_SERVICE_ROLE_KEY,
  SUPABASE_URL: unifiedEnv.getConfig().SUPABASE_URL,

  // Validação
  validate: () => unifiedEnv.getValidationResult(),
};

/**
 * Função para validar ambiente (compatibilidade)
 */
export function validateEnvironment(): ValidationResult {
  return unifiedEnv.getValidationResult();
}

/**
 * Função para obter configuração específica
 */
export function getServiceConfig(service: string): ServiceConfig | undefined {
  switch (service) {
    case 'auth':
      return unifiedEnv.getAuthConfig();
    case 'ai':
      return unifiedEnv.getAIConfig();
    case 'database':
      return unifiedEnv.getDatabaseConfig();
    case 'stripe':
      return unifiedEnv.getStripeConfig();
    default:
      return undefined;
  }
}
