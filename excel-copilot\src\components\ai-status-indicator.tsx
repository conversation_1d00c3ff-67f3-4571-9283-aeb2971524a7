'use client';

import { <PERSON>rk<PERSON>, AlertTriangle } from 'lucide-react';
import { useState, useEffect } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { useToast } from '@/components/ui/use-toast';

interface AIStatusProps {
  className?: string;
}

export function AIStatusIndicator({ className }: AIStatusProps) {
  const [isMock, setIsMock] = useState<boolean | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [, setHasApiKey] = useState<boolean | null>(null);
  // Removendo a variável theme não utilizada
  const { toast } = useToast();

  useEffect(() => {
    const checkAIStatus = async () => {
      try {
        setIsLoading(true);
        const response = await fetch('/api/ai/status');

        if (response.ok) {
          const data = await response.json();
          setIsMock(data.useMockAI === true);
          setHasApiKey(data.hasApiKey === true);
        } else {
          // Se não conseguir verificar, assume que está em modo mock
          setIsMock(true);
          setHasApiKey(false);
        }
      } catch (error) {
        console.error('Erro ao verificar status da IA:', error);
        setIsMock(true);
        setHasApiKey(false);
      } finally {
        setIsLoading(false);
      }
    };

    checkAIStatus();
  }, []);

  const handleSetupAI = () => {
    toast({
      title: 'Configuração da IA',
      description:
        'Para usar a IA real, adicione uma chave de API do Google Gemini ao arquivo .env.local e reinicie a aplicação.',
    });
  };

  if (isLoading) {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button variant="ghost" size="icon" className={className} disabled>
              <Sparkles className="h-5 w-5 animate-pulse text-gray-400" />
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>Verificando status da IA...</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  if (isMock === true) {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button variant="ghost" size="sm" className={className} onClick={() => handleSetupAI()}>
              <AlertTriangle className="h-4 w-4 mr-2 text-amber-500" />
              <Badge
                variant="outline"
                className="text-amber-500 border-amber-200 bg-amber-50 dark:bg-amber-950/20"
              >
                IA Simulada
              </Badge>
            </Button>
          </TooltipTrigger>
          <TooltipContent side="bottom">
            <div className="max-w-xs">
              <p className="font-medium mb-1">Modo de simulação ativo</p>
              <p className="text-xs text-muted-foreground mb-2">
                O Excel Copilot está usando respostas pré-definidas para simular a IA. Para usar a
                IA real, configure uma chave de API do Google Gemini.
              </p>
              <Button
                variant="secondary"
                size="sm"
                onClick={() => handleSetupAI()}
                className="w-full"
              >
                Como configurar
              </Button>
            </div>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div className={className}>
            <Badge
              variant="outline"
              className="text-green-500 border-green-200 bg-green-50 dark:bg-green-950/20"
            >
              <Sparkles className="h-3.5 w-3.5 mr-1.5 text-green-500" />
              IA Ativa
            </Badge>
          </div>
        </TooltipTrigger>
        <TooltipContent side="bottom">
          <p className="text-green-600 dark:text-green-400">
            Excel Copilot está conectado à API do Google Gemini
          </p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
