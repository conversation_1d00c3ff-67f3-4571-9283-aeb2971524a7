-- AlterTable: Adicionar campos de segurança à tabela User
ALTER TABLE "User" ADD COLUMN "userAgent" TEXT;
ALTER TABLE "User" ADD COLUMN "isSuspicious" BOOLEAN NOT NULL DEFAULT false;
ALTER TABLE "User" ADD COLUMN "isBanned" BOOLEAN NOT NULL DEFAULT false;
ALTER TABLE "User" ADD COLUMN "banReason" TEXT;
ALTER TABLE "User" ADD COLUMN "banDate" TIMESTAMP(3);

-- CreateIndex: Adicionar índices para consultas de segurança
CREATE INDEX "User_isBanned_idx" ON "User"("isBanned");
CREATE INDEX "User_isSuspicious_idx" ON "User"("isSuspicious");

-- Atualizar usuários existentes: garantir valores padrão para todos
UPDATE "User" SET 
  "isSuspicious" = false,
  "isBanned" = false
WHERE "isSuspicious" IS NULL OR "isBanned" IS NULL; 