/**
 * Serviço para integração com API do Google Gemini/Vertex AI.
 * Este arquivo é carregado apenas no lado do servidor através de import dinâmico.
 */

// Import types only on build time
import { ENV } from '@/config/unified-environment';
import { ExcelOperationType } from '@/types';

import { logger } from '../logger';
import { logError } from '../utils/error-utils';

// Exportamos as interfaces de erros
export enum GeminiErrorType {
  TIMEOUT = 'TIMEOUT',
  API_UNAVAILABLE = 'API_UNAVAILABLE',
  INVALID_REQUEST = 'INVALID_REQUEST',
  CONTENT_FILTERED = 'CONTENT_FILTERED',
  RATE_LIMITED = 'RATE_LIMITED',
  INVALID_RESPONSE_FORMAT = 'INVALID_RESPONSE_FORMAT',
  CONTEXT_LIMIT_EXCEEDED = 'CONTEXT_LIMIT_EXCEEDED',
  TOKEN_LIMIT_EXCEEDED = 'TOKEN_LIMIT_EXCEEDED',
  UNKNOWN = 'UNKNOWN',
}

// Ampliar o enum ExcelOperationType com os tipos adicionais
// Isso será utilizado apenas neste arquivo
export type ExtendedExcelOperationType =
  | ExcelOperationType
  | 'FORMULA_GENERATION'
  | 'DATA_ANALYSIS'
  | 'TEXT_GENERATION';

// Configurações para o mecanismo de retry
interface RetryConfig {
  maxRetries: number;
  initialDelay: number;
  maxDelay: number;
  factor: number;
  retryableErrors: GeminiErrorType[];
}

// As opções são re-exportadas de gemini-api.ts
import type { SendMessageOptions, AnalysisResult, FormulaResult } from './gemini-api';

// Estender a interface SendMessageOptions para incluir as propriedades usadas
declare module './gemini-api' {
  interface SendMessageOptions {
    userId?: string;
    useMock?: boolean;
    timeout?: number;
    preserveContext?: boolean;
    skipCache?: boolean;
    context?: string;
    operationType?: ExtendedExcelOperationType;
    retryConfig?: Partial<RetryConfig>;
  }
}

// Interface para o cache de resposta
interface ResponseCacheItem {
  response: string;
  timestamp: number;
  contextHash: string | undefined;
}

/**
 * Serviço para integração com API do Google Vertex AI
 * Esta implementação será carregada dinamicamente apenas no servidor
 */
class GeminiService {
  private static instance: GeminiService;
  private vertexClient: any = null;
  private isInitialized = false;
  private modelName: string;
  private conversationHistory: Map<string, Array<{ role: string; content: string }>> = new Map();
  private mockMode: boolean;
  private requestsInLastMinute: number;
  private lastReset: number;
  private readonly requestLimit = 60; // Limite de 60 pedidos por minuto
  private readonly MAX_CACHE_SIZE = 100; // Máximo de itens no cache
  private readonly CACHE_TTL = 30 * 60 * 1000; // 30 minutos em ms
  private cacheCleanupInterval: NodeJS.Timeout | null = null;

  // Configuração padrão de retry
  private readonly defaultRetryConfig: RetryConfig = {
    maxRetries: 3,
    initialDelay: 1000, // 1 segundo
    maxDelay: 10000, // 10 segundos
    factor: 2, // Backoff exponencial
    retryableErrors: [
      GeminiErrorType.API_UNAVAILABLE,
      GeminiErrorType.RATE_LIMITED,
      GeminiErrorType.TIMEOUT,
    ],
  };

  // Cache para evitar chamadas repetidas
  private responseCache: Map<string, ResponseCacheItem>;
  // Cache para tokens comuns
  private tokenCache: Map<string, string> = new Map();

  private constructor() {
    this.modelName = ENV.VERTEX_AI.MODEL_NAME || 'gemini-1.5-pro';
    this.mockMode = !ENV.VERTEX_AI.ENABLED || !ENV.VERTEX_AI.PROJECT_ID || !ENV.VERTEX_AI.LOCATION;
    this.requestsInLastMinute = 0;
    this.lastReset = Date.now();
    this.responseCache = new Map();

    // Iniciar limpeza periódica do cache
    this.cacheCleanupInterval = setInterval(() => this.cleanExpiredCache(), 5 * 60 * 1000); // A cada 5 minutos

    // Pré-popular o cache de tokens com respostas comuns
    this.populateTokenCache();

    // Iniciar serviço apenas no servidor e se configurado
    if (typeof window === 'undefined' && ENV.VERTEX_AI.ENABLED) {
      this.initializeClients().catch(err => {
        logError('Erro ao inicializar cliente Vertex AI:', err);
      });
    } else {
      logger.info('GeminiService instanciado em ambiente não-servidor ou não habilitado');
    }
  }

  /**
   * Pré-popular o cache de tokens para respostas comuns
   */
  private populateTokenCache(): void {
    // Tokens comuns para operações do Excel
    this.tokenCache.set(
      'soma-columns',
      '{"operations":[{"type":"FORMULA","data":{"formula":"SUM","range":"$range","resultCell":"$target"}}]}'
    );
    this.tokenCache.set(
      'media-columns',
      '{"operations":[{"type":"FORMULA","data":{"formula":"AVERAGE","range":"$range","resultCell":"$target"}}]}'
    );
    this.tokenCache.set(
      'grafico-barras',
      '{"operations":[{"type":"CHART","data":{"type":"BAR","dataX":"$rangeX","dataY":"$rangeY","title":"$title"}}]}'
    );
    this.tokenCache.set(
      'encontrar-valor',
      '{"operations":[{"type":"FORMULA","data":{"formula":"VLOOKUP","lookup":"$lookup","range":"$range","column":"$column","exact":"$exact"}}]}'
    );
  }

  /**
   * Inicializar cliente Vertex AI
   */
  private async initializeClients(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    // Verificar se estamos no cliente
    if (typeof window !== 'undefined') {
      logger.warn('Tentativa de inicializar GeminiService no cliente. Operação cancelada.');
      return;
    }

    try {
      // Verificar se as configurações do Vertex AI estão disponíveis
      if (ENV.VERTEX_AI.PROJECT_ID && ENV.VERTEX_AI.LOCATION) {
        logger.info('Inicializando cliente Vertex AI');
        const vertexModule = await import('@google-cloud/vertexai');
        const { VertexAI } = vertexModule;

        this.vertexClient = new VertexAI({
          project: ENV.VERTEX_AI.PROJECT_ID,
          location: ENV.VERTEX_AI.LOCATION,
        });
        logger.info('Cliente Vertex AI inicializado com sucesso');
        this.isInitialized = true;
      } else {
        logger.warn('Vertex AI não configurado completamente. Usando modo de simulação.');
        this.mockMode = true;
      }
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : String(error);
      logError(`Erro ao inicializar cliente Vertex AI: ${errorMsg}`, error);
      throw new Error(`Falha na inicialização de GeminiService: ${errorMsg}`);
    }
  }

  /**
   * Obter instância singleton do serviço
   */
  public static getInstance(): GeminiService {
    if (!GeminiService.instance) {
      GeminiService.instance = new GeminiService();
    }
    return GeminiService.instance;
  }

  /**
   * Verifica se uma mensagem contém comandos reconhecíveis pelo token cache
   * @param message Mensagem a ser analisada
   * @returns Objeto com o token encontrado e o parse preliminar, ou null se não encontrado
   */
  private checkForRecognizedCommand(
    message: string
  ): { tokenKey: string; response: string } | null {
    message = message.toLowerCase().trim();

    // Verificar padrões comuns
    if (/som(a|e) (os valores|a coluna|as células)/.test(message)) {
      return { tokenKey: 'soma-columns', response: this.tokenCache.get('soma-columns')! };
    }

    if (/médi(a|e) (os valores|a coluna|as células)/.test(message)) {
      return { tokenKey: 'media-columns', response: this.tokenCache.get('media-columns')! };
    }

    if (/cri(e|ar) (um )?gráfico de (barras|colunas)/.test(message)) {
      return { tokenKey: 'grafico-barras', response: this.tokenCache.get('grafico-barras')! };
    }

    if (/busc(a|ar|e) (valor|dados|informações|registro)/.test(message)) {
      return { tokenKey: 'encontrar-valor', response: this.tokenCache.get('encontrar-valor')! };
    }

    return null;
  }

  /**
   * Enviar mensagem para o modelo de IA
   */
  async sendMessage(message: string, options: SendMessageOptions = {}): Promise<string> {
    const userId = options.userId || 'anonymous';
    const useMock = options.useMock || this.mockMode;
    const timeout = options.timeout || 30000; // 30 segundos padrão
    const preserveContext = options.preserveContext || false;

    // Configuração de retry
    const retryConfig: RetryConfig = {
      ...this.defaultRetryConfig,
      ...options.retryConfig,
    };

    // Usar identificador único para conversa deste usuário
    const conversationId = userId;

    try {
      // Verificar se é um comando reconhecível por tokens
      const recognizedCommand = this.checkForRecognizedCommand(message);
      if (recognizedCommand && !options.skipCache) {
        logger.info(`Usando token cache para comando reconhecido: ${recognizedCommand.tokenKey}`);
        return recognizedCommand.response;
      }

      // Verificar cache antes de processar (se não for uma conversa contextual)
      if (!preserveContext && !options.skipCache) {
        const contextStr = options.context || '';
        const cacheKey = this.generateCacheKey(message, contextStr);
        const contextHash = contextStr ? this.hashString(contextStr) : undefined;
        const cachedResponse = this.getCachedResponse(cacheKey, contextHash);

        if (cachedResponse) {
          logger.info(`Usando resposta em cache para: ${cacheKey.substring(0, 20)}...`);
          return cachedResponse;
        }
      }

      // Inicializar contexto de histórico para este usuário se não existir
      if (preserveContext && !this.conversationHistory.has(conversationId)) {
        this.conversationHistory.set(conversationId, []);
      }

      // Obter histórico se estiver usando contexto
      const history = preserveContext ? this.conversationHistory.get(conversationId) || [] : [];

      // Adicionar mensagem atual ao histórico
      if (preserveContext) {
        history.push({ role: 'user', content: message });
      }

      // Verificar se devemos usar mock
      if (useMock || !this.isInitialized) {
        const mockResponse = this.getMockResponse(message, options);

        // Adicionar resposta ao histórico se estiver preservando contexto
        if (preserveContext) {
          history.push({ role: 'assistant', content: mockResponse });
        }

        return mockResponse;
      }

      // Função para fazer requisição com retry
      const makeRequestWithRetry = async (attempt: number = 0): Promise<string> => {
        try {
          // Controle de taxa de requisições
          this.checkRateLimit();

          logger.info(
            `Enviando mensagem para Vertex AI. User: ${userId}, Tamanho: ${
              message.length
            } caracteres, Tentativa: ${attempt + 1}`
          );

          // Fazer requisição ao Vertex AI
          const response = await this.makeVertexRequest(message, history, options, timeout);
          return response;
        } catch (error) {
          // Se for um tipo de erro que podemos tentar novamente
          const geminiError = this.parseGeminiError(error);

          if (
            attempt < retryConfig.maxRetries &&
            retryConfig.retryableErrors.includes(geminiError.type)
          ) {
            // Calcular delay com exponential backoff
            const delay = Math.min(
              retryConfig.initialDelay * Math.pow(retryConfig.factor, attempt),
              retryConfig.maxDelay
            );

            logger.warn(
              `Erro ${geminiError.type} ao chamar API. Tentativa ${attempt + 1}/${
                retryConfig.maxRetries
              }. Tentando novamente em ${delay}ms`
            );

            // Esperar o delay antes de tentar novamente
            await new Promise(resolve => setTimeout(resolve, delay));

            // Tentar novamente
            return makeRequestWithRetry(attempt + 1);
          }

          // Se não podemos tentar novamente ou está além do limite, registrar e re-lançar
          logError('Erro ao usar Vertex AI:', error);
          throw geminiError;
        }
      };

      // Fazer a requisição com retry
      const response = await makeRequestWithRetry();

      // Adicionar resposta ao histórico se estiver preservando contexto
      if (preserveContext) {
        history.push({ role: 'assistant', content: response });
      }

      // Armazenar em cache se não for uma conversa contextual
      if (!preserveContext && !options.skipCache) {
        const contextStr = options.context || '';
        const cacheKey = this.generateCacheKey(message, contextStr);
        const contextHash = contextStr ? this.hashString(contextStr) : undefined;
        this.cacheResponse(cacheKey, response, contextHash);
      }

      return response;
    } catch (error) {
      logger.error(
        `Erro ao processar mensagem com IA: ${error instanceof Error ? error.message : String(error)}`,
        error
      );

      // Se estamos em desenvolvimento, fornecer resposta de mock mesmo no erro
      if (ENV.IS_DEVELOPMENT) {
        const mockResponse = this.getMockResponse(message, options);
        return mockResponse;
      }

      throw error;
    }
  }

  /**
   * Verificação de taxa de requisições
   */
  private checkRateLimit(): void {
    const now = Date.now();

    // Resetar contador se passou mais de um minuto desde o último reset
    if (now - this.lastReset > 60000) {
      this.requestsInLastMinute = 0;
      this.lastReset = now;
    }

    // Incrementar contador
    this.requestsInLastMinute++;

    // Verificar se excedeu o limite
    if (this.requestsInLastMinute > this.requestLimit) {
      throw new Error('Limite de requisições excedido. Tente novamente em alguns segundos.');
    }
  }

  /**
   * Fazer requisição usando Vertex AI
   */
  private async makeVertexRequest(
    message: string,
    history: Array<{ role: string; content: string }>,
    options: SendMessageOptions,
    timeout: number
  ): Promise<string> {
    // Verificar se estamos no cliente
    if (typeof window !== 'undefined') {
      logger.warn('Tentativa de fazer requisição ao Vertex AI no cliente. Usando mock.');
      return this.getMockResponse(message, options);
    }

    // Verificar se temos o cliente Vertex inicializado
    if (!this.vertexClient) {
      logger.warn('Cliente Vertex AI não inicializado. Usando mock.');
      return this.getMockResponse(message, options);
    }

    try {
      logger.info('Fazendo requisição para Vertex AI');

      // Preparar mensagens no formato esperado pelo Vertex AI
      const vertexMessages = [...history];

      // Usar o cliente Vertex AI
      const generativeModel = this.vertexClient.preview.getGenerativeModel({
        model: this.modelName,
      });

      // Configurar a geração
      const request = {
        contents: vertexMessages.length > 0 ? vertexMessages : [{ role: 'user', content: message }],
        safetySettings: [
          {
            category: 'HARM_CATEGORY_HATE_SPEECH',
            threshold: 'BLOCK_MEDIUM_AND_ABOVE',
          },
          {
            category: 'HARM_CATEGORY_DANGEROUS_CONTENT',
            threshold: 'BLOCK_MEDIUM_AND_ABOVE',
          },
          {
            category: 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
            threshold: 'BLOCK_MEDIUM_AND_ABOVE',
          },
          {
            category: 'HARM_CATEGORY_HARASSMENT',
            threshold: 'BLOCK_MEDIUM_AND_ABOVE',
          },
        ],
        generation_config: {
          temperature: 0.2,
          topP: 0.8,
          topK: 40,
          maxOutputTokens: 8192,
        },
      };

      // Executar a requisição com timeout
      const responsePromise = generativeModel.generateContentStream(request);

      // Adicionar timeout
      const timeoutPromise = new Promise<string>((_, reject) => {
        setTimeout(() => {
          reject(new Error(`Timeout após ${timeout}ms ao aguardar resposta do Vertex AI`));
        }, timeout);
      });

      // Aguardar a primeira resposta ou timeout
      const streamResponse = await Promise.race([responsePromise, timeoutPromise]);

      // Processar stream de resposta
      let fullResponse = '';
      for await (const item of streamResponse.stream) {
        const chunk = item.candidates?.[0]?.content?.parts?.[0]?.text || '';
        fullResponse += chunk;
      }

      // Retornar resposta completa
      return fullResponse.trim();
    } catch (error) {
      logger.error('Erro ao usar Vertex AI:', error);
      // Identificar o tipo de erro para tratamento específico
      const errorMsg = error instanceof Error ? error.message : String(error);

      if (errorMsg.includes('timeout') || errorMsg.includes('Timeout')) {
        throw new Error(`GeminiError/${GeminiErrorType.TIMEOUT}: ${errorMsg}`);
      } else if (errorMsg.includes('quota') || errorMsg.includes('rate limit')) {
        throw new Error(`GeminiError/${GeminiErrorType.RATE_LIMITED}: ${errorMsg}`);
      } else if (errorMsg.includes('content filtered') || errorMsg.includes('safety settings')) {
        throw new Error(`GeminiError/${GeminiErrorType.CONTENT_FILTERED}: ${errorMsg}`);
      } else if (errorMsg.includes('token limit')) {
        throw new Error(`GeminiError/${GeminiErrorType.TOKEN_LIMIT_EXCEEDED}: ${errorMsg}`);
      } else {
        throw new Error(`GeminiError/${GeminiErrorType.UNKNOWN}: ${errorMsg}`);
      }
    }
  }

  /**
   * Gerar resposta simulada para uso em desenvolvimento/testes
   */
  private getMockResponse(message: string, options: SendMessageOptions): string {
    // Simulação de delay para parecer mais realista
    const operationType = options.operationType || 'GENERIC';

    // Respostas simuladas baseadas no tipo de operação
    switch (operationType as ExtendedExcelOperationType) {
      case 'FORMULA_GENERATION':
        return '=SOMA(A1:A10)';

      case 'DATA_ANALYSIS':
        return JSON.stringify({
          insights: [
            'Os dados mostram uma tendência de crescimento nos últimos 3 meses',
            'Existe uma correlação de 0.87 entre as colunas A e B',
            'Há valores atípicos na coluna C que podem distorcer a análise',
          ],
          recommendations: [
            'Remover os valores atípicos para uma análise mais precisa',
            'Aplicar fórmulas de crescimento percentual para comparar períodos',
          ],
          summary: 'Dados mostram crescimento consistente com alguns valores atípicos',
        });

      case 'TEXT_GENERATION':
        return `Aqui está o texto gerado com base na sua solicitação: "${message}".\nEste é um texto de exemplo criado pelo modo de simulação do Excel Copilot.`;

      default:
        return `Resposta simulada para: "${message}"`;
    }
  }

  /**
   * Analisar dados de Excel
   */
  async analyzeExcelData(data: any, options: SendMessageOptions = {}): Promise<AnalysisResult> {
    try {
      // Converter dados para formato adequado para o modelo
      const dataAsString = JSON.stringify(data);

      // Criar prompt para análise dos dados
      const prompt = `
        Analise os seguintes dados de Excel:
        ${dataAsString}

        Forneça:
        1. 3-5 insights sobre os dados (padrões, anomalias, correlações)
        2. 2-3 operações sugeridas para melhorar ou extrair mais valor dos dados
        3. Um resumo conciso dos dados (máximo 2 linhas)

        Responda em formato JSON com as chaves: insights (array), suggestedOperations (array), e summary (string).
      `;

      // Fazer a requisição
      const response = await this.sendMessage(prompt, {
        ...options,
        operationType: 'DATA_ANALYSIS' as ExtendedExcelOperationType,
        timeout: 45000, // Tempo maior para análise de dados
      });

      // Tentar extrair JSON da resposta
      try {
        const jsonMatch = response.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          const result = JSON.parse(jsonMatch[0]);
          return {
            insights: result.insights || [],
            suggestedOperations: result.suggestedOperations || [],
            summary: result.summary || '',
          };
        }
      } catch (parseError) {
        logError('Falha ao extrair JSON da resposta de análise:', parseError);
      }

      // Fallback para resposta estruturada manual se JSON não for encontrado
      return {
        insights: [response.split('\n')[0] || 'Análise concluída'],
        suggestedOperations: ['Analisar tendências', 'Filtrar outliers'],
        summary: 'Análise de dados realizada com sucesso.',
      };
    } catch (error) {
      logError('Erro na análise de dados Excel:', error);

      // Retornar erro formatado para o cliente
      return {
        insights: [`Erro: ${error instanceof Error ? error.message : String(error)}`],
        suggestedOperations: [],
        summary: 'Ocorreu um erro durante a análise dos dados.',
      };
    }
  }

  /**
   * Gerar fórmula do Excel com base em descrição
   */
  async generateExcelFormula(
    description: string,
    options: SendMessageOptions = {}
  ): Promise<FormulaResult> {
    try {
      // Criar prompt para geração de fórmula
      const prompt = `
        Crie uma fórmula Excel baseada nesta descrição: "${description}"

        Responda apenas com a fórmula Excel completa e uma explicação de como ela funciona.
        Use o formato JSON com as propriedades: formula, explanation, example (opcional).
      `;

      // Fazer a requisição
      const response = await this.sendMessage(prompt, {
        ...options,
        operationType: 'FORMULA_GENERATION' as ExtendedExcelOperationType,
        timeout: 20000,
      });

      // Tentar extrair JSON da resposta
      try {
        const jsonMatch = response.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          const result = JSON.parse(jsonMatch[0]);
          return {
            formula: result.formula || '',
            explanation: result.explanation || '',
            example: result.example,
          };
        }
      } catch (parseError) {
        logError('Falha ao extrair JSON da resposta de fórmula:', parseError);
      }

      // Se não conseguiu extrair JSON, tentar extrair a fórmula do texto
      const formulaMatch = response.match(/=(.*?)(\n|$)/);
      if (formulaMatch && formulaMatch[1]) {
        return {
          formula: `=${formulaMatch[1].trim()}`,
          explanation: response.replace(formulaMatch[0], '').trim(),
        };
      }

      // Fallback se não conseguir extrair nada
      return {
        formula: '',
        explanation: response,
      };
    } catch (error) {
      logError('Erro na geração de fórmula Excel:', error);

      // Retornar erro formatado para o cliente
      return {
        formula: '',
        explanation: `Erro: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  }

  /**
   * Finalizar o serviço e liberar recursos
   */
  async shutdown(): Promise<void> {
    // Limpar histórico de conversas
    this.conversationHistory.clear();

    // Limpar o intervalo de limpeza do cache
    if (this.cacheCleanupInterval) {
      clearInterval(this.cacheCleanupInterval);
      this.cacheCleanupInterval = null;
    }

    // Limpar cache
    this.responseCache.clear();

    // Desalocar clientes
    this.vertexClient = null;
    this.isInitialized = false;

    logger.info('GeminiService finalizado com sucesso');
  }

  /**
   * Verificar se o serviço está saudável
   */
  async healthCheck(): Promise<boolean> {
    if (!this.isInitialized) {
      return false;
    }

    try {
      // Tentar fazer uma requisição simples para verificar saúde
      const response = await this.sendMessage('ping health check', {
        timeout: 5000,
        useMock: false,
      });

      return response.length > 0;
    } catch (error) {
      logError('Falha no health check do GeminiService:', error);
      return false;
    }
  }

  /**
   * Limpar entradas expiradas do cache
   */
  private cleanExpiredCache(): void {
    const now = Date.now();
    let expiredCount = 0;

    // Remover entradas expiradas
    for (const [key, item] of this.responseCache.entries()) {
      if (now - item.timestamp > this.CACHE_TTL) {
        this.responseCache.delete(key);
        expiredCount++;
      }
    }

    // Limitar tamanho do cache se exceder o máximo
    if (this.responseCache.size > this.MAX_CACHE_SIZE) {
      // Ordenar por timestamp (mais antigo primeiro)
      const entries = Array.from(this.responseCache.entries()).sort(
        (a, b) => a[1].timestamp - b[1].timestamp
      );

      // Remover entradas mais antigas até ficar dentro do limite
      const toRemove = this.responseCache.size - this.MAX_CACHE_SIZE;
      for (let i = 0; i < toRemove && i < entries.length; i++) {
        const entry = entries[i];
        if (entry && Array.isArray(entry) && entry.length > 0) {
          this.responseCache.delete(entry[0]);
        }
      }

      logger.info(`Cache limitado: ${toRemove} entradas antigas removidas`);
    }

    if (expiredCount > 0) {
      logger.info(`Cache limpo: ${expiredCount} entradas expiradas removidas`);
    }

    // Prevenir overflow do cache
    if (this.responseCache.size >= this.MAX_CACHE_SIZE) {
      // Remover o item mais antigo
      const entries = Array.from(this.responseCache.entries()).sort(
        (a, b) => a[1].timestamp - b[1].timestamp
      );
      if (entries.length > 0) {
        const entry = entries[0];
        if (entry && Array.isArray(entry) && entry.length > 0) {
          this.responseCache.delete(entry[0]);
        }
      }
    }
  }

  /**
   * Gera uma chave de cache baseada no prompt e contexto
   */
  private generateCacheKey(prompt: string, context: string): string {
    // Simplificar o prompt removendo espaços extras e tornando lowercase
    const normalizedPrompt = prompt.trim().toLowerCase();

    // Se não há contexto, usar apenas o prompt como chave
    if (!context) {
      return normalizedPrompt;
    }

    // Usar hash do contexto combinado com o prompt para a chave
    return `${normalizedPrompt}_${this.hashString(context)}`;
  }

  /**
   * Função simples de hash para strings
   */
  private hashString(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash; // Converter para 32bit integer
    }
    return hash.toString(16);
  }

  /**
   * Obtém uma resposta do cache se disponível e válida
   */
  private getCachedResponse(key: string, contextHash?: string): string | null {
    const cacheItem = this.responseCache.get(key);

    if (!cacheItem) {
      return null;
    }

    // Verificar se o item está expirado
    const now = Date.now();
    if (now - cacheItem.timestamp > this.CACHE_TTL) {
      this.responseCache.delete(key);
      return null;
    }

    // Se houver hash de contexto, verificar se corresponde
    if (contextHash && cacheItem.contextHash && contextHash !== cacheItem.contextHash) {
      return null;
    }

    return cacheItem.response;
  }

  /**
   * Armazena uma resposta no cache
   */
  private cacheResponse(key: string, response: string, contextHash?: string): void {
    // Prevenir overflow do cache
    if (this.responseCache.size >= this.MAX_CACHE_SIZE) {
      // Remover o item mais antigo
      const entries = Array.from(this.responseCache.entries()).sort(
        (a, b) => a[1].timestamp - b[1].timestamp
      );
      if (entries.length > 0) {
        const entry = entries[0];
        if (entry && Array.isArray(entry) && entry.length > 0) {
          this.responseCache.delete(entry[0]);
        }
      }
    }

    this.responseCache.set(key, {
      response,
      timestamp: Date.now(),
      contextHash,
    });
  }

  private parseGeminiError(error: unknown): { type: GeminiErrorType; message: string } {
    const errorMsg = error instanceof Error ? error.message : String(error);

    if (errorMsg.includes('timeout') || errorMsg.includes('Timeout')) {
      return { type: GeminiErrorType.TIMEOUT, message: errorMsg };
    } else if (errorMsg.includes('quota') || errorMsg.includes('rate limit')) {
      return { type: GeminiErrorType.RATE_LIMITED, message: errorMsg };
    } else if (errorMsg.includes('content filtered') || errorMsg.includes('safety settings')) {
      return { type: GeminiErrorType.CONTENT_FILTERED, message: errorMsg };
    } else if (errorMsg.includes('token limit')) {
      return { type: GeminiErrorType.TOKEN_LIMIT_EXCEEDED, message: errorMsg };
    } else {
      return { type: GeminiErrorType.UNKNOWN, message: errorMsg };
    }
  }
}

// Exportar instância singleton para uso no servidor
export const geminiService = GeminiService.getInstance();
