#!/usr/bin/env tsx

/**
 * Script para testar a integridade das assinaturas diretamente
 * Uso: npx tsx scripts/test-subscription-integrity.ts
 */

import { config } from 'dotenv';
import { resolve } from 'path';
import { PrismaClient } from '@prisma/client';

// Carregar variáveis de ambiente
config({ path: resolve(process.cwd(), '.env.local') });

// Constantes locais
const PLANS = {
  FREE: 'free',
  PRO_MONTHLY: 'pro_monthly',
  PRO_ANNUAL: 'pro_annual',
};

const API_CALL_LIMITS = {
  [PLANS.FREE]: 50,
  [PLANS.PRO_MONTHLY]: 500,
  [PLANS.PRO_ANNUAL]: 1000,
};

const prisma = new PrismaClient();

interface IntegrityReport {
  summary: {
    totalUsers: number;
    usersWithSubscription: number;
    usersWithoutSubscription: number;
    inconsistencies: number;
  };
  issues: Array<{
    type:
      | 'missing_subscription'
      | 'invalid_plan'
      | 'expired_subscription'
      | 'duplicate_subscription';
    userId: string;
    email: string;
    details: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
  }>;
  recommendations: string[];
}

async function checkSubscriptionIntegrity(): Promise<IntegrityReport> {
  console.log('🔍 Iniciando verificação de integridade das assinaturas...\n');

  const report: IntegrityReport = {
    summary: {
      totalUsers: 0,
      usersWithSubscription: 0,
      usersWithoutSubscription: 0,
      inconsistencies: 0,
    },
    issues: [],
    recommendations: [],
  };

  try {
    // 1. Buscar todos os usuários com suas assinaturas
    const users = await prisma.user.findMany({
      select: {
        id: true,
        email: true,
        name: true,
        createdAt: true,
        subscriptions: {
          select: {
            id: true,
            plan: true,
            status: true,
            apiCallsLimit: true,
            apiCallsUsed: true,
            currentPeriodEnd: true,
            createdAt: true,
          },
          orderBy: { createdAt: 'desc' },
        },
      },
    });

    report.summary.totalUsers = users.length;
    console.log(`📊 Total de usuários encontrados: ${users.length}`);

    // 2. Analisar cada usuário
    for (const user of users) {
      const activeSubscriptions = user.subscriptions.filter(
        sub => sub.status === 'active' || sub.status === 'trialing'
      );

      // Verificar usuários sem assinatura
      if (user.subscriptions.length === 0) {
        report.summary.usersWithoutSubscription++;
        report.issues.push({
          type: 'missing_subscription',
          userId: user.id,
          email: user.email || 'Email não definido',
          details: 'Usuário não possui nenhuma assinatura registrada',
          severity: 'high',
        });
      } else {
        report.summary.usersWithSubscription++;

        // Verificar múltiplas assinaturas ativas
        if (activeSubscriptions.length > 1) {
          report.issues.push({
            type: 'duplicate_subscription',
            userId: user.id,
            email: user.email || 'Email não definido',
            details: `Usuário possui ${activeSubscriptions.length} assinaturas ativas`,
            severity: 'medium',
          });
        }

        // Verificar planos inválidos
        for (const subscription of user.subscriptions) {
          if (!Object.values(PLANS).includes(subscription.plan)) {
            report.issues.push({
              type: 'invalid_plan',
              userId: user.id,
              email: user.email || 'Email não definido',
              details: `Plano inválido: ${subscription.plan}`,
              severity: 'critical',
            });
          }

          // Verificar limites de API inconsistentes
          const expectedLimit = API_CALL_LIMITS[subscription.plan as keyof typeof API_CALL_LIMITS];
          if (expectedLimit && subscription.apiCallsLimit !== expectedLimit) {
            report.issues.push({
              type: 'invalid_plan',
              userId: user.id,
              email: user.email || 'Email não definido',
              details: `Limite de API inconsistente: esperado ${expectedLimit}, atual ${subscription.apiCallsLimit}`,
              severity: 'medium',
            });
          }

          // Verificar assinaturas expiradas mas ainda ativas
          if (
            subscription.currentPeriodEnd &&
            subscription.currentPeriodEnd < new Date() &&
            subscription.status === 'active'
          ) {
            report.issues.push({
              type: 'expired_subscription',
              userId: user.id,
              email: user.email || 'Email não definido',
              details: `Assinatura expirada em ${subscription.currentPeriodEnd.toISOString()} mas ainda ativa`,
              severity: 'high',
            });
          }
        }
      }
    }

    report.summary.inconsistencies = report.issues.length;

    // 3. Gerar recomendações
    if (report.summary.usersWithoutSubscription > 0) {
      report.recommendations.push(
        `Execute o script de migração para criar assinaturas Free para ${report.summary.usersWithoutSubscription} usuários sem assinatura.`
      );
    }

    const criticalIssues = report.issues.filter(issue => issue.severity === 'critical').length;
    if (criticalIssues > 0) {
      report.recommendations.push(
        `Corrija imediatamente ${criticalIssues} problemas críticos identificados.`
      );
    }

    const duplicateSubscriptions = report.issues.filter(
      issue => issue.type === 'duplicate_subscription'
    ).length;
    if (duplicateSubscriptions > 0) {
      report.recommendations.push(
        `Revise e consolide ${duplicateSubscriptions} usuários com múltiplas assinaturas ativas.`
      );
    }

    // 4. Estatísticas adicionais
    const planStats = await prisma.subscription.groupBy({
      by: ['plan', 'status'],
      _count: {
        plan: true,
      },
    });

    console.log(`✅ Usuários com assinatura: ${report.summary.usersWithSubscription}`);
    console.log(`⚠️  Usuários sem assinatura: ${report.summary.usersWithoutSubscription}`);
    console.log(`🚨 Inconsistências encontradas: ${report.summary.inconsistencies}`);

    console.log('\n📊 Estatísticas de planos:');
    planStats.forEach(stat => {
      console.log(`  - ${stat.plan} (${stat.status}): ${stat._count.plan} usuários`);
    });

    if (report.issues.length > 0) {
      console.log('\n🚨 PROBLEMAS ENCONTRADOS:');
      report.issues.forEach((issue, index) => {
        console.log(
          `  ${index + 1}. [${issue.severity.toUpperCase()}] ${issue.email}: ${issue.details}`
        );
      });
    }

    if (report.recommendations.length > 0) {
      console.log('\n💡 RECOMENDAÇÕES:');
      report.recommendations.forEach((rec, index) => {
        console.log(`  ${index + 1}. ${rec}`);
      });
    }

    return report;
  } catch (error) {
    console.error('💥 Erro ao verificar integridade:', error);
    throw error;
  }
}

async function main(): Promise<void> {
  try {
    const startTime = Date.now();

    const report = await checkSubscriptionIntegrity();

    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;

    console.log('\n' + '='.repeat(60));
    console.log('📋 RELATÓRIO FINAL DE INTEGRIDADE');
    console.log('='.repeat(60));
    console.log(`⏱️  Tempo de execução: ${duration.toFixed(2)}s`);
    console.log(`👥 Total de usuários: ${report.summary.totalUsers}`);
    console.log(`✅ Usuários com assinatura: ${report.summary.usersWithSubscription}`);
    console.log(`⚠️  Usuários sem assinatura: ${report.summary.usersWithoutSubscription}`);
    console.log(`🚨 Inconsistências: ${report.summary.inconsistencies}`);

    if (report.summary.inconsistencies === 0) {
      console.log('\n🎉 Sistema de assinaturas está íntegro!');
    } else {
      console.log('\n⚠️  Foram encontrados problemas que precisam de atenção.');
    }
  } catch (error) {
    console.error('\n💥 Falha na verificação:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Executar script se chamado diretamente
if (require.main === module) {
  main().catch(console.error);
}

export { checkSubscriptionIntegrity };
