# 💳 Stripe MCP Integration - Excel Copilot

Integração completa com Stripe via Model Context Protocol (MCP) para monitoramento de pagamentos, assinaturas, clientes e métricas de receita.

## 📋 Índice

- [Visão Geral](#-visão-geral)
- [Configuração](#-configuração)
- [Funcionalidades](#-funcionalidades)
- [Endpoints da API](#-endpoints-da-api)
- [Exemplos de Uso](#-exemplos-de-uso)
- [Monitoramento](#-monitoramento)
- [Desenvolvimento](#-desenvolvimento)
- [Troubleshooting](#-troubleshooting)

## 🎯 Visão Geral

A integração Stripe MCP permite:

- **💰 Monitoramento de Receita**: Acompanhar MRR, ARPU, crescimento e tendências
- **👥 Gestão de Clientes**: Visualizar clientes, gastos totais e métodos de pagamento
- **📋 Análise de Assinaturas**: Monitorar status, churn rate e performance por plano
- **💳 Rastreamento de Pagamentos**: Analisar transações, taxas de sucesso e falhas
- **📊 Métricas de Negócio**: Dashboard completo com KPIs e alertas automáticos
- **🔍 Analytics Avançados**: Análise de coortes, geografia e comportamento

## ✅ Status da Implementação

- ✅ **Cliente Stripe API** - Implementado em `src/lib/stripe-integration.ts`
- ✅ **Endpoints de API** - Criados em `src/app/api/stripe/`
- ✅ **Health Check Integration** - Integrado ao sistema de health checks
- ✅ **Documentação** - Completa com exemplos de uso

## 🔧 Configuração

### 1. Variáveis de Ambiente

Adicione as seguintes variáveis ao seu arquivo `.env.local`:

```bash
# Stripe Configuration
STRIPE_SECRET_KEY="sk_test_seu-token-stripe-aqui"
STRIPE_WEBHOOK_SECRET="whsec_seu-webhook-secret-aqui"

# Opcional: Para produção
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY="pk_live_seu-token-publico"
```

### 2. Estrutura de Arquivos

```
src/
├── lib/
│   └── stripe-integration.ts         # Cliente e serviços Stripe
├── app/api/stripe/
│   ├── status/route.ts              # Status e métricas gerais
│   ├── customers/route.ts           # Gestão de clientes
│   ├── subscriptions/route.ts       # Análise de assinaturas
│   └── payments/route.ts            # Monitoramento de pagamentos
└── lib/health-checker.ts            # Health check integrado
```

## 📡 Endpoints Disponíveis

### 🏥 Status Geral

```http
GET /api/stripe/status
POST /api/stripe/status  # Análise detalhada
```

**Resposta:**

```json
{
  "service": {
    "name": "stripe",
    "status": "healthy",
    "message": "Negócio funcionando normalmente"
  },
  "business": {
    "revenue": {
      "total": 125000,
      "currency": "brl",
      "growth": 15.5,
      "mrr": 8500,
      "arpu": 2900
    },
    "customers": {
      "total": 150,
      "new": 12,
      "churn": 2
    },
    "subscriptions": {
      "total": 145,
      "active": 132,
      "trialing": 8,
      "pastDue": 3,
      "canceled": 2
    }
  }
}
```

### 👥 Clientes

```http
GET /api/stripe/customers?limit=50&email=<EMAIL>
POST /api/stripe/customers  # Análise avançada
```

**Parâmetros GET:**

- `limit`: Número máximo de clientes (máx: 100)
- `email`: Filtrar por email específico
- `created_after`: Timestamp Unix para filtrar por data de criação
- `created_before`: Timestamp Unix para filtrar por data de criação

**Exemplo POST:**

```json
{
  "filters": {
    "limit": 50,
    "created": {
      "gte": **********
    }
  },
  "analytics": true,
  "includeSubscriptions": true,
  "includePayments": false
}
```

### 📋 Assinaturas

```http
GET /api/stripe/subscriptions?status=active&limit=50
POST /api/stripe/subscriptions  # Análise de churn e coortes
```

**Parâmetros GET:**

- `status`: active, canceled, trialing, past_due, etc.
- `customer`: ID do cliente
- `price`: ID do preço/plano
- `limit`: Número máximo de assinaturas

**Exemplo POST:**

```json
{
  "analysis": "advanced",
  "period": "30d",
  "includeChurn": true,
  "includeCohorts": true
}
```

### 💳 Pagamentos

```http
GET /api/stripe/payments?customer=cus_123&limit=50
POST /api/stripe/payments  # Análise de falhas e tendências
```

**Parâmetros GET:**

- `customer`: ID do cliente
- `created_after`: Timestamp Unix
- `created_before`: Timestamp Unix
- `limit`: Número máximo de pagamentos

**Exemplo POST:**

```json
{
  "period": "30d",
  "includeFailureAnalysis": true,
  "includeGeography": true,
  "includeTrends": true
}
```

## 🔍 Exemplos de Uso

### Monitorar Status do Negócio

```javascript
// Verificar saúde geral do Stripe
const response = await fetch('/api/stripe/status');
const data = await response.json();

console.log(`MRR: R$ ${data.business.revenue.mrr}`);
console.log(`Clientes ativos: ${data.business.customers.total}`);
console.log(`Taxa de crescimento: ${data.business.revenue.growth}%`);
```

### Análise de Churn

```javascript
// Analisar churn de assinaturas
const response = await fetch('/api/stripe/subscriptions', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    includeChurn: true,
    includeCohorts: true,
  }),
});

const data = await response.json();
console.log(`Taxa de churn: ${data.churn.rate}%`);
```

### Análise de Falhas de Pagamento

```javascript
// Analisar falhas de pagamento
const response = await fetch('/api/stripe/payments', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    includeFailureAnalysis: true,
    period: '7d',
  }),
});

const data = await response.json();
console.log(`Taxa de falha: ${data.failureAnalysis.failureRate}%`);
```

## 📊 Métricas Disponíveis

### Receita

- **MRR** (Monthly Recurring Revenue)
- **ARPU** (Average Revenue Per User)
- **Crescimento de receita**
- **Receita por plano/país/método de pagamento**

### Clientes

- **Total de clientes**
- **Novos clientes por período**
- **Valor total gasto por cliente**
- **Distribuição por método de pagamento**

### Assinaturas

- **Status breakdown** (ativo, trial, cancelado, etc.)
- **Taxa de churn**
- **Análise de coortes**
- **Performance por plano**

### Pagamentos

- **Taxa de sucesso**
- **Análise de falhas**
- **Distribuição geográfica**
- **Tendências temporais**

## 🚨 Alertas Automáticos

O sistema monitora automaticamente:

- **Receita em declínio** (> 10% = crítico, > 0% = aviso)
- **Taxa de sucesso baixa** (< 80% = crítico, < 90% = aviso)
- **Taxa de erro elevada** (> 5% = crítico)
- **Webhook não configurado** (aviso)

## 🔧 Classes e Métodos

### StripeClient

Cliente base para interação com APIs do Stripe:

```typescript
const client = new StripeClient({
  apiKey: 'sk_test_...',
  webhookSecret: 'whsec_...',
});

// Obter clientes
const customers = await client.getCustomers({ limit: 50 });

// Obter assinaturas
const subscriptions = await client.getSubscriptions({ status: 'active' });

// Obter pagamentos
const payments = await client.getPayments({ customer: 'cus_123' });

// Métricas de negócio
const metrics = await client.getBusinessMetrics('30d');

// Health check
const health = await client.checkHealth();
```

### StripeMonitoringService

Serviço de alto nível para monitoramento:

```typescript
const service = new StripeMonitoringService({
  apiKey: 'sk_test_...',
});

// Status do negócio
const status = await service.getBusinessStatus();

// Análise de receita
const revenue = await service.getRevenueAnalysis('30d');

// Métricas do Excel Copilot
const metrics = await service.getExcelCopilotMetrics();
```

## 🚨 Troubleshooting

### Erro: "STRIPE_SECRET_KEY não configurado"

- Verifique se a variável está definida no `.env.local`
- Confirme que a chave é válida no dashboard do Stripe

### Erro: "Webhook signature verification failed"

- Verifique se `STRIPE_WEBHOOK_SECRET` está correto
- Confirme que o endpoint está configurado no Stripe

### Taxa de erro alta

- Verifique logs de pagamentos falhados
- Analise razões de falha mais comuns
- Considere implementar retry automático

### Performance lenta

- Use filtros de data para limitar resultados
- Implemente cache para métricas frequentes
- Considere paginação para grandes volumes

## 📈 Roadmap

- [ ] **Dashboard Visual**: Interface gráfica para métricas
- [ ] **Alertas Personalizados**: Configuração de thresholds
- [ ] **Relatórios Automáticos**: Envio periódico por email
- [ ] **Integração com BI**: Export para ferramentas de análise
- [ ] **Previsões**: ML para prever churn e receita

---

**Desenvolvido para Excel Copilot** | Documentação atualizada em Janeiro 2025
