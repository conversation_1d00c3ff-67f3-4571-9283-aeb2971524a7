import { test, expect } from '@playwright/test';

test('página inicial carrega corretamente', async ({ page }) => {
  // Navegar para a aplicação
  await page.goto('/');

  // Verificar se o título é carregado
  await expect(page.locator('h1')).toContainText('Excel Copilot');

  // Verificar se os elementos básicos da UI estão presentes
  await expect(page.getByRole('tab', { name: 'Chat' })).toBeVisible();
  await expect(page.getByRole('tab', { name: 'Upload de Planilha' })).toBeVisible();
});
