const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

async function checkSupabaseConnection() {
  console.log('🔍 Verificando conexão com Supabase...\n');

  // Verificar variáveis de ambiente
  const requiredVars = [
    'SUPABASE_URL',
    'SUPABASE_ANON_KEY',
    'SUPABASE_SERVICE_ROLE_KEY',
    'DATABASE_URL',
    'DIRECT_URL',
  ];

  console.log('📋 Verificando variáveis de ambiente...');
  const missingVars = requiredVars.filter(varName => !process.env[varName]);

  if (missingVars.length > 0) {
    console.error('❌ Variáveis de ambiente faltando:', missingVars);
    process.exit(1);
  }
  console.log('✅ Todas as variáveis de ambiente estão configuradas\n');

  // Mostrar configurações atuais (sem expor secrets)
  console.log('🔧 Configurações atuais:');
  console.log(`   URL: ${process.env.SUPABASE_URL}`);
  console.log(`   Anon Key: ${process.env.SUPABASE_ANON_KEY.substring(0, 20)}...`);
  console.log(`   Service Key: ${process.env.SUPABASE_SERVICE_ROLE_KEY.substring(0, 20)}...`);
  console.log(
    `   Database URL: ${process.env.DB_DATABASE_URL.split('@')[1]?.split('?')[0] || 'N/A'}`
  );
  console.log(
    `   Direct URL: ${process.env.DB_DIRECT_URL.split('@')[1]?.split('?')[0] || 'N/A'}\n`
  );

  // Testar cliente Supabase com anon key
  console.log('🔐 Testando cliente Supabase (anon)...');
  const supabaseAnon = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_ANON_KEY);

  try {
    // Testar autenticação
    const { data: authData, error: authError } = await supabaseAnon.auth.getSession();
    if (authError && authError.message !== 'Auth session missing!') {
      throw authError;
    }
    console.log('✅ Supabase Auth (anon): Conectado');

    // Testar storage
    const { data: buckets, error: storageError } = await supabaseAnon.storage.listBuckets();
    if (storageError) {
      console.log('⚠️  Supabase Storage (anon): Acesso limitado (esperado)');
    } else {
      console.log('✅ Supabase Storage (anon): Conectado');
      console.log(`📦 Buckets encontrados: ${buckets.length}`);
    }
  } catch (error) {
    console.error('❌ Erro no cliente anon:', error.message);
  }

  // Testar cliente Supabase com service role
  console.log('\n🔑 Testando cliente Supabase (service role)...');
  const supabaseAdmin = createClient(
    process.env.SUPABASE_URL,
    process.env.SUPABASE_SERVICE_ROLE_KEY,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false,
      },
    }
  );

  try {
    // Testar storage com service role
    const { data: buckets, error: storageError } = await supabaseAdmin.storage.listBuckets();
    if (storageError) throw storageError;
    console.log('✅ Supabase Storage (admin): Conectado');
    console.log(`📦 Buckets encontrados: ${buckets.length}`);

    if (buckets.length > 0) {
      buckets.forEach(bucket => {
        console.log(`   - ${bucket.name} (${bucket.public ? 'público' : 'privado'})`);
      });
    }

    // Testar query simples no banco
    const { data: tables, error: queryError } = await supabaseAdmin
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .limit(5);

    if (queryError) {
      console.log('⚠️  Query de teste: Acesso limitado (pode ser esperado)');
    } else {
      console.log('✅ Query de teste: Sucesso');
      console.log(`📊 Tabelas encontradas: ${tables.length}`);
    }
  } catch (error) {
    console.error('❌ Erro no cliente admin:', error.message);
  }

  // Testar conectividade básica
  console.log('\n🌐 Testando conectividade básica...');
  try {
    const response = await fetch(`${process.env.SUPABASE_URL}/rest/v1/`, {
      headers: {
        apikey: process.env.SUPABASE_ANON_KEY,
        Authorization: `Bearer ${process.env.SUPABASE_ANON_KEY}`,
      },
    });

    if (response.ok) {
      console.log('✅ API REST: Conectado');
      console.log(`   Status: ${response.status}`);
    } else {
      console.log(`⚠️  API REST: Status ${response.status}`);
    }
  } catch (error) {
    console.error('❌ Erro na conectividade:', error.message);
  }

  console.log('\n🎉 Verificação de conectividade concluída!');
  console.log('\n📝 Próximos passos:');
  console.log('   1. Se houver erros, verifique as credenciais no painel Supabase');
  console.log('   2. Certifique-se de que o projeto está ativo');
  console.log('   3. Configure buckets de storage se necessário');
  console.log('   4. Implemente RLS policies para segurança');
}

// Executar verificação
checkSupabaseConnection().catch(error => {
  console.error('\n💥 Erro fatal na verificação:', error);
  process.exit(1);
});
