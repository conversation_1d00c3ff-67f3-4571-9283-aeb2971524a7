#!/usr/bin/env node

/**
 * Script para verificar e validar a configuração do Vertex AI
 * Garante que não há implementações do Google AI Studio
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Cores para output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bright: '\x1b[1m',
};

console.log(`${colors.blue}${colors.bright}
🔍 VERIFICAÇÃO DE CONFIGURAÇÃO VERTEX AI
========================================${colors.reset}
`);

let hasErrors = false;
let hasWarnings = false;

/**
 * Verificar se há referências ao Google AI Studio no código
 */
function checkForGoogleAIStudio() {
  console.log(`${colors.blue}📋 Verificando referências ao Google AI Studio...${colors.reset}`);

  const patterns = [
    'GoogleGenerativeAI',
    '@google/generative-ai',
    'GOOGLE_AI_API_KEY',
    'genAI.getGenerativeModel',
    'new GoogleGenerativeAI',
  ];

  const srcDir = path.join(__dirname, '../src');
  let foundIssues = [];

  patterns.forEach(pattern => {
    try {
      const result = execSync(
        `grep -r "${pattern}" "${srcDir}" --include="*.ts" --include="*.js" --include="*.tsx" --include="*.jsx" || true`,
        { encoding: 'utf8' }
      );

      if (result.trim()) {
        foundIssues.push({
          pattern,
          matches: result.trim().split('\n'),
        });
      }
    } catch (error) {
      // Ignorar erros de grep (quando não encontra nada)
    }
  });

  if (foundIssues.length > 0) {
    console.log(`${colors.red}❌ Encontradas referências ao Google AI Studio:${colors.reset}`);
    foundIssues.forEach(issue => {
      console.log(`${colors.red}   Padrão: ${issue.pattern}${colors.reset}`);
      issue.matches.forEach(match => {
        console.log(`${colors.red}   - ${match}${colors.reset}`);
      });
    });
    hasErrors = true;
  } else {
    console.log(
      `${colors.green}✅ Nenhuma referência ao Google AI Studio encontrada${colors.reset}`
    );
  }
}

/**
 * Verificar dependências no package.json
 */
function checkPackageJson() {
  console.log(`${colors.blue}📦 Verificando dependências...${colors.reset}`);

  const packageJsonPath = path.join(__dirname, '../package.json');
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));

  const problematicDeps = ['@google/generative-ai'];

  const foundDeps = [];

  // Verificar dependencies
  if (packageJson.dependencies) {
    problematicDeps.forEach(dep => {
      if (packageJson.dependencies[dep]) {
        foundDeps.push({ dep, type: 'dependencies' });
      }
    });
  }

  // Verificar devDependencies
  if (packageJson.devDependencies) {
    problematicDeps.forEach(dep => {
      if (packageJson.devDependencies[dep]) {
        foundDeps.push({ dep, type: 'devDependencies' });
      }
    });
  }

  if (foundDeps.length > 0) {
    console.log(`${colors.red}❌ Dependências problemáticas encontradas:${colors.reset}`);
    foundDeps.forEach(({ dep, type }) => {
      console.log(`${colors.red}   - ${dep} (${type})${colors.reset}`);
    });
    hasErrors = true;
  } else {
    console.log(`${colors.green}✅ Nenhuma dependência problemática encontrada${colors.reset}`);
  }

  // Verificar se tem as dependências corretas do Vertex AI
  const requiredDeps = ['@google-cloud/vertexai'];

  const missingDeps = [];
  requiredDeps.forEach(dep => {
    if (!packageJson.dependencies || !packageJson.dependencies[dep]) {
      missingDeps.push(dep);
    }
  });

  if (missingDeps.length > 0) {
    console.log(`${colors.yellow}⚠️ Dependências do Vertex AI ausentes:${colors.reset}`);
    missingDeps.forEach(dep => {
      console.log(`${colors.yellow}   - ${dep}${colors.reset}`);
    });
    hasWarnings = true;
  } else {
    console.log(`${colors.green}✅ Dependências do Vertex AI presentes${colors.reset}`);
  }
}

/**
 * Verificar configuração de ambiente
 */
function checkEnvironmentConfig() {
  console.log(`${colors.blue}⚙️ Verificando configuração de ambiente...${colors.reset}`);

  const envExamplePath = path.join(__dirname, '../env.example');

  if (fs.existsSync(envExamplePath)) {
    const envExample = fs.readFileSync(envExamplePath, 'utf8');

    // Verificar se tem GOOGLE_AI_API_KEY
    if (envExample.includes('GOOGLE_AI_API_KEY')) {
      console.log(`${colors.red}❌ env.example contém GOOGLE_AI_API_KEY${colors.reset}`);
      hasErrors = true;
    } else {
      console.log(`${colors.green}✅ env.example não contém GOOGLE_AI_API_KEY${colors.reset}`);
    }

    // Verificar se tem configurações do Vertex AI
    const vertexAIVars = ['VERTEX_AI_ENABLED', 'VERTEX_AI_PROJECT_ID', 'VERTEX_AI_LOCATION'];

    const missingVars = vertexAIVars.filter(varName => !envExample.includes(varName));

    if (missingVars.length > 0) {
      console.log(
        `${colors.yellow}⚠️ Variáveis do Vertex AI ausentes no env.example:${colors.reset}`
      );
      missingVars.forEach(varName => {
        console.log(`${colors.yellow}   - ${varName}${colors.reset}`);
      });
      hasWarnings = true;
    } else {
      console.log(
        `${colors.green}✅ Configurações do Vertex AI presentes no env.example${colors.reset}`
      );
    }
  } else {
    console.log(`${colors.yellow}⚠️ Arquivo env.example não encontrado${colors.reset}`);
    hasWarnings = true;
  }
}

/**
 * Verificar arquivos de configuração do Vertex AI
 */
function checkVertexAIFiles() {
  console.log(`${colors.blue}📁 Verificando arquivos do Vertex AI...${colors.reset}`);

  const vertexCredentialsPath = path.join(__dirname, '../vertex-credentials.json');

  if (fs.existsSync(vertexCredentialsPath)) {
    try {
      const credentials = JSON.parse(fs.readFileSync(vertexCredentialsPath, 'utf8'));

      if (credentials.type === 'service_account' && credentials.project_id) {
        console.log(
          `${colors.green}✅ Arquivo vertex-credentials.json válido encontrado${colors.reset}`
        );
        console.log(`${colors.blue}   Projeto: ${credentials.project_id}${colors.reset}`);
      } else {
        console.log(`${colors.red}❌ Arquivo vertex-credentials.json inválido${colors.reset}`);
        hasErrors = true;
      }
    } catch (error) {
      console.log(
        `${colors.red}❌ Erro ao ler vertex-credentials.json: ${error.message}${colors.reset}`
      );
      hasErrors = true;
    }
  } else {
    console.log(`${colors.yellow}⚠️ Arquivo vertex-credentials.json não encontrado${colors.reset}`);
    console.log(
      `${colors.yellow}   Configure as credenciais via variáveis de ambiente ou arquivo JSON${colors.reset}`
    );
    hasWarnings = true;
  }
}

/**
 * Verificar implementação dos serviços de IA
 */
function checkAIServices() {
  console.log(`${colors.blue}🤖 Verificando implementação dos serviços de IA...${colors.reset}`);

  const geminiServicePath = path.join(__dirname, '../src/lib/ai/gemini-service.ts');

  if (fs.existsSync(geminiServicePath)) {
    const content = fs.readFileSync(geminiServicePath, 'utf8');

    // Verificar se usa Vertex AI
    if (content.includes('@google-cloud/vertexai') && content.includes('VertexAI')) {
      console.log(`${colors.green}✅ GeminiService usa Vertex AI corretamente${colors.reset}`);
    } else {
      console.log(`${colors.red}❌ GeminiService não usa Vertex AI${colors.reset}`);
      hasErrors = true;
    }

    // Verificar se não usa Google AI Studio
    if (content.includes('GoogleGenerativeAI') || content.includes('@google/generative-ai')) {
      console.log(`${colors.red}❌ GeminiService ainda usa Google AI Studio${colors.reset}`);
      hasErrors = true;
    } else {
      console.log(`${colors.green}✅ GeminiService não usa Google AI Studio${colors.reset}`);
    }
  } else {
    console.log(`${colors.red}❌ Arquivo gemini-service.ts não encontrado${colors.reset}`);
    hasErrors = true;
  }
}

/**
 * Função principal
 */
function main() {
  checkForGoogleAIStudio();
  console.log('');

  checkPackageJson();
  console.log('');

  checkEnvironmentConfig();
  console.log('');

  checkVertexAIFiles();
  console.log('');

  checkAIServices();
  console.log('');

  // Resumo final
  console.log(`${colors.blue}${colors.bright}📊 RESUMO DA VERIFICAÇÃO${colors.reset}`);
  console.log('=========================');

  if (hasErrors) {
    console.log(`${colors.red}❌ Encontrados problemas que precisam ser corrigidos${colors.reset}`);
    process.exit(1);
  } else if (hasWarnings) {
    console.log(`${colors.yellow}⚠️ Verificação concluída com avisos${colors.reset}`);
    console.log(`${colors.yellow}   Revise as configurações para otimizar o setup${colors.reset}`);
  } else {
    console.log(`${colors.green}✅ Configuração do Vertex AI está correta!${colors.reset}`);
    console.log(`${colors.green}   Todas as verificações passaram${colors.reset}`);
  }
}

// Executar verificação
main();
