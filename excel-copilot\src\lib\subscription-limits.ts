import { logger } from '@/lib/logger';
import { PLANS } from '@/lib/stripe';
import { rateLimit } from '@/lib/utils/rate-limit';
import { prisma } from '@/server/db/client';

// Definição de limites por plano
export const PLAN_LIMITS = {
  // Workbooks por usuário
  MAX_WORKBOOKS: {
    [PLANS.FREE]: 5,
    [PLANS.PRO_MONTHLY]: Infinity,
    [PLANS.PRO_ANNUAL]: Infinity,
  },
  // Células por planilha
  MAX_CELLS: {
    [PLANS.FREE]: 1000,
    [PLANS.PRO_MONTHLY]: 50000,
    [PLANS.PRO_ANNUAL]: Infinity,
  },
  // Gráficos por planilha
  MAX_CHARTS: {
    [PLANS.FREE]: 1,
    [PLANS.PRO_MONTHLY]: Infinity,
    [PLANS.PRO_ANNUAL]: Infinity,
  },
  // Comandos avançados de IA
  ADVANCED_AI_COMMANDS: {
    [PLANS.FREE]: false,
    [PLANS.PRO_MONTHLY]: true,
    [PLANS.PRO_ANNUAL]: true,
  },
  // Limites de taxa de uso (requisições por minuto)
  RATE_LIMITS: {
    [PLANS.FREE]: 30,
    [PLANS.PRO_MONTHLY]: 120,
    [PLANS.PRO_ANNUAL]: 240,
  },
};

// Lista de comandos de IA considerados avançados (apenas para planos pagos)
export const ADVANCED_AI_COMMANDS = [
  'análise preditiva',
  'previsão',
  'machine learning',
  'regressão',
  'correlação avançada',
  'modelo estatístico',
  'tendência futura',
  'clustering',
  'classificação',
  'série temporal',
  'forecast',
  'prediction',
];

// Tempo máximo de inatividade antes de reverificar o plano (30 minutos)
const PLAN_VERIFICATION_INTERVAL = 30 * 60 * 1000;

// Cache para reduzir consultas ao banco e prevenir contorno de limites
const userPlanCache = new Map<string, { plan: string; timestamp: number }>();

/**
 * Verifica se um comando de IA é avançado (apenas para planos pagos)
 */
export function isAdvancedAICommand(command: string): boolean {
  const normalizedCommand = command.toLowerCase();
  return ADVANCED_AI_COMMANDS.some(advancedCmd => normalizedCommand.includes(advancedCmd));
}

/**
 * Obtém o plano atual do usuário com cache e verificação de segurança
 */
export async function getUserSubscriptionPlan(userId: string): Promise<string> {
  try {
    // Verificar cache primeiro
    const cachedPlan = userPlanCache.get(userId);
    const now = Date.now();

    if (cachedPlan && now - cachedPlan.timestamp < PLAN_VERIFICATION_INTERVAL) {
      return cachedPlan.plan;
    }

    // Buscar assinatura ativa do usuário com verificação de status
    const subscription = await prisma.subscription.findFirst({
      where: {
        userId,
        OR: [{ status: 'active' }, { status: 'trialing' }],
        // Garantir que a assinatura não está expirada
        AND: [
          {
            OR: [{ currentPeriodEnd: { gt: new Date() } }, { currentPeriodEnd: null }],
          },
        ],
      },
      orderBy: { createdAt: 'desc' },
    });

    // Verificar se existem múltiplas contas com mesmo IP/device
    await detectPotentialAbusePattern(userId);

    const plan = subscription?.plan || PLANS.FREE;

    // Atualizar cache
    userPlanCache.set(userId, {
      plan,
      timestamp: now,
    });

    return plan;
  } catch (error) {
    logger.error('[GET_USER_PLAN_ERROR]', error);
    // Em caso de erro, NÃO assumir plano gratuito automaticamente
    // Logar o evento de falha para investigação e impedir o uso até resolução
    await logSecurityEvent(userId, 'plan_verification_failure', error);
    throw new Error(
      'Não foi possível verificar seu plano de assinatura. Tente novamente mais tarde.'
    );
  }
}

/**
 * Detecta padrões potenciais de abuso (múltiplas contas, compartilhamento, etc)
 */
async function detectPotentialAbusePattern(userId: string): Promise<void> {
  try {
    // Verificar usuários com mesmo IP
    const user = (await prisma.user.findUnique({
      where: { id: userId },
    })) as Record<string, unknown>;

    if (!user) return;

    // Verificação de múltiplas contas com mesmo IP
    if ('lastIpAddress' in user && user.lastIpAddress) {
      const accountsWithSameIp = (await prisma.user.findMany({
        where: {
          lastIpAddress: user.lastIpAddress,
          id: { not: userId },
        },
      })) as Record<string, unknown>[];

      // Contar apenas contas ativas recentemente criadas (últimos 30 dias)
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const recentAccountsCount = accountsWithSameIp.filter(
        account =>
          'createdAt' in account && account.createdAt && (account.createdAt as Date) > thirtyDaysAgo
      ).length;

      // Se houver mais de 3 contas recentes com mesmo IP, marcar como suspeito
      if (recentAccountsCount > 3) {
        // Verificar domínios de email para ver se são similares mas diferentes
        const emailDomains = accountsWithSameIp.map(a =>
          'email' in a && a.email ? (a.email as string).split('@')[1] || '' : ''
        );
        const uniqueDomains = new Set(emailDomains);
        const suspiciousDomains = uniqueDomains.size > 1 && uniqueDomains.size <= 3;

        // Criar log de segurança detalhado
        await logSecurityEvent(userId, 'multiple_accounts_same_ip', {
          ipAddress: user.lastIpAddress,
          accountCount: accountsWithSameIp.length,
          recentAccountsCount,
          suspiciousDomains,
          creationDates: accountsWithSameIp
            .map(a => ('createdAt' in a ? a.createdAt : null))
            .filter(Boolean),
          severity: recentAccountsCount > 5 ? 'high' : 'medium',
        });

        // Criar objeto de dados para atualização com campos que podem não existir ainda
        const updateData: Record<string, unknown> = {};

        // Campos novos adicionados pela migração
        updateData.isSuspicious = true;

        // Atualizar usuário
        await prisma.user.update({
          where: { id: userId },
          data: updateData,
        });

        // Se for um caso extremo (mais de 5 contas recentes), pode banir automaticamente
        if (recentAccountsCount > 5) {
          const banData: Record<string, unknown> = {
            // Campos adicionados pela migração - serão válidos após migração
            isBanned: true,
            banReason: 'Múltiplas contas com mesmo IP detectadas (possível abuso)',
            banDate: new Date(),
          };

          await prisma.user.update({
            where: { id: userId },
            data: banData,
          });

          // Revogar sessões do usuário
          await prisma.session.deleteMany({
            where: { userId },
          });
        }
      }
    }

    // Verificar tentativas de burlar limites (contas recém-criadas após atingir limite)
    if ('createdAt' in user && user.createdAt) {
      const creationTime = (user.createdAt as Date).getTime();
      const now = Date.now();
      const accountAgeHours = (now - creationTime) / (1000 * 60 * 60);

      // Se a conta for muito recente (menos de 24h), verificar atividades suspeitas
      if (accountAgeHours < 24) {
        const recentActivityCount = await prisma.userActionLog.count({
          where: {
            userId,
            action: {
              in: ['attempt_create_workbook', 'attempt_add_cells', 'attempt_advanced_ai'],
            },
            timestamp: {
              gte: new Date(Date.now() - 1000 * 60 * 60 * 2), // Últimas 2 horas
            },
          },
        });

        // Se houver muita atividade em conta nova, isso é suspeito
        if (recentActivityCount > 50) {
          await logSecurityEvent(userId, 'high_activity_new_account', {
            accountAgeHours,
            recentActivityCount,
            ipAddress: user.lastIpAddress,
            severity: 'high',
          });

          // Marcar como suspeito usando objeto dinâmico
          const suspiciousData: Record<string, unknown> = {
            isSuspicious: true,
          };

          await prisma.user.update({
            where: { id: userId },
            data: suspiciousData,
          });
        }
      }
    }
  } catch (error) {
    logger.error('[ABUSE_DETECTION_ERROR]', { userId, error });
    // Não interromper o fluxo em caso de erro na detecção
  }
}

/**
 * Registra eventos de segurança para auditoria
 */
async function logSecurityEvent(
  userId: string,
  eventType: string,
  details: unknown
): Promise<void> {
  try {
    await prisma.securityLog.create({
      data: {
        userId,
        eventType,
        details: JSON.stringify(details),
        timestamp: new Date(),
      },
    });
  } catch (error) {
    logger.error('[SECURITY_LOG_ERROR]', { userId, eventType, details, error });
  }
}

/**
 * Verifica se o usuário pode adicionar mais gráficos à planilha
 */
export async function canAddChart(
  userId: string,
  sheetId: string,
  currentChartCount: number
): Promise<{
  allowed: boolean;
  message?: string | undefined;
  limit: number;
}> {
  try {
    // Obter plano do usuário
    const userPlan = await getUserSubscriptionPlan(userId);

    // Obter limite do plano com fallback seguro para garantir que é sempre um número
    const defaultLimit = PLAN_LIMITS.MAX_CHARTS[PLANS.FREE] ?? 1;
    const chartLimitSafe = PLAN_LIMITS.MAX_CHARTS[userPlan] ?? defaultLimit;

    // Verificar se excedeu o limite
    const allowed = currentChartCount < chartLimitSafe;

    // Aplicar rate limiting baseado no plano
    const rateCheckResult = await checkRateLimit(userId, userPlan, 'add_chart');
    if (!rateCheckResult.allowed) {
      return {
        allowed: false,
        message: `Limite de taxa excedido. Tente novamente em ${rateCheckResult.timeRemaining ?? 60} segundos.`,
        limit: chartLimitSafe === Infinity ? -1 : chartLimitSafe,
      };
    }

    return {
      allowed,
      message: allowed
        ? undefined
        : `Você atingiu o limite de ${chartLimitSafe} ${chartLimitSafe === 1 ? 'gráfico' : 'gráficos'} para este plano. Faça upgrade para adicionar mais.`,
      limit: chartLimitSafe === Infinity ? -1 : chartLimitSafe,
    };
  } catch (error) {
    logger.error('[CAN_ADD_CHART_ERROR]', { userId, sheetId, error });
    throw new Error('Não foi possível verificar seus limites de uso. Tente novamente mais tarde.');
  }
}

export async function canCreateWorkbook(userId: string): Promise<{
  allowed: boolean;
  message?: string | undefined;
  currentCount: number;
  limit: number;
}> {
  try {
    // Obter plano do usuário
    const userPlan = await getUserSubscriptionPlan(userId);

    // Obter limite do plano com fallback seguro para garantir que é sempre um número
    const defaultLimit = PLAN_LIMITS.MAX_WORKBOOKS[PLANS.FREE] ?? 3;
    const workbookLimitSafe = PLAN_LIMITS.MAX_WORKBOOKS[userPlan] ?? defaultLimit;

    // Contar workbooks atuais do usuário
    const workbookCount = await prisma.workbook.count({
      where: { userId },
    });

    // Verificar se excedeu o limite
    const allowed = workbookCount < workbookLimitSafe;

    // Aplicar rate limiting baseado no plano
    const rateCheckResult = await checkRateLimit(userId, userPlan, 'create_workbook');
    if (!rateCheckResult.allowed) {
      return {
        allowed: false,
        currentCount: workbookCount,
        limit: workbookLimitSafe === Infinity ? -1 : workbookLimitSafe,
        message: rateCheckResult.message,
      };
    }

    // Registrar tentativa para auditoria
    await logUserAction(userId, 'attempt_create_workbook', {
      allowed,
      workbookCount,
      workbookLimit: workbookLimitSafe,
    });

    return {
      allowed,
      currentCount: workbookCount,
      limit: workbookLimitSafe === Infinity ? -1 : workbookLimitSafe,
      message: allowed
        ? undefined
        : `Você atingiu o limite de ${workbookLimitSafe} planilhas do seu plano ${userPlan}. Faça upgrade para criar mais.`,
    };
  } catch (error) {
    logger.error('[CHECK_WORKBOOK_LIMIT_ERROR]', error);
    // Em caso de erro, NÃO permitir criação
    // Forçar verificação de plano novamente
    userPlanCache.delete(userId);

    return {
      allowed: false,
      currentCount: 0,
      limit: 0,
      message: 'Não foi possível verificar seu limite de planilhas. Tente novamente mais tarde.',
    };
  }
}

/**
 * Verifica se o usuário pode adicionar mais células a uma planilha
 */
export async function canAddCells(
  userId: string,
  currentCells: number,
  additionalCells: number
): Promise<{
  allowed: boolean;
  message?: string | undefined;
  limit: number;
}> {
  try {
    // Obter plano do usuário
    const userPlan = await getUserSubscriptionPlan(userId);

    // Obter limite do plano com fallback seguro para garantir que é sempre um número
    const defaultLimit = PLAN_LIMITS.MAX_CELLS[PLANS.FREE] ?? 1000;
    const cellLimitSafe = PLAN_LIMITS.MAX_CELLS[userPlan] ?? defaultLimit;

    // Calcular total de células
    const totalCells = currentCells + additionalCells;

    // Verificar se excedeu o limite (Infinity permite ilimitado)
    const allowed = totalCells <= cellLimitSafe || cellLimitSafe === Infinity;

    // Aplicar rate limiting baseado no plano
    const rateCheckResult = await checkRateLimit(userId, userPlan, 'add_cells');
    if (!rateCheckResult.allowed) {
      return {
        allowed: false,
        message: rateCheckResult.message,
        // Use -1 para representar "ilimitado" na interface
        limit: cellLimitSafe === Infinity ? -1 : cellLimitSafe,
      };
    }

    return {
      allowed,
      message: allowed
        ? undefined
        : `Limite de ${cellLimitSafe === Infinity ? 'ilimitado' : cellLimitSafe} células excedido.`,
      // Use -1 para representar "ilimitado" na interface
      limit: cellLimitSafe === Infinity ? -1 : cellLimitSafe,
    };
  } catch (error) {
    logger.error('Erro ao verificar limite de células', { userId, error });
    return {
      allowed: false,
      message: 'Erro ao verificar limite de células',
      limit: PLAN_LIMITS.MAX_CELLS[PLANS.FREE] ?? 1000,
    };
  }
}

/**
 * Verifica se o usuário pode usar comandos avançados de IA
 */
export async function canUseAdvancedAI(
  userId: string,
  command: string
): Promise<{
  allowed: boolean;
  message?: string | undefined;
}> {
  // Se não for um comando avançado, permitir para qualquer plano
  if (!isAdvancedAICommand(command)) {
    return { allowed: true, message: undefined };
  }

  try {
    // Obter plano do usuário
    const userPlan = await getUserSubscriptionPlan(userId);

    // Verificar se o plano permite comandos avançados
    const allowed = PLAN_LIMITS.ADVANCED_AI_COMMANDS[userPlan] || false;

    // Aplicar rate limiting baseado no plano
    const rateCheckResult = await checkRateLimit(userId, userPlan, 'advanced_ai_command');
    if (!rateCheckResult.allowed) {
      return {
        allowed: false,
        message:
          rateCheckResult.message ||
          'Limite de taxa excedido para comandos avançados de IA. Tente novamente mais tarde.',
      };
    }

    // Registrar tentativa para auditoria
    await logUserAction(userId, 'attempt_advanced_ai', {
      allowed,
      command,
    });

    return {
      allowed,
      message: allowed
        ? undefined
        : `Recursos avançados de IA requerem um plano Premium. Faça upgrade para desbloquear esta funcionalidade.`,
    };
  } catch (error) {
    logger.error('[CHECK_ADVANCED_AI_ERROR]', { userId, command, error });
    // Em caso de erro, NÃO permitir
    return {
      allowed: false,
      message:
        'Não foi possível verificar seu acesso a recursos avançados de IA. Tente novamente mais tarde.',
    };
  }
}

/**
 * Valida se um sheet pertence ao usuário solicitante
 */
async function _validateSheetOwnership(
  userId: string,
  sheetId: string
): Promise<{
  valid: boolean;
}> {
  try {
    const sheet = await prisma.sheet.findUnique({
      where: { id: sheetId },
      include: { workbook: true },
    });

    if (!sheet) {
      return { valid: false };
    }

    return { valid: sheet.workbook.userId === userId };
  } catch (error) {
    logger.error('[VALIDATE_SHEET_OWNERSHIP_ERROR]', { userId, sheetId, error });
    return { valid: false };
  }
}

/**
 * Calcula o número de células em uma planilha com base nos dados
 * Com validação adicional para prevenir manipulações
 */
export function countCellsInSheet(sheetData: unknown): number {
  if (!sheetData) return 0;

  // Função para verificar se o valor é válido (não é estrutura maliciosa)
  const isValidCellValue = (value: unknown): boolean => {
    if (value === null || value === undefined) return true;

    // Verificar tipos primitivos
    const type = typeof value;
    if (type === 'string' || type === 'number' || type === 'boolean') return true;

    // Verificar se é objeto simples ou data
    if (type === 'object') {
      return (
        value instanceof Date ||
        // Permitir objetos simples mas não funções ou classes complexas
        (Object.getPrototypeOf(value) === Object.prototype && Object.keys(value).length < 10)
      );
    }

    return false;
  };

  // Se for formato de matriz
  if (Array.isArray(sheetData)) {
    if (sheetData.length === 0) return 0;

    // Limitar tamanho máximo para prevenir ataques DoS
    const maxRowsToProcess = 10000;
    const rowsToProcess = Math.min(sheetData.length, maxRowsToProcess);

    // Se for array de arrays
    if (Array.isArray(sheetData[0])) {
      let total = 0;
      for (let i = 0; i < rowsToProcess; i++) {
        const row = sheetData[i];
        if (Array.isArray(row)) {
          // Limitar número de colunas também
          const colsToProcess = Math.min(row.length, 1000);
          for (let j = 0; j < colsToProcess; j++) {
            if (isValidCellValue(row[j])) {
              total++;
            }
          }
        }
      }
      return total;
    }

    // Se for array de objetos, contar propriedades
    if (typeof sheetData[0] === 'object' && sheetData[0] !== null) {
      const headerCount = Object.keys(sheetData[0] || {}).filter(key =>
        isValidCellValue(sheetData[0][key])
      ).length;

      // Validar cada objeto para garantir consistência
      let validRowCount = 0;
      for (let i = 0; i < rowsToProcess; i++) {
        const row = sheetData[i];
        if (typeof row === 'object' && row !== null) {
          validRowCount++;
        }
      }

      return headerCount * validRowCount;
    }

    // Contar apenas valores válidos
    return sheetData.filter(item => isValidCellValue(item)).length;
  }

  // Se for formato de células { A1: valor, B2: valor }
  if (typeof sheetData === 'object' && sheetData !== null) {
    // Filtrar apenas chaves válidas (formato A1, B2, etc)
    return Object.entries(sheetData).filter(
      ([key, value]) => /^[A-Z]+\d+$/.test(key) && isValidCellValue(value)
    ).length;
  }

  return 0;
}

/**
 * Verifica limites de taxa com base no plano
 */
async function checkRateLimit(
  userId: string,
  userPlan: string,
  action: string
): Promise<{ allowed: boolean; message?: string; timeRemaining?: number }> {
  try {
    // Garantir que temos um limite válido, com fallback para plano FREE
    const rateLimitDefault = PLAN_LIMITS.RATE_LIMITS[PLANS.FREE] || 10;
    const limit = PLAN_LIMITS.RATE_LIMITS[userPlan] || rateLimitDefault;

    // Aplicar rate limit baseado no plano
    const { success, limit: _maxLimit, remaining, reset, error } = await rateLimit(userId, limit);

    if (!success) {
      // Se tiver uma mensagem de erro específica do rate limiter, usar ela
      if (error) {
        await logSecurityEvent(userId, 'rate_limit_error', { action, error });
        return {
          allowed: false,
          message: error,
        };
      }

      const resetInSeconds = Math.ceil((reset - Date.now()) / 1000);
      return {
        allowed: false,
        message: `Você excedeu o limite de ações por minuto. Tente novamente em ${resetInSeconds} segundos.`,
        timeRemaining: resetInSeconds,
      };
    }

    // Registrar proximidade de limite para monitoramento (quando abaixo de 20%)
    // Usar o valor limite que temos certeza que é um número
    if (remaining <= Math.floor(limit * 0.2) && remaining > 0) {
      await logUserAction(userId, 'approaching_rate_limit', {
        action,
        remaining,
        limit,
        percentRemaining: Math.round((remaining / limit) * 100),
      });
    }

    return { allowed: true };
  } catch (error) {
    logger.error('[RATE_LIMIT_CHECK_ERROR]', { userId, userPlan, action, error });

    // Em caso de erro, NÃO permitir por segurança
    await logSecurityEvent(userId, 'rate_limit_verification_error', {
      action,
      error: String(error),
    });

    return {
      allowed: false,
      message: 'Não foi possível verificar limites de uso. Tente novamente em alguns instantes.',
    };
  }
}

/**
 * Registra ações do usuário para análise e detecção de padrões
 */
async function logUserAction(
  userId: string,
  action: string,
  details: Record<string, unknown>
): Promise<void> {
  try {
    await prisma.userActionLog.create({
      data: {
        userId,
        action,
        details: JSON.stringify(details),
        timestamp: new Date(),
      },
    });
  } catch (error) {
    logger.error('[USER_ACTION_LOG_ERROR]', { userId, action, details, error });
    // Erro no log não deve afetar a operação principal
  }
}

export async function processRemainingUsage(
  userId: string,
  resource: 'operations' | 'data_transfer' | 'api_calls',
  usageData: unknown
): Promise<{
  allowed: boolean;
  limitExceeded?: boolean;
  currentUsage: number;
  limit: number;
  remaining: number;
  percentRemaining: number;
  warningLevel: 'ok' | 'warning' | 'critical';
}> {
  try {
    // Obter plano do usuário
    const plan = await getUserSubscriptionPlan(userId);

    // Obter limites para o plano com valor padrão seguro
    const limitByPlan: Record<string, number> = {
      free: 100,
      basic: 1000,
      premium: 10000,
      enterprise: Infinity,
    };

    // Valor padrão se nada mais funcionar
    const DEFAULT_LIMIT = 100;

    // Garantir que temos um limite válido, mesmo que o plano não exista no mapa
    const limitSafe = (plan && limitByPlan[plan]) || limitByPlan.free || DEFAULT_LIMIT;

    // Calcular uso atual e restante
    const currentUsage = ((usageData as Record<string, unknown>)?.total as number) || 0;
    const remaining = limitSafe === Infinity ? 999999 : Math.max(0, limitSafe - currentUsage);

    // Determinar se excedeu o limite
    const limitExceeded = limitSafe !== Infinity && currentUsage >= limitSafe;

    // Determinar nível de alerta
    let warningLevel: 'ok' | 'warning' | 'critical' = 'ok';
    if (limitExceeded) {
      warningLevel = 'critical';
    } else if (limitSafe !== Infinity && remaining <= Math.floor(limitSafe * 0.2)) {
      warningLevel = 'warning';
    }

    return {
      allowed: !limitExceeded,
      limitExceeded,
      currentUsage,
      limit: limitSafe === Infinity ? -1 : limitSafe,
      remaining: limitSafe === Infinity ? -1 : remaining,
      percentRemaining: limitSafe === Infinity ? 100 : Math.round((remaining / limitSafe) * 100),
      warningLevel,
    };
  } catch (error) {
    logger.error('Erro ao processar uso restante', { userId, resource, error });
    return {
      allowed: true, // Permitir em caso de erro para evitar bloqueios indevidos
      currentUsage: 0,
      limit: 100,
      remaining: 100,
      percentRemaining: 100,
      warningLevel: 'ok',
    };
  }
}
