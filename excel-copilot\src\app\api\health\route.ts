/**
 * 🏥 COMPREHENSIVE HEALTH CHECK ENDPOINT
 *
 * GET /api/health
 * GET /api/health?type=critical
 * GET /api/health?details=false
 *
 * Verifica a saúde de todos os serviços do sistema
 */

import { NextRequest, NextResponse } from 'next/server';

import {
  checkAllServices,
  checkCriticalServices,
  healthStatusToHttpCode,
  formatHealthResponse,
} from '@/lib/health-checks/index';

// Verificar se estamos no processo de build estático ou exportação
const isBuildTime =
  process.env.NEXT_PHASE === 'phase-production-build' || process.env.NEXT_STATIC_EXPORT === 'true';

// Forçar o modo dinâmico para essa rota
export const dynamic = 'force-dynamic';

/**
 * @swagger
 * /api/health:
 *   get:
 *     summary: Verifica o status de saúde da aplicação
 *     description: Endpoint para verificação de saúde que verifica todos os serviços necessários
 *     parameters:
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *           enum: [all, critical]
 *         description: Tipo de verificação (all=todos os serviços, critical=apenas críticos)
 *       - in: query
 *         name: details
 *         schema:
 *           type: boolean
 *         description: Incluir detalhes na resposta (padrão=true)
 *     responses:
 *       200:
 *         description: Sistema saudável
 *       503:
 *         description: Um ou mais serviços não estão respondendo
 */
export async function GET(request: NextRequest) {
  // Pular a verificação completa durante o build estático
  if (isBuildTime) {
    return NextResponse.json(
      {
        status: 'skipped',
        message: 'Verificação de saúde ignorada durante build estático',
        timestamp: new Date().toISOString(),
      },
      { status: 200 }
    );
  }

  try {
    // Extrair parâmetros da query
    const url = new URL(request.url);
    const type = url.searchParams.get('type') || 'all';
    const includeDetails = url.searchParams.get('details') !== 'false';

    // Executar health check baseado no tipo
    let result;
    if (type === 'critical') {
      result = await checkCriticalServices();
    } else {
      result = await checkAllServices();
    }

    // Determinar código de status HTTP
    const statusCode = healthStatusToHttpCode(result.overall);

    // Formatar resposta
    const response = formatHealthResponse(result, includeDetails);

    return NextResponse.json(response, {
      status: statusCode,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        Pragma: 'no-cache',
        Expires: '0',
      },
    });
  } catch (error: unknown) {
    return NextResponse.json(
      {
        status: 'unhealthy',
        message: 'Erro ao verificar status do sistema',
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      {
        status: 500,
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
        },
      }
    );
  }
}

// Permitir apenas GET
export async function POST() {
  return NextResponse.json({ error: 'Method not allowed' }, { status: 405 });
}

export async function PUT() {
  return NextResponse.json({ error: 'Method not allowed' }, { status: 405 });
}

export async function DELETE() {
  return NextResponse.json({ error: 'Method not allowed' }, { status: 405 });
}
