import { CommandFeedbackData } from '@/components/command-feedback';
import { logger } from '@/lib/logger';

interface FeedbackAnalytics {
  totalCommands: number;
  successRate: number;
  commonIssues: Array<{ issue: string; count: number }>;
  commandPatterns: Array<{ pattern: string; successRate: number; count: number }>;
}

export class FeedbackService {
  private static instance: FeedbackService;
  private feedbackItems: CommandFeedbackData[] = [];
  private feedbackStorage: Storage | null = null;
  private readonly STORAGE_KEY = 'excel_copilot_feedback';
  private readonly MAX_STORED_ITEMS = 100;

  private constructor() {
    // Inicializar armazenamento apenas no cliente
    if (typeof window !== 'undefined') {
      this.feedbackStorage = window.localStorage;
      this.loadFromStorage();
    }
  }

  public static getInstance(): FeedbackService {
    if (!FeedbackService.instance) {
      FeedbackService.instance = new FeedbackService();
    }
    return FeedbackService.instance;
  }

  /**
   * Armazena um item de feedback para análise posterior
   */
  public async storeFeedback(feedback: CommandFeedbackData): Promise<void> {
    // Adicionar timestamp
    const feedbackWithTime = {
      ...feedback,
      timestamp: new Date().toISOString(),
    } as CommandFeedbackData & { timestamp: string };

    // Adicionar à lista local
    this.feedbackItems.unshift(feedbackWithTime);

    // Limitar tamanho da lista
    if (this.feedbackItems.length > this.MAX_STORED_ITEMS) {
      this.feedbackItems = this.feedbackItems.slice(0, this.MAX_STORED_ITEMS);
    }

    // Persistir no armazenamento local
    this.saveToStorage();

    // Enviar para API se estiver em produção (feature flag)
    if (process.env.NEXT_PUBLIC_ENABLE_FEEDBACK_API === 'true') {
      try {
        await fetch('/api/feedback', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(feedbackWithTime),
        });
      } catch (error) {
        logger.error('Erro ao enviar feedback para API:', error);
        // Continue mesmo se falhar o envio para a API
      }
    }
  }

  /**
   * Obtém analytics baseados no feedback coletado
   */
  public getAnalytics(): FeedbackAnalytics {
    const totalCommands = this.feedbackItems.length;
    if (totalCommands === 0) {
      return {
        totalCommands: 0,
        successRate: 0,
        commonIssues: [],
        commandPatterns: [],
      };
    }

    // Calcular taxa de sucesso
    const successfulCommands = this.feedbackItems.filter(item => item.successful).length;
    const successRate = (successfulCommands / totalCommands) * 100;

    // Extrair problemas comuns de feedback negativo
    const negativeItems = this.feedbackItems.filter(item => !item.successful && item.feedbackText);

    // Análise simplificada de texto para problemas comuns
    const issueMap = new Map<string, number>();

    negativeItems.forEach(item => {
      const text = item.feedbackText?.toLowerCase() || '';

      // Categorizar baseado em palavras-chave
      const keywords = [
        { word: 'entend', issue: 'Não entendeu o comando' },
        { word: 'error', issue: 'Erro na execução' },
        { word: 'lent', issue: 'Performance lenta' },
        { word: 'format', issue: 'Problemas de formatação' },
        { word: 'gráfico', issue: 'Problemas com gráficos' },
        { word: 'tabela', issue: 'Problemas com tabelas' },
        { word: 'fórmula', issue: 'Problemas com fórmulas' },
      ];

      let matched = false;
      for (const keyword of keywords) {
        if (text.includes(keyword.word)) {
          issueMap.set(keyword.issue, (issueMap.get(keyword.issue) || 0) + 1);
          matched = true;
        }
      }

      // Se nenhuma palavra-chave foi encontrada
      if (!matched && text.length > 0) {
        issueMap.set('Outros problemas', (issueMap.get('Outros problemas') || 0) + 1);
      }
    });

    const commonIssues = Array.from(issueMap.entries())
      .map(([issue, count]) => ({ issue, count }))
      .sort((a, b) => b.count - a.count);

    // Análise de padrões de comando (simplificada)
    const patternMap = new Map<string, { success: number; total: number }>();

    this.feedbackItems.forEach(item => {
      const command = item.command.toLowerCase();

      // Categorizar baseado em palavras-chave no comando
      const patterns = [
        { words: ['cri', 'tabela'], pattern: 'Criar tabela' },
        { words: ['gráfico', 'chart'], pattern: 'Criar gráfico' },
        { words: ['calcul', 'média', 'soma'], pattern: 'Cálculos' },
        { words: ['format', 'cor', 'estilo'], pattern: 'Formatação' },
        { words: ['filtr', 'ordem'], pattern: 'Filtro/Ordenação' },
      ];

      for (const pattern of patterns) {
        if (pattern.words.some(word => command.includes(word))) {
          const currentData = patternMap.get(pattern.pattern) || { success: 0, total: 0 };
          patternMap.set(pattern.pattern, {
            success: currentData.success + (item.successful ? 1 : 0),
            total: currentData.total + 1,
          });
          break; // Associar apenas a um padrão
        }
      }
    });

    const commandPatterns = Array.from(patternMap.entries())
      .map(([pattern, data]) => ({
        pattern,
        successRate: (data.success / data.total) * 100,
        count: data.total,
      }))
      .sort((a, b) => b.count - a.count);

    return {
      totalCommands,
      successRate,
      commonIssues,
      commandPatterns,
    };
  }

  /**
   * Salva feedback no armazenamento local
   */
  private saveToStorage(): void {
    if (this.feedbackStorage) {
      try {
        this.feedbackStorage.setItem(this.STORAGE_KEY, JSON.stringify(this.feedbackItems));
      } catch (error) {
        logger.error('Erro ao salvar feedback no localStorage:', error);
      }
    }
  }

  /**
   * Carrega feedback do armazenamento local
   */
  private loadFromStorage(): void {
    if (this.feedbackStorage) {
      try {
        const storedData = this.feedbackStorage.getItem(this.STORAGE_KEY);
        if (storedData) {
          this.feedbackItems = JSON.parse(storedData);
        }
      } catch (error) {
        logger.error('Erro ao carregar feedback do localStorage:', error);
        // Em caso de erro, iniciar com array vazio
        this.feedbackItems = [];
      }
    }
  }
}

// Exportar instância singleton
export const feedbackService = FeedbackService.getInstance();
