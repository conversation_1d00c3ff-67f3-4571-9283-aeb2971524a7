/**
 * Rate Limiting específico para endpoints OAuth
 * Implementa proteção contra ataques de força bruta em autenticação
 */

import { NextRequest } from 'next/server';

import { logger } from '@/lib/logger';

interface RateLimitConfig {
  windowMs: number;
  maxAttempts: number;
  blockDurationMs: number;
}

interface RateLimitEntry {
  attempts: number;
  firstAttempt: number;
  blockedUntil?: number;
}

// Configurações por tipo de endpoint
const RATE_LIMIT_CONFIGS: Record<string, RateLimitConfig> = {
  oauth_signin: {
    windowMs: 15 * 60 * 1000, // 15 minutos
    maxAttempts: 5,
    blockDurationMs: 30 * 60 * 1000, // 30 minutos
  },
  oauth_callback: {
    windowMs: 5 * 60 * 1000, // 5 minutos
    maxAttempts: 10,
    blockDurationMs: 15 * 60 * 1000, // 15 minutos
  },
  oauth_error: {
    windowMs: 10 * 60 * 1000, // 10 minutos
    maxAttempts: 3,
    blockDurationMs: 60 * 60 * 1000, // 1 hora
  },
};

// Store em memória (em produção, usar Redis)
const rateLimitStore = new Map<string, RateLimitEntry>();

/**
 * Gera chave única para rate limiting baseada em IP e tipo
 */
function generateKey(ip: string, type: string): string {
  return `oauth_rate_limit:${type}:${ip}`;
}

/**
 * Obtém IP do cliente de forma segura
 */
function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');

  if (forwarded) {
    return forwarded.split(',')[0]?.trim() || 'unknown';
  }

  if (realIP) {
    return realIP;
  }

  return request.ip || 'unknown';
}

/**
 * Verifica se o IP está dentro do limite de rate limiting
 */
export function checkOAuthRateLimit(
  request: NextRequest,
  type: keyof typeof RATE_LIMIT_CONFIGS
): { allowed: boolean; remaining: number; resetTime: number } {
  const config = RATE_LIMIT_CONFIGS[type];
  if (!config) {
    logger.warn(`Rate limit config not found for type: ${type}`);
    return { allowed: true, remaining: 999, resetTime: 0 };
  }

  const ip = getClientIP(request);
  const key = generateKey(ip, type);
  const now = Date.now();

  let entry = rateLimitStore.get(key);

  // Verificar se está bloqueado
  if (entry?.blockedUntil && now < entry.blockedUntil) {
    logger.warn(`OAuth rate limit blocked`, {
      ip,
      type,
      blockedUntil: new Date(entry.blockedUntil).toISOString(),
    });

    return {
      allowed: false,
      remaining: 0,
      resetTime: entry.blockedUntil,
    };
  }

  // Limpar entrada se a janela expirou
  if (entry && now - entry.firstAttempt > config.windowMs) {
    rateLimitStore.delete(key);
    entry = undefined;
  }

  // Criar nova entrada se não existir
  if (!entry) {
    entry = {
      attempts: 1,
      firstAttempt: now,
    };
    rateLimitStore.set(key, entry);

    return {
      allowed: true,
      remaining: config.maxAttempts - 1,
      resetTime: now + config.windowMs,
    };
  }

  // Incrementar tentativas
  entry.attempts++;

  // Verificar se excedeu o limite
  if (entry.attempts > config.maxAttempts) {
    entry.blockedUntil = now + config.blockDurationMs;

    logger.error(`OAuth rate limit exceeded`, {
      ip,
      type,
      attempts: entry.attempts,
      maxAttempts: config.maxAttempts,
      blockedUntil: new Date(entry.blockedUntil).toISOString(),
    });

    return {
      allowed: false,
      remaining: 0,
      resetTime: entry.blockedUntil,
    };
  }

  return {
    allowed: true,
    remaining: config.maxAttempts - entry.attempts,
    resetTime: entry.firstAttempt + config.windowMs,
  };
}

/**
 * Registra tentativa de OAuth bem-sucedida (limpa rate limit)
 */
export function recordOAuthSuccess(request: NextRequest, type: string): void {
  const ip = getClientIP(request);
  const key = generateKey(ip, type);

  rateLimitStore.delete(key);

  logger.info(`OAuth success recorded`, {
    ip,
    type,
    timestamp: new Date().toISOString(),
  });
}

/**
 * Registra tentativa de OAuth falhada
 */
export function recordOAuthFailure(request: NextRequest, type: string, error: string): void {
  const ip = getClientIP(request);

  logger.warn(`OAuth failure recorded`, {
    ip,
    type,
    error,
    timestamp: new Date().toISOString(),
  });
}

/**
 * Limpa entradas expiradas do store (executar periodicamente)
 */
export function cleanupExpiredEntries(): void {
  const now = Date.now();
  let cleaned = 0;

  for (const [key, entry] of rateLimitStore.entries()) {
    const isExpired = entry.blockedUntil
      ? now > entry.blockedUntil
      : now - entry.firstAttempt >
        Math.max(...Object.values(RATE_LIMIT_CONFIGS).map(c => c.windowMs));

    if (isExpired) {
      rateLimitStore.delete(key);
      cleaned++;
    }
  }

  if (cleaned > 0) {
    logger.info(`Cleaned up ${cleaned} expired rate limit entries`);
  }
}

/**
 * Obtém estatísticas do rate limiting
 */
export function getRateLimitStats(): {
  totalEntries: number;
  blockedIPs: number;
  configuredLimits: typeof RATE_LIMIT_CONFIGS;
} {
  const now = Date.now();
  let blockedIPs = 0;

  for (const entry of rateLimitStore.values()) {
    if (entry.blockedUntil && now < entry.blockedUntil) {
      blockedIPs++;
    }
  }

  return {
    totalEntries: rateLimitStore.size,
    blockedIPs,
    configuredLimits: RATE_LIMIT_CONFIGS,
  };
}
