import {
  Command,
  X,
  Calculator,
  <PERSON><PERSON><PERSON>,
  <PERSON>rk<PERSON>,
  Table,
  FileSpreadsheet,
  Filter,
  ArrowDownUp,
  Search,
} from 'lucide-react';
import { useState, useEffect, useRef } from 'react';

import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';

// Categorias de comandos
type CommandCategory = 'all' | 'calculation' | 'visualization' | 'formatting' | 'filter' | 'data';

// Lista de comandos expandida e categorizada
const EXAMPLE_COMMANDS = [
  // Comandos de cálculo
  {
    id: 'soma',
    command: '/soma',
    description: 'Soma valores em um intervalo de células',
    example: '/soma A1:A10',
    category: 'calculation',
    icon: <Calculator className="h-4 w-4 text-blue-500" />,
  },
  {
    id: 'media',
    command: '/media',
    description: 'Calcula a média de um intervalo de células',
    example: '/media B1:B10',
    category: 'calculation',
    icon: <Calculator className="h-4 w-4 text-blue-500" />,
  },
  {
    id: 'maximo',
    command: '/maximo',
    description: 'Encontra o valor máximo em um intervalo',
    example: '/maximo C1:C20',
    category: 'calculation',
    icon: <Calculator className="h-4 w-4 text-blue-500" />,
  },

  // Comandos de visualização
  {
    id: 'grafico',
    command: '/grafico',
    description: 'Cria um gráfico com os dados selecionados',
    example: '/grafico tipo="barras" dados=A1:B10',
    category: 'visualization',
    icon: <BarChart className="h-4 w-4 text-green-500" />,
  },
  {
    id: 'pizza',
    command: '/grafico-pizza',
    description: 'Cria um gráfico de pizza',
    example: '/grafico-pizza dados=C1:D10 titulo="Vendas por Região"',
    category: 'visualization',
    icon: <BarChart className="h-4 w-4 text-green-500" />,
  },

  // Comandos de formatação
  {
    id: 'formatar',
    command: '/formatar',
    description: 'Formata células selecionadas',
    example: '/formatar A1:C10 negrito cor="azul"',
    category: 'formatting',
    icon: <Sparkles className="h-4 w-4 text-purple-500" />,
  },
  {
    id: 'condicional',
    command: '/formato-condicional',
    description: 'Aplica formatação condicional',
    example: '/formato-condicional A1:A10 maior=100 cor="verde"',
    category: 'formatting',
    icon: <Sparkles className="h-4 w-4 text-purple-500" />,
  },

  // Comandos de filtro
  {
    id: 'filtrar',
    command: '/filtrar',
    description: 'Filtra dados com base em critérios',
    example: '/filtrar coluna="Vendas" valor>1000',
    category: 'filter',
    icon: <Filter className="h-4 w-4 text-amber-500" />,
  },
  {
    id: 'ordenar',
    command: '/ordenar',
    description: 'Ordena dados de uma coluna',
    example: '/ordenar coluna="Data" crescente=true',
    category: 'filter',
    icon: <ArrowDownUp className="h-4 w-4 text-amber-500" />,
  },

  // Comandos de manipulação de dados
  {
    id: 'tabela',
    command: '/tabela',
    description: 'Converte intervalo em tabela formatada',
    example: '/tabela A1:D10 nome="MinhaTabela"',
    category: 'data',
    icon: <Table className="h-4 w-4 text-red-500" />,
  },
  {
    id: 'inserir',
    command: '/inserir',
    description: 'Insere novas linhas ou colunas',
    example: '/inserir linhas=5 posicao=A10',
    category: 'data',
    icon: <FileSpreadsheet className="h-4 w-4 text-red-500" />,
  },
];

interface CommandPaletteProps {
  onSelect: (command: string) => void;
  onClose: () => void;
}

export function CommandPalette({ onSelect, onClose }: CommandPaletteProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredCommands, setFilteredCommands] = useState(EXAMPLE_COMMANDS);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [activeCategory, setActiveCategory] = useState<CommandCategory>('all');
  const inputRef = useRef<HTMLInputElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Filtrar comandos com base no termo de busca e categoria
  useEffect(() => {
    let filtered = EXAMPLE_COMMANDS;

    // Filtrar por categoria se não for 'todos'
    if (activeCategory !== 'all') {
      filtered = filtered.filter(cmd => cmd.category === activeCategory);
    }

    // Filtrar por termo de busca
    if (searchTerm) {
      filtered = filtered.filter(
        cmd =>
          cmd.command.toLowerCase().includes(searchTerm.toLowerCase()) ||
          cmd.description.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    setFilteredCommands(filtered);
    setSelectedIndex(0);
  }, [searchTerm, activeCategory]);

  // Focar no input ao montar o componente
  useEffect(() => {
    inputRef.current?.focus();

    // Fechar com clique fora
    const handleClickOutside = (e: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(e.target as Node)) {
        onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [onClose]);

  // Navegar com teclado
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!filteredCommands || filteredCommands.length === 0) return;

    if (e.key === 'ArrowDown') {
      e.preventDefault();
      setSelectedIndex(prev => (prev + 1) % filteredCommands.length);
    } else if (e.key === 'ArrowUp') {
      e.preventDefault();
      setSelectedIndex(prev => (prev - 1 + filteredCommands.length) % filteredCommands.length);
    } else if (e.key === 'Enter') {
      e.preventDefault();
      // Verificar se selectedIndex é válido e se existe um comando na posição selecionada
      if (
        selectedIndex >= 0 &&
        selectedIndex < filteredCommands.length &&
        filteredCommands[selectedIndex] &&
        typeof filteredCommands[selectedIndex].command === 'string'
      ) {
        handleSelect(filteredCommands[selectedIndex].command);
      }
    } else if (e.key === 'Escape') {
      e.preventDefault();
      onClose();
    }
  };

  const handleSelect = (command: string) => {
    if (typeof command !== 'string') return;
    onSelect(command);
    onClose();
  };

  // Lista de categorias para filtro
  const categories: { id: CommandCategory; label: string; icon: JSX.Element }[] = [
    { id: 'all', label: 'Todos', icon: <Search className="h-4 w-4" /> },
    { id: 'calculation', label: 'Cálculos', icon: <Calculator className="h-4 w-4" /> },
    { id: 'visualization', label: 'Gráficos', icon: <BarChart className="h-4 w-4" /> },
    { id: 'formatting', label: 'Formatação', icon: <Sparkles className="h-4 w-4" /> },
    { id: 'filter', label: 'Filtros', icon: <Filter className="h-4 w-4" /> },
    { id: 'data', label: 'Dados', icon: <Table className="h-4 w-4" /> },
  ];

  return (
    <div
      ref={containerRef}
      className="absolute bottom-full left-0 w-full max-w-md bg-background border border-input rounded-md shadow-md z-50 mb-2 overflow-hidden"
      role="dialog"
      aria-label="Paleta de comandos"
    >
      <div className="flex items-center p-2 border-b">
        <Command className="h-4 w-4 text-muted-foreground mr-2" />
        <input
          ref={inputRef}
          type="text"
          value={searchTerm}
          onChange={e => setSearchTerm(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder="Pesquisar comandos..."
          className="flex-1 bg-transparent outline-none text-sm"
          aria-label="Pesquisar comandos"
        />
        <button
          onClick={() => onClose()}
          className="h-6 w-6 flex items-center justify-center rounded-sm hover:bg-muted"
          aria-label="Fechar paleta de comandos"
        >
          <X className="h-4 w-4 text-muted-foreground" />
        </button>
      </div>

      {/* Categorias de filtro */}
      <div className="flex items-center gap-1 p-2 overflow-x-auto border-b scrollbar-hide">
        {categories.map(category => (
          <Badge
            key={category.id}
            variant={activeCategory === category.id ? 'default' : 'outline'}
            className="cursor-pointer px-2 py-1 flex items-center gap-1"
            onClick={() => setActiveCategory(category.id)}
          >
            {category.icon}
            <span>{category.label}</span>
          </Badge>
        ))}
      </div>

      <ScrollArea className="max-h-[300px]">
        <div className="py-1" role="listbox">
          {filteredCommands.length > 0 ? (
            filteredCommands.map((cmd, index) => (
              <div
                key={cmd.id}
                className={`px-3 py-2 text-sm cursor-pointer transition-colors ${
                  index === selectedIndex ? 'bg-muted' : 'hover:bg-muted/50'
                }`}
                onClick={() => handleSelect(cmd.command)}
                onMouseEnter={() => setSelectedIndex(index)}
                role="option"
                aria-selected={index === selectedIndex}
                tabIndex={-1}
              >
                <div className="flex items-center gap-2">
                  {cmd.icon}
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <span className="font-medium">{cmd.command}</span>
                      <kbd className="text-xs bg-muted px-1.5 py-0.5 rounded text-muted-foreground">
                        Enter ↵
                      </kbd>
                    </div>
                    <p className="text-xs text-muted-foreground">{cmd.description}</p>
                    <p className="text-xs italic mt-0.5 text-muted-foreground">{cmd.example}</p>
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className="px-3 py-4 text-sm text-center text-muted-foreground">
              Nenhum comando encontrado
            </div>
          )}
        </div>
      </ScrollArea>
    </div>
  );
}
