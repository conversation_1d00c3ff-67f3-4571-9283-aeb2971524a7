# Configuração de Conexão Supabase para Excel Copilot

## Visão Geral

Este documento descreve a configuração atualizada para conectar o Excel Copilot ao banco de dados PostgreSQL hospedado no Supabase, utilizando Prisma ORM em ambiente de produção na Vercel.

## Novas Variáveis de Ambiente

Configure as seguintes variáveis de ambiente na Vercel:

```
DATABASE_URL=postgres://postgres.[PROJECT-ID]:231Bancovercel@aws-0-[REGION].pooler.supabase.com:6543/postgres?pgbouncer=true&connection_limit=1&pool_timeout=20
DIRECT_URL=postgres://postgres.[PROJECT-ID]:231Bancovercel@aws-0-[REGION].pooler.supabase.com:5432/postgres
```

Substitua `[PROJECT-ID]` e `[REGION]` pelos valores específicos do seu projeto Supabase.

### Explicação

- **DATABASE_URL**: Utiliza a porta 6543 (modo transação) do Supavisor. Esta configuração é otimizada para funções serverless com conexões transitórias, como é o caso da Vercel. Os parâmetros adicionais:

  - `pgbouncer=true`: Necessário para usar o connection pooler
  - `connection_limit=1`: Limita o número de conexões para evitar exceder limites do Supabase
  - `pool_timeout=20`: Aumenta o timeout para 20 segundos, evitando erros de conexão

- **DIRECT_URL**: Utiliza a porta 5432 (modo sessão) para operações administrativas como migrações. Não precisa dos parâmetros adicionais de pooling.

## Migrações de Banco de Dados

Para aplicar migrações ao banco de dados, execute:

```bash
npx prisma migrate deploy
```

Este comando deve ser executado após cada alteração no schema do banco de dados.

## Recomendações Adicionais

1. **Evite aspas ou colchetes**: Ao configurar as variáveis de ambiente na Vercel, não use aspas ou colchetes ao redor dos valores.

2. **Script postinstall**: O projeto já inclui o script `postinstall` no package.json que executa `prisma generate` automaticamente após a instalação de dependências.

3. **Monitoramento**: Considere implementar monitoramento para verificar os tempos de resposta do banco de dados e possíveis erros de conexão.

4. **Verificação de saúde**: Utilize o endpoint `/api/health/db` para verificar a saúde da conexão com o banco de dados.

## Solução de Problemas

Se encontrar erros relacionados à conexão com o banco de dados, verifique:

1. **Erro "Connection pool timeout"**: Aumente o valor de `pool_timeout` na string de conexão
2. **Erro "Not enough connections in the pool"**: Verifique se o limite de conexões do Supabase não está sendo excedido
3. **Erro "Cannot reach database server"**: Confirme que as credenciais estão corretas e que não há bloqueios de rede

Consulte a documentação do Prisma e Supabase para mais informações sobre configuração avançada e solução de problemas.
