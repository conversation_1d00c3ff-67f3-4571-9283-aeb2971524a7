// Tipos para componentes de UI
import { ReactNode } from 'react';

// Re-exportar componentes usando caminhos absolutos diretos para o consumo
export { default as Input } from '../components/ui/input';
export { default as Textarea } from '../components/ui/textarea';

export interface BaseComponentProps {
  className?: string;
  id?: string;
}

export interface ButtonProps extends BaseComponentProps {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'link';
  size?: 'sm' | 'md' | 'lg';
  isLoading?: boolean;
  disabled?: boolean;
  onClick?: () => void;
  type?: 'button' | 'submit' | 'reset';
  children: ReactNode;
}

export interface CardProps extends BaseComponentProps {
  children: ReactNode;
  header?: ReactNode;
  footer?: ReactNode;
}

export interface DashboardWidgetProps extends BaseComponentProps {
  title: string;
  description?: string;
  icon?: ReactNode;
  children: ReactNode;
  isLoading?: boolean;
}

export interface ChartComponentProps extends BaseComponentProps {
  data: any;
  type: 'bar' | 'line' | 'pie' | 'scatter' | 'area';
  options?: any;
  height?: number | string;
  width?: number | string;
}
