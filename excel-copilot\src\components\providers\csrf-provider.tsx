'use client';

import { createContext, useContext, useState, useEffect, ReactNode } from 'react';

interface CSRFContextType {
  csrfToken: string | null;
  isLoading: boolean;
  refreshToken: () => Promise<string | null>;
}

const CSRFContext = createContext<CSRFContextType>({
  csrfToken: null,
  isLoading: true,
  refreshToken: async () => null,
});

export const useCSRF = () => useContext(CSRFContext);

export function CSRFProvider({ children }: { children: ReactNode }) {
  const [csrfToken, setCsrfToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const fetchCSRFToken = async (): Promise<string | null> => {
    try {
      setIsLoading(true);

      // Implementar lógica de retry
      let attempts = 0;
      const maxAttempts = 3;

      while (attempts < maxAttempts) {
        try {
          attempts++;
          const response = await fetch('/api/csrf', {
            method: 'GET',
            credentials: 'include',
          });

          if (response.ok) {
            const data = await response.json();
            setCsrfToken(data.csrfToken);
            setIsLoading(false);
            return data.csrfToken;
          } else {
            console.warn(
              `Tentativa ${attempts}/${maxAttempts} falhou ao obter token CSRF: ${response.status}`
            );
            if (attempts < maxAttempts) {
              // Espera exponencial entre tentativas
              await new Promise(resolve => setTimeout(resolve, 1000 * Math.pow(2, attempts - 1)));
              continue;
            }
            // Se todas as tentativas falharem
            throw new Error(`Falha ao obter token CSRF: ${response.status}`);
          }
        } catch (error) {
          if (attempts >= maxAttempts) {
            throw error;
          }
          // Espera antes da próxima tentativa
          await new Promise(resolve => setTimeout(resolve, 1000 * Math.pow(2, attempts - 1)));
        }
      }

      throw new Error('Máximo de tentativas excedido');
    } catch (error) {
      console.error('Erro ao obter token CSRF:', error);
      setIsLoading(false);
      return null;
    }
  };

  useEffect(() => {
    fetchCSRFToken();
  }, []);

  return (
    <CSRFContext.Provider
      value={{
        csrfToken,
        isLoading,
        refreshToken: fetchCSRFToken,
      }}
    >
      {children}
    </CSRFContext.Provider>
  );
}

// Hook personalizado para adicionar o token CSRF a qualquer requisição
export function useFetchWithCSRF() {
  const { csrfToken, isLoading, refreshToken } = useCSRF();

  const fetchWithCSRF = async (url: string, options: RequestInit = {}) => {
    if (isLoading) {
      // Esperar carregamento do token
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    const headers = new Headers(options.headers || {});

    if (csrfToken) {
      headers.set('x-csrf-token', csrfToken);
    } else {
      // Se não tiver token, tentar obter novamente
      const newToken = await refreshToken();
      if (newToken) {
        headers.set('x-csrf-token', newToken);
      }
    }

    return fetch(url, {
      ...options,
      credentials: 'include',
      headers,
    });
  };

  return { fetchWithCSRF, csrfToken, isLoading };
}
