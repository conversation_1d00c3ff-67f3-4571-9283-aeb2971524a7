/**
 * Middleware para corrigir problemas de callback URL do NextAuth
 * Resolve o problema de codificação dupla de URLs
 */

import { NextRequest, NextResponse } from 'next/server';

export function fixAuthCallbackUrl(request: NextRequest) {
  const url = request.nextUrl.clone();

  // Verificar se é uma requisição para o OAuth do Google
  if (url.pathname.includes('/api/auth/signin/google')) {
    const callbackUrl = url.searchParams.get('callbackUrl');

    if (callbackUrl) {
      try {
        // Decodificar URL se estiver codificada duas vezes
        let decodedUrl = callbackUrl;

        // Se contém %25, significa que foi codificada duas vezes
        if (callbackUrl.includes('%25')) {
          decodedUrl = decodeURIComponent(callbackUrl);
          console.log('🔧 Corrigindo callback URL:', { original: callbackUrl, fixed: decodedUrl });

          // Atualizar o parâmetro com a URL corrigida
          url.searchParams.set('callbackUrl', decodedUrl);

          // Retornar redirect com URL corrigida
          return NextResponse.redirect(url);
        }
      } catch (error) {
        console.error('❌ Erro ao corrigir callback URL:', error);
      }
    }
  }

  return NextResponse.next();
}
