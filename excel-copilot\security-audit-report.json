{"timestamp": "2025-06-05T15:41:52.058Z", "overallRisk": "MÉDIO", "summary": {"critical": 0, "high": 2, "medium": 3, "recommendations": 4}, "vulnerabilities": [{"file": "src/lib/subscription-limits.ts", "type": "UNSAFE_FALLBACK", "severity": "HIGH", "description": "Fallback inseguro permite acesso em caso de erro"}, {"file": "src/lib/middleware/plan-based-rate-limiter.ts", "type": "UNSAFE_FALLBACK", "severity": "HIGH", "description": "Fallback inseguro permite acesso em caso de erro"}], "securityIssues": [{"file": "src/app/api/workbooks/route.ts", "type": "MISSING_RATE_LIMIT", "severity": "MEDIUM", "description": "Endpoint sem rate limiting"}, {"file": "src/app/api/chat/route.ts", "type": "MISSING_VALIDATION", "severity": "MEDIUM", "description": "Falta validação de entrada com schema"}, {"file": "src/lib/middleware/plan-based-rate-limiter.ts", "type": "CACHE_BYPASS_RISK", "severity": "MEDIUM", "description": "Cache de planos sem expiração adequada"}], "recommendations": [{"file": "src/app/api/chat/route.ts", "type": "MISSING_AUDIT_LOG", "severity": "LOW", "description": "Falta log de auditoria para ações de assinatura"}, {"file": "src/app/api/workbook/save/route.ts", "type": "MISSING_AUDIT_LOG", "severity": "LOW", "description": "Falta log de auditoria para ações de assinatura"}, {"file": "src/server/services/workbook-service.ts", "type": "MISSING_AUDIT_LOG", "severity": "LOW", "description": "Falta log de auditoria para ações de assinatura"}, {"file": "src/lib/middleware/plan-based-rate-limiter.ts", "type": "MISSING_AUDIT_LOG", "severity": "LOW", "description": "Falta log de auditoria para ações de assinatura"}]}