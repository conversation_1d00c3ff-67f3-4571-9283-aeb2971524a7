#!/usr/bin/env node

/**
 * Script para testar o fluxo OAuth em tempo real
 * Monitora os endpoints e verifica se tudo está funcionando
 */

const https = require('https');
const http = require('http');

const BASE_URL = 'https://excel-copilot-eight.vercel.app';

// Cores para output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m',
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function makeRequest(url, method = 'GET') {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const options = {
      hostname: urlObj.hostname,
      port: urlObj.port || (urlObj.protocol === 'https:' ? 443 : 80),
      path: urlObj.pathname + urlObj.search,
      method: method,
      headers: {
        'User-Agent': 'OAuth-Test-Script/1.0',
        Accept: 'application/json',
      },
    };

    const client = urlObj.protocol === 'https:' ? https : http;

    const req = client.request(options, res => {
      let data = '';

      res.on('data', chunk => {
        data += chunk;
      });

      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: jsonData,
          });
        } catch (e) {
          resolve({
            status: res.statusCode,
            headers: res.headers,
            data: data,
          });
        }
      });
    });

    req.on('error', err => {
      reject(err);
    });

    req.setTimeout(10000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    req.end();
  });
}

async function testEndpoint(name, url, expectedStatus = 200) {
  try {
    log(`\n🔍 Testando ${name}...`, 'blue');
    log(`   URL: ${url}`, 'yellow');

    const result = await makeRequest(url);

    if (result.status === expectedStatus) {
      log(`   ✅ Status: ${result.status} (OK)`, 'green');
    } else {
      log(`   ❌ Status: ${result.status} (Esperado: ${expectedStatus})`, 'red');
    }

    // Mostrar dados relevantes
    if (typeof result.data === 'object') {
      if (result.data.status) {
        log(
          `   📊 Status: ${result.data.status}`,
          result.data.status === 'success' ? 'green' : 'red'
        );
      }
      if (result.data.message) {
        log(`   💬 Mensagem: ${result.data.message}`, 'yellow');
      }
      if (result.data.missingVariables && result.data.missingVariables.length > 0) {
        log(`   ⚠️  Variáveis ausentes: ${result.data.missingVariables.join(', ')}`, 'red');
      }
      if (result.data.recommendations) {
        log(`   💡 Recomendações:`, 'blue');
        result.data.recommendations.forEach(rec => {
          log(`      - ${rec}`, 'yellow');
        });
      }
    }

    return result;
  } catch (error) {
    log(`   🚨 Erro: ${error.message}`, 'red');
    return null;
  }
}

async function testOAuthFlow() {
  log('🚀 Iniciando teste do fluxo OAuth...', 'bold');
  log('=' * 50, 'blue');

  // 1. Testar configuração OAuth
  await testEndpoint('Configuração OAuth', `${BASE_URL}/api/auth/debug-oauth`);

  // 2. Testar debug do fluxo
  await testEndpoint('Debug do Fluxo', `${BASE_URL}/api/auth/debug-flow`);

  // 3. Testar endpoints de autenticação
  await testEndpoint('Sessão Atual', `${BASE_URL}/api/auth/session`);

  await testEndpoint('CSRF Token', `${BASE_URL}/api/csrf`);

  // 4. Testar redirecionamento OAuth (deve retornar 302)
  await testEndpoint('Redirecionamento Google OAuth', `${BASE_URL}/api/auth/signin/google`, 302);

  await testEndpoint('Redirecionamento GitHub OAuth', `${BASE_URL}/api/auth/signin/github`, 302);

  // 5. Testar páginas de autenticação
  await testEndpoint('Página de Login', `${BASE_URL}/auth/signin`);

  log('\n' + '=' * 50, 'blue');
  log('🏁 Teste concluído!', 'bold');

  // Instruções finais
  log('\n📋 Próximos passos:', 'blue');
  log('1. Verifique se todos os endpoints retornaram status OK', 'yellow');
  log('2. Se houver variáveis ausentes, configure-as na Vercel', 'yellow');
  log('3. Teste manualmente o login em: ' + BASE_URL + '/auth/signin', 'yellow');
  log('4. Verifique os logs da Vercel durante o teste manual', 'yellow');

  log('\n🔧 Debug adicional:', 'blue');
  log('- Debug OAuth: ' + BASE_URL + '/api/auth/debug-oauth', 'yellow');
  log('- Debug Fluxo: ' + BASE_URL + '/api/auth/debug-flow', 'yellow');
  log('- Logs Vercel: https://vercel.com/dashboard → Projeto → Functions', 'yellow');
}

// Executar o teste
if (require.main === module) {
  testOAuthFlow().catch(error => {
    log(`\n🚨 Erro fatal: ${error.message}`, 'red');
    process.exit(1);
  });
}

module.exports = { testOAuthFlow, testEndpoint };
