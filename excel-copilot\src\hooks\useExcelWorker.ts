import { nanoid } from 'nanoid';
import { useState, useEffect, useRef, useCallback } from 'react';

// Worker config simplificado (substituindo arquivo removido)
const WORKER_CONFIG = {
  timeout: 30000,
  maxRetries: 3,
  VERBOSE_LOGGING: process.env.NODE_ENV === 'development'
};

const createWorkerSafely = (url: URL, options?: WorkerOptions) => {
  try {
    return new Worker(url, options);
  } catch (error) {
    console.warn('Worker não pôde ser criado:', error);
    return null;
  }
};
import { ExcelOperation } from '@/types';

// Tipo para o resultado da operação Excel
interface ExcelOperationResult {
  updatedData: unknown;
  resultSummary: string;
  modifiedCells?: Array<{ row: number; col: number }>;
}

// Interface para o hook
interface UseExcelWorkerOptions {
  onSuccess?: (result: ExcelOperationResult, requestId: string) => void;
  onError?: (error: Error, requestId: string) => void;
}

export interface WorkerCallbacks {
  onMessage: (data: ExcelWorkerMessage) => void;
  onError: (error: ErrorEvent) => void;
}

export interface WorkerRequestOptions {
  timeout?: number;
  priority?: 'high' | 'normal' | 'low';
  abortSignal?: AbortSignal;
}

// Interface removida pois não é utilizada
// interface ExcelWorkerRequest {
//   operation: ExcelOperation;
//   sheetData: unknown;
//   requestId: string;
//   operationType: string;
// }

interface ExcelWorkerResponse {
  result?: ExcelOperationResult;
  error?: string;
  requestId: string;
}

interface ExcelWorkerMessage {
  data: ExcelWorkerResponse;
}

/**
 * Hook para execução de operações Excel em worker thread separada
 */
export function useExcelWorker(options: UseExcelWorkerOptions = {}) {
  // Referência para o worker
  const workerRef = useRef<Worker | null>(null);

  // Estado para tracking de operações pendentes
  const [isProcessing, setIsProcessing] = useState(false);
  const pendingOperationsRef = useRef<
    Map<
      string,
      {
        operation: ExcelOperation;
        resolve: (value: ExcelOperationResult | PromiseLike<ExcelOperationResult>) => void;
        reject: (reason?: unknown) => void;
      }
    >
  >(new Map());

  // Inicializar worker
  useEffect(() => {
    // Função para inicializar/reinicializar o worker
    function initializeWorker() {
      try {
        // Limpar worker existente
        if (workerRef.current) {
          workerRef.current.terminate();
        }

        // Criar novo worker
        workerRef.current = new Worker(
          new URL('../workers/excel-operations.worker.ts', import.meta.url),
          { type: 'module' }
        );

        // Configurar handler de mensagens
        workerRef.current.onmessage = event => {
          const { result, error, requestId } = event.data;

          // Buscar operação pendente
          const pendingOp = pendingOperationsRef.current.get(requestId);
          if (pendingOp) {
            // Remover da lista de pendentes
            pendingOperationsRef.current.delete(requestId);

            // Verificar se ainda há operações pendentes
            if (pendingOperationsRef.current.size === 0) {
              setIsProcessing(false);
            }

            // Resolver ou rejeitar promessa
            if (error) {
              const errorObj = new Error(error);
              pendingOp.reject(errorObj);
              options.onError?.(errorObj, requestId);
            } else {
              pendingOp.resolve(result);
              options.onSuccess?.(result, requestId);
            }
          }
        };

        // Configurar handler de erros
        workerRef.current.onerror = error => {
          console.error('Erro no worker Excel:', error);

          // Rejeitar todas as operações pendentes
          pendingOperationsRef.current.forEach((pendingOp, requestId) => {
            const errorObj = new Error('Erro fatal no worker Excel');
            pendingOp.reject(errorObj);
            options.onError?.(errorObj, requestId);
          });

          pendingOperationsRef.current.clear();
          setIsProcessing(false);

          // Tentar reiniciar o worker
          initializeWorker();
        };
      } catch (error) {
        console.error('Erro ao reinicializar worker Excel:', error);
      }
    }

    // Tentar criar worker usando utilitário seguro
    if (typeof window !== 'undefined') {
      try {
        workerRef.current = createWorkerSafely(
          new URL('../workers/excel-operations.worker.ts', import.meta.url),
          { type: 'module' }
        );

        if (!workerRef.current) {
          if (WORKER_CONFIG.VERBOSE_LOGGING) {
            console.info('Excel Worker não disponível, usando fallback na thread principal');
          }
          return;
        }

        if (WORKER_CONFIG.VERBOSE_LOGGING) {
          console.info('Excel Worker inicializado com sucesso');
        }

        // Configurar handlers usando a função
        initializeWorker();
      } catch (error) {
        console.warn('Workers não suportados ou bloqueados pela CSP, usando fallback:', error);
        // Worker não disponível - operações serão executadas na thread principal
        workerRef.current = null;
      }
    } else {
      console.info('Workers não disponíveis neste ambiente, usando fallback');
      workerRef.current = null;
    }

    // Limpeza ao desmontar
    return () => {
      if (workerRef.current) {
        workerRef.current.terminate();
        workerRef.current = null;
      }
    };
  }, [options]);

  /**
   * Executa uma operação Excel no worker
   */
  const executeOperation = useCallback(
    async (operation: ExcelOperation, sheetData: unknown): Promise<ExcelOperationResult> => {
      // Se worker não está disponível, usar fallback (execução na thread principal)
      if (!workerRef.current) {
        console.info('Executando operação Excel na thread principal (fallback)');

        // Simular o comportamento do worker na thread principal
        try {
          setIsProcessing(true);

          // Aqui você implementaria a lógica de fallback
          // Por enquanto, retornar um resultado mock
          const result: ExcelOperationResult = {
            updatedData: sheetData,
            resultSummary: `Operação ${operation.type} executada (fallback)`,
            modifiedCells: [],
          };

          setIsProcessing(false);
          return result;
        } catch (error) {
          setIsProcessing(false);
          throw error;
        }
      }

      // Gerar ID único para esta requisição
      const requestId = nanoid();
      setIsProcessing(true);

      // Criar promessa para aguardar resultado
      return new Promise<ExcelOperationResult>((resolve, reject) => {
        // Armazenar referência à operação para resolver mais tarde
        pendingOperationsRef.current.set(requestId, {
          operation,
          resolve,
          reject,
        });

        // Enviar mensagem para o worker
        workerRef.current!.postMessage({
          operation,
          sheetData,
          requestId,
          operationType: operation.type,
        });
      });
    },
    []
  );

  /**
   * Cancela todas as operações pendentes
   */
  const cancelAllOperations = useCallback(() => {
    pendingOperationsRef.current.forEach((pendingOp, _requestId) => {
      pendingOp.reject(new Error('Operação cancelada'));
    });

    pendingOperationsRef.current.clear();
    setIsProcessing(false);
  }, []);

  return {
    executeOperation,
    isProcessing,
    cancelAllOperations,
  };
}
