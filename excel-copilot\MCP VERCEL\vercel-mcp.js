#!/usr/bin/env node

/**
 * Vercel MCP Integration
 * Script para interagir com a API do Vercel
 */

// eslint-disable-next-line @typescript-eslint/no-require-imports
require('dotenv').config();

const VERCEL_API_TOKEN = process.env.VERCEL_API_TOKEN;
const VERCEL_PROJECT_ID = process.env.VERCEL_PROJECT_ID;
const VERCEL_TEAM_ID = process.env.VERCEL_TEAM_ID;

const BASE_URL = 'https://api.vercel.com';

class VercelMCP {
  constructor() {
    this.headers = {
      Authorization: `Bearer ${VERCEL_API_TOKEN}`,
      'Content-Type': 'application/json',
    };
  }

  async makeRequest(endpoint, method = 'GET', body = null) {
    const url = `${BASE_URL}${endpoint}`;
    const options = {
      method,
      headers: this.headers,
    };

    if (body) {
      options.body = JSON.stringify(body);
    }

    try {
      const response = await fetch(url, options);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(`Vercel API Error: ${data.error?.message || response.statusText}`);
      }

      return data;
    } catch (error) {
      console.error('Erro na requisição:', error.message);
      throw error;
    }
  }

  // Listar projetos
  async listProjects() {
    return await this.makeRequest(`/v9/projects?teamId=${VERCEL_TEAM_ID}`);
  }

  // Obter detalhes do projeto
  async getProject(projectId = VERCEL_PROJECT_ID) {
    return await this.makeRequest(`/v9/projects/${projectId}?teamId=${VERCEL_TEAM_ID}`);
  }

  // Listar deployments
  async listDeployments(projectId = VERCEL_PROJECT_ID) {
    return await this.makeRequest(
      `/v6/deployments?projectId=${projectId}&teamId=${VERCEL_TEAM_ID}`
    );
  }

  // Obter deployment específico
  async getDeployment(deploymentId) {
    return await this.makeRequest(`/v13/deployments/${deploymentId}?teamId=${VERCEL_TEAM_ID}`);
  }

  // Listar variáveis de ambiente
  async listEnvVars(projectId = VERCEL_PROJECT_ID) {
    return await this.makeRequest(`/v9/projects/${projectId}/env?teamId=${VERCEL_TEAM_ID}`);
  }

  // Criar variável de ambiente
  async createEnvVar(
    key,
    value,
    target = ['production', 'preview', 'development'],
    projectId = VERCEL_PROJECT_ID
  ) {
    const body = {
      key,
      value,
      target,
      type: 'encrypted',
    };
    return await this.makeRequest(
      `/v9/projects/${projectId}/env?teamId=${VERCEL_TEAM_ID}`,
      'POST',
      body
    );
  }

  // Deletar variável de ambiente
  async deleteEnvVar(envId, projectId = VERCEL_PROJECT_ID) {
    return await this.makeRequest(
      `/v9/projects/${projectId}/env/${envId}?teamId=${VERCEL_TEAM_ID}`,
      'DELETE'
    );
  }

  // Obter logs de deployment
  async getDeploymentLogs(deploymentId) {
    return await this.makeRequest(
      `/v2/deployments/${deploymentId}/events?teamId=${VERCEL_TEAM_ID}`
    );
  }

  // Listar domínios
  async listDomains() {
    return await this.makeRequest(`/v5/domains?teamId=${VERCEL_TEAM_ID}`);
  }

  // Obter informações da equipe
  async getTeam() {
    return await this.makeRequest(`/v2/teams/${VERCEL_TEAM_ID}`);
  }

  // Corrigir variável de ambiente malformada
  async fixMalformedEnvVar(envId, key, value, target = ['production']) {
    // Primeiro deletar a variável problemática
    await this.deleteEnvVar(envId);

    // Depois criar uma nova com o valor correto
    return await this.createEnvVar(key, value, target);
  }
}

// Função principal para CLI
async function main() {
  const vercel = new VercelMCP();
  const command = process.argv[2];

  try {
    switch (command) {
      case 'projects': {
        const projects = await vercel.listProjects();
        // eslint-disable-next-line no-console
        console.log('📁 Projetos:', JSON.stringify(projects, null, 2));
        break;
      }

      case 'project': {
        const project = await vercel.getProject();
        // eslint-disable-next-line no-console
        console.log('🏗️ Projeto atual:', JSON.stringify(project, null, 2));
        break;
      }

      case 'deployments': {
        const deployments = await vercel.listDeployments();
        // eslint-disable-next-line no-console
        console.log('🚀 Deployments:', JSON.stringify(deployments, null, 2));
        break;
      }

      case 'env': {
        const envVars = await vercel.listEnvVars();
        // eslint-disable-next-line no-console
        console.log('🔐 Variáveis de ambiente:', JSON.stringify(envVars, null, 2));
        break;
      }

      case 'domains': {
        const domains = await vercel.listDomains();
        // eslint-disable-next-line no-console
        console.log('🌐 Domínios:', JSON.stringify(domains, null, 2));
        break;
      }

      case 'team': {
        const team = await vercel.getTeam();
        // eslint-disable-next-line no-console
        console.log('👥 Equipe:', JSON.stringify(team, null, 2));
        break;
      }

      case 'fix-url': {
        // eslint-disable-next-line no-console
        console.log('🔧 Corrigindo variáveis de URL malformadas...');

        const correctUrl = 'https://excel-copilot-eight.vercel.app';

        try {
          // Corrigir APP_URL para produção
          // eslint-disable-next-line no-console
          console.log('🔧 Corrigindo APP_URL...');
          const appUrlId = 'CnLlrDJlqoZpEUH8';
          await vercel.fixMalformedEnvVar(appUrlId, 'APP_URL', correctUrl, ['production']);
          // eslint-disable-next-line no-console
          console.log('✅ APP_URL corrigida!');

          // Corrigir NEXTAUTH_URL para produção
          // eslint-disable-next-line no-console
          console.log('🔧 Corrigindo NEXTAUTH_URL...');
          const nextAuthUrlId = '3aAkcLrWv3pUa1gQ';
          await vercel.fixMalformedEnvVar(nextAuthUrlId, 'NEXTAUTH_URL', correctUrl, [
            'production',
          ]);
          // eslint-disable-next-line no-console
          console.log('✅ NEXTAUTH_URL corrigida!');

          // eslint-disable-next-line no-console
          console.log('✅ Todas as variáveis de URL foram corrigidas!');
        } catch (error) {
          // eslint-disable-next-line no-console
          console.log('❌ Erro ao corrigir variáveis:', error.message);
        }
        break;
      }

      case 'test': {
        // eslint-disable-next-line no-console
        console.log('🧪 Testando conexão com Vercel...');
        const testProject = await vercel.getProject();
        // eslint-disable-next-line no-console
        console.log('✅ Conexão bem-sucedida!');
        // eslint-disable-next-line no-console
        console.log(`📋 Projeto: ${testProject.name}`);
        // eslint-disable-next-line no-console
        console.log(`🔗 URL: https://${testProject.alias?.[0]?.domain || 'N/A'}`);
        break;
      }

      default:
        // eslint-disable-next-line no-console
        console.log(`
🚀 Vercel MCP Integration

Comandos disponíveis:
  node vercel-mcp.js test        - Testar conexão
  node vercel-mcp.js projects    - Listar projetos
  node vercel-mcp.js project     - Detalhes do projeto atual
  node vercel-mcp.js deployments - Listar deployments
  node vercel-mcp.js env         - Listar variáveis de ambiente
  node vercel-mcp.js domains     - Listar domínios
  node vercel-mcp.js team        - Informações da equipe
                `);
    }
  } catch (error) {
    console.error('❌ Erro:', error.message);
    process.exit(1);
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  main();
}

module.exports = VercelMCP;
