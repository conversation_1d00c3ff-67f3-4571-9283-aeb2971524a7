import { NextRequest, NextResponse } from 'next/server';

import { logger } from '@/lib/logger';

/**
 * Tipo para handler de middleware
 */
export type MiddlewareHandler = (
  req: NextRequest,
  res: NextResponse,
  context: Record<string, any>
) => Promise<NextResponse | void>;

/**
 * Cria uma cadeia de middleware que será executada em sequência
 * @param handlers Array de funções de middleware para executar
 * @returns Função que executa a cadeia de middleware
 */
export function createMiddlewareChain(...handlers: MiddlewareHandler[]) {
  return async (
    req: NextRequest,
    res: NextResponse = new NextResponse(),
    context: Record<string, any> = {}
  ) => {
    try {
      for (const handler of handlers) {
        const result = await handler(req, res, context);
        if (result instanceof NextResponse) {
          return result; // Middleware interrompeu a cadeia com uma resposta
        }
      }
      return { res, context };
    } catch (error) {
      logger.error('Erro na cadeia de middleware', error);
      return NextResponse.json(
        {
          code: 'MIDDLEWARE_ERROR',
          message: 'Erro interno no servidor',
          timestamp: new Date().toISOString(),
        },
        { status: 500 }
      );
    }
  };
}

/**
 * Wrapper para simplificar o uso do middleware com handlers de API
 * @param middlewares Array de middlewares a serem executados antes do handler
 * @param handler Handler da API que será executado após os middlewares
 * @returns Handler combinado com middleware
 */
export function withMiddleware(
  middlewares: MiddlewareHandler[],
  handler: (req: NextRequest, context: Record<string, any>) => Promise<NextResponse>
) {
  const middlewareChain = createMiddlewareChain(...middlewares);

  return async (req: NextRequest) => {
    const res = new NextResponse();
    const context: Record<string, any> = {};

    // Executar cadeia de middleware
    const middlewareResult = await middlewareChain(req, res, context);

    // Se middleware já respondeu, retornar essa resposta
    if (middlewareResult instanceof NextResponse) {
      return middlewareResult;
    }

    // Caso contrário, executar o handler com o contexto do middleware
    try {
      // middlewareResult agora contém { res, context } se não houve erro
      const finalContext =
        middlewareResult && typeof middlewareResult === 'object' && 'context' in middlewareResult
          ? { ...context, ...middlewareResult.context } // ✅ Merge contexts para preservar dados
          : context;

      console.log('🔧 withMiddleware: Context final:', {
        hasUserId: !!finalContext.userId,
        keys: Object.keys(finalContext),
      });

      return await handler(req, finalContext);
    } catch (error) {
      logger.error('Erro no handler de API', error);
      return NextResponse.json(
        {
          code: 'HANDLER_ERROR',
          message: error instanceof Error ? error.message : 'Erro interno no servidor',
          timestamp: new Date().toISOString(),
        },
        { status: 500 }
      );
    }
  };
}
