'use client';

import { FileSpreadsheet, Sparkles, CheckCircle2, User2 } from 'lucide-react';

import { cn } from '@/lib/utils';

import { ChatMessageProps } from './types';

export function ChatMessage({
  message,
  isLast = false,
  isProcessing = false,
  isExcelConnected = false,
}: ChatMessageProps) {
  // Variáveis prefixadas com underscore para indicar que são intencionalmente não utilizadas
  // Podemos remover esses valores ou mantê-los se forem potencialmente úteis no futuro
  // const _isUser = message.role === 'user';
  // const _messageId = useId();
  // const _authorId = useId();
  // const _timestamp = message.createdAt
  //   ? new Date(message.createdAt).toLocaleTimeString()
  //   : new Date().toLocaleTimeString();

  // Determinar estilos com base no status de conexão
  const getBubbleStyle = () => {
    const baseStyles = cn(
      'rounded-lg p-3 max-w-full',
      message.role === 'user' ? 'bg-primary text-primary-foreground' : 'bg-muted'
    );

    // Se for assistente e Excel estiver conectado, adicionar estilo diferenciado
    if (message.role === 'assistant' && isExcelConnected) {
      return cn(baseStyles, 'border-l-4 border-green-500');
    }

    return baseStyles;
  };

  return (
    <div className={cn('flex flex-col', message.role === 'user' ? 'items-end' : 'items-start')}>
      <div className="flex items-start gap-2 max-w-[80%]">
        {message.role === 'assistant' && (
          <div className="w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center flex-shrink-0 mt-1">
            {isExcelConnected ? (
              <FileSpreadsheet className="h-3.5 w-3.5 text-green-600" />
            ) : (
              <Sparkles className="h-3.5 w-3.5 text-blue-600" />
            )}
          </div>
        )}

        <div className={getBubbleStyle()}>
          {/* Conteúdo */}
          <div className="whitespace-pre-wrap">{message.content}</div>

          {/* Indicador de operações executadas no Excel */}
          {message.role === 'assistant' && message.operationsExecuted && isExcelConnected && (
            <div className="mt-2 pt-2 border-t border-border text-xs text-green-600 flex items-center gap-1.5">
              <CheckCircle2 className="h-3.5 w-3.5" />
              <span>Executado no Excel</span>
            </div>
          )}
        </div>

        {message.role === 'user' && (
          <div className="w-6 h-6 rounded-full bg-primary flex items-center justify-center flex-shrink-0 mt-1">
            <User2 className="h-3.5 w-3.5 text-primary-foreground" />
          </div>
        )}
      </div>

      {/* Status indicator */}
      {isLast && message.role === 'assistant' && isProcessing && (
        <div className="mt-1 ml-8 text-xs text-muted-foreground animate-pulse">Processando...</div>
      )}
    </div>
  );
}
