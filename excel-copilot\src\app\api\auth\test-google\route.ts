import { NextResponse } from 'next/server';

export const dynamic = 'force-dynamic';

/**
 * Endpoint para testar configuração do Google OAuth
 * Verifica se todas as variáveis estão configuradas corretamente
 */
export async function GET() {
  try {
    // Verificar variáveis de ambiente
    const googleClientId = process.env.AUTH_GOOGLE_CLIENT_ID;
    const googleClientSecret = process.env.AUTH_GOOGLE_CLIENT_SECRET;
    const nextAuthUrl = process.env.AUTH_NEXTAUTH_URL;
    const nextAuthSecret = process.env.AUTH_NEXTAUTH_SECRET;

    // Construir URLs de callback
    const callbackUrl = `${nextAuthUrl}/api/auth/callback/google`;
    const signinUrl = `${nextAuthUrl}/api/auth/signin/google`;

    // Verificar configuração
    const config = {
      environment: process.env.NODE_ENV,
      timestamp: new Date().toISOString(),
      variables: {
        GOOGLE_CLIENT_ID: googleClientId ? `${googleClientId.substring(0, 10)}...` : 'MISSING',
        GOOGLE_CLIENT_SECRET: googleClientSecret ? 'CONFIGURED' : 'MISSING',
        NEXTAUTH_URL: nextAuthUrl || 'MISSING',
        NEXTAUTH_SECRET: nextAuthSecret ? 'CONFIGURED' : 'MISSING',
      },
      urls: {
        callback: callbackUrl,
        signin: signinUrl,
        base: nextAuthUrl,
      },
      validation: {
        hasGoogleClientId: !!googleClientId,
        hasGoogleClientSecret: !!googleClientSecret,
        hasNextAuthUrl: !!nextAuthUrl,
        hasNextAuthSecret: !!nextAuthSecret,
        allConfigured: !!(googleClientId && googleClientSecret && nextAuthUrl && nextAuthSecret),
      },
    };

    // Verificar se todas as configurações estão presentes
    if (!config.validation.allConfigured) {
      return NextResponse.json(
        {
          status: 'error',
          message: 'Configuração OAuth incompleta',
          config,
          recommendations: [
            'Verifique se todas as variáveis de ambiente estão configuradas no Vercel',
            'GOOGLE_CLIENT_ID deve ser obtido do Google Cloud Console',
            'GOOGLE_CLIENT_SECRET deve ser obtido do Google Cloud Console',
            'NEXTAUTH_URL deve ser a URL base da aplicação',
            'NEXTAUTH_SECRET deve ser uma string aleatória segura',
          ],
        },
        { status: 500 }
      );
    }

    // Testar construção da URL de autorização do Google
    const authUrl = new URL('https://accounts.google.com/oauth/authorize');
    authUrl.searchParams.set('client_id', googleClientId!);
    authUrl.searchParams.set('redirect_uri', callbackUrl);
    authUrl.searchParams.set('response_type', 'code');
    authUrl.searchParams.set('scope', 'openid email profile');
    authUrl.searchParams.set('state', 'test-state');

    return NextResponse.json({
      status: 'success',
      message: 'Configuração OAuth válida',
      config,
      testUrls: {
        googleAuth: authUrl.toString(),
        nextAuthSignin: signinUrl,
        nextAuthCallback: callbackUrl,
      },
      instructions: [
        '1. Verifique se as URLs de callback estão configuradas no Google Cloud Console',
        '2. Teste o login acessando a URL de signin',
        '3. Verifique os logs do Vercel para erros detalhados',
      ],
    });
  } catch (error) {
    return NextResponse.json(
      {
        status: 'error',
        message: 'Erro ao verificar configuração OAuth',
        error: error instanceof Error ? error.message : 'Erro desconhecido',
      },
      { status: 500 }
    );
  }
}
