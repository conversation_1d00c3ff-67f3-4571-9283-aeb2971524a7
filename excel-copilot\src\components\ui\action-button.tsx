/**
 * Componente base para botões de ação com estados de carregamento
 */
'use client';

import { Loader2 } from 'lucide-react';
import { useState, ReactNode } from 'react';

import { Button } from '@/components/ui/button';

export interface ActionButtonProps {
  onClick: () => Promise<void>;
  icon: ReactNode;
  loadingText: string;
  text: string;
  disabled?: boolean;
  variant?: 'default' | 'outline' | 'ghost';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  title?: string;
  className?: string;
}

/**
 * Botão de ação reutilizável com estado de carregamento
 */
export function ActionButton({
  onClick,
  icon,
  loadingText,
  text,
  disabled = false,
  variant = 'outline',
  size = 'sm',
  title,
  className = '',
}: ActionButtonProps) {
  const [isLoading, setIsLoading] = useState(false);

  const handleClick = async () => {
    if (isLoading || disabled) return;

    setIsLoading(true);
    try {
      await onClick();
    } catch (error) {
      console.error('Erro ao executar ação:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Button
      variant={variant}
      size={size}
      onClick={() => handleClick()}
      disabled={disabled || isLoading}
      title={title}
      className={className}
    >
      {isLoading ? (
        <>
          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
          {loadingText}
        </>
      ) : (
        <>
          {icon}
          {text}
        </>
      )}
    </Button>
  );
}
