import { extractGroup } from '@/utils/regex-utils';

import { ExcelOperationType } from '../../types/index';
import { ExcelOperation } from '../excel/types';

/**
 * Extrai operações de filtro do texto da resposta da IA
 * @param response Resposta da IA
 * @returns Array de operações de filtro
 */
export function extractFilterOperations(response: string): ExcelOperation[] {
  const operations: ExcelOperation[] = [];

  // Verificar padrões de filtros estruturados
  const filterPattern =
    /OPERAÇÃO:\s*FILTRO[\s\S]*?COLUNA:\s*([^\n]+)[\s\S]*?CONDIÇÃO:\s*([^\n]+)(?:[\s\S]*?VALOR:\s*([^\n]+))?/gi;

  let match;
  while ((match = filterPattern.exec(response)) !== null) {
    const column = match[1]?.trim();
    const condition = match[2]?.trim();
    const value = match[3]?.trim();

    if (column && condition) {
      // Mapear condição para operador padrão
      const { operator, formattedValue } = parseFilterCondition(condition, value);

      const operation: ExcelOperation = {
        type: ExcelOperationType.FILTER,
        data: {
          column,
          condition: {
            operator,
            value: formattedValue,
          },
        },
      };

      operations.push(operation);
    }
  }

  // Também verificar padrões em linguagem natural
  const languagePatterns = [
    // "Filtrar a coluna A onde valor > 50"
    /filtrar\s+(?:a\s+)?coluna\s+([A-Za-z0-9_]+)\s+(?:onde|com|para|que|cujo)\s+(?:valores?|dados?|conteúdo)?\s*([<>=!]+|contém|contem|contendo|começa|comeca|termina|igual a|maior que|menor que|diferente de)\s*(.+)(?:$|[,;\s])/gi,

    // "Mostre apenas valores maiores que 100 na coluna B"
    /(?:mostrar?|exiba|exibir)\s+(?:apenas|somente)?\s+(?:os\s+)?(?:dados|valores|linhas|registros)\s+(?:onde|com|que|cujos?)?\s+([<>=!]+|contém|contem|contendo|começa|comeca|termina|igual a|maior que|menor que|diferente de)\s*([^,;\s]+)(?:\s+(?:n[ao]|d[ao])\s+coluna\s+([A-Za-z0-9_]+))?/gi,
  ];

  // Processar padrões em linguagem natural
  for (const pattern of languagePatterns) {
    let match;
    while ((match = pattern.exec(response)) !== null) {
      let column, condition, value;

      // Extrair valores dependendo do padrão
      if (match[3] && match[3].match(/[A-Za-z0-9_]+/)) {
        // Padrão 1: coluna, condição, valor
        column = match[1];
        condition = match[2];
        value = match[3];
      } else if (match[1] && match[2]) {
        // Padrão 2: condição, valor, coluna
        condition = match[1];
        value = match[2];
        column = match[3] || 'A'; // Default para coluna A se não especificada
      }

      if (column && condition && value) {
        // Mapear condição para operador padrão
        const { operator, formattedValue } = parseFilterCondition(condition, value);

        const operation: ExcelOperation = {
          type: ExcelOperationType.FILTER,
          data: {
            column,
            condition: {
              operator,
              value: formattedValue,
            },
          },
        };

        operations.push(operation);
      }
    }
  }

  return operations;
}

/**
 * Parseia uma condição de filtro em um operador e valor formatado
 * @param condition Condição em texto
 * @param value Valor a ser filtrado
 * @returns Operador e valor formatado
 */
function parseFilterCondition(
  condition: string,
  value?: string
): { operator: string; formattedValue: any } {
  const condLower = condition.toLowerCase().trim();
  let actualValue = value?.trim() || '';

  // Se a condição já contém um valor, extraí-lo
  if (!actualValue && condLower.includes(' ')) {
    const parts = condLower.split(/\s+/);
    if (parts.length >= 2) {
      actualValue = parts.slice(1).join(' ');
    }
  }

  // Mapear condições textuais para operadores
  if (condLower === '>' || condLower.includes('maior') || condLower.includes('acima')) {
    return { operator: 'greaterThan', formattedValue: parseValueToType(actualValue) };
  }

  if (condLower === '>=' || condLower.includes('maior ou igual')) {
    return { operator: 'greaterThanOrEqual', formattedValue: parseValueToType(actualValue) };
  }

  if (condLower === '<' || condLower.includes('menor') || condLower.includes('abaixo')) {
    return { operator: 'lessThan', formattedValue: parseValueToType(actualValue) };
  }

  if (condLower === '<=' || condLower.includes('menor ou igual')) {
    return { operator: 'lessThanOrEqual', formattedValue: parseValueToType(actualValue) };
  }

  if (condLower === '=' || condLower === '==' || condLower.includes('igual')) {
    return { operator: 'equals', formattedValue: parseValueToType(actualValue) };
  }

  if (condLower === '!=' || condLower === '<>' || condLower.includes('diferente')) {
    return { operator: 'notEquals', formattedValue: parseValueToType(actualValue) };
  }

  if (
    condLower.includes('contém') ||
    condLower.includes('contem') ||
    condLower.includes('contendo')
  ) {
    return { operator: 'contains', formattedValue: actualValue };
  }

  if (
    condLower.includes('começa') ||
    condLower.includes('comeca') ||
    condLower.includes('inicia')
  ) {
    return { operator: 'startsWith', formattedValue: actualValue };
  }

  if (condLower.includes('termina') || condLower.includes('acaba')) {
    return { operator: 'endsWith', formattedValue: actualValue };
  }

  // Se não conseguirmos identificar o operador, usar equals como padrão
  return { operator: 'equals', formattedValue: parseValueToType(actualValue) };
}

/**
 * Tenta converter um valor string para o tipo apropriado (número, booleano, etc)
 * @param value Valor em string
 * @returns Valor convertido para o tipo adequado
 */
function parseValueToType(value: string): any {
  if (!value) return '';

  // Verificar se é número
  const numValue = Number(value.replace(',', '.'));
  if (!isNaN(numValue)) {
    return numValue;
  }

  // Verificar se é booleano
  if (['true', 'verdadeiro', 'sim', 'yes'].includes(value.toLowerCase())) {
    return true;
  }

  if (['false', 'falso', 'não', 'nao', 'no'].includes(value.toLowerCase())) {
    return false;
  }

  // Se não for número nem booleano, retornar string
  return value;
}

/**
 * Executa uma operação de filtro em dados da planilha
 * @param sheetData Dados da planilha
 * @param operation Operação de filtro a ser executada
 * @returns Dados atualizados e resumo da operação
 */
export async function executeFilterOperation(
  sheetData: any,
  operation: ExcelOperation
): Promise<{ updatedData: any; resultSummary: string }> {
  try {
    const { column, condition } = operation.data;

    if (!column || !condition || !condition.operator) {
      throw new Error('Parâmetros insuficientes para operação de filtro');
    }

    // Clonar dados para não modificar o original diretamente
    const updatedData = { ...sheetData };

    // Determinar o índice da coluna (letra ou nome)
    let columnIndex: number;

    if (/^[A-Z]+$/.test(column)) {
      // Converter letra de coluna para índice numérico (0-based)
      columnIndex = convertColumnLetterToIndex(column);
    } else {
      // Procurar o nome da coluna nos cabeçalhos
      columnIndex = updatedData.headers.findIndex(
        (header: string) => header.toLowerCase() === column.toLowerCase()
      );
    }

    if (columnIndex === -1) {
      throw new Error(`Coluna "${column}" não encontrada`);
    }

    // Aplicar o filtro em uma cópia dos dados
    const { operator, value } = condition;
    const filteredRows = updatedData.rows.filter((row: any[]) => {
      const cellValue = row[columnIndex];

      switch (operator) {
        case 'equals':
          return cellValue == value;
        case 'notEquals':
          return cellValue != value;
        case 'greaterThan':
          return Number(cellValue) > Number(value);
        case 'lessThan':
          return Number(cellValue) < Number(value);
        case 'greaterThanOrEqual':
          return Number(cellValue) >= Number(value);
        case 'lessThanOrEqual':
          return Number(cellValue) <= Number(value);
        case 'contains':
          return String(cellValue).toLowerCase().includes(String(value).toLowerCase());
        case 'notContains':
          return !String(cellValue).toLowerCase().includes(String(value).toLowerCase());
        case 'startsWith':
          return String(cellValue).toLowerCase().startsWith(String(value).toLowerCase());
        case 'endsWith':
          return String(cellValue).toLowerCase().endsWith(String(value).toLowerCase());
        default:
          return true; // Se operador desconhecido, não filtrar
      }
    });

    // Armazenar informações do filtro para exibição na UI
    updatedData.filters = updatedData.filters || [];
    updatedData.filters.push({
      column,
      columnIndex,
      operator,
      value,
    });

    // Atualizar os dados filtrados
    updatedData.filteredRows = filteredRows;

    // Criar mensagem amigável para o operador
    const operatorText = getOperatorText(operator);

    // Resumo da operação
    const resultSummary = `Filtro aplicado na coluna ${column} ${operatorText} ${value}. ${filteredRows.length} de ${updatedData.rows.length} linhas correspondem.`;

    return { updatedData, resultSummary };
  } catch (error) {
    console.error('Erro ao executar operação de filtro:', error);
    throw new Error(
      `Falha ao aplicar filtro: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
    );
  }
}

/**
 * Converte letra de coluna (A, B, C...) para índice (0, 1, 2...)
 * @param column Letra da coluna (ex: "A", "B", "AA")
 * @returns Índice da coluna (0-based)
 */
function convertColumnLetterToIndex(column: string): number {
  let index = 0;
  const upperColumn = column.toUpperCase();

  for (let i = 0; i < upperColumn.length; i++) {
    index = index * 26 + (upperColumn.charCodeAt(i) - 64);
  }

  return index - 1; // Converter para 0-based
}

/**
 * Retorna texto amigável para o operador de filtro
 * @param operator Operador de filtro
 * @returns Texto descritivo do operador
 */
function getOperatorText(operator: string): string {
  switch (operator) {
    case 'equals':
      return 'igual a';
    case 'notEquals':
      return 'diferente de';
    case 'greaterThan':
      return 'maior que';
    case 'lessThan':
      return 'menor que';
    case 'greaterThanOrEqual':
      return 'maior ou igual a';
    case 'lessThanOrEqual':
      return 'menor ou igual a';
    case 'contains':
      return 'contém';
    case 'notContains':
      return 'não contém';
    case 'startsWith':
      return 'começa com';
    case 'endsWith':
      return 'termina com';
    default:
      return operator;
  }
}

/**
 * Extrai operações de ordenação do texto da resposta de IA
 * @param response Resposta da IA
 * @returns Array de operações de ordenação
 */
export function extractSortOperations(response: string): ExcelOperation[] {
  const operations: ExcelOperation[] = [];

  // Verificar padrões de ordenação estruturados
  const sortPattern = /OPERAÇÃO:\s*ORDENAR[\s\S]*?COLUNA:\s*([^\n]+)[\s\S]*?ORDEM:\s*([^\n]+)/gi;

  let match;
  while ((match = sortPattern.exec(response)) !== null) {
    const column = extractGroup(match, 1);
    const direction = extractGroup(match, 2);

    if (column && direction) {
      // Mapear direção para ascending/descending
      const sortDirection = direction.toLowerCase().includes('cresc') ? 'ascending' : 'descending';

      const operation: ExcelOperation = {
        type: ExcelOperationType.SORT,
        data: {
          column,
          direction: sortDirection,
        },
      };

      operations.push(operation);
    }
  }

  // Também verificar padrões em linguagem natural
  const languagePatterns = [
    // "Ordenar a coluna B em ordem crescente"
    /ordenar\s+(?:a\s+)?coluna\s+([A-Za-z0-9_]+)\s+em\s+ordem\s+(crescente|decrescente|ascendente|descendente)/gi,

    // "Classificar dados por Nome em ordem alfabética"
    /classificar\s+(?:os\s+)?(?:dados|valores|registros)\s+por\s+([A-Za-z0-9_]+)\s+em\s+ordem\s+(crescente|decrescente|ascendente|descendente|alfabética|alfabetica|numérica|numerica)/gi,
  ];

  // Processar padrões em linguagem natural
  for (const pattern of languagePatterns) {
    let match;
    while ((match = pattern.exec(response)) !== null) {
      const column = extractGroup(match, 1);
      const direction = extractGroup(match, 2, '').toLowerCase();

      // Determinar a direção de ordenação
      const sortDirection =
        direction.includes('cresc') ||
        direction.includes('ascend') ||
        direction.includes('alfabética') ||
        direction.includes('alfabetica')
          ? 'ascending'
          : 'descending';

      if (column) {
        const operation: ExcelOperation = {
          type: ExcelOperationType.SORT,
          data: {
            column,
            direction: sortDirection,
          },
        };

        operations.push(operation);
      }
    }
  }

  return operations;
}

/**
 * Executa uma operação de ordenação em dados da planilha
 * @param sheetData Dados da planilha
 * @param operation Operação de ordenação a ser executada
 * @returns Dados atualizados e resumo da operação
 */
export async function executeSortOperation(
  sheetData: any,
  operation: ExcelOperation
): Promise<{ updatedData: any; resultSummary: string }> {
  try {
    const { column, direction } = operation.data;

    if (!column || !direction) {
      throw new Error('Parâmetros insuficientes para operação de ordenação');
    }

    // Clonar dados para não modificar o original diretamente
    const updatedData = { ...sheetData };

    // Determinar o índice da coluna (letra ou nome)
    let columnIndex: number;

    if (/^[A-Z]+$/.test(column)) {
      // Converter letra de coluna para índice numérico (0-based)
      columnIndex = convertColumnLetterToIndex(column);
    } else {
      // Procurar o nome da coluna nos cabeçalhos
      columnIndex = updatedData.headers.findIndex(
        (header: string) => header.toLowerCase() === column.toLowerCase()
      );
    }

    if (columnIndex === -1) {
      throw new Error(`Coluna "${column}" não encontrada`);
    }

    // Clonar as linhas para ordenação
    const sortedRows = [...updatedData.rows];

    // Ordenar as linhas
    sortedRows.sort((a, b) => {
      const valueA = a[columnIndex];
      const valueB = b[columnIndex];

      // Tentar converter para números se ambos forem numéricos
      if (!isNaN(Number(valueA)) && !isNaN(Number(valueB))) {
        return direction === 'ascending'
          ? Number(valueA) - Number(valueB)
          : Number(valueB) - Number(valueA);
      }

      // Comparação de strings para valores não numéricos
      const strA = String(valueA).toLowerCase();
      const strB = String(valueB).toLowerCase();

      if (direction === 'ascending') {
        return strA.localeCompare(strB);
      } else {
        return strB.localeCompare(strA);
      }
    });

    // Atualizar os dados ordenados
    updatedData.rows = sortedRows;

    // Armazenar informações da ordenação para exibição na UI
    updatedData.sortInfo = {
      column,
      columnIndex,
      direction,
    };

    // Resumo da operação
    const directionText = direction === 'ascending' ? 'crescente' : 'decrescente';
    const resultSummary = `Dados ordenados pela coluna ${column} em ordem ${directionText}`;

    return { updatedData, resultSummary };
  } catch (error) {
    console.error('Erro ao executar operação de ordenação:', error);
    throw new Error(
      `Falha ao ordenar dados: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
    );
  }
}
