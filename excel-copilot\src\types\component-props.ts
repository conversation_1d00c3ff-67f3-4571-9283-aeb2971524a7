/**
 * Tipos para props de componentes
 */

export interface ExportButtonProps {
  workbookId: string;
  workbookName: string;
  sheets: Array<{
    id: string;
    name: string;
    data: string | any;
  }>;
}

export interface ChartDisplayProps {
  data: any;
  title: string;
  type: 'bar' | 'line' | 'pie' | 'scatter' | 'area';
  height?: number;
  showFormula?: boolean;
  formula?: string;
}

export interface VirtualTableProps {
  data: any[][];
  headers?: string[];
  rowHeight: number;
  visibleRows: number;
}

export interface ClientLayoutWrapperProps {
  children: React.ReactNode;
  _className?: string;
  _interClassName?: string;
}
