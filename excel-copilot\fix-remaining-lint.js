#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Lista de correções simples
const simpleReplacements = [
  // Variáveis não utilizadas - adicionar prefixo _
  {
    file: 'src/lib/excel/operationUtils.ts',
    from: 'ExcelOperationBase,',
    to: '_ExcelOperationBase,',
  },
  {
    file: 'src/lib/logger.ts',
    from: '} catch (_e) {',
    to: '} catch {',
  },
  {
    file: 'src/lib/operations/chartOperations.ts',
    from: 'extractDataForChart',
    to: '_extractDataForChart',
  },
  {
    file: 'src/lib/operations/formulaOperations.ts',
    from: 'isFormulaSafe',
    to: '_isFormulaSafe',
  },
  {
    file: 'src/lib/security/sanitization-excel.ts',
    from: 'hasDangerousFormula',
    to: '_hasDangerousFormula',
  },
  {
    file: 'src/lib/security/sanitization-excel.ts',
    from: 'sanitizeValue',
    to: '_sanitizeValue',
  },
  {
    file: 'src/lib/subscription-limits.ts',
    from: 'validateSheetOwnership',
    to: '_validateSheetOwnership',
  },
  {
    file: 'src/server/db/query-cache.ts',
    from: '} catch (_e) {',
    to: '} catch {',
  },
  {
    file: 'src/utils/data/json.ts',
    from: '} catch (_e) {',
    to: '} catch {',
  },
  {
    file: 'src/utils/logger-utils.ts',
    from: '} catch (_e) {',
    to: '} catch {',
  },
  {
    file: 'src/utils/route-migration.ts',
    from: '} catch (_e) {',
    to: '} catch {',
  },
  {
    file: 'src/utils/usage-example.ts',
    from: 'hasCellReference',
    to: '_hasCellReference',
  },
];

// Correções de ordem de imports
const importOrderFixes = [
  {
    file: 'src/lib/operations/advancedChartOperations.ts',
    from: `import { ExcelOperation, ExcelOperationResult } from '../../types';
import { extractGroup } from '@/utils/regex-utils';`,
    to: `import { extractGroup } from '@/utils/regex-utils';
import { ExcelOperation, ExcelOperationResult } from '../../types';`,
  },
  {
    file: 'src/lib/operations/filterOperations.ts',
    from: `import { ExcelOperation, ExcelOperationResult } from '../../types/index';
import { extractGroup } from '@/utils/regex-utils';`,
    to: `import { extractGroup } from '@/utils/regex-utils';

import { ExcelOperation, ExcelOperationResult } from '../../types/index';`,
  },
  {
    file: 'src/lib/operations/pivotTableOperations.ts',
    from: `import { ExcelOperation, ExcelOperationResult } from '../../types/index';
import { extractGroup } from '@/utils/regex-utils';`,
    to: `import { extractGroup } from '@/utils/regex-utils';

import { ExcelOperation, ExcelOperationResult } from '../../types/index';`,
  },
  {
    file: 'src/utils/excel-utils.ts',
    from: `import { extractGroup } from './regex-utils';
import { toError } from './error-utils';`,
    to: `import { toError } from './error-utils';
import { extractGroup } from './regex-utils';`,
  },
];

// Função para aplicar correções simples
function applySimpleReplacements() {
  console.log('🔧 Aplicando correções simples...\n');

  simpleReplacements.forEach(({ file, from, to }) => {
    const filePath = path.join(__dirname, file);

    if (!fs.existsSync(filePath)) {
      console.log(`⚠️  Arquivo não encontrado: ${file}`);
      return;
    }

    let content = fs.readFileSync(filePath, 'utf8');

    if (content.includes(from)) {
      content = content.replace(new RegExp(from.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), to);
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ ${file}: ${from} → ${to}`);
    }
  });
}

// Função para corrigir ordem de imports
function fixImportOrder() {
  console.log('\n📦 Corrigindo ordem de imports...\n');

  importOrderFixes.forEach(({ file, from, to }) => {
    const filePath = path.join(__dirname, file);

    if (!fs.existsSync(filePath)) {
      console.log(`⚠️  Arquivo não encontrado: ${file}`);
      return;
    }

    let content = fs.readFileSync(filePath, 'utf8');

    if (content.includes(from)) {
      content = content.replace(from, to);
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ ${file}: Ordem de imports corrigida`);
    }
  });
}

// Função para remover imports não utilizados do usage-example.ts
function fixUsageExample() {
  console.log('\n🗑️  Removendo imports não utilizados...\n');

  const filePath = path.join(__dirname, 'src/utils/usage-example.ts');

  if (!fs.existsSync(filePath)) {
    console.log(`⚠️  Arquivo não encontrado: src/utils/usage-example.ts`);
    return;
  }

  let content = fs.readFileSync(filePath, 'utf8');

  // Remover imports não utilizados
  const unusedImports = [
    '_logWarn',
    '_logInfo',
    '_logDebug',
    '_toErrorType',
    '_toLogMetadata',
    '_createApiError',
    '_normalizeError',
    '_EnhancedError',
    '_parseRange',
    '_getRangeValues',
  ];

  unusedImports.forEach(importName => {
    // Remover da lista de imports
    content = content.replace(new RegExp(`\\s*${importName},?`, 'g'), '');
    // Remover linhas de uso
    content = content.replace(new RegExp(`.*${importName}.*\\n`, 'g'), '');
  });

  // Limpar imports vazios
  content = content.replace(/import\s*{\s*,?\s*}\s*from.*;\n/g, '');
  content = content.replace(/,\s*,/g, ',');
  content = content.replace(/{\s*,/g, '{');
  content = content.replace(/,\s*}/g, '}');

  fs.writeFileSync(filePath, content, 'utf8');
  console.log(`✅ src/utils/usage-example.ts: Imports não utilizados removidos`);
}

// Executar todas as correções
function main() {
  console.log('🚀 Iniciando correções de linting...\n');

  applySimpleReplacements();
  fixImportOrder();
  fixUsageExample();

  console.log('\n🎉 Todas as correções foram aplicadas!');
  console.log('\n💡 Execute "npm run lint" novamente para verificar o progresso.');
}

// Executar se chamado diretamente
if (require.main === module) {
  main();
}

module.exports = { main };
