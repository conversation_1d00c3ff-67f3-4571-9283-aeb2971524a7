import { z } from 'zod';

/**
 * Esquema de validação para criação de um workbook
 */
export const createWorkbookValidator = z.object({
  name: z
    .string()
    .min(1, 'O nome é obrigatório')
    .max(100, 'O nome deve ter no máximo 100 caracteres'),
  description: z.string().max(500, 'A descrição deve ter no máximo 500 caracteres').optional(),
});

/**
 * Esquema de validação para atualização de um workbook
 */
export const updateWorkbookValidator = z.object({
  id: z.string().min(1, 'ID é obrigatório'),
  name: z
    .string()
    .min(1, 'O nome é obrigatório')
    .max(100, 'O nome deve ter no máximo 100 caracteres')
    .optional(),
  description: z.string().max(500, 'A descrição deve ter no máximo 500 caracteres').optional(),
  isPublic: z.boolean().optional(),
});

/**
 * Esquema de validação para exclusão de um workbook
 */
export const deleteWorkbookValidator = z.object({
  id: z.string().min(1, 'ID é obrigatório'),
});

/**
 * Tipo para criação de um workbook
 */
export type CreateWorkbookInput = z.infer<typeof createWorkbookValidator>;

/**
 * Tipo para atualização de um workbook
 */
export type UpdateWorkbookInput = z.infer<typeof updateWorkbookValidator>;

/**
 * Tipo para exclusão de um workbook
 */
export type DeleteWorkbookInput = z.infer<typeof deleteWorkbookValidator>;
