/**
 * Arquivo temporário para testar regex
 */

// Vers<PERSON> correta (usando $ dentro de character class)
const correctTest1 = /=[\w\d\s()+\-*/:.,]+/i;
const correctTest2 = /=[\w\d\s()+\-*/:.,]+/i;

// Versão sem o símbolo $ (também funciona)
const alternativeTest1 = /=[\w\d\s()+\-*/:.,]+/i;
const alternativeTest2 = /=[\w\d\s()+\-*/:.,]+/i;

export function testRegex() {
  return {
    correctTest1,
    correctTest2,
    alternativeTest1,
    alternativeTest2,
  };
}
