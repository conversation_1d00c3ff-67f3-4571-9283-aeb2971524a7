'use client';

import * as React from 'react';

import {
  getFormFieldClasses,
  renderWithWrapper,
  type FormFieldWrapperProps,
  type FormFieldVariant,
  type FormFieldSize,
} from './form-field-styles';

export interface InputProps
  extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size'>,
    FormFieldWrapperProps {
  variant?: FormFieldVariant;
  inputSize?: FormFieldSize;
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  (
    {
      className,
      type,
      wrapperClassName,
      variant = 'default',
      fieldSize = 'md',
      inputSize,
      ...props
    },
    ref
  ) => {
    // Manter compatibilidade com inputSize (deprecated)
    const size = inputSize || fieldSize;

    const inputElement = (
      <input
        type={type}
        className={getFormFieldClasses(variant, size, false, className)}
        ref={ref}
        {...props}
      />
    );

    return renderWithWrapper(inputElement, wrapperClassName);
  }
);

Input.displayName = 'Input';

export { Input };
export default Input;
