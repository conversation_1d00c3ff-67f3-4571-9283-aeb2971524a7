@tailwind base;
@tailwind components;
@tailwind utilities;

/* Esquema de cores do Excel Copilot - estilo Office */
@layer base {
  :root {
    /* Base Colors */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;

    /* Layout Variables */
    --header-height: 4rem;
    --sidebar-width: 18rem;
    --content-width: min(1200px, 100% - 2rem);
    --page-padding-x: clamp(1rem, 5vw, 2rem);
    --component-spacing: clamp(1rem, 3vw, 3rem);

    /* Animation Durations */
    --duration-fast: 0.15s;
    --duration-medium: 0.3s;
    --duration-slow: 0.5s;

    /* Typography - Fluid sizes that scale with viewport */
    --font-size-xs: clamp(0.75rem, 0.7rem + 0.25vw, 0.875rem);
    --font-size-sm: clamp(0.875rem, 0.8rem + 0.25vw, 1rem);
    --font-size-base: clamp(1rem, 0.9rem + 0.5vw, 1.125rem);
    --font-size-lg: clamp(1.125rem, 1rem + 0.625vw, 1.25rem);
    --font-size-xl: clamp(1.25rem, 1.1rem + 0.75vw, 1.5rem);
    --font-size-2xl: clamp(1.5rem, 1.3rem + 1vw, 1.875rem);
    --font-size-3xl: clamp(1.875rem, 1.6rem + 1.25vw, 2.25rem);
    --font-size-4xl: clamp(2.25rem, 2rem + 1.5vw, 3rem);

    /* Line Heights */
    --line-height-tight: 1.2;
    --line-height-normal: 1.5;
    --line-height-loose: 1.75;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);

    /* Performance Metrics */
    --lcp-elem-bg: transparent; /* Target background for LCP element */
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 48%;

    /* Dark theme specific */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.3);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.4), 0 2px 4px -2px rgb(0 0 0 / 0.4);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.4), 0 4px 6px -4px rgb(0 0 0 / 0.4);
    --lcp-elem-bg: hsl(var(--background)); /* Dark bg for LCP */
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground min-h-screen;
    font-feature-settings:
      'rlig' 1,
      'calt' 1;
    line-height: var(--line-height-normal);
    font-size: var(--font-size-base);
  }

  html.dark {
    color-scheme: dark;
  }

  html.light {
    color-scheme: light;
  }

  /* Evitar flash durante a transição de tema */
  html.transitioning-theme * {
    @apply transition-colors duration-300;
  }

  /* Corrigir problemas de alinhamento na barra de navegação */
  header.sticky {
    position: sticky;
    display: flex;
    width: 100%;
    align-items: center;
    border-bottom-width: 1px;
    box-sizing: border-box;
  }

  header.sticky > div.container {
    display: flex;
    width: 100%;
    height: 3.5rem;
    align-items: center;
    justify-content: space-between;
  }
}

/* Classes de fonte */
.font-inter {
  font-family:
    'Inter',
    ui-sans-serif,
    system-ui,
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    'Helvetica Neue',
    Arial,
    sans-serif;
}
