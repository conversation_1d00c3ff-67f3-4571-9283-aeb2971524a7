#!/usr/bin/env ts-node
/**
 * Script para testar a funcionalidade de exportação para Excel
 *
 * Execução: ts-node scripts/test-excel-export.ts
 */

import { createExcelFile } from '../src/lib/excel';
import fs from 'fs';
import path from 'path';
import { PrismaClient } from '@prisma/client';

// Cliente Prisma para acesso ao banco de dados
const prisma = new PrismaClient();

/**
 * Função principal
 */
async function main() {
  console.log('=== TESTE DE EXPORTAÇÃO PARA EXCEL ===\n');

  try {
    // 1. Criar dados de exemplo para exportação
    console.log('Criando dados de exemplo...');

    const sheetData1 = {
      headers: ['Produto', 'Categoria', 'Preço', 'Estoque', 'Vendas'],
      rows: [
        ['Produto A', 'Eletrônicos', 1500, 20, 8],
        ['Produto B', 'Informática', 800, 35, 15],
        ['Produto C', 'Eletrônicos', 2000, 15, 5],
        ['Produto D', 'Escritório', 50, 200, 120],
        ['Produto E', 'Informática', 1200, 40, 25],
      ],
    };

    const sheetData2 = {
      headers: ['Categoria', 'Total Produtos', 'Valor Total', 'Vendas Totais'],
      rows: [
        ['Eletrônicos', 2, 3500, 13],
        ['Informática', 2, 2000, 40],
        ['Escritório', 1, 50, 120],
      ],
    };

    // 2. Criar diretório para arquivos exportados
    const outputDir = path.join(__dirname, '../temp');

    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    // 3. Testar exportação de dados em memória
    console.log('\nTestando exportação de dados em memória...');

    const excelBlob = await createExcelFile(
      [
        { name: 'Produtos', data: sheetData1 },
        { name: 'Resumo', data: sheetData2 },
      ],
      'Teste de Exportação'
    );

    const memoryFilePath = path.join(outputDir, 'teste-exportacao-memoria.xlsx');
    const buffer = await excelBlob.arrayBuffer();

    fs.writeFileSync(memoryFilePath, Buffer.from(buffer));

    console.log(`✅ Arquivo Excel salvo em: ${memoryFilePath}`);
    console.log(`   Tamanho: ${fs.statSync(memoryFilePath).size} bytes`);

    // 4. Testar exportação de dados do banco de dados
    console.log('\nBuscando dados do banco de dados para exportação...');

    // Buscar workbooks do usuário de teste
    const workbooks = await prisma.workbook.findMany({
      where: {
        userId: 'test-user',
      },
      include: {
        sheets: true,
      },
      take: 3, // Limitar a 3 workbooks para o teste
    });

    if (workbooks.length > 0) {
      console.log(`Encontrados ${workbooks.length} workbooks para exportação`);

      for (const workbook of workbooks) {
        console.log(`\nExportando workbook: ${workbook.name} (ID: ${workbook.id})`);

        if (workbook.sheets.length === 0) {
          console.log('❌ Workbook não possui planilhas');
          continue;
        }

        // Preparar dados para exportação
        const sheetsToExport = workbook.sheets.map(sheet => ({
          name: sheet.name,
          data: JSON.parse(sheet.data || '{}'),
        }));

        // Criar arquivo Excel
        const workbookBlob = await createExcelFile(sheetsToExport, workbook.name);

        // Salvar arquivo
        const dbFilePath = path.join(
          outputDir,
          `${workbook.name.replace(/\s+/g, '-').toLowerCase()}.xlsx`
        );
        const dbBuffer = await workbookBlob.arrayBuffer();

        fs.writeFileSync(dbFilePath, Buffer.from(dbBuffer));

        console.log(`✅ Workbook exportado para: ${dbFilePath}`);
        console.log(`   Tamanho: ${fs.statSync(dbFilePath).size} bytes`);
      }
    } else {
      console.log('❌ Nenhum workbook encontrado no banco de dados para o usuário de teste');

      // Criar um novo workbook com os dados de exemplo
      console.log('\nCriando workbook de teste no banco de dados...');

      const testWorkbook = await prisma.workbook.create({
        data: {
          name: 'Workbook de Teste para Exportação',
          userId: 'test-user',
          sheets: {
            create: [
              {
                name: 'Produtos',
                data: JSON.stringify(sheetData1),
              },
              {
                name: 'Resumo',
                data: JSON.stringify(sheetData2),
              },
            ],
          },
        },
      });

      console.log(`✅ Workbook criado com ID: ${testWorkbook.id}`);

      // Buscar o workbook recém-criado com suas planilhas
      const createdWorkbook = await prisma.workbook.findUnique({
        where: {
          id: testWorkbook.id,
        },
        include: {
          sheets: true,
        },
      });

      if (createdWorkbook && createdWorkbook.sheets.length > 0) {
        // Preparar dados para exportação
        const sheetsToExport = createdWorkbook.sheets.map(sheet => ({
          name: sheet.name,
          data: JSON.parse(sheet.data || '{}'),
        }));

        // Criar arquivo Excel
        const workbookBlob = await createExcelFile(sheetsToExport, createdWorkbook.name);

        // Salvar arquivo
        const newFilePath = path.join(outputDir, 'novo-workbook-exportado.xlsx');
        const newBuffer = await workbookBlob.arrayBuffer();

        fs.writeFileSync(newFilePath, Buffer.from(newBuffer));

        console.log(`✅ Novo workbook exportado para: ${newFilePath}`);
        console.log(`   Tamanho: ${fs.statSync(newFilePath).size} bytes`);
      }
    }

    // 5. Testar formatos diferentes
    console.log('\nTestando formatos diferentes de dados...');

    // Formato de células específicas (A1, B2, etc.)
    const cellFormat = {
      A1: 'Cabeçalho 1',
      B1: 'Cabeçalho 2',
      C1: 'Cabeçalho 3',
      A2: 100,
      B2: 200,
      C2: 300,
      A3: '=SUM(A2:C2)',
    };

    // Criar arquivo Excel
    const cellFormatBlob = await createExcelFile(
      [{ name: 'Células Específicas', data: cellFormat }],
      'Teste Formato Células'
    );

    // Salvar arquivo
    const cellFormatPath = path.join(outputDir, 'teste-formato-celulas.xlsx');
    const cellFormatBuffer = await cellFormatBlob.arrayBuffer();

    fs.writeFileSync(cellFormatPath, Buffer.from(cellFormatBuffer));

    console.log(`✅ Arquivo de formato de células salvo em: ${cellFormatPath}`);
    console.log(`   Tamanho: ${fs.statSync(cellFormatPath).size} bytes`);

    console.log('\n=== TESTE DE EXPORTAÇÃO CONCLUÍDO ===');
    console.log(`Todos os arquivos exportados estão no diretório: ${outputDir}`);
  } catch (error) {
    console.error('❌ Erro no teste de exportação:', error);
  } finally {
    // Fechar conexão com o banco de dados
    await prisma.$disconnect();
  }
}

// Executar função principal
main().catch(console.error);
