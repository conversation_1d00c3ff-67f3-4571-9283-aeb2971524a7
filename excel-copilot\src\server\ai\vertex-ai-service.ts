import * as fs from 'fs';
import * as path from 'path';

import { VertexAI } from '@google-cloud/vertexai';

import { ENV } from '@/config/unified-environment';
import { logger } from '@/lib/logger';

/**
 * Serviço de integração com Vertex AI do Google Cloud
 * Implementa comunicação segura usando service accounts
 */

// Interface de opções para sendMessage
export interface SendMessageOptions {
  userId?: string;
  timeout?: number;
  preserveContext?: boolean;
  skipCache?: boolean;
  context?: string;
  operationType?: string;
  temperature?: number;
  maxOutputTokens?: number;
  topK?: number;
  topP?: number;
}

// Tipos para erros de serviço
export enum GeminiErrorType {
  TIMEOUT = 'timeout',
  API_UNAVAILABLE = 'api_unavailable',
  INVALID_REQUEST = 'invalid_request',
  CONTENT_FILTERED = 'content_filtered',
  RATE_LIMITED = 'rate_limited',
  UNKNOWN = 'unknown',
  INVALID_RESPONSE_FORMAT = 'invalid_response_format',
  CONTEXT_LIMIT_EXCEEDED = 'context_limit_exceeded',
  TOKEN_LIMIT_EXCEEDED = 'token_limit_exceeded',
  RETRY_FAILED = 'retry_failed',
}

/**
 * Classe de erro para serviço Vertex AI
 */
export class VertexAIError extends Error {
  type: GeminiErrorType;
  details: any;

  constructor(
    message: string,
    type: GeminiErrorType = GeminiErrorType.UNKNOWN,
    details: any = null
  ) {
    super(message);
    this.name = 'VertexAIError';
    this.type = type;
    this.details = details;
  }
}

/**
 * Serviço que gerencia a comunicação com a API Vertex AI
 */
export class VertexAIService {
  private static instance: VertexAIService;
  private vertexAI: VertexAI | null = null;
  private vertexModel: any = null;
  private isInitialized = false;
  private initPromise: Promise<boolean> | null = null;
  private readonly stats = {
    totalRequests: 0,
    successfulRequests: 0,
    failedRequests: 0,
    cacheHits: 0,
  };

  private constructor() {
    if (ENV.VERTEX_AI.ENABLED) {
      // Inicializar o serviço de forma assíncrona
      this.initPromise = this.initialize();
    } else {
      logger.warn('Serviço Vertex AI desabilitado via configuração');
    }
  }

  /**
   * Obter a instância singleton do serviço
   */
  public static getInstance(): VertexAIService {
    if (!VertexAIService.instance) {
      VertexAIService.instance = new VertexAIService();
    }
    return VertexAIService.instance;
  }

  /**
   * Método wrapper para compatibilidade com GeminiService
   * Envia uma mensagem para o modelo de IA
   */
  public async sendMessage(message: string, options: SendMessageOptions = {}): Promise<string> {
    // Incrementar contador seguramente
    this.incrementStat('totalRequests');

    // Montar prompt com contexto se fornecido
    let promptWithContext = message;
    if (options.context) {
      promptWithContext = `Contexto: ${options.context}\n\nPergunta: ${message}`;
    }

    // Configurar opções para o modelo
    const modelOptions = {
      temperature: options.temperature || 0.2,
      maxOutputTokens: options.maxOutputTokens || 2048,
      topK: options.topK || 40,
      topP: options.topP || 0.8,
    };

    try {
      const response = await this.generateText(promptWithContext, modelOptions);
      // Incrementar contador de sucessos
      this.incrementStat('successfulRequests');
      return response;
    } catch (error) {
      // Incrementar contador de falhas
      this.incrementStat('failedRequests');
      throw error;
    }
  }

  /**
   * Incrementa um contador estatístico com segurança
   */
  private incrementStat(
    stat: 'totalRequests' | 'successfulRequests' | 'failedRequests' | 'cacheHits'
  ): void {
    if (this.stats) {
      this.stats[stat]++;
    }
  }

  /**
   * Retorna estatísticas do serviço
   */
  public getStats(): Record<string, number> {
    return { ...this.stats };
  }

  /**
   * Verifica se o serviço Vertex AI está disponível para uso
   * @returns true se o serviço estiver inicializado e pronto
   */
  public async isServiceAvailable(): Promise<boolean> {
    if (this.isInitialized) {
      return true;
    }

    if (this.initPromise) {
      return this.initPromise;
    }

    return false;
  }

  /**
   * Inicializa o serviço Vertex AI
   */
  private async initialize(): Promise<boolean> {
    if (this.isInitialized) {
      return true;
    }

    try {
      // Verificar arquivo de credenciais
      await this.configureCredentials();

      // Extrair o project_id das credenciais ou configuração
      const projectId = this.getProjectId();
      if (!projectId) {
        logger.error('Project ID não encontrado nas credenciais ou configuração');
        return false;
      }

      // Inicializar cliente Vertex AI
      const location = ENV.VERTEX_AI.LOCATION || 'us-central1';
      this.vertexAI = new VertexAI({
        project: projectId,
        location: location,
      });

      // Obter modelo Gemini
      const modelName = ENV.VERTEX_AI.MODEL_NAME || 'gemini-1.5-pro';
      // Garantir que vertexAI não é null antes de usar
      const vertexAI = this.vertexAI;
      if (!vertexAI) {
        logger.error('Cliente Vertex AI não foi inicializado corretamente');
        return false;
      }

      this.vertexModel = vertexAI.preview.getGenerativeModel({
        model: modelName,
      });

      logger.info(
        `Vertex AI inicializado com sucesso: projeto=${projectId}, região=${location}, modelo=${modelName}`
      );
      this.isInitialized = true;
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error(`Falha ao inicializar Vertex AI: ${errorMessage}`, error);
      return false;
    }
  }

  /**
   * Configura as credenciais para o Vertex AI
   */
  private async configureCredentials(): Promise<void> {
    // Verificar se já temos GOOGLE_APPLICATION_CREDENTIALS no ambiente
    if (process.env.GOOGLE_APPLICATION_CREDENTIALS) {
      logger.info(
        `Usando variável de ambiente GOOGLE_APPLICATION_CREDENTIALS: ${process.env.GOOGLE_APPLICATION_CREDENTIALS}`
      );
      return;
    }

    // Verificar se temos credenciais como string JSON na variável de ambiente
    if (process.env.VERTEX_AI_CREDENTIALS) {
      try {
        const credentials = JSON.parse(process.env.VERTEX_AI_CREDENTIALS);

        // Validar se é um service account válido
        if (!credentials.type || credentials.type !== 'service_account') {
          throw new Error('Credenciais devem ser de uma service account');
        }

        // Criar um arquivo temporário com as credenciais
        const tempCredentialsPath = path.join(process.cwd(), '.vertex-credentials-temp.json');
        fs.writeFileSync(tempCredentialsPath, JSON.stringify(credentials, null, 2));
        process.env.GOOGLE_APPLICATION_CREDENTIALS = tempCredentialsPath;
        logger.info('Usando credenciais do Vertex AI da variável de ambiente');
        return;
      } catch (parseError) {
        logger.error('Erro ao analisar VERTEX_AI_CREDENTIALS:', parseError);
        throw new Error('Credenciais do Vertex AI inválidas na variável de ambiente');
      }
    }

    // Verificar caminho específico nas configurações
    if (ENV.VERTEX_AI.CREDENTIALS_PATH) {
      if (!fs.existsSync(ENV.VERTEX_AI.CREDENTIALS_PATH)) {
        throw new Error(`Arquivo de credenciais não encontrado: ${ENV.VERTEX_AI.CREDENTIALS_PATH}`);
      }
      process.env.GOOGLE_APPLICATION_CREDENTIALS = ENV.VERTEX_AI.CREDENTIALS_PATH;
      logger.info(`Usando arquivo de credenciais configurado: ${ENV.VERTEX_AI.CREDENTIALS_PATH}`);
      return;
    }

    // Verificar arquivo padrão na raiz do projeto
    const defaultPath = path.join(process.cwd(), 'vertex-credentials.json');
    if (fs.existsSync(defaultPath)) {
      process.env.GOOGLE_APPLICATION_CREDENTIALS = defaultPath;
      logger.info(`Usando arquivo de credenciais padrão: ${defaultPath}`);
      return;
    }

    throw new Error('Nenhum arquivo de credenciais encontrado para Vertex AI');
  }

  /**
   * Obtém o ID do projeto das credenciais ou configuração
   */
  private getProjectId(): string {
    // Verificar primeiro nas configurações
    if (ENV.VERTEX_AI.PROJECT_ID) {
      return ENV.VERTEX_AI.PROJECT_ID;
    }

    // Tentar extrair do arquivo de credenciais
    const credentialsPath = process.env.GOOGLE_APPLICATION_CREDENTIALS;
    if (credentialsPath && fs.existsSync(credentialsPath)) {
      try {
        const credentials = JSON.parse(fs.readFileSync(credentialsPath, 'utf8'));
        return credentials.project_id;
      } catch (error) {
        logger.error('Erro ao ler project_id do arquivo de credenciais', error);
      }
    }

    return '';
  }

  /**
   * Gera uma resposta do modelo Gemini via Vertex AI
   */
  public async generateText(
    prompt: string,
    options: {
      temperature?: number;
      maxOutputTokens?: number;
      topK?: number;
      topP?: number;
    } = {}
  ): Promise<string> {
    // Garantir que o serviço foi inicializado
    if (!this.isInitialized) {
      if (!this.initPromise) {
        throw new VertexAIError(
          'Serviço Vertex AI não inicializado',
          GeminiErrorType.API_UNAVAILABLE
        );
      }
      const initialized = await this.initPromise;
      if (!initialized) {
        throw new VertexAIError(
          'Falha ao inicializar serviço Vertex AI',
          GeminiErrorType.API_UNAVAILABLE
        );
      }
    }

    // Verificar se temos acesso ao modelo
    if (!this.vertexModel) {
      throw new VertexAIError('Modelo Vertex AI não disponível', GeminiErrorType.API_UNAVAILABLE);
    }

    try {
      // Configurar parâmetros de geração
      const generationConfig = {
        temperature: options.temperature ?? 0.4,
        maxOutputTokens: options.maxOutputTokens ?? 2048,
        topK: options.topK ?? 40,
        topP: options.topP ?? 0.95,
      };

      // Preparar request para o modelo
      const request = {
        contents: [{ role: 'user', parts: [{ text: prompt }] }],
        generationConfig,
      };

      // Fazer a requisição ao Vertex AI
      const result = await this.vertexModel.generateContent(request);

      // Extrair o texto da resposta
      const response = result.response;
      if (!response.candidates || response.candidates.length === 0) {
        throw new VertexAIError(
          'Resposta vazia do modelo',
          GeminiErrorType.INVALID_RESPONSE_FORMAT
        );
      }

      // Extrair texto da resposta
      const content = response.candidates[0].content;
      if (!content.parts || content.parts.length === 0) {
        throw new VertexAIError(
          'Formato de resposta inválido',
          GeminiErrorType.INVALID_RESPONSE_FORMAT
        );
      }

      const text = content.parts.map((part: any) => part.text).join('');
      return text;
    } catch (error) {
      // Tratar erros específicos da API
      if (error instanceof VertexAIError) {
        throw error;
      }

      // Verificar tipos de erro comuns
      const errorMessage = error instanceof Error ? error.message : String(error);

      if (errorMessage.includes('quota') || errorMessage.includes('rate limit')) {
        throw new VertexAIError(
          'Limite de taxa excedido na API Vertex AI',
          GeminiErrorType.RATE_LIMITED,
          error
        );
      }

      if (errorMessage.includes('blocked') || errorMessage.includes('safety')) {
        throw new VertexAIError(
          'Conteúdo bloqueado por filtros de segurança',
          GeminiErrorType.CONTENT_FILTERED,
          error
        );
      }

      if (errorMessage.includes('token limit') || errorMessage.includes('too long')) {
        throw new VertexAIError(
          'Limite de tokens excedido',
          GeminiErrorType.TOKEN_LIMIT_EXCEEDED,
          error
        );
      }

      // Erro genérico
      throw new VertexAIError(
        `Erro ao gerar texto via Vertex AI: ${errorMessage}`,
        GeminiErrorType.UNKNOWN,
        error
      );
    }
  }

  /**
   * Gera stream de texto para resposta em tempo real
   */
  public async generateTextStream(
    prompt: string,
    options: {
      temperature?: number;
      maxOutputTokens?: number;
      topK?: number;
      topP?: number;
    } = {}
  ): Promise<ReadableStream<Uint8Array>> {
    // Garantir que o serviço foi inicializado
    if (!this.isInitialized) {
      if (!this.initPromise) {
        throw new VertexAIError(
          'Serviço Vertex AI não inicializado',
          GeminiErrorType.API_UNAVAILABLE
        );
      }
      const initialized = await this.initPromise;
      if (!initialized) {
        throw new VertexAIError(
          'Falha ao inicializar serviço Vertex AI',
          GeminiErrorType.API_UNAVAILABLE
        );
      }
    }

    // Verificar se temos acesso ao modelo
    if (!this.vertexModel) {
      throw new VertexAIError('Modelo Vertex AI não disponível', GeminiErrorType.API_UNAVAILABLE);
    }

    try {
      // Configurar parâmetros de geração
      const generationConfig = {
        temperature: options.temperature ?? 0.4,
        maxOutputTokens: options.maxOutputTokens ?? 2048,
        topK: options.topK ?? 40,
        topP: options.topP ?? 0.95,
      };

      // Preparar request para o modelo
      const request = {
        contents: [{ role: 'user', parts: [{ text: prompt }] }],
        generationConfig,
      };

      // Criar um stream para a resposta
      const encoder = new TextEncoder();
      // Armazenar referência ao vertexModel para uso no stream
      const vertexModel = this.vertexModel;

      // Criar stream para processar a resposta do Vertex AI
      return new ReadableStream({
        async start(controller) {
          try {
            // Usar vertexModel diretamente sem aliasing de this
            const result = await vertexModel.generateContentStream(request);

            // Processar chunks de resposta
            for await (const chunk of result.stream) {
              // Verificar se temos texto para processar
              if (chunk.candidates && chunk.candidates.length > 0) {
                const candidate = chunk.candidates[0];
                if (
                  candidate.content &&
                  candidate.content.parts &&
                  candidate.content.parts.length > 0
                ) {
                  const text = candidate.content.parts[0].text || '';
                  controller.enqueue(encoder.encode(text));
                }
              }
            }

            // Finalizar stream
            controller.close();
          } catch (error) {
            // Reportar erro no stream
            const errorMessage = error instanceof Error ? error.message : String(error);
            controller.enqueue(encoder.encode(`Erro: ${errorMessage}`));
            controller.close();
          }
        },
      });
    } catch (error) {
      // Converter erros para stream com mensagem de erro
      const encoder = new TextEncoder();
      const errorMessage = error instanceof Error ? error.message : String(error);

      return new ReadableStream({
        start(controller) {
          controller.enqueue(encoder.encode(`Erro ao iniciar stream: ${errorMessage}`));
          controller.close();
        },
      });
    }
  }
}
