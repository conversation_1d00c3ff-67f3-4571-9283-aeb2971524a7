import axios from 'axios';
import { useState, useCallback, useEffect, useRef } from 'react';

// geminiService removido - usando apenas APIs do servidor
import { feedbackService } from '@/lib/ai/feedback-service';
import { nanoid } from '@/lib/utils';

export interface AIMessage {
  id: string;
  content: string;
  role: 'user' | 'assistant' | 'system';
  timestamp: Date;
}

export enum CommandStatus {
  IDLE = 'idle',
  PENDING = 'pending',
  COMPLETED = 'completed',
  FAILED = 'failed',
}

export interface CommandInterpretation {
  interpretation: string;
  confidence: number;
  commandId?: string;
  _commandId?: string;
}

export interface Message {
  id: string;
  role: 'user' | 'system' | 'assistant';
  content: string;
  timestamp: number;
}

export interface UseAIChatOptions {
  workbookId?: string;
  onMessageReceived?: (content: string) => void;
  onError?: (error: Error) => void;
  useMock?: boolean;
  maxHistorySize?: number;
  onInterpretation?: (interpretation: CommandInterpretation) => void;
  initialMessages?: AIMessage[];
  modelName?: string;
  systemPrompt?: string;
}

export interface UseAIChatResult {
  messages: AIMessage[];
  isProcessing: boolean;
  error: Error | null;
  sendMessage: (content: string) => Promise<void>;
  clearMessages: () => void;
}

/**
 * Mock para simular respostas da IA durante desenvolvimento
 */
function findBestMockResponse(message: string): string {
  // Simulações de respostas baseadas em palavras-chave
  const mockResponses = [
    {
      keywords: ['média', 'coluna'],
      response: `{
        "operations": [
          {
            "type": "FORMULA",
            "data": {
              "formula": "=MÉDIA(B:B)",
              "range": "C1"
            }
          }
        ],
        "explanation": "Calculando a média da coluna B",
        "interpretation": "Você solicitou o cálculo da média dos valores na coluna B"
      }`,
    },
    {
      keywords: ['gráfico', 'barras'],
      response: `{
        "operations": [
          {
            "type": "CHART",
            "data": {
              "type": "bar",
              "title": "Gráfico de Barras",
              "labels": "A1:A10",
              "datasets": ["B1:B10"]
            }
          }
        ],
        "explanation": "Criando um gráfico de barras com dados das colunas A e B",
        "interpretation": "Você solicitou a criação de um gráfico de barras usando os dados existentes"
      }`,
    },
    {
      keywords: ['tabela', 'criar'],
      response: `{
        "operations": [
          {
            "type": "CELL_UPDATE",
            "data": {
              "updates": [
                { "cell": "A1", "value": "Produto" },
                { "cell": "B1", "value": "Valor" },
                { "cell": "C1", "value": "Quantidade" },
                { "cell": "A2", "value": "Produto 1" },
                { "cell": "B2", "value": 100 },
                { "cell": "C2", "value": 10 }
              ]
            }
          }
        ],
        "explanation": "Criando uma tabela com 3 colunas: Produto, Valor e Quantidade",
        "interpretation": "Você solicitou a criação de uma nova tabela para registro de produtos"
      }`,
    },
  ];

  // Verificar quais respostas correspondem ao input do usuário
  const lowerMessage = message.toLowerCase();
  const matchingResponses = mockResponses.filter(mock =>
    mock.keywords.some(keyword => lowerMessage.includes(keyword))
  );

  if (matchingResponses.length > 0 && matchingResponses[0]) {
    // Retornar a resposta com mais correspondências
    return matchingResponses[0].response;
  }

  // Resposta genérica como fallback
  return `{
    "operations": [
      {
        "type": "CELL_UPDATE",
        "data": {
          "updates": [
            { "cell": "A1", "value": "Exemplo" },
            { "cell": "B1", "value": 100 }
          ]
        }
      }
    ],
    "explanation": "Realizando uma operação exemplo baseada no seu comando",
    "interpretation": "Seu comando foi processado como uma solicitação de exemplo"
  }`;
}

/**
 * Hook para interagir com a API de chat de IA
 */
export const useAIChat = (
  options: UseAIChatOptions = {}
): UseAIChatResult & {
  confirmAndExecute: () => Promise<unknown>;
  cancelCommand: () => void;
  pendingInterpretation: CommandInterpretation | null;
  commandStatus: CommandStatus;
} => {
  const [messages, setMessages] = useState<AIMessage[]>(options.initialMessages || []);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [commandStatus, setCommandStatus] = useState<CommandStatus>(CommandStatus.IDLE);
  const [pendingInterpretation, setPendingInterpretation] = useState<CommandInterpretation | null>(
    null
  );
  const abortControllerRef = useRef<AbortController | null>(null);

  // Reset error when messages change
  useEffect(() => {
    if (error && messages.length > 0) {
      setError(null);
    }
  }, [messages, error]);

  // Determinar se deve usar modo de simulação
  const shouldUseMock =
    options.useMock ||
    (process.env.NODE_ENV === 'development' && process.env.AI_USE_MOCK === 'true');

  // Função para simular resposta da IA com delay
  const mockAIResponse = useCallback(async (message: string): Promise<string> => {
    // Simular atraso de rede
    return new Promise(resolve => {
      setTimeout(() => {
        resolve(findBestMockResponse(message));
      }, 1500); // Delay de 1,5 segundos para simular processamento
    });
  }, []);

  // Função para interpretar comando antes de executar
  const _interpretCommand = useCallback(
    async (message: string): Promise<CommandInterpretation | null> => {
      try {
        // Validação básica
        if (!message || message.trim() === '') {
          return null;
        }

        let aiResponse: string;

        // Modo de desenvolvimento/mock ou produção
        if (shouldUseMock) {
          // Usar resposta mockada
          aiResponse = await mockAIResponse(message);
        } else {
          try {
            // Fazer requisição para a API do servidor
            const response = await axios.post('/api/ai/chat', {
              message,
              userId: options.workbookId || 'anonymous',
              context: {
                mode: 'preview',
                excelContext: {
                  activeSheet: 'Atual',
                },
              },
              preserveContext: false,
            });
            aiResponse = response.data;
          } catch (geminiError) {
            console.error('Erro na comunicação com Gemini durante interpretação:', geminiError);
            return null;
          }
        }

        // Tentar extrair a interpretação da resposta
        try {
          const responseObj = JSON.parse(aiResponse);

          if (responseObj.interpretation) {
            return {
              interpretation: responseObj.interpretation,
              confidence: responseObj.confidence || 0,
              commandId: nanoid(),
              _commandId: nanoid(),
            };
          }
        } catch (parseError) {
          console.error('Erro ao parsear resposta de interpretação:', parseError);
        }

        return null;
      } catch (err) {
        console.error('Erro ao interpretar comando:', err);
        return null;
      }
    },
    [options.workbookId, shouldUseMock, mockAIResponse]
  );

  // Função para enviar mensagem para a API
  const sendMessage = useCallback(
    async (content: string) => {
      if (!content.trim()) return;

      // Create user message
      const userMessage: AIMessage = {
        id: `user-${Date.now()}`,
        content,
        role: 'user',
        timestamp: new Date(),
      };

      setMessages(prev => [...prev, userMessage]);
      setIsLoading(true);
      setError(null);

      try {
        // Call API to get AI response
        const response = await fetch('/api/ai/chat', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            messages: [...messages, userMessage].map(m => ({ role: m.role, content: m.content })),
            modelName: options.modelName,
            systemPrompt: options.systemPrompt,
          }),
        });

        if (!response.ok) {
          throw new Error(`Error: ${response.statusText}`);
        }

        const result = await response.json();

        // Add AI response
        const aiMessage: AIMessage = {
          id: `assistant-${Date.now()}`,
          content: result.response,
          role: 'assistant',
          timestamp: new Date(),
        };

        setMessages(prev => [...prev, aiMessage]);
      } catch (err) {
        const error = err instanceof Error ? err : new Error('Unknown error');
        setError(error);
        if (options.onError) {
          options.onError(error);
        }
      } finally {
        setIsLoading(false);
      }
    },
    [messages, options]
  );

  // Função para confirmar e executar um comando interpretado
  const executeCommand = useCallback(
    async (message: string) => {
      try {
        setIsLoading(true);

        // Adicionar mensagem do usuário à lista (com controle de tamanho)
        const userMessage: AIMessage = {
          id: `user-${Date.now()}`,
          content: message,
          role: 'user',
          timestamp: new Date(),
        };
        setMessages(prev => {
          // Manter apenas as últimas N mensagens
          const newMessages = [...prev, userMessage];
          const maxHistory = options.maxHistorySize || 20; // Valor padrão
          return newMessages.length > maxHistory ? newMessages.slice(-maxHistory) : newMessages;
        });

        let aiResponse: string;

        // Modo de desenvolvimento/mock ou produção
        if (shouldUseMock) {
          // Usar resposta mockada
          aiResponse = await mockAIResponse(message);
        } else {
          try {
            // Limitar histórico enviado para a API para reduzir tamanho do contexto
            const maxHistory = options.maxHistorySize || 20; // Valor padrão
            const limitedMessages = messages.slice(-Math.min(maxHistory, 10));
            const historyMessages = limitedMessages.map(msg => ({
              role: msg.role,
              content: msg.content,
            }));

            // Configurar contexto da planilha se disponível
            const workbookId = options.workbookId;
            const contextOptions = workbookId
              ? {
                  excelContext: {
                    activeSheet: 'Atual',
                    // Poderíamos buscar mais informações contextuais aqui se necessário
                  },
                  responseStructure: {
                    preferJson: true,
                  },
                }
              : {};

            // Fazer requisição para a API do servidor
            const response = await axios.post('/api/ai/chat', {
              message,
              userId: workbookId || 'anonymous',
              context: contextOptions,
              preserveContext: historyMessages.length > 0,
            });
            aiResponse = response.data;
          } catch (geminiError) {
            console.error('Erro na comunicação direta com Gemini, tentando API:', geminiError);

            // Fallback para API do servidor (que pode ter lógica adicional)
            const response = await axios.post('/api/chat', {
              message,
              workbookId: options.workbookId,
            });

            // Extrair resposta da IA
            aiResponse = response.data.response;
          }
        }

        // Adicionar resposta da IA à lista de mensagens (com controle de tamanho)
        const assistantMessage: AIMessage = {
          id: `assistant-${Date.now()}`,
          content: aiResponse,
          role: 'assistant',
          timestamp: new Date(),
        };
        setMessages(prev => {
          const newMessages = [...prev, assistantMessage];
          const maxHistory = options.maxHistorySize || 20; // Valor padrão
          return newMessages.length > maxHistory ? newMessages.slice(-maxHistory) : newMessages;
        });

        // Notificar componente pai sobre a resposta recebida
        if (options.onMessageReceived) {
          options.onMessageReceived(aiResponse);
        }

        // Gerar ID para este comando para uso com feedback
        const commandId = nanoid();

        // Armazenar comando para feedback futuro
        try {
          await feedbackService.storeFeedback({
            commandId,
            command: message,
            successful: true, // Presumimos sucesso inicial
          });
        } catch (feedbackError) {
          console.error('Erro ao armazenar comando para feedback:', feedbackError);
        }

        // Processar interpretação, se existir
        if (aiResponse) {
          const interpretation: CommandInterpretation = {
            interpretation: aiResponse,
            confidence: 1.0,
            commandId: nanoid(),
            _commandId: nanoid(),
          };

          setPendingInterpretation(interpretation);
          setCommandStatus(CommandStatus.PENDING);

          if (options.onInterpretation) {
            options.onInterpretation(interpretation);
          }
        }

        return {
          response: aiResponse,
          commandId,
        };
      } catch (err) {
        console.error('Erro ao executar comando:', err);
        throw err;
      } finally {
        setIsLoading(false);
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [
      messages,
      options.workbookId,
      shouldUseMock,
      mockAIResponse,
      options.onMessageReceived,
      options.maxHistorySize,
      // geminiService removido
      setMessages,
      setIsLoading,
      feedbackService,
      options.onInterpretation,
    ]
  );

  // Função para confirmar e executar um comando interpretado
  const confirmAndExecute = useCallback(async () => {
    if (!pendingInterpretation) return null;

    const { _commandId } = pendingInterpretation;
    const result = await executeCommand(pendingInterpretation.interpretation);

    // Limpar a interpretação pendente
    setPendingInterpretation(null);

    return result;
  }, [pendingInterpretation, executeCommand, setPendingInterpretation]);

  // Função para cancelar um comando interpretado
  const cancelCommand = useCallback(() => {
    setPendingInterpretation(null);
    setCommandStatus(CommandStatus.IDLE);
  }, []);

  // Limpar histórico de mensagens
  const clearMessages = useCallback(() => {
    setMessages([]);
    setPendingInterpretation(null);
    setCommandStatus(CommandStatus.IDLE);
  }, []);

  // Abortar requisição ao desmontar componente
  useEffect(() => {
    const controller = abortControllerRef.current;
    return () => {
      if (controller) {
        controller.abort();
      }
    };
  }, []);

  return {
    messages,
    isProcessing: isLoading,
    error,
    sendMessage,
    clearMessages,
    confirmAndExecute,
    cancelCommand,
    pendingInterpretation,
    commandStatus,
  };
};
