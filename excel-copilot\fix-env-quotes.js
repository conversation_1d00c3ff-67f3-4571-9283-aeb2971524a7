#!/usr/bin/env node

/**
 * Script para corrigir aspas duplas extras em arquivos .env
 * Problema: URLs com aspas duplas causam erro "Invalid URL" no build do Vercel
 */

const fs = require('fs');
const path = require('path');

const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m',
};

console.log(`${colors.blue}🔧 Corrigindo aspas duplas extras em arquivos .env...${colors.reset}`);

// Lista de arquivos .env para corrigir
const envFiles = ['.env.production', '.env.verificacao', '.env.do.vercel', '.env.local'];

let filesFixed = 0;
let totalErrors = 0;

envFiles.forEach(filename => {
  const filePath = path.join(process.cwd(), filename);

  if (!fs.existsSync(filePath)) {
    console.log(`${colors.yellow}⚠️  Arquivo não encontrado: ${filename}${colors.reset}`);
    return;
  }

  try {
    console.log(`\n${colors.blue}📝 Processando: ${filename}${colors.reset}`);

    let content = fs.readFileSync(filePath, 'utf8');
    let originalContent = content;
    let lineErrors = 0;

    // Dividir em linhas para processar individualmente
    const lines = content.split('\n');
    const fixedLines = lines.map((line, index) => {
      // Pular comentários e linhas vazias
      if (line.trim().startsWith('#') || line.trim() === '') {
        return line;
      }

      // Verificar se a linha tem formato KEY="VALUE" (incluindo variáveis que começam com NEXT_PUBLIC_)
      const match =
        line.match(/^([A-Z_][A-Z0-9_]*)="(.+)"$/) ||
        line.match(/^(NEXT_PUBLIC_[A-Z_][A-Z0-9_]*)="(.+)"$/);
      if (match) {
        const [, key, value] = match;

        // Casos especiais onde aspas são necessárias (JSON, etc.)
        if (key === 'VERTEX_AI_CREDENTIALS' && value.startsWith('{')) {
          // Para JSON, manter sem aspas externas mas preservar JSON interno
          const fixedLine = `${key}=${value}`;
          console.log(
            `   ${colors.green}✓${colors.reset} Linha ${index + 1}: ${key} (JSON preservado)`
          );
          lineErrors++;
          return fixedLine;
        }

        // Para outros casos, remover aspas duplas
        const fixedLine = `${key}=${value}`;
        console.log(`   ${colors.green}✓${colors.reset} Linha ${index + 1}: ${key}`);
        lineErrors++;
        return fixedLine;
      }

      return line;
    });

    if (lineErrors > 0) {
      const fixedContent = fixedLines.join('\n');
      fs.writeFileSync(filePath, fixedContent, 'utf8');

      console.log(`${colors.green}✅ ${filename}: ${lineErrors} linhas corrigidas${colors.reset}`);
      filesFixed++;
      totalErrors += lineErrors;
    } else {
      console.log(`${colors.green}✅ ${filename}: Nenhuma correção necessária${colors.reset}`);
    }
  } catch (error) {
    console.error(`${colors.red}❌ Erro ao processar ${filename}: ${error.message}${colors.reset}`);
  }
});

console.log(`\n${colors.bold}📊 Resumo:${colors.reset}`);
console.log(`${colors.green}✅ Arquivos processados: ${envFiles.length}${colors.reset}`);
console.log(`${colors.green}✅ Arquivos corrigidos: ${filesFixed}${colors.reset}`);
console.log(`${colors.green}✅ Total de linhas corrigidas: ${totalErrors}${colors.reset}`);

if (totalErrors > 0) {
  console.log(`\n${colors.yellow}🚀 Próximos passos:${colors.reset}`);
  console.log(`1. Execute: ${colors.blue}vercel --prod${colors.reset}`);
  console.log(`2. O erro "Invalid URL" deve estar resolvido`);
  console.log(`3. Verifique se o deploy funciona corretamente`);
} else {
  console.log(`\n${colors.green}✅ Todos os arquivos já estavam corretos!${colors.reset}`);
}
