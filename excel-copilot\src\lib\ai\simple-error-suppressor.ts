/**
 * Supressor robusto de erros de IA no cliente
 * Este arquivo previne completamente a execução de código de IA no navegador
 */

// Verificar se estamos no cliente
if (typeof window !== 'undefined') {
  console.log('[AI Error Suppressor] Inicializando proteções contra IA no cliente');

  // Bloquear completamente qualquer tentativa de usar APIs do Google
  const blockAIClasses = () => {
    // Mock classes que sempre falham silenciosamente
    const createMockClass = (name: string) => {
      return class MockAIClass {
        constructor(..._args: any[]) {
          console.warn(`[AI Error Suppressor] Bloqueada tentativa de criar ${name} no cliente`);
          // Não fazer nada, apenas existir
        }

        // Métodos que retornam promises rejeitadas silenciosamente
        generateContent() {
          return Promise.resolve({ response: { text: () => 'IA não disponível no cliente' } });
        }
        sendMessage() {
          return Promise.resolve('IA não disponível no cliente');
        }
        getGenerativeModel() {
          return new MockAIClass('GenerativeModel');
        }

        // Propriedades que retornam mocks
        get preview() {
          return { getGenerativeModel: () => new MockAIClass('GenerativeModel') };
        }
      };
    };

    // Substituir classes de IA por mocks
    (window as any).GoogleGenerativeAI = createMockClass('GoogleGenerativeAI');
    (window as any).VertexAI = createMockClass('VertexAI');
    (window as any).GenerativeModel = createMockClass('GenerativeModel');
  };

  // Aplicar bloqueios imediatamente
  blockAIClasses();

  // Interceptar tentativas de import dinâmico
  const originalImport = (window as any).__webpack_require__;
  if (originalImport) {
    (window as any).__webpack_require__ = function (moduleId: any, ...args: any[]) {
      if (
        typeof moduleId === 'string' &&
        (moduleId.includes('@google-cloud/vertexai') ||
          moduleId.includes('@google/generative-ai') ||
          moduleId.includes('google-auth-library'))
      ) {
        console.warn(`[AI Error Suppressor] Bloqueado import de ${moduleId} no cliente`);
        return {
          VertexAI: (window as any).VertexAI,
          GoogleGenerativeAI: (window as any).GoogleGenerativeAI,
        };
      }
      return originalImport.apply(this, [moduleId, ...args]);
    };
  }

  // Interceptar console.error para suprimir erros específicos de IA
  const originalConsoleError = console.error;
  console.error = function (...args) {
    const message = args.join(' ');

    // Lista expandida de padrões de erro para suprimir
    const errorPatterns = [
      'Neither apiKey nor config.authenticator provided',
      '_setAuthenticator',
      'Failed to fetch RSC payload',
      'GoogleGenerativeAI',
      'VertexAI',
      'google-auth-library',
      '@google-cloud/vertexai',
      '@google/generative-ai',
      'genai',
      'generative-ai',
      'aiplatform.googleapis.com',
      'generativelanguage.googleapis.com',
      'ml.googleapis.com',
      'GOOGLE_APPLICATION_CREDENTIALS',
      'service account',
      'authentication failed',
      'API key not provided',
      'authenticator not provided',
      'Rejeição de Promise não tratada',
      'Erro da aplicação',
      'Unhandled Promise Rejection',
      'Application Error',
      'ChunkLoadError',
      'Loading chunk',
      'Loading CSS chunk',
    ];

    // Verificar se é um erro que queremos suprimir
    if (errorPatterns.some(pattern => message.toLowerCase().includes(pattern.toLowerCase()))) {
      // Suprimir o erro silenciosamente
      return;
    }

    // Verificar se é um erro de objeto vazio
    if (
      message === '[object Object]' ||
      message === 'Object' ||
      message.includes('[object Object]')
    ) {
      return;
    }

    // Se não for um erro de IA, usar o console.error original
    return originalConsoleError.apply(this, args);
  };

  // Interceptar unhandled promise rejections
  const originalUnhandledRejection = window.onunhandledrejection;
  window.onunhandledrejection = function (event) {
    const reason = event.reason;
    const reasonStr = String(reason);

    // Usar a mesma lista de padrões para consistência
    const errorPatterns = [
      'Neither apiKey nor config.authenticator provided',
      '_setAuthenticator',
      'Failed to fetch RSC payload',
      'GoogleGenerativeAI',
      'VertexAI',
      'google-auth-library',
      '@google-cloud/vertexai',
      '@google/generative-ai',
      'genai',
      'generative-ai',
      'aiplatform.googleapis.com',
      'generativelanguage.googleapis.com',
      'ml.googleapis.com',
      'GOOGLE_APPLICATION_CREDENTIALS',
      'service account',
      'authentication failed',
      'API key not provided',
      'authenticator not provided',
      'Rejeição de Promise não tratada',
      'Erro da aplicação',
      'Unhandled Promise Rejection',
      'Application Error',
      'ChunkLoadError',
      'Loading chunk',
      'Loading CSS chunk',
    ];

    // Verificar se é um erro que queremos suprimir
    if (errorPatterns.some(pattern => reasonStr.toLowerCase().includes(pattern.toLowerCase()))) {
      // Prevenir que a rejeição apareça no console
      event.preventDefault();
      return;
    }

    // Verificar se é um erro de objeto vazio
    if (
      reasonStr === '[object Object]' ||
      reasonStr === 'Object' ||
      reasonStr.includes('[object Object]')
    ) {
      event.preventDefault();
      return;
    }

    // Se não for um erro de IA, usar o handler original
    if (originalUnhandledRejection) {
      return originalUnhandledRejection.call(window, event);
    }
  };

  console.log('[AI Error Suppressor] Proteções ativadas com sucesso');
}

export {};
