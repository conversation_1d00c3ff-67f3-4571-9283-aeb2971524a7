# 🔐 **INSTRUÇÕES PARA GERAÇÃO DE NOVAS CREDENCIAIS**

## **🚨 AÇÃO OBRIGATÓRIA APÓS AUDITORIA DE SEGURANÇA**

<PERSON>das as credenciais antigas foram **REVOGADAS** por motivos de segurança. Você deve gerar novas credenciais para restaurar a funcionalidade completa da aplicação.

---

## **📋 CREDENCIAIS QUE PRECISAM SER REGENERADAS**

### **✅ 1. NEXTAUTH_SECRET (GERADO)**

```bash
# Novo secret seguro gerado:
NEXTAUTH_SECRET="LkO+IUEkiw+aqZQWHOFUVea8FXM+lBSLSYviUtf4F5c="
```

### **🔄 2. GOOGLE OAUTH (REGENERAR)**

1. Acesse: https://console.cloud.google.com/apis/credentials
2. Selecione seu projeto: `excel-copilot`
3. Vá em "Credenciais" → "IDs do cliente OAuth 2.0"
4. Clique no cliente existente ou crie um novo
5. **REGENERE o Client Secret**
6. Configure URIs autorizados:
   - `http://localhost:3000/api/auth/callback/google` (desenvolvimento)
   - `https://excel-copilot-eight.vercel.app/api/auth/callback/google` (produção)

### **🔄 3. GITHUB OAUTH (REGENERAR)**

1. Acesse: https://github.com/settings/applications
2. Selecione sua aplicação OAuth ou crie uma nova
3. **REGENERE o Client Secret**
4. Configure URLs de callback:
   - `http://localhost:3000/api/auth/callback/github` (desenvolvimento)
   - `https://excel-copilot-eight.vercel.app/api/auth/callback/github` (produção)

### **🔄 4. STRIPE (REGENERAR SE NECESSÁRIO)**

1. Acesse: https://dashboard.stripe.com/apikeys
2. **REVOGUE** as chaves antigas se ainda não fez
3. **GERE** novas chaves:
   - Secret Key (sk*live*... para produção, sk*test*... para desenvolvimento)
   - Publishable Key (pk*live*... para produção, pk*test*... para desenvolvimento)
4. Configure webhooks com nova secret

### **🔄 5. LINEAR MCP (REGENERAR)**

1. Acesse: https://linear.app/settings/api
2. **REVOGUE** o token antigo: `************************************************`
3. **GERE** novo Personal API Token
4. Configure permissões necessárias

### **🔄 6. GITHUB MCP (REGENERAR)**

1. Acesse: https://github.com/settings/tokens
2. **REVOGUE** o token antigo: `****************************************`
3. **GERE** novo Personal Access Token
4. Configure scopes necessários: `repo`, `read:user`, `read:org`

### **🔄 7. VERCEL MCP (REGENERAR)**

1. Acesse: https://vercel.com/account/tokens
2. **REVOGUE** o token antigo: `SnFZRFinjbcwexn461bo32on`
3. **GERE** novo token de API
4. Configure permissões para seu projeto

---

## **⚙️ CONFIGURAÇÃO NO VERCEL**

### **Passo 1: Acessar Dashboard**

1. Acesse: https://vercel.com/dashboard
2. Selecione projeto: `excel-copilot`
3. Vá em "Settings" → "Environment Variables"

### **Passo 2: Remover Variáveis Antigas**

**REMOVA** todas as variáveis com credenciais comprometidas:

- `NEXTAUTH_SECRET` (antiga)
- `GOOGLE_CLIENT_SECRET` (antiga)
- `GITHUB_CLIENT_SECRET` (antiga)
- `STRIPE_SECRET_KEY` (antiga)
- `LINEAR_API_KEY` (antiga)
- `GITHUB_TOKEN` (antiga)
- `VERCEL_API_TOKEN` (antiga)

### **Passo 3: Adicionar Novas Variáveis**

Configure as novas credenciais para **Production**, **Preview** e **Development**:

```bash
# Autenticação
NEXTAUTH_SECRET="LkO+IUEkiw+aqZQWHOFUVea8FXM+lBSLSYviUtf4F5c="
NEXTAUTH_URL="https://excel-copilot-eight.vercel.app"
GOOGLE_CLIENT_ID="************-1gocm6a0sa9jcrk8s08dubqn8n2001lv.apps.googleusercontent.com"
GOOGLE_CLIENT_SECRET="[NOVA_GOOGLE_CLIENT_SECRET]"
GITHUB_CLIENT_ID="c5d97a325b78e452d671"
GITHUB_CLIENT_SECRET="[NOVA_GITHUB_CLIENT_SECRET]"

# Banco de Dados (manter as existentes se não comprometidas)
DATABASE_URL="[SUA_DATABASE_URL]"
DIRECT_URL="[SUA_DIRECT_URL]"
SUPABASE_URL="https://eliuoignzzxnjkcmmtml.supabase.co"
NEXT_PUBLIC_SUPABASE_URL="https://eliuoignzzxnjkcmmtml.supabase.co"
SUPABASE_ANON_KEY="[SUA_SUPABASE_ANON_KEY]"
NEXT_PUBLIC_SUPABASE_ANON_KEY="[SUA_SUPABASE_ANON_KEY]"

# Stripe (se necessário)
STRIPE_SECRET_KEY="[NOVA_STRIPE_SECRET_KEY]"
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY="[NOVA_STRIPE_PUBLISHABLE_KEY]"
STRIPE_WEBHOOK_SECRET="[NOVA_STRIPE_WEBHOOK_SECRET]"

# MCPs
MCP_VERCEL_TOKEN="[NOVO_VERCEL_API_TOKEN]"
MCP_VERCEL_PROJECT_ID="prj_IQemY1bXbDdiiQmHDfRAYLUqMZIg"
MCP_VERCEL_TEAM_ID="team_BLCIn3CF09teqBeBn8u0fLqp"
MCP_LINEAR_API_KEY="[NOVA_LINEAR_API_KEY]"
MCP_GITHUB_TOKEN="[NOVO_GITHUB_TOKEN]"

# IA
AI_ENABLED="true"
AI_USE_MOCK="false"
VERTEX_AI_PROJECT_ID="excel-copilot"
VERTEX_AI_LOCATION="us-central1"
```

---

## **🧪 CONFIGURAÇÃO PARA DESENVOLVIMENTO**

### **Criar .env.local**

1. Copie o template: `cp .env.local.template .env.local`
2. Configure as variáveis essenciais:

```bash
# Mínimo para desenvolvimento
NEXTAUTH_SECRET="LkO+IUEkiw+aqZQWHOFUVea8FXM+lBSLSYviUtf4F5c="
NEXTAUTH_URL="http://localhost:3000"
DATABASE_URL="[SUA_DATABASE_URL_LOCAL_OU_SUPABASE]"
AI_USE_MOCK="true"  # Usar mocks em desenvolvimento
```

---

## **✅ VALIDAÇÃO**

### **Testar Configuração**

```bash
# 1. Verificar segurança
npm run dev  # Deve mostrar validação de segurança

# 2. Testar autenticação
# Acesse: http://localhost:3000
# Teste login com Google/GitHub

# 3. Verificar MCPs
# Teste funcionalidades que usam integrações
```

### **Comandos de Verificação**

```bash
# Verificar se não há credenciais expostas
grep -r "sk_live_\|GOCSPX-\|ghp_" . --exclude-dir=node_modules

# Deve retornar vazio (nenhum resultado)
```

---

## **🚨 IMPORTANTE**

1. **NUNCA** commite arquivos `.env.local` ou `.env.production` com credenciais reais
2. **SEMPRE** use credenciais de teste em desenvolvimento
3. **CONFIGURE** apenas no Vercel Dashboard para produção
4. **TESTE** cada integração após configurar novas credenciais
5. **MONITORE** logs para detectar problemas de autenticação

---

## **📞 SUPORTE**

Se encontrar problemas:

1. Verifique logs do Vercel Dashboard
2. Execute validação de segurança: `npm run dev`
3. Confirme que todas as credenciais foram regeneradas
4. Teste em ambiente local primeiro

**Status:** 🟡 **Aguardando configuração de novas credenciais**
