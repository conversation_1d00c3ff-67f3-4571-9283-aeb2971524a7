/**
 * Utilitário para importação dinâmica de serviços apenas no servidor.
 * Este módulo gerencia o carregamento de serviços que só podem ser executados no servidor.
 */

import { ENV } from '@/config/unified-environment';

import { logger } from '../logger';

import type { AnalysisResult, FormulaResult, SendMessageOptions } from './gemini-api';

/**
 * Interface para serviço AI carregado dinamicamente
 */
export interface DynamicAIService {
  sendMessage(message: string, options?: SendMessageOptions): Promise<string>;
  analyzeExcelData(data: any, options?: SendMessageOptions): Promise<AnalysisResult>;
  generateExcelFormula(description: string, options?: SendMessageOptions): Promise<FormulaResult>;
  healthCheck(): Promise<boolean>;
  shutdown(): Promise<void>;
}

/**
 * Adapta o serviço VertexAI para a interface DynamicAIService
 */
class VertexAIAdapter implements DynamicAIService {
  private vertexService: any; // Tipo real será VertexAIService

  constructor(vertexService: any) {
    this.vertexService = vertexService;
  }

  async sendMessage(message: string, options?: SendMessageOptions): Promise<string> {
    return this.vertexService.generateText(message, {
      temperature: options?.temperature,
      maxOutputTokens: options?.responseStructure?.preferConcise ? 1024 : 2048,
    });
  }

  async analyzeExcelData(data: any, _options?: SendMessageOptions): Promise<AnalysisResult> {
    // Formatar os dados do Excel para um prompt adequado
    const dataStr = typeof data === 'string' ? data : JSON.stringify(data);
    const prompt = `Analise os seguintes dados do Excel e forneça insights, resumo e possíveis operações:\n\n${dataStr}`;

    try {
      const response = await this.vertexService.generateText(prompt, {
        temperature: 0.2,
        maxOutputTokens: 2048,
      });

      // Tentar extrair um JSON com os campos esperados da resposta
      try {
        // Verificar se a resposta contém JSON
        const jsonMatch =
          response.match(/```(?:json)?\s*([\s\S]*?)\s*```/) || response.match(/(\{[\s\S]*?\})/);
        if (jsonMatch && jsonMatch[1]) {
          const result = JSON.parse(jsonMatch[1]);
          return {
            insights: result.insights || [],
            suggestedOperations: result.suggestedOperations || [],
            summary: result.summary || 'Análise concluída',
          };
        }
      } catch (parseError) {
        // Se falhar ao extrair JSON, retornar resposta em formato legível
        logger.warn(
          'Falha ao extrair JSON da resposta de análise:',
          parseError instanceof Error ? parseError : undefined
        );
      }

      // Formato padrão caso não consiga extrair JSON
      return {
        insights: [response],
        suggestedOperations: [],
        summary: 'Análise concluída',
      };
    } catch (error) {
      logger.error('Erro na análise de dados Excel:', error instanceof Error ? error : undefined);
      return {
        insights: [
          `Erro ao analisar dados: ${error instanceof Error ? error.message : String(error)}`,
        ],
        suggestedOperations: [],
        summary: 'Ocorreu um erro durante a análise',
      };
    }
  }

  async generateExcelFormula(
    description: string,
    _options?: SendMessageOptions
  ): Promise<FormulaResult> {
    const prompt = `Crie uma fórmula do Excel para: ${description}\nRetorne apenas a fórmula e uma breve explicação.`;

    try {
      const response = await this.vertexService.generateText(prompt, {
        temperature: 0.2,
        maxOutputTokens: 1024,
      });

      // Exemplo: procurar por fórmulas Excel no formato =FUNCAO(...)
      const formulaMatch = response.match(/=[\w\d\s()+\-*/:.,]+/i);
      const formula = formulaMatch ? formulaMatch[0] : '';

      return {
        formula,
        explanation: formula ? response.replace(formula, '').trim() : response,
      };
    } catch (error) {
      logger.error('Erro ao gerar fórmula Excel:', error instanceof Error ? error : undefined);
      return {
        formula: '',
        explanation: `Erro: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  }

  async healthCheck(): Promise<boolean> {
    try {
      // Verificar se o serviço está inicializado
      const status = await this.vertexService.generateText('teste de conexão', {
        maxOutputTokens: 10,
      });
      return !!status;
    } catch (error) {
      logger.error('Erro no healthcheck do VertexAI:', error instanceof Error ? error : undefined);
      return false;
    }
  }

  async shutdown(): Promise<void> {
    // O serviço Vertex não tem método shutdown explícito
    logger.info('VertexAI adapter shutdown');
    return Promise.resolve();
  }
}

/**
 * Adapta o serviço Gemini para a interface DynamicAIService
 */
class GeminiAdapter implements DynamicAIService {
  private geminiService: any; // Tipo real será GeminiService

  constructor(geminiService: any) {
    this.geminiService = geminiService;
  }

  async sendMessage(message: string, options?: SendMessageOptions): Promise<string> {
    return this.geminiService.sendMessage(message, [], options);
  }

  async analyzeExcelData(data: any, options?: SendMessageOptions): Promise<AnalysisResult> {
    const dataStr = typeof data === 'string' ? data : JSON.stringify(data);
    const prompt = `Analise os seguintes dados do Excel e forneça insights, resumo e possíveis operações:\n\n${dataStr}`;

    try {
      const response = await this.geminiService.sendMessage(prompt, [], {
        ...options,
        temperature: 0.2,
        responseStructure: {
          ...options?.responseStructure,
          preferJson: true,
        },
      });

      // O serviço Gemini já retorna JSON estruturado se solicitado
      try {
        const result = JSON.parse(response);
        return {
          insights: result.insights || [],
          suggestedOperations: result.suggestedOperations || [],
          summary: result.summary || 'Análise concluída',
        };
      } catch {
        // Fallback para resposta em texto
        return {
          insights: [response],
          suggestedOperations: [],
          summary: 'Análise concluída',
        };
      }
    } catch (error) {
      logger.error(
        'Erro na análise de dados Excel com Gemini:',
        error instanceof Error ? error : undefined
      );
      return {
        insights: [
          `Erro ao analisar dados: ${error instanceof Error ? error.message : String(error)}`,
        ],
        suggestedOperations: [],
        summary: 'Ocorreu um erro durante a análise',
      };
    }
  }

  async generateExcelFormula(
    description: string,
    options?: SendMessageOptions
  ): Promise<FormulaResult> {
    const prompt = `Crie uma fórmula do Excel para: ${description}\nRetorne apenas a fórmula e uma breve explicação.`;

    try {
      const response = await this.geminiService.sendMessage(prompt, [], {
        ...options,
        temperature: 0.2,
      });

      // Exemplo: procurar por fórmulas Excel no formato =FUNCAO(...)
      const formulaMatch = response.match(/=[\w\d\s()+\-*/:.,]+/i);
      const formula = formulaMatch ? formulaMatch[0] : '';

      return {
        formula,
        explanation: formula ? response.replace(formula, '').trim() : response,
      };
    } catch (error) {
      logger.error(
        'Erro ao gerar fórmula Excel com Gemini:',
        error instanceof Error ? error : undefined
      );
      return {
        formula: '',
        explanation: `Erro: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  }

  async healthCheck(): Promise<boolean> {
    try {
      // O serviço Gemini não tem método dedicado de healthcheck
      await this.geminiService.sendMessage('teste', [], { bypassCache: true });
      return true;
    } catch (error) {
      logger.error('Erro no healthcheck do Gemini:', error instanceof Error ? error : undefined);
      return false;
    }
  }

  async shutdown(): Promise<void> {
    try {
      await this.geminiService.shutdown();
    } catch (error) {
      logger.error('Erro ao fazer shutdown do Gemini:', error instanceof Error ? error : undefined);
    }
  }
}

/**
 * Carrega o serviço de IA dinamicamente de acordo com a configuração
 */
export async function loadAIService(): Promise<DynamicAIService | null> {
  // BLOQUEIO ABSOLUTO NO CLIENTE - NUNCA EXECUTAR IA NO NAVEGADOR
  if (typeof window !== 'undefined') {
    console.warn('[AI Service] Tentativa de carregar IA no cliente bloqueada - retornando null');
    return null;
  }

  // PRODUÇÃO: Remover completamente a lógica de mocks
  // Sempre usar serviços reais de IA

  // Continuar com a tentativa de carregamento real dos serviços
  try {
    // Verificar se Vertex AI está configurado e habilitado
    if (ENV.VERTEX_AI.ENABLED) {
      logger.info('Tentando carregar VertexAI Service');

      try {
        // Carregar dinamicamente o serviço VertexAI (apenas no lado do servidor)
        const { VertexAIService } = await import('@/server/ai/vertex-ai-service');
        const vertexService = VertexAIService.getInstance();
        logger.info('VertexAI Service carregado com sucesso');
        return new VertexAIAdapter(vertexService);
      } catch (error) {
        logger.error(
          'Erro ao carregar VertexAI Service:',
          error instanceof Error ? error : undefined
        );
        throw error;
      }
    }

    // Fallback para Gemini se Vertex AI falhar ou não estiver habilitado
    logger.info('Tentando carregar Gemini Service');
    try {
      // Carregar dinamicamente o serviço Gemini (apenas no lado do servidor)
      const { GeminiService } = await import('@/server/ai/gemini-service');
      const geminiService = GeminiService.getInstance();
      logger.info('Gemini Service carregado com sucesso');
      return new GeminiAdapter(geminiService);
    } catch (error) {
      logger.error('Erro ao carregar Gemini Service:', error instanceof Error ? error : undefined);
      logger.error('Todos os serviços de IA falharam ao carregar');
      return null;
    }
  } catch (error) {
    logger.error(
      'Erro global ao carregar serviços de AI:',
      error instanceof Error ? error : undefined
    );
    return null;
  }
}

/**
 * Singleton para gerenciar a instância carregada dinamicamente
 */
class DynamicImportManager {
  private static instance: DynamicImportManager;
  private servicePromise: Promise<DynamicAIService | null> | null = null;

  private constructor() {}

  public static getInstance(): DynamicImportManager {
    if (!DynamicImportManager.instance) {
      DynamicImportManager.instance = new DynamicImportManager();
    }
    return DynamicImportManager.instance;
  }

  /**
   * Obtém o serviço AI, carregando-o dinamicamente se necessário
   */
  public getAIService(): Promise<DynamicAIService | null> {
    // Se já temos uma promise pendente, retorna ela
    if (this.servicePromise) {
      return this.servicePromise;
    }

    // Se estamos no navegador, retorna null imediatamente
    if (typeof window !== 'undefined') {
      return Promise.resolve(null);
    }

    // Iniciar carregamento dinâmico
    this.servicePromise = loadAIService();
    return this.servicePromise;
  }
}

// Exportar gerenciador singleton
export const dynamicImportManager = DynamicImportManager.getInstance();
