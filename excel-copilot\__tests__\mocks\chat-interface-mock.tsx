import React from 'react';

// Mock para o componente ChatInterface
const ChatInterfaceMock: React.FC<{
  workbookId?: string;
  onQuerySubmit?: (query: string) => void;
}> = ({ workbookId, onQuerySubmit }) => {
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (onQuerySubmit) {
      onQuerySubmit('Calcular média da coluna A');
    }
  };

  // Implementação mock que evita o uso de scrollIntoView
  const messagesEndRef = React.useRef<HTMLDivElement>(null);

  React.useEffect(() => {
    // Mock simples para substituir scrollIntoView
    const scrollToBottom = () => {
      console.log('Mock scrollIntoView called');
    };

    scrollToBottom();
  }, []);

  return (
    <div data-testid="chat-interface" className="flex flex-col h-[400px]">
      <div data-testid="chat-messages" className="flex-1 overflow-y-auto p-4">
        <div className="mb-4">
          <div data-testid="ai-message" className="bg-blue-100 p-3 rounded-lg">
            Olá! Como posso ajudar com seus dados hoje?
          </div>
        </div>
        <div ref={messagesEndRef} />
      </div>
      <div data-testid="chat-input-area" className="border-t p-2">
        <form className="flex gap-2" onSubmit={handleSubmit}>
          <input
            type="text"
            data-testid="chat-input"
            className="flex-1 p-2 border rounded"
            placeholder="Digite uma instrução..."
          />
          <button type="submit" data-testid="send-button">
            Enviar
          </button>
        </form>
      </div>
    </div>
  );
};

export default ChatInterfaceMock;
