import { NextRequest } from 'next/server';

import { ENV } from '@/config/unified-environment';
import { logger } from '@/lib/logger';
import { StripeMonitoringService } from '@/lib/stripe-integration';
import { ApiResponse } from '@/utils/api-response';

// Configurar rota como dinâmica
export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

/**
 * GET /api/stripe/status
 * Obtém status geral do Stripe e métricas de negócio
 */
export async function GET(_request: NextRequest) {
  try {
    // Verificar se temos as credenciais necessárias
    const apiKey = process.env.STRIPE_SECRET_KEY;
    const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;

    if (!apiKey) {
      return ApiResponse.error('STRIPE_SECRET_KEY não configurado', 'STRIPE_NOT_CONFIGURED', 500);
    }

    // Criar instância do serviço de monitoramento
    const stripeService = new StripeMonitoringService({
      apiKey,
      ...(webhookSecret && { webhookSecret }),
    });

    // Obter status do negócio
    const businessStatus = await stripeService.getBusinessStatus();

    // Preparar resposta
    const response = {
      service: {
        name: 'stripe',
        environment: ENV.IS_PRODUCTION ? 'production' : 'development',
        status: businessStatus.status,
        message: businessStatus.message,
        webhookConfigured: !!webhookSecret,
      },
      business: {
        revenue: {
          total: businessStatus.metrics.revenue.total,
          currency: businessStatus.metrics.revenue.currency,
          growth: businessStatus.metrics.revenue.growth,
          mrr: businessStatus.metrics.mrr,
          arpu: businessStatus.metrics.arpu,
        },
        customers: {
          total: businessStatus.metrics.customers.total,
          new: businessStatus.metrics.customers.new,
          churn: businessStatus.metrics.customers.churn,
        },
        subscriptions: {
          total: businessStatus.metrics.subscriptions.total,
          active: businessStatus.metrics.subscriptions.active,
          trialing: businessStatus.metrics.subscriptions.trialing,
          pastDue: businessStatus.metrics.subscriptions.pastDue,
          canceled: businessStatus.metrics.subscriptions.canceled,
        },
        payments: {
          successful: businessStatus.metrics.payments.successful,
          failed: businessStatus.metrics.payments.failed,
          refunded: businessStatus.metrics.payments.refunded,
          successRate: businessStatus.metrics.payments.successRate,
        },
      },
      recentActivity: businessStatus.recentActivity,
      alerts: businessStatus.alerts,
      timestamp: new Date().toISOString(),
    };

    logger.info('Status Stripe obtido com sucesso', {
      status: businessStatus.status,
      revenue: businessStatus.metrics.revenue.total,
      customers: businessStatus.metrics.customers.total,
    });

    return ApiResponse.success(response);
  } catch (error) {
    logger.error('Erro ao obter status do Stripe', { error });

    if (error instanceof Error) {
      return ApiResponse.error(
        `Erro ao conectar com Stripe: ${error.message}`,
        'STRIPE_CONNECTION_ERROR',
        500
      );
    }

    return ApiResponse.error('Erro interno do servidor', 'INTERNAL_ERROR', 500);
  }
}

/**
 * POST /api/stripe/status
 * Força uma verificação de status (útil para debugging)
 */
export async function POST(_request: NextRequest) {
  try {
    const apiKey = process.env.STRIPE_SECRET_KEY;
    const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;

    if (!apiKey) {
      return ApiResponse.error('STRIPE_SECRET_KEY não configurado', 'STRIPE_NOT_CONFIGURED', 500);
    }

    const stripeService = new StripeMonitoringService({
      apiKey,
      ...(webhookSecret && { webhookSecret }),
    });

    // Obter análise detalhada de receita
    const revenueAnalysis = await stripeService.getRevenueAnalysis('30d');

    // Obter métricas específicas do Excel Copilot
    const excelCopilotMetrics = await stripeService.getExcelCopilotMetrics();

    const response = {
      revenue: revenueAnalysis,
      excelCopilot: excelCopilotMetrics,
      timestamp: new Date().toISOString(),
    };

    return ApiResponse.success(response);
  } catch (error) {
    logger.error('Erro na verificação forçada do Stripe', { error });
    return ApiResponse.error('Erro ao verificar status', 'STRIPE_STATUS_ERROR', 500);
  }
}
