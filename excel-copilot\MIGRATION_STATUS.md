# Status da Migração Pages Router → App Router

## Visão Geral

Este documento acompanha a migração da arquitetura de roteamento do Excel Copilot do Next.js Pages Router para o App Router. A migração está sendo realizada de forma gradual para minimizar o impacto nos usuários e na estabilidade do sistema.

## Status Atual

- ✅ **Estrutura App Router Implementada**: Arquitetura básica do App Router já está em funcionamento
- ✅ **Redirecionador de API Legacy**: Sistema para redirecionar chamadas API antigas para novas rotas
- ✅ **Middleware de Redirecionamento**: Configurado para redirecionamento transparente
- ✅ **Sistema de Rastreamento de Uso**: Implementado rastreamento de uso de APIs legadas
- ✅ **Dashboard de Depreciação**: Endpoint para visualizar estatísticas de uso de rotas legadas
- ✅ **Utilitários de Migração**: Ferramentas para converter handlers do Pages Router para App Router

## Etapas Concluídas

1. **Estrutura Inicial**:

   - Criação da pasta `app/` com estrutura de rotas principais
   - Implementação de layouts compartilhados e providers

2. **Configuração de Redirecionamento**:

   - Implementado middleware para redirecionamento de rotas
   - Configurado `next.config.js` com redirecionamentos
   - Criado handler para `/api/legacy-redirect/[...path]` para compatibilidade com APIs antigas

3. **Sistema de Rastreamento e Depreciação**:

   - Implementado `LegacyApiTracker` para registrar uso de APIs legadas
   - Criado endpoint `/api/deprecated-usage` para administradores
   - Adicionado headers de depreciação nas respostas de APIs legadas

4. **Utilitários de Migração**:
   - Implementado `utils/route-migration.ts` com ferramentas de conversão de handlers
   - Criado helper `deprecateHandler` para adicionar avisos em rotas do Pages Router
   - Documentado processo de migração com exemplos

## Em Andamento

1. **Migração de Rotas API**:

   - Migração gradual das APIs existentes para o formato App Router
   - Implementação de testes para garantir comportamento consistente

2. **Linting e Validação**:
   - Implementação de regras ESLint para detectar uso de padrões legados
   - Validação automática em CI para garantir conformidade

## Próximos Passos

1. **Remoção Gradual de Pages**:

   - Depreciar a pasta `pages/` gradualmente
   - Criar redirecionamentos 301 para rotas migradas

2. **Documentação**:

   - Atualizar a documentação para refletir a nova estrutura de roteamento
   - Guia de migração para consumidores da API

3. **Automação**:
   - Ferramentas CLI para migração automática de endpoints simples
   - Scripts para validação de compatibilidade entre implementações

## Mapeamento de Rotas

| Rota Antiga (Pages) | Rota Nova (App)                     | Status         |
| ------------------- | ----------------------------------- | -------------- |
| `/api/*`            | `/api/legacy-redirect/*` → `/api/*` | ✅ Configurado |

## Instruções para Desenvolvedores

### Como Adicionar Novas Rotas

Todas as novas rotas devem ser adicionadas usando o padrão App Router:

```typescript
// Em app/api/exemplo/route.ts
import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  return NextResponse.json({ message: 'Exemplo de rota App Router' });
}
```

### Como Migrar uma Rota Existente

1. Crie a nova rota no formato App Router:

   ```typescript
   // Em app/api/usuarios/route.ts
   import { NextRequest, NextResponse } from 'next/server';
   import { createAppRouteHandlers } from '@/utils/route-migration';
   import { handler } from '@/pages/api/usuarios';

   // Opção 1: Migração manual
   export async function GET(request: NextRequest) {
     // Implementação do novo handler usando o padrão App Router
   }

   // Opção 2: Migração automática usando o utilitário
   export const { GET, POST, PUT, DELETE } = createAppRouteHandlers(handler);
   ```

2. Adicione testes para garantir comportamento equivalente

3. Adicione aviso de depreciação no handler antigo:

   ```typescript
   // Em pages/api/usuarios.ts
   import { NextApiRequest, NextApiResponse } from 'next';
   import { deprecateHandler } from '@/utils/route-migration';

   async function originalHandler(req: NextApiRequest, res: NextApiResponse) {
     // Implementação original
   }

   // Exportar handler com aviso de depreciação
   export default deprecateHandler(originalHandler, '/api/usuarios');
   ```

4. Configure redirecionamento se necessário em `next.config.js`

### Monitoramento de Uso

Para visualizar estatísticas de uso de APIs legadas, acesse `/api/deprecated-usage` (apenas para administradores).

## Referências

- [Next.js App Router Documentation](https://nextjs.org/docs/app)
- [Migration Guide from Pages to App](https://nextjs.org/docs/app/building-your-application/upgrading/app-router-migration)
