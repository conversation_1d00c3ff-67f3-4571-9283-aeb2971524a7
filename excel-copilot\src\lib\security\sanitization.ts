/**
 * <PERSON>ódulo de sanitização de entrada para prevenir vulnerabilidades de segurança
 * Implementa funções para sanitizar diferentes tipos de dados e prevenir ataques
 * como XSS, injeção SQL, etc.
 */

import DOMPurify from 'dompurify';
import { JSDOM } from 'jsdom';
import { z } from 'zod';

import { ErrorType, AppError } from '../errors';

const window = new JSDOM('').window;
const purify = DOMPurify(window);

/**
 * Interface para resultado de validação
 */
export interface SanitizationResult<T = unknown> {
  isValid: boolean;
  sanitized?: T;
  originalValue?: unknown;
  errors?: string[];
}

/**
 * Propriedades aceitas para sanitização HTML
 * Baseado na interface Config do DOMPurify
 */
export interface SanitizeOptions {
  /** Permitir elementos específicos */
  ALLOWED_TAGS?: string[];
  /** Permitir atributos específicos */
  ALLOWED_ATTR?: string[];
  /** Preservar as estruturas de dados de entrada */
  KEEP_CONTENT?: boolean;
  /** Atributos proibidos como array de strings */
  FORBID_ATTR?: string[];
  /** Tags proibidas como array de strings */
  FORBID_TAGS?: string[];
  /** Remover todos os atributos de eventos */
  FORBID_EVENTS?: boolean;
  /** Outras opções do DOMPurify */
  [key: string]: boolean | string[] | undefined;
}

/**
 * Configuração padrão para sanitização HTML
 * Apenas tags básicas e formatação simples permitidos
 */
const _DEFAULT_OPTIONS: SanitizeOptions = {
  ALLOWED_TAGS: [
    'a',
    'b',
    'br',
    'code',
    'div',
    'em',
    'h1',
    'h2',
    'h3',
    'h4',
    'h5',
    'h6',
    'hr',
    'i',
    'li',
    'ol',
    'p',
    'pre',
    'span',
    'strong',
    'table',
    'tbody',
    'td',
    'th',
    'thead',
    'tr',
    'ul',
  ],
  ALLOWED_ATTR: ['class', 'href', 'id', 'style', 'target', 'title'],
  KEEP_CONTENT: true,
  FORBID_EVENTS: true,
};

/**
 * Sanitiza HTML para prevenir XSS
 * @param unsafeHtml HTML não confiável para ser sanitizado
 * @param options Opções de configuração para DOMPurify
 * @returns HTML sanitizado seguro
 */
export const sanitizeHtml = (html: string): string => {
  if (!html) return '';
  return purify.sanitize(html, {
    ALLOWED_TAGS: ['b', 'i', 'em', 'strong', 'a', 'p', 'ul', 'ol', 'li', 'code', 'pre'],
    ALLOWED_ATTR: ['href', 'target', 'rel'],
  });
};

/**
 * Remove tags HTML de uma string
 * @param input String para limpar
 * @returns String sem tags HTML
 */
export function stripHtml(input: string): string {
  if (!input || typeof input !== 'string') return '';

  // Remover todas as tags HTML
  return input.replace(/<[^>]*>/g, '');
}

/**
 * Sanitiza string para SQL prevenindo injeção
 * @param input String para sanitizar
 * @returns String sanitizada
 */
export function sanitizeSql(input: string): string {
  if (!input) return '';

  return String(input)
    .replace(/'/g, "''")
    .replace(/\\/g, '\\\\')
    .replace(/;/g, '\\;')
    .replace(/--/g, '\\-\\-')
    .replace(/\/\*/g, '\\/\\*')
    .replace(/\*\//g, '\\*\\/');
}

/**
 * Verifica se uma string contém apenas caracteres alfanuméricos e alguns símbolos específicos
 * @param input String para verificar
 * @param allowedChars Caracteres adicionais permitidos além de alfanuméricos
 * @returns Verdadeiro se a string for segura
 */
export function isAlphanumericSafe(input: string, allowedChars: string = '-_. '): boolean {
  if (!input || typeof input !== 'string') return false;

  const safeRegex = new RegExp(
    `^[a-zA-Z0-9${allowedChars.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&')}]+$`
  );
  return safeRegex.test(input);
}

/**
 * Verifica se uma string contém padrões suspeitos que podem indicar tentativas de injeção
 * @param input String para verificar
 * @returns Verdadeiro se encontrar padrões suspeitos
 */
export function hasSuspiciousPatterns(input: string): boolean {
  if (!input) return false;

  const suspiciousPatterns = [
    new RegExp('((%3C)|<)((%2F)|/)*[a-z0-9%]+((%3E)|>)', 'i'),
    new RegExp('javascript:', 'i'),
    new RegExp('data:text/html', 'i'),
    new RegExp('vbscript:', 'i'),
    new RegExp('onclick', 'i'),
    new RegExp('onload', 'i'),
    new RegExp('onerror', 'i'),
  ];

  return suspiciousPatterns.some(pattern => pattern.test(input));
}

/**
 * Valida e sanitiza objetos usando Zod
 * @param input Dados para validar e sanitizar
 * @param schema Schema Zod para validação
 * @returns Resultado da sanitização
 */
export function validateAndSanitize<T>(input: unknown, schema: z.Schema<T>): SanitizationResult<T> {
  try {
    const result = schema.safeParse(input);

    if (result.success) {
      return {
        isValid: true,
        sanitized: result.data,
        originalValue: input,
      };
    } else {
      return {
        isValid: false,
        originalValue: input,
        errors: result.error.errors.map(e => `${e.path.join('.')}: ${e.message}`),
      };
    }
  } catch (error) {
    return {
      isValid: false,
      originalValue: input,
      errors: [error instanceof Error ? error.message : 'Erro de validação desconhecido'],
    };
  }
}

/**
 * Sanitiza recursivamente todas as strings em um objeto
 * @param obj Objeto para sanitizar
 * @returns Objeto com todas as strings sanitizadas
 */
export function deepSanitizeObject<T extends Record<string, unknown>>(obj: T): T {
  if (!obj || typeof obj !== 'object') return obj;

  const result: Record<string, unknown> = {};

  for (const [key, value] of Object.entries(obj)) {
    if (typeof value === 'string') {
      result[key] = sanitizeHtml(value);
    } else if (Array.isArray(value)) {
      result[key] = value.map(item =>
        typeof item === 'string'
          ? sanitizeHtml(item)
          : typeof item === 'object' && item !== null
            ? deepSanitizeObject(item as Record<string, unknown>)
            : item
      );
    } else if (typeof value === 'object' && value !== null) {
      result[key] = deepSanitizeObject(value as Record<string, unknown>);
    } else {
      result[key] = value;
    }
  }

  return result as T;
}

/**
 * Cria uma função de validação compatível com o middleware withValidation
 * @param schema Schema Zod para validação
 * @returns Função de validação
 */
export function createZodValidator<T>(schema: z.Schema<T>) {
  return (body: unknown) => {
    const result = validateAndSanitize(body, schema);

    return {
      isValid: result.isValid,
      data: result.sanitized,
      errors: result.errors,
      message: result.errors?.join('; '),
    };
  };
}

/**
 * Sanitiza URL para prevenir injeção de script
 * @param url URL para sanitizar
 * @returns URL sanitizada ou null se for inválida
 */
export function sanitizeUrl(url: string): string | null {
  if (!url || typeof url !== 'string') return null;

  // Verificar protocolos permitidos
  const validProtocols = ['http:', 'https:', 'mailto:', 'tel:'];

  try {
    const parsedUrl = new URL(url);
    if (!validProtocols.includes(parsedUrl.protocol)) {
      return null;
    }

    return url;
  } catch {
    // Se não for uma URL válida, verificar se é uma rota relativa
    if (url.startsWith('/') && !url.includes(':')) {
      return url;
    }

    return null;
  }
}

/**
 * Verifica se uma string possui tamanho válido
 * @param input String para verificar
 * @param minLength Tamanho mínimo (padrão: 1)
 * @param maxLength Tamanho máximo (padrão: 1000)
 * @returns Verdadeiro se o tamanho estiver dentro dos limites
 */
export function hasValidLength(
  input: string,
  minLength: number = 1,
  maxLength: number = 1000
): boolean {
  if (typeof input !== 'string') return false;
  return input.length >= minLength && input.length <= maxLength;
}

/**
 * Sanitiza nome de arquivo para evitar traversal de diretório
 * @param filename Nome do arquivo para sanitizar
 * @returns Nome de arquivo sanitizado
 */
export function sanitizeFilename(filename: string): string {
  if (!filename || typeof filename !== 'string') return '';

  // Remover caracteres perigosos e limitar a caracteres seguros
  return filename
    .replace(/[/\\]/g, '_') // Substituir / e \ por _
    .replace(/../g, '_') // Substituir .. por _
    .replace(/[&<>:"/|?*]/g, '_') // Substituir outros caracteres inválidos
    .slice(0, 255); // Limitar tamanho
}

/**
 * Sanitiza dados de formulário com base em regras
 * @param data Dados do formulário
 * @param fields Regras de validação
 * @returns Dados sanitizados
 */
export function sanitizeFormData(
  data: Record<string, unknown>,
  fields: Record<
    string,
    {
      required?: boolean;
      type?: 'string' | 'number' | 'boolean' | 'email' | 'url' | 'date';
      maxLength?: number;
      minLength?: number;
      pattern?: RegExp;
    }
  >
): Record<string, unknown> {
  const sanitized: Record<string, unknown> = {};
  const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  const urlRegex = /^(https?:\/\/)?([\w-]+\.)+[a-z]{2,}(\/[\w\-.~!$&'()*+,;=:@%?]*)*$/i;

  for (const [field, rules] of Object.entries(fields)) {
    // Verificar campos obrigatórios
    if (
      rules.required &&
      (data[field] === undefined || data[field] === null || data[field] === '')
    ) {
      throw new AppError(`O campo ${field} é obrigatório`, ErrorType.VALIDATION_ERROR, 400);
    }

    // Se o campo não existe e não é obrigatório, continuar
    if (data[field] === undefined) continue;

    const value = data[field];

    // Sanitização baseada no tipo
    if (rules.type === 'string') {
      const stringValue = String(value);
      sanitized[field] = sanitizeHtml(stringValue);

      // Verificar comprimento
      if (rules.maxLength && stringValue.length > rules.maxLength) {
        throw new AppError(
          `O campo ${field} excede o tamanho máximo permitido`,
          ErrorType.VALIDATION_ERROR,
          400
        );
      }

      if (rules.minLength && stringValue.length < rules.minLength) {
        throw new AppError(
          `O campo ${field} não atinge o tamanho mínimo necessário`,
          ErrorType.VALIDATION_ERROR,
          400
        );
      }

      // Verificar padrão
      if (rules.pattern && !rules.pattern.test(stringValue)) {
        throw new AppError(
          `O valor do campo ${field} não corresponde ao padrão exigido`,
          ErrorType.VALIDATION_ERROR,
          400
        );
      }
    } else if (rules.type === 'number') {
      const numValue = Number(value);
      if (isNaN(numValue)) {
        throw new AppError(`O campo ${field} deve ser um número`, ErrorType.VALIDATION_ERROR, 400);
      }
      sanitized[field] = numValue;
    } else if (rules.type === 'boolean') {
      if (typeof value === 'string') {
        sanitized[field] = value.toLowerCase() === 'true';
      } else {
        sanitized[field] = Boolean(value);
      }
    } else if (rules.type === 'email') {
      const emailValue = String(value).trim().toLowerCase();
      if (!emailRegex.test(emailValue)) {
        throw new AppError(
          `O campo ${field} deve ser um email válido`,
          ErrorType.VALIDATION_ERROR,
          400
        );
      }
      sanitized[field] = emailValue;
    } else if (rules.type === 'url') {
      const urlValue = String(value).trim();
      if (!urlRegex.test(urlValue)) {
        throw new AppError(
          `O campo ${field} deve ser uma URL válida`,
          ErrorType.VALIDATION_ERROR,
          400
        );
      }
      sanitized[field] = sanitizeUrl(urlValue);
    } else if (rules.type === 'date') {
      const date = new Date(value as string | number | Date);
      if (isNaN(date.getTime())) {
        throw new AppError(
          `O campo ${field} deve ser uma data válida`,
          ErrorType.VALIDATION_ERROR,
          400
        );
      }
      sanitized[field] = date.toISOString();
    } else {
      // Para tipos não especificados, sanitizar como string
      if (typeof value === 'string') {
        sanitized[field] = sanitizeHtml(value);
      } else if (typeof value === 'object' && value !== null) {
        sanitized[field] = deepSanitizeObject(value as Record<string, unknown>);
      } else {
        sanitized[field] = value;
      }
    }
  }

  return sanitized;
}

/**
 * Sanitiza fórmulas Excel para prevenir injeções
 * @param formula Fórmula para sanitizar
 * @returns Objeto com resultado da sanitização
 */
export function sanitizeExcelFormula(formula: string | null): {
  isSafe: boolean;
  sanitized: string | null;
  reason?: string;
} {
  if (formula === null) {
    return { isSafe: true, sanitized: null };
  }

  // Lista de funções permitidas
  const allowedFunctions = [
    'SUM',
    'AVERAGE',
    'COUNT',
    'MAX',
    'MIN',
    'IF',
    'AND',
    'OR',
    'NOT',
    'VLOOKUP',
    'HLOOKUP',
    'INDEX',
    'MATCH',
    'CONCATENATE',
    'LEN',
    'LEFT',
    'RIGHT',
    'MID',
    'TRIM',
    'LOWER',
    'UPPER',
    'PROPER',
    'TODAY',
    'NOW',
    'DATE',
    'YEAR',
    'MONTH',
    'DAY',
    'ROUND',
    'ROUNDUP',
    'ROUNDDOWN',
    'INT',
    'ABS',
  ];

  // Rejeitar fórmulas que não começam com '='
  if (!formula.startsWith('=')) {
    return {
      isSafe: false,
      sanitized: null,
      reason: 'A fórmula deve começar com "="',
    };
  }

  // Verificar se contém caracteres de controle
  if (containsControlCharacters(formula)) {
    return {
      isSafe: false,
      sanitized: null,
      reason: 'A fórmula contém caracteres de controle não permitidos',
    };
  }

  // Extrair funções usadas na fórmula
  const usedFunctions = formula.match(/[A-Z][A-Z0-9]*/g) || [];

  // Verificar se todas as funções usadas são permitidas
  for (const func of usedFunctions) {
    if (func.length > 2 && !allowedFunctions.includes(func)) {
      return {
        isSafe: false,
        sanitized: null,
        reason: `A função "${func}" não é permitida`,
      };
    }
  }

  // Verificar se contém padrões de injeção
  if (formula.includes('!') || formula.includes('//') || formula.includes('/*')) {
    return {
      isSafe: false,
      sanitized: null,
      reason: 'A fórmula contém padrões suspeitos',
    };
  }

  // Verificar se contém URLs ou links externos
  if (formula.includes('http') || formula.includes('www') || formula.includes('.com')) {
    return {
      isSafe: false,
      sanitized: null,
      reason: 'A fórmula contém URLs ou links externos',
    };
  }

  // Remover espaços excessivos
  const sanitized = formula.replace(/\s+/g, ' ').trim();

  return { isSafe: true, sanitized };
}

/**
 * Verifica se um texto contém caracteres de controle
 * @param text Texto para verificar
 * @returns true se o texto contiver caracteres de controle
 */
function containsControlCharacters(text: string): boolean {
  if (!text) return false;

  // Verifica se há algum caractere com código entre 0-31 (exceto espaços, tabs, quebras de linha)
  for (let i = 0; i < text.length; i++) {
    const code = text.charCodeAt(i);
    // Excluir caracteres permitidos: tab (9), nova linha (10), retorno (13)
    if (code < 32 && code !== 9 && code !== 10 && code !== 13) {
      return true;
    }
  }
  return false;
}

/**
 * Remove caracteres de controle de um texto
 * @param text Texto para sanitizar
 * @returns Texto sem caracteres de controle
 */
function removeControlCharacters(text: string): string {
  if (!text) return '';

  // Remove caracteres de controle (0-31) exceto espaços, tabs, quebras de linha
  return text
    .split('')
    .map(char => {
      const code = char.charCodeAt(0);
      return code < 32 && code !== 9 && code !== 10 && code !== 13 ? ' ' : char;
    })
    .join('');
}

/**
 * Sanitiza dados de planilha Excel
 * @param sheetData Dados da planilha
 * @returns Dados sanitizados e relatório de segurança
 */
export function sanitizeExcelData(sheetData: unknown): {
  sanitizedData: unknown;
  securityReport: {
    hasDangerousFormulas: boolean;
    formulasRejected: number;
    details: Array<{
      rowIndex: number;
      columnName: string;
      reason: string;
    }>;
  };
} {
  if (!sheetData || typeof sheetData !== 'object') {
    return {
      sanitizedData: {} as Record<string, unknown>,
      securityReport: {
        hasDangerousFormulas: false,
        formulasRejected: 0,
        details: [],
      },
    };
  }

  // Inicializar relatório
  const securityReport = {
    hasDangerousFormulas: false,
    formulasRejected: 0,
    details: [] as Array<{
      rowIndex: number;
      columnName: string;
      reason: string;
    }>,
  };

  // Criar cópia profunda dos dados
  const sanitizedData = Array.isArray(sheetData) ? [] : ({} as Record<string, unknown>);

  // Função recursiva para sanitizar valores
  function sanitizeValue(value: unknown, rowIndex: number, colName: string): unknown {
    if (value === null || value === undefined) return value;

    // Sanitizar strings
    if (typeof value === 'string') {
      // Verificar e sanitizar fórmulas
      if (value.startsWith('=')) {
        const result = sanitizeExcelFormula(value);
        if (!result.isSafe) {
          securityReport.hasDangerousFormulas = true;
          securityReport.formulasRejected++;
          securityReport.details.push({
            rowIndex,
            columnName: colName,
            reason: result.reason || 'Fórmula potencialmente perigosa',
          });

          // Substituir fórmula perigosa por texto seguro
          return '[Fórmula removida por segurança]';
        }
        return result.sanitized;
      }

      // Sanitizar texto comum
      return sanitizeHtml(value);
    }

    // Sanitizar objetos recursivamente
    if (typeof value === 'object') {
      if (Array.isArray(value)) {
        return value.map((item, idx) => sanitizeValue(item, rowIndex, `${colName}[${idx}]`));
      } else if (value !== null) {
        const result: Record<string, unknown> = {};
        for (const key in value) {
          if (Object.prototype.hasOwnProperty.call(value, key)) {
            result[key] = sanitizeValue(
              (value as Record<string, unknown>)[key],
              rowIndex,
              `${colName}.${key}`
            );
          }
        }
        return result;
      }
    }

    // Retornar outros tipos primitivos sem alterações
    return value;
  }

  // Processar dados
  if (Array.isArray(sheetData)) {
    for (let i = 0; i < sheetData.length; i++) {
      (sanitizedData as unknown[])[i] = sanitizeValue(sheetData[i], i, `row${i}`);
    }
  } else {
    for (const key in sheetData) {
      if (Object.prototype.hasOwnProperty.call(sheetData, key)) {
        (sanitizedData as Record<string, unknown>)[key] = sanitizeValue(
          (sheetData as Record<string, unknown>)[key],
          0,
          key
        );
      }
    }
  }

  return { sanitizedData, securityReport };
}

/**
 * Remove caracteres de controle e substitui por espaço
 */
export function sanitizeControlChars(input: string): string {
  if (!input) return '';
  // Substituir a expressão regex pelo método criado
  return removeControlCharacters(input);
}

/**
 * Sanitiza o conteúdo de uma célula Excel para prevenir injeções
 * @param cellContent Conteúdo da célula para sanitizar
 * @returns Conteúdo sanitizado
 */
export function sanitizeExcelCellContent(cellContent: unknown): unknown {
  if (cellContent === null || cellContent === undefined) return cellContent;

  // Se for string, sanitizar
  if (typeof cellContent === 'string') {
    // Verificar se parece uma fórmula (começa com '=')
    if (cellContent.startsWith('=')) {
      const sanitizationResult = sanitizeExcelFormula(cellContent);
      return sanitizationResult.isSafe
        ? sanitizationResult.sanitized
        : '[Fórmula removida por segurança]';
    }

    // Sanitizar para prevenir XSS em células de texto
    return sanitizeHtml(cellContent);
  }

  // Se for número, boolean ou outro tipo primitivo, retornar sem alterações
  if (typeof cellContent !== 'object') return cellContent;

  // Se for array, sanitizar cada elemento
  if (Array.isArray(cellContent)) {
    return cellContent.map(item => sanitizeExcelCellContent(item));
  }

  // Se for objeto, sanitizar recursivamente
  if (cellContent !== null && typeof cellContent === 'object') {
    return deepSanitizeObject(cellContent as Record<string, unknown>);
  }

  return cellContent;
}

/**
 * Escapa caracteres de controle em uma string para SQL
 * @param str String para escapar
 * @returns String com caracteres de controle escapados
 */
export function escapeControlCharsSql(str: string): string {
  if (!str || typeof str !== 'string') return '';

  // Usar método manual em vez de regex para evitar o erro no-control-regex
  return str
    .replace(/\0/g, '\\0')
    .replace(/\n/g, '\\n')
    .replace(/\r/g, '\\r')
    .replace(/\t/g, '\\t')
    .split('')
    .map(char => (char === String.fromCharCode(26) ? '\\Z' : char))
    .join(''); // Substituir Ctrl+Z (EOT) manualmente
}

/**
 * Alias para função sanitizeHtml para compatibilidade com código legado
 * @param htmlContent String para sanitizar
 * @returns String sanitizada
 * @deprecated Use sanitizeHtml diretamente
 */
export function sanitizeHtmlContent(htmlContent: string): string {
  return sanitizeHtml(htmlContent);
}

/**
 * Sanitiza string de entrada, removendo scripts maliciosos e conteúdo HTML não seguro
 * @param input Input para sanitizar
 * @returns Input sanitizado
 */
export const sanitizeInput = (
  input: string | null | undefined | Record<string, unknown>
): unknown => {
  if (input === null || input === undefined) {
    return '';
  }

  if (typeof input === 'string') {
    return sanitizeHtml(input);
  }

  if (typeof input === 'object') {
    return deepSanitizeObject(input as Record<string, unknown>);
  }

  return input;
};

/**
 * Valida uma string JSON
 * @param json String JSON para validar
 * @param schema Schema opcional para validação
 * @returns Resultado da validação
 */
export const validateJSON = (
  json: string,
  schema?: Record<string, unknown>
): { valid: boolean; data?: unknown; error?: string } => {
  try {
    const data = JSON.parse(json);

    if (schema) {
      // Se schema é fornecido, implementar validação aqui
      // Por enquanto apenas retornar válido sem validação schema
      return { valid: true, data };
    }

    return { valid: true, data };
  } catch (error) {
    return {
      valid: false,
      error: error instanceof Error ? error.message : 'JSON inválido',
    };
  }
};

/**
 * Serviço de limitação de taxa
 */
export class RateLimiterService {
  private windowMs: number;
  private maxRequests: number;
  private clients: Map<string, { count: number; resetTime: number }>;

  constructor(options: { windowMs: number; maxRequests: number; message?: string }) {
    this.windowMs = options.windowMs;
    this.maxRequests = options.maxRequests;
    this.clients = new Map();
  }

  consume(key: string): { success: boolean; remaining: number; resetTime: Date } {
    const now = Date.now();
    const client = this.clients.get(key) || { count: 0, resetTime: now + this.windowMs };

    // Reset if time window expired
    if (now > client.resetTime) {
      client.count = 0;
      client.resetTime = now + this.windowMs;
    }

    const remaining = this.maxRequests - client.count;
    const success = remaining > 0;

    if (success) {
      client.count++;
      this.clients.set(key, client);
    }

    return {
      success,
      remaining: Math.max(0, remaining),
      resetTime: new Date(client.resetTime),
    };
  }
}
