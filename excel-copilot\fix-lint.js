#!/usr/bin/env node

/**
 * Script para corrigir problemas comuns de lint no projeto Excel Copilot
 *
 * Este script faz:
 * 1. Adiciona prefixo _ em variáveis não utilizadas
 * 2. Organiza imports
 * 3. Corrige problemas de formatação comuns
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const glob = require('glob');

// Cores para console
const colors = {
  reset: '\x1b[0m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  red: '\x1b[31m',
};

console.log(`${colors.blue}Iniciando correção automática de problemas de lint${colors.reset}`);

// Executa o ESLint com correção automática ativada
try {
  console.log(
    `${colors.yellow}Executando ESLint com --fix para corrigir problemas simples...${colors.reset}`
  );
  execSync('npx eslint --fix "src/**/*.{ts,tsx}"', { stdio: 'inherit' });
  console.log(`${colors.green}ESLint concluído com sucesso!${colors.reset}`);
} catch (error) {
  console.error(`${colors.red}Erro ao executar ESLint: ${error.message}${colors.reset}`);
}

// Executa Prettier para corrigir problemas de formatação
try {
  console.log(
    `${colors.yellow}Executando Prettier para corrigir problemas de formatação...${colors.reset}`
  );
  execSync('npx prettier --write "src/**/*.{ts,tsx}"', { stdio: 'inherit' });
  console.log(`${colors.green}Prettier concluído com sucesso!${colors.reset}`);
} catch (error) {
  console.error(`${colors.red}Erro ao executar Prettier: ${error.message}${colors.reset}`);
}

// Corrige problemas específicos de importação
function fixImportOrder() {
  console.log(`${colors.yellow}Corrigindo problemas de ordenação de imports...${colors.reset}`);
  try {
    execSync('npx eslint --fix --rule "import/order: error" "src/**/*.{ts,tsx}"', {
      stdio: 'inherit',
    });
    console.log(`${colors.green}Ordenação de imports concluída!${colors.reset}`);
  } catch (error) {
    console.error(
      `${colors.red}Erro ao corrigir ordenação de imports: ${error.message}${colors.reset}`
    );
  }
}

// Corrige o problema de imports não utilizados
function fixUnusedImports() {
  console.log(`${colors.yellow}Removendo imports não utilizados...${colors.reset}`);
  try {
    execSync(
      'npx eslint --fix --rule "unused-imports/no-unused-imports: error" --plugin unused-imports "src/**/*.{ts,tsx}"',
      { stdio: 'inherit' }
    );
    console.log(`${colors.green}Remoção de imports não utilizados concluída!${colors.reset}`);
  } catch (error) {
    console.error(
      `${colors.red}Erro ao remover imports não utilizados: ${error.message}${colors.reset}`
    );
  }
}

// Corrige o problema de falta de linha em branco no final de arquivos com extensão .ts
function addNewlineToFiles() {
  console.log(`${colors.yellow}Corrigindo arquivos sem linha em branco no final...${colors.reset}`);

  // Encontrar arquivos .ts e .tsx
  try {
    const files = glob.sync('src/**/*.{ts,tsx}');
    let fixedCount = 0;

    files.forEach(filePath => {
      try {
        let content = fs.readFileSync(filePath, 'utf8');

        // Verifica se o arquivo já termina com uma linha em branco
        if (!content.endsWith('\n')) {
          content += '\n';
          fs.writeFileSync(filePath, content);
          fixedCount++;
          console.log(`${colors.green}Corrigido: ${filePath}${colors.reset}`);
        }
      } catch (error) {
        console.error(
          `${colors.red}Erro ao processar ${filePath}: ${error.message}${colors.reset}`
        );
      }
    });

    console.log(
      `${colors.green}${fixedCount} arquivos corrigidos para adicionar linha em branco no final${colors.reset}`
    );
  } catch (error) {
    console.error(`${colors.red}Erro ao buscar arquivos: ${error.message}${colors.reset}`);
  }
}

// Corrige problemas de formatação específicos em arquivos conhecidos por terem problemas
function fixFormatIssues() {
  console.log(`${colors.yellow}Corrigindo problemas específicos de formatação...${colors.reset}`);

  // Lista de arquivos problemáticos conhecidos
  const problemFiles = [
    'src/types/ui-components.ts',
    'src/lib/excel/executionOperations.ts',
    'src/lib/chartOperations.ts',
    'src/components/enhanced-chat-input.tsx',
  ];

  // Aplica prettier especificamente nestes arquivos
  problemFiles.forEach(filePath => {
    if (fs.existsSync(filePath)) {
      try {
        execSync(`npx prettier --write "${filePath}"`, { stdio: 'inherit' });
        console.log(`${colors.green}Formatação corrigida: ${filePath}${colors.reset}`);
      } catch (error) {
        console.error(`${colors.red}Erro ao formatar ${filePath}: ${error.message}${colors.reset}`);
      }
    } else {
      console.log(`${colors.yellow}Arquivo não encontrado: ${filePath}${colors.reset}`);
    }
  });
}

// Executa as funções de correção
fixImportOrder();
fixUnusedImports();
addNewlineToFiles();
fixFormatIssues();

console.log(`${colors.green}Concluída a correção automática de problemas de lint.${colors.reset}`);
console.log(
  `${colors.blue}Execute 'npm run lint' para verificar os problemas restantes.${colors.reset}`
);
