/**
 * Script para testar o fluxo de workbook (criação/atualização de planilhas)
 */

const { PrismaClient } = require('@prisma/client');

// Inicializar cliente Prisma
const prisma = new PrismaClient();

/**
 * Função principal
 */
async function main() {
  console.log('=== TESTE DE FLUXO DE WORKBOOK ===\n');

  try {
    // 1. Criar um novo workbook para teste
    console.log('Criando novo workbook para teste...');

    const workbookName = `Teste Workbook ${new Date().toISOString().slice(0, 10)}`;

    const newWorkbook = await prisma.workbook.create({
      data: {
        name: workbookName,
        userId: 'test-user',
        sheets: {
          create: {
            name: '<PERSON><PERSON> de Vendas',
            data: JSON.stringify({
              headers: ['Produto', 'Preço', 'Quantidade', 'Total'],
              rows: [
                ['Produto A', 100, 2, 200],
                ['Produto B', 150, 3, 450],
                ['Produto C', 75, 5, 375],
              ],
            }),
          },
        },
      },
      include: {
        sheets: true,
      },
    });

    console.log(`✅ Workbook criado com ID: ${newWorkbook.id}`);
    console.log(`   - Nome: ${newWorkbook.name}`);
    console.log(`   - Planilhas: ${newWorkbook.sheets.length}`);

    // 2. Buscar o workbook criado
    console.log('\nBuscando workbook criado...');

    const foundWorkbook = await prisma.workbook.findUnique({
      where: {
        id: newWorkbook.id,
      },
      include: {
        sheets: true,
      },
    });

    if (!foundWorkbook) {
      throw new Error('Workbook não encontrado após criação');
    }

    console.log('✅ Workbook encontrado no banco de dados');

    // 3. Atualizar dados da planilha
    console.log('\nAtualizando dados da planilha...');

    const sheet = foundWorkbook.sheets[0];
    const sheetData = JSON.parse(sheet.data);

    // Adicionar nova linha
    sheetData.rows.push(['Produto D', 200, 4, 800]);

    // Atualizar soma da coluna Total
    sheetData.rows.push(['Total', '', '', 1825]);

    // Salvar no banco de dados
    await prisma.sheet.update({
      where: {
        id: sheet.id,
      },
      data: {
        data: JSON.stringify(sheetData),
      },
    });

    console.log('✅ Dados da planilha atualizados');

    // 4. Adicionar nova planilha ao workbook
    console.log('\nAdicionando nova planilha ao workbook...');

    const newSheet = await prisma.sheet.create({
      data: {
        name: 'Resumo',
        workbookId: newWorkbook.id,
        data: JSON.stringify({
          headers: ['Categoria', 'Total'],
          rows: [
            ['Eletrônicos', 1025],
            ['Móveis', 800],
            ['Total', 1825],
          ],
        }),
      },
    });

    console.log(`✅ Nova planilha criada com ID: ${newSheet.id}`);

    // 5. Listar todas as planilhas do workbook
    console.log('\nListando todas as planilhas do workbook:');

    const updatedWorkbook = await prisma.workbook.findUnique({
      where: {
        id: newWorkbook.id,
      },
      include: {
        sheets: true,
      },
    });

    if (updatedWorkbook) {
      updatedWorkbook.sheets.forEach((sheet, index) => {
        console.log(`  ${index + 1}. ${sheet.name} (ID: ${sheet.id})`);
        const data = JSON.parse(sheet.data);
        console.log(`     Colunas: ${data.headers.join(', ')}`);
        console.log(`     Linhas: ${data.rows.length}`);
      });
    }

    console.log('\n✅ TESTE DE WORKBOOK CONCLUÍDO COM SUCESSO');
  } catch (error) {
    console.error('❌ Erro no teste:', error);
  } finally {
    // Fechar conexão com o banco de dados
    await prisma.$disconnect();
    console.log('\nConexão com o banco de dados fechada');
  }
}

// Executar função principal
main().catch(error => {
  console.error('❌ Erro fatal:', error);
  process.exit(1);
});
