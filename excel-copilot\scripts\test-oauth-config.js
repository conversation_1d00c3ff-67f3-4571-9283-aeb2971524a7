#!/usr/bin/env node

/**
 * Script para testar e diagnosticar configuração OAuth
 * Verifica se as variáveis de ambiente estão corretas e se as URLs estão configuradas
 */

const https = require('https');
const { URL } = require('url');

// Carregar variáveis de ambiente
require('dotenv').config({ path: '.env.local' });

console.log('🔍 DIAGNÓSTICO DE CONFIGURAÇÃO OAUTH\n');

// 1. Verificar variáveis de ambiente
console.log('📋 VARIÁVEIS DE AMBIENTE:');
console.log('NODE_ENV:', process.env.NODE_ENV);
console.log('AUTH_NEXTAUTH_URL:', process.env.AUTH_NEXTAUTH_URL);
console.log('NEXTAUTH_URL:', process.env.NEXTAUTH_URL);
console.log(
  'AUTH_GOOGLE_CLIENT_ID:',
  process.env.AUTH_GOOGLE_CLIENT_ID ? '✅ Configurado' : '❌ Não configurado'
);
console.log(
  'AUTH_GOOGLE_CLIENT_SECRET:',
  process.env.AUTH_GOOGLE_CLIENT_SECRET ? '✅ Configurado' : '❌ Não configurado'
);
console.log(
  'AUTH_GITHUB_CLIENT_ID:',
  process.env.AUTH_GITHUB_CLIENT_ID ? '✅ Configurado' : '❌ Não configurado'
);
console.log(
  'AUTH_GITHUB_CLIENT_SECRET:',
  process.env.AUTH_GITHUB_CLIENT_SECRET ? '✅ Configurado' : '❌ Não configurado'
);

// 2. URLs de callback esperadas
console.log('\n🔗 URLS DE CALLBACK ESPERADAS:');
const baseUrl =
  process.env.AUTH_NEXTAUTH_URL || process.env.NEXTAUTH_URL || 'http://localhost:3000';
console.log('Base URL:', baseUrl);
console.log('Google Callback:', `${baseUrl}/api/auth/callback/google`);
console.log('GitHub Callback:', `${baseUrl}/api/auth/callback/github`);

// 3. Verificar se é desenvolvimento ou produção
console.log('\n🏗️ AMBIENTE:');
const isDev = process.env.NODE_ENV === 'development';
const isLocalhost = baseUrl.includes('localhost');
console.log('Desenvolvimento:', isDev ? '✅ Sim' : '❌ Não');
console.log('Localhost:', isLocalhost ? '✅ Sim' : '❌ Não');

// 4. Verificações de consistência
console.log('\n⚠️ VERIFICAÇÕES DE CONSISTÊNCIA:');

// Verificar se NEXTAUTH_URL e AUTH_NEXTAUTH_URL são consistentes
const nextAuthUrl = process.env.NEXTAUTH_URL;
const authNextAuthUrl = process.env.AUTH_NEXTAUTH_URL;

if (nextAuthUrl && authNextAuthUrl && nextAuthUrl !== authNextAuthUrl) {
  console.log('❌ INCONSISTÊNCIA: NEXTAUTH_URL e AUTH_NEXTAUTH_URL são diferentes');
  console.log('   NEXTAUTH_URL:', nextAuthUrl);
  console.log('   AUTH_NEXTAUTH_URL:', authNextAuthUrl);
} else {
  console.log('✅ URLs de NextAuth consistentes');
}

// Verificar se está usando localhost em desenvolvimento
if (isDev && !isLocalhost) {
  console.log('⚠️ AVISO: Em desenvolvimento, recomenda-se usar localhost:3000');
}

// Verificar se está usando HTTPS em produção
if (!isDev && !baseUrl.startsWith('https://')) {
  console.log('❌ ERRO: Em produção, deve usar HTTPS');
}

// 5. Instruções para configuração do Google OAuth
console.log('\n📝 CONFIGURAÇÃO NECESSÁRIA NO GOOGLE CLOUD CONSOLE:');
console.log('1. Acesse: https://console.cloud.google.com/apis/credentials');
console.log('2. Selecione seu projeto OAuth');
console.log('3. Configure as URLs autorizadas:');
console.log('   - JavaScript origins:', baseUrl);
console.log('   - Redirect URIs:', `${baseUrl}/api/auth/callback/google`);

// 6. Instruções para configuração do GitHub OAuth
console.log('\n📝 CONFIGURAÇÃO NECESSÁRIA NO GITHUB:');
console.log('1. Acesse: https://github.com/settings/developers');
console.log('2. Selecione sua OAuth App');
console.log('3. Configure:');
console.log('   - Homepage URL:', baseUrl);
console.log('   - Authorization callback URL:', `${baseUrl}/api/auth/callback/github`);

// 7. Teste de conectividade (opcional)
console.log('\n🌐 TESTE DE CONECTIVIDADE:');
if (isLocalhost) {
  console.log('✅ Localhost - sem teste de conectividade necessário');
} else {
  console.log('🔄 Testando conectividade com', baseUrl);

  try {
    const url = new URL(baseUrl);
    const options = {
      hostname: url.hostname,
      port: url.port || (url.protocol === 'https:' ? 443 : 80),
      path: '/',
      method: 'HEAD',
      timeout: 5000,
    };

    const req = https.request(options, res => {
      console.log('✅ Conectividade OK - Status:', res.statusCode);
    });

    req.on('error', err => {
      console.log('❌ Erro de conectividade:', err.message);
    });

    req.on('timeout', () => {
      console.log('⏰ Timeout - servidor pode estar lento');
      req.destroy();
    });

    req.end();
  } catch (error) {
    console.log('❌ URL inválida:', error.message);
  }
}

console.log('\n✅ Diagnóstico concluído!');
console.log('\n💡 PRÓXIMOS PASSOS:');
console.log('1. Verifique se as URLs estão configuradas corretamente nos provedores OAuth');
console.log('2. Reinicie o servidor de desenvolvimento: npm run dev');
console.log('3. Teste o login em: http://localhost:3000/auth/signin');
