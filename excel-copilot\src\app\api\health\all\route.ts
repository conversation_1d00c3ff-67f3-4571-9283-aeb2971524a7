/**
 * 📋 ALL HEALTH CHECKS ENDPOINT
 *
 * GET /api/health/all
 *
 * Retorna informações sobre todos os health checks disponíveis
 */

import { NextRequest, NextResponse } from 'next/server';

import { getAvailableServices } from '@/lib/health-checks/index';

export async function GET(request: NextRequest) {
  try {
    // Obter lista de serviços disponíveis
    const availableServices = getAvailableServices();

    // Informações sobre cada endpoint
    const endpoints = [
      {
        name: 'Health Geral',
        path: '/api/health',
        description: 'Verifica a saúde de todos os serviços do sistema',
        critical: true,
        parameters: [
          { name: 'type', description: 'Tipo de verificação (all|critical)', default: 'all' },
          { name: 'details', description: 'Incluir detalhes na resposta', default: 'true' },
        ],
      },
      {
        name: 'Health Críticos',
        path: '/api/health?type=critical',
        description: 'Verifica apenas os serviços críticos (database, auth)',
        critical: true,
        parameters: [],
      },
      {
        name: 'Database',
        path: '/api/health/database',
        description: 'Verifica a conectividade e performance do banco de dados',
        critical: true,
        service: 'database',
      },
      {
        name: 'Authentication',
        path: '/api/health/auth',
        description: 'Verifica a configuração do sistema de autenticação',
        critical: true,
        service: 'auth',
      },
      {
        name: 'AI (Vertex AI)',
        path: '/api/health/ai',
        description: 'Verifica a disponibilidade do sistema de IA',
        critical: false,
        service: 'ai',
      },
      {
        name: 'Stripe',
        path: '/api/health/stripe',
        description: 'Verifica a configuração do sistema de pagamentos',
        critical: false,
        service: 'stripe',
      },
      {
        name: 'MCP Integrations',
        path: '/api/health/mcp',
        description: 'Verifica as integrações MCP (Vercel, Linear, GitHub)',
        critical: false,
        service: 'mcp',
      },
      {
        name: 'Test Endpoint',
        path: '/api/health/test',
        description: 'Endpoint de teste simples para validar funcionamento',
        critical: false,
        service: 'test',
      },
      {
        name: 'Debug Info',
        path: '/api/health/debug',
        description: 'Informações de debug sobre importações e configurações',
        critical: false,
        service: 'debug',
      },
    ];

    // Estatísticas
    const stats = {
      totalEndpoints: endpoints.length,
      criticalEndpoints: endpoints.filter(e => e.critical).length,
      availableServices: availableServices.length,
      serviceEndpoints: endpoints.filter(e => e.service).length,
    };

    // Informações sobre códigos de status
    const statusCodes = {
      200: 'Serviço saudável',
      503: 'Serviço não disponível ou com falhas',
      500: 'Erro interno do servidor',
      405: 'Método não permitido (apenas GET é suportado)',
    };

    // Exemplos de uso
    const examples = [
      {
        title: 'Verificar saúde geral',
        url: '/api/health',
        description: 'Verifica todos os serviços e retorna status geral',
      },
      {
        title: 'Verificar apenas serviços críticos',
        url: '/api/health?type=critical',
        description: 'Verifica apenas database e authentication',
      },
      {
        title: 'Verificar database específico',
        url: '/api/health/database',
        description: 'Verifica conectividade e performance do banco',
      },
      {
        title: 'Verificar sem detalhes',
        url: '/api/health?details=false',
        description: 'Retorna apenas status básico sem detalhes',
      },
    ];

    const response = {
      title: 'Excel Copilot Health Checks API',
      description: 'Sistema completo de monitoramento de saúde dos serviços',
      version: '1.0.0',
      timestamp: new Date().toISOString(),

      stats,

      endpoints: endpoints.map(endpoint => ({
        ...endpoint,
        fullUrl: `${request.nextUrl.origin}${endpoint.path}`,
        available: endpoint.service ? availableServices.includes(endpoint.service) : true,
      })),

      statusCodes,
      examples: examples.map(example => ({
        ...example,
        fullUrl: `${request.nextUrl.origin}${example.url}`,
      })),

      usage: {
        monitoring: 'Use para monitoramento contínuo da saúde do sistema',
        cicd: 'Integre no pipeline CI/CD para validação automática',
        alerts: 'Configure alertas baseados nos códigos de status HTTP',
        dashboard: 'Consuma os endpoints para criar dashboards de monitoramento',
      },

      recommendations: [
        'Execute health checks a cada 30-60 segundos para monitoramento contínuo',
        'Configure alertas para falhas em serviços críticos (database, auth)',
        'Use timeouts apropriados (5-10 segundos) ao consumir os endpoints',
        'Monitore tempos de resposta para detectar degradação de performance',
        'Implemente retry logic para lidar com falhas temporárias',
      ],
    };

    return NextResponse.json(response, {
      status: 200,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        Pragma: 'no-cache',
        Expires: '0',
      },
    });
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return NextResponse.json(
      {
        error: 'Failed to retrieve health check information',
        message: errorMessage,
        timestamp: new Date().toISOString(),
      },
      {
        status: 500,
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
        },
      }
    );
  }
}

// Permitir apenas GET
export async function POST() {
  return NextResponse.json({ error: 'Method not allowed' }, { status: 405 });
}

export async function PUT() {
  return NextResponse.json({ error: 'Method not allowed' }, { status: 405 });
}

export async function DELETE() {
  return NextResponse.json({ error: 'Method not allowed' }, { status: 405 });
}
