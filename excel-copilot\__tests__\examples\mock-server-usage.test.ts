/**
 * @jest-environment node
 */

import { server } from '../mocks/server';
import {
  setupExcelMockHandlers,
  setupAuthMockHandlers,
  setupErrorMockHandlers,
} from '../utils/server-test-utils';
import {
  callMockEndpoint,
  expectSuccessResponse,
  expectErrorResponse,
} from '../utils/test-helpers';

describe('Exemplos de uso do mock server e utilitários', () => {
  beforeAll(() => {
    server.listen();
  });

  afterEach(() => {
    server.resetHandlers();
  });

  afterAll(() => {
    server.close();
  });

  describe('API de Excel', () => {
    // Configurar os handlers para testes de Excel
    beforeEach(() => {
      setupExcelMockHandlers();
    });

    test('deve retornar a lista de workbooks', () => {
      // Chamar o endpoint mockado
      const response = callMockEndpoint('/api/workbooks', 'GET');

      // Verificar a resposta
      expectSuccessResponse(response);
      expect(response.body.data).toHaveLength(2);
      expect(response.body.data[0].id).toBe('wb-1');
      expect(response.body.data[1].id).toBe('wb-2');
    });

    test('deve retornar detalhes de um workbook válido', () => {
      // Chamar o endpoint mockado com um ID válido
      const response = callMockEndpoint('/api/workbooks/wb-test', 'GET');

      // Verificar a resposta
      expectSuccessResponse(response);
      expect(response.body.data.id).toBe('wb-test');
      expect(response.body.data.sheets).toHaveLength(2);
    });

    test('deve retornar erro para workbook inválido', () => {
      // Chamar o endpoint mockado com um ID inválido
      const response = callMockEndpoint('/api/workbooks/invalid-id', 'GET');

      // Verificar a resposta de erro
      expectErrorResponse(response, 404, 'Workbook não encontrado');
    });

    test('deve retornar dados de uma planilha', () => {
      // Chamar o endpoint mockado
      const response = callMockEndpoint('/api/workbooks/wb-1/sheets/Sheet1', 'GET');

      // Verificar a resposta
      expectSuccessResponse(response);
      expect(response.body.data).toHaveLength(6); // 1 linha de cabeçalho + 5 linhas de dados
    });

    test('deve processar operações em planilha', () => {
      // Chamar o endpoint mockado com dados
      const response = callMockEndpoint('/api/workbooks/wb-1/operations', 'POST', {
        operations: [{ type: 'SUM', range: 'A1:A5' }],
      });

      // Verificar a resposta
      expectSuccessResponse(response);
      expect(response.body.data.operationsExecuted).toBe(1);
      expect(response.body.data.updatedCells).toContain('A1');
    });

    test('deve retornar erro para operações inválidas', () => {
      // Chamar o endpoint mockado com dados inválidos
      const response = callMockEndpoint('/api/workbooks/wb-1/operations', 'POST', {
        operations: 'invalid',
      });

      // Verificar a resposta de erro
      expectErrorResponse(response, 400, 'Formato de operações inválido');
    });
  });

  describe('API de Autenticação', () => {
    // Configurar os handlers para testes de autenticação
    beforeEach(() => {
      setupAuthMockHandlers();
    });

    test('deve fazer login com sucesso', () => {
      // Chamar o endpoint mockado
      const response = callMockEndpoint('/api/auth/login', 'POST', {
        email: '<EMAIL>',
        password: 'password123',
      });

      // Verificar a resposta
      expectSuccessResponse(response);
      expect(response.body.data.token).toBeTruthy();
      expect(response.body.data.user.email).toBe('<EMAIL>');
    });

    test('deve retornar erro para credenciais inválidas', () => {
      // Chamar o endpoint mockado com email de erro
      const response = callMockEndpoint('/api/auth/login', 'POST', {
        email: '<EMAIL>',
        password: 'password123',
      });

      // Verificar a resposta de erro
      expectErrorResponse(response, 401, 'Credenciais inválidas');
    });

    test('deve retornar informações da sessão', () => {
      // Chamar o endpoint mockado
      const response = callMockEndpoint('/api/auth/session', 'GET');

      // Verificar a resposta
      expectSuccessResponse(response);
      expect(response.body.data.isLoggedIn).toBe(true);
      expect(response.body.data.user.id).toBe('user-1');
    });
  });

  describe('Tratamento de Erros', () => {
    // Configurar os handlers para testes de erros
    beforeEach(() => {
      setupErrorMockHandlers();
    });

    test('deve lidar com erro de servidor', () => {
      // Chamar o endpoint mockado que retorna erro 500
      const response = callMockEndpoint('/api/error/server', 'GET');

      // Verificar a resposta de erro
      expectErrorResponse(response, 500, 'Erro interno do servidor');
    });

    test('deve lidar com erro de rede', () => {
      // Tentar chamar o endpoint que gera erro de rede
      expect(() => {
        callMockEndpoint('/api/error/network', 'GET');
      }).toThrow('Erro de rede simulado');
    });
  });

  describe('Combinação de APIs', () => {
    // Configurar múltiplos handlers
    beforeEach(() => {
      setupExcelMockHandlers();
      setupAuthMockHandlers();
      setupErrorMockHandlers();
    });

    test('deve funcionar com múltiplos handlers configurados', () => {
      // Teste para APIs de Excel
      const excelResponse = callMockEndpoint('/api/workbooks', 'GET');
      expectSuccessResponse(excelResponse);

      // Teste para APIs de Autenticação
      const authResponse = callMockEndpoint('/api/auth/session', 'GET');
      expectSuccessResponse(authResponse);

      // Teste para APIs de Erro
      const errorResponse = callMockEndpoint('/api/error/server', 'GET');
      expectErrorResponse(errorResponse, 500);
    });
  });
});
