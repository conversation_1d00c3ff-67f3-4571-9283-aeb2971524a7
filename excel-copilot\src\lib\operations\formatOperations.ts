import { ExcelOperationType } from '@/types';
import { safeArrayAccess } from '@/utils';
import { extractGroup } from '@/utils/regex-utils';

import { ExcelOperation } from '../excel/types';

/**
 * Tipos de formatação suportados
 */
export type FormatType =
  | 'number'
  | 'currency'
  | 'percentage'
  | 'date'
  | 'text'
  | 'background'
  | 'font'
  | 'border'
  | 'alignment';

/**
 * Interface para dados de operação de formatação
 */
export interface FormatOperationData {
  type: FormatType;
  range: string; // Ex: "A1:C3"
  format: {
    // Formato numérico
    decimal?: number;
    locale?: string;
    symbol?: string; // Para moeda ($, €, etc.)

    // Cores
    backgroundColor?: string;
    color?: string;

    // Fonte
    fontName?: string;
    fontSize?: number;
    bold?: boolean;
    italic?: boolean;
    underline?: boolean;
    strikethrough?: boolean;

    // Borda
    borderStyle?: 'thin' | 'medium' | 'thick' | 'dashed' | 'dotted' | 'double' | 'none';
    borderColor?: string;

    // Alinhamento
    horizontal?: 'left' | 'center' | 'right';
    vertical?: 'top' | 'middle' | 'bottom';
    wrapText?: boolean;
  };
}

/**
 * Executa uma operação de formatação
 * @param sheetData Dados da planilha
 * @param operation Operação a ser executada
 * @returns Dados atualizados e resumo da operação
 */
export async function executeFormatOperation(
  sheetData: any,
  operation: ExcelOperation
): Promise<{ updatedData: any; resultSummary: string }> {
  try {
    const { type, range, format } = operation.data as FormatOperationData;

    if (!type || !range || !format) {
      throw new Error('Parâmetros insuficientes para operação de formatação');
    }

    // Clonar dados para não modificar o original diretamente
    const updatedData = { ...sheetData };

    // Garantir que temos um objeto de formatação
    if (!updatedData.formatting) {
      updatedData.formatting = {};
    }

    // Processar a formatação
    const rangeCoords = parseRange(range);
    const { startRow, startCol, endRow, endCol } = rangeCoords;

    // Aplicar formatação para cada célula no range
    for (let row = startRow; row <= endRow; row++) {
      for (let col = startCol; col <= endCol; col++) {
        const cellId = getCellId(row, col);

        // Criar ou atualizar formatação para esta célula
        updatedData.formatting[cellId] = {
          ...(updatedData.formatting[cellId] || {}),
          ...processFormatting(type, format),
        };
      }
    }

    // Gerar resumo da operação
    const formatDescription = getFormatDescription(type, format);
    const resultSummary = `Aplicada formatação ${formatDescription} ao range ${range}`;

    return { updatedData, resultSummary };
  } catch (error) {
    console.error('Erro ao executar operação de formatação:', error);
    throw new Error(
      `Falha ao aplicar formatação: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
    );
  }
}

/**
 * Processa os dados de formatação baseado no tipo
 * @param type Tipo de formatação
 * @param format Dados de formatação
 * @returns Objeto de formatação processado
 */
function processFormatting(type: FormatType, format: any): any {
  switch (type) {
    case 'number':
      return {
        numberFormat: {
          type: 'number',
          decimals: format.decimal || 0,
        },
      };

    case 'currency':
      return {
        numberFormat: {
          type: 'currency',
          decimals: format.decimal || 2,
          symbol: format.symbol || '$',
          locale: format.locale || 'en-US',
        },
      };

    case 'percentage':
      return {
        numberFormat: {
          type: 'percentage',
          decimals: format.decimal || 0,
        },
      };

    case 'date':
      return {
        numberFormat: {
          type: 'date',
          format: format.format || 'dd/MM/yyyy',
        },
      };

    case 'background':
      return {
        background: {
          color: format.backgroundColor,
        },
      };

    case 'font':
      return {
        font: {
          name: format.fontName,
          size: format.fontSize,
          bold: format.bold,
          italic: format.italic,
          underline: format.underline,
          strikethrough: format.strikethrough,
          color: format.color,
        },
      };

    case 'border':
      return {
        border: {
          style: format.borderStyle || 'thin',
          color: format.borderColor || '#000000',
        },
      };

    case 'alignment':
      return {
        alignment: {
          horizontal: format.horizontal,
          vertical: format.vertical,
          wrapText: format.wrapText,
        },
      };

    default:
      return format;
  }
}

/**
 * Gera uma descrição legível da formatação aplicada
 * @param type Tipo de formatação
 * @param format Dados de formatação
 * @returns Descrição da formatação
 */
function getFormatDescription(type: FormatType, format: any): string {
  switch (type) {
    case 'number':
      return `numérico (${format.decimal || 0} decimais)`;

    case 'currency':
      return `moeda (${format.symbol || '$'}, ${format.decimal || 2} decimais)`;

    case 'percentage':
      return `porcentagem (${format.decimal || 0} decimais)`;

    case 'date':
      return `data (${format.format || 'dd/MM/yyyy'})`;

    case 'text':
      return `texto`;

    case 'background':
      return `de cor de fundo (${format.backgroundColor})`;

    case 'font': {
      const fontProps = [];
      if (format.fontName) fontProps.push(format.fontName);
      if (format.fontSize) fontProps.push(`${format.fontSize}pt`);
      if (format.bold) fontProps.push('negrito');
      if (format.italic) fontProps.push('itálico');
      if (format.color) fontProps.push(`cor ${format.color}`);
      return `de fonte (${fontProps.join(', ')})`;
    }

    case 'border':
      return `de borda (${format.borderStyle || 'fina'})`;

    case 'alignment': {
      const alignProps = [];
      if (format.horizontal) alignProps.push(`horizontal: ${format.horizontal}`);
      if (format.vertical) alignProps.push(`vertical: ${format.vertical}`);
      if (format.wrapText) alignProps.push('quebra de texto');
      return `de alinhamento (${alignProps.join(', ')})`;
    }

    default:
      return type;
  }
}

/**
 * Converte range (ex: "A1:C3") para índices de linha e coluna
 * @param range Range de células (ex: "A1:C3")
 * @returns Índices de linha e coluna
 */
function parseRange(range: string): {
  startRow: number;
  startCol: number;
  endRow: number;
  endCol: number;
} {
  // Dividir o range em células de início e fim
  const parts = range.split(':');
  if (parts.length !== 2) {
    throw new Error(`Range inválido: ${range}`);
  }

  // Converter cada célula para índices
  const start = parseCellReference(safeArrayAccess(parts, 0) || '');
  const end = parseCellReference(safeArrayAccess(parts, 1) || '');

  return {
    startRow: start.row,
    startCol: start.col,
    endRow: end.row,
    endCol: end.col,
  };
}

/**
 * Converte referência de célula (ex: "A1") para índices de linha e coluna
 * @param cellRef Referência de célula (ex: "A1")
 * @returns Índices de linha e coluna
 */
function parseCellReference(cellRef: string): { row: number; col: number } {
  // Extrair parte alfabética (coluna) e numérica (linha)
  const match = cellRef.match(/([A-Za-z]+)([0-9]+)/);
  if (!match) {
    throw new Error(`Referência de célula inválida: ${cellRef}`);
  }

  const colStr = extractGroup(match, 1).toUpperCase();
  const rowStr = extractGroup(match, 2);

  // Converter coluna de alfabética para numérica (A=1, B=2, ...)
  let colNum = 0;
  for (let i = 0; i < colStr.length; i++) {
    colNum = colNum * 26 + (colStr.charCodeAt(i) - 64);
  }

  // Converter linha para número
  const rowNum = parseInt(rowStr, 10);

  return { row: rowNum, col: colNum };
}

/**
 * Gera um ID para uma célula a partir de índices de linha e coluna
 * @param row Índice da linha (1-indexed)
 * @param col Índice da coluna (1-indexed)
 * @returns ID da célula
 */
function getCellId(row: number, col: number): string {
  let colStr = '';

  // Converter coluna de numérica para alfabética (1=A, 2=B, ...)
  let temp = col;
  while (temp > 0) {
    const remainder = (temp - 1) % 26;
    colStr = String.fromCharCode(65 + remainder) + colStr;
    temp = Math.floor((temp - 1) / 26);
  }

  return `${colStr}${row}`;
}

/**
 * Extrai operações de formatação do texto da resposta de IA
 * @param response Resposta da IA
 * @returns Array de operações de formatação
 */
export function extractFormatOperations(response: string): ExcelOperation[] {
  const operations: ExcelOperation[] = [];

  // Verificar padrões de formatação
  const formatPatterns = [
    // Formatação numérica
    /OPERAÇÃO:\s*FORMATAR[\s\S]*?TIPO:\s*(número|moeda|porcentagem|data)[\s\S]*?RANGE:\s*([^\n]+)(?:[\s\S]*?DECIMAIS:\s*([^\n]+))?(?:[\s\S]*?SÍMBOLO:\s*([^\n]+))?(?:[\s\S]*?FORMATO_DATA:\s*([^\n]+))?/gi,

    // Formatação de aparência
    /OPERAÇÃO:\s*FORMATAR[\s\S]*?TIPO:\s*(fundo|fonte|borda|alinhamento)[\s\S]*?RANGE:\s*([^\n]+)(?:[\s\S]*?COR(?:_FUNDO)?:\s*([^\n]+))?(?:[\s\S]*?FONTE:\s*([^\n]+))?(?:[\s\S]*?TAMANHO:\s*([^\n]+))?(?:[\s\S]*?NEGRITO:\s*([^\n]+))?(?:[\s\S]*?ITÁLICO:\s*([^\n]+))?(?:[\s\S]*?BORDA:\s*([^\n]+))?(?:[\s\S]*?ALINHAMENTO(?:_H)?:\s*([^\n]+))?(?:[\s\S]*?ALINHAMENTO_V:\s*([^\n]+))?(?:[\s\S]*?QUEBRA_TEXTO:\s*([^\n]+))?/gi,
  ];

  // Processar cada padrão
  formatPatterns.forEach(pattern => {
    let match;
    while ((match = pattern.exec(response)) !== null) {
      const formatType = match[1]?.trim().toLowerCase();
      const range = match[2]?.trim();

      if (!formatType || !range) continue;

      // Mapear tipo de formatação para FormatType
      const type = mapFormatType(formatType);

      // Construir objeto de formato baseado no tipo
      const format = buildFormatObject(type, match);

      // Adicionar operação à lista
      operations.push({
        type: ExcelOperationType.FORMAT,
        data: {
          type,
          range,
          format,
        },
      });
    }
  });

  return operations;
}

/**
 * Mapeia descrição de tipo de formatação para FormatType
 * @param formatTypeDescription Descrição do tipo de formatação
 * @returns FormatType correspondente
 */
function mapFormatType(formatTypeDescription: string): FormatType {
  const typeMap: Record<string, FormatType> = {
    número: 'number',
    moeda: 'currency',
    porcentagem: 'percentage',
    data: 'date',
    fundo: 'background',
    fonte: 'font',
    borda: 'border',
    alinhamento: 'alignment',
  };

  return typeMap[formatTypeDescription] || 'text';
}

/**
 * Constrói objeto de formato com base no tipo e nos valores extraídos
 * @param type Tipo de formatação
 * @param match Resultado da regex
 * @returns Objeto de formato
 */
function buildFormatObject(type: FormatType, match: RegExpExecArray): any {
  switch (type) {
    case 'number':
      return {
        decimal: match[3] ? parseInt(match[3], 10) : 0,
      };

    case 'currency':
      return {
        decimal: match[3] ? parseInt(match[3], 10) : 2,
        symbol: match[4] || '$',
      };

    case 'percentage':
      return {
        decimal: match[3] ? parseInt(match[3], 10) : 0,
      };

    case 'date':
      return {
        format: match[5] || 'dd/MM/yyyy',
      };

    case 'background':
      return {
        backgroundColor: match[3] || '#FFFFFF',
      };

    case 'font':
      return {
        fontName: match[4] || undefined,
        fontSize: match[5] ? parseInt(match[5], 10) : undefined,
        bold: match[6]
          ? match[6].toLowerCase() === 'sim' || match[6].toLowerCase() === 'true'
          : undefined,
        italic: match[7]
          ? match[7].toLowerCase() === 'sim' || match[7].toLowerCase() === 'true'
          : undefined,
        color: match[3] || undefined,
      };

    case 'border':
      return {
        borderStyle: mapBorderStyle(match[8] || 'thin'),
        borderColor: match[3] || '#000000',
      };

    case 'alignment':
      return {
        horizontal: mapAlignment(match[9] || 'left', 'horizontal'),
        vertical: mapAlignment(match[10] || 'middle', 'vertical'),
        wrapText: match[11]
          ? match[11].toLowerCase() === 'sim' || match[11].toLowerCase() === 'true'
          : undefined,
      };

    default:
      return {};
  }
}

/**
 * Mapeia descrição de estilo de borda para valor válido
 * @param borderStyle Descrição do estilo de borda
 * @returns Estilo de borda válido
 */
function mapBorderStyle(
  borderStyle: string
): 'thin' | 'medium' | 'thick' | 'dashed' | 'dotted' | 'double' | 'none' {
  const styleMap: Record<
    string,
    'thin' | 'medium' | 'thick' | 'dashed' | 'dotted' | 'double' | 'none'
  > = {
    fina: 'thin',
    média: 'medium',
    grossa: 'thick',
    tracejada: 'dashed',
    pontilhada: 'dotted',
    dupla: 'double',
    nenhuma: 'none',

    // English equivalents
    thin: 'thin',
    medium: 'medium',
    thick: 'thick',
    dashed: 'dashed',
    dotted: 'dotted',
    double: 'double',
    none: 'none',
  };

  return styleMap[borderStyle.toLowerCase()] || 'thin';
}

/**
 * Mapeia descrição de alinhamento para valor válido
 * @param alignment Descrição do alinhamento
 * @param direction Direção do alinhamento ('horizontal' ou 'vertical')
 * @returns Alinhamento válido
 */
function mapAlignment(
  alignment: string,
  direction: 'horizontal' | 'vertical'
): 'left' | 'center' | 'right' | 'top' | 'middle' | 'bottom' | undefined {
  if (direction === 'horizontal') {
    const horizontalMap: Record<string, 'left' | 'center' | 'right'> = {
      esquerda: 'left',
      centro: 'center',
      direita: 'right',

      // English equivalents
      left: 'left',
      center: 'center',
      right: 'right',
    };

    return horizontalMap[alignment.toLowerCase()];
  } else {
    const verticalMap: Record<string, 'top' | 'middle' | 'bottom'> = {
      topo: 'top',
      meio: 'middle',
      centro: 'middle',
      base: 'bottom',

      // English equivalents
      top: 'top',
      middle: 'middle',
      bottom: 'bottom',
    };

    return verticalMap[alignment.toLowerCase()];
  }
}
