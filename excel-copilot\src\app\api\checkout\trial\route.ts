import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';

import { logger } from '@/lib/logger';
import { stripe, getPriceIdFromPlan, PLANS } from '@/lib/stripe';
import { prisma } from '@/server/db/client';

// Forçar o modo dinâmico para essa rota
export const dynamic = 'force-dynamic';

const TRIAL_PERIOD_DAYS = 7;

// Interface para tipo de usuário com ID
interface UserWithId {
  id: string;
  email?: string | null;
  name?: string | null;
}

export async function GET(req: NextRequest) {
  try {
    // Verificar se o Stripe está configurado
    if (!stripe) {
      return NextResponse.redirect(new URL('/dashboard?error=stripe-not-configured', req.url));
    }

    // Verificar autenticação
    const session = await getServerSession();
    if (!session?.user) {
      return NextResponse.redirect(new URL('/auth/signin', req.url));
    }

    // Obter o usuário do banco de dados
    const userId = (session.user as UserWithId).id;

    // Buscar todas as assinaturas do usuário para verificar se já usou trial
    const subscriptions = await prisma.subscription.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' },
    });

    const user = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      return NextResponse.redirect(new URL('/dashboard?error=user-not-found', req.url));
    }

    // Verificar se o usuário já utilizou o período de trial antes
    const hasUsedTrial = subscriptions.some(
      sub => sub.plan !== PLANS.FREE && sub.status !== 'canceled'
    );

    if (hasUsedTrial) {
      // Se já usou trial, redirecionar para a página de preços com mensagem
      return NextResponse.redirect(new URL('/pricing?error=trial-already-used', req.url));
    }

    let customerId = subscriptions[0]?.stripeCustomerId;

    // Se não houver ID de cliente, criar um novo cliente no Stripe
    if (!customerId) {
      // Criar objeto de parâmetros com verificações de tipo para evitar undefined
      const customerParams: { metadata: { userId: string }; email?: string; name?: string } = {
        metadata: {
          userId: user.id,
        },
      };

      // Adicionar apenas se não forem null ou undefined
      if (user.email) customerParams.email = user.email;
      if (user.name) customerParams.name = user.name;

      const customer = await stripe.customers.create(customerParams);
      customerId = customer.id;
    }

    // Usar o plano Pro mensal para o trial
    const priceId = getPriceIdFromPlan(PLANS.PRO_MONTHLY);

    // Criar sessão de checkout com trial
    const checkoutSession = await stripe.checkout.sessions.create({
      customer: customerId,
      line_items: [
        {
          price: priceId,
          quantity: 1,
        },
      ],
      mode: 'subscription',
      success_url: `${req.headers.get('origin')}/dashboard?checkout=success&trial=true`,
      cancel_url: `${req.headers.get('origin')}/dashboard?checkout=cancelled`,
      allow_promotion_codes: true,
      billing_address_collection: 'auto',
      metadata: {
        userId: user.id,
        plan: PLANS.PRO_MONTHLY,
        isTrial: 'true',
      },
      payment_method_types: ['card'],
      locale: 'pt-BR',
      subscription_data: {
        trial_period_days: TRIAL_PERIOD_DAYS,
        metadata: {
          userId: user.id,
          plan: PLANS.PRO_MONTHLY,
          isTrial: 'true',
        },
      },
    });

    // Redirecionar para o checkout do Stripe
    return NextResponse.redirect(checkoutSession.url as string);
  } catch (error) {
    logger.error('[TRIAL_CHECKOUT_ERROR]', error);

    // Em caso de erro, redirecionar de volta para o dashboard
    return NextResponse.redirect(new URL('/dashboard?error=checkout-failed', req.url));
  }
}
