import { createClient } from '@supabase/supabase-js';

// Verificar se estamos no servidor ou cliente
const isServer = typeof window === 'undefined';

// Verificar variáveis de ambiente públicas (disponíveis no cliente)
if (!process.env.NEXT_PUBLIC_SUPABASE_URL) {
  throw new Error('NEXT_PUBLIC_SUPABASE_URL não está configurada');
}

if (!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
  throw new Error('NEXT_PUBLIC_SUPABASE_ANON_KEY não está configurada');
}

// Verificar variáveis de ambiente do servidor apenas no servidor
if (isServer) {
  if (!process.env.SUPABASE_URL) {
    console.warn('SUPABASE_URL não está configurada - usando NEXT_PUBLIC_SUPABASE_URL');
  }

  if (!process.env.SUPABASE_SERVICE_ROLE_KEY) {
    console.warn(
      'SUPABASE_SERVICE_ROLE_KEY não está configurada - cliente admin não estará disponível'
    );
  }
}

/**
 * Cliente Supabase para uso no cliente (com anon key)
 * Usado para operações do lado do cliente com RLS ativo
 * Sempre disponível tanto no servidor quanto no cliente
 */
export const supabaseClient = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
  {
    auth: {
      autoRefreshToken: !isServer,
      persistSession: !isServer,
      detectSessionInUrl: !isServer,
    },
    db: {
      schema: 'public',
    },
  }
);

/**
 * Cliente Supabase para uso no servidor (com service role)
 * Usado para operações administrativas que requerem privilégios elevados
 * Apenas disponível no servidor
 */
export const supabaseAdmin =
  isServer && process.env.SUPABASE_SERVICE_ROLE_KEY
    ? createClient(
        process.env.SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.SUPABASE_SERVICE_ROLE_KEY,
        {
          auth: {
            autoRefreshToken: false,
            persistSession: false,
          },
          db: {
            schema: 'public',
          },
        }
      )
    : null;

/**
 * Função para criar cliente Supabase no lado do servidor
 * com contexto de usuário específico
 */
export function createServerSupabaseClient(accessToken?: string) {
  if (!isServer) {
    throw new Error('createServerSupabaseClient só pode ser usado no servidor');
  }

  const supabaseUrl = process.env.SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL!;
  const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!serviceRoleKey) {
    throw new Error('SUPABASE_SERVICE_ROLE_KEY não está configurada');
  }

  return createClient(supabaseUrl, serviceRoleKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
    global: {
      headers: accessToken
        ? {
            Authorization: `Bearer ${accessToken}`,
          }
        : {},
    },
  });
}

/**
 * Tipos para melhor tipagem
 */
export type SupabaseClient = typeof supabaseClient;
export type SupabaseAdmin = typeof supabaseAdmin;

/**
 * Utilitários para verificação de conectividade
 */
export async function testSupabaseConnection() {
  try {
    // Testar cliente anon
    const { error: anonError } = await supabaseClient.auth.getSession();
    if (anonError && anonError.message !== 'Auth session missing!') {
      throw anonError;
    }

    let adminResult = { success: false, buckets: 0 };

    // Testar cliente admin apenas se estiver disponível
    if (supabaseAdmin) {
      try {
        const { data: adminData, error: adminError } = await supabaseAdmin.storage.listBuckets();
        if (adminError) {
          throw adminError;
        }
        adminResult = { success: true, buckets: adminData.length };
      } catch (adminError) {
        console.warn('Cliente admin não disponível:', adminError);
      }
    }

    return {
      success: true,
      anonClient: true,
      adminClient: adminResult.success,
      buckets: adminResult.buckets,
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Erro desconhecido',
    };
  }
}

/**
 * Função para obter informações do projeto
 */
export async function getProjectInfo() {
  const supabaseUrl = process.env.SUPABASE_URL || process.env.NEXT_PUBLIC_SUPABASE_URL;

  try {
    if (!supabaseAdmin) {
      return {
        url: supabaseUrl,
        buckets: [],
        connected: false,
        error: 'Cliente admin não disponível (apenas no servidor)',
      };
    }

    const { data: buckets } = await supabaseAdmin.storage.listBuckets();

    return {
      url: supabaseUrl,
      buckets: buckets?.map(b => ({ name: b.name, public: b.public })) || [],
      connected: true,
    };
  } catch (error) {
    return {
      url: supabaseUrl,
      buckets: [],
      connected: false,
      error: error instanceof Error ? error.message : 'Erro desconhecido',
    };
  }
}
