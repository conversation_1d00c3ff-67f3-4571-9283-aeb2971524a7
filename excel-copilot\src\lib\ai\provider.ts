/**
 * Provedor de IA para o Excel Copilot
 * Responsável pela integração com API de IA (Vertex AI)
 */

import { logger } from '../logger';
import { logError } from '../utils/error-utils';

// Import the browser-safe version
import { dynamicImportManager } from './dynamic-import';
import { geminiApiClient, AnalysisResult, FormulaResult, SendMessageOptions } from './gemini-api';
// Import dynamic import manager for server-side

// Re-export the interfaces - using explicit type exports for isolatedModules
export type { AnalysisResult, FormulaResult, SendMessageOptions } from './gemini-api';

/**
 * Provedor para integração com serviço de IA - versão segura para browser
 */
class AIProvider {
  private static instance: AIProvider;
  private isClient: boolean;

  private constructor() {
    this.isClient = typeof window !== 'undefined';
    logger.info(`AIProvider inicializado em ambiente: ${this.isClient ? 'cliente' : 'servidor'}`);
  }

  /**
   * Obter instância singleton do provedor de IA
   */
  public static getInstance(): AIProvider {
    if (!AIProvider.instance) {
      AIProvider.instance = new AIProvider();
    }
    return AIProvider.instance;
  }

  /**
   * Processar prompt de texto e retornar resposta da IA
   * @param prompt Texto para processar com IA
   * @param options Opções de configuração
   */
  async processPrompt(prompt: string, options?: SendMessageOptions): Promise<string> {
    try {
      // No cliente, usamos a implementação mock do browser
      if (this.isClient) {
        return await geminiApiClient.sendMessage(prompt, options);
      }

      // No servidor, carregamos o serviço real dinamicamente
      const aiService = await dynamicImportManager.getAIService();

      if (aiService) {
        // Se conseguimos carregar o serviço real, use-o
        return await aiService.sendMessage(prompt, options);
      } else {
        // Fallback para o cliente mock mesmo no servidor
        logger.warn('Não foi possível carregar o serviço AI real, usando mock no servidor');
        return await geminiApiClient.sendMessage(prompt, options);
      }
    } catch (error) {
      logError('Erro ao processar prompt com IA:', error);
      return `Erro ao processar solicitação: ${error instanceof Error ? error.message : String(error)}`;
    }
  }

  /**
   * Analisar dados do Excel usando IA
   * @param data Dados do Excel para análise
   * @param options Opções de configuração
   */
  async analyzeExcelData(data: any, options?: SendMessageOptions): Promise<AnalysisResult> {
    try {
      // No cliente, usamos a implementação mock do browser
      if (this.isClient) {
        return await geminiApiClient.analyzeExcelData(data, options);
      }

      // No servidor, carregamos o serviço real dinamicamente
      const aiService = await dynamicImportManager.getAIService();

      if (aiService) {
        // Se conseguimos carregar o serviço real, use-o
        return await aiService.analyzeExcelData(data, options);
      } else {
        // Fallback para o cliente mock mesmo no servidor
        logger.warn('Não foi possível carregar o serviço AI real, usando mock no servidor');
        return await geminiApiClient.analyzeExcelData(data, options);
      }
    } catch (error) {
      logError('Erro ao analisar dados do Excel com IA:', error);
      return {
        insights: [`Erro: ${error instanceof Error ? error.message : String(error)}`],
        suggestedOperations: [],
        summary: 'Não foi possível analisar os dados devido a um erro.',
      };
    }
  }

  /**
   * Gerar fórmula do Excel com base em descrição
   * @param description Descrição em linguagem natural do que a fórmula deve fazer
   * @param options Opções de configuração
   */
  async generateExcelFormula(
    description: string,
    options?: SendMessageOptions
  ): Promise<FormulaResult> {
    try {
      // No cliente, usamos a implementação mock do browser
      if (this.isClient) {
        return await geminiApiClient.generateExcelFormula(description, options);
      }

      // No servidor, carregamos o serviço real dinamicamente
      const aiService = await dynamicImportManager.getAIService();

      if (aiService) {
        // Se conseguimos carregar o serviço real, use-o
        return await aiService.generateExcelFormula(description, options);
      } else {
        // Fallback para o cliente mock mesmo no servidor
        logger.warn('Não foi possível carregar o serviço AI real, usando mock no servidor');
        return await geminiApiClient.generateExcelFormula(description, options);
      }
    } catch (error) {
      logError('Erro ao gerar fórmula Excel com IA:', error);
      return {
        formula: '',
        explanation: `Erro: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  }

  /**
   * Verificar status do serviço de IA
   * @returns true se o serviço estiver operacional
   */
  async healthCheck(): Promise<boolean> {
    try {
      // No cliente, usamos a implementação mock do browser
      if (this.isClient) {
        return await geminiApiClient.healthCheck();
      }

      // No servidor, carregamos o serviço real dinamicamente
      const aiService = await dynamicImportManager.getAIService();

      if (aiService) {
        // Se conseguimos carregar o serviço real, use-o
        return await aiService.healthCheck();
      } else {
        // Fallback para o cliente mock mesmo no servidor
        logger.warn('Não foi possível carregar o serviço AI real, usando mock no servidor');
        return await geminiApiClient.healthCheck();
      }
    } catch (error) {
      logError('Erro ao verificar saúde do serviço de IA:', error);
      return false;
    }
  }
}

// Exportar instância singleton
export const aiProvider = AIProvider.getInstance();
