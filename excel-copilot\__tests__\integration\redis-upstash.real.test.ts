/**
 * @jest-environment node
 */

import { Redis } from '@upstash/redis';
import * as dotenv from 'dotenv';
import * as path from 'path';

// Carregar variáveis de ambiente do arquivo .env.local
dotenv.config({ path: path.resolve(process.cwd(), '.env.local') });

// Este teste usa as credenciais reais, então só será executado se as variáveis estiverem definidas
const hasRedisConfig =
  typeof process.env.UPSTASH_REDIS_REST_URL === 'string' &&
  typeof process.env.UPSTASH_REDIS_REST_TOKEN === 'string';

// Executar testes apenas se a configuração existir
const testOrSkip = hasRedisConfig ? describe : describe.skip;

testOrSkip('Redis Upstash Integration Real Tests', () => {
  let redis: Redis;
  const testKey = `test-key-${Date.now()}`;
  const testValue = { data: 'test-value', timestamp: Date.now() };

  beforeAll(() => {
    // Inicializar cliente Redis com as credenciais da aplicação real
    redis = new Redis({
      url: process.env.UPSTASH_REDIS_REST_URL as string,
      token: process.env.UPSTASH_REDIS_REST_TOKEN as string,
    });
  });

  afterAll(async () => {
    // Limpar dados de teste
    if (redis) {
      try {
        await redis.del(testKey);
        await redis.del(`${testKey}-expiring`);
        await redis.del(`${testKey}-counter`);
        await redis.del(`chat-history:session-${Date.now()}`);
        await redis.del(`api-usage:user-${Date.now()}`);
      } catch (error) {
        console.error('Erro ao limpar chaves de teste:', error);
      }
    }
  });

  describe('Verificação de Conexão Real', () => {
    test('Pode conectar ao Redis Upstash', async () => {
      // Verificar conexão com um ping simples
      const pingResult = await redis.ping();
      expect(pingResult).toBe('PONG');
    });
  });

  describe('Operações Reais com Redis', () => {
    test('Pode armazenar e recuperar dados', async () => {
      // Armazenar dados reais
      const setResult = await redis.set(testKey, testValue);
      expect(setResult).toBe('OK');

      // Recuperar dados
      const retrievedValue = await redis.get(testKey);
      expect(retrievedValue).toEqual(testValue);
    });

    test('Pode definir expiração de chaves', async () => {
      const tempKey = `${testKey}-expiring`;

      // Armazenar com TTL de 2 segundos
      await redis.set(tempKey, 'expiring-value', { ex: 2 });

      // Verificar se existe
      let value = await redis.get(tempKey);
      expect(value).toBe('expiring-value');

      // Esperar expiração
      await new Promise(resolve => setTimeout(resolve, 2500));

      // Verificar se foi removido
      value = await redis.get(tempKey);
      expect(value).toBeNull();
    });

    test('Suporta operações de incremento/decremento', async () => {
      const counterKey = `${testKey}-counter`;

      // Inicializar contador
      await redis.set(counterKey, 0);

      // Incrementar
      const incResult = await redis.incr(counterKey);
      expect(incResult).toBe(1);

      // Incrementar com valor específico
      const incrByResult = await redis.incrby(counterKey, 5);
      expect(incrByResult).toBe(6);

      // Decrementar
      const decrResult = await redis.decr(counterKey);
      expect(decrResult).toBe(5);

      // Limpar
      await redis.del(counterKey);
    });
  });

  describe('Cenários de uso do Excel Copilot', () => {
    test('Pode armazenar histórico de conversas', async () => {
      const sessionId = `session-${Date.now()}`;
      const historyKey = `chat-history:${sessionId}`;

      const chatMessages = [
        { role: 'user', content: 'Como somar valores na coluna B?' },
        { role: 'assistant', content: 'Use a fórmula =SUM(B:B)' },
      ];

      // Armazenar histórico
      await redis.set(historyKey, chatMessages);

      // Recuperar histórico
      const retrievedHistory = await redis.get(historyKey);
      expect(retrievedHistory).toEqual(chatMessages);

      // Limpar
      await redis.del(historyKey);
    });

    test('Pode rastrear limites de API por usuário', async () => {
      const userId = `user-${Date.now()}`;
      const usageKey = `api-usage:${userId}`;

      // Inicializar contador
      await redis.set(usageKey, 0);

      // Simular 3 chamadas de API
      for (let i = 0; i < 3; i++) {
        await redis.incr(usageKey);
      }

      // Verificar contagem
      const usage = await redis.get(usageKey);
      expect(usage).toBe(3);

      // Limpar
      await redis.del(usageKey);
    });
  });
});
