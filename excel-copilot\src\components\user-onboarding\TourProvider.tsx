'use client';

import { useRouter } from 'next/navigation';
import React, { createContext, useState, useContext, useEffect, ReactNode } from 'react';

import { useToast } from '@/components/ui/use-toast';

// Definir os passos do tour
export type TourStep = {
  target: string; // Seletor CSS para o elemento alvo
  title: string;
  content: string;
  placement?: 'top' | 'right' | 'bottom' | 'left';
  action?: () => void;
};

// Tours disponíveis na aplicação
export type TourType = 'welcome' | 'workbook' | 'chat' | 'dashboard';

// Interface do contexto
type TourContextType = {
  currentTour: TourType | null;
  currentStep: number;
  startTour: (tourType: TourType) => void;
  endTour: () => void;
  nextStep: () => void;
  prevStep: () => void;
  isTourActive: boolean;
  shouldShowTour: (tourType: TourType) => boolean;
  markTourCompleted: (tourType: TourType) => void;
};

// Criar o contexto
const TourContext = createContext<TourContextType>({
  currentTour: null,
  currentStep: 0,
  startTour: () => {},
  endTour: () => {},
  nextStep: () => {},
  prevStep: () => {},
  isTourActive: false,
  shouldShowTour: () => false,
  markTourCompleted: () => {},
});

// Hook para usar o contexto
export const useTour = () => useContext(TourContext);

// Props do provider
interface TourProviderProps {
  children: ReactNode;
}

// Provider para gerenciar o tour
export function TourProvider({ children }: TourProviderProps) {
  const [currentTour, setCurrentTour] = useState<TourType | null>(null);
  const [currentStep, setCurrentStep] = useState(0);
  const [completedTours, setCompletedTours] = useState<TourType[]>([]);
  const _router = useRouter();
  const { toast } = useToast();

  // Recuperar tours completados do localStorage
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const savedTours = localStorage.getItem('excel-copilot-completed-tours');
      if (savedTours) {
        try {
          setCompletedTours(JSON.parse(savedTours));
        } catch (error) {
          console.error('Erro ao recuperar tours completados:', error);
        }
      }
    }
  }, []);

  // Salvar tours completados no localStorage
  const saveCompletedTours = (tours: TourType[]) => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('excel-copilot-completed-tours', JSON.stringify(tours));
    }
  };

  // Iniciar um tour
  const startTour = (tourType: TourType) => {
    setCurrentTour(tourType);
    setCurrentStep(0);
    toast({
      title: 'Tour iniciado',
      description: 'Vamos conhecer as principais funcionalidades do Excel Copilot',
    });
  };

  // Finalizar o tour atual
  const endTour = () => {
    if (currentTour) {
      markTourCompleted(currentTour);
    }
    setCurrentTour(null);
    setCurrentStep(0);
  };

  // Avançar para o próximo passo
  const nextStep = () => {
    const tours = getTours();
    if (currentTour && currentStep < tours[currentTour].length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      endTour();
    }
  };

  // Voltar para o passo anterior
  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  // Verificar se deve mostrar o tour
  const shouldShowTour = (tourType: TourType): boolean => {
    return !completedTours.includes(tourType);
  };

  // Marcar um tour como completado
  const markTourCompleted = (tourType: TourType) => {
    if (!completedTours.includes(tourType)) {
      const updatedTours = [...completedTours, tourType];
      setCompletedTours(updatedTours);
      saveCompletedTours(updatedTours);
      toast({
        title: 'Tour completado',
        description: 'Você concluiu o tour com sucesso!',
      });
    }
  };

  // Verificar se há algum tour ativo
  const isTourActive = currentTour !== null;

  return (
    <TourContext.Provider
      value={{
        currentTour,
        currentStep,
        startTour,
        endTour,
        nextStep,
        prevStep,
        isTourActive,
        shouldShowTour,
        markTourCompleted,
      }}
    >
      {children}
    </TourContext.Provider>
  );
}

// Definição dos passos de cada tour
export function getTours() {
  return {
    welcome: [
      {
        target: '#welcome-header',
        title: 'Bem-vindo ao Excel Copilot',
        content:
          'Este assistente vai ajudar você a explorar as funcionalidades do Excel usando linguagem natural.',
        placement: 'bottom',
      },
      {
        target: '#dashboard-button',
        title: 'Painel Principal',
        content: 'Acesse suas planilhas recentes e crie novas a partir daqui.',
        placement: 'right',
      },
      {
        target: '#create-button',
        title: 'Crie Planilhas',
        content: 'Você pode criar uma nova planilha do zero ou fazer upload de uma existente.',
        placement: 'left',
      },
    ],
    workbook: [
      {
        target: '#chat-tab',
        title: 'Conversação com IA',
        content:
          'Nesta aba você pode dar comandos em linguagem natural para manipular sua planilha.',
        placement: 'bottom',
      },
      {
        target: '#spreadsheet-container',
        title: 'Visualização da Planilha',
        content: 'Aqui você vê os resultados em tempo real enquanto trabalha com sua planilha.',
        placement: 'top',
      },
      {
        target: '#command-examples',
        title: 'Exemplos de Comandos',
        content: 'Use esses exemplos para começar. Basta clicar em um deles para usar no chat.',
        placement: 'left',
      },
    ],
    dashboard: [
      {
        target: '#recent-workbooks',
        title: 'Planilhas Recentes',
        content: 'Acesse rapidamente suas planilhas mais recentes aqui.',
        placement: 'bottom',
      },
      {
        target: '#templates-section',
        title: 'Templates Prontos',
        content: 'Use um de nossos templates para começar rapidamente.',
        placement: 'top',
      },
      {
        target: '#actions-menu',
        title: 'Ações Rápidas',
        content: 'Acesse rapidamente ações como criar, importar ou exportar planilhas.',
        placement: 'left',
      },
    ],
    chat: [
      {
        target: '#chat-input',
        title: 'Comandos em Linguagem Natural',
        content:
          "Digite comandos como 'Crie um gráfico de vendas por região' ou perguntas como 'Qual o total de vendas em 2023?'",
        placement: 'top',
      },
      {
        target: '#command-categories',
        title: 'Categorias de Comandos',
        content: 'Explore diferentes tipos de comandos organizados por categoria.',
        placement: 'right',
      },
      {
        target: '#operations-indicator',
        title: 'Operações Executadas',
        content: 'Veja quais operações foram executadas na sua planilha após cada comando.',
        placement: 'bottom',
      },
    ],
  };
}
