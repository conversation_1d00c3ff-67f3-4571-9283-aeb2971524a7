import { z } from 'zod';

import { cellSchema } from './workbook';

/**
 * Schemas para mensagens WebSocket
 */

// Tipos específicos de mensagens usando discriminated union
export const socketMessageSchema = z.discriminatedUnion('type', [
  // Mensagem para entrar em uma sala
  z.object({
    type: z.literal('join_room'),
    workbookId: z.string().cuid({ message: 'ID de workbook inválido' }),
  }),

  // Mensagem para sair de uma sala
  z.object({
    type: z.literal('leave_room'),
    workbookId: z.string().cuid({ message: 'ID de workbook inválido' }),
  }),

  // Mensagem para atualização de célula
  z.object({
    type: z.literal('cell_update'),
    workbookId: z.string().cuid({ message: 'ID de workbook inválido' }),
    sheetId: z.string().cuid({ message: 'ID de sheet inválido' }),
    cell: cellSchema,
  }),

  // Mensagem para indicar que usuário está digitando/editando
  z.object({
    type: z.literal('user_activity'),
    workbookId: z.string().cuid({ message: 'ID de workbook inválido' }),
    sheetId: z.string().cuid({ message: 'ID de sheet inválido' }),
    activity: z.enum(['typing', 'editing', 'idle']),
    position: z
      .object({
        row: z.number().int().nonnegative().optional(),
        col: z.number().int().nonnegative().optional(),
      })
      .optional(),
  }),

  // Mensagem para criar novo comentário
  z.object({
    type: z.literal('add_comment'),
    workbookId: z.string().cuid({ message: 'ID de workbook inválido' }),
    sheetId: z.string().cuid({ message: 'ID de sheet inválido' }),
    position: z.object({
      row: z.number().int().nonnegative(),
      col: z.number().int().nonnegative(),
    }),
    content: z.string().min(1, { message: 'Conteúdo do comentário é obrigatório' }),
  }),

  // Mensagem para heartbeat/ping
  z.object({
    type: z.literal('heartbeat'),
  }),
]);

// Schema para mensagens enviadas pelo servidor para o cliente
export const serverMessageSchema = z.discriminatedUnion('type', [
  // Notificação de atualização de célula
  z.object({
    type: z.literal('cell_updated'),
    sheetId: z.string().cuid(),
    cell: cellSchema,
    userId: z.string(),
    userName: z.string().optional(),
    timestamp: z.number(),
  }),

  // Notificação de usuário conectado/desconectado
  z.object({
    type: z.literal('user_presence'),
    workbookId: z.string().cuid(),
    users: z.array(
      z.object({
        id: z.string(),
        name: z.string().optional(),
        status: z.enum(['connected', 'disconnected']),
        activity: z.enum(['typing', 'editing', 'idle']).optional(),
        position: z
          .object({
            sheetId: z.string().optional(),
            row: z.number().int().nonnegative().optional(),
            col: z.number().int().nonnegative().optional(),
          })
          .optional(),
      })
    ),
  }),

  // Mensagem de erro
  z.object({
    type: z.literal('error'),
    message: z.string(),
    details: z.any().optional(),
  }),
]);

// Tipos inferidos para uso na aplicação
export type SocketMessage = z.infer<typeof socketMessageSchema>;
export type ServerMessage = z.infer<typeof serverMessageSchema>;
