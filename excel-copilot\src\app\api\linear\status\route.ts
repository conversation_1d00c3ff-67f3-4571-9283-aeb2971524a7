/**
 * API endpoint para status e métricas do Linear
 * GET /api/linear/status
 */

import { NextRequest, NextResponse } from 'next/server';

import { LinearMonitoringService } from '@/lib/linear-integration';
import { logger } from '@/lib/logger';

export async function GET(request: NextRequest) {
  try {
    const linearService = new LinearMonitoringService();

    // Obtém parâmetros da query
    const { searchParams } = new URL(request.url);
    const includeMetrics = searchParams.get('metrics') === 'true';
    const includeIssues = searchParams.get('issues') === 'true';

    // Dados básicos sempre incluídos
    const workspaceSummary = await linearService.getWorkspaceSummary();

    const response: Record<string, unknown> = {
      status: 'success',
      timestamp: new Date().toISOString(),
      workspace: workspaceSummary.workspace,
      summary: workspaceSummary.summary,
    };

    // Inclui métricas se solicitado
    if (includeMetrics) {
      try {
        const metrics = await linearService.getDevelopmentMetrics();
        response.metrics = metrics;
      } catch (error) {
        logger.warn('Erro ao obter métricas Linear:', error);
        response.metrics = { error: 'Falha ao obter métricas' };
      }
    }

    // Inclui issues recentes se solicitado
    if (includeIssues) {
      try {
        const excelCopilotIssues = await linearService.getExcelCopilotIssues();
        response.excelCopilotIssues = excelCopilotIssues;
        response.recentIssues = workspaceSummary.recentIssues;
      } catch (error) {
        logger.warn('Erro ao obter issues Linear:', error);
        response.issues = { error: 'Falha ao obter issues' };
      }
    }

    return NextResponse.json(response);
  } catch (error) {
    logger.error('Erro no endpoint Linear status:', error);

    return NextResponse.json(
      {
        status: 'error',
        message: error instanceof Error ? error.message : 'Erro interno do servidor',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, data } = body;

    const linearService = new LinearMonitoringService();

    switch (action) {
      case 'create_issue': {
        if (!data.title || !data.type) {
          return NextResponse.json({ error: 'Title e type são obrigatórios' }, { status: 400 });
        }

        const newIssue = await linearService.createExcelCopilotIssue({
          type: data.type,
          title: data.title,
          description: data.description || '',
          priority: data.priority,
          assigneeId: data.assigneeId,
        });

        return NextResponse.json({
          status: 'success',
          action: 'issue_created',
          issue: newIssue.issue,
          timestamp: new Date().toISOString(),
        });
      }

      case 'refresh_cache': {
        // Força atualização do cache (se implementado)
        const refreshedSummary = await linearService.getWorkspaceSummary();

        return NextResponse.json({
          status: 'success',
          action: 'cache_refreshed',
          summary: refreshedSummary.summary,
          timestamp: new Date().toISOString(),
        });
      }

      default:
        return NextResponse.json({ error: `Ação '${action}' não suportada` }, { status: 400 });
    }
  } catch (error) {
    logger.error('Erro no POST Linear status:', error);

    return NextResponse.json(
      {
        status: 'error',
        message: error instanceof Error ? error.message : 'Erro interno do servidor',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
