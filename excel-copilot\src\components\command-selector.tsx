'use client';

import { useRouter } from 'next/navigation';

import { CommandExamples } from '@/components/command-examples';

export function CommandSelector() {
  const router = useRouter();

  const handleCommandSelect = (command: string) => {
    // Na implementação real, podemos redirecionar para criar uma nova planilha
    // com este comando pré-preenchido na interface de chat
    router.push(`/workbook/new?command=${encodeURIComponent(command)}`);
  };

  return <CommandExamples onSelect={handleCommandSelect} />;
}
