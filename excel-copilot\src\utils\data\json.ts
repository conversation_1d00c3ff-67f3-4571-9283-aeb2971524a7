/**
 * Utilitários para manipulação de dados JSON
 *
 * Funções puras para processamento e manipulação de JSON.
 */
import { extractGroup } from '@/utils/regex-utils';

/**
 * Tenta parsear uma string JSON de forma segura.
 * Retorna undefined se ocorrer erro.
 *
 * @param jsonString String a ser convertida para objeto
 * @returns Objeto parseado ou undefined em caso de erro
 */
export function parseJsonSafely<T = any>(jsonString: string): T | undefined {
  if (!jsonString) return undefined;

  try {
    // Tentar extrair JSON da resposta se estiver em formato markdown
    let contentToparse = jsonString;

    // Verificar se está em bloco de código markdown
    const jsonMatch = jsonString.match(/```(?:json)?\s*([\s\S]*?)\s*```/);
    if (jsonMatch) {
      contentToparse = extractGroup(jsonMatch, 1);
    }

    // Verificar se é um objeto JSON completo
    if (!contentToparse.startsWith('{') && !contentToparse.startsWith('[')) {
      // Tentar encontrar um objeto JSON na string
      const objectMatch = jsonString.match(/(\{[\s\S]*\}|\[[\s\S]*\])/);
      if (objectMatch) {
        contentToparse = extractGroup(objectMatch, 1);
      }
    }

    return JSON.parse(contentToparse) as T;
  } catch (error) {
    // Usar console.warn em vez de logger para manter como função pura
    if (process.env.NODE_ENV !== 'production') {
      console.warn('Erro ao fazer parse de JSON:', {
        error,
        preview: jsonString.substring(0, 200) + (jsonString.length > 200 ? '...' : ''),
      });
    }
    return undefined;
  }
}

/**
 * Converte um objeto para string JSON de forma segura,
 * tratando referências circulares
 *
 * @param obj Objeto a ser convertido para string JSON
 * @returns String JSON ou undefined em caso de erro
 */
export function stringifyJsonSafely(obj: any): string | undefined {
  if (obj === undefined || obj === null) return undefined;

  try {
    // Tratar referências circulares
    const cache = new Set();

    return JSON.stringify(
      obj,
      (_key, value) => {
        if (typeof value === 'object' && value !== null) {
          if (cache.has(value)) {
            return '[Circular]';
          }
          cache.add(value);
        }
        return value;
      },
      2
    );
  } catch (error) {
    // Usar console.warn em vez de logger para manter como função pura
    if (process.env.NODE_ENV !== 'production') {
      console.warn('Erro ao converter objeto para JSON:', error);
    }
    return undefined;
  }
}

/**
 * Verifica se uma string é um JSON válido
 *
 * @param str String a ser verificada
 * @returns true se for um JSON válido
 */
export function isValidJson(str: string): boolean {
  if (!str) return false;

  try {
    JSON.parse(str);
    return true;
  } catch {
    return false;
  }
}

/**
 * Compara dois objetos JSON para verificar igualdade estrutural
 *
 * @param obj1 Primeiro objeto
 * @param obj2 Segundo objeto
 * @returns true se os objetos forem estruturalmente iguais
 */
export function areJsonEqual(obj1: any, obj2: any): boolean {
  try {
    return JSON.stringify(obj1) === JSON.stringify(obj2);
  } catch {
    return false;
  }
}
