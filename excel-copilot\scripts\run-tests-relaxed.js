#!/usr/bin/env node

/**
 * Script para executar testes com verificações de tipo mais relaxadas
 * Útil durante o desenvolvimento e para resolver erros de tipo em testes
 */

import { execSync } from 'child_process';
import path from 'path';
import fs from 'fs';

// Cores para mensagens no console
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

console.log(`${colors.blue}Executando testes com verificações de tipo relaxadas${colors.reset}`);

try {
  // Verifica se o arquivo tsconfig.test.json existe
  const tsconfigTestPath = path.resolve(process.cwd(), 'tsconfig.test.json');
  if (!fs.existsSync(tsconfigTestPath)) {
    console.error(`${colors.red}Erro: Arquivo tsconfig.test.json não encontrado${colors.reset}`);
    process.exit(1);
  }

  console.log(`${colors.cyan}Utilizando configuração: ${tsconfigTestPath}${colors.reset}`);

  // Executa o TypeScript em modo verificação com configurações relaxadas
  console.log(`${colors.yellow}Verificando tipos com configurações relaxadas...${colors.reset}`);
  execSync('npx tsc --noEmit --project tsconfig.test.json', { stdio: 'inherit' });

  console.log(`${colors.green}Verificação de tipos concluída com sucesso!${colors.reset}`);

  // Executa os testes
  console.log(`${colors.yellow}Executando testes...${colors.reset}`);
  execSync('npm test', { stdio: 'inherit' });

  console.log(`${colors.green}Testes concluídos com sucesso!${colors.reset}`);
} catch (error) {
  console.error(`${colors.red}Falha na execução:${colors.reset}`, error.message);
  process.exit(1);
}
