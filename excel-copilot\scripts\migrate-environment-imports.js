#!/usr/bin/env node

/**
 * 🔄 MIGRAÇÃO DE IMPORTS DE ENVIRONMENT - FASE 3 OTIMIZAÇÃO
 *
 * Script para migrar todas as importações de '@/config/environment'
 * para '@/config/unified-environment' de forma segura.
 *
 * <AUTHOR> Copilot Team - Fase 3 Auditoria
 * @version 1.0.0
 */

const fs = require('fs');
const path = require('path');

// Cores para output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m',
};

// Lista de arquivos que precisam ser migrados
const filesToMigrate = [
  'src/app/api/ai/status/route.ts',
  'src/app/api/auth/debug/route.ts',
  'src/app/api/auth/debug-oauth/route.ts',
  'src/app/api/auth/debug-providers/route.ts',
  'src/app/api/auth/health/route.ts',
  'src/app/api/auth/reset-rate-limit/route.ts',
  'src/app/api/auth/test-config/route.ts',
  'src/app/api/auth/test-providers/route.ts',
  'src/app/api/init.ts',
  'src/app/api/stripe/status/route.ts',
  'src/app/api/vercel/env/route.ts',
  'src/app/api/vercel/status/route.ts',
  'src/app/api/workbooks/recent/route.ts',
  'src/app/api/workbooks/shared/route.ts',
  'src/app/auth/signin/page.tsx',
  'src/lib/ai/ai-factory.ts',
  'src/lib/ai/conditional-import.ts',
  'src/lib/ai/dynamic-import.ts',
  'src/lib/ai/gemini-service.ts',
  'src/lib/app-initializer.ts',
  'src/lib/auth-config.ts',
  'src/lib/cache-manager.ts',
  'src/lib/github-integration.ts',
  'src/lib/health-checker.ts',
  'src/lib/linear-integration.ts',
  'src/lib/middleware/payment-limiter.ts',
  'src/lib/middleware/rate-limiter.ts',
  'src/lib/security/enhanced-rate-limiter.ts',
  'src/lib/supabase-integration.ts',
  'src/lib/telemetry.ts',
  'src/server/ai/gemini-service.ts',
  'src/server/ai/vertex-ai-service.ts',
  'src/server/db/query-cache.ts',
];

/**
 * Classe principal para migração de imports
 */
class EnvironmentImportMigrator {
  constructor() {
    this.rootDir = path.join(__dirname, '..');
    this.migratedFiles = [];
    this.failedFiles = [];
    this.skippedFiles = [];
  }

  /**
   * Executa a migração completa
   */
  async run() {
    console.log(
      `${colors.blue}${colors.bold}🔄 MIGRAÇÃO DE IMPORTS - FASE 3 OTIMIZAÇÃO${colors.reset}`
    );
    console.log('='.repeat(70));
    console.log();

    console.log(`${colors.yellow}📋 Arquivos a migrar: ${filesToMigrate.length}${colors.reset}`);
    console.log();

    // Verificar se unified-environment.ts existe
    const unifiedEnvPath = path.join(this.rootDir, 'src/config/unified-environment.ts');
    if (!fs.existsSync(unifiedEnvPath)) {
      console.log(`${colors.red}❌ Erro: unified-environment.ts não encontrado!${colors.reset}`);
      return false;
    }

    // Migrar cada arquivo
    for (const filePath of filesToMigrate) {
      await this.migrateFile(filePath);
    }

    // Gerar relatório
    this.generateReport();

    return this.failedFiles.length === 0;
  }

  /**
   * Migra um arquivo específico
   */
  async migrateFile(relativePath) {
    const fullPath = path.join(this.rootDir, relativePath);

    try {
      // Verificar se arquivo existe
      if (!fs.existsSync(fullPath)) {
        console.log(`${colors.yellow}⚠️  Arquivo não encontrado: ${relativePath}${colors.reset}`);
        this.skippedFiles.push(relativePath);
        return;
      }

      // Ler conteúdo do arquivo
      const content = fs.readFileSync(fullPath, 'utf8');

      // Verificar se precisa de migração
      if (!content.includes("from '@/config/environment'")) {
        console.log(`${colors.blue}ℹ️  Já migrado: ${relativePath}${colors.reset}`);
        this.skippedFiles.push(relativePath);
        return;
      }

      // Fazer backup
      const backupPath = `${fullPath}.backup-migration`;
      fs.writeFileSync(backupPath, content);

      // Aplicar migração
      const migratedContent = content.replace(
        /from '@\/config\/environment'/g,
        "from '@/config/unified-environment'"
      );

      // Verificar se houve mudança
      if (migratedContent === content) {
        console.log(
          `${colors.yellow}⚠️  Nenhuma mudança necessária: ${relativePath}${colors.reset}`
        );
        fs.unlinkSync(backupPath); // Remover backup desnecessário
        this.skippedFiles.push(relativePath);
        return;
      }

      // Salvar arquivo migrado
      fs.writeFileSync(fullPath, migratedContent);

      console.log(`${colors.green}✅ Migrado: ${relativePath}${colors.reset}`);
      this.migratedFiles.push(relativePath);

      // Remover backup se tudo deu certo
      fs.unlinkSync(backupPath);
    } catch (error) {
      console.log(
        `${colors.red}❌ Erro ao migrar ${relativePath}: ${error.message}${colors.reset}`
      );
      this.failedFiles.push({ file: relativePath, error: error.message });
    }
  }

  /**
   * Gera relatório da migração
   */
  generateReport() {
    console.log();
    console.log(`${colors.blue}${colors.bold}📊 RELATÓRIO DE MIGRAÇÃO${colors.reset}`);
    console.log('='.repeat(50));
    console.log();

    console.log(`${colors.green}✅ Arquivos migrados: ${this.migratedFiles.length}${colors.reset}`);
    console.log(`${colors.blue}ℹ️  Arquivos ignorados: ${this.skippedFiles.length}${colors.reset}`);
    console.log(`${colors.red}❌ Arquivos com erro: ${this.failedFiles.length}${colors.reset}`);
    console.log();

    if (this.migratedFiles.length > 0) {
      console.log(`${colors.green}📁 Arquivos migrados com sucesso:${colors.reset}`);
      this.migratedFiles.forEach(file => {
        console.log(`  ✅ ${file}`);
      });
      console.log();
    }

    if (this.failedFiles.length > 0) {
      console.log(`${colors.red}❌ Arquivos com erro:${colors.reset}`);
      this.failedFiles.forEach(({ file, error }) => {
        console.log(`  ❌ ${file}: ${error}`);
      });
      console.log();
    }

    // Status final
    if (this.failedFiles.length === 0) {
      console.log(`${colors.green}${colors.bold}🎉 MIGRAÇÃO CONCLUÍDA COM SUCESSO!${colors.reset}`);
      console.log(
        `${colors.green}Todos os imports foram migrados para unified-environment.ts${colors.reset}`
      );
    } else {
      console.log(`${colors.red}${colors.bold}⚠️  MIGRAÇÃO PARCIAL${colors.reset}`);
      console.log(`${colors.red}Alguns arquivos falharam na migração${colors.reset}`);
    }
    console.log();
  }
}

// Executar migração se chamado diretamente
if (require.main === module) {
  const migrator = new EnvironmentImportMigrator();
  migrator
    .run()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error(`${colors.red}Erro fatal: ${error.message}${colors.reset}`);
      process.exit(1);
    });
}

module.exports = EnvironmentImportMigrator;
