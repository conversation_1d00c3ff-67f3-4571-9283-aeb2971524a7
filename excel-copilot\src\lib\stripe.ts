import { loadStripe, Stripe as StripeClient } from '@stripe/stripe-js';
import Stripe from 'stripe';

// Constantes de preços do Stripe - usando variáveis de ambiente
export const STRIPE_PRICE_IDS = {
  PRO_MONTHLY: process.env.NEXT_PUBLIC_STRIPE_PRICE_MONTHLY || '',
  PRO_ANNUAL: process.env.NEXT_PUBLIC_STRIPE_PRICE_ANNUAL || '',
};

// Constantes de planos
export const PLANS = {
  FREE: 'free',
  PRO_MONTHLY: 'pro_monthly',
  PRO_ANNUAL: 'pro_annual',
};

// Limites de API por plano
export const API_CALL_LIMITS: Record<string, number> = {
  [PLANS.FREE]: 50,
  [PLANS.PRO_MONTHLY]: 500,
  [PLANS.PRO_ANNUAL]: 1000,
};

// Chaves do Stripe usando variáveis de ambiente
const STRIPE_SECRET_KEY = process.env.STRIPE_SECRET_KEY || '';
const STRIPE_PUBLISHABLE_KEY = process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY || '';
export const STRIPE_WEBHOOK_SECRET = process.env.STRIPE_WEBHOOK_SECRET || '';

// Instância Stripe para backend
export const stripe = STRIPE_SECRET_KEY
  ? new Stripe(STRIPE_SECRET_KEY, {
      apiVersion: '2023-10-16' as Stripe.LatestApiVersion,
      appInfo: {
        name: 'Excel Copilot',
        version: '1.0.0',
      },
    })
  : null;

// Cliente Stripe para frontend
let stripePromise: Promise<StripeClient | null>;
export const getStripe = () => {
  if (!stripePromise) {
    if (!STRIPE_PUBLISHABLE_KEY) {
      console.error('Chave pública do Stripe não está definida');
      return Promise.resolve(null);
    }
    stripePromise = loadStripe(STRIPE_PUBLISHABLE_KEY);
  }
  return stripePromise;
};

// Funções utilitárias

/**
 * Retorna o priceId do Stripe com base no plano selecionado
 */
export function getPriceIdFromPlan(plan: string): string {
  switch (plan) {
    case PLANS.PRO_MONTHLY:
      return STRIPE_PRICE_IDS.PRO_MONTHLY;
    case PLANS.PRO_ANNUAL:
      return STRIPE_PRICE_IDS.PRO_ANNUAL;
    default:
      return STRIPE_PRICE_IDS.PRO_MONTHLY; // Padrão
  }
}

/**
 * Normaliza o status de assinatura do Stripe para usar em nossa aplicação
 */
export function normalizeSubscriptionStatus(stripeStatus: string): string {
  switch (stripeStatus) {
    case 'active':
    case 'trialing':
      return 'active';
    case 'canceled':
    case 'unpaid':
    case 'incomplete_expired':
      return 'canceled';
    case 'past_due':
      return 'past_due';
    case 'incomplete':
      return 'incomplete';
    default:
      return 'unknown';
  }
}

/**
 * Formata um valor monetário com símbolo de moeda
 */
export function formatCurrency(amount: number, currency: string = 'BRL'): string {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency,
    minimumFractionDigits: 2,
  }).format(amount / 100);
}

/**
 * Valida se um plano é plano pago
 */
export function isPaidPlan(plan: string): boolean {
  return plan === PLANS.PRO_ANNUAL || plan === PLANS.PRO_MONTHLY;
}

/**
 * Retorna o nome amigável do plano
 */
export function getPlanDisplayName(plan: string): string {
  switch (plan) {
    case PLANS.FREE:
      return 'Grátis';
    case PLANS.PRO_MONTHLY:
      return 'Pro Mensal';
    case PLANS.PRO_ANNUAL:
      return 'Pro Anual';
    default:
      return 'Desconhecido';
  }
}

/**
 * Retorna o preço formatado do plano
 */
export function getPlanPrice(plan: string): string {
  switch (plan) {
    case PLANS.FREE:
      return 'R$ 0';
    case PLANS.PRO_MONTHLY:
      return 'R$ 20/mês';
    case PLANS.PRO_ANNUAL:
      return 'R$ 16,67/mês (R$ 200/ano)';
    default:
      return '';
  }
}

// Determinar o limite de API com base no plano
export const getApiLimit = (plan: string | null): number => {
  // Valor padrão seguro
  const defaultLimit = API_CALL_LIMITS[PLANS.FREE] ?? 50;

  if (!plan) {
    return defaultLimit;
  }

  // Verificar se o plano é uma chave válida de PLANS
  const validPlans = Object.values(PLANS) as string[];
  if (!validPlans.includes(plan)) {
    return defaultLimit;
  }

  // Usar operador de coalescência nula para garantir que sempre retornamos um número
  return API_CALL_LIMITS[plan as keyof typeof API_CALL_LIMITS] ?? defaultLimit;
};

// API pública para frontend - Implementação real
export const getStripePublicKey = () => {
  const key = STRIPE_PUBLISHABLE_KEY;

  // Log de debug apenas em desenvolvimento
  if (process.env.NODE_ENV === 'development') {
    // eslint-disable-next-line no-console
    console.log('🔍 Debug - Stripe Public Key:', key ? `${key.substring(0, 12)}...` : 'UNDEFINED');
    // eslint-disable-next-line no-console
    console.log(
      '🔍 Debug - Env NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY:',
      process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY
        ? `${process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY.substring(0, 12)}...`
        : 'UNDEFINED'
    );
  }

  return key;
};
