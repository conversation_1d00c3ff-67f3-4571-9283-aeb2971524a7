'use client';

import { Users, History, X } from 'lucide-react';
import { useSession } from 'next-auth/react';
import { useState, useEffect } from 'react';

import { CollaborationIndicator } from '@/components/collaboration-indicator';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { <PERSON><PERSON>, <PERSON>etContent, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet';
import { useCollabStore, type CellEdit } from '@/lib/collaborative-sync';
import { cn } from '@/lib/utils';

/**
 * Painel completo de colaboração para edição em tempo real
 */
export function CollaborationPanel({
  workbookId,
  onApplyEdit,
}: {
  workbookId: string;
  onApplyEdit?: (edit: CellEdit) => void;
}) {
  const [open, setOpen] = useState(false);
  const session = useSession();
  const {
    connect,
    disconnect,
    connected,
    pendingEdits,
    submitCellEdit: _submitCellEdit,
    acknowledgeEdit,
    userName: _userName,
    userId: _userId,
  } = useCollabStore();

  // Conectar ao servidor de colaboração quando o componente montar
  useEffect(() => {
    if (workbookId && session.data?.user) {
      const name = session.data.user.name || 'Usuário Anônimo';
      // Garantir acesso ao ID com tipagem correta
      const id =
        typeof session.data.user === 'object' && 'id' in session.data.user
          ? (session.data.user as any).id
          : `anon-${Math.random().toString(36).substring(2, 9)}`;

      connect(workbookId, id, name);

      return () => {
        disconnect();
      };
    }
  }, [workbookId, session.data, connect, disconnect]);

  const handleApplyEdit = (edit: CellEdit) => {
    if (onApplyEdit) {
      onApplyEdit(edit);
    }
    acknowledgeEdit(edit.id);
  };

  // Agrupar edições por usuário para melhor organização
  const editsByUser: Record<string, CellEdit[]> = {};

  if (Array.isArray(pendingEdits)) {
    pendingEdits.forEach(edit => {
      if (!edit || typeof edit !== 'object' || !edit.userId) return;

      // Inicializa o array para este usuário se ainda não existir
      if (!editsByUser[edit.userId]) {
        editsByUser[edit.userId] = [];
      }

      // Acesso seguro com type assertion
      const userEdits = editsByUser[edit.userId];
      if (Array.isArray(userEdits)) {
        userEdits.push(edit);
      } else {
        // Reinicialização em caso de corrupção
        editsByUser[edit.userId] = [edit];
      }
    });
  }

  return (
    <>
      {/* Indicador de usuários online que sempre fica visível */}
      <div className="fixed bottom-4 right-4 z-50">
        <Sheet open={open} onOpenChange={setOpen}>
          <SheetTrigger asChild>
            <Button
              variant="outline"
              size="icon"
              className={cn(
                'h-10 w-10 rounded-full border',
                'flex items-center justify-center',
                'bg-background shadow-md',
                'hover:bg-accent hover:text-accent-foreground',
                connected && 'border-primary'
              )}
            >
              <Users
                className={cn('h-5 w-5', connected ? 'text-primary' : 'text-muted-foreground')}
              />
              {Array.isArray(pendingEdits) && pendingEdits.length > 0 && (
                <span className="absolute -top-1 -right-1 h-5 w-5 rounded-full bg-primary flex items-center justify-center text-xs text-white">
                  {pendingEdits.length}
                </span>
              )}
            </Button>
          </SheetTrigger>

          <SheetContent side="right" className="sm:max-w-md">
            <SheetHeader>
              <SheetTitle className="flex items-center">
                <Users className="mr-2 h-5 w-5 text-primary" />
                Colaboração em Tempo Real
              </SheetTitle>
            </SheetHeader>

            {/* Lista de colaboradores */}
            <div className="py-6">
              <h3 className="text-sm font-medium text-muted-foreground mb-2">Usuários Ativos</h3>
              <CollaborationIndicator _workbookId={workbookId} />
            </div>

            {/* Lista de alterações pendentes */}
            <div className="mb-2">
              <h3 className="text-sm font-medium flex items-center text-muted-foreground mb-2">
                <History className="mr-2 h-4 w-4" />
                Alterações Pendentes
              </h3>

              {Object.keys(editsByUser).length === 0 ? (
                <p className="text-sm text-muted-foreground my-4">Não há alterações pendentes.</p>
              ) : (
                <ScrollArea className="h-[300px] rounded-md border p-2">
                  {Object.entries(editsByUser).map(([userId, edits]) => (
                    <div key={userId} className="mb-4 border-b pb-3 last:border-0">
                      <p className="text-sm font-medium mb-2">
                        {Array.isArray(edits) && edits.length > 0 && edits[0]?.userName && (
                          <>{edits[0].userName} </>
                        )}
                        <span className="text-xs text-muted-foreground">
                          ({Array.isArray(edits) ? edits.length : 0} alterações)
                        </span>
                      </p>

                      <ul className="space-y-2">
                        {Array.isArray(edits) &&
                          edits.map(edit => (
                            <li
                              key={edit.id}
                              className="flex items-center justify-between p-2 rounded-md bg-accent/50 text-sm"
                            >
                              <div>
                                <span className="font-medium">{edit.cellId}</span>:{' '}
                                {typeof edit.value === 'string'
                                  ? String(edit.value).substring(0, 20)
                                  : ''}
                                {typeof edit.value === 'string' &&
                                  String(edit.value).length > 20 &&
                                  '...'}
                              </div>
                              <div className="flex gap-2">
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  className="h-6 w-6"
                                  onClick={() => acknowledgeEdit(edit.id)}
                                >
                                  <X className="h-3 w-3" />
                                </Button>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="h-6 px-2 text-xs"
                                  onClick={() => handleApplyEdit(edit)}
                                >
                                  Aplicar
                                </Button>
                              </div>
                            </li>
                          ))}
                      </ul>
                    </div>
                  ))}
                </ScrollArea>
              )}
            </div>
          </SheetContent>
        </Sheet>
      </div>
    </>
  );
}
