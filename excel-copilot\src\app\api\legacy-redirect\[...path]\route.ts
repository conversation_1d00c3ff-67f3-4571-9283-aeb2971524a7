import { NextRequest, NextResponse } from 'next/server';

import { trackLegacyApiUsage } from '@/lib/api-tracker';

export const dynamic = 'force-dynamic';

/**
 * Mapeamento de rotas antigas para novas rotas
 * Adicione aqui novos mapeamentos conforme necessário
 */
const API_ROUTE_MAPPING: Record<string, string> = {
  // Defina mapeamentos específicos aqui
  // Por exemplo: '/api/v1/users': '/api/users'
};

/**
 * Handler para redirecionar chamadas de API da estrutura pages/api para app/api
 *
 * Este handler captura todas as solicitações para /api/legacy-redirect/[...path]
 * e as redireciona para suas equivalentes no App Router
 */
export async function GET(request: NextRequest, { params }: { params: { path: string[] } }) {
  const path = Array.isArray(params.path) ? params.path.join('/') : params.path;
  const fullPath = `/api/${path}`;

  // Se tivermos um mapeamento específico, use-o
  const newPath = API_ROUTE_MAPPING[fullPath] || fullPath;

  // Rastrear uso da API legada
  trackLegacyApiUsage(
    fullPath,
    request.headers.get('user-agent') || undefined,
    request.headers.get('referer') || undefined
  );

  // Adicionar header de aviso de depreciação na resposta
  const response = NextResponse.redirect(new URL(newPath, request.url));
  response.headers.set('X-API-Deprecated', 'true');
  response.headers.set(
    'X-API-Deprecation-Warning',
    `Esta rota será descontinuada. Migre para a nova versão: ${newPath}`
  );

  return response;
}

export async function POST(request: NextRequest, { params }: { params: { path: string[] } }) {
  // Mesmo comportamento do GET
  return GET(request, { params });
}

export async function PUT(request: NextRequest, { params }: { params: { path: string[] } }) {
  // Mesmo comportamento do GET
  return GET(request, { params });
}

export async function DELETE(request: NextRequest, { params }: { params: { path: string[] } }) {
  // Mesmo comportamento do GET
  return GET(request, { params });
}

export async function PATCH(request: NextRequest, { params }: { params: { path: string[] } }) {
  // Mesmo comportamento do GET
  return GET(request, { params });
}
