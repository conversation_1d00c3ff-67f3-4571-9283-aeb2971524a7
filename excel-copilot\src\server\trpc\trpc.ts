import { initTRPC, TRPCError } from '@trpc/server';
import type { Session } from 'next-auth';
import { getServerSession } from 'next-auth/next';
import superjson from 'superjson';

import { AUTH_FLAGS } from '@/config/auth-flags';
import { validateEnvironment } from '@/lib/env-validator';
import { logger } from '@/lib/logger';
import { prisma } from '@/server/db/client';

// Verificar variáveis de ambiente durante inicialização do servidor usando o validador centralizado
// Mas não falhar durante o build
const envValidation = validateEnvironment();
if (!envValidation.valid) {
  logger.error(
    `Erro na configuração do servidor tRPC: Variáveis de ambiente ausentes: ${envValidation.missing.join(', ')}`
  );
  // Se estamos em produção, mas não estamos usando o modo mock, então lançar erro
  // Mas não durante o build (quando NEXT_PHASE é 'phase-production-build')
  const isBuildTime = process.env.NEXT_PHASE === 'phase-production-build';
  if (process.env.NODE_ENV === 'production' && process.env.AI_USE_MOCK !== 'true' && !isBuildTime) {
    throw new Error('Configuração de ambiente incompleta para ambiente de produção');
  } else if (process.env.NODE_ENV === 'production') {
    logger.warn('Usando configuração incompleta com modo mock ativado em produção');
  }
}

// Usar o tipo Session diretamente sem estendê-lo
type CreateContextOptions = {
  session: Session | null;
};

const createInnerTRPCContext = (opts: CreateContextOptions) => {
  return {
    session: opts.session,
    prisma,
  };
};

/**
 * Cria o contexto para o tRPC, tanto para App Router quanto Pages Router
 */
export const createTRPCContext = async (opts: unknown) => {
  // Detecta se estamos usando App Router ou Pages Router
  const isAppRouter =
    !opts || (typeof opts === 'object' && opts !== null && !('req' in opts && 'res' in opts));

  let session = null;

  try {
    if (isAppRouter) {
      // Para Next.js App Router
      session = (await getServerSession()) as Session | null;

      // Garantir que o usuário tenha um ID
      if (session?.user) {
        // Verifica se session.user tem uma propriedade id, se não tiver, usa 'unknown-id'
        const userId =
          typeof session.user === 'object' && session.user !== null && 'id' in session.user
            ? (session.user as { id?: string }).id
            : undefined;
        session.user.id = userId || 'unknown-id';
      }
    } else {
      // Para o adaptador Next.js Pages Router
      // Não usamos _req que é propriedade inexistente
      session = (await getServerSession()) as Session | null;

      // Garantir que o usuário tenha um ID
      if (session?.user) {
        // Verifica se session.user tem uma propriedade id, se não tiver, usa 'unknown-id'
        const userId =
          typeof session.user === 'object' && session.user !== null && 'id' in session.user
            ? (session.user as { id?: string }).id
            : undefined;
        session.user.id = userId || 'unknown-id';
      }
    }
  } catch (error) {
    logger.error('Erro ao obter sessão:', error);
  }

  return createInnerTRPCContext({ session });
};

const t = initTRPC.context<typeof createTRPCContext>().create({
  transformer: superjson,
  errorFormatter({ shape }) {
    return shape;
  },
});

export const createTRPCRouter = t.router;

export const publicProcedure = t.procedure;

// Middleware para proteger rotas que exigem autenticação
const enforceUserIsAuthed = t.middleware(({ ctx, next }) => {
  // Verificar se o usuário já está autenticado
  const isAuthenticated = Boolean(ctx.session?.user);

  // Verificar se devemos usar o usuário demo
  if (!isAuthenticated && AUTH_FLAGS.USE_DEMO_USER) {
    if (AUTH_FLAGS.VERBOSE_LOGGING) {
      logger.info('Autenticação com usuário demo ativada: Usando credenciais de demonstração');
    }

    // Criar sessão demo com usuário temporário
    return next({
      ctx: {
        session: {
          user: AUTH_FLAGS.DEMO_USER,
          expires: new Date(Date.now() + AUTH_FLAGS.DEMO_SESSION_EXPIRY).toISOString(),
        },
      },
    });
  }

  // Exigir autenticação real quando não estiver usando demo ou não houver sessão
  if (!isAuthenticated) {
    throw new TRPCError({
      code: 'UNAUTHORIZED',
      message: 'Você precisa estar autenticado para acessar este recurso',
    });
  }

  // Usuário autenticado normalmente
  return next({
    ctx: {
      // Inferir o tipo do usuário como não nulo
      session: { ...ctx.session, user: ctx.session?.user },
    },
  });
});

// Procedimento para rotas que exigem autenticação
export const protectedProcedure = t.procedure.use(enforceUserIsAuthed);
