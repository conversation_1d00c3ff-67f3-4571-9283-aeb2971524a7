import { NextRequest } from 'next/server';

// Remover import do Express já que não é necessário ou usar um tipo alternativo
// import { Request } from 'express';
import { logger } from '@/lib/logger';

export interface RateLimiterOptions {
  /**
   * Janela de tempo em milissegundos para contar as requisições
   */
  windowMs: number;

  /**
   * Número máximo de requisições permitidas na janela de tempo
   */
  maxRequests: number;

  /**
   * Mensagem de erro quando o limite é excedido
   */
  message?: string;

  /**
   * Se true, incrementa automaticamente o contador ao verificar
   * Default: true
   */
  autoIncrement?: boolean;

  /**
   * Número máximo de entradas para armazenar no mapa
   * Default: 1000
   */
  maxEntries?: number;

  /**
   * Intervalo para limpeza de entradas expiradas em ms (padrão: 5 minutos)
   */
  cleanupInterval?: number;

  /**
   * Número máximo de requisições (compatibilidade com API antiga)
   */
  max?: number;
}

/**
 * Resultado de uma verificação de rate limiting
 */
export interface RateLimiterResult {
  /**
   * Se o limite foi excedido
   */
  limited: boolean;

  /**
   * Mensagem de erro, se o limite foi excedido
   */
  message?: string | undefined;

  /**
   * Número de requisições restantes
   */
  remaining: number;

  /**
   * Quando o limite será redefinido (timestamp)
   */
  resetAt: number;

  /**
   * Identificador usado (IP ou outro valor)
   */
  identifier: string;

  /**
   * Se a requisição é permitida (compatibilidade com API antiga)
   */
  allowed?: boolean;

  /**
   * Tempo de reset (compatibilidade com API antiga)
   */
  resetTime?: number;

  /**
   * Tempo para tentar novamente (compatibilidade com API antiga)
   */
  retryAfter?: number;
}

interface RateLimiterEntry {
  count: number;
  resetAt: number;
  lastAccessed: number;
}

/**
 * Implementação simplificada de rate limiting em memória
 * Este serviço limita o número de requisições por IP/usuário em um intervalo de tempo
 */

// Tipo para contagem de requisições
interface RequestCount {
  count: number;
  resetTime: number;
}

// Parâmetros para check de rate limiting
export interface RateLimitCheckParams {
  key: string; // Identificador único (geralmente IP ou ID do usuário)
  endpoint?: string; // Endpoint opcional para limites específicos por rota
}

/**
 * Serviço de Rate Limiting para proteção contra abusos
 */
export class RateLimiterService {
  private readonly store: Map<string, RequestCount>;
  private readonly windowMs: number;
  private readonly maxRequests: number;

  /**
   * Cria uma nova instância do serviço de rate limiting
   * @param options Opções de configuração
   */
  constructor(options: RateLimiterOptions) {
    this.store = new Map();
    this.windowMs = options.windowMs;
    this.maxRequests = options.max || options.maxRequests;
  }

  /**
   * Verifica se uma requisição pode ser processada de acordo com os limites
   * @param params Parâmetros para verificação (key, endpoint)
   * @returns Resultado da verificação
   */
  public check(params: RateLimitCheckParams): RateLimiterResult {
    const { key, endpoint = 'global' } = params;
    const identifier = `${key}:${endpoint}`;
    const now = Date.now();

    // Obter contagem atual ou inicializar
    let record = this.store.get(identifier);

    if (!record || now > record.resetTime) {
      // Nova janela de tempo ou expirou
      record = {
        count: 0,
        resetTime: now + this.windowMs,
      };
    }

    // Incrementar contagem
    record.count += 1;

    // Verificar se excedeu o limite
    const isAllowed = record.count <= this.maxRequests;

    // Atualizar store
    this.store.set(identifier, record);

    // Calcular informações para resposta
    const remaining = Math.max(0, this.maxRequests - record.count);
    const retryAfter = isAllowed ? undefined : Math.ceil((record.resetTime - now) / 1000);

    return {
      allowed: isAllowed,
      limited: !isAllowed,
      remaining,
      resetTime: record.resetTime,
      resetAt: record.resetTime,
      identifier,
      ...(retryAfter && { retryAfter }),
    };
  }

  /**
   * Limpa entradas expiradas do store (para evitar vazamento de memória)
   */
  public cleanup(): void {
    const now = Date.now();

    for (const [key, record] of this.store.entries()) {
      if (now > record.resetTime) {
        this.store.delete(key);
      }
    }
  }
}

/**
 * Implementação de um rate limiter simples baseado em memória
 * com suporte a LRU (Least Recently Used) para evitar vazamento de memória
 * limitando o número de requisições por IP em um período de tempo
 */
export class RateLimiter {
  private options: RateLimiterOptions;
  private requestCounts: Map<string, RateLimiterEntry>;
  private cleanupInterval: NodeJS.Timeout | null = null;
  private activeLocks = new Set<string>();
  private lockQueue: Map<string, Array<() => void>> = new Map();
  private logger = logger.createChild({ prefix: 'RateLimiter' });

  constructor(options: RateLimiterOptions) {
    // Definir opções padrão
    this.options = {
      autoIncrement: true,
      maxEntries: 1000, // Limite padrão de 1000 entradas
      ...options,
    };

    this.requestCounts = new Map();

    // Limpar periodicamente os IPs expirados para evitar vazamento de memória
    if (typeof setInterval !== 'undefined') {
      this.cleanupInterval = setInterval(() => this.cleanup(), Math.min(options.windowMs, 60000)); // No máximo a cada minuto
    }
  }

  /**
   * Limpa recursos ao descartar o rate limiter
   */
  public dispose(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
    this.requestCounts.clear();
  }

  /**
   * Verifica se um IP excedeu o limite de requisições
   * Não incrementa o contador automaticamente
   */
  public async isRateLimited(ip: string): Promise<boolean> {
    return (await this.checkWithoutIncrement(ip)).limited;
  }

  /**
   * Incrementa o contador de requisições para um IP
   * @returns Informações sobre o estado atual do limite
   */
  public async incrementCounter(ip: string): Promise<RateLimiterResult> {
    const now = Date.now();
    const data = this.requestCounts.get(ip);

    // Se não há dados ou a janela de tempo expirou, começar nova contagem
    if (!data || now > data.resetAt) {
      const resetAt = now + this.options.windowMs;

      // Verificar se é necessário aplicar LRU antes de adicionar nova entrada
      if (!data && this.requestCounts.size >= (this.options.maxEntries || 1000)) {
        this.evictLeastRecentlyUsed();
      }

      this.requestCounts.set(ip, {
        count: 1,
        resetAt,
        lastAccessed: now,
      });

      return {
        limited: false,
        remaining: this.options.maxRequests - 1,
        resetAt,
        identifier: ip,
      };
    }

    // Atualizar timestamp de último acesso e incrementar contador
    data.count++;
    data.lastAccessed = now;
    this.requestCounts.set(ip, data);

    // Verificar se excedeu o limite após o incremento
    const limited = data.count >= this.options.maxRequests;

    return {
      limited,
      message: limited ? this.options.message : undefined,
      remaining: Math.max(0, this.options.maxRequests - data.count),
      resetAt: data.resetAt,
      identifier: ip,
    };
  }

  /**
   * Verifica o status de rate limiting para um IP
   * sem incrementar o contador automaticamente
   */
  private async checkWithoutIncrement(ip: string): Promise<RateLimiterResult> {
    const now = Date.now();
    const data = this.requestCounts.get(ip);

    // Se não há dados ou a janela de tempo expirou, permite a requisição
    if (!data || now > data.resetAt) {
      return {
        limited: false,
        message: undefined,
        remaining: this.options.maxRequests,
        resetAt: data ? data.resetAt : now + this.options.windowMs,
        identifier: ip,
      };
    }

    // Atualizar timestamp de último acesso
    data.lastAccessed = now;
    this.requestCounts.set(ip, data);

    // Verificar se excedeu o limite
    const limited = data.count >= this.options.maxRequests;

    return {
      limited,
      message: limited ? this.options.message : undefined,
      remaining: Math.max(0, this.options.maxRequests - data.count),
      resetAt: data.resetAt,
      identifier: ip,
    };
  }

  /**
   * Verifica o status de rate limiting para um IP
   * e incrementa o contador automaticamente se autoIncrement estiver ativado
   */
  public async check(ip: string): Promise<RateLimiterResult> {
    // Verificar sem incrementar
    const result = await this.checkWithoutIncrement(ip);

    // Se já está limitado, retornar sem incrementar
    if (result.limited) {
      return result;
    }

    // Se autoIncrement estiver ativado e não estiver limitado, incrementar
    if (this.options.autoIncrement) {
      return this.incrementCounter(ip);
    }

    return result;
  }

  /**
   * Verifica o status de rate limiting para uma requisição
   * usando o cabeçalho x-forwarded-for ou o endereço IP remoto
   */
  public async checkRequest(req: NextRequest): Promise<RateLimiterResult> {
    const ip = this.getIpFromRequest(req);
    return this.check(ip);
  }

  /**
   * Extrai o IP de uma requisição considerando diversos cabeçalhos
   */
  private getIpFromRequest(req: NextRequest): string {
    // Tenta obter o IP real por trás de proxies
    const forwarded = req.headers.get('x-forwarded-for');
    if (forwarded) {
      // Pega o primeiro IP da lista (cliente original)
      const ips = forwarded.split(',').map(ip => ip.trim());
      if (ips.length > 0 && ips[0]) return ips[0];
    }

    // Outras tentativas de cabeçalhos comuns
    const realIp = req.headers.get('x-real-ip');
    if (realIp) return realIp;

    // Usa um ID padrão se não encontrou nenhum IP
    return 'unknown-ip';
  }

  /**
   * Remove a entrada menos recentemente acessada (LRU)
   */
  private evictLeastRecentlyUsed(): void {
    let oldestKey: string | null = null;
    let oldestTime = Date.now();

    // Encontrar a entrada com o timestamp de acesso mais antigo
    for (const [key, entry] of this.requestCounts.entries()) {
      if (entry.lastAccessed < oldestTime) {
        oldestTime = entry.lastAccessed;
        oldestKey = key;
      }
    }

    // Remover a entrada mais antiga
    if (oldestKey) {
      this.requestCounts.delete(oldestKey);
    }
  }

  /**
   * Limpa os IPs expirados para evitar vazamento de memória
   */
  private cleanup(): void {
    const now = Date.now();
    // Converter para array para evitar problemas de iteração em diferentes ambientes
    Array.from(this.requestCounts.entries()).forEach(([ip, data]) => {
      if (now > data.resetAt) {
        this.requestCounts.delete(ip);
      }
    });

    // Aplicar LRU se o mapa ficar muito grande, mesmo após limpeza normal
    if (this.requestCounts.size > (this.options.maxEntries || 1000)) {
      const entriesToRemove = this.requestCounts.size - (this.options.maxEntries || 1000);
      for (let i = 0; i < entriesToRemove; i++) {
        this.evictLeastRecentlyUsed();
      }
    }
  }

  /**
   * Reseta o contador para um identificador específico
   */
  public resetCounter(identifier: string): void {
    this.requestCounts.delete(identifier);
  }

  /**
   * Reseta todos os contadores
   */
  public resetAllCounters(): void {
    this.requestCounts.clear();
  }

  /**
   * Retorna informações de diagnóstico sobre o estado atual do rate limiter
   */
  public getDiagnostics(): {
    totalEntries: number;
    maxEntries: number;
    activeEntries: number;
    expiredEntries: number;
  } {
    const now = Date.now();
    let expiredEntries = 0;

    for (const entry of this.requestCounts.values()) {
      if (now > entry.resetAt) {
        expiredEntries++;
      }
    }

    return {
      totalEntries: this.requestCounts.size,
      maxEntries: this.options.maxEntries || 1000,
      activeEntries: this.requestCounts.size - expiredEntries,
      expiredEntries,
    };
  }

  /**
   * Adquire um lock para o identificador específico para garantir operações thread-safe
   * @param identifier O identificador para qual o lock será adquirido
   * @returns Uma Promise que resolve quando o lock é adquirido
   */
  private async acquireLock(identifier: string): Promise<void> {
    // Se não há lock ativo para este identificador, adquire imediatamente
    if (!this.activeLocks.has(identifier)) {
      this.activeLocks.add(identifier);
      return;
    }

    // Caso contrário, espera na fila
    return new Promise<void>(resolve => {
      if (!this.lockQueue.has(identifier)) {
        this.lockQueue.set(identifier, []);
      }
      this.lockQueue.get(identifier)!.push(resolve);
    });
  }

  /**
   * Libera o lock para o identificador e processa a próxima operação na fila
   * @param identifier O identificador para qual o lock será liberado
   */
  private releaseLock(identifier: string): void {
    const queue = this.lockQueue.get(identifier);

    if (queue && queue.length > 0) {
      // Se há operações na fila, processa a próxima
      const nextResolve = queue.shift()!;
      setTimeout(() => nextResolve(), 0);
    } else {
      // Caso contrário, remove o lock
      this.activeLocks.delete(identifier);
      if (queue && queue.length === 0) {
        this.lockQueue.delete(identifier);
      }
    }
  }
}
