# 🔐 Configuração OAuth para Excel Copilot

## 📋 Pré-requisitos

Para habilitar login com Google e GitHub, você precisa configurar as credenciais OAuth.

## 🔧 1. Configurar Google OAuth

### 1.1. Acesse o Google Cloud Console

1. Vá para: https://console.cloud.google.com/
2. Crie um novo projeto ou selecione um existente
3. Ative a API "Google+ API" ou "Google Identity"

### 1.2. <PERSON><PERSON>r <PERSON>enciais OAuth

1. Vá para **APIs & Services > Credentials**
2. Clique em **+ CREATE CREDENTIALS > OAuth 2.0 Client IDs**
3. Escolha **Web application**
4. Configure:
   - **Name**: Excel Copilot
   - **Authorized JavaScript origins**:
     - `https://excel-copilot-eight.vercel.app`
   - **Authorized redirect URIs**:
     - `https://excel-copilot-eight.vercel.app/api/auth/callback/google`

### 1.3. <PERSON><PERSON><PERSON>

- **Client ID**: Copie o ID gerado pelo Google
- **Client Secret**: Copie o Secret gerado pelo Google

## 🔧 2. Configurar GitHub OAuth

### 2.1. Acesse GitHub Settings

1. Vá para: https://github.com/settings/developers
2. Clique em **New OAuth App**

### 2.2. Configurar Aplicação

- **Application name**: Excel Copilot
- **Homepage URL**: `https://excel-copilot-eight.vercel.app`
- **Authorization callback URL**: `https://excel-copilot-eight.vercel.app/api/auth/callback/github`

### 2.3. Copiar Credenciais

- **Client ID**: Copie o ID gerado pelo GitHub
- **Client Secret**: Clique em "Generate a new client secret" e copie

## 🚀 3. Configurar na Vercel

### 3.1. Acesse o Dashboard da Vercel

1. Vá para: https://vercel.com/dashboard
2. Selecione o projeto **excel-copilot**
3. Vá para **Settings > Environment Variables**

### 3.2. Adicionar Variáveis

Adicione as seguintes variáveis de ambiente:

```
GOOGLE_CLIENT_ID=seu_google_client_id_aqui
GOOGLE_CLIENT_SECRET=seu_google_client_secret_aqui
GITHUB_CLIENT_ID=seu_github_client_id_aqui
GITHUB_CLIENT_SECRET=seu_github_client_secret_aqui
NEXTAUTH_SECRET=uma_string_aleatoria_muito_segura
NEXTAUTH_URL=https://excel-copilot-eight.vercel.app
```

### 3.3. Gerar NEXTAUTH_SECRET

Execute no terminal:

```bash
openssl rand -base64 32
```

## ✅ 4. Fazer Redeploy

Após configurar as variáveis, faça um novo deploy:

```bash
vercel --prod
```

## 🔍 5. Testar

1. Acesse: https://excel-copilot-eight.vercel.app
2. Clique em "Entrar"
3. Teste login com Google e GitHub

## ⚠️ Problemas Comuns

- **Erro de redirect**: Verifique se as URLs de callback estão corretas
- **Credenciais inválidas**: Confirme se copiou corretamente
- **Cache**: Limpe o cache do navegador após mudanças
