#!/usr/bin/env node

/**
 * Script para testar a integração de IA no Excel Copilot
 *
 * Este script:
 * 1. Faz uma requisição para o endpoint de chat
 * 2. Verifica a resposta em streaming
 * 3. Testa o formato da resposta
 */

const fetch = require('node-fetch');
const { PassThrough } = require('stream');
const { TextDecoder } = require('util');

// Cores para console
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

console.log(`${colors.blue}=== Iniciando teste de integração da IA ===${colors.reset}\n`);

// Testar com diferentes mensagens
const messages = [
  'Olá, como você pode me ajudar?',
  'Calcule a média dos valores na coluna B',
  'Crie um gráfico com os dados das colunas A e B',
];

// Função para testar o streaming de respostas
async function testChat(userMessage) {
  try {
    console.log(`${colors.yellow}Testando mensagem:${colors.reset} "${userMessage}"`);

    // Criar payload
    const payload = {
      messages: [{ role: 'user', content: userMessage }],
    };

    // Fazer requisição
    const response = await fetch('http://localhost:3000/api/chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error(`${colors.red}Erro na requisição:${colors.reset}`, errorData);
      return false;
    }

    // Verificar se a resposta é streamada
    const reader = response.body.getReader();

    console.log(`${colors.cyan}Resposta:${colors.reset}`);

    let completeResponse = '';
    let decoder = new TextDecoder();

    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      // Decodificar chunk
      const chunk = decoder.decode(value);
      process.stdout.write(chunk);
      completeResponse += chunk;
    }

    console.log(`\n\n${colors.green}Teste concluído com sucesso!${colors.reset}\n`);

    // Analisar resposta para verificar formato de operações Excel, se presente
    if (completeResponse.includes('"operations"')) {
      console.log(`${colors.magenta}Detectadas operações Excel na resposta!${colors.reset}`);

      try {
        // Tentar extrair o objeto JSON de operações
        const matches = completeResponse.match(/\{[\s\S]*"operations"[\s\S]*\}/);
        if (matches) {
          const operations = JSON.parse(matches[0]);
          console.log(
            `${colors.cyan}Número de operações:${colors.reset} ${operations.operations?.length || 0}`
          );
          console.log(
            `${colors.cyan}Tipos de operações:${colors.reset} ${operations.operations?.map(op => op.type).join(', ')}`
          );
        }
      } catch (e) {
        console.log(
          `${colors.yellow}Formato JSON de operações não extraível:${colors.reset}`,
          e.message
        );
      }
    }

    return true;
  } catch (error) {
    console.error(`${colors.red}Erro ao testar chat:${colors.reset}`, error);
    return false;
  }
}

// Executar testes em sequência
async function runAllTests() {
  console.log(`${colors.blue}Executando ${messages.length} testes de chat...${colors.reset}\n`);

  let success = 0;
  let failed = 0;

  for (const message of messages) {
    const result = await testChat(message);
    if (result) {
      success++;
    } else {
      failed++;
    }
    console.log(`${colors.blue}---------------------------------------${colors.reset}\n`);
  }

  console.log(`${colors.blue}=== Resumo dos testes ===${colors.reset}`);
  console.log(`${colors.green}Sucesso: ${success}${colors.reset}`);
  console.log(`${colors.red}Falhas: ${failed}${colors.reset}`);

  if (failed === 0) {
    console.log(
      `\n${colors.green}Todos os testes de integração da IA foram bem-sucedidos!${colors.reset}`
    );
  } else {
    console.log(
      `\n${colors.red}Alguns testes falharam. Verifique os logs para mais detalhes.${colors.reset}`
    );
  }
}

runAllTests();
