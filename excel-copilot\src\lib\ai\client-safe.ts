/**
 * <PERSON><PERSON><PERSON><PERSON> que centraliza as exportações relacionadas à IA que são seguras para o cliente
 * CORREÇÃO: Não importa módulos que causam problemas no webpack
 */

// Importar o ExcelAIProcessor como classe para reutilização
import { ExcelAIProcessor } from './ExcelAIProcessor';

// Definir tipos localmente para evitar importações problemáticas
export enum GeminiErrorType {
  NETWORK_ERROR = 'NETWORK_ERROR',
  API_ERROR = 'API_ERROR',
  RATE_LIMIT = 'RATE_LIMIT',
  INVALID_REQUEST = 'INVALID_REQUEST',
  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR',
  UNKNOWN = 'UNKNOWN',
  API_UNAVAILABLE = 'API_UNAVAILABLE',
}

// Exportar o processador de comandos de IA para Excel
export { ExcelAIProcessor } from './ExcelAIProcessor';

// Factory para criar uma instância de processador de IA para Excel
export function createExcelAIProcessor(isTestMode: boolean = false) {
  // Criar uma nova instância do processador
  return new ExcelAIProcessor({}, isTestMode);
}

// Prompts e constantes
export { EXCEL_SYSTEM_PROMPT as DEFAULT_EXCEL_SYSTEM_PROMPT } from './prompts';

// Tipos
export type { AICommandParserOptions } from './types';

// Função para carregar geminiService apenas no servidor (via API)
export async function getGeminiServiceAPI() {
  if (typeof window !== 'undefined') {
    // No cliente, usar API endpoints
    return {
      async sendMessage(message: string, options: any = {}) {
        const response = await fetch('/api/ai/chat', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            message,
            ...options,
          }),
        });

        if (!response.ok) {
          throw new Error(`API Error: ${response.statusText}`);
        }

        const result = await response.json();
        return result.response;
      },
    };
  }

  // No servidor, usar API endpoint também para evitar problemas de build
  return {
    async sendMessage(message: string, options: any = {}) {
      // No servidor, podemos usar fetch para localhost
      const response = await fetch('http://localhost:3000/api/ai/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message,
          ...options,
        }),
      });

      if (!response.ok) {
        throw new Error(`API Error: ${response.statusText}`);
      }

      const result = await response.json();
      return result.response;
    },
  };
}
