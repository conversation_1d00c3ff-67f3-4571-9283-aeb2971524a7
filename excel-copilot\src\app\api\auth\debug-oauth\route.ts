import { NextResponse } from 'next/server';

import { ENV } from '@/config/unified-environment';

export const dynamic = 'force-dynamic';

export async function GET() {
  try {
    // Verificar todas as configurações OAuth
    const googleClientId = process.env.AUTH_GOOGLE_CLIENT_ID;
    const googleClientSecret = process.env.AUTH_GOOGLE_CLIENT_SECRET;
    const githubClientId = process.env.AUTH_GITHUB_CLIENT_ID;
    const githubClientSecret = process.env.AUTH_GITHUB_CLIENT_SECRET;
    const nextAuthUrl = process.env.AUTH_NEXTAUTH_URL;
    const nextAuthSecret = process.env.AUTH_NEXTAUTH_SECRET;
    const nodeEnv = process.env.NODE_ENV;
    const skipAuthProviders = process.env.AUTH_SKIP_PROVIDERS;

    // URLs de callback
    const googleCallbackUrl = `${nextAuthUrl}/api/auth/callback/google`;
    const githubCallbackUrl = `${nextAuthUrl}/api/auth/callback/github`;

    // Verificar configurações do ENV
    const envConfig = {
      IS_PRODUCTION: ENV.IS_PRODUCTION,
      IS_DEVELOPMENT: ENV.IS_DEVELOPMENT,
      SKIP_AUTH_PROVIDERS: ENV.FEATURES.SKIP_AUTH_PROVIDERS,
      NODE_ENV: ENV.NODE_ENV,
    };

    // Status das variáveis
    const envStatus = {
      GOOGLE_CLIENT_ID: googleClientId ? '✅ Configurado' : '❌ Ausente',
      GOOGLE_CLIENT_SECRET: googleClientSecret ? '✅ Configurado' : '❌ Ausente',
      GITHUB_CLIENT_ID: githubClientId ? '✅ Configurado' : '❌ Ausente',
      GITHUB_CLIENT_SECRET: githubClientSecret ? '✅ Configurado' : '❌ Ausente',
      NEXTAUTH_URL: nextAuthUrl ? '✅ Configurado' : '❌ Ausente',
      NEXTAUTH_SECRET: nextAuthSecret ? '✅ Configurado' : '❌ Ausente',
      NODE_ENV: nodeEnv || 'undefined',
      SKIP_AUTH_PROVIDERS: skipAuthProviders || 'undefined',
    };

    // Verificar se todas as variáveis críticas estão presentes
    const missingVars = [];
    if (!googleClientId) missingVars.push('AUTH_GOOGLE_CLIENT_ID');
    if (!googleClientSecret) missingVars.push('AUTH_GOOGLE_CLIENT_SECRET');
    if (!githubClientId) missingVars.push('AUTH_GITHUB_CLIENT_ID');
    if (!githubClientSecret) missingVars.push('AUTH_GITHUB_CLIENT_SECRET');
    if (!nextAuthUrl) missingVars.push('AUTH_NEXTAUTH_URL');
    if (!nextAuthSecret) missingVars.push('AUTH_NEXTAUTH_SECRET');

    const isConfigurationValid = missingVars.length === 0;

    return NextResponse.json({
      status: isConfigurationValid ? 'success' : 'error',
      message: isConfigurationValid
        ? 'Configuração OAuth completa'
        : 'Configuração OAuth incompleta',
      environment: envConfig,
      variables: envStatus,
      missingVariables: missingVars,
      callbackUrls: {
        google: googleCallbackUrl,
        github: githubCallbackUrl,
      },
      recommendations:
        missingVars.length > 0
          ? [
              'Verifique se todas as variáveis de ambiente estão configuradas na Vercel',
              'Confirme se os valores não contêm espaços ou caracteres especiais',
              'Verifique se o NEXTAUTH_URL corresponde ao domínio de produção',
              'Confirme se as URLs de callback estão configuradas corretamente no Google/GitHub',
            ]
          : [
              'Configuração OAuth está completa',
              'Verifique se as URLs de callback estão corretas nos provedores OAuth',
            ],
    });
  } catch (error) {
    return NextResponse.json(
      {
        status: 'error',
        message: 'Erro ao verificar configuração OAuth',
        error: error instanceof Error ? error.message : 'Erro desconhecido',
      },
      { status: 500 }
    );
  }
}
