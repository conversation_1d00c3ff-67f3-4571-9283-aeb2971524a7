'use client';

import {
  FileSpreadsheet,
  FileText,
  BarChart4,
  Check,
  Calculator,
  Table,
  PiggyBank,
  Download,
} from 'lucide-react';
import React, { useState, useEffect } from 'react';
import { toast } from 'sonner';

import { useCSRF } from '@/components/providers/csrf-provider';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';

// Interface que define a estrutura de um template
interface WorkbookTemplate {
  id: string;
  title: string;
  description: string;
  category: string;
  thumbnailUrl?: string;
  popularity: number;
  sheets: number;
  isNew?: boolean;
  isFeatured?: boolean;
}

// Definir tipo para as props do componente
interface WorkbookTemplatesProps {
  onTemplateSelected?: (templateId: string) => void;
}

// Mapear ícones para componentes
const _iconMap: Record<string, React.ReactNode> = {
  'file-spreadsheet': <FileSpreadsheet className="h-8 w-8" />,
  'file-text': <FileText className="h-8 w-8" />,
  calculator: <Calculator className="h-8 w-8" />,
  table: <Table className="h-8 w-8" />,
  'bar-chart': <BarChart4 className="h-8 w-8" />,
  'piggy-bank': <PiggyBank className="h-8 w-8" />,
};

export function WorkbookTemplates({ onTemplateSelected }: WorkbookTemplatesProps) {
  // Usando hooks apenas no lado do cliente
  const [templates, setTemplates] = useState<WorkbookTemplate[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null);
  const [isCreating, setIsCreating] = useState(false);
  const { csrfToken } = useCSRF();

  // Carregar templates da API
  useEffect(() => {
    const fetchTemplates = async () => {
      try {
        setIsLoading(true);
        const response = await fetch('/api/templates', {
          headers: {
            'Content-Type': 'application/json',
            ...(csrfToken ? { 'x-csrf-token': csrfToken } : {}),
          },
        });

        if (!response.ok) {
          throw new Error('Erro ao carregar templates');
        }

        const data = await response.json();
        setTemplates(data.templates);
      } catch (error) {
        console.error('Erro ao buscar templates:', error);
        // Usar templates de fallback em caso de erro
        setTemplates(FALLBACK_TEMPLATES);
        toast.error('Não foi possível carregar templates');
      } finally {
        setIsLoading(false);
      }
    };

    fetchTemplates();
  }, [csrfToken]);

  // Selecionar um template
  const handleSelectTemplate = (templateId: string) => {
    setSelectedTemplate(templateId);
  };

  // Usar o template selecionado para criar uma planilha
  const handleUseTemplate = async () => {
    if (!selectedTemplate) return;

    try {
      setIsCreating(true);

      // Chamar a API para criar planilha a partir do template
      const response = await fetch('/api/workbooks/from-template', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(csrfToken ? { 'x-csrf-token': csrfToken } : {}),
        },
        body: JSON.stringify({
          templateId: selectedTemplate,
        }),
      });

      if (!response.ok) {
        throw new Error('Erro ao criar planilha a partir do template');
      }

      const data = await response.json();

      // Notificar componente pai
      if (onTemplateSelected && typeof onTemplateSelected === 'function') {
        onTemplateSelected(selectedTemplate);
      }

      // Redirecionar para a nova planilha
      if (data?.workbook?.id) {
        // Usar window.location para navegação no cliente
        window.location.href = `/workbook/${data.workbook.id}`;
      }
    } catch (error) {
      console.error('Erro ao usar template:', error);
      toast.error('Não foi possível criar planilha a partir do template');
      setIsCreating(false);
    }
  };

  // Mostrar esqueleto de carregamento
  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {Array(6)
          .fill(0)
          .map((_, index) => (
            <Card key={index} className="overflow-hidden">
              <CardHeader className="pb-2">
                <Skeleton className="h-4 w-3/4" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-3 w-full mb-2" />
                <Skeleton className="h-3 w-2/3" />
              </CardContent>
              <CardFooter>
                <Skeleton className="h-8 w-full" />
              </CardFooter>
            </Card>
          ))}
      </div>
    );
  }

  return (
    <div className="space-y-6" id="templates-section">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold">Templates Prontos</h2>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {templates.map(template => (
          <Card
            key={template.id}
            className={`overflow-hidden cursor-pointer transition-all hover:shadow-md ${selectedTemplate === template.id ? 'ring-2 ring-primary' : ''}`}
            onClick={() => handleSelectTemplate(template.id)}
          >
            <CardHeader className="pb-2">
              <div className="flex justify-between items-start">
                <CardTitle className="text-base flex items-center gap-2">
                  <FileSpreadsheet className="h-4 w-4 text-primary" />
                  {template.title}
                </CardTitle>

                <div className="flex gap-1">
                  {template.isNew && (
                    <Badge variant="secondary" className="text-xs font-normal">
                      Novo
                    </Badge>
                  )}
                  {template.isFeatured && (
                    <Badge
                      variant="default"
                      className="text-xs font-normal bg-gradient-to-r from-blue-600 to-indigo-600"
                    >
                      Destaque
                    </Badge>
                  )}
                </div>
              </div>
            </CardHeader>

            <CardContent>
              <p className="text-sm text-muted-foreground line-clamp-2 mb-2">
                {template.description}
              </p>
              <div className="flex items-center text-xs text-muted-foreground">
                <Badge variant="outline" className="mr-2 font-normal">
                  {template.sheets} {template.sheets > 1 ? 'folhas' : 'folha'}
                </Badge>
                <Badge variant="outline" className="font-normal capitalize">
                  {template.category}
                </Badge>
              </div>
            </CardContent>

            <CardFooter>
              <Button
                variant="outline"
                className="w-full justify-center gap-2"
                onClick={e => {
                  e.stopPropagation();
                  if (selectedTemplate === template.id) {
                    handleUseTemplate();
                  } else {
                    handleSelectTemplate(template.id);
                  }
                }}
                disabled={isCreating && selectedTemplate === template.id}
              >
                {isCreating && selectedTemplate === template.id ? (
                  <>
                    <div className="h-4 w-4 rounded-full border-2 border-primary/20 border-t-primary animate-spin"></div>
                    Criando...
                  </>
                ) : selectedTemplate === template.id ? (
                  <>
                    <Check className="h-4 w-4" />
                    Usar Template
                  </>
                ) : (
                  <>
                    <Download className="h-4 w-4" />
                    Selecionar
                  </>
                )}
              </Button>
            </CardFooter>
          </Card>
        ))}
      </div>
    </div>
  );
}

// Templates de fallback para quando a API falha
const FALLBACK_TEMPLATES: WorkbookTemplate[] = [
  {
    id: 'financial-basic',
    title: 'Controle Financeiro',
    description:
      'Modelo básico para controle de finanças pessoais com categorias de gastos e receitas',
    category: 'finanças',
    popularity: 95,
    sheets: 3,
    isFeatured: true,
  },
  {
    id: 'project-tracker',
    title: 'Gerenciador de Projetos',
    description: 'Acompanhe prazos, responsáveis e status de projetos em um só lugar',
    category: 'produtividade',
    popularity: 87,
    sheets: 2,
  },
  {
    id: 'sales-dashboard',
    title: 'Dashboard de Vendas',
    description: 'Visualize dados de vendas com gráficos e indicadores de performance',
    category: 'negócios',
    popularity: 92,
    sheets: 4,
    isNew: true,
  },
  {
    id: 'invoice-generator',
    title: 'Gerador de Faturas',
    description: 'Crie e organize faturas para seus clientes com cálculos automáticos',
    category: 'negócios',
    popularity: 78,
    sheets: 2,
  },
  {
    id: 'inventory-control',
    title: 'Controle de Estoque',
    description:
      'Gerencie seu inventário com alertas de estoque baixo e histórico de movimentações',
    category: 'negócios',
    popularity: 82,
    sheets: 3,
  },
  {
    id: 'workout-tracker',
    title: 'Acompanhamento Fitness',
    description: 'Registre treinos, medidas corporais e evolução de exercícios',
    category: 'saúde',
    popularity: 75,
    sheets: 4,
    isNew: true,
  },
];

// Versão do componente que pode ser usada do lado do servidor
export function WorkbookTemplatesServer() {
  return (
    <div className="space-y-6" id="templates-section">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold">Templates Prontos</h2>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {Array(6)
          .fill(0)
          .map((_, index) => (
            <Card key={index} className="overflow-hidden">
              <CardHeader className="pb-2">
                <Skeleton className="h-4 w-3/4" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-3 w-full mb-2" />
                <Skeleton className="h-3 w-2/3" />
              </CardContent>
              <CardFooter>
                <Skeleton className="h-8 w-full" />
              </CardFooter>
            </Card>
          ))}
      </div>
    </div>
  );
}
