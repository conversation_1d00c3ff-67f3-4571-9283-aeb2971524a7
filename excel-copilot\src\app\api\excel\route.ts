import ExcelJS from 'exceljs';
import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';

import { logger } from '../../../lib/logger';
import { sanitizeExcelData } from '../../../lib/security/sanitization-excel';
import { prisma } from '../../../server/db/client';

export const dynamic = 'force-dynamic';

export async function POST(req: NextRequest) {
  try {
    // Verificar autenticação
    const session = await getServerSession();
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Não autorizado. Faça login para continuar.' },
        { status: 401 }
      );
    }

    // Obter o arquivo da requisição
    const formData = await req.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json({ error: 'Nenhum arquivo enviado.' }, { status: 400 });
    }

    // Verificar tamanho do arquivo (limitar a 5MB)
    const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
    if (file.size > MAX_FILE_SIZE) {
      return NextResponse.json(
        { error: 'Arquivo muito grande. O tamanho máximo permitido é 5MB.' },
        { status: 400 }
      );
    }

    // Verificar tipo de arquivo
    const isExcel =
      file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
      file.type === 'application/vnd.ms-excel' ||
      file.name.endsWith('.xlsx') ||
      file.name.endsWith('.xls');

    const isCSV = file.type === 'text/csv' || file.name.endsWith('.csv');

    if (!isExcel && !isCSV) {
      return NextResponse.json(
        {
          error:
            'Formato de arquivo inválido. Por favor, envie um arquivo Excel (.xlsx, .xls) ou CSV (.csv).',
        },
        { status: 400 }
      );
    }

    // Verificar segurança do nome do arquivo
    const fileName = file.name.replace(/[^\w\s.-]/gi, '');
    if (fileName !== file.name) {
      logger.warn(`Nome de arquivo potencialmente inseguro detectado: ${file.name}`);
    }

    // Ler o arquivo
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    // Processar com ExcelJS
    const workbook = new ExcelJS.Workbook();

    try {
      if (isCSV) {
        // Para CSV, converter para string e usar o método parseCSV
        const csvContent = buffer.toString('utf-8');
        const worksheet = workbook.addWorksheet('Sheet1');

        // Processar CSV manualmente
        const rows = csvContent.split('\n').map(line => line.split(','));
        if (rows.length > 0) {
          worksheet.addRows(rows);
        }
      } else {
        // Para XLSX, criar buffer compatível com ExcelJS
        await workbook.xlsx.load(arrayBuffer);
      }
    } catch (error) {
      logger.error('Erro ao processar arquivo:', error);
      return NextResponse.json(
        { error: 'Erro ao processar o arquivo. Formato inválido ou arquivo corrompido.' },
        { status: 400 }
      );
    }

    // Extrair dados das planilhas
    interface SheetData {
      id: number;
      name: string;
      rows: RowData[];
    }

    interface RowData {
      cells: CellData[];
    }

    interface CellData {
      columnName: string;
      value: unknown;
      formula: string | null;
      type: ExcelJS.ValueType;
    }

    interface WorkbookData {
      sheets: SheetData[];
    }

    const workbookData: WorkbookData = {
      sheets: [],
    };

    workbook.eachSheet((worksheet, sheetId) => {
      const sheetData: SheetData = {
        id: sheetId,
        name: worksheet.name,
        rows: [],
      };

      // Extrair cabeçalhos e dados
      const headers: string[] = [];

      worksheet.eachRow({ includeEmpty: false }, (row, rowNumber) => {
        const rowData: RowData = { cells: [] };

        row.eachCell({ includeEmpty: true }, (cell, colNumber) => {
          // Salvar cabeçalhos da primeira linha
          if (rowNumber === 1) {
            headers.push(cell.value?.toString() || `Coluna ${colNumber}`);
          }

          rowData.cells.push({
            columnName: headers[colNumber - 1] || `Coluna ${colNumber}`,
            value: cell.value,
            formula: cell.formula || null,
            type: cell.type,
          });
        });

        sheetData.rows.push(rowData);
      });

      workbookData.sheets.push(sheetData);
    });

    // Sanitizar os dados antes de salvar no banco de dados
    interface SanitizedWorkbookData {
      sheets: SanitizedSheetData[];
    }

    interface SanitizedSheetData {
      name: string;
      [key: string]: unknown;
    }

    const sanitizedWorkbookData: SanitizedWorkbookData = {
      sheets: [],
    };

    // Registrar relatórios de segurança
    let totalDangerousFormulas = 0;
    const securityReports = [];

    // Sanitizar cada planilha
    for (const sheet of workbookData.sheets) {
      const { sanitizedData, securityReport } = sanitizeExcelData(sheet);

      // Adicionar a planilha sanitizada
      sanitizedWorkbookData.sheets.push(sanitizedData as SanitizedSheetData);

      // Registrar problemas de segurança
      if (securityReport.hasDangerousFormulas) {
        totalDangerousFormulas += securityReport.formulasRejected;
        securityReports.push({
          sheetName: (sanitizedData as SanitizedSheetData).name,
          ...securityReport,
        });
      }
    }

    // Registrar alertas de segurança se encontradas fórmulas perigosas
    if (totalDangerousFormulas > 0) {
      logger.warn(
        `[SECURITY_ALERT] Detectadas ${totalDangerousFormulas} fórmulas potencialmente maliciosas em '${file.name}'`,
        {
          userId: (session.user as { id?: string })?.id,
          fileName: file.name,
          securityReports,
        }
      );
    }

    // Salvar no banco de dados
    const workbookRecord = await prisma.workbook.create({
      data: {
        name: file.name,
        userId: (session.user as { id?: string })?.id || '',
        sheets: {
          create: sanitizedWorkbookData.sheets.map(sheet => ({
            name: sheet.name,
            data: JSON.stringify(sheet), // Armazena como string JSON
          })),
        },
      },
      include: {
        sheets: true,
      },
    });

    // Montar resposta com avisos de segurança se aplicável
    interface ResponseData {
      success: boolean;
      workbook: {
        id: string;
        name: string;
        sheetCount: number;
      };
      warnings?: {
        security?: {
          formulasRemoved: number;
          message: string;
        };
      };
    }

    const response: ResponseData = {
      success: true,
      workbook: {
        id: workbookRecord.id,
        name: workbookRecord.name,
        sheetCount: workbookRecord.sheets.length,
      },
    };

    // Adicionar informações sobre fórmulas removidas se houver
    if (totalDangerousFormulas > 0) {
      response.warnings = {
        security: {
          formulasRemoved: totalDangerousFormulas,
          message: `${totalDangerousFormulas} fórmula(s) potencialmente perigosa(s) foram removidas por motivos de segurança.`,
        },
      };
    }

    return NextResponse.json(response);
  } catch (error) {
    logger.error('[EXCEL_PROCESSING_ERROR]', error);
    return NextResponse.json(
      {
        error: 'Erro ao processar o arquivo. Por favor, tente novamente.',
        details: process.env.NODE_ENV === 'development' ? String(error) : undefined,
      },
      { status: 500 }
    );
  }
}
