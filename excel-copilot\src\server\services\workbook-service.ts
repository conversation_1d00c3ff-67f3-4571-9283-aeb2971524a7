// Imports organizados corretamente
import { logger } from '@/lib/logger';
// import { queueManager } from '@/lib/queue/queue-manager'; // Removido - não compatível com Vercel
// import { notificationService } from '@/lib/queue/notification-service'; // Removido - não compatível com Vercel

// Mocks para compatibilidade com Vercel serverless
const queueManager = {
  addJob: () => Promise.resolve({ id: 'mock-job' }),
  getJobStatus: () => Promise.resolve({ status: 'completed' })
};

const notificationService = {
  sendNotification: () => Promise.resolve(),
  scheduleNotification: () => Promise.resolve()
};
import { CreateWorkbookInput, UpdateWorkbookInput, WorkbookFilter } from '@/schemas/workbook';
import { prisma } from '@/server/db/client';
import { cachifyQuery } from '@/server/db/query-cache';

// Tipos para planos e limites
type PlanType = 'free' | 'pro_monthly' | 'pro_annual';

// Interfaces para melhorar tipagem dos limites de plano
interface PlanLimits {
  free: number;
  pro_monthly: number;
  pro_annual: number;
}

interface SubscriptionLimits {
  MAX_WORKBOOKS: PlanLimits;
  MAX_CELLS: PlanLimits;
}

// Definição das constantes que seriam importadas
const PLAN_LIMITS: SubscriptionLimits = {
  MAX_WORKBOOKS: {
    free: 5,
    pro_monthly: Infinity,
    pro_annual: Infinity,
  },
  MAX_CELLS: {
    free: 1000,
    pro_monthly: 50000,
    pro_annual: Infinity,
  },
};

// Interface para melhorar a tipagem dos dados da planilha
interface Cell {
  value: unknown;
  [key: string]: unknown;
}

interface Row {
  cells: Cell[];
  [key: string]: unknown;
}

interface SheetData {
  rows?: Row[];
  cells?: Record<string, unknown>;
  [key: string]: unknown;
}

// Função melhor tipada para contar células
function countCellsInSheet(sheetData: unknown): number {
  if (!sheetData) return 0;

  const typedData = sheetData as SheetData;
  let cellCount = 0;

  // Verificação simplificada para contagem de células
  if (typedData.rows && Array.isArray(typedData.rows)) {
    typedData.rows.forEach(row => {
      if (row && row.cells && Array.isArray(row.cells)) {
        cellCount += row.cells.filter(
          cell => cell && cell.value !== null && cell.value !== undefined
        ).length;
      }
    });
  } else if (typedData.cells && typeof typedData.cells === 'object') {
    cellCount = Object.keys(typedData.cells).length;
  }

  return cellCount;
}

/**
 * Serviço para operações relacionadas a workbooks
 * Centraliza lógica de negócios para ser compartilhada entre API Routes e tRPC
 */
export const WorkbookService = {
  /**
   * Obtém todas as planilhas de um usuário
   */
  async getUserWorkbooks(userId: string, filter?: Partial<WorkbookFilter>) {
    try {
      const limit = filter?.limit || 20;
      const page = filter?.page || 0;

      // Buscar workbooks e contagem total em paralelo
      const [workbooks, totalItems] = await Promise.all([
        cachifyQuery(
          () =>
            prisma.workbook.findMany({
              where: {
                userId,
                ...(filter?.isPublic !== undefined && { isPublic: filter.isPublic }),
                ...(filter?.search && {
                  OR: [
                    { name: { contains: filter.search, mode: 'insensitive' } },
                    { description: { contains: filter.search, mode: 'insensitive' } },
                  ],
                }),
              },
              include: {
                sheets: {
                  select: {
                    id: true,
                    name: true,
                    updatedAt: true,
                  },
                },
              },
              orderBy: { updatedAt: 'desc' },
              take: limit,
              skip: page * limit,
            }),
          ['user-workbooks', userId, filter],
          { ttl: 60 } // 1 minuto de cache
        ),
        cachifyQuery(
          () =>
            prisma.workbook.count({
              where: {
                userId,
                ...(filter?.isPublic !== undefined && { isPublic: filter.isPublic }),
                ...(filter?.search && {
                  OR: [
                    { name: { contains: filter.search, mode: 'insensitive' } },
                    { description: { contains: filter.search, mode: 'insensitive' } },
                  ],
                }),
              },
            }),
          ['user-workbooks-count', userId, filter],
          { ttl: 60 }
        ),
      ]);

      // Calcular informações de paginação
      const totalPages = Math.ceil(totalItems / limit);
      const hasMore = page < totalPages - 1;

      return {
        workbooks,
        pagination: {
          page,
          limit,
          totalItems,
          totalPages,
          hasMore,
        },
      };
    } catch (error) {
      logger.error('Erro ao buscar workbooks do usuário', {
        userId,
        error,
      });
      throw new Error('Falha ao buscar workbooks');
    }
  },

  /**
   * Obtém workbooks públicos
   */
  async getPublicWorkbooks(filter?: Partial<WorkbookFilter>) {
    try {
      return await cachifyQuery(
        () =>
          prisma.workbook.findMany({
            where: {
              isPublic: true,
              ...(filter?.search && {
                OR: [
                  { name: { contains: filter.search, mode: 'insensitive' } },
                  { description: { contains: filter.search, mode: 'insensitive' } },
                ],
              }),
            },
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  image: true,
                },
              },
              sheets: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
            orderBy: { updatedAt: 'desc' },
            take: filter?.limit || 20,
            skip: filter?.page ? filter.page * (filter.limit || 20) : 0,
          }),
        ['public-workbooks', filter],
        { ttl: 300 } // 5 minutos de cache para workbooks públicos
      );
    } catch (error) {
      logger.error('Erro ao buscar workbooks públicos', { error });
      throw new Error('Falha ao buscar workbooks públicos');
    }
  },

  /**
   * Obtém um workbook específico
   */
  async getWorkbookById(id: string, userId?: string) {
    try {
      const workbook = await prisma.workbook.findUnique({
        where: { id },
        include: {
          sheets: true,
          user: {
            select: {
              id: true,
              name: true,
              image: true,
            },
          },
        },
      });

      if (!workbook) {
        throw new Error('Workbook não encontrado');
      }

      // Verificar permissão - usuário deve ser dono ou workbook deve ser público
      if (userId && workbook.userId !== userId && !workbook.isPublic) {
        // Registrar tentativa não autorizada para auditoria
        await prisma.securityLog.create({
          data: {
            userId,
            eventType: 'unauthorized_workbook_access',
            details: JSON.stringify({ workbookId: id, ownerId: workbook.userId }),
            timestamp: new Date(),
          },
        });

        throw new Error('Usuário não autorizado a acessar este workbook');
      }

      return workbook;
    } catch (error) {
      logger.error('Erro ao buscar workbook por ID', {
        workbookId: id,
        userId,
        error,
      });
      throw error;
    }
  },

  /**
   * Cria um novo workbook
   */
  async createWorkbook(data: CreateWorkbookInput, userId: string) {
    try {
      // Criar workbook com verificação de limites dentro da transação
      const workbook = await prisma.$transaction(async tx => {
        // Verificar limite de workbooks do plano dentro da transação
        const userSubscription = await tx.subscription.findFirst({
          where: {
            userId,
            OR: [{ status: 'active' }, { status: 'trialing' }],
          },
          select: { plan: true, apiCallsLimit: true },
        });

        // Contar workbooks existentes
        const currentCount = await tx.workbook.count({ where: { userId } });

        // Usar constantes definidas localmente para evitar erros de tipo
        const PLANS = {
          FREE: 'free' as PlanType,
          PRO_MONTHLY: 'pro_monthly' as PlanType,
          PRO_ANNUAL: 'pro_annual' as PlanType,
        };

        const plan = (userSubscription?.plan as PlanType) || PLANS.FREE;
        const workbookLimit =
          PLAN_LIMITS.MAX_WORKBOOKS[plan] || PLAN_LIMITS.MAX_WORKBOOKS[PLANS.FREE];

        // Verificar se o usuário pode criar mais workbooks
        if (currentCount >= workbookLimit) {
          throw new Error(
            `Limite de planilhas excedido. Você tem ${currentCount} de ${workbookLimit} planilhas permitidas para seu plano.`
          );
        }

        // Verificar número de células nos dados iniciais, se fornecidos
        if (data.initialData) {
          const initialCellCount = countCellsInSheet(data.initialData as Record<string, unknown>);
          const cellLimit = PLAN_LIMITS.MAX_CELLS[plan] || PLAN_LIMITS.MAX_CELLS[PLANS.FREE];

          if (initialCellCount > cellLimit) {
            throw new Error(
              `Os dados iniciais contêm ${initialCellCount} células, excedendo o limite de ${cellLimit} do seu plano.`
            );
          }
        }

        // Criar o workbook após verificação de limites
        const workbook = await tx.workbook.create({
          data: {
            name: data.name,
            description: data.description || '',
            isPublic: data.isPublic ?? false,
            userId,
            sheets: {
              create: {
                name: 'Planilha 1',
                data: data.initialData ? JSON.stringify(data.initialData) : null,
              },
            },
          },
          include: { sheets: true },
        });

        // Se há comando de IA, processar assincronamente
        if (data.aiCommand) {
          logger.info('Workbook criado com comando de IA - iniciando processamento assíncrono', {
            workbookId: workbook.id,
            userId,
            aiCommand: data.aiCommand.substring(0, 100) + '...',
          });

          // Adicionar job à fila de processamento
          try {
            await queueManager.initialize();

            const job = await queueManager.addAIProcessingJob({
              workbookId: workbook.id,
              userId,
              command: data.aiCommand,
              context: {
                headers: [],
                rowCount: 0,
                colCount: 0,
                existingData: data.initialData,
              },
              priority: 'normal',
            });

            // Notificar início do processamento
            await notificationService.sendAIProcessingStarted(
              userId,
              workbook.id,
              data.aiCommand
            );

            logger.info('✅ Job de IA adicionado à fila', {
              workbookId: workbook.id,
              jobId: job.id,
              userId,
            });

          } catch (queueError) {
            logger.error('💥 Erro ao adicionar job de IA à fila:', queueError, {
              workbookId: workbook.id,
              userId,
            });

            // Não falhar a criação do workbook por erro na fila
            // O usuário ainda pode usar a planilha normalmente
          }
        }

        return workbook;
      });

      logger.info('Novo workbook criado', {
        workbookId: workbook.id,
        userId,
      });

      return workbook;
    } catch (error) {
      logger.error('Erro ao criar workbook', {
        userId,
        data,
        error,
      });
      throw error instanceof Error ? error : new Error('Falha ao criar workbook');
    }
  },

  /**
   * Atualiza um workbook existente
   */
  async updateWorkbook(data: UpdateWorkbookInput, userId: string) {
    try {
      // Verificar se o workbook existe e pertence ao usuário com uma operação atômica
      const result = await prisma.$transaction(async prisma => {
        const existingWorkbook = await prisma.workbook.findFirst({
          where: {
            id: data.id,
            userId,
          },
        });

        if (!existingWorkbook) {
          // Registrar tentativa potencialmente maliciosa
          await prisma.securityLog.create({
            data: {
              userId,
              eventType: 'unauthorized_workbook_update',
              details: JSON.stringify({ workbookId: data.id }),
              timestamp: new Date(),
            },
          });

          throw new Error('Workbook não encontrado ou não pertence ao usuário');
        }

        // Construir objeto de atualização apenas com campos fornecidos - tipado corretamente
        const updateData: Record<string, unknown> = {};
        if (data.name !== undefined) updateData.name = data.name;
        if (data.description !== undefined) updateData.description = data.description;
        if (data.isPublic !== undefined) updateData.isPublic = data.isPublic;

        const updatedWorkbook = await prisma.workbook.update({
          where: { id: data.id },
          // Usamos tipagem genérica para dados de atualização que são controlados e validados acima
          data: updateData as Record<string, unknown>,
          include: { sheets: true },
        });

        // Registrar ação bem-sucedida
        await prisma.userActionLog.create({
          data: {
            userId,
            action: 'workbook_updated',
            details: JSON.stringify({
              workbookId: data.id,
              fields: Object.keys(updateData as object),
            }),
            timestamp: new Date(),
          },
        });

        return updatedWorkbook;
      });

      logger.info('Workbook atualizado', {
        workbookId: result.id,
        userId,
      });

      return result;
    } catch (error) {
      logger.error('Erro ao atualizar workbook', {
        workbookId: data.id,
        userId,
        error,
      });
      throw error;
    }
  },

  /**
   * Exclui um workbook
   */
  async deleteWorkbook(id: string, userId: string) {
    try {
      // Verificação e exclusão em transação atômica
      await prisma.$transaction(async prisma => {
        // Verificar se o workbook existe e pertence ao usuário
        const existingWorkbook = await prisma.workbook.findFirst({
          where: {
            id,
            userId,
          },
        });

        if (!existingWorkbook) {
          // Registrar tentativa potencialmente maliciosa
          await prisma.securityLog.create({
            data: {
              userId,
              eventType: 'unauthorized_workbook_deletion',
              details: JSON.stringify({ workbookId: id }),
              timestamp: new Date(),
            },
          });

          throw new Error('Workbook não encontrado ou não pertence ao usuário');
        }

        // Excluir workbook
        await prisma.workbook.delete({
          where: { id },
        });

        // Registrar ação bem-sucedida
        await prisma.userActionLog.create({
          data: {
            userId,
            action: 'workbook_deleted',
            details: JSON.stringify({ workbookId: id }),
            timestamp: new Date(),
          },
        });
      });

      logger.info('Workbook excluído', {
        workbookId: id,
        userId,
      });

      return true;
    } catch (error) {
      logger.error('Erro ao excluir workbook', {
        workbookId: id,
        userId,
        error,
      });
      throw error;
    }
  },
};
