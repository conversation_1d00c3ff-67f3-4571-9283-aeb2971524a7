/**
 * Extensões de tipos para componentes
 * Este arquivo estende as interfaces de componentes existentes para incluir propriedades com prefixo '_'
 */

// Extensões para hooks e contextos
declare module '@/hooks/use-theme' {
  interface UseThemeProps {
    _theme?: string;
  }
}

declare module '@/lib/locale' {
  interface LocaleContextType {
    _t?: (key: string) => string;
  }
}

declare module '@/components/ui/use-toast' {
  interface UseToastReturn {
    _toast?: any;
  }
}

// Extensões para estados
declare module '@/types/collaboration' {
  interface CollabState {
    _submitCellEdit?: (cellId: string, value: any) => void;
    _userName?: string;
    _userId?: string;
  }
}

declare module '@/types/bridge' {
  interface BridgeState {
    _status?: string;
    _platform?: string;
  }

  interface BridgeActions {
    _disconnect?: () => Promise<boolean>;
  }
}

// Extensões para props de componentes
declare module '@/components/ui/theme-toggle' {
  interface ThemeToggleProps {
    _variant?: string;
  }
}

declare module '@/components/ClientLayoutWrapper' {
  interface ClientLayoutWrapperProps {
    _className?: string;
    _interClassName?: string;
  }
}

declare module '@/app/providers' {
  interface ProvidersProps {
    _locale?: string;
  }
}

// Interfaces para tipos de detalhes
export interface ServiceDetails {
  criticalServicesReady?: boolean;
  initializedServices?: string[];
  healthChecksComplete?: boolean;
}

// Para hooks de form
export interface UseFormRegisterReturn<T extends string = string> {
  value?: string;
}

// Extensões para ExcelFileInfo
export interface ExcelFileInfoExtended {
  name?: string;
  path?: string;
  installed?: boolean;
}

// Tipos de eventos para formulários
export type FormInputEvent = React.ChangeEvent<HTMLInputElement>;
export type FormTextareaEvent = React.ChangeEvent<HTMLTextAreaElement>;
