import { logger } from '../../lib/logger';
import { ExcelOperationType, ExcelOperation } from '../../types/index';
import {
  executeFormulaOperation,
  executeChartOperation,
  executeFilterOperation,
  executeSortOperation,
  executeCellOperation,
  executeTableOperation,
} from '../operations';

import { normalizeOperation } from './operationUtils';

// Interface local para compatibilidade
interface LocalExcelOperation extends ExcelOperation {
  // Este campo garante que a interface não fique vazia
  _localId?: string;
}

// Função auxiliar para converter para operação local
function convertToLocalOperation(operation: ExcelOperation): LocalExcelOperation {
  return operation as LocalExcelOperation;
}

// Interfaces removidas pois não são utilizadas
// interface DataWithMeta extends Array<any> {
//   meta?: {
//     filters?: Array<{ column: string; operator: string; value: any }>;
//     sort?: { column: string; direction: string };
//   };
// }

// interface ExcelBaseOperation {
//   /** Identificador único da operação */
//   id?: string;
// }

// Função removida pois não é utilizada
// function deepClone<T>(obj: T): T {
//   if (obj === null || typeof obj !== 'object') {
//     return obj;
//   }

//   // Verificar se é um array
//   if (Array.isArray(obj)) {
//     const copy: any[] = [];
//     for (let i = 0; i < obj.length; i++) {
//       copy[i] = deepClone(obj[i]);
//     }
//     return copy as unknown as T;
//   }

//   // Verificar se é um objeto
//   const copy: Record<string, any> = {};
//   for (const key in obj) {
//     if (Object.prototype.hasOwnProperty.call(obj, key)) {
//       copy[key] = deepClone((obj as Record<string, any>)[key]);
//     }
//   }
//   return copy as T;
// }

/**
 * Executa operações Excel de forma sequencial
 * @param currentData Dados atuais
 * @param operations Lista de operações a serem executadas
 * @returns Dados atualizados após as operações
 */
export async function executeOperationsSequential(
  currentData: any,
  operations: ExcelOperation[]
): Promise<any> {
  try {
    let result = { ...currentData };

    for (const op of operations) {
      logger.debug(`Executando operação: ${op.type}`, op);
      result = await executeOperation(result, op);
    }

    return result;
  } catch (error) {
    logger.error('Erro ao executar operações sequencialmente:', error);
    throw error;
  }
}

/**
 * Executa uma única operação Excel
 * @param currentData Dados atuais
 * @param operation Operação a ser executada
 * @returns Dados atualizados após a operação
 */
export async function executeOperation(currentData: any, operation: ExcelOperation): Promise<any> {
  try {
    // Normalizar e converter para LocalExcelOperation para garantir que type é ExcelOperationType
    const baseNormalized = normalizeOperation(operation);
    const normalizedOperation = convertToLocalOperation(baseNormalized);

    // Usar uma abordagem de string no switch para maior compatibilidade
    const operationType = normalizedOperation.type;

    switch (operationType) {
      case ExcelOperationType.FORMULA:
        return await executeFormulaOperation(currentData, normalizedOperation);
      case ExcelOperationType.FILTER:
        return await executeFilterOperation(currentData, normalizedOperation);
      case ExcelOperationType.SORT:
        return await executeSortOperation(currentData, normalizedOperation);
      case ExcelOperationType.CHART:
        return await executeChartOperation(currentData, normalizedOperation);
      case ExcelOperationType.CELL_UPDATE:
        return await executeCellOperation(currentData, normalizedOperation);
      case 'CELL': // String literal para compatibilidade com código existente
        return await executeCellOperation(currentData, normalizedOperation);
      case ExcelOperationType.TABLE:
      case 'GENERIC': // String literal para compatibilidade
        return await executeTableOperation(currentData, normalizedOperation);
      default:
        logger.warn(`Tipo de operação não implementado: ${normalizedOperation.type}`);
        return currentData;
    }
  } catch (error) {
    logger.error(`Erro ao executar operação ${operation.type}:`, error);
    throw error;
  }
}
