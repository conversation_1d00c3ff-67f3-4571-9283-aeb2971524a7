'use client';

import { motion } from 'framer-motion';

import { MotionSafe } from '../ui/motion';

/**
 * Exemplo de uso do componente MotionSafe
 * Demonstra como criar animações que respeitam preferências de redução de movimento
 */
export function MotionSafeExample() {
  return (
    <div className="space-y-8 py-8">
      <h2 className="text-2xl font-bold">Exemplo de MotionSafe</h2>

      <div className="space-y-4">
        <h3 className="text-xl">Exemplo 1: Animação de entrada com fallback estático</h3>
        <MotionSafe
          fallback={<div className="p-6 bg-card rounded-lg shadow">Conteúdo sem animação</div>}
        >
          <motion.div
            className="p-6 bg-card rounded-lg shadow"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            Conteúdo com animação de fade e slide
          </motion.div>
        </MotionSafe>
      </div>

      <div className="space-y-4">
        <h3 className="text-xl">Exemplo 2: Lista com animação sequencial</h3>
        <MotionSafe>
          <div className="space-y-2">
            {[1, 2, 3, 4].map(item => (
              <motion.div
                key={item}
                className="p-4 bg-card rounded-lg shadow"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3, delay: item * 0.1 }}
              >
                Item {item}
              </motion.div>
            ))}
          </div>
        </MotionSafe>
      </div>

      <div className="space-y-4">
        <h3 className="text-xl">Exemplo 3: Hover animation</h3>
        <MotionSafe>
          <motion.button
            className="px-4 py-2 bg-primary text-primary-foreground rounded-md"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            Botão com animação
          </motion.button>
        </MotionSafe>
      </div>
    </div>
  );
}
