import { PrismaClient } from '@prisma/client';

// Implementação temporária até que o módulo logger esteja disponível
const logger = {
  info: (message: string, ...args: unknown[]): void => {
    if (process.env.NODE_ENV !== 'production') {
      console.info(`[DB INFO] ${message}`, ...args);
    }
  },
  error: (message: string, ...args: unknown[]): void => {
    console.error(`[DB ERROR] ${message}`, ...args);
  },
  warn: (message: string, ...args: unknown[]): void => {
    console.warn(`[DB WARNING] ${message}`, ...args);
  },
  debug: (_message: string, ..._args: unknown[]): void => {
    if (process.env.NODE_ENV === 'development') {
      // [DB DEBUG] logged
    }
  },
};

// Detectar se estamos no Edge Runtime
const isEdgeRuntime =
  typeof (globalThis as Record<string, unknown>).EdgeRuntime !== 'undefined' ||
  process.env.VERCEL_REGION !== undefined ||
  (globalThis as Record<string, unknown>).EdgeRuntime !== undefined;

// Cliente Prisma para Edge Runtime
let edgePrisma: PrismaClient | null = null;

// Configurações avançadas para o Prisma Client compatível com Edge Runtime
const createEdgePrismaClient = () => {
  try {
    if (isEdgeRuntime) {
      // Para Edge Runtime, usar configuração simplificada
      const client = new PrismaClient({
        log: process.env.NODE_ENV === 'development' ? ['error', 'warn'] : ['error'],
        datasources: {
          db: {
            url: process.env.DB_DATABASE_URL || '',
          },
        },
      });

      logger.info('Cliente Prisma Edge Runtime com adapter criado com sucesso');
      return client;
    } else {
      // Para Node.js Runtime, usar configuração padrão
      const client = new PrismaClient({
        log: process.env.NODE_ENV === 'development' ? ['error', 'warn'] : ['error'],
        datasources: {
          db: {
            url: process.env.DB_DATABASE_URL || '',
          },
        },
      });

      logger.info('Cliente Prisma Node.js Runtime criado com sucesso');
      return client;
    }
  } catch (error) {
    logger.error('Erro ao criar cliente Prisma:', error);
    throw error;
  }
};

// Função para obter o cliente Prisma apropriado
export const getPrismaClient = (): PrismaClient => {
  if (isEdgeRuntime) {
    if (!edgePrisma) {
      edgePrisma = createEdgePrismaClient();
    }
    return edgePrisma;
  }

  // Fallback para o cliente padrão
  return new PrismaClient({
    log: process.env.NODE_ENV === 'development' ? ['error', 'warn'] : ['error'],
    datasources: {
      db: {
        url: process.env.DB_DATABASE_URL || '',
      },
    },
  });
};

// Export do cliente principal
export const prisma = getPrismaClient();

// Função para desconectar de forma segura
export async function disconnectEdgeDatabase(): Promise<void> {
  try {
    if (edgePrisma) {
      await edgePrisma.$disconnect();
      edgePrisma = null;
      logger.info('Conexão Edge Runtime com o banco de dados encerrada com sucesso');
    }
  } catch (error) {
    logger.error('Erro ao desconectar do banco de dados Edge Runtime', error);
  }
}

// Métricas simplificadas para Edge Runtime
interface EdgeDatabaseMetrics {
  isEdgeRuntime: boolean;
  connectionStatus: 'connected' | 'disconnected' | 'error';
  lastCheck: string;
}

export function getEdgeDatabaseMetrics(): EdgeDatabaseMetrics {
  return {
    isEdgeRuntime,
    connectionStatus: edgePrisma ? 'connected' : 'disconnected',
    lastCheck: new Date().toISOString(),
  };
}

// Função de teste de conexão para Edge Runtime
export async function testEdgeConnection(): Promise<boolean> {
  try {
    const client = getPrismaClient();
    await client.$queryRaw`SELECT 1`;
    logger.info('Teste de conexão Edge Runtime bem-sucedido');
    return true;
  } catch (error) {
    logger.error('Teste de conexão Edge Runtime falhou:', error);
    return false;
  }
}

// Export default para compatibilidade
export default prisma;
