#!/usr/bin/env node

/**
 * Script para testar a configuração do Stripe
 * Execute com: node scripts/test-stripe-config.js
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Testando configuração do Stripe...\n');

// Função para carregar variáveis de ambiente
function loadEnvFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const env = {};

    content.split('\n').forEach(line => {
      const match = line.match(/^([^#][^=]*?)=(.*)$/);
      if (match) {
        const key = match[1].trim();
        const value = match[2].trim().replace(/^["']|["']$/g, '');
        env[key] = value;
      }
    });

    return env;
  } catch (error) {
    console.error(`❌ Erro ao ler ${filePath}:`, error.message);
    return {};
  }
}

// Carregar variáveis de ambiente
const envPath = path.join(__dirname, '../.env');
const envLocalPath = path.join(__dirname, '../.env.local');

const env = loadEnvFile(envPath);
const envLocal = loadEnvFile(envLocalPath);

console.log('📁 Arquivos de ambiente encontrados:');
console.log(`   .env: ${fs.existsSync(envPath) ? '✅' : '❌'}`);
console.log(`   .env.local: ${fs.existsSync(envLocalPath) ? '✅' : '❌'}\n`);

// Testar chaves do Stripe
console.log('🔑 Verificando chaves do Stripe:\n');

const stripeKeys = {
  STRIPE_SECRET_KEY: env.STRIPE_SECRET_KEY || envLocal.STRIPE_SECRET_KEY,
  NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY:
    env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY || envLocal.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY,
  STRIPE_WEBHOOK_SECRET: env.STRIPE_WEBHOOK_SECRET || envLocal.STRIPE_WEBHOOK_SECRET,
  NEXT_PUBLIC_STRIPE_PRICE_MONTHLY:
    env.NEXT_PUBLIC_STRIPE_PRICE_MONTHLY || envLocal.NEXT_PUBLIC_STRIPE_PRICE_MONTHLY,
  NEXT_PUBLIC_STRIPE_PRICE_ANNUAL:
    env.NEXT_PUBLIC_STRIPE_PRICE_ANNUAL || envLocal.NEXT_PUBLIC_STRIPE_PRICE_ANNUAL,
};

let hasErrors = false;

Object.entries(stripeKeys).forEach(([key, value]) => {
  if (!value) {
    console.log(`❌ ${key}: NÃO DEFINIDA`);
    hasErrors = true;
  } else {
    const preview = value.length > 20 ? `${value.substring(0, 20)}...` : value;
    console.log(`✅ ${key}: ${preview}`);

    // Validações específicas
    if (key === 'STRIPE_SECRET_KEY') {
      if (!value.startsWith('sk_')) {
        console.log(`   ⚠️  Aviso: Chave secreta deve começar com 'sk_'`);
        hasErrors = true;
      }
    }

    if (key === 'NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY') {
      if (!value.startsWith('pk_')) {
        console.log(`   ⚠️  Aviso: Chave pública deve começar com 'pk_'`);
        hasErrors = true;
      }
    }

    if (key === 'STRIPE_WEBHOOK_SECRET') {
      if (!value.startsWith('whsec_')) {
        console.log(`   ⚠️  Aviso: Webhook secret deve começar com 'whsec_'`);
        hasErrors = true;
      }
    }

    if (key.includes('PRICE_')) {
      if (!value.startsWith('price_')) {
        console.log(`   ⚠️  Aviso: Price ID deve começar com 'price_'`);
        hasErrors = true;
      }
    }
  }
});

console.log('\n🔒 Verificando tipo de chaves:');
const secretKey = stripeKeys.STRIPE_SECRET_KEY;
const publicKey = stripeKeys.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY;

if (secretKey && publicKey) {
  const isLive = secretKey.includes('_live_') && publicKey.includes('_live_');
  const isTest = secretKey.includes('_test_') && publicKey.includes('_test_');

  if (isLive) {
    console.log('🔴 Usando chaves LIVE (produção)');
    console.log('   ⚠️  Certifique-se de que está em produção!');
  } else if (isTest) {
    console.log('🟡 Usando chaves TEST (desenvolvimento)');
    console.log('   ✅ Seguro para desenvolvimento');
  } else {
    console.log('❌ Chaves inconsistentes (mistura de live/test)');
    hasErrors = true;
  }
}

// Verificar CSP
console.log('\n🛡️  Verificando Content Security Policy:');

const middlewarePath = path.join(__dirname, '../src/middleware.ts');
const nextConfigPath = path.join(__dirname, '../next.config.js');

try {
  const middlewareContent = fs.readFileSync(middlewarePath, 'utf8');
  const hasStripeInCSP = middlewareContent.includes('https://js.stripe.com');
  const hasScriptSrcElem = middlewareContent.includes('script-src-elem');

  console.log(`   middleware.ts - Stripe no CSP: ${hasStripeInCSP ? '✅' : '❌'}`);
  console.log(`   middleware.ts - script-src-elem: ${hasScriptSrcElem ? '✅' : '❌'}`);

  if (!hasStripeInCSP || !hasScriptSrcElem) {
    hasErrors = true;
  }
} catch (error) {
  console.log(`   ❌ Erro ao verificar middleware.ts: ${error.message}`);
  hasErrors = true;
}

try {
  const nextConfigContent = fs.readFileSync(nextConfigPath, 'utf8');
  const hasStripeInNextConfig = nextConfigContent.includes('https://js.stripe.com');
  const hasScriptSrcElemInNext = nextConfigContent.includes('script-src-elem');

  console.log(`   next.config.js - Stripe no CSP: ${hasStripeInNextConfig ? '✅' : '❌'}`);
  console.log(`   next.config.js - script-src-elem: ${hasScriptSrcElemInNext ? '✅' : '❌'}`);

  if (!hasStripeInNextConfig || !hasScriptSrcElemInNext) {
    hasErrors = true;
  }
} catch (error) {
  console.log(`   ❌ Erro ao verificar next.config.js: ${error.message}`);
  hasErrors = true;
}

// Resultado final
console.log('\n' + '='.repeat(50));
if (hasErrors) {
  console.log('❌ CONFIGURAÇÃO COM PROBLEMAS');
  console.log('\nVerifique os itens marcados com ❌ ou ⚠️  acima.');
  process.exit(1);
} else {
  console.log('✅ CONFIGURAÇÃO OK');
  console.log('\nTodas as verificações passaram!');
  console.log('Você pode testar o Stripe na página /pricing');
}
