// Re-export all UI components for easier imports

import { Badge } from './badge';
import { Button } from './button';
import ErrorMessage from './error-message';
import Input from './input';
import { OptimizedButton, ActionButton } from './optimized-button';
import { ScrollArea } from './scroll-area';
import Textarea from './textarea';
import { ThemeToggle } from './theme-toggle';
import { useToastContext as useToast, toastFn } from './use-toast';

// Re-export all components
export { Badge };
export { Button };
export { ErrorMessage };
export { Input };
export { OptimizedButton };
export { ActionButton };
export { ScrollArea };
export { Textarea };
export { ThemeToggle };
export { useToast };
export const toast = toastFn;

// Default export with all components
const UI = {
  Badge,
  Button,
  ErrorMessage,
  Input,
  OptimizedButton,
  ActionButton,
  ScrollArea,
  Textarea,
  ThemeToggle,
  useToast,
  toast,
};

export default UI;
