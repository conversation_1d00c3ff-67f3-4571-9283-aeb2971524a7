import crypto from 'crypto';

import { logger } from '@/lib/logger';

import { cacheManager, CACHE_KEYS, CACHE_TTL } from './cache-manager';

/**
 * Interface para resultado de comando de IA cacheado
 */
interface CachedAICommandResult {
  command: string;
  result: any;
  timestamp: string;
  userId: string;
  workbookContext?: {
    headers: string[];
    rowCount: number;
    colCount: number;
  };
}

/**
 * Gerenciador de cache específico para comandos de IA
 */
class AICommandCache {
  /**
   * Gera hash único para um comando de IA baseado no comando e contexto
   */
  private generateCommandHash(
    command: string,
    userId: string,
    context?: {
      headers?: string[];
      rowCount?: number;
      colCount?: number;
    }
  ): string {
    const normalizedCommand = command.toLowerCase().trim();
    const contextStr = context ? JSON.stringify(context) : '';
    const hashInput = `${normalizedCommand}:${userId}:${contextStr}`;
    
    return crypto
      .createHash('sha256')
      .update(hashInput)
      .digest('hex')
      .substring(0, 16); // Usar apenas os primeiros 16 caracteres
  }

  /**
   * Busca resultado de comando de IA no cache
   */
  async getCachedCommand(
    command: string,
    userId: string,
    context?: {
      headers?: string[];
      rowCount?: number;
      colCount?: number;
    }
  ): Promise<CachedAICommandResult | null> {
    try {
      const commandHash = this.generateCommandHash(command, userId, context);
      const cacheKey = CACHE_KEYS.AI_COMMAND(commandHash);
      
      const cached = await cacheManager.get<CachedAICommandResult>(cacheKey);
      
      if (cached) {
        logger.info('✅ AI command cache hit', {
          commandHash,
          command: command.substring(0, 50) + '...',
          userId,
        });
        
        return cached;
      }
      
      logger.debug('❌ AI command cache miss', {
        commandHash,
        command: command.substring(0, 50) + '...',
        userId,
      });
      
      return null;
    } catch (error) {
      logger.error('💥 Erro ao buscar comando de IA no cache:', error, {
        command: command.substring(0, 50) + '...',
        userId,
      });
      return null;
    }
  }

  /**
   * Salva resultado de comando de IA no cache
   */
  async setCachedCommand(
    command: string,
    result: any,
    userId: string,
    context?: {
      headers?: string[];
      rowCount?: number;
      colCount?: number;
    }
  ): Promise<boolean> {
    try {
      const commandHash = this.generateCommandHash(command, userId, context);
      const cacheKey = CACHE_KEYS.AI_COMMAND(commandHash);
      
      const cachedResult: CachedAICommandResult = {
        command,
        result,
        timestamp: new Date().toISOString(),
        userId,
        workbookContext: context ? {
          headers: context.headers || [],
          rowCount: context.rowCount || 0,
          colCount: context.colCount || 0,
        } : undefined,
      };
      
      const success = await cacheManager.set(
        cacheKey,
        cachedResult,
        CACHE_TTL.AI_COMMAND_RESULTS
      );
      
      if (success) {
        logger.info('💾 AI command cached successfully', {
          commandHash,
          command: command.substring(0, 50) + '...',
          userId,
          ttl: CACHE_TTL.AI_COMMAND_RESULTS,
        });
      }
      
      return success;
    } catch (error) {
      logger.error('💥 Erro ao salvar comando de IA no cache:', error, {
        command: command.substring(0, 50) + '...',
        userId,
      });
      return false;
    }
  }

  /**
   * Invalida cache de comandos de IA para um usuário específico
   */
  async invalidateUserAICache(userId: string): Promise<number> {
    try {
      const pattern = `ai:command:*`;
      const deletedCount = await cacheManager.invalidatePattern(pattern);
      
      logger.info('🧹 AI command cache invalidated for user', {
        userId,
        deletedCount,
      });
      
      return deletedCount;
    } catch (error) {
      logger.error('💥 Erro ao invalidar cache de IA do usuário:', error, { userId });
      return 0;
    }
  }

  /**
   * Busca comandos de IA populares do cache
   */
  async getPopularCommands(): Promise<string[]> {
    try {
      const cached = await cacheManager.get<string[]>(CACHE_KEYS.POPULAR_AI_COMMANDS());
      
      if (cached) {
        logger.debug('✅ Popular AI commands cache hit');
        return cached;
      }
      
      // Retornar comandos padrão se não houver cache
      const defaultCommands = [
        'Criar planilha de controle financeiro com categorias',
        'Adicionar gráfico de barras para vendas mensais',
        'Calcular soma total da coluna',
        'Criar tabela dinâmica com dados',
        'Formatar células como moeda',
      ];
      
      // Cachear comandos padrão
      await cacheManager.set(
        CACHE_KEYS.POPULAR_AI_COMMANDS(),
        defaultCommands,
        CACHE_TTL.POPULAR_COMMANDS
      );
      
      return defaultCommands;
    } catch (error) {
      logger.error('💥 Erro ao buscar comandos populares:', error);
      return [];
    }
  }

  /**
   * Atualiza lista de comandos populares
   */
  async updatePopularCommands(commands: string[]): Promise<boolean> {
    try {
      const success = await cacheManager.set(
        CACHE_KEYS.POPULAR_AI_COMMANDS(),
        commands,
        CACHE_TTL.POPULAR_COMMANDS
      );
      
      if (success) {
        logger.info('📈 Popular AI commands updated', {
          commandCount: commands.length,
        });
      }
      
      return success;
    } catch (error) {
      logger.error('💥 Erro ao atualizar comandos populares:', error);
      return false;
    }
  }

  /**
   * Obtém estatísticas do cache de comandos de IA
   */
  async getAICacheStats(): Promise<{
    totalCachedCommands: number;
    popularCommandsCount: number;
    cacheHitRate: number;
  }> {
    try {
      const cacheMetrics = cacheManager.getMetrics();
      const popularCommands = await this.getPopularCommands();
      
      return {
        totalCachedCommands: 0, // Seria necessário implementar contagem específica
        popularCommandsCount: popularCommands.length,
        cacheHitRate: cacheMetrics.hitRate,
      };
    } catch (error) {
      logger.error('💥 Erro ao obter estatísticas do cache de IA:', error);
      return {
        totalCachedCommands: 0,
        popularCommandsCount: 0,
        cacheHitRate: 0,
      };
    }
  }

  /**
   * Limpa cache de comandos de IA antigos (mais de 24 horas)
   */
  async cleanupOldCommands(): Promise<number> {
    try {
      // Esta implementação seria mais complexa em produção
      // Por enquanto, vamos apenas invalidar todos os comandos
      const pattern = 'ai:command:*';
      const deletedCount = await cacheManager.invalidatePattern(pattern);
      
      logger.info('🧹 Old AI commands cleaned up', { deletedCount });
      return deletedCount;
    } catch (error) {
      logger.error('💥 Erro ao limpar comandos antigos:', error);
      return 0;
    }
  }
}

export const aiCommandCache = new AICommandCache();
