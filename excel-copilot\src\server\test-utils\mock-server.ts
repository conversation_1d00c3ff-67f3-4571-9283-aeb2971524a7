/**
 * Mock Server para testes
 *
 * Esta é uma implementação simples de um servidor de mock para testes,
 * que permite registrar handlers para diferentes rotas e métodos HTTP.
 */

// Tipos para o mock server
export interface MockRequest {
  params: Record<string, string>;
  query: Record<string, string>;
  body: unknown;
  headers: Record<string, string>;
  url: string;
  method: string;
}

export interface MockResponse {
  status: number;
  body: unknown;
  headers?: Record<string, string>;
}

export interface MockHandler {
  path: string;
  method: string;
  handler: (req: MockRequest) => MockResponse;
}

export interface MockServerInstance {
  addHandler: (handler: <PERSON>ck<PERSON>andler) => void;
  handleRequest: (path: string, method: string, body?: unknown) => MockResponse;
  resetHandlers: () => void;
}

/**
 * Extrai parâmetros da rota a partir de um caminho
 * Ex: "/users/:id" e "/users/123" => { id: "123" }
 */
const extractParams = (routePath: string, actualPath: string): Record<string, string> | null => {
  const routeParts = routePath.split('/').filter(Boolean);
  const actualParts = actualPath.split('/').filter(Boolean);

  if (routeParts.length !== actualParts.length) return null;

  const params: Record<string, string> = {};

  for (let i = 0; i < routeParts.length; i++) {
    const routePart = routeParts[i];
    const actualPart = actualParts[i];

    // Verificar se routePart e actualPart estão definidos
    if (!routePart || !actualPart) {
      return null;
    }

    if (routePart.startsWith(':')) {
      // Este é um parâmetro
      const paramName = routePart.substring(1);
      // Garantir que paramName é uma string válida
      if (paramName) {
        params[paramName] = actualPart;
      }
    } else if (routePart !== actualPart) {
      // Não corresponde à rota
      return null;
    }
  }

  return params;
};

/**
 * Cria uma nova instância do mock server
 */
export const setupServer = (): MockServerInstance => {
  const handlers: MockHandler[] = [];

  return {
    addHandler: (handler: MockHandler): void => {
      // Verificar se já existe um handler para esta rota e método
      const existingHandlerIndex = handlers.findIndex(
        h => h.path === handler.path && h.method === handler.method
      );

      if (existingHandlerIndex !== -1) {
        // Substituir handler existente
        handlers[existingHandlerIndex] = handler;
      } else {
        // Adicionar novo handler
        handlers.push(handler);
      }
    },

    handleRequest: (path: string, method: string, body?: unknown): MockResponse => {
      // Procurar um handler que corresponda à rota e método
      for (const handler of handlers) {
        if (method.toUpperCase() === handler.method.toUpperCase()) {
          // Verificar se o path corresponde à rota (incluindo parâmetros)
          const params = extractParams(handler.path, path);

          if (params !== null) {
            // Encontramos um handler correspondente
            const request: MockRequest = {
              params,
              url: path || '',
              method,
              query: {}, // TODO: Implementar extração de query params
              body: body || {},
              headers: {},
            };

            // Executar o handler diretamente sem try/catch desnecessário
            return handler.handler(request);
          }
        }
      }

      // Nenhum handler encontrado, retornar 404
      return {
        status: 404,
        body: { error: 'Not found', message: `No handler found for ${method} ${path}` },
      };
    },

    resetHandlers: (): void => {
      // Limpar todos os handlers
      handlers.length = 0;
    },
  };
};
