'use client';

import { FileSpreadsheet, ArrowRight } from 'lucide-react';

import { TYPOGRAPHY } from '@/lib/design-tokens';
import { cn } from '@/lib/utils';

export interface WorkbookItemProps {
  workbook: {
    id: string;
    name: string;
    sheets: any[];
    updatedAt: string | Date;
  };
  showBorder?: boolean;
  className?: string;
}

/**
 * Item de lista de workbook
 */
export function WorkbookListItem({ workbook, showBorder = true, className }: WorkbookItemProps) {
  return (
    <a
      href={`/workbook/${workbook.id}`}
      className={cn(
        'flex items-center justify-between p-4 hover:bg-gray-50 transition-colors duration-200 mx-1 my-1 rounded-md',
        showBorder && 'border-b border-gray-200/50',
        'no-underline text-inherit',
        className
      )}
    >
      <div className="flex items-center gap-4">
        <div className="w-10 h-10 rounded-md bg-blue-50 flex items-center justify-center">
          <FileSpreadsheet className="text-blue-500 w-5 h-5" />
        </div>
        <div>
          <h3 className={cn(TYPOGRAPHY.heading.h4, 'mb-1')}>{workbook.name}</h3>
          <p className={cn(TYPOGRAPHY.caption)}>
            {workbook.sheets.length} {workbook.sheets.length === 1 ? 'planilha' : 'planilhas'} •
            Atualizado em {new Date(workbook.updatedAt).toLocaleDateString('pt-BR')}
          </p>
        </div>
      </div>
      <div className="flex items-center justify-center w-8 h-8 rounded-full hover:bg-blue-50 transition-colors duration-200">
        <ArrowRight className="text-gray-500 w-4 h-4" />
      </div>
    </a>
  );
}
