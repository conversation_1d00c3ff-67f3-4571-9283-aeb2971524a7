'use client';

import { Slot } from '@radix-ui/react-slot';
import { cva, type VariantProps } from 'class-variance-authority';
import { motion, HTMLMotionProps } from 'framer-motion';
import * as React from 'react';

import { ANIMATION_DURATION, ANIMATION_EASE } from '@/lib/animations';
import { cn } from '@/lib/utils';

const buttonVariants = cva(
  'inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',
  {
    variants: {
      variant: {
        default: 'bg-primary text-primary-foreground hover:bg-primary-dark',
        destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90',
        outline: 'border border-input bg-background hover:bg-accent hover:text-accent-foreground',
        secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',
        ghost: 'hover:bg-accent hover:text-accent-foreground',
        link: 'text-primary underline-offset-4 hover:underline',
        gradient: 'bg-gradient-primary text-primary-foreground border-none shadow-md',
        success: 'bg-success text-success-foreground hover:bg-success/90',
        info: 'bg-info text-info-foreground hover:bg-info/90',
        warning: 'bg-warning text-warning-foreground hover:bg-warning/90',
        glass: 'bg-background/80 backdrop-blur-md border border-border hover:bg-background/90',
      },
      size: {
        default: 'h-10 px-4 py-2',
        sm: 'h-9 rounded-md px-3',
        lg: 'h-11 rounded-md px-8',
        xl: 'h-12 rounded-md px-10 text-base',
        icon: 'h-10 w-10',
        'icon-sm': 'h-8 w-8',
      },
      rounded: {
        default: 'rounded-md',
        full: 'rounded-full',
        xl: 'rounded-xl',
      },
      // Remove CSS animation quando usar Framer Motion
      cssFeedback: {
        none: '',
        scale: 'transition-transform active:scale-95',
        pulse: 'transition-all active:scale-95 hover:shadow-md',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
      rounded: 'default',
      cssFeedback: 'scale',
    },
  }
);

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
  animated?: boolean;
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      className,
      variant,
      size,
      rounded,
      cssFeedback,
      asChild = false,
      animated = false,
      icon,
      iconPosition = 'left',
      children,
      ...props
    },
    ref
  ) => {
    const Comp = asChild ? Slot : 'button';

    // Componente de botão com foco na acessibilidade
    const buttonContent = (
      <span className="inline-flex items-center justify-center">
        {icon && iconPosition === 'left' && <span className="mr-2">{icon}</span>}
        {children}
        {icon && iconPosition === 'right' && <span className="ml-2">{icon}</span>}
      </span>
    );

    // Se o botão for animado, use Framer Motion
    if (animated) {
      // Configurações de animação consistentes com o sistema central
      const buttonAnimations = {
        whileTap: { scale: 0.97 },
        // Aplicar hover somente nos variantes que suportam elevação
        whileHover: ['link', 'ghost'].includes(variant as string) ? undefined : { y: -2 },
        transition: {
          duration: ANIMATION_DURATION * 0.67,
          ease: ANIMATION_EASE,
        },
      };

      // Desativar feedback CSS quando usar Framer Motion
      const motionButtonClassNames = cn(
        buttonVariants({
          variant,
          size,
          rounded,
          cssFeedback: 'none', // Desativa animações CSS
          className,
        })
      );

      // Usar uma asserção de tipo mais segura para evitar erros do TypeScript
      const motionProps = {
        ...props,
        className: motionButtonClassNames,
        ...buttonAnimations,
      } as unknown as Omit<HTMLMotionProps<'button'>, 'ref'>;

      return (
        <motion.button ref={ref as React.Ref<HTMLButtonElement>} {...motionProps}>
          {buttonContent}
        </motion.button>
      );
    }

    return (
      <Comp
        className={cn(buttonVariants({ variant, size, rounded, cssFeedback, className }))}
        ref={ref}
        {...props}
      >
        {buttonContent}
      </Comp>
    );
  }
);
Button.displayName = 'Button';

export { Button, buttonVariants };
