import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';

import { extractSafeIP, extractSafeUserAgent } from '@/lib/auth/security';
import { logger } from '@/lib/logger';
import { authOptions } from '@/server/auth/options';
import { prisma } from '@/server/db/client';
import { ApiResponse } from '@/utils/api-response';

/**
 * Middleware de autenticação para rotas API
 * Verifica se o usuário está autenticado e adiciona informações da sessão ao contexto
 */
export async function authMiddleware(
  req: NextRequest,
  res: NextResponse,
  context: Record<string, any>
) {
  const startTime = Date.now();
  const ip = extractSafeIP(req);
  const userAgent = extractSafeUserAgent(req.headers.get('user-agent') || undefined);

  try {
    // Obter sessão do usuário
    const session = await getServerSession(authOptions);

    // Verificar se o usuário está autenticado
    if (!session?.user) {
      logger.warn('🚫 Acesso não autorizado a rota protegida', {
        path: req.nextUrl.pathname,
        method: req.method,
        ip,
        userAgent,
        timestamp: new Date().toISOString(),
        duration: Date.now() - startTime,
      });

      return ApiResponse.unauthorized('Usuário não autenticado');
    }

    // Adicionar informações de sessão ao contexto para uso nos handlers
    context.session = session;
    context.user = session.user;

    // Adicionar userId para facilitar acesso
    const userId = (session.user as any).id;
    if (userId) {
      context.userId = userId;

      try {
        // Verificar se o usuário está banido - buscar todos os campos necessários
        // Usando tipo any para lidar com o schema em transição após migração
        const userDB = (await prisma.user.findUnique({
          where: { id: userId },
        })) as any;

        // Verificação de segurança - os campos serão definidos quando a migração for aplicada
        if (userDB && userDB.isBanned === true) {
          logger.warn('Tentativa de acesso por usuário banido', {
            userId,
            path: req.nextUrl.pathname,
            banReason: userDB.banReason,
            banDate: userDB.banDate,
          });

          // Revogar a sessão atual
          try {
            await prisma.session.deleteMany({
              where: { userId },
            });
          } catch (sessionError) {
            logger.error('Erro ao revogar sessão de usuário banido', sessionError);
          }

          return ApiResponse.forbidden(
            `Conta suspensa: ${userDB.banReason || 'Violação de termos de uso'}`
          );
        }

        // Atualizar dados de último acesso - usando Record para criar um objeto dinâmico
        const updateData: Record<string, any> = {
          lastIpAddress: ip,
          lastLoginAt: new Date(),
          loginCount: { increment: 1 },
        };

        // Adicionar userAgent se o campo existir (após migração)
        if (userDB !== null) {
          // Este campo será válido após a migração
          updateData.userAgent = userAgent;
        }

        await prisma.user.update({
          where: { id: userId },
          data: updateData,
        });

        logger.debug('📊 Dados de auditoria atualizados', {
          userId,
          ip,
          userAgent: userAgent.substring(0, 50) + '...',
          path: req.nextUrl.pathname,
        });
      } catch (error) {
        logger.error('Erro ao verificar dados do usuário', error);
        // Continuar, não bloquear o fluxo por erros de busca/atualização
      }
    }

    logger.debug('✅ Usuário autenticado com sucesso', {
      userId: context.userId,
      path: req.nextUrl.pathname,
      duration: Date.now() - startTime,
    });

    // Continuar para o próximo middleware ou handler
  } catch (error) {
    logger.error('❌ Erro crítico ao verificar autenticação', {
      error: error instanceof Error ? error.message : 'Erro desconhecido',
      stack: error instanceof Error ? error.stack : undefined,
      path: req.nextUrl.pathname,
      method: req.method,
      ip,
      duration: Date.now() - startTime,
    });

    return ApiResponse.error('Erro ao verificar autenticação', 'AUTH_ERROR', 500);
  }
}

/**
 * Middleware opcional de autenticação
 * Semelhante ao authMiddleware, mas não bloqueia acesso se o usuário não estiver autenticado
 */
export async function optionalAuthMiddleware(
  req: NextRequest,
  res: NextResponse,
  context: Record<string, any>
) {
  try {
    const session = await getServerSession(authOptions);

    if (session?.user) {
      context.session = session;
      context.user = session.user;

      const userId = (session.user as any).id;
      if (userId) {
        context.userId = userId;

        try {
          // Verificar se o usuário está banido
          // Usando any para permitir a transição de schema
          const userDB = (await prisma.user.findUnique({
            where: { id: userId },
          })) as any;

          // Verificar isBanned de forma segura
          if (userDB && userDB.isBanned === true) {
            context.isBanned = true;
            logger.warn('Usuário banido acessando rota opcional', {
              userId,
              path: req.nextUrl.pathname,
            });
          } else {
            context.isBanned = false;
          }
        } catch (error) {
          logger.error('Erro ao verificar banimento do usuário', error);
          context.isBanned = false;
        }
      }

      context.isAuthenticated = true;
    } else {
      context.isAuthenticated = false;
      context.isBanned = false;
    }

    // Sempre continua para o próximo middleware/handler
  } catch (error) {
    logger.error('Erro ao verificar autenticação opcional', error);
    context.isAuthenticated = false;
    context.isBanned = false;
    // Continua mesmo com erro para não bloquear o acesso
  }
}
