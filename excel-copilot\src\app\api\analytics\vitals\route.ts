import { NextRequest, NextResponse } from 'next/server';
import { logger } from '@/lib/logger';

// Logger seguro para API routes - não vaza informações em produção
const sentryLogger = {
  info: (message: string, metadata?: any) => logger.info(message, metadata),
  error: (message: string, error?: any, metadata?: any) => logger.error(message, error, metadata),
  warn: (message: string, metadata?: any) => logger.warn(message, metadata),
  performance: (metric: string, value: number, metadata?: any) =>
    logger.info(`Performance metric: ${metric}`, { value, ...metadata })
};
import { prisma } from '@/server/db/client';

export const runtime = 'nodejs';
export const dynamic = 'force-dynamic';

interface WebVitalMetric {
  name: string;
  value: number;
  rating: 'good' | 'needs-improvement' | 'poor';
  delta: number;
  id: string;
  navigationType: string;
  url: string;
  userAgent: string;
  timestamp: number;
}

export async function POST(req: NextRequest) {
  try {
    const metric: WebVitalMetric = await req.json();

    // Validar dados recebidos
    if (!metric.name || typeof metric.value !== 'number') {
      return NextResponse.json({ error: 'Invalid metric data' }, { status: 400 });
    }

    // Log da métrica
    sentryLogger.performance(`web_vital_${metric.name}`, metric.value, {
      rating: metric.rating,
      url: metric.url,
      userAgent: metric.userAgent,
      navigationType: metric.navigationType,
    });

    // Salvar no banco de dados para análise posterior
    try {
      await prisma.webVital.create({
        data: {
          name: metric.name,
          value: metric.value,
          rating: metric.rating,
          delta: metric.delta,
          metricId: metric.id,
          navigationType: metric.navigationType,
          url: metric.url,
          userAgent: metric.userAgent,
          timestamp: new Date(metric.timestamp),
        },
      });
    } catch (dbError) {
      // Se não conseguir salvar no banco, apenas log o erro
      sentryLogger.warn('Failed to save web vital to database', { error: dbError });
    }

    // Alertar sobre métricas ruins
    if (metric.rating === 'poor') {
      sentryLogger.warn(`Poor Web Vital detected: ${metric.name}`, {
        value: metric.value,
        url: metric.url,
        userAgent: metric.userAgent,
      });
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    sentryLogger.error('Error processing web vital metric', error, {
      component: 'web-vitals-api',
    });

    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// Endpoint para obter estatísticas de Web Vitals
export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const days = parseInt(searchParams.get('days') || '7');
    const metric = searchParams.get('metric');

    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    // Query base
    const whereClause: {
      timestamp: { gte: Date };
      name?: string;
    } = {
      timestamp: {
        gte: startDate,
      },
    };

    if (metric) {
      whereClause.name = metric;
    }

    // Buscar métricas
    const metrics = await prisma.webVital.findMany({
      where: whereClause,
      orderBy: {
        timestamp: 'desc',
      },
      take: 1000, // Limitar resultados
    });

    // Calcular estatísticas
    const stats = {
      total: metrics.length,
      byRating: {
        good: metrics.filter(m => m.rating === 'good').length,
        needsImprovement: metrics.filter(m => m.rating === 'needs-improvement').length,
        poor: metrics.filter(m => m.rating === 'poor').length,
      },
      byMetric: {} as Record<
        string,
        {
          count: number;
          average: number;
          p75: number;
          p95: number;
        }
      >,
    };

    // Calcular estatísticas por métrica
    const metricNames = [...new Set(metrics.map(m => m.name))];

    for (const metricName of metricNames) {
      const metricData = metrics.filter(m => m.name === metricName);
      const values = metricData.map(m => m.value).sort((a, b) => a - b);

      stats.byMetric[metricName] = {
        count: metricData.length,
        average: values.reduce((sum, val) => sum + val, 0) / values.length,
        p75: values[Math.floor(values.length * 0.75)] || 0,
        p95: values[Math.floor(values.length * 0.95)] || 0,
      };
    }

    return NextResponse.json({
      success: true,
      data: {
        period: `${days} days`,
        stats,
        recentMetrics: metrics.slice(0, 50), // Últimas 50 métricas
      },
    });
  } catch (error) {
    sentryLogger.error('Error fetching web vital statistics', error, {
      component: 'web-vitals-api',
    });

    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
