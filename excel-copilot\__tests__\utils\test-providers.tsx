/**
 * Providers centralizados para testes
 * Evita duplicação de código entre diferentes arquivos de teste
 */

import React from 'react';

import { ThemeProvider } from '@/components/theme-provider';

// Mock session para testes
export const mockSession = {
  user: {
    id: 'test-user-id',
    name: 'Test User',
    email: '<EMAIL>',
    image: null,
  },
  expires: '2024-12-31',
};

interface AllProvidersProps {
  children: React.ReactNode;
}

/**
 * Wrapper para testes de componentes
 * Versão simplificada para evitar problemas de importação
 */
export const AllProviders = ({ children }: AllProvidersProps) => (
  <div data-testid="test-wrapper">{children}</div>
);

/**
 * Wrapper simplificado para testes que não precisam de autenticação
 */
export const SimpleProviders = ({ children }: { children: React.ReactNode }) => (
  <ThemeProvider defaultTheme="light" enableSystem={false}>
    {children}
  </ThemeProvider>
);

/**
 * Wrapper para testes que precisam apenas de QueryClient
 * Removido temporariamente devido a problemas de importação
 */

export default AllProviders;
