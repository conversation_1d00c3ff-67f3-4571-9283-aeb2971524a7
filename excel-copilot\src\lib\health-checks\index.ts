/**
 * 🏥 HEALTH CHECKS AGGREGATOR - EXCEL COPILOT
 *
 * Coordena todos os health checks e fornece interface unificada
 * para monitoramento do sistema
 *
 * <AUTHOR> Copilot Team
 * @version 1.0.0
 */

// import { alertManager } from '../alerts/alert-manager'; // Removido - não existe mais
import {
  HealthCheckResult,
  HealthReport,
  HealthStatus,
  createHealthReport,
  measureTime,
  healthLogger,
} from '../health-checks';
// import { metricsCollector } from '../monitoring/metrics-collector'; // Removido - não existe mais

// Mocks para compatibilidade
const alertManager = {
  sendAlert: () => Promise.resolve(),
  checkAlertRules: () => Promise.resolve([])
};

const metricsCollector = {
  recordHealthCheck: () => {},
  getMetrics: () => ({ uptime: Date.now(), requests: 0, errors: 0 })
};

import { createAIHealthCheck } from './ai';
import { createAuthHealthCheck } from './auth';
import { createDatabaseHealthCheck } from './database';
import { createMCPHealthCheck } from './mcp';
import { createStripeHealthCheck } from './stripe';

// ============================================================================
// TIPOS E INTERFACES
// ============================================================================

interface HealthCheck {
  execute(): Promise<HealthCheckResult>;
}

// ============================================================================
// HEALTH CHECKS MANAGER
// ============================================================================

export class HealthChecksManager {
  private static instance: HealthChecksManager;
  private checks: Map<string, HealthCheck> = new Map();

  private constructor() {
    this.initializeChecks();
  }

  static getInstance(): HealthChecksManager {
    if (!HealthChecksManager.instance) {
      HealthChecksManager.instance = new HealthChecksManager();
    }
    return HealthChecksManager.instance;
  }

  /**
   * Inicializa todos os health checks
   */
  private initializeChecks(): void {
    this.checks.set('database', createDatabaseHealthCheck());
    this.checks.set('auth', createAuthHealthCheck());
    this.checks.set('ai', createAIHealthCheck());
    this.checks.set('stripe', createStripeHealthCheck());
    this.checks.set('mcp', createMCPHealthCheck());
  }

  /**
   * Executa um health check específico
   */
  async checkService(serviceName: string): Promise<HealthCheckResult> {
    const check = this.checks.get(serviceName);

    if (!check) {
      throw new Error(`Health check not found for service: ${serviceName}`);
    }

    healthLogger.info(`Starting health check for ${serviceName}`);

    try {
      const result = await check.execute();

      healthLogger.info(`Health check completed for ${serviceName}`, {
        status: result.status,
        responseTime: result.responseTime,
      });

      // Processar resultado para alertas
      try {
        await alertManager.processHealthCheck(result);
      } catch (alertError: unknown) {
        const errorMessage = alertError instanceof Error ? alertError.message : 'Unknown error';
        healthLogger.error(`Failed to process alert for ${serviceName}`, {
          error: errorMessage,
        });
      }

      // Coletar métricas
      try {
        metricsCollector.recordHealthCheck(result);
      } catch (metricsError: unknown) {
        const errorMessage = metricsError instanceof Error ? metricsError.message : 'Unknown error';
        healthLogger.error(`Failed to record metrics for ${serviceName}`, {
          error: errorMessage,
        });
      }

      return result;
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      healthLogger.error(`Health check failed for ${serviceName}`, {
        error: errorMessage,
      });

      throw error;
    }
  }

  /**
   * Executa todos os health checks
   */
  async checkAll(): Promise<HealthReport> {
    healthLogger.info('Starting comprehensive health check');

    const { result: results, time: totalTime } = await measureTime(async () => {
      const serviceNames = Array.from(this.checks.keys());

      // Executar todos os checks em paralelo
      const checkPromises = serviceNames.map(async serviceName => {
        try {
          return await this.checkService(serviceName);
        } catch (error: unknown) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          // Se um check falhar, retornar resultado de erro
          return {
            service: serviceName,
            status: 'unhealthy' as HealthStatus,
            responseTime: 0,
            timestamp: new Date().toISOString(),
            details: {
              error: errorMessage,
            },
          };
        }
      });

      return await Promise.all(checkPromises);
    });

    const report = createHealthReport(results, totalTime);

    healthLogger.info('Comprehensive health check completed', {
      overall: report.overall,
      totalTime,
      servicesChecked: results.length,
      healthyServices: report.summary.healthy,
    });

    // Registrar métricas do relatório completo
    try {
      metricsCollector.recordHealthReport(report);
    } catch (metricsError: unknown) {
      const errorMessage = metricsError instanceof Error ? metricsError.message : 'Unknown error';
      healthLogger.error('Failed to record health report metrics', {
        error: errorMessage,
      });
    }

    return report;
  }

  /**
   * Executa health checks críticos apenas
   */
  async checkCritical(): Promise<HealthReport> {
    const criticalServices = ['database', 'auth'];

    healthLogger.info('Starting critical health check', {
      services: criticalServices,
    });

    const { result: results, time: totalTime } = await measureTime(async () => {
      const checkPromises = criticalServices.map(async serviceName => {
        try {
          return await this.checkService(serviceName);
        } catch (error: unknown) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          return {
            service: serviceName,
            status: 'unhealthy' as HealthStatus,
            responseTime: 0,
            timestamp: new Date().toISOString(),
            details: {
              error: errorMessage,
            },
          };
        }
      });

      return await Promise.all(checkPromises);
    });

    const report = createHealthReport(results, totalTime);

    healthLogger.info('Critical health check completed', {
      overall: report.overall,
      totalTime,
    });

    return report;
  }

  /**
   * Obtém lista de serviços disponíveis
   */
  getAvailableServices(): string[] {
    return Array.from(this.checks.keys());
  }
}

// ============================================================================
// INSTÂNCIA GLOBAL
// ============================================================================

export const healthManager = HealthChecksManager.getInstance();

// ============================================================================
// FUNÇÕES DE CONVENIÊNCIA
// ============================================================================

/**
 * Executa health check para um serviço específico
 */
export async function checkService(serviceName: string): Promise<HealthCheckResult> {
  return await healthManager.checkService(serviceName);
}

/**
 * Executa todos os health checks
 */
export async function checkAllServices(): Promise<HealthReport> {
  return await healthManager.checkAll();
}

/**
 * Executa apenas health checks críticos
 */
export async function checkCriticalServices(): Promise<HealthReport> {
  return await healthManager.checkCritical();
}

/**
 * Obtém lista de serviços disponíveis
 */
export function getAvailableServices(): string[] {
  return healthManager.getAvailableServices();
}

// ============================================================================
// UTILITÁRIOS PARA APIS
// ============================================================================

/**
 * Converte status de health para código HTTP
 */
export function healthStatusToHttpCode(status: HealthStatus): number {
  switch (status) {
    case 'healthy':
      return 200;
    case 'degraded':
      return 200; // Ainda funcional, mas com avisos
    case 'unhealthy':
      return 503; // Service Unavailable
    case 'unknown':
      return 500; // Internal Server Error
    default:
      return 500;
  }
}

/**
 * Formata resposta para API
 */
export function formatHealthResponse(
  result: HealthCheckResult | HealthReport,
  includeDetails = true
) {
  const baseResponse = {
    status: 'overall' in result ? result.overall : (result as HealthCheckResult).status,
    timestamp: result.timestamp,
    responseTime: result.responseTime,
  };

  if ('services' in result) {
    // É um HealthReport
    return {
      ...baseResponse,
      summary: result.summary,
      services: includeDetails
        ? result.services
        : result.services.map(s => ({
            service: s.service,
            status: s.status,
            responseTime: s.responseTime,
          })),
    };
  } else {
    // É um HealthCheckResult
    return {
      ...baseResponse,
      service: (result as HealthCheckResult).service,
      details: includeDetails ? (result as HealthCheckResult).details : undefined,
    };
  }
}

// ============================================================================
// EXPORTS
// ============================================================================

export * from '../health-checks';
export * from './database';
export * from './auth';
export * from './ai';
export * from './stripe';
export * from './mcp';
