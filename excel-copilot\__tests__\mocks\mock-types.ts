/**
 * Tipos para o sistema de mocks
 * Este arquivo define interfaces que são usadas em testes
 */

// Importar apenas tipos do msw-mock
import type { <PERSON>ck<PERSON><PERSON><PERSON> as MSWMockHandler, MockResponse as MSWMockResponse } from './msw-mock';

// Re-exportar tipos do MSW
export type { MSW<PERSON><PERSON><PERSON><PERSON><PERSON>, MSWMockResponse };

/**
 * Tipo para o objeto de requisição mock
 */
export interface MockRequest {
  method: string;
  url: string;
  body?: any;
  path?: string;
  params?: Record<string, string>;
  headers: Record<string, string>;
  query?: Record<string, string>;
}

/**
 * Tipo para o objeto de resposta mock
 */
export interface MockResponseObject {
  status: (code: number) => {
    json: (data: any) => any;
    text: (text: string) => any;
  };
  json?: (data: any) => any;
  text?: (text: string) => any;
  headers?: (headers: Record<string, string>) => any;
}

/**
 * Tipo para o context do mock server
 */
export interface MockContext {
  status: (code: number) => {
    json: (data: any) => any;
    text: (text: string) => any;
  };
}

/**
 * Tipo para os handlers
 */
export interface MockHandler {
  path: string;
  method: string;
  callback: (req: MockRequest, res: MockResponseObject, ctx: MockContext) => any;
}

/**
 * Tipo para o corpo de resposta
 */
export interface MockResponseBody {
  status: number;
  body: any;
}

/**
 * Tipo para endpoints da API
 */
export interface ApiEndpoint {
  path: string;
  method: string;
  status: number;
  response: any;
}

/**
 * Tipo para o resultado de uma operação
 */
export interface ApiResult<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

/**
 * Util para criar mock handlers
 */
export const createMockHandler = (
  method: string,
  path: string,
  handler: (req: MockRequest, res: MockResponseObject, ctx: MockContext) => any
): MockHandler => {
  return {
    path,
    method: method.toLowerCase(),
    callback: handler,
  };
};
