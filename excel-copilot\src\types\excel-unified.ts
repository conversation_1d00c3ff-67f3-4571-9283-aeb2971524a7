/**
 * Tipos unificados para Excel Copilot
 * Este arquivo centraliza todas as definições de tipos relacionadas ao Excel
 * para evitar duplicações e inconsistências
 */

// ===== TIPOS BÁSICOS =====

/**
 * Valores que podem aparecer em células do Excel
 */
export type CellValue = string | number | boolean | Date | null;

/**
 * Tipos de gráficos suportados
 */
export type ChartType =
  | 'line'
  | 'bar'
  | 'column'
  | 'area'
  | 'scatter'
  | 'pie'
  | 'donut'
  | 'radar'
  | 'heatmap'
  | 'treemap'
  | 'bubble'
  | 'candlestick'
  | 'waterfall'
  | 'funnel'
  | 'gauge'
  | 'boxplot'
  | 'sankey'
  | 'pareto';

/**
 * Operadores de filtro disponíveis
 */
export type FilterOperator =
  | '='
  | '<>'
  | '<'
  | '<='
  | '>'
  | '>='
  | 'contains'
  | 'notContains'
  | 'startsWith'
  | 'endsWith'
  | 'between'
  | 'notBetween'
  | 'isEmpty'
  | 'isNotEmpty';

// ===== INTERFACES PRINCIPAIS =====

/**
 * Estrutura principal de dados da planilha
 * Interface unificada para todos os componentes
 */
export interface SpreadsheetData {
  /** Cabeçalhos das colunas */
  headers: string[];
  /** Linhas de dados */
  rows: CellValue[][];
  /** Gráficos associados */
  charts?: ChartData[];
  /** Nome da planilha */
  name: string;
  /** ID único (opcional) */
  id?: string;
  /** Metadados adicionais */
  metadata?: SpreadsheetMetadata;
}

/**
 * Metadados da planilha
 */
export interface SpreadsheetMetadata {
  /** Data de criação */
  createdAt?: Date;
  /** Data de última modificação */
  updatedAt?: Date;
  /** Autor/criador */
  author?: string;
  /** Descrição */
  description?: string;
  /** Tags/categorias */
  tags?: string[];
  /** Se está filtrada */
  filtered?: boolean;
  /** Critérios de filtro aplicados */
  filterCriteria?: FilterCriteria;
  /** Se é uma tabela formatada */
  isTable?: boolean;
  /** Informações de formatação */
  formatting?: Record<string, unknown>;
}

/**
 * Dados de gráfico
 */
export interface ChartData {
  /** Tipo do gráfico */
  type: ChartType;
  /** Dados do gráfico */
  data: ChartDataset[];
  /** Opções de configuração */
  options?: ChartOptions;
  /** Título do gráfico */
  title?: string;
  /** ID único do gráfico */
  id?: string;
}

/**
 * Dataset de dados para gráficos
 */
export interface ChartDataset {
  /** Rótulo do dataset */
  label: string;
  /** Dados do dataset */
  data: (number | null)[];
  /** Cor de fundo */
  backgroundColor?: string | string[];
  /** Cor da borda */
  borderColor?: string | string[];
  /** Largura da borda */
  borderWidth?: number;
  /** Outras propriedades específicas do tipo de gráfico */
  [key: string]: unknown;
}

/**
 * Opções de configuração para gráficos
 */
export interface ChartOptions {
  /** Se o gráfico é responsivo */
  responsive?: boolean;
  /** Configurações dos eixos */
  scales?: {
    x?: AxisOptions;
    y?: AxisOptions;
  };
  /** Configurações da legenda */
  legend?: LegendOptions;
  /** Configurações do tooltip */
  tooltip?: TooltipOptions;
  /** Outras opções específicas */
  [key: string]: unknown;
}

/**
 * Opções de eixo
 */
export interface AxisOptions {
  /** Título do eixo */
  title?: {
    display?: boolean;
    text?: string;
  };
  /** Se deve mostrar a grade */
  grid?: {
    display?: boolean;
  };
  /** Outras configurações */
  [key: string]: unknown;
}

/**
 * Opções de legenda
 */
export interface LegendOptions {
  /** Se deve mostrar a legenda */
  display?: boolean;
  /** Posição da legenda */
  position?: 'top' | 'bottom' | 'left' | 'right';
  /** Outras configurações */
  [key: string]: unknown;
}

/**
 * Opções de tooltip
 */
export interface TooltipOptions {
  /** Se deve mostrar tooltips */
  enabled?: boolean;
  /** Modo de exibição */
  mode?: 'point' | 'nearest' | 'index' | 'dataset';
  /** Outras configurações */
  [key: string]: unknown;
}

/**
 * Critérios de filtro
 */
export interface FilterCriteria {
  /** Coluna a ser filtrada */
  column: string;
  /** Operador de filtro */
  operator: FilterOperator;
  /** Valor(es) para comparação */
  value: CellValue | CellValue[];
  /** Se o filtro está ativo */
  active?: boolean;
}

/**
 * Informações de tabela formatada
 */
export interface TableInfo {
  /** Nome da tabela */
  name: string;
  /** Intervalo da tabela (ex: A1:D10) */
  range: string;
  /** Se tem cabeçalhos */
  hasHeaders: boolean;
  /** Estilo da tabela */
  style?: string;
}

/**
 * Dados de tabela dinâmica
 */
export interface PivotTableData {
  /** Cabeçalhos da tabela dinâmica */
  headers: string[];
  /** Linhas de dados */
  rows: CellValue[][];
  /** Campo usado para linhas */
  rowsField: string;
  /** Campo usado para colunas */
  columnsField: string;
  /** Campo usado para valores */
  valuesField: string;
  /** Função de agregação */
  aggregationFunction?: 'sum' | 'average' | 'count' | 'min' | 'max';
}

/**
 * Planilha com dados completos
 */
export interface SheetWithData {
  /** ID único da planilha */
  id?: string;
  /** Nome da planilha */
  name: string;
  /** Dados da planilha */
  data: SpreadsheetData;
  /** Data de criação */
  createdAt?: Date;
  /** Data de última atualização */
  updatedAt?: Date;
}

// ===== TIPOS PARA OPERAÇÕES =====

/**
 * Tipos de operação Excel suportados
 */
export enum ExcelOperationType {
  CREATE_SHEET = 'CREATE_SHEET',
  UPDATE_CELL = 'UPDATE_CELL',
  INSERT_ROW = 'INSERT_ROW',
  DELETE_ROW = 'DELETE_ROW',
  INSERT_COLUMN = 'INSERT_COLUMN',
  DELETE_COLUMN = 'DELETE_COLUMN',
  APPLY_FORMULA = 'APPLY_FORMULA',
  CREATE_CHART = 'CREATE_CHART',
  FILTER_DATA = 'FILTER_DATA',
  SORT_DATA = 'SORT_DATA',
  CREATE_PIVOT_TABLE = 'CREATE_PIVOT_TABLE',
  FORMAT_CELLS = 'FORMAT_CELLS',
  MERGE_CELLS = 'MERGE_CELLS',
  UNMERGE_CELLS = 'UNMERGE_CELLS',
}

/**
 * Operação Excel genérica
 */
export interface ExcelOperation<T = unknown> {
  /** Tipo da operação */
  type: ExcelOperationType;
  /** Dados específicos da operação */
  data: T;
  /** ID da planilha alvo */
  sheetId?: string;
  /** Metadados da operação */
  metadata?: {
    timestamp?: Date;
    user?: string;
    description?: string;
  };
}

// ===== EXPORTS PARA COMPATIBILIDADE =====

/**
 * @deprecated Use SpreadsheetData instead
 */
export type LegacySpreadsheetData = SpreadsheetData;

/**
 * @deprecated Use CellValue instead
 */
export type LegacyCellValue = CellValue;

/**
 * @deprecated Use ChartData instead
 */
export type LegacyChartData = ChartData;
