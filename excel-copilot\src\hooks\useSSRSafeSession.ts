'use client';

import { useSession as useNextAuthSession } from 'next-auth/react';
import { useEffect, useState } from 'react';

/**
 * Hook que protege useSession contra problemas de SSR
 * Retorna null durante o prerendering e dados reais no cliente
 */
export function useSSRSafeSession() {
  const [isClient, setIsClient] = useState(false);
  const session = useNextAuthSession();

  useEffect(() => {
    setIsClient(true);
  }, []);

  // Durante SSR ou antes da hidratação, retornar estado seguro
  if (!isClient) {
    return {
      data: null,
      status: 'loading' as const,
      update: async () => null,
    };
  }

  // No cliente, retornar dados reais
  return session;
}
