// Mock para next/server

// Mock básico para Request se não estiver disponível
const MockRequest =
  global.Request ||
  class MockRequest {
    constructor(input, init) {
      this.url = input || 'http://localhost';
      this.method = init?.method || 'GET';
      this.headers = new Map(Object.entries(init?.headers || {}));
      this.body = init?.body || null;
    }
  };

class NextRequest extends MockRequest {
  constructor(input, init) {
    super(input || 'http://localhost', init);
    this.cookies = {
      get: jest.fn(),
      getAll: jest.fn(),
      set: jest.fn(),
      delete: jest.fn(),
      has: jest.fn(),
      clear: jest.fn(),
    };
    this.nextUrl = new URL(input || 'http://localhost');
  }
}

// Mock básico para Response se não estiver disponível
const MockResponse =
  global.Response ||
  class MockResponse {
    constructor(body, init) {
      this.body = body;
      this.status = init?.status || 200;
      this.statusText = init?.statusText || 'OK';
      this.headers = new Map(Object.entries(init?.headers || {}));
    }
  };

class NextResponse extends MockResponse {
  constructor(body, init) {
    super(body, init);
    this.cookies = {
      get: jest.fn(),
      getAll: jest.fn(),
      set: jest.fn(),
      delete: jest.fn(),
    };
  }

  static json(body, init) {
    return new NextResponse(JSON.stringify(body), {
      ...init,
      headers: {
        ...init?.headers,
        'content-type': 'application/json',
      },
    });
  }

  static redirect(url, init) {
    return new NextResponse(null, {
      status: 307,
      headers: { Location: url },
      ...init,
    });
  }

  static rewrite(destination) {
    return new NextResponse(null, {
      headers: { 'x-middleware-rewrite': destination },
    });
  }

  static next(init) {
    return new NextResponse(null, init);
  }
}

// Mock de outras funções/classes do next/server
const userAgent = jest.fn().mockReturnValue({
  isBot: false,
  browser: { name: 'chrome', version: '90' },
  os: { name: 'windows', version: '10' },
  device: { type: 'desktop' },
});

module.exports = {
  NextRequest,
  NextResponse,
  userAgent,
};
