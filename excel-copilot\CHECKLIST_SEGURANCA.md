# Checklist de Segurança para Lançamento em Produção

Este documento fornece uma lista detalhada de verificações de segurança que devem ser realizadas antes de lançar o Excel Copilot em produção.

## Configuração de Ambiente

- [ ] **Variáveis de ambiente**

  - [ ] Remover todas as chaves de teste e desenvolvimento
  - [ ] Configurar NEXTAUTH_SECRET com valor forte e aleatório
  - [ ] Configurar DATABASE_URL com credenciais seguras
  - [ ] Verificar se todas as chaves e segredos estão no arquivo .env.production e não no código-fonte

- [ ] **Configurações de segurança**
  - [ ] Desativar USE_MOCK_AI em produção
  - [ ] Desativar SKIP_AUTH_PROVIDERS em produção
  - [ ] Configurar limites de rate adequados (API_RATE_LIMIT, AI_RATE_LIMIT)
  - [ ] Configurar timeouts adequados (API_TIMEOUT, AI_TIMEOUT, EXCEL_TIMEOUT)

## Autenticação e Autorização

- [ ] **Provedores OAuth**

  - [ ] Obter e configurar chaves de produção para OAuth do Google
  - [ ] Obter e configurar chaves de produção para OAuth do GitHub
  - [ ] Verificar se os redirecionamentos estão configurados corretamente

- [ ] **Proteção de endpoints**

  - [ ] Verificar se todos os endpoints da API estão protegidos com autenticação
  - [ ] Verificar se a autorização (verificação de permissão) está implementada em todas as rotas
  - [ ] Confirmar que não existem endpoints sensíveis sem proteção

- [ ] **CSRF Protection**
  - [ ] Verificar se a proteção CSRF está implementada em todos os formulários
  - [ ] Verificar se os tokens CSRF estão sendo validados corretamente
  - [ ] Confirmar que ações sensíveis exigem validação CSRF

## Sanitização e Validação

- [ ] **Validação de entrada**

  - [ ] Verificar se todas as entradas de usuário são validadas com esquemas Zod
  - [ ] Confirmar que validações específicas (como email, URL) estão implementadas
  - [ ] Verificar limites adequados para tamanhos de entrada

- [ ] **Sanitização**

  - [ ] Confirmar que a sanitização HTML está sendo aplicada em todas as entradas
  - [ ] Verificar se as fórmulas Excel estão sendo validadas antes de execução
  - [ ] Confirmar que dados de API externos são sanitizados

- [ ] **Proteção contra injeção**
  - [ ] Verificar proteção contra SQL Injection (uso do Prisma ajuda, mas verificar queries personalizadas)
  - [ ] Verificar proteção contra XSS em saídas HTML
  - [ ] Verificar proteção contra XML/JSON injection

## Servidor e Infraestrutura

- [ ] **Serviços e portas**

  - [ ] Verificar se apenas as portas necessárias estão abertas (80, 443, 3001)
  - [ ] Confirmar que serviços internos não estão expostos publicamente
  - [ ] Implementar firewall adequado

- [ ] **HTTPS**

  - [ ] Obter e configurar certificado SSL válido
  - [ ] Configurar redirecionamento HTTP para HTTPS
  - [ ] Implementar HSTS (HTTP Strict Transport Security)
  - [ ] Verificar configuração de TLS (mínimo TLS 1.2)

- [ ] **Headers de segurança**
  - [ ] Configurar Content-Security-Policy
  - [ ] Configurar X-Frame-Options
  - [ ] Configurar X-Content-Type-Options
  - [ ] Configurar Referrer-Policy

## Monitoramento e Logging

- [ ] **Logs de segurança**

  - [ ] Confirmar que eventos de segurança estão sendo registrados
  - [ ] Verificar se tentativas de login são registradas
  - [ ] Implementar alerta para falhas repetidas de autenticação

- [ ] **Monitoramento**
  - [ ] Configurar monitoramento de disponibilidade
  - [ ] Configurar alertas para atividades suspeitas
  - [ ] Implementar monitoramento de performance e disponibilidade

## Pagamentos (Stripe)

- [ ] **Configuração do Stripe**

  - [ ] Obter e configurar chaves de produção do Stripe
  - [ ] Configurar webhook com chave de assinatura adequada
  - [ ] Verificar se os IDs de preço estão corretos

- [ ] **Segurança de pagamentos**
  - [ ] Confirmar que dados de cartão nunca são armazenados
  - [ ] Verificar se a comunicação com Stripe está usando HTTPS
  - [ ] Testar fluxo completo de pagamento em ambiente de teste

## Backup e Recuperação

- [ ] **Backup**

  - [ ] Configurar backup automático do banco de dados
  - [ ] Testar restauração de backup
  - [ ] Garantir que backups são armazenados de forma segura

- [ ] **Plano de recuperação**
  - [ ] Documentar procedimentos para recuperação de desastres
  - [ ] Definir RPO (Recovery Point Objective) e RTO (Recovery Time Objective)
  - [ ] Testar plano de recuperação

## Verificação Final

- [ ] **Testes de penetração**

  - [ ] Realizar verificação automatizada de vulnerabilidades
  - [ ] Testar manualmente pontos de entrada críticos
  - [ ] Verificar se não há credenciais ou segredos expostos

- [ ] **Revisão de código**

  - [ ] Verificar se não há hardcoding de credenciais
  - [ ] Verificar tratamento adequado de erros
  - [ ] Confirmar que não há logs de informações sensíveis

- [ ] **Documentação**
  - [ ] Documentar todas as configurações de segurança
  - [ ] Criar procedimentos para resposta a incidentes
  - [ ] Estabelecer canais de relatório para vulnerabilidades (security.txt)

## Aprovação para Lançamento

- [ ] Todas as verificações críticas foram concluídas
- [ ] Todos os problemas de segurança identificados foram corrigidos
- [ ] A equipe de segurança aprovou o lançamento

**Data da última verificação:** **\*\***\_\_\_**\*\***

**Responsável:** **\*\***\_\_\_**\*\***

**Assinatura:** **\*\***\_\_\_**\*\***
