import { NextRequest } from 'next/server';

import { logger } from '@/lib/logger';
import { StripeClient } from '@/lib/stripe-integration';
import { ApiResponse } from '@/utils/api-response';

// Configurar rota como dinâmica
export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

/**
 * GET /api/stripe/payments
 * Lista pagamentos do Stripe com filtros opcionais
 */
export async function GET(request: NextRequest) {
  try {
    const apiKey = process.env.STRIPE_SECRET_KEY;

    if (!apiKey) {
      return ApiResponse.error('STRIPE_SECRET_KEY não configurado', 'STRIPE_NOT_CONFIGURED', 500);
    }

    // Obter parâmetros de query
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '50');
    const customer = searchParams.get('customer') || undefined;
    const createdAfter = searchParams.get('created_after');
    const createdBefore = searchParams.get('created_before');

    // Validar parâmetros
    if (limit > 100) {
      return ApiResponse.error('Limite máximo é 100 pagamentos', 'INVALID_LIMIT', 400);
    }

    // Preparar filtros de data
    let created: { gte?: number; lte?: number } | undefined;
    if (createdAfter || createdBefore) {
      created = {};
      if (createdAfter) {
        const timestamp = parseInt(createdAfter);
        if (isNaN(timestamp)) {
          return ApiResponse.error(
            'created_after deve ser um timestamp Unix válido',
            'INVALID_TIMESTAMP',
            400
          );
        }
        created.gte = timestamp;
      }
      if (createdBefore) {
        const timestamp = parseInt(createdBefore);
        if (isNaN(timestamp)) {
          return ApiResponse.error(
            'created_before deve ser um timestamp Unix válido',
            'INVALID_TIMESTAMP',
            400
          );
        }
        created.lte = timestamp;
      }
    }

    // Criar cliente Stripe
    const stripeClient = new StripeClient({ apiKey });

    // Obter pagamentos
    const paymentParams: {
      customer?: string;
      limit: number;
      created?: { gte?: number; lte?: number };
    } = {
      limit,
    };

    if (customer) {
      paymentParams.customer = customer;
    }

    if (created) {
      paymentParams.created = created;
    }

    const result = await stripeClient.getPayments(paymentParams);

    // Preparar resposta
    const response = {
      payments: result.payments.map(payment => ({
        id: payment.id,
        amount: payment.amount / 100, // Converter de centavos
        currency: payment.currency,
        status: payment.status,
        created: new Date(payment.created * 1000).toISOString(),
        customer: payment.customer,
        description: payment.description,
        paymentMethod: payment.paymentMethod
          ? {
              type: payment.paymentMethod.type,
              card: payment.paymentMethod.card
                ? {
                    brand: payment.paymentMethod.card.brand,
                    last4: payment.paymentMethod.card.last4,
                    country: payment.paymentMethod.card.country,
                  }
                : undefined,
            }
          : undefined,
        metadata: payment.metadata,
      })),
      summary: {
        total: result.payments.length,
        totalAmount: result.payments.reduce((sum, p) => sum + p.amount, 0) / 100,
        byStatus: result.payments.reduce(
          (acc, payment) => {
            acc[payment.status] = (acc[payment.status] || 0) + 1;
            return acc;
          },
          {} as Record<string, number>
        ),
        byPaymentMethod: result.payments.reduce(
          (acc, payment) => {
            const method = payment.paymentMethod?.type || 'unknown';
            acc[method] = (acc[method] || 0) + 1;
            return acc;
          },
          {} as Record<string, number>
        ),
        successRate:
          result.payments.length > 0
            ? Math.round(
                (result.payments.filter(p => p.status === 'succeeded').length /
                  result.payments.length) *
                  100 *
                  100
              ) / 100
            : 0,
      },
      pagination: {
        limit,
        count: result.payments.length,
        hasMore: result.payments.length === limit,
      },
      filters: {
        customer,
        createdAfter: createdAfter
          ? new Date(parseInt(createdAfter) * 1000).toISOString()
          : undefined,
        createdBefore: createdBefore
          ? new Date(parseInt(createdBefore) * 1000).toISOString()
          : undefined,
      },
      timestamp: new Date().toISOString(),
    };

    logger.info('Pagamentos Stripe obtidos com sucesso', {
      count: result.payments.length,
      totalAmount: response.summary.totalAmount,
      filters: { customer, createdAfter, createdBefore },
    });

    return ApiResponse.success(response);
  } catch (error) {
    logger.error('Erro ao obter pagamentos Stripe', { error });

    if (error instanceof Error) {
      return ApiResponse.error(
        `Erro ao buscar pagamentos: ${error.message}`,
        'STRIPE_API_ERROR',
        500
      );
    }

    return ApiResponse.error('Erro interno do servidor', 'INTERNAL_ERROR', 500);
  }
}

/**
 * POST /api/stripe/payments
 * Análise avançada de pagamentos
 */
export async function POST(request: NextRequest) {
  try {
    const apiKey = process.env.STRIPE_SECRET_KEY;

    if (!apiKey) {
      return ApiResponse.error('STRIPE_SECRET_KEY não configurado', 'STRIPE_NOT_CONFIGURED', 500);
    }

    // Obter dados do corpo da requisição
    const body = await request.json();
    const {
      period = '30d',
      includeFailureAnalysis = false,
      includeGeography = false,
      includeTrends = false,
    } = body;

    const stripeClient = new StripeClient({ apiKey });

    // Calcular período
    const periodDays = period === '7d' ? 7 : period === '30d' ? 30 : 90;
    const periodStart = Math.floor(Date.now() / 1000) - periodDays * 24 * 60 * 60;

    // Obter pagamentos do período
    const result = await stripeClient.getPayments({
      created: { gte: periodStart },
      limit: 100,
    });

    // Análise básica
    const successfulPayments = result.payments.filter(p => p.status === 'succeeded');
    const failedPayments = result.payments.filter(p => p.status === 'failed');
    const refundedPayments = result.payments.filter(
      p => p.status === 'succeeded' && p.metadata.refunded === 'true'
    );

    const totalAmount = successfulPayments.reduce((sum, p) => sum + p.amount, 0) / 100;
    const averageAmount =
      successfulPayments.length > 0 ? totalAmount / successfulPayments.length : 0;

    const response = {
      summary: {
        period,
        totalPayments: result.payments.length,
        successfulPayments: successfulPayments.length,
        failedPayments: failedPayments.length,
        refundedPayments: refundedPayments.length,
        totalAmount: Math.round(totalAmount * 100) / 100,
        averageAmount: Math.round(averageAmount * 100) / 100,
        successRate:
          result.payments.length > 0
            ? Math.round((successfulPayments.length / result.payments.length) * 100 * 100) / 100
            : 0,
      },
      breakdown: {
        byStatus: result.payments.reduce(
          (acc, payment) => {
            acc[payment.status] = (acc[payment.status] || 0) + 1;
            return acc;
          },
          {} as Record<string, number>
        ),
        byPaymentMethod: result.payments.reduce(
          (acc, payment) => {
            const method = payment.paymentMethod?.type || 'unknown';
            if (!acc[method]) {
              acc[method] = { count: 0, amount: 0, successRate: 0 };
            }
            acc[method].count += 1;
            if (payment.status === 'succeeded') {
              acc[method].amount += payment.amount / 100;
            }
            return acc;
          },
          {} as Record<string, { count: number; amount: number; successRate: number }>
        ),
        byCurrency: result.payments.reduce(
          (acc, payment) => {
            if (!acc[payment.currency]) {
              acc[payment.currency] = { count: 0, amount: 0 };
            }
            const currencyData = acc[payment.currency];
            if (currencyData) {
              currencyData.count += 1;
              if (payment.status === 'succeeded') {
                currencyData.amount += payment.amount / 100;
              }
            }
            return acc;
          },
          {} as Record<string, { count: number; amount: number }>
        ),
      },
      timestamp: new Date().toISOString(),
    } as {
      summary: Record<string, unknown>;
      breakdown: {
        byStatus: Record<string, number>;
        byPaymentMethod: Record<string, { count: number; amount: number; successRate: number }>;
        byCurrency: Record<string, { count: number; amount: number }>;
      };
      timestamp: string;
      failureAnalysis?: unknown;
      geography?: unknown;
      trends?: unknown;
    };

    // Calcular taxa de sucesso por método de pagamento
    Object.keys(response.breakdown.byPaymentMethod).forEach(method => {
      const methodData = response.breakdown.byPaymentMethod[method];
      const methodPayments = result.payments.filter(
        p => (p.paymentMethod?.type || 'unknown') === method
      );
      const methodSuccessful = methodPayments.filter(p => p.status === 'succeeded').length;
      if (methodData) {
        methodData.successRate =
          methodPayments.length > 0
            ? Math.round((methodSuccessful / methodPayments.length) * 100 * 100) / 100
            : 0;
      }
    });

    // Análise de falhas se solicitada
    if (includeFailureAnalysis) {
      const failureReasons = failedPayments.reduce(
        (acc, _payment) => {
          // Simular razões de falha (seria extraído dos dados reais)
          const reasons = [
            'insufficient_funds',
            'card_declined',
            'expired_card',
            'processing_error',
          ];
          const reason = reasons[Math.floor(Math.random() * reasons.length)];
          if (reason) {
            acc[reason] = (acc[reason] || 0) + 1;
          }
          return acc;
        },
        {} as Record<string, number>
      );

      response.failureAnalysis = {
        totalFailures: failedPayments.length,
        failureRate:
          result.payments.length > 0
            ? Math.round((failedPayments.length / result.payments.length) * 100 * 100) / 100
            : 0,
        reasonBreakdown: failureReasons,
        recommendations: [
          'Implementar retry automático para falhas temporárias',
          'Melhorar validação de cartões antes do processamento',
          'Adicionar métodos de pagamento alternativos',
          'Implementar notificações proativas para cartões expirados',
        ],
      };
    }

    // Análise geográfica se solicitada
    if (includeGeography) {
      const geography = result.payments.reduce(
        (acc, payment) => {
          const country = payment.paymentMethod?.card?.country || 'unknown';
          if (!acc[country]) {
            acc[country] = { count: 0, amount: 0, successRate: 0 };
          }
          acc[country].count += 1;
          if (payment.status === 'succeeded') {
            acc[country].amount += payment.amount / 100;
          }
          return acc;
        },
        {} as Record<string, { count: number; amount: number; successRate: number }>
      );

      // Calcular taxa de sucesso por país
      Object.keys(geography).forEach(country => {
        const countryData = geography[country];
        if (countryData) {
          const countryPayments = result.payments.filter(
            p => (p.paymentMethod?.card?.country || 'unknown') === country
          );
          const countrySuccessful = countryPayments.filter(p => p.status === 'succeeded').length;
          countryData.successRate =
            countryPayments.length > 0
              ? Math.round((countrySuccessful / countryPayments.length) * 100 * 100) / 100
              : 0;
        }
      });

      response.geography = geography;
    }

    // Análise de tendências se solicitada
    if (includeTrends) {
      const dailyTrends = Array.from({ length: periodDays }, (_, i) => {
        const date = new Date();
        date.setDate(date.getDate() - (periodDays - 1 - i));
        const dayStart = Math.floor(date.getTime() / 1000);
        const dayEnd = dayStart + 24 * 60 * 60;

        const dayPayments = result.payments.filter(
          p => p.created >= dayStart && p.created < dayEnd
        );

        const daySuccessful = dayPayments.filter(p => p.status === 'succeeded');
        const dayAmount = daySuccessful.reduce((sum, p) => sum + p.amount, 0) / 100;

        return {
          date: date.toISOString().split('T')[0],
          payments: dayPayments.length,
          successful: daySuccessful.length,
          amount: Math.round(dayAmount * 100) / 100,
          successRate:
            dayPayments.length > 0
              ? Math.round((daySuccessful.length / dayPayments.length) * 100 * 100) / 100
              : 0,
        };
      });

      response.trends = {
        daily: dailyTrends,
        growth: {
          paymentsGrowth: 0, // Seria calculado comparando com período anterior
          amountGrowth: 0,
          successRateChange: 0,
        },
      };
    }

    logger.info('Análise avançada de pagamentos realizada', {
      count: result.payments.length,
      period,
      includeFailureAnalysis,
      includeGeography,
      includeTrends,
    });

    return ApiResponse.success(response);
  } catch (error) {
    logger.error('Erro na análise de pagamentos', { error });

    if (error instanceof Error) {
      return ApiResponse.error(`Erro na análise: ${error.message}`, 'STRIPE_ANALYSIS_ERROR', 500);
    }

    return ApiResponse.error('Erro interno do servidor', 'INTERNAL_ERROR', 500);
  }
}
