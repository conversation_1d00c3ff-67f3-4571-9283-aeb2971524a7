import { test, expect } from '@playwright/test';

test.describe('Interações com Gráficos', () => {
  // Configuração para autenticar e preparar uma planilha com dados
  test.beforeEach(async ({ page }) => {
    // Simular autenticação
    await page.context().addCookies([
      {
        name: 'next-auth.session-token',
        value: 'mock-session-token',
        domain: 'localhost',
        path: '/',
        httpOnly: true,
        secure: false,
      },
    ]);

    // Configurar dados do usuário
    await page.evaluate(() => {
      localStorage.setItem(
        'user',
        JSON.stringify({
          id: 'test-user-id',
          name: 'Test User',
          email: '<EMAIL>',
        })
      );
    });

    // Navegar para o dashboard
    await page.goto('/dashboard');

    // Criar uma nova planilha para testes
    await page.getByText('Criar Nova Planilha').click();
    await page.getByLabel('Nome da Planilha').fill('Planilha de Gráficos E2E');
    await page.getByRole('button', { name: /Criar/i }).click();

    // Verificar redirecionamento para a planilha
    await expect(page.url()).toContain('/workbook/');

    // Preencher a planilha com dados de exemplo para gráficos
    await page.locator('.excel-grid .cell').first().dblclick();
    await page.keyboard.type('Produto');
    await page.keyboard.press('Tab');
    await page.keyboard.type('Vendas');
    await page.keyboard.press('Tab');
    await page.keyboard.type('Custo');
    await page.keyboard.press('Enter');

    // Linha 2
    await page.keyboard.type('A');
    await page.keyboard.press('Tab');
    await page.keyboard.type('100');
    await page.keyboard.press('Tab');
    await page.keyboard.type('80');
    await page.keyboard.press('Enter');

    // Linha 3
    await page.keyboard.type('B');
    await page.keyboard.press('Tab');
    await page.keyboard.type('150');
    await page.keyboard.press('Tab');
    await page.keyboard.type('90');
    await page.keyboard.press('Enter');

    // Linha 4
    await page.keyboard.type('C');
    await page.keyboard.press('Tab');
    await page.keyboard.type('80');
    await page.keyboard.press('Tab');
    await page.keyboard.type('70');
    await page.keyboard.press('Enter');

    // Linha 5
    await page.keyboard.type('D');
    await page.keyboard.press('Tab');
    await page.keyboard.type('120');
    await page.keyboard.press('Tab');
    await page.keyboard.type('95');
    await page.keyboard.press('Enter');
  });

  test('deve criar um gráfico de barras via comando de chat', async ({ page }) => {
    // Navegar para a aba de chat
    await page.getByRole('tab', { name: 'Chat' }).click();

    // Enviar comando para criar gráfico de barras
    await page
      .getByPlaceholder('Digite sua mensagem...')
      .fill('Crie um gráfico de barras com os produtos na coluna A e vendas na coluna B');
    await page.getByRole('button', { name: 'Enviar' }).click();

    // Verificar resposta do assistente
    await expect(page.getByText(/Gráfico criado/i)).toBeVisible({ timeout: 10000 });

    // Verificar se o gráfico é exibido
    await expect(page.locator('.chart-container canvas')).toBeVisible();

    // Verificar se o gráfico possui elementos visuais corretos
    const chartElement = page.locator('.chart-container canvas');
    expect(await chartElement.screenshot()).not.toBeNull();
  });

  test('deve criar um gráfico de linha via comando de chat', async ({ page }) => {
    // Navegar para a aba de chat
    await page.getByRole('tab', { name: 'Chat' }).click();

    // Enviar comando para criar gráfico de linha
    await page
      .getByPlaceholder('Digite sua mensagem...')
      .fill('Crie um gráfico de linha com os produtos na coluna A e vendas na coluna B');
    await page.getByRole('button', { name: 'Enviar' }).click();

    // Verificar resposta do assistente
    await expect(page.getByText(/Gráfico criado/i)).toBeVisible({ timeout: 10000 });

    // Verificar se o gráfico é exibido
    await expect(page.locator('.chart-container canvas')).toBeVisible();
  });

  test('deve criar um gráfico de pizza via comando de chat', async ({ page }) => {
    // Navegar para a aba de chat
    await page.getByRole('tab', { name: 'Chat' }).click();

    // Enviar comando para criar gráfico de pizza
    await page
      .getByPlaceholder('Digite sua mensagem...')
      .fill('Crie um gráfico de pizza com os produtos na coluna A e vendas na coluna B');
    await page.getByRole('button', { name: 'Enviar' }).click();

    // Verificar resposta do assistente
    await expect(page.getByText(/Gráfico criado/i)).toBeVisible({ timeout: 10000 });

    // Verificar se o gráfico é exibido
    await expect(page.locator('.chart-container canvas')).toBeVisible();
  });

  test('deve modificar um gráfico existente via comando de chat', async ({ page }) => {
    // Primeiro criar um gráfico
    await page.getByRole('tab', { name: 'Chat' }).click();
    await page
      .getByPlaceholder('Digite sua mensagem...')
      .fill('Crie um gráfico de barras com os produtos na coluna A e vendas na coluna B');
    await page.getByRole('button', { name: 'Enviar' }).click();

    // Verificar que o gráfico foi criado
    await expect(page.locator('.chart-container canvas')).toBeVisible();

    // Modificar o gráfico existente
    await page
      .getByPlaceholder('Digite sua mensagem...')
      .fill('Altere o gráfico para mostrar os custos da coluna C em vez das vendas');
    await page.getByRole('button', { name: 'Enviar' }).click();

    // Verificar resposta do assistente
    await expect(page.getByText(/Gráfico atualizado/i)).toBeVisible({ timeout: 10000 });

    // Verificar se o gráfico ainda está visível após a atualização
    await expect(page.locator('.chart-container canvas')).toBeVisible();
  });

  test('deve permitir exportar apenas o gráfico', async ({ page }) => {
    // Primeiro criar um gráfico
    await page.getByRole('tab', { name: 'Chat' }).click();
    await page
      .getByPlaceholder('Digite sua mensagem...')
      .fill('Crie um gráfico de barras com os produtos na coluna A e vendas na coluna B');
    await page.getByRole('button', { name: 'Enviar' }).click();

    // Verificar que o gráfico foi criado
    await expect(page.locator('.chart-container canvas')).toBeVisible();

    // Verificar se o menu de opções do gráfico está disponível
    await page.locator('.chart-container .chart-options-button').click();

    // Clicar na opção de download/exportar
    const downloadPromise = page.waitForEvent('download');
    await page.getByText('Exportar Gráfico').click();

    // Aguardar o download iniciar
    const download = await downloadPromise;

    // Verificar se o arquivo foi baixado com extensão de imagem
    expect(download.suggestedFilename()).toMatch(/\.(png|jpg|jpeg|svg)/i);
  });
});
