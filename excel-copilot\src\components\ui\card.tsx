'use client';

import { motion } from 'framer-motion';
import { HTMLAttributes, forwardRef } from 'react';

import { cardVariants, getAnimationProps } from '@/lib/animations';
import { cn } from '@/lib/utils';

// Componente base do Card
const Card = forwardRef<
  HTMLDivElement,
  HTMLAttributes<HTMLDivElement> & {
    hoverable?: boolean;
    variant?: 'default' | 'outline' | 'glass' | 'gradient';
    noPadding?: boolean;
    animated?: boolean;
  }
>(
  (
    {
      className,
      children,
      hoverable = false,
      variant = 'default',
      noPadding = false,
      animated = false,
      ...props
    },
    ref
  ) => {
    // Configurações de base para todos os cards
    const baseStyles = cn(
      'rounded-xl border shadow-sm',
      {
        'p-6': !noPadding,
        'hover:shadow-md hover:-translate-y-1 transition-all duration-200': hoverable && !animated,
        'border-border bg-card': variant === 'default',
        'border-border/50 bg-transparent': variant === 'outline',
        'bg-card/90 backdrop-blur-md border-border/50': variant === 'glass',
        'bg-gradient-primary text-primary-foreground border-none': variant === 'gradient',
      },
      className
    );

    if (animated) {
      return (
        <motion.div
          ref={ref}
          className={baseStyles}
          {...getAnimationProps('card')}
          whileHover={hoverable ? cardVariants.hover : undefined}
          whileTap={hoverable ? cardVariants.tap : undefined}
          {...(props as any)}
        >
          {children}
        </motion.div>
      );
    }

    return (
      <div ref={ref} className={baseStyles} {...props}>
        {children}
      </div>
    );
  }
);

Card.displayName = 'Card';

// Componente para o Cabeçalho do Card
const CardHeader = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(
  ({ className, ...props }, ref) => (
    <div ref={ref} className={cn('mb-4 flex flex-col space-y-1.5', className)} {...props} />
  )
);

CardHeader.displayName = 'CardHeader';

// Componente para o Título do Card
const CardTitle = forwardRef<HTMLHeadingElement, HTMLAttributes<HTMLHeadingElement>>(
  ({ className, ...props }, ref) => (
    <h3
      ref={ref}
      className={cn('text-xl font-semibold leading-none tracking-tight', className)}
      {...props}
    />
  )
);

CardTitle.displayName = 'CardTitle';

// Componente para a Descrição do Card
const CardDescription = forwardRef<HTMLParagraphElement, HTMLAttributes<HTMLParagraphElement>>(
  ({ className, ...props }, ref) => (
    <p ref={ref} className={cn('text-sm text-muted-foreground', className)} {...props} />
  )
);

CardDescription.displayName = 'CardDescription';

// Componente para o Conteúdo do Card
const CardContent = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(
  ({ className, ...props }, ref) => (
    <div ref={ref} className={cn('card-content', className)} {...props} />
  )
);

CardContent.displayName = 'CardContent';

// Componente para o Rodapé do Card
const CardFooter = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(
  ({ className, ...props }, ref) => (
    <div ref={ref} className={cn('flex items-center pt-4 mt-auto', className)} {...props} />
  )
);

CardFooter.displayName = 'CardFooter';

export { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter };
