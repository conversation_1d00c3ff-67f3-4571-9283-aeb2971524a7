/**
 * Utilitários para manipulação de JSON
 */
import { logger } from '@/lib/logger';
import { extractGroup } from '@/utils/regex-utils';

/**
 * Tenta parsear uma string JSON de forma segura.
 * Retorna undefined se ocorrer erro.
 *
 * @param jsonString String a ser convertida para objeto
 * @returns Objeto parseado ou undefined em caso de erro
 */
export function parseJsonSafely<T = any>(jsonString: string): T | undefined {
  if (!jsonString) return undefined;

  try {
    // Tentar extrair JSON da resposta se estiver em formato markdown
    let contentToparse = jsonString;

    // Verificar se está em bloco de código markdown
    const jsonMatch = jsonString.match(/```(?:json)?\s*([\s\S]*?)\s*```/);
    if (jsonMatch) {
      contentToparse = extractGroup(jsonMatch, 1);
    }

    // Verificar se é um objeto JSON completo
    if (!contentToparse.startsWith('{') && !contentToparse.startsWith('[')) {
      // Tentar encontrar um objeto JSON na string
      const objectMatch = jsonString.match(/(\{[\s\S]*\}|\[[\s\S]*\])/);
      if (objectMatch) {
        contentToparse = extractGroup(objectMatch, 1);
      }
    }

    return JSON.parse(contentToparse) as T;
  } catch (error) {
    logger.debug('Erro ao fazer parse de JSON:', {
      error,
      jsonString: jsonString.substring(0, 200) + (jsonString.length > 200 ? '...' : ''),
    });
    return undefined;
  }
}

/**
 * Converte um objeto para string JSON de forma segura,
 * tratando referências circulares
 *
 * @param obj Objeto a ser convertido para string JSON
 * @returns String JSON ou undefined em caso de erro
 */
export function stringifyJsonSafely(obj: any): string | undefined {
  if (obj === undefined || obj === null) return undefined;

  try {
    // Tratar referências circulares
    const cache = new Set();

    return JSON.stringify(
      obj,
      (key, value) => {
        if (typeof value === 'object' && value !== null) {
          if (cache.has(value)) {
            return '[Circular]';
          }
          cache.add(value);
        }
        return value;
      },
      2
    );
  } catch (error) {
    logger.debug('Erro ao converter objeto para JSON:', { error });
    return undefined;
  }
}
