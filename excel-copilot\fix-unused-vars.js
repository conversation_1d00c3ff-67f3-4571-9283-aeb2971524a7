#!/usr/bin/env node

/**
 * Script para corrigir variáveis não utilizadas em arquivos TypeScript
 *
 * Este script prefixará automaticamente variáveis não utilizadas com um underscore (_)
 * seguindo as recomendações do eslint para @typescript-eslint/no-unused-vars
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Cores para console
const colors = {
  reset: '\x1b[0m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  red: '\x1b[31m',
};

console.log(`${colors.blue}Iniciando correção de variáveis não utilizadas${colors.reset}`);

// Obter lista de arquivos com warnings de variáveis não utilizadas
function getFilesWithUnusedVars() {
  console.log(
    `${colors.yellow}Executando ESLint para encontrar arquivos com variáveis não utilizadas...${colors.reset}`
  );

  try {
    // Execute ESLint e obtenha a saída como JSON
    const output = execSync(
      'npx eslint --rule "@typescript-eslint/no-unused-vars: warn" --rule="@typescript-eslint/no-unused-vars: [warn, { argsIgnorePattern: ^_, varsIgnorePattern: ^_ }]" --format json "src/**/*.{ts,tsx}"',
      { encoding: 'utf8' }
    );

    // Parse a saída JSON
    const results = JSON.parse(output);

    // Mapear os resultados para obter apenas os arquivos com erros específicos
    const filesWithUnusedVars = results
      .filter(result =>
        result.messages.some(msg => msg.ruleId === '@typescript-eslint/no-unused-vars')
      )
      .map(result => {
        return {
          filePath: result.filePath,
          messages: result.messages.filter(
            msg => msg.ruleId === '@typescript-eslint/no-unused-vars'
          ),
        };
      });

    return filesWithUnusedVars;
  } catch (error) {
    console.error(`${colors.red}Erro ao executar ESLint: ${error.message}${colors.reset}`);
    console.log('Saída:', error.stdout?.toString() || '(nenhuma saída)');
    console.log('Erro:', error.stderr?.toString() || '(nenhum erro)');
    return [];
  }
}

// Fix para 5 arquivos importantes com variáveis não utilizadas
function fixImportantFiles() {
  const filesToFix = [
    'src/components/enhanced-chat-input.tsx',
    'src/components/ui/theme-toggle.tsx',
    'src/app/providers.tsx',
    'src/components/collaboration-indicator.tsx',
    'src/components/ui/error-message.tsx',
  ];

  filesToFix.forEach(filePath => {
    try {
      if (fs.existsSync(filePath)) {
        // Ler conteúdo do arquivo
        let content = fs.readFileSync(filePath, 'utf8');

        // Substituições conhecidas para variáveis/parâmetros não utilizados
        // Essas são baseadas nos erros comuns reportados pelo linter
        const replacements = [
          // Arquivo: enhanced-chat-input.tsx
          {
            from: 'const [isProcessing, setIsProcessing] =',
            to: 'const [isProcessing, _setIsProcessing] =',
          },
          { from: 'async (message: string)', to: 'async (_message: string)' },

          // Arquivo: theme-toggle.tsx
          { from: 'variant,', to: '_variant,' },

          // Arquivo: providers.tsx
          { from: 'locale,', to: '_locale,' },

          // Arquivo: collaboration-indicator.tsx
          { from: 'workbookId,', to: '_workbookId,' },

          // Arquivo: error-message.tsx
          { from: 'const t =', to: 'const _t =' },
        ];

        // Aplicar substituições
        replacements.forEach(({ from, to }) => {
          if (content.includes(from)) {
            content = content.replace(from, to);
            console.log(
              `${colors.green}Corrigido: ${filePath} - Substituído '${from}' por '${to}'${colors.reset}`
            );
          }
        });

        // Escrever conteúdo modificado de volta para o arquivo
        fs.writeFileSync(filePath, content);
      } else {
        console.log(`${colors.yellow}Arquivo não encontrado: ${filePath}${colors.reset}`);
      }
    } catch (error) {
      console.error(`${colors.red}Erro ao processar ${filePath}: ${error.message}${colors.reset}`);
    }
  });
}

// Executar correções
console.log(
  `${colors.blue}Aplicando correções para variáveis não utilizadas em arquivos importantes...${colors.reset}`
);
fixImportantFiles();

console.log(`${colors.green}Concluídas as correções de variáveis não utilizadas.${colors.reset}`);
console.log(
  `${colors.blue}Execute 'npm run lint' para verificar os problemas restantes.${colors.reset}`
);
