import { test, expect } from '@playwright/test';

test.describe('Upload e Análise de Planilha', () => {
  test('deve permitir upload de arquivo e interação via chat', async ({ page }) => {
    // Navegar para a página inicial
    await page.goto('/');

    // Verificar se a página carregou corretamente
    await expect(page.getByText('Excel Copilot')).toBeVisible();
    await expect(page.getByText('Interaja com suas planilhas')).toBeVisible();

    // Alternar para a aba de upload
    await page.getByRole('tab', { name: 'Upload de Planilha' }).click();

    // Verificar se a área de upload está visível
    await expect(page.getByText('Arraste e solte')).toBeVisible();

    // Simular upload de um arquivo Excel
    const fileChooserPromise = page.waitForEvent('filechooser');
    await page.click('text=ou clique para selecionar');
    const fileChooser = await fileChooserPromise;

    // Criar um arquivo Excel de teste
    await fileChooser.setFiles({
      name: 'test-data.xlsx',
      mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      buffer: Buffer.from('fake excel data'),
    });

    // Aguardar redirecionamento para dashboard ou confirmação de upload
    await page.waitForNavigation();

    // Verificar se estamos na página de workbook
    await expect(page.url()).toContain('/workbook/');

    // Verificar se a interface de chat está disponível
    await expect(page.getByPlaceholder(/Digite sua mensagem/i)).toBeVisible();

    // Enviar um comando via chat
    await page.getByPlaceholder(/Digite sua mensagem/i).fill('Calcule a soma da coluna Valor');
    await page.getByRole('button', { name: /Enviar/i }).click();

    // Aguardar a resposta do assistente
    await expect(page.getByText(/Soma da coluna Valor/i)).toBeVisible({ timeout: 10000 });

    // Verificar se a tabela foi atualizada (deve mostrar um total)
    await expect(page.getByText(/Total:/i)).toBeVisible();
  });

  test('deve mostrar exemplos de comandos ao clicar em sugestões', async ({ page }) => {
    // Navegar para a página de workbook
    await page.goto('/dashboard');

    // Clicar em uma planilha existente para abrir
    await page
      .getByText(/Planilha de Vendas/)
      .first()
      .click();

    // Aguardar carregamento da página de workbook
    await expect(page.url()).toContain('/workbook/');

    // Verificar se há exemplos de comandos disponíveis
    await expect(page.getByText('Análise de Dados')).toBeVisible();

    // Clicar em uma categoria de comandos
    await page.getByText('Análise de Dados').click();

    // Verificar se aparecem exemplos específicos
    await expect(page.getByText(/Some os valores/i)).toBeVisible();

    // Clicar em um exemplo para copiá-lo para o chat
    await page.getByText(/Some os valores/i).click();

    // Verificar se o texto foi copiado para o input de chat
    const inputValue = await page.getByPlaceholder(/Digite sua mensagem/i).inputValue();
    expect(inputValue).toContain('Some os valores');
  });
});
