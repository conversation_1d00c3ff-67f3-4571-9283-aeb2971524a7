import { AlertCircle, Play, XCircle } from 'lucide-react';
import { useState, useEffect } from 'react';

import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';

interface CommandPreviewProps {
  command: string;
  interpretation: string | null;
  isLoading: boolean;
  onExecute: () => void;
  onCancel: () => void;
}

export function CommandPreview({
  command: _command,
  interpretation,
  isLoading,
  onExecute,
  onCancel,
}: CommandPreviewProps) {
  const [visible, setVisible] = useState(false);

  useEffect(() => {
    if (interpretation) {
      setVisible(true);
    } else {
      setVisible(false);
    }
  }, [interpretation]);

  if (!visible) return null;

  return (
    <Card className="p-4 mb-3 border border-blue-200 dark:border-blue-900 bg-blue-50 dark:bg-blue-950/30">
      <div className="flex flex-col space-y-2">
        <div className="text-sm font-medium">
          <span className="text-blue-600 dark:text-blue-400">Interpretação do comando:</span>
        </div>
        <p className="text-sm text-slate-700 dark:text-slate-300">{interpretation}</p>
        <div className="flex justify-end space-x-2 mt-2">
          <Button
            variant="outline"
            size="sm"
            onClick={onCancel}
            className="flex items-center"
            disabled={isLoading}
          >
            <XCircle className="h-4 w-4 mr-1" />
            Cancelar
          </Button>
          <Button
            variant="default"
            size="sm"
            onClick={onExecute}
            className="flex items-center bg-green-600 hover:bg-green-700"
            disabled={isLoading}
          >
            {isLoading ? (
              <AlertCircle className="h-4 w-4 mr-1 animate-pulse" />
            ) : (
              <Play className="h-4 w-4 mr-1" />
            )}
            {isLoading ? 'Executando...' : 'Executar'}
          </Button>
        </div>
      </div>
    </Card>
  );
}
