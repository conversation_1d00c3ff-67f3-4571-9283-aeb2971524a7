/**
 * Utilitários para manipulação tipada de erros
 *
 * Este arquivo fornece funções para criar e lidar com erros de forma segura,
 * garantindo tipagem correta e preservando informações de contexto.
 */

// Tipos básicos para os erros
export type ErrorSeverity = 'info' | 'warning' | 'error' | 'critical';
export type ErrorSource = 'api' | 'ui' | 'network' | 'database' | 'auth' | 'excel' | 'ai';
export type ErrorCode = string;

// Interface para metadados de erro extras
export interface ErrorContext {
  userId?: string;
  requestId?: string;
  resourceId?: string;
  source?: ErrorSource;
  severity?: ErrorSeverity;
  code?: ErrorCode;
  [key: string]: any;
}

/**
 * Erro tipado estendido com contexto adicional
 */
export class EnhancedError extends Error {
  public readonly context: ErrorContext;
  public readonly originalError?: Error;
  public readonly timestamp: number;

  constructor(
    message: string,
    options: {
      context?: ErrorContext;
      originalError?: Error | unknown;
    } = {}
  ) {
    super(message);
    this.name = this.constructor.name;
    this.context = options.context || {};
    this.timestamp = Date.now();

    // Preservar o erro original quando disponível
    if (options.originalError) {
      this.originalError =
        options.originalError instanceof Error
          ? options.originalError
          : new Error(String(options.originalError));
    }

    // Manter a cadeia de protótipos correta
    Object.setPrototypeOf(this, EnhancedError.prototype);
  }

  /**
   * Cria um objeto serializável para logging
   */
  toJSON() {
    return {
      message: this.message,
      name: this.name,
      stack: this.stack,
      context: this.context,
      timestamp: this.timestamp,
      originalError: this.originalError
        ? {
            message: this.originalError.message,
            name: this.originalError.name,
            stack: this.originalError.stack,
          }
        : undefined,
    };
  }
}

/**
 * Cria um erro de API com código e contexto
 * @param message Mensagem de erro
 * @param code Código de erro específico
 * @param context Contexto adicional
 */
export function createApiError(
  message: string,
  code: string,
  context?: Omit<ErrorContext, 'code' | 'source'>
): EnhancedError {
  return new EnhancedError(message, {
    context: {
      ...context,
      code,
      source: 'api',
    },
  });
}

/**
 * Cria um erro de banco de dados com contexto
 * @param message Mensagem de erro
 * @param originalError Erro original do DB
 * @param context Contexto adicional
 */
export function createDatabaseError(
  message: string,
  originalError?: unknown,
  context?: Omit<ErrorContext, 'source'>
): EnhancedError {
  return new EnhancedError(message, {
    originalError,
    context: {
      ...context,
      source: 'database',
    },
  });
}

/**
 * Cria um erro de Excel com contexto
 * @param message Mensagem de erro
 * @param code Código de erro opcional
 * @param context Contexto adicional
 */
export function createExcelError(
  message: string,
  code?: string,
  context?: Omit<ErrorContext, 'code' | 'source'>
): EnhancedError {
  return new EnhancedError(message, {
    context: {
      ...context,
      code: code || 'EXCEL_OPERATION_ERROR',
      source: 'excel',
    },
  });
}

/**
 * Extrai informações úteis de qualquer erro desconhecido
 * @param error Erro de tipo desconhecido
 * @returns Objeto com informações estruturadas sobre o erro
 */
export function extractErrorInfo(error: unknown): {
  message: string;
  name: string;
  stack?: string;
  isError: boolean;
  [key: string]: any;
} {
  if (!error) {
    return {
      message: 'Erro desconhecido',
      name: 'UnknownError',
      isError: false,
    };
  }

  // Se já for um Error, retornar informações dele
  if (error instanceof Error) {
    return {
      message: error.message,
      name: error.name,
      stack: error.stack,
      isError: true,
      ...(error as any), // Inclui quaisquer propriedades extras
    };
  }

  // Se for uma string, usá-la como mensagem
  if (typeof error === 'string') {
    return {
      message: error,
      name: 'StringError',
      isError: false,
    };
  }

  // Para objetos, tentar extrair dados úteis
  if (typeof error === 'object' && error !== null) {
    const obj = error as Record<string, any>;

    return {
      message: obj.message || 'Erro de objeto desconhecido',
      name: obj.name || 'ObjectError',
      stack: obj.stack,
      isError: false,
      ...obj, // Incluir todas as propriedades do objeto
    };
  }

  // Para outros tipos
  return {
    message: `Erro de tipo ${typeof error}`,
    name: 'TypedError',
    isError: false,
    value: error,
  };
}

/**
 * Normaliza um erro de qualquer tipo para um EnhancedError
 * @param error Erro a ser normalizado
 * @param defaultMessage Mensagem padrão se não for possível extrair uma
 */
export function normalizeError(
  error: unknown,
  defaultMessage = 'Ocorreu um erro inesperado'
): EnhancedError {
  if (error instanceof EnhancedError) {
    return error;
  }

  const errorInfo = extractErrorInfo(error);
  const message = errorInfo.message || defaultMessage;

  return new EnhancedError(message, {
    originalError: error instanceof Error ? error : undefined,
    context: {
      ...errorInfo,
      message: undefined, // Remover a mensagem do contexto para evitar duplicação
      name: undefined, // Remover o nome do contexto para evitar duplicação
      stack: undefined, // Remover o stack do contexto para evitar duplicação
    },
  });
}

/**
 * Verifica se um objeto é um erro ou se contém informações de erro
 * @param value Valor a ser verificado
 */
export function isErrorLike(value: unknown): boolean {
  if (!value) return false;
  if (value instanceof Error) return true;

  if (typeof value === 'object' && value !== null) {
    const obj = value as Record<string, any>;
    // Verifica se tem propriedades comuns de erro
    return (
      (typeof obj.message === 'string' && obj.message.length > 0) ||
      (typeof obj.error === 'string' && obj.error.length > 0) ||
      (typeof obj.code === 'string' && obj.code.length > 0 && obj.code.includes('ERR'))
    );
  }

  return false;
}
