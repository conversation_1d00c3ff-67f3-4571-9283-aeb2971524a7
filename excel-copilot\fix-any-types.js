#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Lista de substituições específicas para tipos any
const typeReplacements = [
  // Tipos de dados genéricos
  { from: ': any', to: ': unknown' },
  { from: ': any[]', to: ': unknown[]' },
  { from: ': Array<any>', to: ': Array<unknown>' },
  { from: '(data: any)', to: '(data: unknown)' },
  { from: '(params: any)', to: '(params: unknown)' },
  { from: '(error: any)', to: '(error: unknown)' },
  { from: '(result: any)', to: '(result: unknown)' },
  { from: '(value: any)', to: '(value: unknown)' },
  { from: '(event: any)', to: '(event: unknown)' },

  // Tipos específicos para Excel/Office
  { from: 'comExcel: any', to: 'comExcel: unknown' },
  { from: 'excelCOM: any', to: 'excelCOM: unknown' },

  // Tipos para operações
  { from: 'operation: any', to: 'operation: unknown' },
  { from: 'operationData: any', to: 'operationData: unknown' },

  // Tipos para handlers e callbacks
  { from: 'handler: any', to: 'handler: unknown' },
  { from: 'callback: any', to: 'callback: unknown' },

  // Tipos para configurações
  { from: 'config: any', to: 'config: unknown' },
  { from: 'options: any', to: 'options: unknown' },
  { from: 'settings: any', to: 'settings: unknown' },
];

// Arquivos que devem ser processados
const filesToProcess = [
  'client/src/desktop-bridge.ts',
  'client/src/lib/logger.ts',
  'client/src/types.ts',
  'desktop-bridge/src/excel-bridge.ts',
  'desktop-bridge/src/excelBridge.ts',
  'desktop-bridge/src/logger.ts',
  'desktop-bridge/src/main.ts',
  'desktop-bridge/src/preload.ts',
  'desktop-bridge/src/utils/operationUtils.ts',
  'prisma/seed.ts',
];

function processFile(filePath) {
  const fullPath = path.join(__dirname, filePath);

  if (!fs.existsSync(fullPath)) {
    console.log(`⚠️  Arquivo não encontrado: ${filePath}`);
    return;
  }

  let content = fs.readFileSync(fullPath, 'utf8');
  let modified = false;

  // Aplicar substituições
  for (const replacement of typeReplacements) {
    const regex = new RegExp(replacement.from.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g');
    if (content.includes(replacement.from)) {
      content = content.replace(regex, replacement.to);
      modified = true;
    }
  }

  if (modified) {
    fs.writeFileSync(fullPath, content, 'utf8');
    console.log(`✅ Corrigido: ${filePath}`);
  } else {
    console.log(`ℹ️  Nenhuma alteração necessária: ${filePath}`);
  }
}

function main() {
  console.log('🔧 Iniciando correção de tipos any...\n');

  for (const file of filesToProcess) {
    processFile(file);
  }

  console.log('\n✨ Correção de tipos any concluída!');
  console.log('Execute "npm run lint" para verificar os resultados.');
}

if (require.main === module) {
  main();
}

module.exports = { processFile, typeReplacements };
