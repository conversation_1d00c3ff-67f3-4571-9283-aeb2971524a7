/**
 * MSW Server Mock
 * Este módulo configura um servidor mock para testes
 */

// Importações com tipagem para TypeScript
import { Mock<PERSON><PERSON><PERSON> } from './msw-mock';

// Servidor de mock para testes usando MSW
import { setupServer, http, HttpResponse } from './msw-mock';

// Para compatibilidade com ESM
const handlers: <PERSON>ckHandler[] = [];

// Configurar o servidor com os handlers
const server = setupServer(...handlers);

// Context mock para REST API
interface ResponseObject {
  status: number;
  body: unknown;
}

interface JsonMethod {
  (data: unknown): ResponseObject;
}

interface TextMethod {
  (text: string): ResponseObject;
}

interface StatusMethod {
  (code: number): {
    json: JsonMethod;
    text: TextMethod;
  };
}

interface ContextType {
  status: StatusMethod;
}

const ctx: ContextType = {
  status: (code: number) => ({
    json: (data: unknown): ResponseObject => ({ status: code, body: data }),
    text: (text: string): ResponseObject => ({ status: code, body: text }),
  }),
};

/**
 * Interface para o servidor (adicionada para compatibilidade com testes)
 */
interface ServerInterface {
  listen: () => void;
  close: () => void;
  resetHandlers: () => void;
  use: (...handlers: any[]) => void;
  handleRequest: (path: string, method: string, body?: any) => any;
  events: { on: () => void; removeListener: () => void };
  handlers: any[];
}

/**
 * Interface para rest (adicionada para compatibilidade com testes)
 */
interface RestInterface {
  get: (url: string, resolver: (req: any, res: any, ctx: any) => any) => any;
  post: (url: string, resolver: (req: any, res: any, ctx: any) => any) => any;
  put: (url: string, resolver: (req: any, res: any, ctx: any) => any) => any;
  patch: (url: string, resolver: (req: any, res: any, ctx: any) => any) => any;
  delete: (url: string, resolver: (req: any, res: any, ctx: any) => any) => any;
}

// Para compatibilidade com versões anteriores
const rest: RestInterface = {
  get: http.get.bind(http) as any,
  post: http.post.bind(http) as any,
  put: http.put.bind(http) as any,
  patch: http.patch.bind(http) as any,
  delete: http.delete.bind(http) as any,
};

// Adicionando o método handleRequest para compatibilidade com testes existentes
interface MockResponse {
  status: number;
  body: any;
  json: () => any;
}

const handleRequest = (path: string, method = 'GET', body?: any): MockResponse => {
  // Implementação mock simplificada para testes
  return {
    status: 200,
    body: {
      success: true,
      data: body || {},
      method,
    },
    json: () => ({}),
  };
};

// Estender o objeto server para incluir o handleRequest e outras propriedades necessárias
const extendedServer = {
  ...server,
  handleRequest,
  events: {
    on: () => {},
    removeListener: () => {},
  },
  handlers: [],
};

// Exportar todos os objetos necessários
export { extendedServer as server, rest, ctx, HttpResponse };
