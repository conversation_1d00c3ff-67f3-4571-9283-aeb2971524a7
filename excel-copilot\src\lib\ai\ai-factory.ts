import { ENV } from '@/config/unified-environment';
import { geminiService } from '@/lib/ai/gemini-service';
import { logger } from '@/lib/logger';

/**
 * Interface base para provedores de IA
 */
export interface AIProvider {
  processCommand(command: string, context?: string): Promise<string>;
  getServiceStats(): Record<string, number>;
}

/**
 * Adaptador para compatibilizar o GeminiService com a interface AIProvider
 */
class GeminiAdapter implements AIProvider {
  constructor(private service: typeof geminiService) {}

  async processCommand(command: string, context?: string): Promise<string> {
    const options = context !== undefined ? { context } : {};
    return this.service.sendMessage(command, options);
  }

  getServiceStats(): Record<string, number> {
    return { requests: 0, cacheHits: 0, cacheMisses: 0 }; // Stats básicas
  }
}

/**
 * Classe que seleciona o provedor de IA apropriado com base na configuração
 */
export class AIFactory {
  private static provider: AIProvider | null = null;

  /**
   * Inicializa e retorna a instância apropriada do provedor de IA
   */
  public static async getProvider(): Promise<AIProvider> {
    if (this.provider) {
      return this.provider;
    }

    // Verificar se estamos usando ambiente de mock/teste
    if (process.env.AI_USE_MOCK === 'true') {
      logger.info('Usando provedor de IA em modo mock');
      this.provider = new GeminiAdapter(geminiService);
      return this.provider;
    }

    // Verificar configuração do Vertex AI - usar apenas no servidor
    if (ENV.VERTEX_AI.ENABLED && ENV.VERTEX_AI.PROJECT_ID && typeof window === 'undefined') {
      logger.info('Usando Vertex AI como provedor de IA');
      try {
        // Importação dinâmica do VertexAIService apenas no servidor
        const vertexModule = await import('@/server/ai/vertex-ai-service');
        const vertexService = vertexModule.VertexAIService.getInstance();

        // Criar adaptador para compatibilizar com a interface AIProvider
        this.provider = {
          async processCommand(command, context) {
            // Usar o método sendMessage do VertexAIService que agora tem a mesma interface
            const options = context !== undefined ? { context } : {};
            return vertexService.sendMessage(command, options);
          },
          getServiceStats() {
            return vertexService.getStats();
          },
        };

        return this.provider;
      } catch (error) {
        logger.error('Erro ao carregar módulo Vertex AI:', error);
        // Fallback para Gemini com modo mock
        logger.warn('Fallback para modo mock devido a erro ao inicializar Vertex AI');
        this.provider = new GeminiAdapter(geminiService);
        return this.provider;
      }
    }

    // Se não há configuração válida, usar modo mock com aviso
    logger.warn('Nenhum provedor de IA configurado corretamente. Usando modo mock como fallback.');
    this.provider = new GeminiAdapter(geminiService);
    return this.provider;
  }
}

// Exportar função para obter o provedor
export const getAIProvider = AIFactory.getProvider;
