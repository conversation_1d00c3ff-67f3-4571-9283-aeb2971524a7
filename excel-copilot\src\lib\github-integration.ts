/**
 * GitHub MCP Integration - Excel Copilot
 *
 * Cliente e serviços para integração com GitHub API
 * Permite monitoramento de repositórios, issues, PRs e CI/CD via MCP
 */

import { ENV } from '@/config/unified-environment';

import { logger } from './logger';

// Tipos GitHub
export interface GitHubRepository {
  id: number;
  name: string;
  full_name: string;
  description?: string;
  private: boolean;
  html_url: string;
  clone_url: string;
  ssh_url: string;
  default_branch: string;
  language?: string;
  stargazers_count: number;
  forks_count: number;
  open_issues_count: number;
  created_at: string;
  updated_at: string;
  pushed_at: string;
  owner: {
    login: string;
    id: number;
    avatar_url: string;
    type: string;
  };
}

export interface GitHubIssue {
  id: number;
  number: number;
  title: string;
  body?: string;
  state: 'open' | 'closed';
  html_url: string;
  user: {
    login: string;
    id: number;
    avatar_url: string;
  };
  assignees: Array<{
    login: string;
    id: number;
    avatar_url: string;
  }>;
  labels: Array<{
    id: number;
    name: string;
    color: string;
    description?: string;
  }>;
  milestone?: {
    id: number;
    title: string;
    description?: string;
    state: 'open' | 'closed';
    due_on?: string;
  };
  created_at: string;
  updated_at: string;
  closed_at?: string;
}

export interface GitHubPullRequest extends GitHubIssue {
  draft: boolean;
  merged: boolean;
  merged_at?: string;
  merge_commit_sha?: string;
  head: {
    ref: string;
    sha: string;
    repo: GitHubRepository;
  };
  base: {
    ref: string;
    sha: string;
    repo: GitHubRepository;
  };
  mergeable?: boolean;
  mergeable_state: string;
  review_comments: number;
  commits: number;
  additions: number;
  deletions: number;
  changed_files: number;
}

export interface GitHubWorkflowRun {
  id: number;
  name: string;
  head_branch: string;
  head_sha: string;
  status: 'queued' | 'in_progress' | 'completed';
  conclusion?:
    | 'success'
    | 'failure'
    | 'neutral'
    | 'cancelled'
    | 'skipped'
    | 'timed_out'
    | 'action_required';
  workflow_id: number;
  html_url: string;
  created_at: string;
  updated_at: string;
  run_started_at?: string;
  jobs_url: string;
  logs_url: string;
  check_suite_url: string;
  artifacts_url: string;
  cancel_url: string;
  rerun_url: string;
  workflow_url: string;
  pull_requests: Array<{
    id: number;
    number: number;
    url: string;
    head: {
      ref: string;
      sha: string;
    };
    base: {
      ref: string;
      sha: string;
    };
  }>;
}

export interface GitHubHealthStatus {
  configured: boolean;
  tokenValid: boolean;
  repositoryAccessible: boolean;
  rateLimitRemaining: number;
  rateLimitReset: string;
  lastSync?: string;
  repositoryCount: number;
  issueCount: number;
  pullRequestCount: number;
  recentActivity: number;
}

export interface GitHubRateLimit {
  limit: number;
  remaining: number;
  reset: number;
  used: number;
  resource: string;
}

/**
 * Cliente base para GitHub API
 */
export class GitHubClient {
  /**
   * Verifica se deve usar OAuth em vez de token pessoal
   */
  private shouldUseOAuth(): boolean {
    return (
      process.env.MCP_GITHUB_TOKEN === 'OAUTH_MODE' &&
      !!process.env.AUTH_GITHUB_CLIENT_ID &&
      !!process.env.AUTH_GITHUB_CLIENT_SECRET
    );
  }

  /**
   * Obtém token OAuth para requisições
   */
  private async getOAuthToken(): Promise<string> {
    // Em produção, usar o token OAuth do NextAuth
    // Por enquanto, usar credenciais básicas para demonstração
    return Buffer.from(
      `${process.env.AUTH_GITHUB_CLIENT_ID}:${process.env.AUTH_GITHUB_CLIENT_SECRET}`
    ).toString('base64');
  }

  private token: string;
  private baseUrl = 'https://api.github.com';
  private graphqlUrl = 'https://api.github.com/graphql';
  private owner?: string;
  private repo?: string;

  constructor(options?: { token?: string; owner?: string; repo?: string }) {
    this.token = options?.token || ENV.GITHUB_TOKEN || '';

    const ownerValue = options?.owner || ENV.GITHUB_OWNER;
    if (ownerValue) {
      this.owner = ownerValue;
    }

    const repoValue = options?.repo || ENV.GITHUB_REPO;
    if (repoValue) {
      this.repo = repoValue;
    }

    if (!this.token) {
      logger.warn('GitHub token não configurado');
    }
  }

  /**
   * Executa requisição REST para GitHub API
   */
  private async restRequest<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    if (!this.token) {
      throw new Error('GitHub token não configurado');
    }

    const url = `${this.baseUrl}${endpoint}`;
    const headers = {
      Authorization: `Bearer ${this.token}`,
      Accept: 'application/vnd.github+json',
      'X-GitHub-Api-Version': '2022-11-28',
      ...options.headers,
    };

    try {
      const response = await fetch(url, {
        ...options,
        headers,
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`GitHub API error: ${response.status} - ${errorText}`);
      }

      return await response.json();
    } catch (error) {
      logger.error('Erro na requisição GitHub API', { endpoint, error });
      throw error;
    }
  }

  /**
   * Executa query GraphQL na GitHub API
   */
  private async graphqlQuery<T>(query: string, variables?: Record<string, unknown>): Promise<T> {
    if (!this.token) {
      throw new Error('GitHub token não configurado');
    }

    const response = await fetch(this.graphqlUrl, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${this.token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query,
        variables,
      }),
    });

    if (!response.ok) {
      throw new Error(`GitHub GraphQL error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();

    if (data.errors) {
      throw new Error(
        `GitHub GraphQL error: ${data.errors.map((e: { message: string }) => e.message).join(', ')}`
      );
    }

    return data.data;
  }

  /**
   * Verifica rate limit atual
   */
  async getRateLimit(): Promise<GitHubRateLimit> {
    const response = await this.restRequest<{
      rate: GitHubRateLimit;
    }>('/rate_limit');

    return response.rate;
  }

  /**
   * Lista repositórios do usuário/organização
   */
  async getRepositories(
    options: {
      type?: 'all' | 'owner' | 'public' | 'private' | 'member';
      sort?: 'created' | 'updated' | 'pushed' | 'full_name';
      direction?: 'asc' | 'desc';
      per_page?: number;
      page?: number;
    } = {}
  ): Promise<{
    repositories: GitHubRepository[];
    total: number;
  }> {
    const {
      type = 'owner',
      sort = 'updated',
      direction = 'desc',
      per_page = 30,
      page = 1,
    } = options;

    const endpoint = this.owner ? `/orgs/${this.owner}/repos` : '/user/repos';

    const params = new URLSearchParams({
      type,
      sort,
      direction,
      per_page: per_page.toString(),
      page: page.toString(),
    });

    const repositories = await this.restRequest<GitHubRepository[]>(`${endpoint}?${params}`);

    return {
      repositories,
      total: repositories.length,
    };
  }

  /**
   * Obtém detalhes de um repositório específico
   */
  async getRepository(owner: string, repo: string): Promise<GitHubRepository> {
    return await this.restRequest<GitHubRepository>(`/repos/${owner}/${repo}`);
  }

  /**
   * Lista issues de um repositório
   */
  async getIssues(
    options: {
      owner?: string;
      repo?: string;
      state?: 'open' | 'closed' | 'all';
      labels?: string;
      sort?: 'created' | 'updated' | 'comments';
      direction?: 'asc' | 'desc';
      since?: string;
      per_page?: number;
      page?: number;
    } = {}
  ): Promise<{
    issues: GitHubIssue[];
    total: number;
  }> {
    const owner = options.owner || this.owner;
    const repo = options.repo || this.repo;

    if (!owner || !repo) {
      throw new Error('Owner e repo são obrigatórios');
    }

    const {
      state = 'open',
      sort = 'updated',
      direction = 'desc',
      per_page = 30,
      page = 1,
    } = options;

    const params = new URLSearchParams({
      state,
      sort,
      direction,
      per_page: per_page.toString(),
      page: page.toString(),
    });

    if (options.labels) {
      params.append('labels', options.labels);
    }

    if (options.since) {
      params.append('since', options.since);
    }

    const issues = await this.restRequest<GitHubIssue[]>(
      `/repos/${owner}/${repo}/issues?${params}`
    );

    // Filtrar apenas issues (não PRs)
    const filteredIssues = issues.filter(issue => !('pull_request' in issue));

    return {
      issues: filteredIssues,
      total: filteredIssues.length,
    };
  }

  /**
   * Lista pull requests de um repositório
   */
  async getPullRequests(
    options: {
      owner?: string;
      repo?: string;
      state?: 'open' | 'closed' | 'all';
      head?: string;
      base?: string;
      sort?: 'created' | 'updated' | 'popularity' | 'long-running';
      direction?: 'asc' | 'desc';
      per_page?: number;
      page?: number;
    } = {}
  ): Promise<{
    pullRequests: GitHubPullRequest[];
    total: number;
  }> {
    const owner = options.owner || this.owner;
    const repo = options.repo || this.repo;

    if (!owner || !repo) {
      throw new Error('Owner e repo são obrigatórios');
    }

    const {
      state = 'open',
      sort = 'updated',
      direction = 'desc',
      per_page = 30,
      page = 1,
    } = options;

    const params = new URLSearchParams({
      state,
      sort,
      direction,
      per_page: per_page.toString(),
      page: page.toString(),
    });

    if (options.head) {
      params.append('head', options.head);
    }

    if (options.base) {
      params.append('base', options.base);
    }

    const pullRequests = await this.restRequest<GitHubPullRequest[]>(
      `/repos/${owner}/${repo}/pulls?${params}`
    );

    return {
      pullRequests,
      total: pullRequests.length,
    };
  }

  /**
   * Lista workflow runs de um repositório
   */
  async getWorkflowRuns(
    options: {
      owner?: string;
      repo?: string;
      workflow_id?: string;
      actor?: string;
      branch?: string;
      event?: string;
      status?: 'queued' | 'in_progress' | 'completed';
      per_page?: number;
      page?: number;
    } = {}
  ): Promise<{
    workflowRuns: GitHubWorkflowRun[];
    total: number;
  }> {
    const owner = options.owner || this.owner;
    const repo = options.repo || this.repo;

    if (!owner || !repo) {
      throw new Error('Owner e repo são obrigatórios');
    }

    const { per_page = 30, page = 1 } = options;

    const params = new URLSearchParams({
      per_page: per_page.toString(),
      page: page.toString(),
    });

    if (options.workflow_id) {
      params.append('workflow_id', options.workflow_id);
    }

    if (options.actor) {
      params.append('actor', options.actor);
    }

    if (options.branch) {
      params.append('branch', options.branch);
    }

    if (options.event) {
      params.append('event', options.event);
    }

    if (options.status) {
      params.append('status', options.status);
    }

    const response = await this.restRequest<{
      workflow_runs: GitHubWorkflowRun[];
      total_count: number;
    }>(`/repos/${owner}/${repo}/actions/runs?${params}`);

    return {
      workflowRuns: response.workflow_runs,
      total: response.total_count,
    };
  }

  /**
   * Obtém informações do usuário autenticado
   */
  async getAuthenticatedUser(): Promise<{
    login: string;
    id: number;
    name?: string;
    email?: string;
    avatar_url: string;
    type: string;
    public_repos: number;
    public_gists: number;
    followers: number;
    following: number;
    created_at: string;
    updated_at: string;
  }> {
    return await this.restRequest('/user');
  }

  /**
   * Verifica saúde da conexão GitHub
   */
  async checkHealth(): Promise<GitHubHealthStatus> {
    try {
      if (!this.token) {
        return {
          configured: false,
          tokenValid: false,
          repositoryAccessible: false,
          rateLimitRemaining: 0,
          rateLimitReset: new Date().toISOString(),
          repositoryCount: 0,
          issueCount: 0,
          pullRequestCount: 0,
          recentActivity: 0,
        };
      }

      // Verificar rate limit e validade do token
      const rateLimit = await this.getRateLimit();

      // Verificar acesso ao usuário autenticado
      const _user = await this.getAuthenticatedUser();

      // Se temos owner/repo configurados, verificar acesso ao repositório
      let repositoryAccessible = true;
      let repositoryCount = 0;
      let issueCount = 0;
      let pullRequestCount = 0;
      let recentActivity = 0;

      try {
        if (this.owner && this.repo) {
          // Verificar acesso ao repositório específico
          await this.getRepository(this.owner, this.repo);
          repositoryCount = 1;

          // Obter estatísticas básicas
          const [issues, pullRequests] = await Promise.all([
            this.getIssues({ per_page: 10 }),
            this.getPullRequests({ per_page: 10 }),
          ]);

          issueCount = issues.total;
          pullRequestCount = pullRequests.total;

          // Calcular atividade recente (últimas 24h)
          const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
          recentActivity = [...issues.issues, ...pullRequests.pullRequests].filter(
            item => new Date(item.updated_at) > oneDayAgo
          ).length;
        } else {
          // Obter repositórios do usuário
          const repos = await this.getRepositories({ per_page: 10 });
          repositoryCount = repos.total;
        }
      } catch (repoError) {
        logger.warn('Erro ao acessar repositório específico:', repoError);
        repositoryAccessible = false;
      }

      return {
        configured: true,
        tokenValid: true,
        repositoryAccessible,
        rateLimitRemaining: rateLimit.remaining,
        rateLimitReset: new Date(rateLimit.reset * 1000).toISOString(),
        lastSync: new Date().toISOString(),
        repositoryCount,
        issueCount,
        pullRequestCount,
        recentActivity,
      };
    } catch (error) {
      logger.error('GitHub health check failed:', error);

      return {
        configured: !!this.token,
        tokenValid: false,
        repositoryAccessible: false,
        rateLimitRemaining: 0,
        rateLimitReset: new Date().toISOString(),
        repositoryCount: 0,
        issueCount: 0,
        pullRequestCount: 0,
        recentActivity: 0,
      };
    }
  }
}

/**
 * Serviço de monitoramento GitHub de alto nível
 */
export class GitHubMonitoringService {
  private client: GitHubClient;

  constructor(options?: { token?: string; owner?: string; repo?: string }) {
    this.client = new GitHubClient(options);
  }

  /**
   * Obtém dashboard de status do repositório
   */
  async getRepositoryDashboard(
    owner?: string,
    repo?: string
  ): Promise<{
    repository: GitHubRepository;
    openIssues: number;
    openPullRequests: number;
    recentWorkflowRuns: GitHubWorkflowRun[];
    recentActivity: Array<GitHubIssue | GitHubPullRequest>;
    healthStatus: 'healthy' | 'warning' | 'critical';
  }> {
    const repoOwner = owner || ENV.GITHUB_OWNER;
    const repoName = repo || ENV.GITHUB_REPO;

    if (!repoOwner || !repoName) {
      throw new Error('Owner e repo são obrigatórios');
    }

    const [repository, issues, pullRequests, workflowRuns] = await Promise.all([
      this.client.getRepository(repoOwner, repoName),
      this.client.getIssues({ owner: repoOwner, repo: repoName, state: 'open', per_page: 5 }),
      this.client.getPullRequests({ owner: repoOwner, repo: repoName, state: 'open', per_page: 5 }),
      this.client.getWorkflowRuns({ owner: repoOwner, repo: repoName, per_page: 5 }),
    ]);

    // Combinar atividade recente
    const recentActivity = [...issues.issues, ...pullRequests.pullRequests]
      .sort((a, b) => new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime())
      .slice(0, 10);

    // Determinar status de saúde baseado em workflow runs
    let healthStatus: 'healthy' | 'warning' | 'critical' = 'healthy';
    const recentFailures = workflowRuns.workflowRuns.filter(
      run => run.conclusion === 'failure'
    ).length;

    if (recentFailures > 2) {
      healthStatus = 'critical';
    } else if (recentFailures > 0) {
      healthStatus = 'warning';
    }

    return {
      repository,
      openIssues: issues.total,
      openPullRequests: pullRequests.total,
      recentWorkflowRuns: workflowRuns.workflowRuns,
      recentActivity,
      healthStatus,
    };
  }

  /**
   * Obtém métricas de CI/CD
   */
  async getCICDMetrics(
    owner?: string,
    repo?: string
  ): Promise<{
    totalRuns: number;
    successRate: number;
    averageDuration: number;
    recentFailures: GitHubWorkflowRun[];
    trendsLast30Days: {
      date: string;
      runs: number;
      failures: number;
    }[];
  }> {
    const repoOwner = owner || ENV.GITHUB_OWNER;
    const repoName = repo || ENV.GITHUB_REPO;

    if (!repoOwner || !repoName) {
      throw new Error('Owner e repo são obrigatórios');
    }

    // Obter runs dos últimos 30 dias
    const workflowRuns = await this.client.getWorkflowRuns({
      owner: repoOwner,
      repo: repoName,
      per_page: 100,
    });

    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const recentRuns = workflowRuns.workflowRuns.filter(
      run => new Date(run.created_at) > thirtyDaysAgo
    );

    const totalRuns = recentRuns.length;
    const successfulRuns = recentRuns.filter(run => run.conclusion === 'success').length;
    const successRate = totalRuns > 0 ? (successfulRuns / totalRuns) * 100 : 0;

    // Calcular duração média (aproximada)
    const completedRuns = recentRuns.filter(
      run => run.status === 'completed' && run.run_started_at
    );
    const averageDuration =
      completedRuns.length > 0
        ? completedRuns.reduce((acc, run) => {
            const start = new Date(run.run_started_at!).getTime();
            const end = new Date(run.updated_at).getTime();
            return acc + (end - start);
          }, 0) /
          completedRuns.length /
          1000 /
          60 // em minutos
        : 0;

    const recentFailures = recentRuns.filter(run => run.conclusion === 'failure').slice(0, 5);

    // Agrupar por dia para trends
    const trendsMap = new Map<string, { runs: number; failures: number }>();
    recentRuns.forEach(run => {
      if (!run.created_at) return; // Pular se não tiver data
      const date = new Date(run.created_at).toISOString().split('T')[0];
      if (!date) return; // Pular se a data for inválida
      const current = trendsMap.get(date) || { runs: 0, failures: 0 };
      current.runs++;
      if (run.conclusion === 'failure') {
        current.failures++;
      }
      trendsMap.set(date, current);
    });

    const trendsLast30Days = Array.from(trendsMap.entries())
      .map(([date, stats]) => ({ date, ...stats }))
      .sort((a, b) => a.date.localeCompare(b.date));

    return {
      totalRuns,
      successRate,
      averageDuration,
      recentFailures,
      trendsLast30Days,
    };
  }
}
