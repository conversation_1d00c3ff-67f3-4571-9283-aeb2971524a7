#!/bin/bash

# Script de deploy para Vercel
# Corrige problemas de produção antes do deploy

echo "🚀 Preparando deploy para produção..."

# 1. Limpar cache
echo "🧹 Limpando cache..."
rm -rf .next
rm -rf node_modules/.cache

# 2. Instalar dependências
echo "📦 Instalando dependências..."
npm ci

# 3. Executar verificações
echo "🔍 Executando verificações..."
npm run type-check

# 4. Build de produção
echo "🏗️ Executando build..."
npm run build

echo "✅ Deploy preparado com sucesso!"
echo "Execute: vercel --prod"
