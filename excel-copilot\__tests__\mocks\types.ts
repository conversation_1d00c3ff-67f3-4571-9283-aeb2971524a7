/**
 * Types for mock testing
 * Arquivo de compatibilidade para ser importado em testes
 */

import type { MockResponseObject, MockContext as BaseMockContext } from './mock-types';

// Tipo para servidor MSW
export interface ServerType {
  listen: () => void;
  close: () => void;
  resetHandlers: () => void;
  use: (...handlers: any[]) => void;
}

// Handler Rest para MSW
export interface RestType {
  get: (url: string, resolver: any) => any;
  post: (url: string, resolver: any) => any;
  put: (url: string, resolver: any) => any;
  patch: (url: string, resolver: any) => any;
  delete: (url: string, resolver: any) => any;
}

// Definição do tipo para compatibilidade com MSW
export interface RestRequest {
  url: URL;
  method: string;
  cookies: Record<string, string>;
  headers: Headers;
  params: Record<string, string>;
  body: any;
}

// Interfaces para mock dos testes
export interface ApiResult<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  statusCode?: number;
  message?: string;
}

export interface MockRequest {
  url: string;
  method: string;
  headers?: Record<string, string>;
  body?: any;
  params?: Record<string, string>;
  query?: Record<string, string>;
}

// Como BaseMockContext tem uma propriedade text, precisamos incluir ela aqui
export interface MockContext {
  status: (code: number) => MockContext;
  json: (data: any) => MockResponse;
  text: (text: string) => MockResponse;
  headers: Record<string, string>;
}

export interface MockResponse<T = any> {
  status: number;
  data: T;
  headers?: Record<string, string>;
  ok: boolean;
}

// Re-exportar tipos para torná-los disponíveis para os testes
export type Request = RestRequest | MockRequest;
export type Response = MockResponse;
export type Context = MockContext;

// Type guards
export function isRestRequest(req: any): req is RestRequest {
  return typeof req === 'object' && req !== null && 'cookies' in req;
}

export function isMockRequest(req: any): req is MockRequest {
  return typeof req === 'object' && req !== null && !('cookies' in req);
}
