import { PrismaClient, type Prisma } from '@prisma/client';

/**
 * Cliente Prisma Universal - Funciona em qualquer runtime
 * Edge Runtime, Node.js Runtime, Serverless Functions
 */

// Detectar o ambiente de execução
const isEdgeRuntime =
  (typeof globalThis !== 'undefined' &&
    (globalThis as Record<string, unknown>).EdgeRuntime !== undefined) ||
  process.env.VERCEL_REGION !== undefined;

const isServerless = process.env.VERCEL === '1' || process.env.AWS_LAMBDA_FUNCTION_NAME;

// Configuração do cliente baseada no ambiente
function createPrismaClient(): PrismaClient {
  const config: Prisma.PrismaClientOptions = {
    log: process.env.NODE_ENV === 'development' ? ['error', 'warn'] : ['error'],
  };

  // Para Edge Runtime, usar configurações mínimas
  if (isEdgeRuntime) {
    // Configuração simplificada para Edge Runtime
    if (process.env.DB_DATABASE_URL) {
      config.datasources = {
        db: {
          url: process.env.DB_DATABASE_URL,
        },
      };
    }

    // Desabilitar features que não funcionam no Edge Runtime
    config.errorFormat = 'minimal';
  } else {
    // Configuração completa para Node.js Runtime
    if (process.env.DB_DATABASE_URL) {
      config.datasources = {
        db: {
          url: process.env.DB_DATABASE_URL,
        },
      };
    }
  }

  return new PrismaClient(config);
}

// Singleton pattern para reutilizar a conexão
let prismaInstance: PrismaClient | null = null;

export function getPrismaClient(): PrismaClient {
  if (!prismaInstance) {
    prismaInstance = createPrismaClient();
  }
  return prismaInstance;
}

// Cliente principal para uso geral
export const prisma = getPrismaClient();

// Função para desconectar de forma segura
export async function disconnectPrisma(): Promise<void> {
  if (prismaInstance) {
    try {
      await prismaInstance.$disconnect();
      prismaInstance = null;
    } catch (error) {
      console.error('Erro ao desconectar Prisma:', error);
    }
  }
}

// Função de teste de conexão universal
export async function testConnection(): Promise<boolean> {
  try {
    const client = getPrismaClient();
    await client.$queryRaw`SELECT 1`;
    return true;
  } catch (error) {
    console.error('Teste de conexão falhou:', error);
    return false;
  }
}

// Métricas do cliente
export function getClientInfo() {
  return {
    isEdgeRuntime,
    isServerless,
    hasInstance: !!prismaInstance,
    environment: process.env.NODE_ENV,
    databaseUrl: process.env.DB_DATABASE_URL ? 'configured' : 'missing',
  };
}

// Export default para compatibilidade
export default prisma;
