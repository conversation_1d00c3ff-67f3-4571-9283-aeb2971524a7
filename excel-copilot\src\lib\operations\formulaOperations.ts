import { safeArrayAccess } from '@/utils';
import { extractGroup } from '@/utils/regex-utils';

import { ExcelOperationType } from '../../types/index';
import { ExcelOperation } from '../excel/types';
/**
 * Interface para dados de operação de fórmula
 */
export interface FormulaOperationData {
  cell: string;
  formula: string;
  description?: string;
}

/**
 * Lista de fórmulas permitidas do Excel para evitar injeção de código malicioso
 */
const SAFE_EXCEL_FUNCTIONS = [
  // Funções matemáticas
  'SOMA',
  'MÉDIA',
  'MÁXIMO',
  'MÍNIMO',
  'MED',
  'CONT.NÚM',
  'CONT.SE',
  'CONT.SES',
  'SOMA.SE',
  'SOMA.SES',
  'ARRED',
  'ARREDONDAR.PARA.BAIXO',
  'ARREDONDAR.PARA.CIMA',
  'INT',
  'ABS',
  'RAIZ',
  'POTÊNCIA',
  'MOD',
  'ALEATÓRIO',
  'ALEATÓRIOENTRE',

  // Funções lógicas
  'SE',
  'E',
  'OU',
  'NÃO',
  'VERDADEIRO',
  'FALSO',

  // Funções de texto
  'TEXTO',
  'CONCATENAR',
  'CONCAT',
  'MAIÚSCULA',
  'MINÚSCULA',
  'PRI.MAIÚSCULA',
  'ARRUMAR',
  'ESQUERDA',
  'DIREITA',
  'EXT.TEXTO',
  'NÚM.CARACT',
  'LOCALIZAR',
  'PROCURAR',
  'SUBSTITUIR',
  'REPT',
  'T',
  'TEXTO.PARCIAL',

  // Funções de data
  'HOJE',
  'AGORA',
  'DATA',
  'DIA',
  'MÊS',
  'ANO',
  'DIAS',
  'HORA',
  'MINUTO',
  'SEGUNDO',

  // Funções de pesquisa
  'PROCV',
  'PROCH',
  'CORRESP',
  'ÍNDICE',
  'ESCOLHER',
  'INDIRETO',

  // Funções informativas
  'É.NÚM',
  'É.TEXTO',
  'É.ERRO',
  'É.PAR',
  'É.ÍMPAR',
  'É.VAZIO',
  'NÃO.DISP',

  // Versões em inglês das funções mais comuns (para compatibilidade)
  'SUM',
  'AVERAGE',
  'MAX',
  'MIN',
  'MEDIAN',
  'COUNT',
  'COUNTIF',
  'COUNTIFS',
  'SUMIF',
  'SUMIFS',
  'ROUND',
  'ROUNDDOWN',
  'ROUNDUP',
  'INT',
  'ABS',
  'SQRT',
  'POWER',
  'MOD',
  'RAND',
  'RANDBETWEEN',
  'IF',
  'AND',
  'OR',
  'NOT',
  'TRUE',
  'FALSE',
];

/**
 * Verifica se uma fórmula é segura
 * @param formula Fórmula a ser verificada
 * @returns True se a fórmula for considerada segura
 */
function _isFormulaSafe(formula: string): boolean {
  // Verificar entrada nula ou vazia
  if (!formula || typeof formula !== 'string') return false;

  // Remover o sinal de = inicial, se houver
  const cleanFormula = formula.startsWith('=') ? formula.substring(1) : formula;

  // Verificar comprimento máximo para evitar ataques de DoS
  if (cleanFormula.length > 1000) return false;

  // Verificar caracteres perigosos ou padrões suspeitos
  const suspiciousPatterns = [
    /javascript:/i, // JavaScript URI
    /data:/i, // Data URI
    /vbscript:/i, // VBScript URI
    /http[s]?:/i, // URLs externas
    /file:/i, // Acesso a arquivos
    /\s+on\w+=/i, // Eventos de HTML (onclick, etc)
    /<\s*script/i, // Tags de script
    /<\s*iframe/i, // Tags de iframe
    /\[\s*execute\s*\]/i, // Executor de shell
    /\bRUN\s*\(/i, // Comandos de execução
    /\bEVAL\s*\(/i, // Avaliação dinâmica de código
    /\bSHELL\s*\(/i, // Acesso a shell
    /\bCMD\s*\(/i, // Comandos do sistema
    /\bEXEC\s*\(/i, // Execução de comandos
    ////,                // Comentários
    //\*/                 // Comentários multilinha
  ];

  // Verificar padrões suspeitos
  for (const pattern of suspiciousPatterns) {
    if (pattern.test(cleanFormula)) {
      return false;
    }
  }

  // Verificar se a fórmula contém apenas caracteres seguros
  const safeCharactersRegex = /^[A-Za-z0-9À-Öà-öØ-ÿ_.()-+:,;\s*/"><=']+$/;
  if (!safeCharactersRegex.test(cleanFormula)) {
    return false;
  }

  // Extrair nomes de funções da fórmula
  const functionMatches = cleanFormula.match(/([A-Za-zÀ-ÖØ-öø-ÿ._]+)(?=\()/g);

  if (functionMatches) {
    // Limitar número máximo de funções aninhadas
    if (functionMatches.length > 10) return false;

    // Verificar se todas as funções estão na lista de funções seguras
    return functionMatches.every(func => SAFE_EXCEL_FUNCTIONS.includes(func.toUpperCase()));
  }

  // Se não tiver funções, verificar se é uma fórmula aritmética simples
  return /^[A-Z0-9:.\s()-+*/]+$/.test(cleanFormula);
}

/**
 * Extrai operações de fórmulas a partir do texto da resposta de IA
 * @param response Resposta da IA
 * @returns Array de operações de fórmula
 */
export function extractFormulaOperations(response: string): ExcelOperation[] {
  const operations: ExcelOperation[] = [];

  // Verificar padrões de fórmulas
  const formulaPattern =
    /OPERAÇÃO:\s*FÓRMULA[\s\S]*?TIPO:\s*([^\n]+)[\s\S]*?RANGE:\s*([^\n]+)[\s\S]*?RESULTADO_CÉLULA:\s*([^\n]+)(?:[\s\S]*?FORMATO:\s*([^\n]+))?/gi;

  let match;
  while ((match = formulaPattern.exec(response)) !== null) {
    const formulaType = extractGroup(match, 1).trim();
    const range = extractGroup(match, 2).trim();
    const resultCell = extractGroup(match, 3).trim();
    const format = extractGroup(match, 4).trim();

    if (formulaType && range && resultCell) {
      // Mapear tipo de fórmula para função Excel
      const formulaFunction = mapFormulaTypeToFunction(formulaType);

      const operation: ExcelOperation = {
        type: ExcelOperationType.FORMULA,
        data: {
          formula: formulaFunction,
          range,
          resultCell,
          format: format || undefined,
        },
      };

      operations.push(operation);
    }
  }

  return operations;
}

/**
 * Mapeia tipo de fórmula descrito em linguagem natural para função Excel
 * @param formulaType Tipo de fórmula (ex: "SOMA", "MÉDIA")
 * @returns Função Excel correspondente
 */
function mapFormulaTypeToFunction(formulaType: string): string {
  const typeMap: Record<string, string> = {
    SOMA: 'SUM',
    MÉDIA: 'AVERAGE',
    MEDIA: 'AVERAGE',
    MÁXIMO: 'MAX',
    MAXIMO: 'MAX',
    MÍNIMO: 'MIN',
    MINIMO: 'MIN',
    CONTAGEM: 'COUNT',
    CONTAR: 'COUNT',
    SE: 'IF',
    CONTARVALORES: 'COUNTIF',
    SOMASE: 'SUMIF',
    PROCV: 'VLOOKUP',
    PROCURARVALOR: 'VLOOKUP',
    CONCATENAR: 'CONCATENATE',
    DESVPAD: 'STDEV',
    ARREDONDAR: 'ROUND',
  };

  // Verificar se temos um mapeamento direto
  const upperCaseType = formulaType.toUpperCase();
  if (typeMap[upperCaseType]) {
    return typeMap[upperCaseType];
  }

  // Se não tivermos um mapeamento direto, retornar o próprio tipo
  // Isso permite que o usuário especifique funções diretamente
  return upperCaseType;
}

/**
 * Executa uma operação de fórmula em dados de planilha
 * @param sheetData Dados da planilha
 * @param operation Operação de fórmula a ser executada
 * @returns Dados atualizados e resumo da operação
 */
export async function executeFormulaOperation(
  sheetData: any,
  operation: ExcelOperation
): Promise<{ updatedData: any; resultSummary: string }> {
  try {
    const { formula, range, resultCell, format: _format } = operation.data;

    if (!formula || !range || !resultCell) {
      throw new Error('Parâmetros insuficientes para operação de fórmula');
    }

    // Clonar dados para não modificar o original diretamente
    const updatedData = { ...sheetData };

    // Extrair índices de linhas e colunas do range
    const rangeData = parseRange(range);
    const { endRow, endCol } = rangeData;

    // Extrair índice da célula de resultado
    const { row: resultRow, col: resultCol } = parseCellReference(resultCell);

    // Construir fórmula Excel
    const excelFormula = `=${formula}(${range})`;

    // Em uma implementação real, aqui seria calculado o resultado da fórmula
    // Para simplicidade, vamos apenas definir o valor da célula como a fórmula

    // Garantir que temos linhas e colunas suficientes
    ensureSheetDimensions(updatedData, Math.max(endRow, resultRow), Math.max(endCol, resultCol));

    // Definir a fórmula na célula de resultado
    updatedData.rows[resultRow - 1][resultCol - 1] = excelFormula;

    // Gerar resumo da operação
    const resultSummary = `Aplicada fórmula ${formula} no intervalo ${range} com resultado em ${resultCell}`;

    return { updatedData, resultSummary };
  } catch (error) {
    console.error('Erro ao executar operação de fórmula:', error);
    throw new Error(
      `Falha ao executar fórmula: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
    );
  }
}

/**
 * Converte referência de célula (ex: "A1") para índices de linha e coluna
 * @param cellRef Referência de célula (ex: "A1")
 * @returns Índices de linha e coluna
 */
function parseCellReference(cellRef: string): { row: number; col: number } {
  // Extrair parte alfabética (coluna) e numérica (linha)
  const match = cellRef.match(/([A-Za-z]+)([0-9]+)/);
  if (!match) {
    throw new Error(`Referência de célula inválida: ${cellRef}`);
  }

  const colStr = extractGroup(match, 1).toUpperCase();
  const rowStr = extractGroup(match, 2);

  if (!colStr || !rowStr) {
    throw new Error(`Referência de célula inválida: ${cellRef}`);
  }

  // Converter coluna de alfabética para numérica (A=1, B=2, ...)
  let colNum = 0;
  for (let i = 0; i < colStr.length; i++) {
    colNum = colNum * 26 + (colStr.charCodeAt(i) - 64);
  }

  // Converter linha para número
  const rowNum = parseInt(rowStr, 10);

  if (isNaN(rowNum) || rowNum <= 0) {
    throw new Error(`Número de linha inválido: ${rowStr}`);
  }

  return { row: rowNum, col: colNum };
}

/**
 * Converte range (ex: "A1:C3") para índices de linha e coluna
 * @param range Range de células (ex: "A1:C3")
 * @returns Índices de linha e coluna
 */
function parseRange(range: string): {
  startRow: number;
  startCol: number;
  endRow: number;
  endCol: number;
} {
  // Dividir o range em células de início e fim
  const parts = range.split(':');
  if (parts.length !== 2) {
    throw new Error(`Range inválido: ${range}`);
  }

  const startCell = safeArrayAccess(parts, 0);
  const endCell = safeArrayAccess(parts, 1);

  if (!startCell || !endCell) {
    throw new Error(`Range inválido: ${range}`);
  }

  // Converter cada célula para índices
  const start = parseCellReference(startCell);
  const end = parseCellReference(endCell);

  return {
    startRow: start.row,
    startCol: start.col,
    endRow: end.row,
    endCol: end.col,
  };
}

/**
 * Garante que a planilha tenha dimensões suficientes
 * @param sheetData Dados da planilha
 * @param maxRow Linha máxima necessária
 * @param maxCol Coluna máxima necessária
 */
function ensureSheetDimensions(sheetData: any, maxRow: number, maxCol: number): void {
  // Garantir que temos cabeçalhos suficientes
  while (sheetData.headers.length < maxCol) {
    const colLetter = String.fromCharCode(65 + sheetData.headers.length);
    sheetData.headers.push(colLetter);
  }

  // Garantir que temos linhas suficientes
  while (sheetData.rows.length < maxRow) {
    const newRow = Array(sheetData.headers.length).fill('');
    sheetData.rows.push(newRow);
  }

  // Garantir que cada linha tem células suficientes
  for (let i = 0; i < sheetData.rows.length; i++) {
    while (sheetData.rows[i].length < maxCol) {
      sheetData.rows[i].push('');
    }
  }
}
