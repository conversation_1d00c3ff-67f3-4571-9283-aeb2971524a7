#!/usr/bin/env node

/**
 * Script para automatizar a correção de variáveis não utilizadas
 * Adiciona prefixo '_' a variáveis reportadas pelo ESLint como não utilizadas
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const ts = require('typescript');

// Cores para console
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
};

// Executar o ESLint para identificar variáveis não utilizadas
function getUnusedVariables() {
  try {
    // Roda o ESLint com formato JSON para facilitar o processamento
    const result = execSync('npx eslint --ext .ts,.tsx,.js,.jsx --format json src/', {
      encoding: 'utf-8',
    });

    const lintResults = JSON.parse(result);

    // Filtrar apenas para regras de variáveis não utilizadas
    const unusedVars = [];

    lintResults.forEach(file => {
      file.messages.forEach(message => {
        if (
          message.ruleId === '@typescript-eslint/no-unused-vars' &&
          message.message &&
          typeof message.message === 'string' &&
          !message.message.includes('_') // Verificar se já tem prefixo '_'
        ) {
          unusedVars.push({
            filePath: file.filePath,
            line: message.line,
            column: message.column,
            varName: message.message.match(/['"]([^'"]+)['"]/)?.[1] || null,
          });
        }
      });
    });

    return unusedVars;
  } catch (error) {
    console.error(`${colors.red}Erro ao executar ESLint:${colors.reset}`, error.message);
    return [];
  }
}

// Processar um arquivo para adicionar o prefixo '_' às variáveis não utilizadas
function processFile(filePath, unusedVarsInFile) {
  try {
    // Ler o conteúdo do arquivo
    const fileContent = fs.readFileSync(filePath, 'utf-8');
    const sourceFile = ts.createSourceFile(filePath, fileContent, ts.ScriptTarget.Latest, true);

    // Processar as linhas do arquivo
    const lines = fileContent.split('\n');
    let modifiedContent = fileContent;

    // Agrupar variáveis por linha para evitar processamento repetido
    const varsByLine = {};
    unusedVarsInFile.forEach(varInfo => {
      if (!varsByLine[varInfo.line]) {
        varsByLine[varInfo.line] = [];
      }
      varsByLine[varInfo.line].push(varInfo);
    });

    // Para cada linha com variáveis não utilizadas
    Object.keys(varsByLine).forEach(lineNumber => {
      const lineIdx = parseInt(lineNumber) - 1;
      const originalLine = lines[lineIdx];
      let modifiedLine = originalLine;

      // Processar cada variável na linha
      varsByLine[lineNumber].forEach(varInfo => {
        if (varInfo.varName) {
          // Regex para identificar a variável corretamente
          const varRegex = new RegExp(`\\b${varInfo.varName}\\b(?!\\s*:)`, 'g');
          modifiedLine = modifiedLine.replace(varRegex, `_${varInfo.varName}`);
        }
      });

      // Substituir a linha modificada no conteúdo completo
      if (modifiedLine !== originalLine) {
        modifiedContent = modifiedContent.replace(originalLine, modifiedLine);
      }
    });

    // Salvar as modificações se houve alterações
    if (modifiedContent !== fileContent) {
      fs.writeFileSync(filePath, modifiedContent, 'utf-8');
      return true;
    }

    return false;
  } catch (error) {
    console.error(
      `${colors.red}Erro ao processar arquivo ${filePath}:${colors.reset}`,
      error.message
    );
    return false;
  }
}

// Função principal
function main() {
  console.log(`${colors.cyan}Iniciando correção de variáveis não utilizadas...${colors.reset}`);

  const unusedVars = getUnusedVariables();
  if (unusedVars.length === 0) {
    console.log(`${colors.green}Não foram encontradas variáveis não utilizadas!${colors.reset}`);
    return;
  }

  console.log(
    `${colors.yellow}Encontradas ${unusedVars.length} variáveis não utilizadas${colors.reset}`
  );

  // Agrupar variáveis por arquivo
  const fileGroups = {};
  unusedVars.forEach(varInfo => {
    if (!fileGroups[varInfo.filePath]) {
      fileGroups[varInfo.filePath] = [];
    }
    fileGroups[varInfo.filePath].push(varInfo);
  });

  // Processar cada arquivo
  let totalFixed = 0;
  Object.keys(fileGroups).forEach(filePath => {
    console.log(`${colors.blue}Processando ${filePath}...${colors.reset}`);
    const fixed = processFile(filePath, fileGroups[filePath]);
    if (fixed) {
      console.log(`${colors.green}✓ Corrigido${colors.reset}`);
      totalFixed++;
    } else {
      console.log(`${colors.yellow}✗ Sem alterações${colors.reset}`);
    }
  });

  console.log(`${colors.cyan}Concluído! ${totalFixed} arquivos corrigidos.${colors.reset}`);
}

main();
