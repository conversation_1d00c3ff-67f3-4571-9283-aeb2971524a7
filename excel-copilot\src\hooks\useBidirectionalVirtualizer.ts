import { useVirtualizer, type VirtualItem } from '@tanstack/react-virtual';
import { useRef, useCallback, useMemo } from 'react';

interface BidirectionalVirtualizerOptions {
  rowCount: number;
  columnCount: number;
  containerRef: React.RefObject<HTMLElement>;
  estimateRowHeight?: number;
  estimateColumnWidth?: number;
  overscanRows?: number;
  overscanColumns?: number;
  rowPadding?: number;
  columnPadding?: number;
}

// Interfaces removidas pois não são utilizadas
// interface RowVirtualItem extends VirtualItem {
//   height: number;
// }

// interface ColumnVirtualItem extends VirtualItem {
//   width: number;
// }

// Extender o VirtualItem para incluir a prop isScrolling
interface ExtendedVirtualItem extends VirtualItem {
  isScrolling?: boolean;
}

/**
 * Hook para virtualização bidimensional (linhas e colunas)
 * Permite renderizar apenas células visíveis em planilhas enormes
 */
export function useBidirectionalVirtualizer({
  rowCount,
  columnCount,
  containerRef,
  estimateRowHeight = 36,
  estimateColumnWidth = 120,
  overscanRows = 5,
  overscanColumns = 3,
  rowPadding = 0,
  columnPadding = 0,
}: BidirectionalVirtualizerOptions) {
  // Cria virtualizadores para linhas e colunas
  const rowVirtualizer = useVirtualizer({
    count: rowCount,
    getScrollElement: () => containerRef.current,
    estimateSize: () => estimateRowHeight + rowPadding,
    overscan: overscanRows,
  });

  const columnVirtualizer = useVirtualizer({
    count: columnCount,
    getScrollElement: () => containerRef.current,
    estimateSize: () => estimateColumnWidth + columnPadding,
    horizontal: true,
    overscan: overscanColumns,
  });

  // Referência para o container para medições
  const dimensions = useRef({ width: 0, height: 0 });

  // Atualizar dimensões a cada rerenderização
  if (containerRef.current) {
    dimensions.current = {
      width: containerRef.current.clientWidth,
      height: containerRef.current.clientHeight,
    };
  }

  // Calcular tamanho total da grade (altura x largura)
  const totalSize = useMemo(() => {
    return {
      height: rowVirtualizer.getTotalSize(),
      width: columnVirtualizer.getTotalSize(),
    };
  }, [rowVirtualizer, columnVirtualizer]);

  // Obter células virtuais que estão atualmente na viewport
  const getVirtualItems = useCallback(() => {
    const virtualRows = rowVirtualizer.getVirtualItems() as ExtendedVirtualItem[];
    const virtualColumns = columnVirtualizer.getVirtualItems() as ExtendedVirtualItem[];

    // Criar mapa de células visíveis para renderizar apenas a interseção
    const visibleCells: Array<{
      rowIndex: number;
      colIndex: number;
      top: number;
      left: number;
      width: number;
      height: number;
      isScrolling: boolean;
    }> = [];

    for (const row of virtualRows) {
      for (const column of virtualColumns) {
        visibleCells.push({
          rowIndex: row.index,
          colIndex: column.index,
          top: row.start,
          left: column.start,
          width: column.size,
          height: row.size,
          isScrolling: Boolean(row.isScrolling || column.isScrolling),
        });
      }
    }

    return {
      cells: visibleCells,
      rows: virtualRows,
      columns: virtualColumns,
    };
  }, [rowVirtualizer, columnVirtualizer]);

  // Função para rolar para uma célula específica
  const scrollToCell = useCallback(
    (rowIndex: number, columnIndex: number, options?: { align?: 'start' | 'center' | 'end' }) => {
      rowVirtualizer.scrollToIndex(rowIndex, { align: options?.align || 'start' });
      columnVirtualizer.scrollToIndex(columnIndex, { align: options?.align || 'start' });
    },
    [rowVirtualizer, columnVirtualizer]
  );

  // Verificar se está rolando
  const isScrolling = useMemo(() => {
    return (
      (rowVirtualizer.getVirtualItems() as ExtendedVirtualItem[]).some(item => item.isScrolling) ||
      (columnVirtualizer.getVirtualItems() as ExtendedVirtualItem[]).some(item => item.isScrolling)
    );
  }, [rowVirtualizer, columnVirtualizer]);

  // Obter índices de células visíveis (útil para pré-carregar dados)
  const getVisibleRange = useCallback(() => {
    const rows = rowVirtualizer.getVirtualItems();
    const cols = columnVirtualizer.getVirtualItems();

    // Verificar se temos itens virtuais e se os primeiros e últimos elementos existem
    const firstRow = rows.length > 0 ? rows[0] : null;
    const lastRow = rows.length > 0 ? rows[rows.length - 1] : null;
    const firstCol = cols.length > 0 ? cols[0] : null;
    const lastCol = cols.length > 0 ? cols[cols.length - 1] : null;

    return {
      rowStartIndex: firstRow ? firstRow.index : 0,
      rowEndIndex: lastRow ? lastRow.index : 0,
      colStartIndex: firstCol ? firstCol.index : 0,
      colEndIndex: lastCol ? lastCol.index : 0,
    };
  }, [rowVirtualizer, columnVirtualizer]);

  // Obter dimensões da viewport
  const getViewportDimensions = useCallback(() => {
    return {
      width: dimensions.current.width,
      height: dimensions.current.height,
    };
  }, []);

  // Obter tamanho de célula para célula específica
  const getCellBounds = useCallback(
    (rowIndex: number, colIndex: number) => {
      // Usar método get direto em vez de measureElement que recebe um Element
      const rowSize = estimateRowHeight;
      const rowStart = rowIndex * (estimateRowHeight + rowPadding);

      const colSize = estimateColumnWidth;
      const colStart = colIndex * (estimateColumnWidth + columnPadding);

      return {
        top: rowStart,
        left: colStart,
        width: colSize,
        height: rowSize,
      };
    },
    [estimateRowHeight, estimateColumnWidth, rowPadding, columnPadding]
  );

  return {
    rowVirtualizer,
    columnVirtualizer,
    getVirtualItems,
    scrollToCell,
    totalSize,
    isScrolling,
    getVisibleRange,
    getViewportDimensions,
    getCellBounds,
  };
}
