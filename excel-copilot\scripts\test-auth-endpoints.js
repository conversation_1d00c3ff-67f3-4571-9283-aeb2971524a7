#!/usr/bin/env node

/**
 * Script para testar endpoints de autenticação localmente
 */

const http = require('http');

console.log('🔍 TESTANDO ENDPOINTS DE AUTENTICAÇÃO LOCAL\n');

// Função para fazer requisição HTTP
function makeRequest(path, method = 'GET') {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: path,
      method: method,
      headers: {
        'User-Agent': 'OAuth-Test-Script/1.0',
      },
    };

    const req = http.request(options, res => {
      let data = '';

      res.on('data', chunk => {
        data += chunk;
      });

      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          data: data,
        });
      });
    });

    req.on('error', err => {
      reject(err);
    });

    req.setTimeout(5000, () => {
      req.destroy();
      reject(new Error('Timeout'));
    });

    req.end();
  });
}

// Testar endpoints
async function testEndpoints() {
  const endpoints = [
    '/api/auth/providers',
    '/api/auth/csrf',
    '/api/auth/session',
    '/api/auth/signin/google',
    '/api/auth/signin/github',
  ];

  for (const endpoint of endpoints) {
    try {
      console.log(`🔄 Testando: ${endpoint}`);
      const response = await makeRequest(endpoint);

      console.log(`   Status: ${response.statusCode}`);

      if (endpoint === '/api/auth/providers') {
        try {
          const providers = JSON.parse(response.data);
          console.log('   Providers disponíveis:', Object.keys(providers));
        } catch (e) {
          console.log('   Resposta não é JSON válido');
        }
      }

      if (endpoint.includes('/signin/')) {
        if (response.statusCode === 302) {
          const location = response.headers.location;
          console.log('   Redirecionamento para:', location);

          // Verificar se está redirecionando para localhost ou produção
          if (location && location.includes('localhost:3000')) {
            console.log('   ✅ Redirecionamento correto para localhost');
          } else if (location && location.includes('excel-copilot-eight.vercel.app')) {
            console.log('   ❌ PROBLEMA: Redirecionando para produção em vez de localhost');
          } else if (location && location.includes('accounts.google.com')) {
            console.log('   ✅ Redirecionamento para Google OAuth');
          } else if (location && location.includes('github.com')) {
            console.log('   ✅ Redirecionamento para GitHub OAuth');
          }
        }
      }

      console.log('   ✅ OK\n');
    } catch (error) {
      console.log(`   ❌ Erro: ${error.message}\n`);
    }
  }
}

// Verificar se o servidor está rodando
async function checkServer() {
  try {
    console.log('🔄 Verificando se o servidor está rodando...');
    await makeRequest('/');
    console.log('✅ Servidor está rodando em http://localhost:3000\n');
    return true;
  } catch (error) {
    console.log('❌ Servidor não está rodando em http://localhost:3000');
    console.log('💡 Execute: npm run dev\n');
    return false;
  }
}

// Executar testes
async function main() {
  const serverRunning = await checkServer();

  if (serverRunning) {
    await testEndpoints();

    console.log('📋 RESUMO:');
    console.log('1. Se os endpoints de signin estão redirecionando para produção,');
    console.log('   verifique se NEXTAUTH_URL está configurado como http://localhost:3000');
    console.log('2. Se o Google OAuth não funciona, verifique se localhost:3000');
    console.log('   está configurado no Google Cloud Console');
    console.log('3. Reinicie o servidor após mudanças nas variáveis de ambiente');
  }
}

main().catch(console.error);
