const fs = require('fs');
const path = require('path');

// Caminho para os arquivos de configuração
const testConfigPath = path.join(__dirname, '..', 'tsconfig.test.json');

// Configuração TypeScript específica para testes com verificações mais flexíveis
const testConfig = {
  extends: './tsconfig.json',
  compilerOptions: {
    noImplicitAny: false,
    strictNullChecks: false,
    skipLibCheck: true,
    noEmit: true,
    allowJs: true,
    checkJs: false,
    isolatedModules: false,
    downlevelIteration: true,
    moduleResolution: 'node16',
    esModuleInterop: true,
    allowSyntheticDefaultImports: true,
  },
  include: ['__tests__/**/*'],
  exclude: ['node_modules'],
};

// Atualizar o arquivo de configuração de testes
fs.writeFileSync(testConfigPath, JSON.stringify(testConfig, null, 2));

console.log('✅ Configuração de tipos para testes atualizada.');
console.log('Para executar verificação de tipos apenas no código de produção (ignorando testes):');
console.log('npx tsc --noEmit --skipLibCheck --project tsconfig.json');
console.log('\nPara executar verificação flexível nos arquivos de teste:');
console.log('npx tsc --noEmit --project tsconfig.test.json');

// Verificar se há um script no package.json
const packageJsonPath = path.join(__dirname, '..', 'package.json');
const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));

if (!packageJson.scripts['typecheck:prod']) {
  console.log('\n⚠️ Recomendação: Adicione estes scripts ao seu package.json:');
  console.log(`
"scripts": {
  "typecheck:prod": "tsc --noEmit --skipLibCheck --project tsconfig.json",
  "typecheck:tests": "tsc --noEmit --project tsconfig.test.json",
  "fix:test-types": "node scripts/fix-test-types.js"
}
  `);
}
