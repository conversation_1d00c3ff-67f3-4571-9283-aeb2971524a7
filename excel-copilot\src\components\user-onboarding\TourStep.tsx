'use client';

import { motion, AnimatePresence } from 'framer-motion';
import { ChevronRight, ChevronLeft, X } from 'lucide-react';
import React, { useRef, useEffect, useState } from 'react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { cn } from '@/lib/utils';

import { TourStep as TourStepType } from './TourProvider';

interface TourStepProps {
  step: TourStepType;
  isFirst: boolean;
  isLast: boolean;
  onNext: () => void;
  onPrev: () => void;
  onClose: () => void;
}

export function TourStep({ step, isFirst, isLast, onNext, onPrev, onClose }: TourStepProps) {
  const [position, setPosition] = useState({ top: 0, left: 0 });
  const [arrowPosition, setArrowPosition] = useState<'top' | 'right' | 'bottom' | 'left'>('bottom');
  const [ready, setReady] = useState(false);
  const tooltipRef = useRef<HTMLDivElement>(null);

  // Posicionar o tooltip em relação ao elemento alvo
  useEffect(() => {
    const calculatePosition = () => {
      const targetElement = document.querySelector(step.target);
      if (!targetElement || !tooltipRef.current) return;

      const targetRect = targetElement.getBoundingClientRect();
      const tooltipRect = tooltipRef.current.getBoundingClientRect();

      // Determinar a melhor posição baseada no placement ou no espaço disponível
      const placement = step.placement || 'bottom';

      let top = 0;
      let left = 0;
      let arrow: 'top' | 'right' | 'bottom' | 'left' = placement;

      switch (placement) {
        case 'top':
          top = targetRect.top - tooltipRect.height - 10;
          left = targetRect.left + targetRect.width / 2 - tooltipRect.width / 2;
          arrow = 'bottom';
          break;
        case 'right':
          top = targetRect.top + targetRect.height / 2 - tooltipRect.height / 2;
          left = targetRect.right + 10;
          arrow = 'left';
          break;
        case 'bottom':
          top = targetRect.bottom + 10;
          left = targetRect.left + targetRect.width / 2 - tooltipRect.width / 2;
          arrow = 'top';
          break;
        case 'left':
          top = targetRect.top + targetRect.height / 2 - tooltipRect.height / 2;
          left = targetRect.left - tooltipRect.width - 10;
          arrow = 'right';
          break;
      }

      // Ajuste para manter dentro da viewport
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;

      // Evitar sair da tela horizontalmente
      if (left < 10) left = 10;
      if (left + tooltipRect.width > viewportWidth - 10) {
        left = viewportWidth - tooltipRect.width - 10;
      }

      // Evitar sair da tela verticalmente
      if (top < 10) top = 10;
      if (top + tooltipRect.height > viewportHeight - 10) {
        top = viewportHeight - tooltipRect.height - 10;
      }

      setPosition({ top, left });
      setArrowPosition(arrow);

      // Destacar o elemento alvo
      targetElement.classList.add('tour-highlight');

      // Marcar como pronto para animação
      setReady(true);

      return () => {
        targetElement.classList.remove('tour-highlight');
      };
    };

    // Calcular posição inicial
    calculatePosition();

    // Recalcular quando a janela for redimensionada
    window.addEventListener('resize', calculatePosition);

    return () => {
      window.removeEventListener('resize', calculatePosition);

      // Limpar destaque do elemento ao desmontar
      const targetElement = document.querySelector(step.target);
      if (targetElement) {
        targetElement.classList.remove('tour-highlight');
      }
    };
  }, [step]);

  // Executar ação do passo quando ele for exibido
  useEffect(() => {
    if (step.action) {
      step.action();
    }
  }, [step]);

  // Criar overlay para escurecer o resto da página
  return (
    <>
      {/* Overlay semitransparente */}
      <div className="fixed inset-0 bg-black/40 z-50" onClick={() => onClose()} />

      {/* Tooltip */}
      <AnimatePresence>
        {ready && (
          <motion.div
            ref={tooltipRef}
            className="fixed z-[60]"
            style={{ top: position.top, left: position.left }}
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            transition={{ duration: 0.2 }}
          >
            <Card className="w-80 shadow-lg">
              <CardHeader className="pb-2">
                <CardTitle className="text-lg flex items-center justify-between">
                  {step.title}
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-6 w-6"
                    onClick={() => onClose()}
                    aria-label="Fechar tour"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">{step.content}</p>
              </CardContent>
              <CardFooter className="flex justify-between pt-2">
                <div>
                  {!isFirst && (
                    <Button variant="outline" size="sm" onClick={() => onPrev()} className="mr-2">
                      <ChevronLeft className="h-4 w-4 mr-1" />
                      Anterior
                    </Button>
                  )}
                </div>
                <Button variant="default" size="sm" onClick={() => onNext()}>
                  {isLast ? 'Concluir' : 'Próximo'}
                  {!isLast && <ChevronRight className="h-4 w-4 ml-1" />}
                </Button>
              </CardFooter>

              {/* Seta apontando para o elemento */}
              <div
                className={cn('absolute w-3 h-3 bg-card rotate-45 transform', {
                  'top-0 left-1/2 -translate-x-1/2 -translate-y-1/2': arrowPosition === 'top',
                  'bottom-0 left-1/2 -translate-x-1/2 translate-y-1/2': arrowPosition === 'bottom',
                  'left-0 top-1/2 -translate-x-1/2 -translate-y-1/2': arrowPosition === 'left',
                  'right-0 top-1/2 translate-x-1/2 -translate-y-1/2': arrowPosition === 'right',
                })}
              />
            </Card>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
}
