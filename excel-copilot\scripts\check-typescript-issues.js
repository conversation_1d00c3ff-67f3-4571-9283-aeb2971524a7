#!/usr/bin/env node

/**
 * Script para verificar problemas de tipagem TypeScript restantes
 * Identifica padrões problemáticos que ainda precisam ser corrigidos
 */

const fs = require('fs');
const path = require('path');
const chalk = require('chalk');

// Padrões problemáticos a serem verificados
const PROBLEMATIC_PATTERNS = [
  {
    name: 'Acesso inseguro a match[index]',
    pattern: /match\[(\d+)\](?!\s*\?)/g,
    severity: 'high',
    suggestion: 'Use extractGroup(match, index) em vez de match[index]',
  },
  {
    name: 'Acesso direto a array sem verificação',
    pattern: /(\w+)\[(\d+)\](?!\s*\?)/g,
    severity: 'medium',
    suggestion: 'Use safeArrayAccess(array, index) para acesso seguro',
    exclude: ['charAt', 'charCodeAt'], // Excluir métodos de string
  },
  {
    name: 'Uso de any em tipos',
    pattern: /:\s*any(?!\[\])/g,
    severity: 'medium',
    suggestion: 'Substitua any por unknown ou tipo específico',
  },
  {
    name: '<PERSON><PERSON>ried<PERSON> undefined em objetos',
    pattern: /\{\s*[^}]*:\s*undefined[^}]*\}/g,
    severity: 'low',
    suggestion: 'Use makeExactOptional() para compatibilidade com exactOptionalPropertyTypes',
  },
  {
    name: 'Eventos sem tipagem',
    pattern: /on\w+\s*=\s*\{[^}]*\}/g,
    severity: 'medium',
    suggestion: 'Use SafeEventHandler<T> para tipagem de eventos',
  },
];

// Diretórios a serem verificados
const DIRECTORIES_TO_CHECK = [
  'src/lib/operations',
  'src/components',
  'src/app/api',
  'src/utils',
  'src/types',
];

// Extensões de arquivo a serem verificadas
const FILE_EXTENSIONS = ['.ts', '.tsx'];

/**
 * Verifica se um arquivo deve ser incluído na verificação
 */
function shouldCheckFile(filePath) {
  const ext = path.extname(filePath);
  return FILE_EXTENSIONS.includes(ext) && !filePath.includes('node_modules');
}

/**
 * Lê todos os arquivos de um diretório recursivamente
 */
function getAllFiles(dirPath, arrayOfFiles = []) {
  if (!fs.existsSync(dirPath)) {
    return arrayOfFiles;
  }

  const files = fs.readdirSync(dirPath);

  files.forEach(file => {
    const fullPath = path.join(dirPath, file);
    if (fs.statSync(fullPath).isDirectory()) {
      arrayOfFiles = getAllFiles(fullPath, arrayOfFiles);
    } else if (shouldCheckFile(fullPath)) {
      arrayOfFiles.push(fullPath);
    }
  });

  return arrayOfFiles;
}

/**
 * Verifica um arquivo em busca de padrões problemáticos
 */
function checkFile(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  const lines = content.split('\n');
  const issues = [];

  PROBLEMATIC_PATTERNS.forEach(({ name, pattern, severity, suggestion, exclude = [] }) => {
    let match;
    const regex = new RegExp(pattern.source, pattern.flags);

    while ((match = regex.exec(content)) !== null) {
      // Verificar exclusões
      if (exclude.some(excludePattern => match[0].includes(excludePattern))) {
        continue;
      }

      const lineNumber = content.substring(0, match.index).split('\n').length;
      const lineContent = lines[lineNumber - 1]?.trim() || '';

      issues.push({
        file: filePath,
        line: lineNumber,
        column: match.index - content.lastIndexOf('\n', match.index - 1),
        pattern: name,
        severity,
        suggestion,
        lineContent,
        match: match[0],
      });
    }
  });

  return issues;
}

/**
 * Formata e exibe os resultados
 */
function displayResults(allIssues) {
  if (allIssues.length === 0) {
    console.log(chalk.green('✅ Nenhum problema de tipagem encontrado!'));
    return;
  }

  console.log(chalk.yellow(`⚠️  Encontrados ${allIssues.length} problemas de tipagem:\n`));

  // Agrupar por severidade
  const groupedBySeverity = allIssues.reduce((acc, issue) => {
    if (!acc[issue.severity]) acc[issue.severity] = [];
    acc[issue.severity].push(issue);
    return acc;
  }, {});

  // Exibir por severidade
  ['high', 'medium', 'low'].forEach(severity => {
    const issues = groupedBySeverity[severity] || [];
    if (issues.length === 0) return;

    const severityColor = severity === 'high' ? 'red' : severity === 'medium' ? 'yellow' : 'blue';
    console.log(
      chalk[severityColor](`\n🔴 ${severity.toUpperCase()} (${issues.length} problemas):`)
    );

    issues.forEach((issue, index) => {
      console.log(`\n${index + 1}. ${chalk.cyan(issue.pattern)}`);
      console.log(`   📁 ${issue.file}:${issue.line}:${issue.column}`);
      console.log(`   📝 ${issue.lineContent}`);
      console.log(`   🔍 Encontrado: ${chalk.red(issue.match)}`);
      console.log(`   💡 ${issue.suggestion}`);
    });
  });

  // Resumo por arquivo
  console.log(chalk.blue('\n📊 Resumo por arquivo:'));
  const fileStats = allIssues.reduce((acc, issue) => {
    if (!acc[issue.file]) acc[issue.file] = 0;
    acc[issue.file]++;
    return acc;
  }, {});

  Object.entries(fileStats)
    .sort(([, a], [, b]) => b - a)
    .slice(0, 10)
    .forEach(([file, count]) => {
      console.log(`   ${count} problemas - ${file}`);
    });
}

/**
 * Função principal
 */
function main() {
  console.log(chalk.blue('🔍 Verificando problemas de tipagem TypeScript...\n'));

  let allIssues = [];

  DIRECTORIES_TO_CHECK.forEach(dir => {
    const fullPath = path.join(process.cwd(), dir);
    console.log(`Verificando: ${dir}`);

    const files = getAllFiles(fullPath);
    files.forEach(file => {
      const issues = checkFile(file);
      allIssues = allIssues.concat(issues);
    });
  });

  displayResults(allIssues);

  // Sugestões gerais
  console.log(chalk.blue('\n📋 Próximos passos recomendados:'));
  console.log('1. Execute: npx tsc --noEmit para verificação completa');
  console.log('2. Use os utilitários em src/utils/type-helpers.ts');
  console.log('3. Consulte TYPESCRIPT_FIXES_IMPLEMENTED.md para padrões');
  console.log('4. Execute: npm run lint para verificar outras questões');

  // Código de saída
  process.exit(allIssues.length > 0 ? 1 : 0);
}

// Executar se chamado diretamente
if (require.main === module) {
  main();
}

module.exports = { checkFile, getAllFiles, PROBLEMATIC_PATTERNS };
