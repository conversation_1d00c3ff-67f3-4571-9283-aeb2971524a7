import { z } from 'zod';

/**
 * Schemas relacionados a workbooks e sheets
 */

// Schema para IDs de workbook
export const workbookIdSchema = z.object({
  id: z.string().cuid({ message: 'ID de workbook inválido' }),
});

// Schema para criação de workbook
export const createWorkbookSchema = z.object({
  name: z
    .string()
    .min(1, { message: 'Nome da planilha é obrigatório' })
    .max(100, { message: 'Nome da planilha deve ter no máximo 100 caracteres' }),
  description: z
    .string()
    .max(500, { message: 'Descrição deve ter no máximo 500 caracteres' })
    .optional(),
  isPublic: z.boolean().default(false),
  initialData: z.any().optional(),
  aiCommand: z
    .string()
    .max(1000, { message: 'Comando de IA deve ter no máximo 1000 caracteres' })
    .optional(),
});

// Schema para atualização de workbook
export const updateWorkbookSchema = z.object({
  id: z.string().cuid({ message: 'ID de workbook inválido' }),
  name: z
    .string()
    .min(1, { message: 'Nome da planilha é obrigatório' })
    .max(100, { message: 'Nome da planilha deve ter no máximo 100 caracteres' })
    .optional(),
  description: z
    .string()
    .max(500, { message: 'Descrição deve ter no máximo 500 caracteres' })
    .optional(),
  isPublic: z.boolean().optional(),
});

// Schema para criação de sheet
export const createSheetSchema = z.object({
  name: z
    .string()
    .min(1, { message: 'Nome da folha é obrigatório' })
    .max(50, { message: 'Nome da folha deve ter no máximo 50 caracteres' }),
  workbookId: z.string().cuid({ message: 'ID de workbook inválido' }),
  data: z.any().optional(),
});

// Schema para filtro de workbooks
export const workbookFilterSchema = z.object({
  isPublic: z.boolean().optional(),
  search: z.string().optional(),
  limit: z.number().int().positive().default(10),
  page: z.number().int().nonnegative().default(0),
});

// Schema para estrutura básica de célula
export const cellSchema = z.object({
  row: z.number().int().nonnegative(),
  col: z.number().int().nonnegative(),
  value: z.any(),
  formula: z.string().optional(),
  style: z.any().optional(),
});

// Tipos inferidos para uso na aplicação
export type WorkbookId = z.infer<typeof workbookIdSchema>;
export type CreateWorkbookInput = z.infer<typeof createWorkbookSchema>;
export type UpdateWorkbookInput = z.infer<typeof updateWorkbookSchema>;
export type CreateSheetInput = z.infer<typeof createSheetSchema>;
export type WorkbookFilter = z.infer<typeof workbookFilterSchema>;
export type CellData = z.infer<typeof cellSchema>;
