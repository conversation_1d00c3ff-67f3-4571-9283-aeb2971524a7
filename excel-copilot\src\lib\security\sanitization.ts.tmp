/**
 * <PERSON>ódulo de sanitização de entrada para prevenir vulnerabilidades de segurança
 * Implementa funções para sanitizar diferentes tipos de dados e prevenir ataques
 * como XSS, injeção SQL, etc.
 */

import { z } from 'zod';
import { ErrorType, AppError } from '@/lib/errors';

/**
 * Interface para resultado de validação
 */
export interface SanitizationResult<T = any> {
  isValid: boolean;
  sanitized?: T;
  originalValue?: any;
  errors?: string[];
}

/**
 * Sanitiza strings para prevenir ataques XSS
 * @param input String para sanitizar
 * @returns String sanitizada
 */
export function sanitizeHtml(input: string): string {
  if (!input || typeof input !== 'string') return '';
  
  // Substituir caracteres perigosos por entidades HTML
  return input
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#x27;')
    .replace(/\//g, '&#x2F;')
    .replace(/`/g, '&#96;')
    .replace(/\$/g, '&#36;')  // Escapa $ para prevenir interpolação maliciosa
    .replace(/{/g, '&#123;')  // Escapa { para prevenir template injection
    .replace(/}/g, '&#125;'); // Escapa } para prevenir template injection
}

/**
 * Remove tags HTML de uma string
 * @param input String para limpar
 * @returns String sem tags HTML
 */
export function stripHtml(input: string): string {
  if (!input || typeof input !== 'string') return '';
  
  // Remover todas as tags HTML
  return input.replace(/<[^>]*>/g, '');
}

/**
 * Sanitiza string para SQL prevenindo injeção
 * @param input String para sanitizar
 * @returns String sanitizada
 */
export function sanitizeSql(input: string): string {
  if (!input || typeof input !== 'string') return '';
  
  // Escapar caracteres especiais SQL
  return input
    .replace(/'/g, "''")
    .replace(/\\/g, '\\\\')
    .replace(/\0/g, '\\0')
    .replace(/\n/g, '\\n')
    .replace(/\r/g, '\\r')
    .replace(/\t/g, '\\t')
    .replace(/Z/g, '\\Z'); // Substituir pela letra Z ao invés do caractere de controle
}

/**
 * Verifica se uma string contém apenas caracteres alfanuméricos e alguns símbolos específicos
 * @param input String para verificar
 * @param allowedChars Caracteres adicionais permitidos além de alfanuméricos
 * @returns Verdadeiro se a string for segura
 */
export function isAlphanumericSafe(input: string, allowedChars: string = '-_. '): boolean {
  if (!input || typeof input !== 'string') return false;
  
  const safeRegex = new RegExp(`^[a-zA-Z0-9${allowedChars.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&')}]+$`);
  return safeRegex.test(input);
}

/**
 * Verifica se uma string contém padrões suspeitos que podem indicar tentativas de injeção
 * @param input String para verificar
 * @returns Verdadeiro se encontrar padrões suspeitos
 */
export function hasSuspiciousPatterns(input: string): boolean {
  if (!input || typeof input !== 'string') return false;
  
  const suspiciousPatterns = [
    // Padrões de injeção SQL
    /(\%27)|(\')|(\-\-)|(\%23)|(#)/i,
    /((\%3D)|(=))[^\n]*((\%27)|(\')|(\-\-)|(\%3B)|(;))/i,
    /\w*((\%27)|(\'))((\%6F)|o|(\%4F))((\%72)|r|(\%52))/i,
    /(union).*(select)/i,
    
    // Padrões XSS
    /((\%3C)|<)((\%2F)|\/)*[a-z0-9\%]+((\%3E)|>)/i,
    /((\%3C)|<)((\%69)|i|(\%49))((\%6D)|m|(\%4D))((\%67)|g|(\%47))[^\n]+((\%3E)|>)/i,
    /((\%3C)|<)[^\n]+((\%3E)|>)/i,
    
    // Padrões de injeção de comando
    /\|\s*[\w\-]+/i,
    /;\s*[\w\-]+/i,
    /`\s*[\w\-]+/i
  ];
  
  return suspiciousPatterns.some(pattern => pattern.test(input));
}

/**
 * Valida e sanitiza objetos usando Zod
 * @param input Dados para validar e sanitizar
 * @param schema Schema Zod para validação
 * @returns Resultado da sanitização
 */
export function validateAndSanitize<T>(
  input: unknown, 
  schema: z.Schema<T>
): SanitizationResult<T> {
  try {
    const result = schema.safeParse(input);
    
    if (result.success) {
      return {
        isValid: true,
        sanitized: result.data,
        originalValue: input
      };
    } else {
      return {
        isValid: false,
        originalValue: input,
        errors: result.error.errors.map(e => `${e.path.join('.')}: ${e.message}`)
      };
    }
  } catch (error) {
    return {
      isValid: false,
      originalValue: input,
      errors: [error instanceof Error ? error.message : 'Erro de validação desconhecido']
    };
  }
}

/**
 * Sanitiza recursivamente todas as strings em um objeto
 * @param obj Objeto para sanitizar
 * @returns Objeto com todas as strings sanitizadas
 */
export function deepSanitizeObject<T extends Record<string, any>>(obj: T): T {
  if (!obj || typeof obj !== 'object') return obj;
  
  const result: Record<string, any> = {};
  
  for (const [key, value] of Object.entries(obj)) {
    if (typeof value === 'string') {
      result[key] = sanitizeHtml(value);
    } else if (Array.isArray(value)) {
      result[key] = value.map(item => 
        typeof item === 'string' 
          ? sanitizeHtml(item) 
          : typeof item === 'object' && item !== null
            ? deepSanitizeObject(item)
            : item
      );
    } else if (typeof value === 'object' && value !== null) {
      result[key] = deepSanitizeObject(value);
    } else {
      result[key] = value;
    }
  }
  
  return result as T;
}

/**
 * Cria uma função de validação compatível com o middleware withValidation
 * @param schema Schema Zod para validação
 * @returns Função de validação
 */
export function createZodValidator<T>(schema: z.Schema<T>) {
  return (body: unknown) => {
    const result = validateAndSanitize(body, schema);
    
    return {
      isValid: result.isValid,
      data: result.sanitized,
      errors: result.errors,
      message: result.errors?.join('; ')
    };
  };
}

/**
 * Sanitiza URL para prevenir injeção de script
 * @param url URL para sanitizar
 * @returns URL sanitizada ou null se for inválida
 */
export function sanitizeUrl(url: string): string | null {
  if (!url || typeof url !== 'string') return null;
  
  // Verificar protocolos permitidos
  const validProtocols = ['http:', 'https:', 'mailto:', 'tel:'];
  
  try {
    const parsedUrl = new URL(url);
    if (!validProtocols.includes(parsedUrl.protocol)) {
      return null;
    }
    
    return url;
  } catch (e) {
    // Se não for uma URL válida, verificar se é uma rota relativa
    if (url.startsWith('/') && !url.includes(':')) {
      return url;
    }
    
    return null;
  }
}

/**
 * Verifica se uma string possui tamanho válido
 * @param input String para verificar
 * @param minLength Tamanho mínimo (padrão: 1)
 * @param maxLength Tamanho máximo (padrão: 1000)
 * @returns Verdadeiro se o tamanho estiver dentro dos limites
 */
export function hasValidLength(
  input: string, 
  minLength: number = 1, 
  maxLength: number = 1000
): boolean {
  if (typeof input !== 'string') return false;
  return input.length >= minLength && input.length <= maxLength;
}

/**
 * Sanitiza nome de arquivo para evitar traversal de diretório
 * @param filename Nome do arquivo para sanitizar
 * @returns Nome de arquivo sanitizado
 */
export function sanitizeFilename(filename: string): string {
  if (!filename || typeof filename !== 'string') return '';
  
  // Remover caracteres perigosos e limitar a caracteres seguros
  return filename
    .replace(/[\/\\]/g, '_') // Substituir / e \ por _
    .replace(/\.\./g, '_')   // Substituir .. por _
    .replace(/[&<>:"/|?*]/g, '_') // Substituir outros caracteres inválidos
    .slice(0, 255);         // Limitar tamanho
}

/**
 * Função para validar e sanitizar formulários
 * @param data Dados do formulário
 * @param fields Configuração dos campos
 * @returns Dados sanitizados ou erro
 */
export function sanitizeFormData(
  data: Record<string, any>,
  fields: Record<string, { 
    required?: boolean; 
    type?: 'string' | 'number' | 'boolean' | 'email' | 'url' | 'date'; 
    maxLength?: number;
    minLength?: number;
    pattern?: RegExp;
  }>
): Record<string, any> {
  const sanitized: Record<string, any> = {};
  const errors: string[] = [];

  for (const [fieldName, config] of Object.entries(fields)) {
    // Extrair valor do campo
    const value = data[fieldName];
    
    // Verificar se é requerido
    if (config.required && (value === null || value === undefined || value === '')) {
      errors.push(`Campo ${fieldName} é obrigatório`);
      continue;
    }
    
    // Se valor é nulo ou indefinido e não é requerido, pular
    if (value === null || value === undefined) {
      continue;
    }
    
    // Sanitizar com base no tipo
    let num;
    switch (config.type || 'string') {
      case 'string':
        if (typeof value !== 'string') {
          sanitized[fieldName] = String(value);
        } else {
          sanitized[fieldName] = sanitizeHtml(value);
          
          // Verificar comprimento se especificado
          if (config.minLength && value.length < config.minLength) {
            errors.push(`Campo ${fieldName} deve ter pelo menos ${config.minLength} caracteres`);
          }
          
          if (config.maxLength && value.length > config.maxLength) {
            errors.push(`Campo ${fieldName} deve ter no máximo ${config.maxLength} caracteres`);
          }
          
          // Verificar padrão se especificado
          if (config.pattern && !config.pattern.test(value)) {
            errors.push(`Campo ${fieldName} tem formato inválido`);
          }
        }
        break;
      
      case 'email':
        if (typeof value !== 'string') {
          errors.push(`Campo ${fieldName} deve ser uma string`);
        } else {
          const email = value.trim().toLowerCase();
          const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
          
          if (!emailRegex.test(email)) {
            errors.push(`Campo ${fieldName} deve ser um email válido`);
          } else {
            sanitized[fieldName] = email;
          }
        }
        break;
      
      case 'url':
        if (typeof value !== 'string') {
          errors.push(`Campo ${fieldName} deve ser uma string`);
        } else {
          const safeUrl = sanitizeUrl(value);
          if (safeUrl === null) {
            errors.push(`Campo ${fieldName} não é uma URL válida`);
          } else {
            sanitized[fieldName] = safeUrl;
          }
        }
        break;
      
      case 'number':
        num = Number(value);
        if (isNaN(num)) {
          errors.push(`Campo ${fieldName} deve ser um número`);
        } else {
          sanitized[fieldName] = num;
        }
        break;
      
      case 'boolean':
        sanitized[fieldName] = Boolean(value);
        break;
      
      case 'date':
        try {
          const date = new Date(value);
          if (isNaN(date.getTime())) {
            errors.push(`Campo ${fieldName} não é uma data válida`);
          } else {
            sanitized[fieldName] = date;
          }
        } catch (e) {
          errors.push(`Campo ${fieldName} não é uma data válida`);
        }
        break;
      
      default:
        // Para tipos não especificados, apenas copiar o valor
        sanitized[fieldName] = value;
    }
  }
  
  if (errors.length > 0) {
    throw new AppError(
      `Validação falhou: ${errors.join(', ')}`,
      ErrorType.VALIDATION_ERROR,
      400,
      { errors }
    );
  }
  
  return sanitized;
}

/**
 * Sanitiza fórmulas Excel para prevenir injeções maliciosas
 * @param formula Fórmula Excel para sanitizar
 * @returns Objeto com resultado da sanitização, fórmula sanitizada e flag de segurança
 */
export function sanitizeExcelFormula(formula: string | null): {
  isSafe: boolean;
  sanitized: string | null;
  reason?: string;
} {
  if (!formula) return { isSafe: true, sanitized: null };
  if (typeof formula !== 'string') return { isSafe: true, sanitized: null };
  
  // Lista de padrões de fórmulas potencialmente perigosas
  const dangerousPatterns = [
    // Fórmulas que podem executar comandos do sistema
    { pattern: /EXEC\(/i, reason: 'Função EXEC não permitida' },
    { pattern: /CMD\(/i, reason: 'Função CMD não permitida' },
    { pattern: /SHELL\(/i, reason: 'Função SHELL não permitida' },
    { pattern: /SYSTEM\(/i, reason: 'Função SYSTEM não permitida' },
    
    // Fórmulas que podem acessar a rede
    { pattern: /WEBSERVICE\(/i, reason: 'Função WEBSERVICE não permitida' },
    { pattern: /HYPERLINK\(/i, reason: 'Função HYPERLINK não permitida' },
    { pattern: /URL\(/i, reason: 'Função URL não permitida' },
    
    // Fórmulas que podem manipular arquivos
    { pattern: /FILEOPEN\(/i, reason: 'Função FILEOPEN não permitida' },
    { pattern: /FILESAVE\(/i, reason: 'Função FILESAVE não permitida' },
    { pattern: /FILECOPY\(/i, reason: 'Função FILECOPY não permitida' },
    { pattern: /CALL\(/i, reason: 'Função CALL não permitida' },
    
    // DDE (Dynamic Data Exchange) - pode ser usado para execução de código
    { pattern: /\[.*!\d+\]/i, reason: 'Sintaxe DDE não permitida' },
    { pattern: /=.*\|/i, reason: 'Sintaxe de comando suspeita' },
    
    // Fórmulas com referências externas
    { pattern: /!'.*'/i, reason: 'Referência externa não permitida' },
    { pattern: /!\[.*\]/i, reason: 'Referência externa não permitida' },
    
    // Scripts e macros
    { pattern: /^=.*\.vbs/i, reason: 'Referência a script VBS não permitida' },
    { pattern: /^=.*\.js/i, reason: 'Referência a script JS não permitida' },
    { pattern: /^=.*\.bat/i, reason: 'Referência a arquivo batch não permitida' },
    { pattern: /^=.*\.exe/i, reason: 'Referência a executável não permitida' },
    
    // Fórmulas muito longas (potencial para ofuscação)
    { pattern: (f: string) => f.length > 1000, reason: 'Fórmula muito longa (mais de 1000 caracteres)' }
  ];
  
  // Verificar se a fórmula contém algum padrão perigoso
  for (const { pattern, reason } of dangerousPatterns) {
    if (typeof pattern === 'function') {
      if (pattern(formula)) {
        return { isSafe: false, sanitized: null, reason };
      }
    } else if (pattern.test(formula)) {
      return { isSafe: false, sanitized: null, reason };
    }
  }
  
  // Se a fórmula passa pelas verificações, considera-se segura
  return { isSafe: true, sanitized: formula };
}

/**
 * Sanitiza dados de planilha Excel para prevenir injeções maliciosas
 * @param sheetData Dados da planilha Excel para sanitizar
 * @returns Dados da planilha sanitizados com relatório de fórmulas potencialmente perigosas
 */
export function sanitizeExcelData(sheetData: any): {
  sanitizedData: any;
  securityReport: {
    hasDangerousFormulas: boolean;
    formulasRejected: number;
    details: Array<{
      rowIndex: number;
      columnName: string;
      reason: string;
    }>;
  };
} {
  if (!sheetData || typeof sheetData !== 'object') {
    return {
      sanitizedData: sheetData,
      securityReport: {
        hasDangerousFormulas: false,
        formulasRejected: 0,
        details: []
      }
    };
  }
  
  const securityReport = {
    hasDangerousFormulas: false,
    formulasRejected: 0,
    details: [] as Array<{
      rowIndex: number;
      columnName: string;
      reason: string;
    }>
  };
  
  // Clone para não modificar o original
  const sanitizedData = { ...sheetData };
  
  // Se tiver dados de linhas, processar cada uma
  if (Array.isArray(sanitizedData.rows)) {
    sanitizedData.rows = sanitizedData.rows.map((row: any, rowIndex: number) => {
      if (!row || !Array.isArray(row.cells)) return row;
      
      // Processar cada célula da linha
      const sanitizedRow = { ...row };
      sanitizedRow.cells = row.cells.map((cell: any) => {
        if (!cell) return cell;
        
        const sanitizedCell = { ...cell };
        
        // Se a célula tiver uma fórmula, sanitizá-la
        if (cell.formula) {
          const sanitizationResult = sanitizeExcelFormula(cell.formula);
          
          if (!sanitizationResult.isSafe) {
            // Registrar a fórmula rejeitada
            securityReport.hasDangerousFormulas = true;
            securityReport.formulasRejected++;
            securityReport.details.push({
              rowIndex,
              columnName: cell.columnName || 'Desconhecido',
              reason: sanitizationResult.reason || 'Fórmula potencialmente perigosa'
            });
            
            // Remover a fórmula perigosa
            sanitizedCell.formula = null;
            sanitizedCell.value = '[Fórmula removida por segurança]';
          }
        }
        
        // Se o valor for uma string, sanitizar para prevenir XSS
        if (typeof cell.value === 'string') {
          sanitizedCell.value = sanitizeHtml(cell.value);
        }
        
        return sanitizedCell;
      });
      
      return sanitizedRow;
    });
  }
  
  return { sanitizedData, securityReport };
}

/**
 * Escapa caracteres de controle em uma string
 * @param str String para escapar caracteres de controle
 * @returns String com caracteres de controle escapados
 */
export function escapeControlChars(str: string): string {
  if (!str || typeof str !== 'string') return '';
  
  /* eslint-disable no-control-regex */
  const controlCharsRegex = /[\x00-\x09\x0b-\x1f\x7f]/g;
  /* eslint-enable no-control-regex */
  
  return str.replace(controlCharsRegex, match => {
    const hex = match.charCodeAt(0).toString(16).padStart(2, '0');
    return `\\x${hex}`;
  });
}

// Constante para caracteres de controle
/* eslint-disable no-control-regex */
const CONTROL_CHARS = /[\x00-\x09\x0b-\x1f\x7f]/g;
/* eslint-enable no-control-regex */

/**
 * Sanitiza o conteúdo de uma célula Excel para prevenir injeções
 * @param cellContent Conteúdo da célula para sanitizar
 * @returns Conteúdo sanitizado
 */
export function sanitizeExcelCellContent(cellContent: any): any {
  if (cellContent === null || cellContent === undefined) return cellContent;
  
  // Se for string, sanitizar
  if (typeof cellContent === 'string') {
    // Verificar se parece uma fórmula (começa com '=')
    if (cellContent.startsWith('=')) {
      const sanitizationResult = sanitizeExcelFormula(cellContent);
      return sanitizationResult.isSafe ? sanitizationResult.sanitized : '[Fórmula removida por segurança]';
    }
    
    // Sanitizar para prevenir XSS em células de texto
    return sanitizeHtml(cellContent);
  }
  
  // Se for número, boolean ou outro tipo primitivo, retornar sem alterações
  if (typeof cellContent !== 'object') return cellContent;
  
  // Se for array, sanitizar cada elemento
  if (Array.isArray(cellContent)) {
    return cellContent.map(item => sanitizeExcelCellContent(item));
  }
  
  // Se for objeto, sanitizar recursivamente
  if (cellContent !== null && typeof cellContent === 'object') {
    return deepSanitizeObject(cellContent);
  }
  
  return cellContent;
}

/**
 * Escapa caracteres de controle para SQL
 * @param str String para escapar
 * @returns String com caracteres de controle escapados para SQL
 */
export function escapeControlCharsSql(str: string): string {
  if (!str || typeof str !== 'string') return '';
  
  return str
    .replace(/\0/g, '\\0')
    .replace(/\n/g, '\\n')
    .replace(/\r/g, '\\r')
    .replace(/\t/g, '\\t')
    .replace(/\\x1a/g, '\\Z'); // Corrigido: Mudado de /\x1a/g para /\\x1a/g
}

/**
 * Valida e analisa JSON com suporte opcional a validação de schema
 * @param jsonString String JSON para validar e analisar
 * @param schema Schema para validação (opcional)
 * @returns Resultado da validação com dados analisados ou erro
 */
export function validateJSON(jsonString: string, schema?: any): { valid: boolean; data?: any; error?: string } {
  try {
    const data = JSON.parse(jsonString);
    
    // Validação básica de schema
    if (schema) {
      // Validação de tipo
      if (schema.type === 'object' && typeof data !== 'object') {
        return { valid: false, error: 'Expected object' };
      } else if (schema.type === 'array' && !Array.isArray(data)) {
        return { valid: false, error: 'Expected array' };
      }
      
      // Validação de campos obrigatórios
      if (schema.required && Array.isArray(schema.required)) {
        for (const field of schema.required) {
          if (!(field in data)) {
            return { valid: false, error: `Missing required field: ${field}` };
          }
        }
      }
      
      // Validação de tipos de propriedades
      if (schema.properties) {
        for (const field in schema.properties) {
          if (field in data) {
            const propType = schema.properties[field].type;
            const actualType = Array.isArray(data[field]) ? 'array' : typeof data[field];
            
            if (propType === 'string' && actualType !== 'string') {
              return { valid: false, error: `Field ${field} should be string` };
            } else if (propType === 'number' && actualType !== 'number') {
              return { valid: false, error: `Field ${field} should be number` };
            } else if (propType === 'boolean' && actualType !== 'boolean') {
              return { valid: false, error: `Field ${field} should be boolean` };
            } else if (propType === 'array' && actualType !== 'array') {
              return { valid: false, error: `Field ${field} should be array` };
            } else if (propType === 'object' && actualType !== 'object') {
              return { valid: false, error: `Field ${field} should be object` };
            }
          }
        }
      }
    }
    
    return { valid: true, data };
  } catch (error) {
    return { valid: false, error: (error as Error).message };
  }
}

/**
 * Sanitiza input (string, objeto ou array) para prevenir ataques
 * @param input Valor para sanitizar
 * @returns Valor sanitizado
 */
export function sanitizeInput(input: any): any {
  if (input === null || input === undefined) return '';
  
  if (typeof input === 'string') {
    return sanitizeHtml(input);
  }
  
  if (Array.isArray(input)) {
    return input.map(item => sanitizeInput(item));
  }
  
  if (typeof input === 'object') {
    const result: Record<string, any> = {};
    for (const key in input) {
      if (Object.prototype.hasOwnProperty.call(input, key)) {
        result[key] = sanitizeInput(input[key]);
      }
    }
    return result;
  }
  
  return input;
} 