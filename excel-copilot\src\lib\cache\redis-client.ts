// import Redis from 'ioredis'; // Removido - não compatível com Vercel serverless

import { logger } from '@/lib/logger';

/**
 * Cache em memória simples para substituir Redis (compatível com Vercel serverless)
 */

interface CacheItem {
  value: string;
  expiry?: number;
}

class MemoryCache {
  private static instance: MemoryCache;
  private cache = new Map<string, CacheItem>();
  private isConnected = true; // Sempre conectado para cache em memória

  private constructor() {
    // Limpar itens expirados a cada 5 minutos
    setInterval(() => this.cleanExpired(), 5 * 60 * 1000);
  }

  public static getInstance(): MemoryCache {
    if (!MemoryCache.instance) {
      MemoryCache.instance = new MemoryCache();
    }
    return MemoryCache.instance;
  }

  private cleanExpired(): void {
    const now = Date.now();
    for (const [key, item] of this.cache.entries()) {
      if (item.expiry && item.expiry < now) {
        this.cache.delete(key);
      }
    }
  }

  public async get(key: string): Promise<string | null> {
    try {
      const item = this.cache.get(key);
      if (!item) {
        logger.debug('❌ Cache miss:', key);
        return null;
      }

      // Verificar se expirou
      if (item.expiry && item.expiry < Date.now()) {
        this.cache.delete(key);
        logger.debug('⏰ Cache expired:', key);
        return null;
      }

      logger.debug('✅ Cache hit:', key);
      return item.value;
    } catch (error) {
      logger.error('💥 Erro ao buscar no cache:', error, { key });
      return null;
    }
  }

  public async set(key: string, value: string, ttlSeconds?: number): Promise<boolean> {
    try {
      const item: CacheItem = {
        value,
        expiry: ttlSeconds ? Date.now() + (ttlSeconds * 1000) : undefined
      };

      this.cache.set(key, item);
      logger.debug('💾 Cache set:', key, ttlSeconds ? `TTL: ${ttlSeconds}s` : 'sem TTL');
      return true;
    } catch (error) {
      logger.error('💥 Erro ao salvar no cache:', error, { key });
      return false;
    }
  }

  public async del(key: string): Promise<boolean> {
    try {
      const existed = this.cache.has(key);
      this.cache.delete(key);
      logger.debug('🗑️ Cache deleted:', key, `Existed: ${existed}`);
      return existed;
    } catch (error) {
      logger.error('💥 Erro ao deletar do cache:', error, { key });
      return false;
    }
  }

  public async exists(key: string): Promise<boolean> {
    try {
      const item = this.cache.get(key);
      if (!item) return false;

      // Verificar se expirou
      if (item.expiry && item.expiry < Date.now()) {
        this.cache.delete(key);
        return false;
      }

      return true;
    } catch (error) {
      logger.error('💥 Erro ao verificar existência no cache:', error, { key });
      return false;
    }
  }

  public async flushPattern(pattern: string): Promise<number> {
    try {
      const regex = new RegExp(pattern.replace(/\*/g, '.*'));
      let deleted = 0;

      for (const key of this.cache.keys()) {
        if (regex.test(key)) {
          this.cache.delete(key);
          deleted++;
        }
      }

      logger.info('🧹 Cache pattern flushed:', pattern, `Deleted: ${deleted} keys`);
      return deleted;
    } catch (error) {
      logger.error('💥 Erro ao limpar pattern do cache:', error, { pattern });
      return 0;
    }
  }

  public async getStats(): Promise<{
    connected: boolean;
    connectionAttempts: number;
    memoryUsage?: string;
    keyCount?: number;
  }> {
    try {
      return {
        connected: this.isConnected,
        connectionAttempts: 0, // Não aplicável para cache em memória
        memoryUsage: `${Math.round(process.memoryUsage().heapUsed / 1024 / 1024)}MB`,
        keyCount: this.cache.size,
      };
    } catch (error) {
      logger.error('💥 Erro ao obter stats do cache:', error);
      return {
        connected: this.isConnected,
        connectionAttempts: 0,
      };
    }
  }

  public async disconnect(): Promise<void> {
    this.cache.clear();
    logger.info('👋 Cache em memória limpo');
  }
}

export const redisClient = MemoryCache.getInstance();
