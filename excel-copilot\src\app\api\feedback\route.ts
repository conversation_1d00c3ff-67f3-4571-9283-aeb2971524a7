import { NextResponse } from 'next/server';

import { logger } from '@/lib/logger';

/**
 * API para receber feedback sobre comandos Excel processados por IA
 */
export async function POST(request: Request) {
  try {
    const data = await request.json();

    if (!data.commandId || data.successful === undefined) {
      return NextResponse.json({ error: 'Dados de feedback incompletos' }, { status: 400 });
    }

    // Log do feedback recebido
    logger.info('Feedback recebido:', {
      commandId: data.commandId,
      successful: data.successful,
      hasText: !!data.feedbackText,
    });

    // Em um ambiente de produção, seria implementado o armazenamento em banco de dados
    // Por enquanto, apenas registramos nos logs para fins de desenvolvimento

    return NextResponse.json({ success: true });
  } catch (error) {
    logger.error('Erro ao processar feedback:', error);
    return NextResponse.json({ error: 'Erro interno ao processar feedback' }, { status: 500 });
  }
}
