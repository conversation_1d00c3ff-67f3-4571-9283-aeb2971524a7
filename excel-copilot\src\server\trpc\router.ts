import { z } from 'zod';

import { createChatHistoryInput, createWorkbookInput } from '@/server/db/utils';

import { createTRPCRouter, protectedProcedure } from './trpc';

export const appRouter = createTRPCRouter({
  // Endpoint para listar workbooks do usuário
  getWorkbooks: protectedProcedure.query(async ({ ctx }) => {
    return ctx.prisma.workbook.findMany({
      where: {
        userId: ctx.session.user.id,
      },
      orderBy: {
        updatedAt: 'desc',
      },
      include: {
        sheets: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });
  }),

  // ==== Endpoints de workbook ====

  // Endpoint para listar todos os workbooks do usuário
  workbook_getAll: protectedProcedure.query(async ({ ctx }) => {
    return ctx.prisma.workbook.findMany({
      where: {
        userId: ctx.session.user.id,
      },
      orderBy: {
        updatedAt: 'desc',
      },
      include: {
        sheets: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });
  }),

  // Obter modelos de planilhas disponíveis
  workbook_getTemplates: protectedProcedure.query(async () => {
    // Templates predefinidos
    return [
      {
        id: 'vendas',
        title: 'Controle de Vendas',
        description:
          'Template para controle de vendas com campos para produto, quantidade, valor e total.',
        icon: 'bar-chart',
        category: 'Negócios',
      },
      {
        id: 'financas',
        title: 'Finanças Pessoais',
        description: 'Controle suas receitas e despesas com categorias predefinidas.',
        icon: 'piggy-bank',
        category: 'Finanças',
      },
      {
        id: 'projeto',
        title: 'Gerenciamento de Projeto',
        description: 'Acompanhe tarefas, responsáveis e prazos do seu projeto.',
        icon: 'file-text',
        category: 'Produtividade',
      },
      {
        id: 'inventario',
        title: 'Controle de Estoque',
        description: 'Registre produtos, quantidades e informações de fornecedores.',
        icon: 'table',
        category: 'Negócios',
      },
    ];
  }),

  // Endpoint para obter um workbook específico
  workbook_get: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      const workbook = await ctx.prisma.workbook.findUnique({
        where: {
          id: input.id,
        },
        include: {
          sheets: true,
        },
      });

      // Verificar se o workbook existe e pertence ao usuário atual
      if (!workbook) {
        throw new Error('Workbook não encontrado');
      }

      if (workbook.userId !== ctx.session.user.id && !workbook.isPublic) {
        throw new Error('Você não tem permissão para acessar este workbook');
      }

      return workbook;
    }),

  // Endpoint para excluir um workbook
  workbook_delete: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      // Verificar se o workbook pertence ao usuário
      const workbook = await ctx.prisma.workbook.findFirst({
        where: {
          id: input.id,
          userId: ctx.session.user.id,
        },
      });

      if (!workbook) {
        throw new Error('Workbook não encontrado ou não pertence ao usuário');
      }

      // Excluir o workbook e todas as sheets relacionadas (cascade)
      return ctx.prisma.workbook.delete({
        where: {
          id: input.id,
        },
      });
    }),

  // Endpoint para criar um novo workbook
  workbook_create: protectedProcedure
    .input(
      z.object({
        name: z.string().min(1).max(100),
        description: z.string().max(500).optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      return ctx.prisma.workbook.create({
        data: {
          name: input.name,
          description: input.description || '',
          userId: ctx.session.user.id,
          sheets: {
            create: [
              {
                name: 'Planilha 1',
                data: JSON.stringify({
                  headers: [],
                  rows: [],
                }),
              },
            ],
          },
        },
      });
    }),

  // Endpoint para criar um workbook a partir de um template
  workbook_createFromTemplate: protectedProcedure
    .input(z.object({ templateId: z.string() }))
    .mutation(async ({ ctx, input }) => {
      // Templates predefinidos
      const templates: Record<
        string,
        {
          sheets: Array<{
            name: string;
            data: {
              headers: string[];
              rows: (string | number)[][];
            };
          }>;
        }
      > = {
        vendas: {
          sheets: [
            {
              name: 'Vendas',
              data: {
                headers: ['Data', 'Produto', 'Quantidade', 'Valor Unitário', 'Total'],
                rows: [
                  ['2023-01-01', 'Produto A', 5, 100, 500],
                  ['2023-01-02', 'Produto B', 3, 200, 600],
                  ['2023-01-03', 'Produto C', 10, 50, 500],
                ],
              },
            },
          ],
        },
        financas: {
          sheets: [
            {
              name: 'Orçamento',
              data: {
                headers: ['Categoria', 'Descrição', 'Valor', 'Data', 'Tipo'],
                rows: [
                  ['Moradia', 'Aluguel', 1200, '2023-01-01', 'Despesa'],
                  ['Transporte', 'Combustível', 300, '2023-01-05', 'Despesa'],
                  ['Salário', 'Mensal', 5000, '2023-01-10', 'Receita'],
                ],
              },
            },
          ],
        },
        projeto: {
          sheets: [
            {
              name: 'Tarefas',
              data: {
                headers: ['Tarefa', 'Responsável', 'Prazo', 'Status', 'Prioridade'],
                rows: [
                  ['Definir escopo', 'João', '2023-01-15', 'Concluído', 'Alta'],
                  ['Desenvolver front-end', 'Maria', '2023-02-10', 'Em andamento', 'Média'],
                  ['Testar aplicação', 'Pedro', '2023-02-25', 'Não iniciado', 'Baixa'],
                ],
              },
            },
          ],
        },
        inventario: {
          sheets: [
            {
              name: 'Estoque',
              data: {
                headers: ['Produto', 'Quantidade', 'Preço Unitário', 'Fornecedor', 'Data Entrada'],
                rows: [
                  ['Item A', 100, 10, 'Fornecedor X', '2023-01-05'],
                  ['Item B', 50, 25, 'Fornecedor Y', '2023-01-10'],
                  ['Item C', 200, 5, 'Fornecedor Z', '2023-01-15'],
                ],
              },
            },
          ],
        },
      };

      // Verificar se o template existe
      if (!templates[input.templateId]) {
        throw new Error('Template não encontrado');
      }

      // Obter o template selecionado
      const template = templates[input.templateId];

      // Tipos válidos para templateId
      type TemplateKey = 'vendas' | 'financas' | 'projeto' | 'inventario';

      // Verificar se o ID do template é válido
      const isValidTemplateId = (id: string): id is TemplateKey => {
        return ['vendas', 'financas', 'projeto', 'inventario'].includes(id);
      };

      // Criar nome e descrição padrão baseado no template
      const templateInfo = {
        vendas: {
          name: 'Controle de Vendas',
          description:
            'Planilha para controle de vendas com registros de produtos, quantidades e valores.',
        },
        financas: {
          name: 'Finanças Pessoais',
          description: 'Controle de finanças com categorias para receitas e despesas.',
        },
        projeto: {
          name: 'Gerenciamento de Projeto',
          description: 'Acompanhamento de tarefas e responsáveis do projeto.',
        },
        inventario: {
          name: 'Controle de Estoque',
          description: 'Registro de produtos e quantidades em estoque.',
        },
      };

      // Determinar nome e descrição com base no ID do template
      let name = `Template ${input.templateId}`;
      let description = '';

      if (isValidTemplateId(input.templateId)) {
        name = templateInfo[input.templateId].name;
        description = templateInfo[input.templateId].description;
      }

      // Criar o workbook com base no template
      const workbook = await ctx.prisma.workbook.create({
        data: createWorkbookInput(
          name,
          ctx.session.user.id,
          description,
          (template?.sheets || []).map(sheet => ({
            name: sheet.name,
            data: JSON.stringify(sheet.data),
          }))
        ),
        include: {
          sheets: true,
        },
      });

      return workbook;
    }),

  // Endpoint para criar um novo workbook
  createWorkbook: protectedProcedure
    .input(
      z.object({
        name: z.string().min(1, 'O nome é obrigatório'),
        description: z.string().optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      // Criar o workbook usando o utilitário para tipagem segura
      const workbook = await ctx.prisma.workbook.create({
        data: createWorkbookInput(input.name, ctx.session.user.id, input.description, [
          {
            name: 'Planilha1',
            data: JSON.stringify({
              headers: [],
              rows: [],
            }),
          },
        ]),
        include: {
          sheets: true,
        },
      });

      return workbook;
    }),

  // Endpoint para salvar um histórico de chat
  saveChatHistory: protectedProcedure
    .input(
      z.object({
        message: z.string(),
        response: z.string(),
        workbookId: z.string().optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      return ctx.prisma.chatHistory.create({
        data: createChatHistoryInput(
          ctx.session.user.id,
          input.message,
          input.response,
          input.workbookId
        ),
      });
    }),

  // Endpoint para obter histórico de chat de um workbook
  getWorkbookChatHistory: protectedProcedure
    .input(z.object({ workbookId: z.string() }))
    .query(async ({ ctx, input }) => {
      return ctx.prisma.chatHistory.findMany({
        where: {
          userId: ctx.session.user.id,
          workbookId: input.workbookId,
        },
        orderBy: {
          createdAt: 'desc',
        },
        take: 50,
      });
    }),
});

export type AppRouter = typeof appRouter;
