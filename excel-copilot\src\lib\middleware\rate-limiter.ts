import { NextRequest, NextResponse } from 'next/server';

import { ENV } from '@/config/unified-environment';
import { logger } from '@/lib/logger';

interface RateLimitOptions {
  // Número máximo de requisições por limite de tempo
  maxRequests: number;
  // Período de tempo (em segundos)
  windowSizeInSeconds: number;
  // Endpoints para aplicar o rate limit (regex patterns)
  pathPatterns: RegExp[];
  // Função para obter identifier para o rate limiting
  getIdentifier: (req: NextRequest) => string;
  // Tempo de bloqueio em segundos após exceder o limite (opcional)
  blockDurationSeconds?: number;
}

// Cache em memória para armazenar tentativas em ambiente de desenvolvimento
// Em produção, idealmente, esta implementação seria substituída por Redis
const requestCache = new Map<
  string,
  { count: number; resetTime: number; blocked?: boolean; blockUntil?: number }
>();

// Lista de IPs suspeitos com tentativas excessivas
const suspiciousIPs = new Set<string>();

// Valores padrão para rate limiting
const DEFAULT_OPTIONS: RateLimitOptions = {
  maxRequests: ENV.LIMITS?.API_RATE_LIMIT || (ENV.IS_PRODUCTION ? 60 : 120), // Usar valor do env ou fallback
  windowSizeInSeconds: 60, // Janela de 1 minuto
  pathPatterns: [/^\/api\/excel\/.*/i, /^\/api\/chat\/.*/i],
  getIdentifier: (req: NextRequest) => {
    // Usar IP ou session token como identificador
    const ip = req.headers.get('x-forwarded-for') || 'anonymous';
    const sessionToken = req.cookies.get('next-auth.session-token')?.value;

    // Priorizar session token se disponível, caso contrário usar IP
    return sessionToken ? `session:${sessionToken.substring(0, 16)}` : `ip:${ip}`;
  },
};

// Configurações específicas para diferentes tipos de endpoints
const ENDPOINT_CONFIGS: Record<string, Partial<RateLimitOptions>> = {
  // Limites mais restritivos para pagamentos
  payment: {
    maxRequests: ENV.IS_PRODUCTION ? 10 : 30,
    windowSizeInSeconds: 60,
    pathPatterns: [/^\/api\/payment\/.*/i, /^\/api\/stripe\/.*/i, /^\/api\/webhooks\/stripe.*/i],
    blockDurationSeconds: 300, // 5 minutos de bloqueio após exceder
  },
  // Limites para autenticação (prevenir bruteforce)
  auth: {
    maxRequests: ENV.IS_PRODUCTION ? 20 : 50,
    windowSizeInSeconds: 60 * 5, // 5 minutos
    pathPatterns: [/^\/api\/auth\/.*/i],
    blockDurationSeconds: 600, // 10 minutos de bloqueio após exceder
  },
};

/**
 * Middleware para limitar taxa de requisições
 */
export async function rateLimiter(
  req: NextRequest,
  options: Partial<RateLimitOptions> = {}
): Promise<NextResponse | null> {
  // Se estamos em modo de teste, pular rate limiting
  if (ENV.IS_TEST) {
    return null;
  }

  const path = req.nextUrl.pathname;
  const ip = req.headers.get('x-forwarded-for') || 'anonymous';

  // Verificar se o IP está na lista de suspeitos
  if (suspiciousIPs.has(ip)) {
    logger.warn(`Requisição bloqueada de IP suspeito: ${ip} para ${path}`);
    return new NextResponse(
      JSON.stringify({
        error: 'Access Denied',
        message: 'Seu acesso foi temporariamente restrito devido a atividade suspeita',
      }),
      { status: 403, headers: { 'Content-Type': 'application/json' } }
    );
  }

  // Verificar configuração específica para o path
  let configType = 'default';
  let specificConfig: Partial<RateLimitOptions> = {};

  // Verificar se é um endpoint de pagamento
  if (
    ENDPOINT_CONFIGS.payment &&
    ENDPOINT_CONFIGS.payment.pathPatterns?.some(pattern => pattern.test(path))
  ) {
    specificConfig = ENDPOINT_CONFIGS.payment || {};
    configType = 'payment';
  }
  // Verificar se é um endpoint de autenticação
  else if (
    ENDPOINT_CONFIGS.auth &&
    ENDPOINT_CONFIGS.auth.pathPatterns?.some(pattern => pattern.test(path))
  ) {
    specificConfig = ENDPOINT_CONFIGS.auth || {};
    configType = 'auth';
  }

  const mergedOptions: RateLimitOptions = { ...DEFAULT_OPTIONS, ...specificConfig, ...options };
  const { maxRequests, windowSizeInSeconds, pathPatterns, getIdentifier, blockDurationSeconds } =
    mergedOptions;

  // Verificar se o path deve ser limitado
  const shouldRateLimit = pathPatterns.some(pattern => pattern.test(path));

  if (!shouldRateLimit) {
    return null; // Não aplicar rate limiting
  }

  const identifier = getIdentifier(req);
  const now = Math.floor(Date.now() / 1000);
  const cacheKey = `rate-limit:${identifier}:${configType}`;

  // Obter o registro atual de contagem
  let record = requestCache.get(cacheKey);

  // Verificar se o usuário está bloqueado
  if (record?.blocked && record.blockUntil && now < record.blockUntil) {
    const remainingBlock = record.blockUntil - now;
    logger.warn(`Usuário bloqueado tentando acessar: ${identifier} em ${path}`);

    return new NextResponse(
      JSON.stringify({
        error: 'Too Many Requests',
        message: 'Acesso temporariamente bloqueado. Tente novamente mais tarde.',
        retryAfter: remainingBlock,
      }),
      {
        status: 429,
        headers: {
          'Retry-After': remainingBlock.toString(),
          'Content-Type': 'application/json',
        },
      }
    );
  }

  // Se não existir ou já expirou, criar novo registro
  if (!record || now > record.resetTime) {
    record = {
      count: 0,
      resetTime: now + windowSizeInSeconds,
      blocked: false,
    };
  }

  // Incrementar contador
  record.count++;
  requestCache.set(cacheKey, record);

  // Calcular cabeçalhos de rate limit
  const remaining = Math.max(0, maxRequests - record.count);
  const reset = record.resetTime - now;

  // Configurar headers para informar cliente sobre limite
  const headers = new Headers();
  headers.set('X-RateLimit-Limit', maxRequests.toString());
  headers.set('X-RateLimit-Remaining', remaining.toString());
  headers.set('X-RateLimit-Reset', reset.toString());

  // Buscar informações adicionais para possível abuso
  const userAgent = req.headers.get('user-agent') || 'unknown';
  const referer = req.headers.get('referer') || 'direct';

  // Se excedeu o limite, retornar 429
  if (record.count > maxRequests) {
    // Log mais detalhado para identificar padrões de abuso
    logger.warn(
      `Rate limit excedido: ${identifier} em ${path}. IP: ${ip}, UserAgent: ${userAgent}, Referer: ${referer}, Requests: ${record.count}, Limit: ${maxRequests}, Tipo: ${configType}`
    );

    // Verificar se devemos bloquear temporariamente (apenas para endpoints específicos)
    if (blockDurationSeconds && record.count > maxRequests * 1.5) {
      record.blocked = true;
      record.blockUntil = now + blockDurationSeconds;
      requestCache.set(cacheKey, record);

      // Adicionar à lista de suspeitos se o IP tiver excesso significativo de tentativas
      if (record.count > maxRequests * 3 && ip !== 'anonymous') {
        suspiciousIPs.add(ip);
        logger.error(
          `IP ${ip} adicionado à lista de suspeitos após ${record.count} requisições em ${configType}`
        );
      }

      logger.warn(
        `Usuário ${identifier} bloqueado por ${blockDurationSeconds} segundos após exceder limite em ${configType}`
      );
    }

    return new NextResponse(
      JSON.stringify({
        error: 'Too Many Requests',
        message: 'Limite de requisições excedido. Tente novamente mais tarde.',
        retryAfter: reset,
      }),
      {
        status: 429,
        headers: {
          ...Object.fromEntries(headers.entries()),
          'Retry-After': reset.toString(),
          'Content-Type': 'application/json',
        },
      }
    );
  }

  // Se o usuário está chegando perto do limite (>75%), fazer log para monitoramento
  if (record.count > maxRequests * 0.75 && record.count % 5 === 0) {
    logger.info(
      `Alto uso de API: ${identifier} em ${path} (${record.count}/${maxRequests}). Tipo: ${configType}, UserAgent: ${userAgent}`
    );
  }

  // Requisição permitida, continua
  return null;
}
