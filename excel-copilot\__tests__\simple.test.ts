describe('Testes Simples', () => {
  test('soma dois números corretamente', () => {
    expect(1 + 1).toBe(2);
  });

  test('strings são concatenadas corretamente', () => {
    expect('hello ' + 'world').toBe('hello world');
  });

  test('arrays podem ser verificados', () => {
    const array = [1, 2, 3];
    expect(array).toHaveLength(3);
    expect(array).toContain(2);
  });

  test('objetos podem ser verificados', () => {
    const obj = { name: 'Excel Copilot', type: 'SaaS' };
    expect(obj).toHaveProperty('name');
    expect(obj.type).toBe('SaaS');
  });
});
