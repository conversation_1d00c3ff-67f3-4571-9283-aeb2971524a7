import { prisma } from '../server/db/client';
import { createApiUsageInput } from '../server/db/utils';

import { logger } from './logger';
import { PLANS } from './stripe';

// Garantir que temos os valores dos limites de API como constantes seguras
// para não depender da tipagem da importação
const FREE_TIER_LIMIT: number = 50; // Mesmo valor de API_CALL_LIMITS[PLANS.FREE]

/**
 * Registra o uso de API por um usuário e verifica se o limite foi excedido
 * @param userId ID do usuário que fez a chamada
 * @param endpoint Nome do endpoint chamado
 * @param count Número de chamadas a contabilizar (default: 1)
 * @param workbookId ID opcional do workbook associado à chamada
 * @returns Um objeto indicando se o usuário pode fazer a chamada e informações sobre seu limite
 */
export async function trackApiUsage(
  userId: string,
  endpoint: string,
  count: number = 1,
  workbookId?: string
): Promise<{
  allowed: boolean;
  limitExceeded?: boolean;
  apiCallsUsed: number;
  apiCallsLimit: number;
  plan: string;
}> {
  try {
    // Verificar se o usuário tem uma assinatura ativa
    const subscriptions = await prisma.subscription.findMany({
      where: {
        userId,
        OR: [{ status: 'active' }, { status: 'trialing' }],
      },
      orderBy: { createdAt: 'desc' },
      take: 1,
    });

    // Plano e limites do usuário - garantir valores padrão
    let userPlan: string = PLANS.FREE;
    // Iniciar com o valor padrão do plano FREE
    let apiCallsLimit: number = FREE_TIER_LIMIT;
    let subscription = null;

    if (subscriptions.length > 0) {
      subscription = subscriptions[0];
      // Garantir que userPlan seja sempre uma string
      userPlan = subscription?.plan || PLANS.FREE;

      // Atribuir apiCallsLimit com valor seguro
      if (
        subscription?.apiCallsLimit !== undefined &&
        subscription?.apiCallsLimit !== null &&
        typeof subscription.apiCallsLimit === 'number'
      ) {
        apiCallsLimit = subscription.apiCallsLimit;
      }
      // Se não for válido, manter o valor padrão já atribuído
    }

    // Calcular uso atual
    const currentMonthStart = new Date();
    currentMonthStart.setDate(1);
    currentMonthStart.setHours(0, 0, 0, 0);

    const apiUsage = await prisma.apiUsage.aggregate({
      where: {
        userId,
        billable: true,
        createdAt: {
          gte: currentMonthStart,
        },
      },
      _sum: {
        count: true,
      },
    });

    const currentUsage = apiUsage._sum.count || 0;
    const newTotal = currentUsage + count;

    // Verificar se excedeu o limite (apenas para plano gratuito)
    const limitExceeded = newTotal > apiCallsLimit && userPlan === PLANS.FREE;

    // Para planos pagos, sempre permitir (serão cobrados por uso excedente)
    const allowCall = !limitExceeded || userPlan !== PLANS.FREE;

    // Registrar a chamada da API
    if (allowCall) {
      await prisma.apiUsage.create({
        data: createApiUsageInput(userId, endpoint, count, workbookId, true),
      });

      // Atualizar o contador da assinatura se existir
      if (subscription) {
        await prisma.subscription.update({
          where: { id: subscription.id },
          data: { apiCallsUsed: newTotal },
        });
      }
    }

    return {
      allowed: allowCall,
      limitExceeded: limitExceeded || false,
      apiCallsUsed: newTotal,
      apiCallsLimit: apiCallsLimit, // Agora é garantido ser um número
      plan: userPlan,
    };
  } catch (error) {
    logger.error('[API_USAGE_TRACKING_ERROR]', error);

    // Em caso de erro, permitir a chamada por segurança
    // Garantir que todos os valores retornados sejam do tipo correto
    return {
      allowed: true,
      apiCallsUsed: 0,
      apiCallsLimit: FREE_TIER_LIMIT,
      plan: PLANS.FREE,
    };
  }
}
