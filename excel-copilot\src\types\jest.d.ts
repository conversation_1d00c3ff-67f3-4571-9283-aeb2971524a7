/**
 * Declarações de tipos auxiliares para testes Jest
 */

// Permite que mockResolvedValue aceite qualquer valor em contextos de teste
declare namespace jest {
  interface Mock<T = any, Y extends any[] = any[]> {
    mockResolvedValue(value: any): this;
  }
}

// Utilitário para Response em testes
interface MockResponse extends Response {
  json(): Promise<any>;
  status: number;
}

// Útil para testes de API/controllers
interface MockRequest extends Request {
  json(): Promise<any>;
  body: any;
}
