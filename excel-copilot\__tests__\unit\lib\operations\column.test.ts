import { executeColumnOperation, extractColumnOperations } from '@/lib/operations/columnOperations';
import type { ExcelOperation } from '@/types/index';

/**
 * Testes para as operações de coluna sem mocks
 * Validação de cenários reais de uso
 */
describe('Operações de Coluna (Implementação Real)', () => {
  // Dados para os testes
  const mockSheetData = {
    headers: ['ID', 'Nome', 'Valor', 'Data', 'Status'],
    rows: [
      [1, 'Produto A', 100.5, '2023-01-01', 'Ativo'],
      [2, 'Produto B', 250.75, '2023-01-15', 'Inativo'],
      [3, 'Produto C', 75.3, '2023-02-01', 'Ativo'],
      [4, 'Produto D', 199.99, '2023-02-15', 'Ativo'],
      [5, 'Produto E', 500.0, '2023-03-01', 'Inativo'],
    ],
  };

  describe('Fluxo Completo: Extração e Execução', () => {
    test('deve extrair e executar comando de soma corretamente', async () => {
      // 1. Extrair a operação do texto
      const text = 'Some os valores da coluna Valor';
      const operations = extractColumnOperations(text);

      // Validar a extração
      expect(operations).toHaveLength(1);

      if (operations.length === 0) {
        throw new Error('Nenhuma operação extraída');
      }

      // Usamos non-null assertion (!) para garantir que não é undefined
      const operation = operations[0]!;
      expect(operation.type).toBe('COLUMN_OPERATION');
      expect(operation.data?.operation).toBe('SUM');
      expect(operation.data?.column).toBe('Valor');

      // 2. Executar a operação extraída
      const result = await executeColumnOperation(mockSheetData, operation);

      // Validar o resultado da execução
      expect(result.updatedData).toBeDefined();
      // Soma dos valores: 100.50 + 250.75 + 75.30 + 199.99 + 500.00 = 1126.54
      expect(result.resultSummary).toContain('Soma da coluna Valor:');
      expect(result.resultSummary).toContain('1.126,54');
    });

    test('deve extrair e executar comando de média corretamente', async () => {
      // 1. Extrair a operação do texto
      const text = 'Qual é a média da coluna Valor?';
      const operations = extractColumnOperations(text);

      // Validar a extração
      expect(operations).toHaveLength(1);

      if (operations.length === 0) {
        throw new Error('Nenhuma operação extraída');
      }

      // Usamos non-null assertion (!) para garantir que não é undefined
      const operation = operations[0]!;
      expect(operation.type).toBe('COLUMN_OPERATION');
      expect(operation.data?.operation).toBe('AVERAGE');
      expect(operation.data?.column).toBe('Valor');

      // 2. Executar a operação extraída
      const result = await executeColumnOperation(mockSheetData, operation);

      // Validar o resultado da execução (média = 1126.54 / 5 = 225.308)
      expect(result.updatedData).toBeDefined();
      expect(result.resultSummary).toContain('Média da coluna Valor:');
      // Valor aproximado arredondado para 2 casas
      expect(result.resultSummary).toContain('225,31');
    });

    test('deve extrair e executar comando com célula destino', async () => {
      // 1. Extrair a operação do texto
      const text = 'Some os valores da coluna Valor e coloque o resultado na célula F1';
      const operations = extractColumnOperations(text);

      // Validar a extração - ajustando para a primeira operação extraída com o nome correto da coluna
      expect(operations.length).toBeGreaterThanOrEqual(1);
      const operation = operations.find(
        op =>
          op.data?.operation === 'SUM' &&
          op.data?.column === 'Valor' &&
          op.data?.targetCell === 'F1'
      );
      expect(operation).toBeDefined();

      // Garantir que a operação foi encontrada antes de prosseguir
      if (!operation) {
        throw new Error('Operação não encontrada');
      }

      // 2. Executar a operação extraída
      const result = await executeColumnOperation(mockSheetData, operation);

      // Validar o resultado da execução
      expect(result.updatedData).toBeDefined();
      expect(result.updatedData.rows[0][5]).toBeCloseTo(1126.54, 2);
      expect(result.resultSummary).toContain('célula F1');
    });
  });

  describe('Casos de Borda e Erros', () => {
    test('deve lidar com coluna inexistente', async () => {
      const operation: ExcelOperation = {
        type: 'COLUMN_OPERATION',
        data: {
          column: 'PreçoUnitário',
          operation: 'SUM',
        },
      };

      await expect(executeColumnOperation(mockSheetData, operation)).rejects.toThrow('Coluna');
    });

    test('deve lidar com operação não suportada', async () => {
      const operation: ExcelOperation = {
        type: 'COLUMN_OPERATION',
        data: {
          column: 'Valor',
          operation: 'MEDIAN' as any,
        },
      };

      await expect(executeColumnOperation(mockSheetData, operation)).rejects.toThrow('Operação');
    });

    test('deve lidar com texto sem operações reconhecíveis', () => {
      const text = 'Este texto não contém nenhuma operação que possa ser reconhecida';
      const operations = extractColumnOperations(text);

      expect(operations).toHaveLength(0);
    });
  });

  describe('Múltiplas Operações', () => {
    test('deve extrair múltiplas operações de um texto complexo', () => {
      const text = `
        Preciso de algumas análises:
        1. Some os valores da coluna Valor
        2. Qual é o valor máximo da coluna Valor?
        3. Conte quantos valores existem na coluna Status
      `;

      const operations = extractColumnOperations(text);

      // Verificar se as operações esperadas existem, independente da ordem ou quantidade
      const hasSum = operations.some(
        op => op.data?.operation === 'SUM' && op.data?.column === 'Valor'
      );
      const hasMax = operations.some(
        op => op.data?.operation === 'MAX' && op.data?.column === 'Valor'
      );
      const hasCount = operations.some(
        op => op.data?.operation === 'COUNT' && op.data?.column === 'Status'
      );

      expect(hasSum).toBe(true);
      expect(hasMax).toBe(true);
      expect(hasCount).toBe(true);
    });

    test('deve executar sequência de operações e manter o estado', async () => {
      // Definir sequência de operações
      const operations: ExcelOperation[] = [
        {
          type: 'COLUMN_OPERATION',
          data: {
            column: 'Valor',
            operation: 'MAX',
            targetCell: 'F1',
          },
        },
        {
          type: 'COLUMN_OPERATION',
          data: {
            column: 'Valor',
            operation: 'MIN',
            targetCell: 'F2',
          },
        },
      ];

      // Executar primeira operação
      const sheetData = { ...mockSheetData };
      // Utilizamos não-nulo assertion nos índices dos arrays
      const result1 = await executeColumnOperation(sheetData, operations[0]!);

      // Validar primeira operação
      expect(result1.updatedData.rows[0][5]).toBe(500);

      // Usar resultado da primeira operação como entrada para a segunda
      const result2 = await executeColumnOperation(result1.updatedData, operations[1]!);

      // Validar que ambas operações foram aplicadas corretamente
      expect(result2.updatedData.rows[0][5]).toBe(500); // Máximo ainda presente
      expect(result2.updatedData.rows[1][5]).toBe(75.3); // Mínimo adicionado
    });
  });
});
