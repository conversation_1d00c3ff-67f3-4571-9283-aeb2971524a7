import { PrismaClient } from '@prisma/client';
import * as ExcelJS from 'exceljs';

const prisma = new PrismaClient();

// Dados iniciais para o seed
const initialUsers = [
  {
    name: '<PERSON>u<PERSON><PERSON>',
    email: '<EMAIL>',
    image: 'https://api.dicebear.com/7.x/avataaars/svg?seed=demo',
  },
  {
    name: 'Admin',
    email: '<EMAIL>',
    image: 'https://api.dicebear.com/7.x/avataaars/svg?seed=admin',
  },
];

// Interfaces para tipagem
interface SheetData {
  headers: string[];
  rows: any[][];
}

// Criação de planilha de exemplo
async function createSampleWorkbook(): Promise<string> {
  const workbook = new ExcelJS.Workbook();
  const sheet = workbook.addWorksheet('Vendas 2023');

  // Adicionar cabeçalhos
  sheet.columns = [
    { header: 'Data', key: 'data', width: 15 },
    { header: 'Produto', key: 'produto', width: 25 },
    { header: '<PERSON><PERSON><PERSON>', key: 'regiao', width: 15 },
    { header: 'Vendedor', key: 'vendedor', width: 20 },
    { header: 'Quantidade', key: 'quantidade', width: 12 },
    { header: 'Valor Unitário', key: 'valorUnitario', width: 15 },
    { header: 'Valor Total', key: 'valorTotal', width: 15 },
  ];

  // Estilizar cabeçalhos
  sheet.getRow(1).font = { bold: true };
  sheet.getRow(1).fill = {
    type: 'pattern',
    pattern: 'solid',
    fgColor: { argb: 'FFE6F0FF' },
  };

  // Dados de exemplo
  const regioes = ['Sul', 'Sudeste', 'Centro-Oeste', 'Norte', 'Nordeste'];
  const produtos = ['Laptop', 'Smartphone', 'Monitor', 'Teclado', 'Mouse', 'Headphone', 'Webcam'];
  const vendedores = [
    'João Silva',
    'Maria Oliveira',
    'Carlos Santos',
    'Ana Pereira',
    'Paulo Costa',
  ];

  // Gerar dados aleatórios
  const startDate = new Date('2023-01-01');
  const endDate = new Date('2023-12-31');

  for (let i = 0; i < 50; i++) {
    const data = new Date(
      startDate.getTime() + Math.random() * (endDate.getTime() - startDate.getTime())
    );
    const produto = produtos[Math.floor(Math.random() * produtos.length)];
    const regiao = regioes[Math.floor(Math.random() * regioes.length)];
    const vendedor = vendedores[Math.floor(Math.random() * vendedores.length)];
    const quantidade = Math.floor(Math.random() * 10) + 1;
    const valorUnitario = Math.floor(Math.random() * 1000) + 100;
    const valorTotal = quantidade * valorUnitario;

    sheet.addRow({
      data: data,
      produto: produto,
      regiao: regiao,
      vendedor: vendedor,
      quantidade: quantidade,
      valorUnitario: valorUnitario,
      valorTotal: valorTotal,
    });
  }

  // Formatar células
  sheet.getColumn('data').numFmt = 'dd/mm/yyyy';
  sheet.getColumn('valorUnitario').numFmt = '"R$ "#,##0.00';
  sheet.getColumn('valorTotal').numFmt = '"R$ "#,##0.00';

  // Aplicar bordas
  sheet.eachRow((row, _rowNumber) => {
    row.eachCell({ includeEmpty: true }, cell => {
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
    });
  });

  // Converter para formato compatível com o banco de dados
  const jsonData: SheetData = {
    headers: sheet.columns.map(col => (col.header || '') as string),
    rows: [],
  };

  // Obter todas as linhas e converter valores para formato seguro
  const rows: any[][] = [];
  sheet.eachRow((row, rowNumber) => {
    if (rowNumber > 1) {
      // Pular cabeçalhos
      const rowData: any[] = [];
      row.eachCell((cell, _colNumber) => {
        let value = cell.value;
        if (value instanceof Date) {
          value = value.toISOString();
        }
        rowData.push(value);
      });
      rows.push(rowData);
    }
  });

  jsonData.rows = rows;

  return JSON.stringify(jsonData);
}

// Seed principal
async function main() {
  // eslint-disable-next-line no-console
  console.log('🌱 Iniciando seed do banco de dados...');

  // Criar dados de exemplo
  const sampleSheetData = await createSampleWorkbook();

  // Criar usuários
  for (const userData of initialUsers) {
    const user = await prisma.user.upsert({
      where: { email: userData.email },
      update: {},
      create: userData,
    });

    // eslint-disable-next-line no-console
    console.log(`✅ Usuário criado: ${user.name}`);

    // Criar workbooks de exemplo para o usuário de demonstração
    if (user.email === '<EMAIL>') {
      const workbook = await prisma.workbook.create({
        data: {
          name: 'Relatório de Vendas 2023',
          description: 'Exemplo de planilha com dados de vendas para demonstração',
          userId: user.id,
          isPublic: true,
          sheets: {
            create: {
              name: 'Vendas 2023',
              data: sampleSheetData,
            },
          },
        },
        include: {
          sheets: true,
        },
      });

      // eslint-disable-next-line no-console
      console.log(`✅ Workbook de exemplo criado: ${workbook.name}`);

      // Adicionar algumas interações de chat para demonstração
      await prisma.chatHistory.createMany({
        data: [
          {
            userId: user.id,
            workbookId: workbook.id,
            message: 'Calcule a soma total de vendas',
            response: 'A soma total de vendas é R$ 147.850,00',
          },
          {
            userId: user.id,
            workbookId: workbook.id,
            message: 'Crie um gráfico de barras com vendas por região',
            response:
              'Criei um gráfico de barras mostrando as vendas por região. O Sudeste apresenta o maior volume de vendas com R$ 58.250,00, seguido pelo Nordeste com R$ 37.500,00.',
          },
        ],
      });

      // eslint-disable-next-line no-console
      console.log('✅ Histórico de chat criado para demonstração');
    }
  }

  // eslint-disable-next-line no-console
  console.log('✅ Seed concluído com sucesso!');
}

main()
  .catch(e => {
    // eslint-disable-next-line no-console
    console.error('❌ Erro no seed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
