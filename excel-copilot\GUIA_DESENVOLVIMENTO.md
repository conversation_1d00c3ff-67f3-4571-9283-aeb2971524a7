# 🚀 **GUIA DE DESENVOLVIMENTO - EXCEL COPILOT**

## **📋 Índice**

1. [Setup Inicial](#setup-inicial)
2. [Configuração de Ambiente](#configuração-de-ambiente)
3. [Estrutura do Projeto](#estrutura-do-projeto)
4. [Desenvolvimento Local](#desenvolvimento-local)
5. [Testes](#testes)
6. [Deploy](#deploy)
7. [Troubleshooting](#troubleshooting)

---

## **🛠️ Setup Inicial**

### **Pré-requisitos**

- **Node.js** 18+
- **npm** ou **yarn**
- **Git**
- **PostgreSQL** (local ou Supabase)

### **Instalação**

```bash
# 1. Clonar o repositório
git clone <repository-url>
cd excel-copilot

# 2. Instalar dependências
npm install

# 3. Configurar ambiente
cp .env.local.template .env.local
# Editar .env.local com suas credenciais

# 4. Executar migrações (se necessário)
npm run db:migrate:dev

# 5. Iniciar desenvolvimento
npm run dev
```

---

## **⚙️ Configuração de Ambiente**

### **Sistema Unificado de Configuração**

O projeto usa um sistema unificado de configuração localizado em `src/config/unified-environment.ts`.

### **Variáveis Obrigatórias**

```bash
# Autenticação
AUTH_NEXTAUTH_SECRET="seu-secret-aqui"
AUTH_NEXTAUTH_URL="http://localhost:3000"

# Banco de Dados
DB_DATABASE_URL="postgresql://..."
DB_PROVIDER="postgresql"

# Google OAuth
AUTH_GOOGLE_CLIENT_ID="..."
AUTH_GOOGLE_CLIENT_SECRET="..."

# GitHub OAuth
AUTH_GITHUB_CLIENT_ID="..."
AUTH_GITHUB_CLIENT_SECRET="..."
```

### **Variáveis Opcionais (MCPs)**

```bash
# Vercel MCP
MCP_VERCEL_TOKEN="..."
MCP_VERCEL_PROJECT_ID="..."
MCP_VERCEL_TEAM_ID="..."

# Linear MCP
MCP_LINEAR_API_KEY="..."

# GitHub MCP
MCP_GITHUB_TOKEN="..."

# Stripe
STRIPE_SECRET_KEY="..."
STRIPE_PUBLISHABLE_KEY="..."
```

### **Nomenclatura Padronizada**

- **AUTH\_\*** - Autenticação
- **DB\_\*** - Banco de dados
- **AI\_\*** - Inteligência Artificial
- **MCP\_\*** - Model Context Protocol
- **STRIPE\_\*** - Pagamentos

---

## **📁 Estrutura do Projeto**

```
excel-copilot/
├── src/
│   ├── app/                 # Next.js App Router
│   │   ├── api/            # API Routes
│   │   ├── (auth)/         # Páginas de autenticação
│   │   └── dashboard/      # Dashboard principal
│   ├── components/         # Componentes React
│   ├── config/            # Configurações
│   │   ├── unified-environment.ts  # ✅ Sistema unificado
│   │   ├── validation-system.ts    # ✅ Validação
│   │   └── mcp-config.ts           # ✅ MCPs
│   ├── lib/               # Bibliotecas e utilitários
│   ├── server/            # Lógica do servidor
│   └── types/             # Definições TypeScript
├── __tests__/             # Testes
├── docs/                  # Documentação
└── scripts/               # Scripts de build/deploy
```

---

## **💻 Desenvolvimento Local**

### **Comandos Principais**

```bash
# Desenvolvimento
npm run dev              # Inicia servidor de desenvolvimento

# Build e Produção
npm run build           # Build para produção
npm run start           # Inicia servidor de produção

# Qualidade de Código
npm run type-check      # Verificação TypeScript
npm run lint            # ESLint
npm run lint:fix        # Corrigir problemas ESLint

# Testes
npm test                # Executar todos os testes
npm run test:watch      # Testes em modo watch
npm run test:coverage   # Cobertura de testes

# Banco de Dados
npm run db:migrate:dev  # Migrações desenvolvimento
npm run db:reset        # Reset do banco
npm run db:seed         # Seed de dados
```

### **Workflow de Desenvolvimento**

1. **Criar branch** para feature/bugfix
2. **Desenvolver** com `npm run dev`
3. **Testar** com `npm test`
4. **Verificar tipos** com `npm run type-check`
5. **Lint** com `npm run lint:fix`
6. **Commit** e **push**
7. **Criar PR**

---

## **🧪 Testes**

### **Estrutura de Testes**

```
__tests__/
├── config/                    # Testes de configuração
│   └── unified-environment.test.ts  # ✅ 17 testes
├── components/                # Testes de componentes
├── api/                      # Testes de API
└── integration/              # Testes de integração
```

### **Executar Testes**

```bash
# Todos os testes
npm test

# Testes específicos
npm test -- __tests__/config/unified-environment.test.ts

# Com cobertura
npm run test:coverage

# Modo watch
npm run test:watch
```

### **Escrever Testes**

```typescript
// Exemplo de teste
import { UnifiedEnvironment } from '@/config/unified-environment';

describe('Minha Feature', () => {
  test('deve funcionar corretamente', () => {
    const env = UnifiedEnvironment.getInstance();
    const config = env.getConfig();

    expect(config.APP_NAME).toBe('Excel Copilot');
  });
});
```

---

## **🚀 Deploy**

### **Vercel (Recomendado)**

1. **Conectar repositório** no Vercel
2. **Configurar variáveis** de ambiente
3. **Deploy automático** em push para main

### **Variáveis de Produção**

```bash
# No Vercel Dashboard
AUTH_NEXTAUTH_SECRET="production-secret"
AUTH_NEXTAUTH_URL="https://seu-dominio.vercel.app"
DB_DATABASE_URL="postgresql://production-url"
# ... outras variáveis
```

### **Checklist de Deploy**

- [ ] Todas as variáveis configuradas
- [ ] Testes passando
- [ ] Build sem erros
- [ ] Migrações aplicadas
- [ ] Domínio configurado

---

## **🔧 Troubleshooting**

### **Problemas Comuns**

#### **Erro: "AUTH_NEXTAUTH_SECRET não configurado"**

```bash
# Solução: Adicionar ao .env.local
AUTH_NEXTAUTH_SECRET="seu-secret-aqui"
```

#### **Erro: "Database connection failed"**

```bash
# Verificar URL do banco
DB_DATABASE_URL="postgresql://user:pass@host:port/db"
```

#### **Erro: "Module not found"**

```bash
# Limpar cache e reinstalar
rm -rf node_modules .next
npm install
```

#### **Testes falhando**

```bash
# Verificar configuração de ambiente de teste
NODE_ENV=test npm test
```

### **Logs e Debug**

```bash
# Logs detalhados
DEBUG=* npm run dev

# Verificar configuração
npm run config:validate

# Health check
curl http://localhost:3000/api/health
```

### **Performance**

```bash
# Analisar bundle
npm run analyze

# Verificar memória
npm run dev -- --inspect
```

---

## **📞 Suporte**

### **Recursos**

- **Documentação:** [README.md](README.md)
- **Configuração:** [CONFIGURACAO_AMBIENTE.md](CONFIGURACAO_AMBIENTE.md)
- **MCPs:** [docs/](docs/)

### **Contato**

- **Issues:** GitHub Issues
- **Discussões:** GitHub Discussions

---

**📝 Última atualização:** 29/01/2025  
**👨‍💻 Versão:** 1.0.0
