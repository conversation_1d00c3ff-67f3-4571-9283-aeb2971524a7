/**
 * Utilitários para respostas de API consistentes
 *
 * Este módulo fornece funções para criar respostas de API padronizadas,
 * garantindo consistência no formato e no tratamento de erros.
 */

import { NextResponse } from 'next/server';

/**
 * Estrutura padrão para resposta de erro de API
 */
type ApiErrorResponse = {
  code: string;
  message: string;
  details?: any;
  timestamp: string;
};

/**
 * Estrutura padrão para resposta de sucesso de API
 */
type ApiSuccessResponse<T> = {
  data: T;
  meta?: Record<string, any>;
};

/**
 * Funções utilitárias para criar respostas de API padronizadas
 */
export const ApiResponse = {
  /**
   * Cria uma resposta de sucesso padronizada
   *
   * @param data Dados a serem retornados
   * @param meta Metadados opcionais (paginação, etc)
   * @param status Código HTTP (padrão: 200)
   * @returns NextResponse formatada
   */
  success<T>(data: T, meta?: Record<string, any>, status = 200) {
    const response: ApiSuccessResponse<T> = {
      data,
      ...(meta && { meta }),
    };
    return NextResponse.json(response, { status });
  },

  /**
   * Cria uma resposta de erro padronizada
   *
   * @param message Mensagem de erro
   * @param code Código de erro personalizado
   * @param status Código HTTP (padrão: 500)
   * @param details Detalhes adicionais do erro
   * @returns NextResponse formatada
   */
  error(message: string, code = 'INTERNAL_ERROR', status = 500, details?: any) {
    const error: ApiErrorResponse = {
      code,
      message,
      ...(details && { details }),
      timestamp: new Date().toISOString(),
    };

    // Logging simplificado por console para manter a função pura
    // Em uma implementação real, seria melhor usar um serviço de logger centralizado
    if (process.env.NODE_ENV !== 'production') {
      console.error(`API Error [${code}]: ${message}`, details);
    }

    return NextResponse.json(error, { status });
  },

  /**
   * Cria uma resposta de erro 401 (Não autorizado)
   */
  unauthorized(message = 'Não autorizado', details?: any) {
    return this.error(message, 'UNAUTHORIZED', 401, details);
  },

  /**
   * Cria uma resposta de erro 400 (Requisição inválida)
   */
  badRequest(message: string, details?: any) {
    return this.error(message, 'BAD_REQUEST', 400, details);
  },

  /**
   * Cria uma resposta de erro 404 (Não encontrado)
   */
  notFound(message = 'Recurso não encontrado', details?: any) {
    return this.error(message, 'NOT_FOUND', 404, details);
  },

  /**
   * Cria uma resposta de erro 403 (Proibido)
   */
  forbidden(message = 'Acesso negado', details?: any) {
    return this.error(message, 'FORBIDDEN', 403, details);
  },

  /**
   * Cria uma resposta de erro 429 (Muitas requisições)
   */
  tooManyRequests(
    message = 'Muitas requisições. Tente novamente mais tarde.',
    retryAfterSeconds?: number
  ) {
    const headers: HeadersInit = {};
    if (retryAfterSeconds) {
      headers['Retry-After'] = retryAfterSeconds.toString();
    }

    return NextResponse.json(
      {
        code: 'RATE_LIMIT_EXCEEDED',
        message,
        timestamp: new Date().toISOString(),
      },
      {
        status: 429,
        headers,
      }
    );
  },
};
