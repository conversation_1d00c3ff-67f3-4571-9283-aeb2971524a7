/**
 * Utilitário para monitoramento de performance da aplicação
 * Registra métricas como tempo de carregamento, tempo de renderização e FPS.
 */

import { logger } from './logger';

export type MetricEvent =
  | 'page-load'
  | 'api-call'
  | 'render'
  | 'interaction'
  | 'ws-message'
  | 'excel-operation';

export interface PerformanceMetric {
  event: MetricEvent;
  duration: number;
  timestamp: number;
  metadata?: Record<string, unknown> | undefined;
}

// Armazena as métricas coletadas
const metrics: Record<MetricEvent, PerformanceMetric[]> = {
  'page-load': [],
  'api-call': [],
  render: [],
  interaction: [],
  'ws-message': [],
  'excel-operation': [],
};

/**
 * Marca o início de uma operação para medição de performance
 * @param markName Nome da marca única para a operação
 * @returns void
 */
export function markStart(markName: string): void {
  if (typeof performance === 'undefined') return;

  try {
    performance.mark(`${markName}-start`);
  } catch (error) {
    logger.warn('Erro ao criar performance mark', { markName, error });
  }
}

/**
 * Marca o fim de uma operação e registra a métrica de performance
 * @param markName Nome da marca (mesmo usado em markStart)
 * @param event Tipo de evento sendo medido
 * @param metadata Dados adicionais sobre a operação
 * @returns Duração da operação em ms ou 0 se falhar
 */
export function markEnd(
  markName: string,
  event: MetricEvent,
  metadata?: Record<string, unknown>
): number {
  if (typeof performance === 'undefined') return 0;

  try {
    // Marca o fim da operação
    performance.mark(`${markName}-end`);

    // Mede o tempo entre início e fim
    const measureName = `${event}-${markName}`;
    performance.measure(measureName, `${markName}-start`, `${markName}-end`);

    // Obtém a medição
    const entries = performance.getEntriesByName(measureName, 'measure');
    const duration = entries.length > 0 && entries[0] ? entries[0].duration : 0;

    // Registra a métrica
    recordMetric({
      event,
      duration,
      timestamp: Date.now(),
      metadata,
    });

    // Limpa as marcas para não acumular
    performance.clearMarks(`${markName}-start`);
    performance.clearMarks(`${markName}-end`);
    performance.clearMeasures(measureName);

    return duration;
  } catch (error) {
    logger.warn('Erro ao finalizar performance mark', { markName, error });
    return 0;
  }
}

/**
 * Registra uma métrica de performance no histórico
 * @param metric Métrica a ser registrada
 */
function recordMetric(metric: PerformanceMetric): void {
  if (!metrics[metric.event]) {
    metrics[metric.event] = [];
  }

  metrics[metric.event].push(metric);

  // Limita o histórico para não consumir muita memória
  if (metrics[metric.event].length > 100) {
    metrics[metric.event].shift();
  }
}

/**
 * Obtém as estatísticas de performance para um tipo de evento
 * @param event Tipo de evento para obter estatísticas
 * @returns Objeto com estatísticas ou null se não houver dados
 */
export function getStats(event: MetricEvent): {
  avg: number;
  min: number;
  max: number;
  count: number;
  p95: number;
} | null {
  const eventMetrics = metrics[event];

  if (!eventMetrics || eventMetrics.length === 0) {
    return null;
  }

  const durations = eventMetrics.map(m => m.duration);
  const avg = durations.reduce((sum, d) => sum + d, 0) / durations.length;

  // Valores podem estar vazios se não houver métricas, então fornecemos valores default
  const sortedDurations = [...durations].sort((a, b) => a - b);
  // Garantir que os valores nunca são undefined usando valores default de 0
  const min = sortedDurations.length > 0 ? sortedDurations[0] : 0;
  const max = sortedDurations.length > 0 ? sortedDurations[sortedDurations.length - 1] : 0;
  const count = eventMetrics.length;

  // Percentil 95
  const p95Index = Math.floor(sortedDurations.length * 0.95);
  const p95 = sortedDurations.length > 0 && p95Index >= 0 ? sortedDurations[p95Index] : 0;

  // Garantir que todos os valores no retorno são números definidos
  return {
    avg,
    min: min as number,
    max: max as number,
    count,
    p95: p95 as number,
  };
}

/**
 * Limpa as métricas coletadas para um tipo de evento
 * @param event Tipo de evento para limpar ou 'all' para todos
 */
export function clearMetrics(event: MetricEvent | 'all'): void {
  if (event === 'all') {
    Object.keys(metrics).forEach(key => {
      metrics[key as MetricEvent] = [];
    });
  } else {
    metrics[event] = [];
  }
}

/**
 * Obtém métricas básicas do navegador (se disponível)
 * @returns Objeto com métricas do navegador
 */
export function getBrowserMetrics(): Record<string, number> {
  if (typeof performance === 'undefined' || typeof window === 'undefined') {
    return {};
  }

  try {
    const result: Record<string, number> = {};

    // Tempo de carregamento da página
    if (performance.timing) {
      const timing = performance.timing;
      result.loadTime = timing.loadEventEnd - timing.navigationStart;
      result.domReady = timing.domContentLoadedEventEnd - timing.navigationStart;
      result.firstPaint = timing.responseStart - timing.navigationStart;
    }

    // Métricas de memória (Chrome apenas)
    const extendedPerformance = performance as import('@/types/global-types').ExtendedPerformance;
    const memory = extendedPerformance.memory;
    if (memory) {
      result.jsHeapSizeLimit = memory.jsHeapSizeLimit;
      result.totalJSHeapSize = memory.totalJSHeapSize;
      result.usedJSHeapSize = memory.usedJSHeapSize;
    }

    return result;
  } catch (error) {
    logger.warn('Erro ao obter métricas do navegador', { error });
    return {};
  }
}
