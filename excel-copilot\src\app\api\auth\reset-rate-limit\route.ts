import { NextRequest, NextResponse } from 'next/server';

import { ENV } from '@/config/unified-environment';
import { clearOAuthRateLimit } from '@/lib/middleware/oauth-limiter';

export const dynamic = 'force-dynamic';

/**
 * Endpoint para resetar rate limit OAuth (apenas em desenvolvimento)
 */
export async function POST(request: NextRequest) {
  try {
    // Apenas permitir em desenvolvimento
    if (ENV.IS_PRODUCTION) {
      return NextResponse.json(
        {
          error: 'Não disponível em produção',
          message: 'Este endpoint só está disponível em desenvolvimento',
        },
        { status: 403 }
      );
    }

    // Obter IP do usuário
    const ip =
      request.headers.get('x-forwarded-for') ||
      request.headers.get('x-real-ip') ||
      request.ip ||
      '::1';

    const userAgent = request.headers.get('user-agent') || undefined;

    // Limpar rate limit
    const cleared = clearOAuthRateLimit(ip, userAgent);

    if (cleared) {
      return NextResponse.json({
        success: true,
        message: 'Rate limit resetado com sucesso',
        ip: ip,
        timestamp: new Date().toISOString(),
      });
    } else {
      return NextResponse.json({
        success: false,
        message: 'Nenhum rate limit encontrado para este IP',
        ip: ip,
        timestamp: new Date().toISOString(),
      });
    }
  } catch (error) {
    console.error('Erro ao resetar rate limit:', error);

    return NextResponse.json(
      {
        error: 'Erro interno',
        message: 'Erro ao resetar rate limit',
        details: error instanceof Error ? error.message : 'Erro desconhecido',
      },
      { status: 500 }
    );
  }
}

/**
 * Endpoint GET para verificar status do rate limit
 */
export async function GET(request: NextRequest) {
  try {
    // Apenas permitir em desenvolvimento
    if (ENV.IS_PRODUCTION) {
      return NextResponse.json(
        {
          error: 'Não disponível em produção',
        },
        { status: 403 }
      );
    }

    const ip =
      request.headers.get('x-forwarded-for') ||
      request.headers.get('x-real-ip') ||
      request.ip ||
      '::1';

    return NextResponse.json({
      ip: ip,
      message: 'Use POST para resetar o rate limit',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    return NextResponse.json(
      {
        error: 'Erro interno',
        message: error instanceof Error ? error.message : 'Erro desconhecido',
      },
      { status: 500 }
    );
  }
}
