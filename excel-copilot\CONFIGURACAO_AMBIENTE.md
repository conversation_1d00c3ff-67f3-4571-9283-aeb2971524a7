# 🔧 Configuração de Ambiente - Excel Copilot

## 📋 **Estrutura Limpa Implementada**

A configuração de ambiente foi **completamente reorganizada** para eliminar duplicações, conflitos e seguir as melhores práticas para projetos Next.js/SaaS.

### **📁 Arquivos de Configuração**

#### **✅ Mantidos (4 arquivos)**

1. **`.env.example`** - Template para novos desenvolvedores
2. **`.env.local`** - Configuração local de desenvolvimento (gitignored)
3. **`.env.test`** - Configuração específica para testes
4. **`jest.env.js`** - Configuração Jest para testes

#### **🗑️ Removidos (5 arquivos)**

1. **`.env`** - ❌ Continha credenciais de produção expostas
2. **`.env.production`** - ❌ Apenas comentários, desnecessário
3. **`.vercel/.env.development.local`** - ❌ Configuração obsoleta
4. **`VARIAVEIS_AMBIENTE_ADICIONAR.env`** - ❌ Apenas instruções
5. **`VARIAVEIS_CRITICAS_VERCEL.env`** - ❌ Duplicação desnecessária

---

## 🚀 **Como Configurar o Ambiente**

### **1. Desenvolvimento Local**

```bash
# 1. Copie o template
cp .env.example .env.local

# 2. Preencha suas credenciais reais no .env.local
# 3. O arquivo .env.local já está no .gitignore
```

### **2. Produção (Vercel)**

**As variáveis de produção devem ser configuradas APENAS no painel da Vercel:**

1. Acesse: https://vercel.com/dashboard
2. Vá em **Settings > Environment Variables**
3. Configure as variáveis necessárias
4. **NUNCA** commite credenciais de produção no repositório

### **3. Testes**

```bash
# O arquivo .env.test já está configurado com mocks
npm test
```

---

## 🔐 **Variáveis Essenciais por Ambiente**

### **Desenvolvimento (.env.local)**

```bash
# Básico
NODE_ENV="development"
NEXTAUTH_SECRET="sua-chave-secreta"
NEXTAUTH_URL="http://localhost:3000"

# OAuth
GOOGLE_CLIENT_ID="seu-google-client-id"
GOOGLE_CLIENT_SECRET="seu-google-client-secret"
GITHUB_CLIENT_ID="seu-github-client-id"
GITHUB_CLIENT_SECRET="seu-github-client-secret"

# Supabase
DATABASE_URL="sua-database-url"
NEXT_PUBLIC_SUPABASE_URL="sua-supabase-url"
NEXT_PUBLIC_SUPABASE_ANON_KEY="sua-supabase-anon-key"

# Vertex AI
VERTEX_AI_ENABLED="true"
VERTEX_AI_PROJECT_ID="seu-project-id"

# Stripe
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY="pk_test_..."
STRIPE_SECRET_KEY="sk_test_..."
```

### **Produção (Vercel Dashboard)**

```bash
# Básico
NODE_ENV="production"
NEXTAUTH_SECRET="chave-super-segura-produção"
NEXTAUTH_URL="https://seu-dominio.vercel.app"

# OAuth (URLs de callback atualizadas)
GOOGLE_CLIENT_ID="client-id-produção"
GOOGLE_CLIENT_SECRET="client-secret-produção"

# Supabase (credenciais de produção)
DATABASE_URL="postgresql://..."
NEXT_PUBLIC_SUPABASE_URL="https://..."

# Vertex AI (projeto de produção)
VERTEX_AI_PROJECT_ID="projeto-produção"

# Stripe (chaves live)
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY="pk_live_..."
STRIPE_SECRET_KEY="sk_live_..."

# Feature Flags
USE_MOCK_AI="false"
NEXT_PUBLIC_FORCE_PRODUCTION="true"
```

---

## 🛡️ **Segurança Implementada**

### **✅ Melhorias de Segurança**

1. **Credenciais removidas** do repositório
2. **Separação clara** entre desenvolvimento e produção
3. **Mocks configurados** para testes
4. **URLs de callback** corretas por ambiente
5. **Feature flags** apropriados por ambiente

### **🚨 Problemas Resolvidos**

1. **Duplicações eliminadas**: 50+ variáveis duplicadas removidas
2. **Conflitos resolvidos**: Configurações conflitantes corrigidas
3. **Exposição de credenciais**: Chaves reais removidas do repositório
4. **URLs incorretas**: Callbacks OAuth corrigidos
5. **Configuração obsoleta**: Arquivos desnecessários removidos

---

## 📖 **Guia de Migração**

### **Para Desenvolvedores**

```bash
# 1. Puxe as mudanças
git pull origin main

# 2. Configure seu ambiente local
cp .env.example .env.local

# 3. Preencha suas credenciais no .env.local
# 4. Teste o ambiente
npm run dev
```

### **Para Deploy em Produção**

1. **Configure no Vercel Dashboard** todas as variáveis necessárias
2. **Teste o deploy** em ambiente de preview primeiro
3. **Verifique OAuth** com URLs de callback corretas
4. **Monitore logs** após o deploy

---

## 🔍 **Verificação da Configuração**

### **Comandos de Verificação**

```bash
# Verificar se .env.local existe
ls -la .env.local

# Testar configuração de desenvolvimento
npm run dev

# Executar testes
npm test

# Verificar build de produção
npm run build
```

### **Checklist de Configuração**

- [ ] `.env.local` criado e preenchido
- [ ] OAuth funcionando em desenvolvimento
- [ ] Banco de dados conectando
- [ ] Vertex AI configurado
- [ ] Stripe funcionando
- [ ] Testes passando
- [ ] Build de produção funcionando

---

## 📞 **Suporte**

Se encontrar problemas com a configuração:

1. Verifique se seguiu todos os passos do guia
2. Confirme que as URLs de callback OAuth estão corretas
3. Teste cada serviço individualmente
4. Consulte os logs de erro para mais detalhes

**A nova estrutura garante maior segurança, organização e facilita a manutenção do projeto!** 🚀
