import * as excel from '@/lib/excel';
import * as columnOps from '@/lib/operations/columnOperations';
import * as advancedChartOps from '@/lib/operations/advancedChartOperations';

// Interfaces para definir tipos usados nos testes
interface SheetData {
  headers: string[];
  rows: number[][];
}

interface ColumnOperation {
  type: string;
  data: {
    column: string;
    operation: string;
  };
}

interface ChartOptions {
  xAxis: string;
  yAxis: string;
  title: string;
}

// Interface para definir operações de excel
interface ExcelModule {
  processOperations?: (data: SheetData, operations: ColumnOperation[]) => Promise<any>;
  filterData?: (
    data: SheetData,
    filter: { column: string; condition: string; value: number }
  ) => Promise<any>;
}

// Interface para definir operações de coluna
interface ColumnOperationsModule {
  sum?: (data: SheetData, column: string) => number;
  average?: (data: SheetData, column: string) => number;
  max?: (data: SheetData, column: string) => number;
  min?: (data: SheetData, column: string) => number;
  columnOperations?: {
    sum?: (data: SheetData, column: string) => number;
    average?: (data: SheetData, column: string) => number;
    max?: (data: SheetData, column: string) => number;
    min?: (data: SheetData, column: string) => number;
  };
}

// Interface para definir operações de gráficos
interface ChartOperationsModule {
  createBarChart?: (data: SheetData, options: ChartOptions) => Promise<any>;
  extractAdvancedChartOperations?: {
    createBarChart?: (data: SheetData, options: ChartOptions) => Promise<any>;
  };
}

// Mocks
jest.mock('exceljs');

/**
 * Utility para medir o tempo de execução
 */
const measureExecutionTime = async (
  callback: () => Promise<unknown> | unknown
): Promise<number> => {
  const start = performance.now();
  await callback();
  const end = performance.now();
  return end - start;
};

describe('Testes de Performance', () => {
  describe('Operações Excel Básicas', () => {
    // Dados de planilha para testes
    const mockData: SheetData = {
      headers: ['A', 'B', 'C', 'D', 'E'],
      rows: Array(500)
        .fill(null)
        .map(() => [
          Math.random() * 100,
          Math.random() * 100,
          Math.random() * 100,
          Math.random() * 100,
          Math.random() * 100,
        ]),
    };

    test('Soma de coluna deve executar em menos de 50ms para 500 linhas', async () => {
      const columnOperations = columnOps as ColumnOperationsModule;

      const executionTime = await measureExecutionTime(() => {
        // Usar a função de soma do módulo columnOps
        return columnOperations.sum
          ? columnOperations.sum(mockData, 'A')
          : columnOperations.columnOperations?.sum
            ? columnOperations.columnOperations.sum(mockData, 'A')
            : 0;
      });

      console.info(`Tempo para somar coluna com 500 linhas: ${executionTime.toFixed(2)}ms`);
      expect(executionTime).toBeLessThan(50);
    });

    test('Média de coluna deve executar em menos de 50ms para 500 linhas', async () => {
      const columnOperations = columnOps as ColumnOperationsModule;

      const executionTime = await measureExecutionTime(() => {
        // Usar a função de média do módulo columnOps
        return columnOperations.average
          ? columnOperations.average(mockData, 'B')
          : columnOperations.columnOperations?.average
            ? columnOperations.columnOperations.average(mockData, 'B')
            : 0;
      });

      console.info(
        `Tempo para calcular média de coluna com 500 linhas: ${executionTime.toFixed(2)}ms`
      );
      expect(executionTime).toBeLessThan(50);
    });

    test('Encontrar máximo e mínimo deve executar em menos de 50ms para 500 linhas', async () => {
      const columnOperations = columnOps as ColumnOperationsModule;

      const executionTime = await measureExecutionTime(() => {
        // Usar as funções max e min do módulo columnOps
        if (columnOperations.max && columnOperations.min) {
          columnOperations.max(mockData, 'C');
          columnOperations.min(mockData, 'C');
        } else if (
          columnOperations.columnOperations?.max &&
          columnOperations.columnOperations?.min
        ) {
          columnOperations.columnOperations.max(mockData, 'C');
          columnOperations.columnOperations.min(mockData, 'C');
        }
      });

      console.info(
        `Tempo para encontrar máximo e mínimo em coluna com 500 linhas: ${executionTime.toFixed(2)}ms`
      );
      expect(executionTime).toBeLessThan(50);
    });
  });

  describe('Operações Excel Complexas', () => {
    // Dados de planilha maiores para testes de operações complexas
    const mockLargeData: SheetData = {
      headers: ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J'],
      rows: Array(1000)
        .fill(null)
        .map(() =>
          Array(10)
            .fill(null)
            .map(() => Math.random() * 100)
        ),
    };

    test('Processamento de múltiplas operações deve executar em menos de 200ms para 1000 linhas', async () => {
      const operations: ColumnOperation[] = [
        { type: 'COLUMN_OPERATION', data: { column: 'A', operation: 'SUM' } },
        { type: 'COLUMN_OPERATION', data: { column: 'B', operation: 'AVERAGE' } },
        { type: 'COLUMN_OPERATION', data: { column: 'C', operation: 'MAX' } },
        { type: 'COLUMN_OPERATION', data: { column: 'D', operation: 'MIN' } },
        { type: 'COLUMN_OPERATION', data: { column: 'E', operation: 'COUNT' } },
      ];

      const excelModule = excel as ExcelModule;

      const executionTime = await measureExecutionTime(async () => {
        try {
          if (excelModule.processOperations) {
            await excelModule.processOperations(mockLargeData, operations);
          }
        } catch (e) {
          // Ignorar erro em caso de mock incompleto
          console.warn('Ignorando erro de mock durante teste de performance');
        }
      });

      console.info(
        `Tempo para processar múltiplas operações em 1000 linhas: ${executionTime.toFixed(2)}ms`
      );
      expect(executionTime).toBeLessThan(200);
    });

    test('Geração de gráfico deve executar em menos de 100ms', async () => {
      const chartOps = advancedChartOps as ChartOperationsModule;

      const executionTime = await measureExecutionTime(async () => {
        try {
          // Usar a função de criação de gráfico do módulo advancedChartOps
          const createBarChart =
            chartOps.createBarChart || chartOps.extractAdvancedChartOperations?.createBarChart;

          if (createBarChart) {
            await createBarChart(mockLargeData, {
              xAxis: 'A',
              yAxis: 'B',
              title: 'Teste de Performance',
            });
          }
        } catch (e) {
          // Ignorar erro em caso de mock incompleto
          console.warn('Ignorando erro de mock durante teste de performance de gráfico');
        }
      });

      console.info(`Tempo para gerar gráfico de barras: ${executionTime.toFixed(2)}ms`);
      expect(executionTime).toBeLessThan(100);
    });
  });

  describe('Testes de Carga', () => {
    // Dados de planilha muito grandes para testes de carga
    const mockHugeData: SheetData = {
      headers: Array(26)
        .fill(null)
        .map((_, i) => String.fromCharCode(65 + i)), // A-Z
      rows: Array(5000)
        .fill(null)
        .map(() =>
          Array(26)
            .fill(null)
            .map(() => Math.random() * 1000)
        ),
    };

    test('Processamento de planilha grande deve ser executado em menos de 1000ms', async () => {
      const excelModule = excel as ExcelModule;

      const executionTime = await measureExecutionTime(async () => {
        try {
          // Simular operação de filtro em dados grandes
          if (excelModule.filterData) {
            await excelModule.filterData(mockHugeData, { column: 'A', condition: '>', value: 500 });
          }
        } catch (e) {
          // Ignorar erro em caso de mock incompleto
          console.warn('Ignorando erro de mock durante teste de carga');
        }
      });

      console.info(
        `Tempo para processar filtro em planilha de 5000 linhas x 26 colunas: ${executionTime.toFixed(2)}ms`
      );
      expect(executionTime).toBeLessThan(1000);
    });
  });
});
