import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { z } from 'zod';

import { logger } from '../../../../../../../lib/logger';
import { prisma } from '../../../../../../../server/db/client';

export const dynamic = 'force-dynamic';

// Schema para validação de parâmetros
const chunkQuerySchema = z.object({
  chunk: z.coerce.number().int().min(0),
  chunkSize: z.coerce.number().int().min(10).max(5000).default(1000),
});

/**
 * GET /api/workbooks/[id]/sheets/[sheetId]/chunks
 * Retorna um chunk específico de uma planilha grande
 *
 * Parâmetros de Query:
 * - chunk: Índice do chunk (0-based)
 * - chunkSize: Tamanho do chunk (default: 1000 linhas)
 */
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string; sheetId: string } }
) {
  try {
    // Verificar autenticação
    const session = await getServerSession();
    if (!session || !session.user) {
      return NextResponse.json(
        { error: 'Não autorizado', details: 'Autenticação necessária' },
        { status: 401 }
      );
    }

    // Validar parâmetros de rota
    if (!params.id || !params.sheetId) {
      return NextResponse.json(
        { error: 'Parâmetros inválidos', details: 'ID do workbook e da planilha são necessários' },
        { status: 400 }
      );
    }

    // Obter e validar parâmetros de query
    const { searchParams } = new URL(req.url);
    const queryResult = chunkQuerySchema.safeParse({
      chunk: searchParams.get('chunk'),
      chunkSize: searchParams.get('chunkSize'),
    });

    if (!queryResult.success) {
      return NextResponse.json(
        {
          error: 'Parâmetros de query inválidos',
          details: queryResult.error.format(),
        },
        { status: 400 }
      );
    }

    const { chunk, chunkSize } = queryResult.data;

    // Verificar acesso à planilha
    const workbook = await prisma.workbook.findUnique({
      where: {
        id: params.id,
        userId: session.user.id,
      },
      select: {
        id: true,
      },
    });

    if (!workbook) {
      return NextResponse.json(
        { error: 'Não encontrado', details: 'Workbook não encontrado ou sem permissão de acesso' },
        { status: 404 }
      );
    }

    // Buscar a planilha
    const sheet = await prisma.sheet.findUnique({
      where: {
        id: params.sheetId,
        workbookId: params.id,
      },
      select: {
        id: true,
        name: true,
        data: true,
      },
    });

    if (!sheet) {
      return NextResponse.json(
        { error: 'Não encontrado', details: 'Planilha não encontrada' },
        { status: 404 }
      );
    }

    // Parsing dos dados da planilha
    let sheetData;
    try {
      sheetData = JSON.parse(sheet.data || '{}');
    } catch (error) {
      logger.error(`Erro ao parsear dados da planilha ${params.sheetId}:`, error);
      return NextResponse.json(
        { error: 'Erro interno', details: 'Os dados da planilha estão corrompidos' },
        { status: 500 }
      );
    }

    // Verificar se os dados da planilha possuem o formato esperado
    if (!sheetData || !Array.isArray(sheetData.rows)) {
      return NextResponse.json(
        { error: 'Formato inválido', details: 'Dados da planilha em formato inválido' },
        { status: 500 }
      );
    }

    // Calcular informações para paginação
    const totalRows = sheetData.rows.length;
    const startRow = chunk * chunkSize;
    const endRow = Math.min(startRow + chunkSize, totalRows);

    // Extrair o chunk de dados
    const rowsChunk = sheetData.rows.slice(startRow, endRow);

    // Retornar apenas os dados necessários
    return NextResponse.json({
      name: sheet.name,
      headers: sheetData.headers || [],
      rowsChunk,
      chunk,
      chunkSize,
      totalRows,
      startRow,
      endRow,
    });
  } catch (error) {
    logger.error(`Erro ao obter chunk de planilha [${params.id}/${params.sheetId}]:`, error);

    return NextResponse.json(
      {
        error: 'Erro interno',
        details: error instanceof Error ? error.message : 'Erro desconhecido',
      },
      { status: 500 }
    );
  }
}
