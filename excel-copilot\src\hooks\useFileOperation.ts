import { useState, useCallback } from 'react';
import toast from 'react-hot-toast';

import { AnalyticsEventProperties } from '@/types/analytics';

import { useAnalytics } from './useAnalytics';

type FileOperationType = 'upload' | 'download' | 'process';

interface FileOperationOptions {
  operation: FileOperationType;
  successMessage?: string;
  errorMessage?: string;
  trackEvent?: boolean;
  trackEventName?: string;
  trackEventProperties?: AnalyticsEventProperties;
}

/**
 * Hook para abstrair operações de arquivos com feedback e estados de loading
 */
export function useFileOperation() {
  const [isLoading, setIsLoading] = useState(false);
  const { trackEvent } = useAnalytics();

  const executeOperation = useCallback(
    async <T>(
      operation: (args?: unknown) => Promise<T>,
      options: FileOperationOptions,
      args?: unknown
    ): Promise<T | null> => {
      setIsLoading(true);

      try {
        const result = await operation(args);

        // Show success message if provided
        if (options.successMessage) {
          toast.success(options.successMessage);
        }

        // Track event if enabled
        if (options.trackEvent && options.trackEventName) {
          trackEvent(options.trackEventName, options.trackEventProperties);
        }

        return result;
      } catch (error) {
        console.error(`Error during ${options.operation} operation:`, error);

        // Show error message
        toast.error(
          options.errorMessage ||
            `Erro ao ${
              options.operation === 'upload'
                ? 'fazer upload'
                : options.operation === 'download'
                  ? 'baixar'
                  : 'processar'
            } o arquivo.`
        );

        return null;
      } finally {
        setIsLoading(false);
      }
    },
    [trackEvent]
  );

  return {
    isLoading,
    executeOperation,
  };
}
