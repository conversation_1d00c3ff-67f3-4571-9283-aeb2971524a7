/**
 * Script para gerar relatório de cobertura de testes e verificar se atende à meta
 */
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Configuração
const TARGET_COVERAGE = 80; // Meta de cobertura
const REPORT_DIR = path.join(__dirname, '..', 'coverage');
const REPORT_FILE = path.join(REPORT_DIR, 'coverage-summary.json');
const REPORT_HTML = path.join(REPORT_DIR, 'index.html');

// Garantir que o diretório coverage existe
if (!fs.existsSync(REPORT_DIR)) {
  fs.mkdirSync(REPORT_DIR, { recursive: true });
}

console.log('Executando testes com cobertura...');
try {
  // Executar testes com cobertura
  execSync('npx jest --coverage', { stdio: 'inherit' });

  console.log('\nProcessando resultados da cobertura...');

  // Verificar se o arquivo de cobertura foi gerado
  if (!fs.existsSync(REPORT_FILE)) {
    console.error('Erro: Arquivo de cobertura não foi gerado.');
    process.exit(1);
  }

  // Ler o arquivo de cobertura
  const coverageData = JSON.parse(fs.readFileSync(REPORT_FILE, 'utf8'));
  const totalCoverage = coverageData.total;

  // Exibir cobertura geral
  console.log('\n=== Relatório de Cobertura ===');
  console.log(`Linhas: ${totalCoverage.lines.pct.toFixed(2)}%`);
  console.log(`Funções: ${totalCoverage.functions.pct.toFixed(2)}%`);
  console.log(`Branches: ${totalCoverage.branches.pct.toFixed(2)}%`);
  console.log(`Statements: ${totalCoverage.statements.pct.toFixed(2)}%`);

  // Identificar componentes com cobertura baixa
  console.log('\n=== Componentes com Cobertura Baixa ===');
  const lowCoverageFiles = [];

  for (const file in coverageData) {
    if (file === 'total') continue;

    const fileCoverage = coverageData[file];

    if (fileCoverage.statements.pct < TARGET_COVERAGE) {
      lowCoverageFiles.push({
        file,
        coverage: fileCoverage.statements.pct.toFixed(2),
      });
    }
  }

  // Ordenar do mais baixo para o mais alto
  lowCoverageFiles.sort((a, b) => parseFloat(a.coverage) - parseFloat(b.coverage));

  // Exibir os 10 arquivos com menor cobertura
  const TOP_N = Math.min(10, lowCoverageFiles.length);
  for (let i = 0; i < TOP_N; i++) {
    console.log(`${lowCoverageFiles[i].file}: ${lowCoverageFiles[i].coverage}%`);
  }

  // Verificar se a cobertura atende à meta
  const totalLineCoverage = totalCoverage.lines.pct;

  if (totalLineCoverage < TARGET_COVERAGE) {
    console.warn(
      `\nAtenção: Cobertura de linhas (${totalLineCoverage.toFixed(2)}%) está abaixo da meta (${TARGET_COVERAGE}%).`
    );
    console.log('Sugestão: Foque em adicionar testes para os componentes listados acima.');

    // Opcionalmente, pode falhar o CI se a cobertura estiver muito abaixo da meta
    if (totalLineCoverage < TARGET_COVERAGE * 0.8) {
      console.error(
        `Erro: Cobertura muito abaixo da meta! (${totalLineCoverage.toFixed(2)}% vs ${TARGET_COVERAGE}%)`
      );
      process.exit(1);
    }
  } else {
    console.log(
      `\nSucesso! Cobertura de linhas (${totalLineCoverage.toFixed(2)}%) atende à meta (${TARGET_COVERAGE}%).`
    );
  }

  console.log(`\nRelatório HTML completo disponível em: ${REPORT_HTML}`);
} catch (error) {
  console.error('Erro ao executar testes:', error.message);
  process.exit(1);
}
