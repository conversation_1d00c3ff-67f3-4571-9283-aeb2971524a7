export enum WebSocketStatus {
  Disconnected = 'disconnected',
  Connecting = 'connecting',
  Connected = 'connected',
  Error = 'error',
  Failed = 'failed',
}

export interface WebSocketMessage {
  id: string;
  type: string;
  operation?: string;
  data?: Record<string, unknown>;
  error?: string;
}

export interface OperationResult {
  success: boolean;
  data?: Record<string, unknown>;
  error?: string;
}

// Atualizando para alinhar com o servidor e usar enum
export enum ExcelOperationType {
  FORMULA = 'FORMULA',
  FILTER = 'FILTER',
  SORT = 'SORT',
  FORMAT = 'FORMAT',
  CHART = 'CHART',
  CELL_UPDATE = 'CELL_UPDATE',
  COLUMN_OPERATION = 'COLUMN_OPERATION',
  ROW_OPERATION = 'ROW_OPERATION',
  TABLE_OPERATION = 'TABLE_OPERATION',
  DATA_TRANSFORMATION = 'DATA_TRANSFORMATION',
}

export interface ExcelOperation {
  id?: string;
  type: ExcelOperationType | string;
  data?: Record<string, unknown>;
  params?: Record<string, unknown>;
}

export function createExcelOperation(
  type: ExcelOperationType | string,
  data: Record<string, unknown> = {}
): ExcelOperation {
  return {
    id: crypto.randomUUID(),
    type,
    data,
  };
}

// Interface for workbook information
export interface WorkbookInfo {
  id: string;
  name: string;
  path: string;
  sheets: string[];
  lastModified: string;
}

// Add other types as needed
