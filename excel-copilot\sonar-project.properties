sonar.projectKey=excel-copilot
sonar.organization=your-organization

# Dados do projeto
sonar.projectName=Excel Copilot
sonar.projectVersion=0.1.0

# Caminhos para os arquivos fonte
sonar.sources=src
sonar.exclusions=**/*.test.ts,**/*.test.tsx,**/__tests__/**,**/node_modules/**,**/.next/**

# Caminhos para arquivos de teste
sonar.tests=__tests__
sonar.test.inclusions=**/*.test.ts,**/*.test.tsx,**/__tests__/**

# Cobertura de testes
sonar.javascript.lcov.reportPaths=coverage/lcov.info
sonar.testExecutionReportPaths=test-report.xml

# TypeScript configuration
sonar.typescript.tsconfigPath=tsconfig.json

# Definições de encoding
sonar.sourceEncoding=UTF-8

# Links para análise de débito técnico
sonar.links.homepage=https://excel-copilot.exemplo.com
sonar.links.scm=https://github.com/seu-usuario/excel-copilot 