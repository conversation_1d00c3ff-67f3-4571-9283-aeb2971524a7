import { NextRequest, NextResponse } from 'next/server';

import { logger } from '@/lib/logger';
import { SupabaseMonitoringService } from '@/lib/supabase-integration';
import { apiRateLimiter } from '@/middleware/rate-limit';
import { ApiResponse } from '@/utils/api-response';

// Forçar o modo dinâmico para essa rota
export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

/**
 * GET /api/supabase/status
 * Obtém status geral da integração Supabase
 */
export async function GET(request: NextRequest) {
  try {
    // Aplicar rate limiting
    const rateLimitResult = await apiRateLimiter(request, new NextResponse());
    if (rateLimitResult) {
      return rateLimitResult;
    }

    // Verificar se temos as credenciais necessárias
    const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    const projectUrl = process.env.SUPABASE_URL;

    if (!serviceRoleKey || !projectUrl) {
      return ApiResponse.error(
        'Credenciais do Supabase não configuradas',
        'SUPABASE_NOT_CONFIGURED',
        500
      );
    }

    // Criar instância do serviço de monitoramento
    const supabaseService = new SupabaseMonitoringService({
      serviceRoleKey,
      projectUrl,
    });

    // Obter status do projeto
    const projectStatus = await supabaseService.getProjectStatus();

    // Obter métricas de performance
    let performanceMetrics = null;
    if (projectStatus.status !== 'down') {
      try {
        performanceMetrics = await supabaseService.getPerformanceMetrics();
      } catch (error) {
        logger.warn('Erro ao obter métricas de performance do Supabase:', error);
      }
    }

    const response = {
      status: projectStatus.status,
      message: projectStatus.message,
      project: projectStatus.project
        ? {
            id: projectStatus.project.id,
            name: projectStatus.project.name,
            status: projectStatus.project.status,
            created_at: projectStatus.project.created_at,
            database_version: projectStatus.project.database?.version,
          }
        : null,
      services: projectStatus.services,
      metrics: performanceMetrics
        ? {
            database: {
              tableCount: performanceMetrics.database.tableCount,
              totalSize: performanceMetrics.database.totalSize,
              connectionCount: performanceMetrics.database.connectionCount,
            },
            storage: {
              bucketCount: performanceMetrics.storage.bucketCount,
              totalObjects: performanceMetrics.storage.totalObjects,
              totalSize: performanceMetrics.storage.totalSize,
            },
            realtime: {
              activeConnections: performanceMetrics.realtime.activeConnections,
              channelsCount: performanceMetrics.realtime.channelsCount,
            },
          }
        : null,
      timestamp: new Date().toISOString(),
    };

    logger.info('Status Supabase obtido com sucesso', {
      status: projectStatus.status,
      services: projectStatus.services,
    });

    return ApiResponse.success(response);
  } catch (error) {
    logger.error('Erro ao obter status do Supabase', { error });

    if (error instanceof Error) {
      return ApiResponse.error(
        `Erro ao conectar com Supabase: ${error.message}`,
        'SUPABASE_API_ERROR',
        500
      );
    }

    return ApiResponse.error('Erro interno do servidor', 'INTERNAL_ERROR', 500);
  }
}

/**
 * POST /api/supabase/status
 * Força uma verificação de status (útil para debugging)
 */
export async function POST(request: NextRequest) {
  try {
    // Aplicar rate limiting
    const rateLimitResult = await apiRateLimiter(request, new NextResponse());
    if (rateLimitResult) {
      return rateLimitResult;
    }

    const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    const projectUrl = process.env.SUPABASE_URL;

    if (!serviceRoleKey || !projectUrl) {
      return ApiResponse.error(
        'Credenciais do Supabase não configuradas',
        'SUPABASE_NOT_CONFIGURED',
        500
      );
    }

    const supabaseService = new SupabaseMonitoringService({
      serviceRoleKey,
      projectUrl,
    });

    // Verificação forçada com mais detalhes
    const [projectStatus, databaseSummary, storageSummary] = await Promise.allSettled([
      supabaseService.getProjectStatus(),
      supabaseService.getDatabaseSummary(),
      supabaseService.getStorageSummary(),
    ]);

    const response = {
      status: 'forced_check_completed',
      projectStatus:
        projectStatus.status === 'fulfilled'
          ? projectStatus.value
          : {
              status: 'error',
              error: projectStatus.reason?.message || 'Erro desconhecido',
            },
      databaseSummary:
        databaseSummary.status === 'fulfilled'
          ? {
              totalTables: databaseSummary.value.totalTables,
              totalSize: databaseSummary.value.totalSize,
              largestTables: databaseSummary.value.largestTables.slice(0, 3).map(table => ({
                name: table.name,
                schema: table.schema,
                size: table.size,
              })),
            }
          : {
              error: databaseSummary.reason?.message || 'Erro ao acessar banco',
            },
      storageSummary:
        storageSummary.status === 'fulfilled'
          ? {
              totalBuckets: storageSummary.value.totalBuckets,
              publicBuckets: storageSummary.value.publicBuckets,
              privateBuckets: storageSummary.value.privateBuckets,
              buckets: storageSummary.value.buckets.map(bucket => ({
                name: bucket.name,
                public: bucket.public,
                created_at: bucket.created_at,
              })),
            }
          : {
              error: storageSummary.reason?.message || 'Erro ao acessar storage',
            },
      timestamp: new Date().toISOString(),
    };

    logger.info('Verificação forçada de status Supabase concluída');

    return ApiResponse.success(response);
  } catch (error) {
    logger.error('Erro na verificação forçada do Supabase', { error });

    if (error instanceof Error) {
      return ApiResponse.error(
        `Erro na verificação: ${error.message}`,
        'SUPABASE_CHECK_ERROR',
        500
      );
    }

    return ApiResponse.error('Erro interno do servidor', 'INTERNAL_ERROR', 500);
  }
}
