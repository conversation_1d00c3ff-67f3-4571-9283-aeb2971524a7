# Configuração de Serviços Externos para Excel-Copilot

Este documento fornece instruções detalhadas para configurar todos os serviços externos necessários para o Excel-Copilot em produção.

## 1. Google Vertex AI

A integração com IA do Excel Copilot utiliza o Google Vertex AI para processamento de linguagem natural. Siga os passos abaixo para configurar:

1. **Criar um projeto no Google Cloud Platform**:

   - Acesse [console.cloud.google.com](https://console.cloud.google.com)
   - Crie um novo projeto ou use um existente

2. **Ativar a API do Vertex AI**:

   - Pesquise por "Vertex AI API" no console
   - Clique em "Ativar"

3. **Criar uma conta de serviço**:

   - Navegue até "IAM & Admin" > "Contas de serviço"
   - Clique em "Criar conta de serviço"
   - Dê um nome como "excel-copilot-vertex-ai"
   - Atribua o papel "Vertex AI User" (roles/aiplatform.user)
   - Clique em "Concluir"

4. **Criar uma chave para a conta de serviço**:

   - Clique na conta de serviço criada
   - Vá para a aba "Chaves"
   - Clique em "Adicionar chave" > "Criar nova chave"
   - Selecione "JSON" e clique em "Criar"
   - O arquivo JSON será baixado automaticamente

5. **Configurar no Excel Copilot**:

   **Método 1: Usando o arquivo de credenciais**:

   - Renomeie o arquivo JSON baixado para `vertex-credentials.json`
   - Coloque na raiz do projeto Excel Copilot

   **Método 2: Usando variáveis de ambiente**:

   ```bash
   VERTEX_AI_ENABLED=true
   VERTEX_AI_PROJECT_ID="seu-project-id"
   VERTEX_AI_LOCATION="us-central1"
   VERTEX_AI_MODEL_NAME="gemini-1.5-pro"
   ```

   **Testar a configuração**:

   ```bash
   curl -X POST https://us-central1-aiplatform.googleapis.com/v1/projects/[PROJECT_ID]/locations/us-central1/publishers/google/models/gemini-1.5-pro:generateContent \
   -H "Authorization: Bearer $(gcloud auth print-access-token)" \
   -H "Content-Type: application/json" \
   -d '{
     "contents": [
       {
         "role": "user",
         "parts": [{"text": "Olá, como você está?"}]
       }
     ]
   }'
   ```

## 2. OAuth com Google

### Criação do Projeto no Google Cloud

1. Acesse o [Google Cloud Console](https://console.cloud.google.com/)
2. Crie um novo projeto ou use um existente
3. Navegue até "APIs & Services" > "Credentials"
4. Clique em "Create Credentials" > "OAuth client ID"
5. Configure a tela de consentimento:
   - Tipo de usuário: Externo
   - Nome do aplicativo: Excel Copilot
   - Domínios autorizados: seu-dominio.com
   - Email de suporte: <EMAIL>

### Configuração do OAuth Client

1. Tipo de aplicativo: Web application
2. Nome: Excel Copilot Web
3. URIs de redirecionamento autorizados:
   - `https://excel-copilot-eight.vercel.app/api/auth/callback/google`
   - `http://localhost:3000/api/auth/callback/google` (para desenvolvimento)
4. Clique em "Create"
5. Anote o Client ID e Client Secret gerados

### Configuração no Excel-Copilot

Adicione ao seu arquivo de ambiente:

```
GOOGLE_CLIENT_ID="seu-client-id-aqui"
GOOGLE_CLIENT_SECRET="seu-client-secret-aqui"
SKIP_AUTH_PROVIDERS="false"
```

## 3. OAuth com GitHub

### Criação do OAuth App no GitHub

1. Acesse as [configurações do GitHub](https://github.com/settings/developers)
2. Clique em "OAuth Apps" > "New OAuth App"
3. Preencha as informações:
   - Nome do aplicativo: Excel Copilot
   - URL da página inicial: `https://excel-copilot-eight.vercel.app`
   - Descrição do aplicativo: Excel Copilot - IA para planilhas
   - URL de callback: `https://excel-copilot-eight.vercel.app/api/auth/callback/github`
4. Clique em "Register application"
5. Gere um novo client secret e anote o Client ID e Client Secret

### Configuração no Excel-Copilot

Adicione ao seu arquivo de ambiente:

```
GITHUB_CLIENT_ID="seu-client-id-github-aqui"
GITHUB_CLIENT_SECRET="seu-client-secret-github-aqui"
```

## 4. Stripe para Pagamentos

### Criação da Conta e Obtenção de Chaves

1. Crie uma conta no [Stripe](https://dashboard.stripe.com/register)
2. No Dashboard, acesse "Developers" > "API keys"
3. Anote a Publishable key e a Secret key
   - **Importante:** Use as chaves de teste durante o desenvolvimento
   - Alterne para chaves de produção quando estiver pronto para lançar

### Configuração de Produtos e Preços

1. No Dashboard, acesse "Products"
2. Crie os produtos para seus planos:
   - **Plano Free**: Gratuito
   - **Plano Pro Mensal**: R$ 20/mês
   - **Plano Pro Anual**: R$ 200/ano
3. Para cada produto pago, defina um preço recorrente
4. Anote os IDs dos preços (começam com "price\_")

### Configuração de Webhook

1. No Dashboard, acesse "Developers" > "Webhooks"
2. Clique em "Add endpoint"
3. URL do endpoint: `https://seu-dominio.com/api/webhooks/stripe`
4. Eventos a escutar:
   - checkout.session.completed
   - customer.subscription.created
   - customer.subscription.updated
   - customer.subscription.deleted
   - invoice.payment_succeeded
   - invoice.payment_failed
5. Clique em "Add endpoint"
6. Anote a Signing Secret gerada (começa com "whsec\_")

### Configuração no Excel-Copilot

Adicione ao seu arquivo de ambiente:

```
STRIPE_SECRET_KEY="sk_live_sua_chave_secreta"
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY="pk_live_sua_chave_publicavel"
STRIPE_WEBHOOK_SECRET="whsec_seu_segredo_webhook"
NEXT_PUBLIC_STRIPE_PRICE_MONTHLY="price_id_do_plano_mensal"
NEXT_PUBLIC_STRIPE_PRICE_ANNUAL="price_id_do_plano_anual"
```

## Verificação da Configuração

Após configurar todos os serviços, você pode verificar se estão funcionando corretamente:

1. Teste a API do Google Vertex AI:

   ```bash
   curl -X POST \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer $(gcloud auth print-access-token)" \
     -d '{"contents":[{"parts":[{"text":"Hello, world"}]}]}' \
     "https://us-central1-aiplatform.googleapis.com/v1/projects/[PROJECT_ID]/locations/us-central1/publishers/google/models/gemini-1.5-pro:generateContent"
   ```

2. Teste o login com OAuth:

   - Acesse `https://seu-dominio.com/auth/signin`
   - Tente fazer login com Google e GitHub

3. Teste o Stripe:
   - Crie uma assinatura de teste com o cartão 4242 4242 4242 4242
   - Verifique se os webhooks estão sendo recebidos corretamente

## Próximos Passos

Após configurar todos os serviços externos, atualize o arquivo `excel-copilot/MIGRACAO_PRODUCAO.md` para marcar essas tarefas como concluídas e prossiga para os próximos passos da migração para produção.
