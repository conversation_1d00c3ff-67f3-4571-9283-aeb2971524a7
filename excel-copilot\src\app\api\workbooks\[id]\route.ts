import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';

import { prisma } from '@/server/db/client';

// Interface para tipagem do usuário da sessão
interface SessionUser {
  id: string;
  name?: string;
  email?: string;
}

export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

/**
 * GET /api/workbooks/[id]
 * Obter detalhes de uma planilha específica
 */
export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const workbookId = params.id;

    if (!workbookId) {
      return NextResponse.json({ error: 'ID da planilha é obrigatório' }, { status: 400 });
    }

    // Verificar autenticação
    const session = await getServerSession();
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Não autorizado. Faça login para continuar.' },
        { status: 401 }
      );
    }

    const userId = (session.user as SessionUser).id;

    // Buscar a planilha, incluindo informações de sheets
    const workbook = await prisma.workbook.findUnique({
      where: {
        id: workbookId,
      },
      include: {
        sheets: true,
      },
    });

    if (!workbook) {
      return NextResponse.json({ error: 'Planilha não encontrada' }, { status: 404 });
    }

    // Verificar se o usuário tem acesso à planilha (deve ser dono ou pública)
    if (workbook.userId !== userId && !workbook.isPublic) {
      // Verificar se está compartilhada com o usuário
      const isShared = await prisma.workbookShare.findFirst({
        where: {
          workbookId: workbookId,
          sharedWithUserId: userId,
        },
      });

      if (!isShared) {
        return NextResponse.json({ error: 'Acesso negado' }, { status: 403 });
      }
    }

    // Atualizar o campo lastAccessedAt para registrar o último acesso
    await prisma.workbook.update({
      where: {
        id: workbookId,
      },
      data: {
        lastAccessedAt: new Date(),
      },
    });

    return NextResponse.json({ workbook });
  } catch (error) {
    console.error('Erro ao buscar workbook:', error);
    return NextResponse.json({ error: 'Erro ao carregar planilha' }, { status: 500 });
  }
}

/**
 * PUT /api/workbooks/[id]
 * Atualizar uma planilha
 */
export async function PUT(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const workbookId = params.id;

    if (!workbookId) {
      return NextResponse.json({ error: 'ID da planilha é obrigatório' }, { status: 400 });
    }

    // Verificar autenticação
    const session = await getServerSession();
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Não autorizado. Faça login para continuar.' },
        { status: 401 }
      );
    }

    const userId = (session.user as SessionUser).id;

    // Verificar se a planilha existe e pertence ao usuário
    const workbook = await prisma.workbook.findFirst({
      where: {
        id: workbookId,
        userId,
      },
    });

    if (!workbook) {
      return NextResponse.json(
        { error: 'Planilha não encontrada ou sem permissão' },
        { status: 404 }
      );
    }

    // Obter dados da requisição
    const data = await request.json();
    const { name, description, isPublic } = data;

    // Validar dados
    if (!name) {
      return NextResponse.json({ error: 'Nome da planilha é obrigatório' }, { status: 400 });
    }

    // Atualizar planilha
    const updatedWorkbook = await prisma.workbook.update({
      where: {
        id: workbookId,
      },
      data: {
        name,
        description,
        isPublic: isPublic ?? false,
      },
    });

    return NextResponse.json({ workbook: updatedWorkbook });
  } catch (error) {
    console.error('Erro ao atualizar workbook:', error);
    return NextResponse.json({ error: 'Erro ao atualizar planilha' }, { status: 500 });
  }
}

/**
 * DELETE /api/workbooks/[id]
 * Excluir uma planilha
 */
export async function DELETE(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const workbookId = params.id;

    if (!workbookId) {
      return NextResponse.json({ error: 'ID da planilha é obrigatório' }, { status: 400 });
    }

    // Verificar autenticação
    const session = await getServerSession();
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Não autorizado. Faça login para continuar.' },
        { status: 401 }
      );
    }

    const userId = (session.user as SessionUser).id;

    // Verificar se a planilha existe e pertence ao usuário
    const workbook = await prisma.workbook.findFirst({
      where: {
        id: workbookId,
        userId,
      },
    });

    if (!workbook) {
      return NextResponse.json(
        { error: 'Planilha não encontrada ou sem permissão' },
        { status: 404 }
      );
    }

    // Excluir planilha (o cascade vai excluir as sheets)
    await prisma.workbook.delete({
      where: {
        id: workbookId,
      },
    });

    return NextResponse.json({ message: 'Planilha excluída com sucesso' });
  } catch (error) {
    console.error('Erro ao excluir workbook:', error);
    return NextResponse.json({ error: 'Erro ao excluir planilha' }, { status: 500 });
  }
}
