import { NextRequest } from 'next/server';

import { ENV } from '@/config/unified-environment';
import { logger } from '@/lib/logger';
import { VercelMonitoringService } from '@/lib/vercel-integration';
import { ApiResponse } from '@/utils/api-response';

// Configurar rota como dinâmica
export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

/**
 * GET /api/vercel/status
 * Obtém status geral do projeto no Vercel
 */
export async function GET(_request: NextRequest) {
  try {
    // Verificar se temos as credenciais necessárias
    const apiToken = process.env.MCP_VERCEL_TOKEN;
    const projectId = process.env.MCP_VERCEL_PROJECT_ID;
    const teamId = process.env.MCP_VERCEL_TEAM_ID;

    if (!apiToken) {
      return ApiResponse.error('VERCEL_API_TOKEN não configurado', 'VERCEL_CONFIG_ERROR', 500);
    }

    // Criar instância do serviço de monitoramento
    const vercelService = new VercelMonitoringService(apiToken, teamId, projectId);

    // Obter status do projeto
    const projectStatus = await vercelService.getProjectStatus();

    // Obter métricas de performance
    const performanceMetrics = await vercelService.getPerformanceMetrics();

    // Preparar resposta
    const response = {
      project: {
        name: 'excel-copilot',
        environment: ENV.IS_PRODUCTION ? 'production' : 'development',
        status: projectStatus.status,
        message: projectStatus.message,
        uptime: projectStatus.uptime,
        lastDeployment: projectStatus.lastDeployment
          ? {
              id: projectStatus.lastDeployment.uid,
              url: projectStatus.lastDeployment.url,
              state: projectStatus.lastDeployment.state,
              created: new Date(projectStatus.lastDeployment.created).toISOString(),
              target: projectStatus.lastDeployment.target,
            }
          : null,
      },
      metrics: {
        requests24h: performanceMetrics.requests,
        errors24h: performanceMetrics.errors,
        errorRate: performanceMetrics.errorRate,
        averageResponseTime: performanceMetrics.averageResponseTime,
        bandwidth24h: performanceMetrics.bandwidth,
        cacheHitRate: performanceMetrics.cacheHitRate,
        recentErrors: projectStatus.recentErrors,
      },
      timestamp: new Date().toISOString(),
    };

    logger.info('Status Vercel obtido com sucesso', {
      status: projectStatus.status,
      errors: projectStatus.recentErrors,
    });

    return ApiResponse.success(response);
  } catch (error) {
    logger.error('Erro ao obter status do Vercel', { error });

    if (error instanceof Error) {
      return ApiResponse.error(
        `Erro ao conectar com Vercel: ${error.message}`,
        'VERCEL_API_ERROR',
        500
      );
    }

    return ApiResponse.error('Erro interno do servidor', 'INTERNAL_ERROR', 500);
  }
}

/**
 * POST /api/vercel/status
 * Força uma verificação de status (útil para debugging)
 */
export async function POST(_request: NextRequest) {
  try {
    const apiToken = process.env.MCP_VERCEL_TOKEN;
    const projectId = process.env.MCP_VERCEL_PROJECT_ID;
    const teamId = process.env.MCP_VERCEL_TEAM_ID;

    if (!apiToken) {
      return ApiResponse.error('VERCEL_API_TOKEN não configurado', 'VERCEL_CONFIG_ERROR', 500);
    }

    const vercelService = new VercelMonitoringService(apiToken, teamId, projectId);

    // Verificar erros recentes com mais detalhes
    const errorCheck = await vercelService.getFilteredLogs({
      level: 'error',
      limit: 20,
    });

    // Obter status detalhado
    const projectStatus = await vercelService.getProjectStatus();

    const response = {
      status: projectStatus.status,
      message: projectStatus.message,
      recentErrors: errorCheck.length,
      errorDetails: errorCheck.slice(0, 5).map(log => ({
        timestamp: new Date(log.timestamp).toISOString(),
        message: log.message,
        source: log.source,
        deploymentId: log.deploymentId,
      })),
      lastDeployment: projectStatus.lastDeployment,
      timestamp: new Date().toISOString(),
    };

    return ApiResponse.success(response);
  } catch (error) {
    logger.error('Erro na verificação forçada do Vercel', { error });
    return ApiResponse.error('Erro ao verificar status', 'VERCEL_STATUS_ERROR', 500);
  }
}
