'use client';

import { motion, AnimatePresence } from 'framer-motion';
import {
  ArrowRight,
  FileSpreadsheet,
  BarChart3,
  Sparkles,
  Search,
  AlertCircle,
} from 'lucide-react';
import React, { useEffect, useState } from 'react';

import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useLocale } from '@/contexts/LocaleContext';

import { EmptyStateProps } from './types';

// Comandos expandidos e organizados por categoria
const SAMPLE_COMMANDS = [
  // Análise básica
  {
    command: 'Calcule a média dos valores na coluna Vendas',
    category: 'analysis',
    icon: <FileSpreadsheet className="h-3.5 w-3.5 mr-2 text-amber-500" />,
  },
  {
    command: 'Some os valores da coluna Receita',
    category: 'analysis',
    icon: <FileSpreadsheet className="h-3.5 w-3.5 mr-2 text-amber-500" />,
  },
  {
    command: 'Encontre o valor máximo da coluna Quantidade',
    category: 'analysis',
    icon: <FileSpreadsheet className="h-3.5 w-3.5 mr-2 text-amber-500" />,
  },

  // Visualização de dados
  {
    command: 'Crie um gráfico de barras com vendas por região',
    category: 'visualization',
    icon: <BarChart3 className="h-3.5 w-3.5 mr-2 text-emerald-500" />,
  },
  {
    command: 'Gere um gráfico de linha mostrando a tendência mensal',
    category: 'visualization',
    icon: <BarChart3 className="h-3.5 w-3.5 mr-2 text-emerald-500" />,
  },
  {
    command: 'Faça um gráfico de pizza com a distribuição por categoria',
    category: 'visualization',
    icon: <BarChart3 className="h-3.5 w-3.5 mr-2 text-emerald-500" />,
  },

  // Manipulação de dados
  {
    command: 'Ordene a tabela pelo valor da coluna Receita em ordem decrescente',
    category: 'manipulation',
    icon: <Search className="h-3.5 w-3.5 mr-2 text-blue-500" />,
  },
  {
    command: 'Filtre os dados onde o valor é maior que 1000',
    category: 'manipulation',
    icon: <Search className="h-3.5 w-3.5 mr-2 text-blue-500" />,
  },
  {
    command: 'Agrupe os dados por região e calcule a média de vendas',
    category: 'manipulation',
    icon: <Search className="h-3.5 w-3.5 mr-2 text-blue-500" />,
  },

  // Formatação
  {
    command: 'Destaque em verde as células com valores acima da média',
    category: 'formatting',
    icon: <Sparkles className="h-3.5 w-3.5 mr-2 text-purple-500" />,
  },
  {
    command: 'Formate a coluna de valores como moeda',
    category: 'formatting',
    icon: <Sparkles className="h-3.5 w-3.5 mr-2 text-purple-500" />,
  },
  {
    command: 'Adicione uma linha de totais ao final da tabela',
    category: 'formatting',
    icon: <Sparkles className="h-3.5 w-3.5 mr-2 text-purple-500" />,
  },
];

// Dados de exemplo para visualização
const _mockDatasets = [
  {
    label: 'Vendas (Últimos 6 meses)',
    data: [12, 19, 13, 15, 22, 27],
    backgroundColor: 'rgba(75, 192, 192, 0.5)',
    borderColor: 'rgba(75, 192, 192, 1)',
    type: 'bar',
  },
  {
    label: 'Metas',
    data: [15, 18, 16, 19, 21, 25],
    backgroundColor: 'rgba(153, 102, 255, 0.2)',
    borderColor: 'rgba(153, 102, 255, 1)',
    type: 'line',
  },
];

interface ChartPreviewProps {
  type: 'bar' | 'line' | 'pie';
  label: string;
  animDelay?: number;
}

// Componente miniatura de visualização
const ChartPreview = ({ type, label, animDelay = 0 }: ChartPreviewProps) => {
  // Valores aleatórios para demonstração visual
  const values = Array.from({ length: 6 }, () => Math.floor(Math.random() * 50) + 10);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: animDelay, duration: 0.5 }}
      className="rounded-lg p-3 bg-muted/30 hover:bg-muted/50 transition-all cursor-pointer w-full h-36"
    >
      <div className="text-xs font-medium mb-2 text-muted-foreground">{label}</div>
      <div className="h-24 w-full relative">
        {type === 'bar' && (
          <div className="flex items-end h-full w-full gap-2">
            {values.map((val, i) => (
              <div
                key={i}
                className="bg-gradient-to-t from-emerald-600 to-emerald-400 rounded-t-sm"
                style={{ height: `${val}%`, width: '100%' }}
              />
            ))}
          </div>
        )}

        {type === 'line' && (
          <div className="flex items-end h-full w-full relative">
            <svg className="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
              <polyline
                points={values.map((val, i) => `${i * 20},${100 - val}`).join(' ')}
                fill="none"
                stroke="rgb(16 185 129)"
                strokeWidth="3"
              />
            </svg>
          </div>
        )}

        {type === 'pie' && (
          <div className="flex items-center justify-center h-full">
            <div className="w-16 h-16 rounded-full relative overflow-hidden">
              <div
                className="absolute inset-0 bg-blue-500"
                style={{ clipPath: 'polygon(50% 50%, 0 0, 0 100%, 100% 100%)' }}
              ></div>
              <div
                className="absolute inset-0 bg-green-500"
                style={{ clipPath: 'polygon(50% 50%, 0 0, 100% 0, 100% 50%)' }}
              ></div>
              <div
                className="absolute inset-0 bg-yellow-500"
                style={{ clipPath: 'polygon(50% 50%, 100% 0, 100% 50%, 50% 100%)' }}
              ></div>
              <div
                className="absolute inset-0 bg-purple-500"
                style={{ clipPath: 'polygon(50% 50%, 50% 100%, 100% 100%, 100% 70%)' }}
              ></div>
            </div>
          </div>
        )}
      </div>
    </motion.div>
  );
};

export function EmptyState({
  showSuggestions = true,
  suggestions = [],
  onSuggestionClick,
  isExcelConnected = false,
}: EmptyStateProps) {
  const { _t } = useLocale();
  const [activeTab, setActiveTab] = useState('commands');
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredCommands, setFilteredCommands] = useState(SAMPLE_COMMANDS);

  useEffect(() => {
    if (searchQuery.trim() === '') {
      setFilteredCommands(SAMPLE_COMMANDS);
    } else {
      const filtered = SAMPLE_COMMANDS.filter(cmd =>
        cmd.command.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredCommands(filtered);
    }
  }, [searchQuery]);

  const handleSuggestionClick = (suggestion: { text: string; icon?: React.ReactNode }) => {
    onSuggestionClick?.(suggestion);
  };

  const defaultSuggestions = [
    {
      text: 'Crie uma tabela com vendas por região',
      icon: <FileSpreadsheet className="h-3.5 w-3.5" />,
    },
    { text: 'Como calcular média móvel?', icon: <Sparkles className="h-3.5 w-3.5" /> },
    {
      text: 'Ordene a coluna A em ordem decrescente',
      icon: <FileSpreadsheet className="h-3.5 w-3.5" />,
    },
    { text: 'Crie um gráfico de barras', icon: <Sparkles className="h-3.5 w-3.5" /> },
  ];

  // Sugestões específicas quando o Excel está conectado
  const excelConnectedSuggestions = [
    {
      text: 'Abra a planilha de vendas no Excel',
      icon: <FileSpreadsheet className="h-3.5 w-3.5" />,
    },
    { text: 'Formatar células selecionadas no Excel', icon: <Sparkles className="h-3.5 w-3.5" /> },
    {
      text: 'Aplique filtro na planilha aberta',
      icon: <FileSpreadsheet className="h-3.5 w-3.5" />,
    },
    {
      text: 'Crie um gráfico com os dados selecionados',
      icon: <Sparkles className="h-3.5 w-3.5" />,
    },
  ];

  // Escolher as sugestões a serem exibidas
  const suggestionsToShow = isExcelConnected
    ? excelConnectedSuggestions
    : suggestions.length > 0
      ? suggestions
      : defaultSuggestions;

  return (
    <div className="flex flex-col h-full py-4 px-2">
      <motion.div
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
        className="mb-4 text-center"
      >
        <div className="mb-4">
          {isExcelConnected ? (
            <div className="h-12 w-12 rounded-full bg-green-100 flex items-center justify-center text-green-600 mb-2">
              <FileSpreadsheet className="h-6 w-6" />
            </div>
          ) : (
            <div className="h-12 w-12 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 mb-2">
              <Sparkles className="h-6 w-6" />
            </div>
          )}
        </div>

        <h3 className="text-lg font-medium mb-2">
          {isExcelConnected ? 'Excel Conectado' : 'Como posso ajudar hoje?'}
        </h3>

        <p className="text-sm text-muted-foreground mb-6 max-w-md">
          {isExcelConnected
            ? 'Agora você pode interagir diretamente com o Excel instalado. Os comandos serão executados no Excel em tempo real.'
            : 'Faça perguntas ou solicite operações em planilhas. Sou especializado em ajudar com análises de dados e manipulação de Excel.'}
        </p>
      </motion.div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid grid-cols-3 mb-4">
          <TabsTrigger value="commands">Comandos</TabsTrigger>
          <TabsTrigger value="examples">Visualizações</TabsTrigger>
          <TabsTrigger value="templates">Templates</TabsTrigger>
        </TabsList>

        <TabsContent value="commands" className="space-y-4">
          <div className="relative">
            <input
              type="text"
              placeholder="Pesquise comandos..."
              className="w-full px-3 py-2 text-sm rounded-md border border-input bg-background ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-primary"
              value={searchQuery}
              onChange={e => setSearchQuery(e.target.value)}
            />
            <Search className="absolute right-3 top-2.5 h-4 w-4 text-muted-foreground" />
          </div>

          <AnimatePresence>
            <div className="space-y-1.5 max-h-[230px] overflow-y-auto pr-1">
              {filteredCommands.map((cmd, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: -5 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.03, duration: 0.2 }}
                  className="flex items-center text-sm bg-muted/20 hover:bg-muted/40 transition-colors rounded-md p-2.5 cursor-pointer group"
                  whileHover={{ x: 3 }}
                >
                  {cmd.icon}
                  <span className="flex-1">{cmd.command}</span>
                  <ArrowRight className="h-3.5 w-3.5 text-primary opacity-0 group-hover:opacity-100 transition-opacity" />
                </motion.div>
              ))}
            </div>
          </AnimatePresence>
        </TabsContent>

        <TabsContent value="examples">
          <div className="grid grid-cols-2 gap-3 max-h-[250px] overflow-y-auto pr-1">
            <ChartPreview type="bar" label="Comparativo por Região" animDelay={0.1} />
            <ChartPreview type="line" label="Tendência de Vendas" animDelay={0.2} />
            <ChartPreview type="pie" label="Distribuição de Categorias" animDelay={0.3} />
            <ChartPreview type="bar" label="Performance por Produto" animDelay={0.4} />
          </div>
        </TabsContent>

        <TabsContent value="templates">
          <div className="space-y-3 max-h-[250px] overflow-y-auto pr-1">
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1, duration: 0.3 }}
              className="border rounded-lg p-3 hover:border-primary/50 hover:bg-muted/10 transition-all cursor-pointer"
            >
              <div className="font-medium">Análise de vendas mensal</div>
              <p className="text-xs text-muted-foreground mt-1">
                Cria uma análise completa com tabelas dinâmicas, gráficos e métricas de desempenho.
              </p>
              <Button variant="outline" size="sm" className="mt-2 w-full">
                Usar Template
              </Button>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2, duration: 0.3 }}
              className="border rounded-lg p-3 hover:border-primary/50 hover:bg-muted/10 transition-all cursor-pointer"
            >
              <div className="font-medium">Dashboard financeiro</div>
              <p className="text-xs text-muted-foreground mt-1">
                Transforma seus dados em um dashboard completo com KPIs, gráficos e projeções.
              </p>
              <Button variant="outline" size="sm" className="mt-2 w-full">
                Usar Template
              </Button>
            </motion.div>
          </div>
        </TabsContent>
      </Tabs>

      {showSuggestions && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-2 w-full max-w-lg mt-4">
          {suggestionsToShow.map((suggestion, index) => (
            <Card
              key={index}
              className="p-2 cursor-pointer hover:bg-accent transition-colors flex items-center gap-2 text-sm"
              onClick={() => handleSuggestionClick(suggestion)}
            >
              {suggestion.icon || <AlertCircle className="h-3.5 w-3.5" />}
              <span>{suggestion.text}</span>
            </Card>
          ))}
        </div>
      )}

      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.5, duration: 0.3 }}
        className="text-xs text-center text-muted-foreground mt-4"
      >
        <span className="inline-flex items-center">
          <Sparkles className="h-3 w-3 mr-1 text-primary" />
          Digite qualquer comando ou tente um dos exemplos acima
        </span>
      </motion.div>
    </div>
  );
}
