import { useCallback, useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';

import { useSpreadsheetContext } from '../SpreadsheetContext';

interface ApiUsageInfo {
  used: number;
  limit: number;
}

/**
 * Hook para gerenciar estados de UI do SpreadsheetEditor
 */
export function useSpreadsheetUI() {
  const { state, actions } = useSpreadsheetContext();
  const router = useRouter();

  // Estados locais específicos da UI
  const [apiUsageInfo, setApiUsageInfo] = useState<ApiUsageInfo | null>(null);

  // Verificar se é visualização mobile
  useEffect(() => {
    const checkMobileView = () => {
      actions.updateUI({ isMobileView: window.innerWidth < 768 });
    };

    checkMobileView();
    window.addEventListener('resize', checkMobileView);

    return () => {
      window.removeEventListener('resize', checkMobileView);
    };
  }, [actions]);

  // Verificar primeira visita para mostrar tutorial
  useEffect(() => {
    const hasVisitedBefore = localStorage.getItem('excel_copilot_visited');
    if (!hasVisitedBefore) {
      actions.updateUI({
        isFirstVisit: true,
        showTutorial: true,
      });
      localStorage.setItem('excel_copilot_visited', 'true');
    }
  }, [actions]);

  // Buscar informações de uso da API
  useEffect(() => {
    const fetchApiUsage = async () => {
      try {
        const response = await fetch('/api/user/api-usage');
        if (response.ok) {
          const data = await response.json();
          setApiUsageInfo({
            used: data.apiCallsUsed,
            limit: data.apiCallsLimit,
          });

          // Se o usuário estiver acima de 80% do limite e no plano gratuito, mostrar modal
          if (data.plan === 'free' && data.apiCallsUsed / data.apiCallsLimit >= 0.8) {
            actions.updateUI({ showUpgradeModal: true });
          }
        }
      } catch (error) {
        console.error('Erro ao buscar informações de uso:', error);
      }
    };

    fetchApiUsage();
  }, [actions]);

  // Handlers para UI
  const toggleAIPanel = useCallback(() => {
    actions.updateUI({ aiPanelCollapsed: !state.ui.aiPanelCollapsed });
  }, [actions, state.ui.aiPanelCollapsed]);

  const toggleCommandPalette = useCallback(() => {
    actions.updateUI({ showCommandPalette: !state.ui.showCommandPalette });
  }, [actions, state.ui.showCommandPalette]);

  const toggleMobileChat = useCallback(() => {
    actions.updateUI({ showMobileChat: !state.ui.showMobileChat });
  }, [actions, state.ui.showMobileChat]);

  const toggleFullScreen = useCallback(() => {
    actions.updateUI({ isFullScreen: !state.ui.isFullScreen });
  }, [actions, state.ui.isFullScreen]);

  const showAIIndicator = useCallback(() => {
    actions.updateUI({ showAiIndicator: true });
    setTimeout(() => {
      actions.updateUI({ showAiIndicator: false });
    }, 3000);
  }, [actions]);

  const handleChatInputChange = useCallback((text: string) => {
    actions.updateUI({
      inputText: text,
      showSuggestions: text.trim() === '',
    });
  }, [actions]);

  // Handlers para tutorial
  const handleNextTutorialStep = useCallback(() => {
    const nextStep = state.ui.tutorialStep + 1;
    const maxSteps = 4; // Número total de passos do tutorial

    if (nextStep < maxSteps) {
      actions.updateUI({ tutorialStep: nextStep });
    } else {
      actions.updateUI({ showTutorial: false });
    }
  }, [actions, state.ui.tutorialStep]);

  const handleCloseTutorial = useCallback(() => {
    actions.updateUI({ showTutorial: false });
  }, [actions]);

  // Handlers para upgrade
  const handleUpgradeProClick = useCallback(() => {
    router.push('/pricing');
  }, [router]);

  const handleTrialClick = useCallback(() => {
    router.push('/api/checkout/trial');
  }, [router]);

  // Handler para navegação
  const handleNavigationClick = useCallback(
    (route: string) => {
      if (state.ui.isSaving) {
        toast.info('Aguarde', {
          description: 'Concluindo operações atuais antes de navegar...',
          duration: 2000,
        });
        return;
      }

      // Verificar se há alterações não salvas
      if (state.history.length > 1) {
        if (confirm('Há alterações não salvas. Deseja sair mesmo assim?')) {
          window.location.href = route;
        }
      } else {
        window.location.href = route;
      }
    },
    [state.ui.isSaving, state.history.length]
  );

  // Handler para fechar modais/painéis
  const handleEscapeKey = useCallback(() => {
    actions.updateUI({
      showCommandPalette: false,
      showKeyboardShortcuts: false,
      showMobileChat: false,
    });
    if (state.ui.isFullScreen) {
      actions.updateUI({ isFullScreen: false });
    }
  }, [actions, state.ui.isFullScreen]);

  // Aplicar efeito de tela cheia
  useEffect(() => {
    const element = document.documentElement;

    if (state.ui.isFullScreen) {
      if (element.requestFullscreen) {
        element.requestFullscreen();
      }
    } else {
      if (document.fullscreenElement && document.exitFullscreen) {
        document.exitFullscreen();
      }
    }
  }, [state.ui.isFullScreen]);

  return {
    // Estado da UI
    ui: state.ui,
    apiUsageInfo,

    // Handlers
    toggleAIPanel,
    toggleCommandPalette,
    toggleMobileChat,
    toggleFullScreen,
    showAIIndicator,
    handleChatInputChange,
    handleNextTutorialStep,
    handleCloseTutorial,
    handleUpgradeProClick,
    handleTrialClick,
    handleNavigationClick,
    handleEscapeKey,

    // Utilitários
    isMobile: state.ui.isMobileView,
    shouldShowUpgradeAlert: apiUsageInfo && 
      apiUsageInfo.used / apiUsageInfo.limit >= 0.8 && 
      apiUsageInfo.used / apiUsageInfo.limit < 1,
  };
}
