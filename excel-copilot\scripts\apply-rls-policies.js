const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');
require('dotenv').config({ path: '.env.local' });

async function applyRLSPolicies() {
  console.log('🔒 Aplicando políticas RLS (Row Level Security)...\n');

  // Verificar variáveis de ambiente
  if (!process.env.SUPABASE_URL || !process.env.SUPABASE_SERVICE_ROLE_KEY) {
    console.error('❌ Variáveis SUPABASE_URL e SUPABASE_SERVICE_ROLE_KEY são necessárias');
    process.exit(1);
  }

  const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_SERVICE_ROLE_KEY, {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  });

  try {
    // Ler arquivo SQL com as políticas
    const sqlFilePath = path.join(__dirname, 'setup-rls-policies.sql');

    if (!fs.existsSync(sqlFilePath)) {
      throw new Error(`Arquivo SQL não encontrado: ${sqlFilePath}`);
    }

    const sqlContent = fs.readFileSync(sqlFilePath, 'utf-8');
    console.log('📄 Arquivo SQL carregado com sucesso');
    console.log(`📊 Tamanho do arquivo: ${Math.round(sqlContent.length / 1024)}KB\n`);

    // Dividir o SQL em comandos individuais
    const sqlCommands = sqlContent
      .split(';')
      .map(cmd => cmd.trim())
      .filter(cmd => cmd.length > 0 && !cmd.startsWith('--'));

    console.log(`🔧 Executando ${sqlCommands.length} comandos SQL...\n`);

    let successCount = 0;
    let errorCount = 0;
    const errors = [];

    // Executar cada comando SQL
    for (let i = 0; i < sqlCommands.length; i++) {
      const command = sqlCommands[i];

      // Pular comentários e comandos vazios
      if (command.startsWith('--') || command.trim().length === 0) {
        continue;
      }

      try {
        console.log(`⚙️  Executando comando ${i + 1}/${sqlCommands.length}...`);

        // Executar comando SQL
        const { data, error } = await supabase.rpc('exec_sql', {
          sql_query: command + ';',
        });

        if (error) {
          // Alguns erros são esperados (como políticas já existentes)
          if (
            error.message.includes('already exists') ||
            error.message.includes('does not exist')
          ) {
            console.log(`⚠️  Aviso: ${error.message}`);
          } else {
            throw error;
          }
        }

        successCount++;

        // Mostrar progresso a cada 5 comandos
        if ((i + 1) % 5 === 0) {
          console.log(`✅ Progresso: ${i + 1}/${sqlCommands.length} comandos processados`);
        }
      } catch (error) {
        errorCount++;
        const errorMsg = `Comando ${i + 1}: ${error.message}`;
        errors.push(errorMsg);
        console.error(`❌ Erro no comando ${i + 1}:`, error.message);
      }
    }

    console.log('\n📊 Resumo da execução:');
    console.log(`✅ Comandos executados com sucesso: ${successCount}`);
    console.log(`❌ Comandos com erro: ${errorCount}`);

    if (errors.length > 0) {
      console.log('\n🚨 Erros encontrados:');
      errors.forEach((error, index) => {
        console.log(`   ${index + 1}. ${error}`);
      });
    }

    // Verificar políticas criadas
    console.log('\n🔍 Verificando políticas RLS criadas...');

    const { data: policies, error: policiesError } = await supabase
      .from('pg_policies')
      .select('tablename, policyname, cmd')
      .in('tablename', [
        'User',
        'Workbook',
        'Sheet',
        'Cell',
        'WorkbookShare',
        'Session',
        'Account',
      ]);

    if (policiesError) {
      console.error('❌ Erro ao verificar políticas:', policiesError.message);
    } else {
      console.log(`✅ Total de políticas encontradas: ${policies.length}`);

      // Agrupar por tabela
      const policiesByTable = policies.reduce((acc, policy) => {
        if (!acc[policy.tablename]) {
          acc[policy.tablename] = [];
        }
        acc[policy.tablename].push(policy);
        return acc;
      }, {});

      Object.entries(policiesByTable).forEach(([tableName, tablePolicies]) => {
        console.log(`   📋 ${tableName}: ${tablePolicies.length} políticas`);
        tablePolicies.forEach(policy => {
          console.log(`      - ${policy.policyname} (${policy.cmd})`);
        });
      });
    }

    // Testar uma política simples
    console.log('\n🧪 Testando políticas RLS...');

    try {
      // Tentar acessar tabela User sem autenticação (deve falhar)
      const { data: testData, error: testError } = await supabase
        .from('User')
        .select('id')
        .limit(1);

      if (testError) {
        console.log('✅ RLS funcionando: Acesso negado sem autenticação');
      } else {
        console.log('⚠️  RLS pode não estar funcionando corretamente');
      }
    } catch (error) {
      console.log('✅ RLS funcionando: Erro de acesso esperado');
    }

    console.log('\n🎉 Configuração de RLS concluída!');
    console.log('\n📝 Próximos passos:');
    console.log('   1. Testar autenticação com usuários reais');
    console.log('   2. Verificar isolamento de dados entre usuários');
    console.log('   3. Testar permissões de compartilhamento');
    console.log('   4. Monitorar logs de segurança');

    if (errorCount > 0) {
      console.log('\n⚠️  Alguns erros foram encontrados. Verifique se são críticos.');
      process.exit(1);
    }
  } catch (error) {
    console.error('\n💥 Erro fatal na aplicação das políticas RLS:', error.message);
    process.exit(1);
  }
}

// Função auxiliar para executar SQL (caso não exista a função rpc)
async function executeSQLDirect(supabase, sql) {
  try {
    const { data, error } = await supabase.rpc('exec_sql', { sql_query: sql });
    if (error) throw error;
    return { data, error: null };
  } catch (error) {
    // Se a função rpc não existir, tentar método alternativo
    console.log('⚠️  Função exec_sql não disponível, usando método alternativo...');

    // Para comandos DDL simples, podemos usar uma abordagem diferente
    return { data: null, error };
  }
}

// Executar configuração
applyRLSPolicies();
