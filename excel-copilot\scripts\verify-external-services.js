/**
 * Script para verificar a configuração dos serviços externos
 * Autor: Excel Copilot Team
 * Data: Junho 2023
 *
 * Este script verifica se todas as variáveis de ambiente necessárias
 * para os serviços externos estão configuradas.
 */

const dotenv = require('dotenv');
const path = require('path');
const axios = require('axios');
const chalk = require('chalk');
const fs = require('fs');

// Carrega variáveis de ambiente apropriadas
const envFile = process.env.NODE_ENV === 'production' ? '.env.production' : '.env.local';

const envPath = path.resolve(process.cwd(), envFile);

if (fs.existsSync(envPath)) {
  console.log(chalk.blue(`Carregando variáveis de ambiente de ${envFile}`));
  dotenv.config({ path: envPath });
} else {
  console.log(
    chalk.yellow(`Arquivo ${envFile} não encontrado, usando variáveis de ambiente do sistema`)
  );
  dotenv.config();
}

// Função para verificar se uma variável de ambiente está definida
function checkEnvVar(name, description, required = true) {
  const value = process.env[name];

  if (!value && required) {
    console.log(chalk.red(`❌ ${name}: Não configurado - ${description}`));
    return false;
  } else if (!value && !required) {
    console.log(chalk.yellow(`⚠️ ${name}: Não configurado (opcional) - ${description}`));
    return true;
  } else {
    const displayValue =
      name.includes('KEY') || name.includes('SECRET')
        ? `${value.substring(0, 4)}...${value.substring(value.length - 4)}`
        : value;

    console.log(chalk.green(`✅ ${name}: Configurado - ${displayValue}`));
    return true;
  }
}

// Função para testar a API do Vertex AI
async function testVertexAI() {
  const projectId = process.env.AI_VERTEX_PROJECT_ID;
  const vertexEnabled = process.env.AI_ENABLED === 'true';
  const credentialsFile = path.resolve(process.cwd(), 'vertex-credentials.json');
  const hasCredentialsFile = fs.existsSync(credentialsFile);

  if (process.env.AI_USE_MOCK === 'true') {
    console.log(chalk.yellow('⚠️ Teste do Vertex AI ignorado (modo mock ativado)'));
    return true;
  }

  if (!vertexEnabled) {
    console.log(
      chalk.yellow(
        '⚠️ Vertex AI está desabilitado. Configure VERTEX_AI_ENABLED=true para habilitar.'
      )
    );
    return true;
  }

  if (!projectId && !hasCredentialsFile) {
    console.log(
      chalk.red(
        '❌ Vertex AI: Configuração incompleta (VERTEX_AI_PROJECT_ID não definido e vertex-credentials.json não encontrado)'
      )
    );
    return false;
  }

  console.log(chalk.blue('🔄 Verificando configuração do Vertex AI...'));

  if (hasCredentialsFile) {
    console.log(chalk.green('✅ Arquivo de credenciais do Vertex AI encontrado'));
  } else {
    console.log(
      chalk.yellow(
        '⚠️ Arquivo vertex-credentials.json não encontrado, usando variáveis de ambiente'
      )
    );
  }

  return true; // Retornamos true pois não podemos testar a API do Vertex sem credenciais completas
}

// Função principal para verificar todos os serviços
async function verifyServices() {
  console.log(chalk.blue('🔍 Verificando configuração de serviços externos...'));
  console.log(chalk.blue('================================================'));

  let allGood = true;

  // 1. Verificar API do Vertex AI
  console.log(chalk.blue('\n📊 Google Vertex AI'));
  allGood =
    checkEnvVar('AI_ENABLED', 'Habilitar Vertex AI (deve ser "true" em produção)') && allGood;
  allGood =
    checkEnvVar('AI_USE_MOCK', 'Modo de mock para IA (deve ser "false" em produção)') && allGood;

  if (process.env.AI_ENABLED === 'true') {
    allGood =
      checkEnvVar(
        'AI_VERTEX_PROJECT_ID',
        'ID do projeto do Google Cloud para Vertex AI',
        !fs.existsSync(path.resolve(process.cwd(), 'vertex-credentials.json'))
      ) && allGood;
    allGood =
      checkEnvVar('AI_VERTEX_LOCATION', 'Localização do Vertex AI (ex: us-central1)', false) &&
      allGood;
    allGood =
      checkEnvVar(
        'AI_VERTEX_MODEL',
        'Nome do modelo Gemini a ser usado (ex: gemini-1.5-pro)',
        false
      ) && allGood;

    if (fs.existsSync(path.resolve(process.cwd(), 'vertex-credentials.json'))) {
      console.log(chalk.green('✅ vertex-credentials.json: Arquivo de credenciais encontrado'));
    } else {
      console.log(
        chalk.yellow(
          '⚠️ vertex-credentials.json: Arquivo não encontrado, usando variáveis de ambiente'
        )
      );
    }
  }

  // 2. Verificar OAuth do Google
  console.log(chalk.blue('\n🔐 Google OAuth'));
  allGood = checkEnvVar('AUTH_GOOGLE_CLIENT_ID', 'Client ID do Google OAuth') && allGood;
  allGood = checkEnvVar('AUTH_GOOGLE_CLIENT_SECRET', 'Client Secret do Google OAuth') && allGood;

  // 3. Verificar OAuth do GitHub
  console.log(chalk.blue('\n🔐 GitHub OAuth'));
  allGood = checkEnvVar('AUTH_GITHUB_CLIENT_ID', 'Client ID do GitHub OAuth') && allGood;
  allGood = checkEnvVar('AUTH_GITHUB_CLIENT_SECRET', 'Client Secret do GitHub OAuth') && allGood;

  // 4. Verificar Stripe
  console.log(chalk.blue('\n💳 Stripe'));
  allGood = checkEnvVar('STRIPE_SECRET_KEY', 'Chave secreta do Stripe') && allGood;
  allGood =
    checkEnvVar('NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY', 'Chave publicável do Stripe') && allGood;
  allGood = checkEnvVar('STRIPE_WEBHOOK_SECRET', 'Segredo do webhook do Stripe') && allGood;
  allGood =
    checkEnvVar('NEXT_PUBLIC_STRIPE_PRICE_MONTHLY', 'ID do preço mensal do Stripe') && allGood;
  allGood =
    checkEnvVar('NEXT_PUBLIC_STRIPE_PRICE_ANNUAL', 'ID do preço anual do Stripe') && allGood;

  // 5. Verificar outras configurações importantes
  console.log(chalk.blue('\n⚙️ Outras Configurações'));
  allGood = checkEnvVar('AUTH_NEXTAUTH_SECRET', 'Segredo para NextAuth') && allGood;
  allGood = checkEnvVar('AUTH_NEXTAUTH_URL', 'URL base para NextAuth') && allGood;
  allGood =
    checkEnvVar(
      'AUTH_SKIP_PROVIDERS',
      'Pular provedores de autenticação (deve ser "false" em produção)'
    ) && allGood;

  // 6. Testar APIs
  console.log(chalk.blue('\n🧪 Testes de API'));
  const vertexResult = await testVertexAI();
  allGood = vertexResult && allGood;

  // Resultado final
  console.log(chalk.blue('\n================================================'));

  if (allGood) {
    console.log(chalk.green('✅ Todos os serviços externos estão configurados corretamente!'));
    console.log(chalk.green('✅ O sistema está pronto para produção.'));
  } else {
    console.log(chalk.yellow('⚠️ Alguns serviços externos não estão configurados corretamente.'));
    console.log(
      chalk.yellow(
        '⚠️ Verifique as mensagens acima e consulte docs/EXTERNAL_SERVICES_SETUP.md para instruções.'
      )
    );
  }

  return allGood;
}

// Executa a verificação
verifyServices()
  .then(result => {
    process.exit(result ? 0 : 1);
  })
  .catch(error => {
    console.error(chalk.red(`Erro ao verificar serviços: ${error.message}`));
    process.exit(1);
  });
