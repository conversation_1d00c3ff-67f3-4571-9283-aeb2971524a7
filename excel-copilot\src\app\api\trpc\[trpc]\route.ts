import { fetchRequestHandler } from '@trpc/server/adapters/fetch';
import { NextRequest } from 'next/server';

import { appRouter } from '../../../../server/trpc/router';
import { createTRPCContext } from '../../../../server/trpc/trpc';

export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

const handler = async (req: NextRequest) => {
  return fetchRequestHandler({
    router: appRouter,
    createContext: createTRPCContext,
    req,
    endpoint: '/api/trpc',
  });
};

export { handler as GET, handler as POST };
