# Guia de Tipos Unificados no Excel Copilot

Este documento fornece orientações sobre como manter tipos unificados no projeto Excel Copilot, evitando definições duplicadas e inconsistentes.

## Definição Central de Enums

### ExcelOperationType

O enum `ExcelOperationType` é um componente central do sistema e deve ser mantido em um único local:

```typescript
// Definição central em src/types/index.ts
export enum ExcelOperationType {
  FORMULA = 'FORMULA',
  FILTER = 'FILTER',
  SORT = 'SORT',
  // ... outros valores
}
```

#### Regras para manter a consistência:

1. **Nunca redefina o enum** em outros arquivos. Em vez disso, apenas importe-o:

   ```typescript
   // Correto
   import { ExcelOperationType } from '@/types/index';

   // Incorreto - NUNCA faça isso
   export enum ExcelOperationType {}
   // ...
   ```

2. **Use re-exportação** quando precisar exportar o enum de outro módulo:

   ```typescript
   // Correto
   import { ExcelOperationType } from '@/types/index';
   export { ExcelOperationType };

   // Incorreto - NUNCA faça isso
   import { ExcelOperationType as BaseExcelOperationType } from '@/types/index';
   export enum ExcelOperationType {}
   // ...
   ```

3. **Para adicionar novos valores ao enum**:

   - Adicione sempre na definição original em `src/types/index.ts`
   - Documente cada valor com comentários para esclarecer seu propósito
   - Notifique a equipe sobre a adição para garantir que todos os casos de uso estejam cobertos

4. **Para compatibilidade**, use aliases no próprio enum:

   ```typescript
   export enum ExcelOperationType {
     COLUMN_OPERATION = 'COLUMN_OPERATION',
     COLUMN = 'COLUMN_OPERATION', // Alias para compatibilidade
     // ...
   }
   ```

## Interfaces e Tipos de Operação

### ExcelOperation

A interface `ExcelOperation` também deve seguir um padrão unificado:

```typescript
// Definição central em src/types/index.ts
export interface ExcelOperation {
  type: string | ExcelOperationType;
  data?: any;
  id?: string;
  // ... outras propriedades comuns
}
```

#### Regras para manter a consistência:

1. **Estenda a interface base** quando precisar de variações específicas:

   ```typescript
   // Correto
   import { ExcelOperation as BaseExcelOperation } from '@/types/index';

   interface ExtendedOperation extends BaseExcelOperation {
     additionalProperty: string;
   }
   ```

2. **Use genéricos** para tipar o campo data:

   ```typescript
   // Recomendado
   interface TypedOperation<T> extends ExcelOperation {
     data: T;
   }

   // Uso
   const operation: TypedOperation<FormulaData> = {
     type: ExcelOperationType.FORMULA,
     data: { formula: '=SUM(A1:A10)' },
   };
   ```

## Verificação de Conformidade

Para garantir a conformidade com estas diretrizes:

1. Execute o verificador de tipos após qualquer modificação:

   ```bash
   npm run typecheck
   ```

2. Utilize os scripts de correção, se necessário:

   ```bash
   npm run fix:types
   ```

3. Para problemas de duplicação ou inconsistência nos tipos:
   ```bash
   npm run fix:excel-type-unify
   ```

## Melhores Práticas Gerais

1. **Importe sempre do mesmo local** para garantir consistência:

   ```typescript
   import { ExcelOperationType, ExcelOperation } from '@/types/index';
   ```

2. **Evite type assertions** (`as`) sempre que possível, pois podem ocultar inconsistências:

   ```typescript
   // Evite
   const operation = { type: 'FORMULA', data } as ExcelOperation;

   // Prefira
   const operation: ExcelOperation = {
     type: ExcelOperationType.FORMULA,
     data,
   };
   ```

3. **Documente exceções** quando for absolutamente necessário usar soluções não padronizadas

---

Seguindo estas diretrizes, manteremos um sistema de tipos consistente e robusto, reduzindo erros e facilitando a manutenção do código.
