/**
 * Tipos para componentes da UI
 * Este arquivo fornece aliases para componentes que possam ter problemas de importação
 */

import React from 'react';

// Re-exportar tipos básicos de formulário
import { ErrorMessageProps } from '@/components/ui/error-message';
import { InputProps } from '@/components/ui/input';
import { TextareaProps } from '@/components/ui/textarea';

// Exportar aliases de tipos
export type { InputProps };
export type { TextareaProps };
export type { ErrorMessageProps };

// Re-exportar componentes (TS ignorará estas exportações se os arquivos não existirem,
// mas permitirá que o código compile)
// export { Input } from '@/components/ui/input';
// export { Textarea } from '@/components/ui/textarea';
// export { ErrorMessage } from '@/components/ui/error-message';

// Tipos para componentes que possam ser problemáticos
export interface UIComponentProps {
  className?: string;
  children?: React.ReactNode;
}

// Tipos de tema
export type ThemeType = 'light' | 'dark' | 'system';

// Tipos para componentes de formulário
export interface FormFieldProps {
  name: string;
  label?: string;
  error?: string;
  required?: boolean;
}

// Tipos para componentes de alerta e notificação
export interface AlertProps {
  title?: string;
  description?: string;
  variant?: 'default' | 'destructive';
  className?: string;
}

// Tipos para componentes de carregamento
export interface LoadingProps {
  size?: 'sm' | 'md' | 'lg';
  label?: string;
  className?: string;
}

// Componentes UI - Input removido para evitar conflitos de tipagem

declare module '@/components/ui/textarea' {
  interface TextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
    wrapperClassName?: string;
    _wrapperClassName?: string;
  }

  const Textarea: React.ForwardRefExoticComponent<
    TextareaProps & React.RefAttributes<HTMLTextAreaElement>
  >;

  export { Textarea };
}

declare module '@/components/ui/theme-toggle' {
  interface ThemeToggleProps {
    className?: string;
    variant?: 'default' | 'outline' | 'ghost';
    isMini?: boolean;
    _variant?: string;
  }

  const ThemeToggle: React.FC<ThemeToggleProps>;

  export { ThemeToggle };
}

// Para hooks de contexto
declare module '@/hooks/use-theme' {
  interface UseThemeProps {
    theme: string;
    setTheme: (theme: string) => void;
    _theme?: string;
  }

  function useTheme(): UseThemeProps;

  export { useTheme };
}

// Para componentes de wrapper e layout
declare module '@/components/ClientLayoutWrapper' {
  interface ClientLayoutWrapperProps {
    children: React.ReactNode;
    _className?: string;
    _interClassName?: string;
  }

  const ClientLayoutWrapper: React.FC<ClientLayoutWrapperProps>;

  export { ClientLayoutWrapper };
}

// Para contexto de locale
declare module '@/lib/locale' {
  interface LocaleContextType {
    locale: string;
    setLocale: (locale: string) => void;
    _t?: (key: string) => string;
  }

  const useLocale: () => LocaleContextType;

  export { useLocale };
}

// Para o provider context
declare module '@/app/providers' {
  interface ProvidersProps {
    children: React.ReactNode;
    locale?: string;
    _locale?: string;
  }

  const Providers: React.FC<ProvidersProps>;

  export { Providers };
}

// Hook para toast
declare module '@/components/ui/use-toast' {
  interface UseToastReturn {
    toast: ({ ...props }: Toast) => {
      id: string;
      dismiss: () => void;
      update: (props: ToasterToast) => void;
    };
    dismiss: (toastId?: string) => void;
    toasts: ToasterToast[];
    _toast?: any;
  }

  const useToast: () => UseToastReturn;

  export { useToast };
}
