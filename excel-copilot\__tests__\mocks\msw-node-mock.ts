// Mock para MSW Node
import { type setupServer as SetupServerType } from 'msw/node';

// Importar a implementação mock local
import { setupServer, http, HttpResponse } from './msw-mock';

// Re-exportar com o tipo correto
export { setupServer, http, HttpResponse };

// Exportar quaisquer outras funções do msw/node que possam ser utilizadas nos testes
export const rest = {
  get: (url: string, handler: any) => ({ url, method: 'GET', handler }),
  post: (url: string, handler: any) => ({ url, method: 'POST', handler }),
  put: (url: string, handler: any) => ({ url, method: 'PUT', handler }),
  delete: (url: string, handler: any) => ({ url, method: 'DELETE', handler }),
  patch: (url: string, handler: any) => ({ url, method: 'PATCH', handler }),
};

// Implementação simplificada para o módulo msw/node
module.exports = {
  setupServer,
  http,
  HttpResponse,
  rest,
};
