/**
 * @jest-environment jsdom
 */

import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';

import { server, rest } from '../mocks/server';
import { MockRequest, MockContext } from '../mocks/types';
import { createMockWorkbook } from '../mocks/factories';

// Ajuste de tipo para o MockResponse
type JsonResponseFunction = (body: any) => any;

interface CustomMockResponse {
  status: (code: number) => CustomMockResponse;
  json: JsonResponseFunction;
}

// Corrigir a definição do tipo MockResponse para corresponder ao padrão correto
// Adicionar uma interface de patch para o tipo res.status
interface PatchedMockResponse extends CustomMockResponse {
  status: (code: number) => PatchedMockResponse;
}

// Componente de exemplo para demonstrar o uso do mock server
const WorkbookList = () => {
  const [workbooks, setWorkbooks] = React.useState<any[]>([]);
  const [loading, setLoading] = React.useState<boolean>(true);
  const [error, setError] = React.useState<string | null>(null);

  const fetchWorkbooks = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/workbooks');
      if (!response.ok) {
        throw new Error('Falha ao carregar as planilhas');
      }

      const result = await response.json();
      setWorkbooks(result.data || []);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  // Carregar workbooks ao montar o componente
  React.useEffect(() => {
    fetchWorkbooks();
  }, []);

  // Função para recarregar dados
  const handleRefresh = () => {
    fetchWorkbooks();
  };

  if (loading) {
    return <div data-testid="loading">Carregando...</div>;
  }

  if (error) {
    return (
      <div data-testid="error">
        <p>Erro: {error}</p>
        <button onClick={handleRefresh}>Tentar novamente</button>
      </div>
    );
  }

  return (
    <div data-testid="workbook-list">
      <h2>Minhas Planilhas</h2>
      {workbooks.length === 0 ? (
        <p>Nenhuma planilha encontrada</p>
      ) : (
        <ul>
          {workbooks.map(workbook => (
            <li key={workbook.id} data-testid={`workbook-${workbook.id}`}>
              {workbook.name}
            </li>
          ))}
        </ul>
      )}
      <button onClick={handleRefresh} data-testid="refresh-button">
        Atualizar
      </button>
    </div>
  );
};

// Testes para o componente
describe('WorkbookList Component', () => {
  // Configurar mock do fetch global
  const originalFetch = global.fetch;

  beforeAll(() => {
    server.listen();
  });

  afterEach(() => {
    server.resetHandlers();
    // Restaurar o mock do fetch para o estado inicial
    global.fetch = originalFetch;
  });

  afterAll(() => {
    server.close();
  });

  test('deve renderizar lista de workbooks', async () => {
    // Configurar o mock server para retornar dados
    const mockWorkbooks = [
      createMockWorkbook({ id: 'wb-1', name: 'Planilha de Vendas' }),
      createMockWorkbook({ id: 'wb-2', name: 'Orçamento Anual' }),
    ];

    server.use(
      rest.get('/api/workbooks', (req: MockRequest, res: CustomMockResponse, ctx: MockContext) => {
        return res.json({
          success: true,
          data: mockWorkbooks,
        });
      })
    );

    // Mock do fetch para utilizar o nosso server
    global.fetch = jest.fn().mockImplementation((url: string) => {
      if (url === '/api/workbooks') {
        return Promise.resolve({
          ok: true,
          status: 200,
          statusText: 'OK',
          json: () =>
            Promise.resolve({
              success: true,
              data: mockWorkbooks,
            }),
        } as Response);
      }
      return Promise.reject(new Error('Fetch inválido'));
    });

    // Renderizar o componente
    render(<WorkbookList />);

    // Verificar estado de carregamento
    expect(screen.getByTestId('loading')).toBeInTheDocument();

    // Esperar os dados serem carregados
    await waitFor(() => {
      expect(screen.getByTestId('workbook-list')).toBeInTheDocument();
    });

    // Verificar se os items foram renderizados
    expect(screen.getByText('Planilha de Vendas')).toBeInTheDocument();
    expect(screen.getByText('Orçamento Anual')).toBeInTheDocument();
  });

  test('deve mostrar erro quando a API falha', async () => {
    // Configurar o mock server para retornar erro
    server.use(
      rest.get('/api/workbooks', (req: MockRequest, res: CustomMockResponse, ctx: MockContext) => {
        return res.json({
          success: false,
          error: 'Erro do servidor',
        });
      })
    );

    // Mock do fetch para falhar
    global.fetch = jest.fn().mockImplementation(() => {
      return Promise.resolve({
        ok: false,
        status: 500,
        statusText: 'Internal Server Error',
        json: () =>
          Promise.resolve({
            success: false,
            error: 'Erro do servidor',
          }),
      } as Response);
    });

    // Renderizar o componente
    render(<WorkbookList />);

    // Esperar o erro ser mostrado
    await waitFor(() => {
      expect(screen.getByTestId('error')).toBeInTheDocument();
    });

    // Verificar texto de erro
    expect(screen.getByText(/Falha ao carregar as planilhas/i)).toBeInTheDocument();
  });

  test('deve recarregar dados ao clicar no botão atualizar', async () => {
    // Dados iniciais
    const initialWorkbooks = [createMockWorkbook({ id: 'wb-1', name: 'Planilha Inicial' })];

    // Dados após atualização
    const updatedWorkbooks = [
      createMockWorkbook({ id: 'wb-1', name: 'Planilha Inicial' }),
      createMockWorkbook({ id: 'wb-2', name: 'Nova Planilha' }),
    ];

    // Mock do fetch para retornar sequencialmente diferentes resultados
    const fetchMock = jest
      .fn()
      // Primeira chamada - retorna dados iniciais
      .mockImplementationOnce(() =>
        Promise.resolve({
          ok: true,
          status: 200,
          statusText: 'OK',
          json: () =>
            Promise.resolve({
              success: true,
              data: initialWorkbooks,
            }),
        } as Response)
      )
      // Segunda chamada - retorna dados atualizados
      .mockImplementationOnce(() =>
        Promise.resolve({
          ok: true,
          status: 200,
          statusText: 'OK',
          json: () =>
            Promise.resolve({
              success: true,
              data: updatedWorkbooks,
            }),
        } as Response)
      );

    global.fetch = fetchMock;

    // Renderizar componente
    render(<WorkbookList />);

    // Esperar carregamento inicial
    await waitFor(() => {
      expect(screen.getByTestId('workbook-list')).toBeInTheDocument();
    });

    // Verificar dados iniciais
    expect(screen.getByText('Planilha Inicial')).toBeInTheDocument();
    expect(screen.queryByText('Nova Planilha')).not.toBeInTheDocument();

    // Clicar em atualizar
    const user = userEvent.setup();
    await user.click(screen.getByTestId('refresh-button'));

    // Esperar atualização e verificar novos dados
    await waitFor(() => {
      expect(screen.getByText('Nova Planilha')).toBeInTheDocument();
    });

    // Verificar que fetch foi chamado duas vezes
    expect(fetchMock).toHaveBeenCalledTimes(2);
  });
});
