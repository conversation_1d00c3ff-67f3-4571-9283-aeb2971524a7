/**
 * Script de teste para verificar a implementação do Supabase MCP
 * Execute com: node test-supabase-mcp.js
 *
 * ✅ Configurações já presentes no .env:
 * - SUPABASE_URL=https://eliuoignzzxnjkcmmtml.supabase.co
 * - SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
 * - SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
 */

const BASE_URL = 'http://localhost:3000';

async function testSupabaseMCP() {
  console.log('🧪 Testando Supabase MCP Integration...\n');

  try {
    // Teste 1: Status geral
    console.log('1️⃣ Testando /api/supabase/status...');
    const statusResponse = await fetch(`${BASE_URL}/api/supabase/status`);
    const statusData = await statusResponse.json();

    console.log(`   Status: ${statusResponse.status}`);
    console.log(`   Response:`, JSON.stringify(statusData, null, 2));
    console.log('   ✅ Status endpoint funcionando\n');

    // Teste 2: Tabelas
    console.log('2️⃣ Testando /api/supabase/tables...');
    const tablesResponse = await fetch(`${BASE_URL}/api/supabase/tables?details=true&limit=5`);
    const tablesData = await tablesResponse.json();

    console.log(`   Status: ${tablesResponse.status}`);
    console.log(`   Response:`, JSON.stringify(tablesData, null, 2));
    console.log('   ✅ Tables endpoint funcionando\n');

    // Teste 3: Storage
    console.log('3️⃣ Testando /api/supabase/storage...');
    const storageResponse = await fetch(`${BASE_URL}/api/supabase/storage`);
    const storageData = await storageResponse.json();

    console.log(`   Status: ${storageResponse.status}`);
    console.log(`   Response:`, JSON.stringify(storageData, null, 2));
    console.log('   ✅ Storage endpoint funcionando\n');

    // Teste 4: Health check geral
    console.log('4️⃣ Testando /api/health (deve incluir Supabase MCP)...');
    const healthResponse = await fetch(`${BASE_URL}/api/health`);
    const healthData = await healthResponse.json();

    console.log(`   Status: ${healthResponse.status}`);
    console.log(
      `   MCP Integrations:`,
      JSON.stringify(healthData.details?.['mcp-integrations'], null, 2)
    );
    console.log('   ✅ Health check funcionando\n');

    console.log('🎉 Todos os testes passaram! Supabase MCP implementado com sucesso.');
  } catch (error) {
    console.error('❌ Erro durante os testes:', error);

    if (error.code === 'ECONNREFUSED') {
      console.log(
        '\n💡 Dica: Certifique-se de que o servidor está rodando em http://localhost:3000'
      );
      console.log('   Execute: npm run dev');
    }
  }
}

// Executar testes
testSupabaseMCP();
