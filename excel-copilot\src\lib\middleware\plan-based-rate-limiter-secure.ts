/**
 * 🔒 MIDDLEWARE DE RATE LIMITING SEGURO - CORREÇÃO CRÍTICA
 * Implementa limitações específicas por plano de assinatura com validação criptográfica
 *
 * CORREÇÕES IMPLEMENTADAS:
 * - Validação HMAC para integridade do cache
 * - Estrutura de cache corrigida
 * - Logs sanitizados para segurança
 */

import crypto from 'crypto';

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';

import { logger } from '@/lib/logger';
import { PLANS } from '@/lib/stripe';
import { prisma } from '@/server/db/client';

// 🔒 CACHE SEGURO: Estrutura corrigida com validação criptográfica
interface SecureCacheEntry {
  count: number;
  resetTime: number;
  plan?: string | undefined;
  userId?: string | undefined;
  timestamp: number;
  signature: string;
}

const rateLimitCache = new Map<string, SecureCacheEntry>();

// Chave secreta para HMAC (em produção, usar variável de ambiente)
const CACHE_SECRET = process.env.CACHE_SECRET || 'fallback-secret-key-change-in-production';

// Configurações de rate limiting por plano
const RATE_LIMIT_CONFIG = {
  [PLANS.FREE]: {
    requestsPerMinute: 30,
    requestsPerHour: 300,
    requestsPerDay: 1000,
  },
  [PLANS.PRO_MONTHLY]: {
    requestsPerMinute: 120,
    requestsPerHour: 2000,
    requestsPerDay: 10000,
  },
  [PLANS.PRO_ANNUAL]: {
    requestsPerMinute: 240,
    requestsPerHour: 5000,
    requestsPerDay: 25000,
  },
};

/**
 * 🔒 CORREÇÃO CRÍTICA: Gera assinatura HMAC para validação de integridade do cache
 */
function generateCacheSignature(data: {
  userId?: string | undefined;
  plan?: string | undefined;
  count: number;
  timestamp: number;
}): string {
  const payload = `${data.userId || 'anonymous'}:${data.plan || 'free'}:${data.count}:${data.timestamp}`;
  return crypto.createHmac('sha256', CACHE_SECRET).update(payload).digest('hex');
}

/**
 * 🔒 CORREÇÃO CRÍTICA: Valida integridade do cache usando HMAC
 */
function validateCacheEntry(entry: SecureCacheEntry): boolean {
  try {
    const expectedSignature = generateCacheSignature({
      userId: entry.userId,
      plan: entry.plan,
      count: entry.count,
      timestamp: entry.timestamp,
    });

    return crypto.timingSafeEqual(
      Buffer.from(entry.signature, 'hex'),
      Buffer.from(expectedSignature, 'hex')
    );
  } catch (error) {
    logger.warn('[CACHE_VALIDATION_ERROR]', { error });
    return false;
  }
}

/**
 * 🔒 CORREÇÃO CRÍTICA: Define entrada segura no cache com validação
 */
function setSecureCache(
  key: string,
  data: {
    count: number;
    resetTime: number;
    plan?: string | undefined;
    userId?: string | undefined;
  }
): void {
  const timestamp = Date.now();
  const signature = generateCacheSignature({
    userId: data.userId,
    plan: data.plan,
    count: data.count,
    timestamp,
  });

  const secureEntry: SecureCacheEntry = {
    count: data.count,
    resetTime: data.resetTime,
    plan: data.plan,
    userId: data.userId,
    timestamp,
    signature,
  };

  rateLimitCache.set(key, secureEntry);
}

/**
 * 🔒 CORREÇÃO CRÍTICA: Obtém entrada do cache com validação de integridade
 */
function getSecureCache(key: string): SecureCacheEntry | null {
  const entry = rateLimitCache.get(key);

  if (!entry) {
    return null;
  }

  // Validar integridade
  if (!validateCacheEntry(entry)) {
    logger.warn('[CACHE_INTEGRITY_VIOLATION]', {
      key: key.replace(/user_\w+/, 'user_***'), // Sanitizar logs
      timestamp: entry.timestamp,
    });

    // Remover entrada corrompida
    rateLimitCache.delete(key);
    return null;
  }

  return entry;
}

interface RateLimitResult {
  allowed: boolean;
  limit: number;
  remaining: number;
  resetTime: number;
  retryAfter?: number;
}

/**
 * 🔒 CORREÇÃO: Verifica rate limit com cache seguro
 */
export async function checkRateLimit(
  userId: string,
  plan: string,
  timeWindow: 'minute' | 'hour' | 'day' = 'minute'
): Promise<RateLimitResult> {
  const now = Date.now();
  const windowMs = getWindowMs(timeWindow);
  const key = `${userId}:${timeWindow}`;

  // Obter configuração do plano
  const config = RATE_LIMIT_CONFIG[plan] || RATE_LIMIT_CONFIG[PLANS.FREE];
  const limit = getLimit(config, timeWindow);

  // 🔒 CORREÇÃO: Usar cache seguro com validação
  const cached = getSecureCache(key);
  const resetTime = Math.floor(now / windowMs) * windowMs + windowMs;

  if (!cached || cached.resetTime <= now) {
    // Primeira requisição ou janela expirada
    setSecureCache(key, {
      count: 1,
      resetTime,
      userId: userId.slice(0, 8), // Apenas primeiros 8 chars para logs
      plan: plan,
    });

    return {
      allowed: true,
      limit,
      remaining: limit - 1,
      resetTime,
    };
  }

  if (cached.count >= limit) {
    // Limite excedido
    const retryAfter = Math.ceil((cached.resetTime - now) / 1000);

    // 🔒 CORREÇÃO: Logs sanitizados para segurança
    logger.warn(`[RATE_LIMIT_EXCEEDED] Usuário excedeu limite:`, {
      userHash: crypto.createHash('sha256').update(userId).digest('hex').slice(0, 8),
      planTier: plan === PLANS.FREE ? 'free' : 'paid',
      timeWindow,
      limit,
      count: cached.count,
      retryAfter,
    });

    return {
      allowed: false,
      limit,
      remaining: 0,
      resetTime: cached.resetTime,
      retryAfter,
    };
  }

  // 🔒 CORREÇÃO: Incrementar contador com cache seguro
  setSecureCache(key, {
    count: cached.count + 1,
    resetTime: cached.resetTime,
    userId: userId.slice(0, 8),
    plan: plan,
  });

  return {
    allowed: true,
    limit,
    remaining: limit - (cached.count + 1),
    resetTime: cached.resetTime,
  };
}

/**
 * 🔒 CORREÇÃO CRÍTICA: Obtém o plano do usuário com cache seguro
 * Implementa validação criptográfica e logs sanitizados
 */
async function getUserPlanForMiddleware(userId: string): Promise<string> {
  try {
    // Cache seguro para middleware
    const cacheKey = `middleware_plan_${crypto.createHash('sha256').update(userId).digest('hex').slice(0, 16)}`;
    const cached = getSecureCache(cacheKey);
    const now = Date.now();

    // Cache por 5 minutos para middleware
    if (cached && cached.resetTime > now && cached.plan) {
      return cached.plan;
    }

    // Buscar assinatura ativa do usuário
    const subscription = await prisma.subscription.findFirst({
      where: {
        userId,
        OR: [{ status: 'active' }, { status: 'trialing' }],
        AND: [
          {
            OR: [{ currentPeriodEnd: { gt: new Date() } }, { currentPeriodEnd: null }],
          },
        ],
      },
      orderBy: { createdAt: 'desc' },
      select: { plan: true }, // Otimização: buscar apenas o campo necessário
    });

    const plan = subscription?.plan || PLANS.FREE;

    // 🔒 CORREÇÃO: Atualizar cache seguro
    setSecureCache(cacheKey, {
      count: 0, // Não usado para cache de plano
      resetTime: now + 5 * 60 * 1000,
      plan: plan,
      userId: userId.slice(0, 8), // Apenas primeiros 8 chars
    });

    return plan;
  } catch (error) {
    // 🔒 CORREÇÃO: Log sanitizado
    logger.error('[MIDDLEWARE_GET_PLAN_ERROR]', {
      userHash: crypto.createHash('sha256').update(userId).digest('hex').slice(0, 8),
      error: error instanceof Error ? error.message : String(error),
    });

    // Em caso de erro, usar plano FREE como fallback seguro
    return PLANS.FREE;
  }
}

/**
 * Middleware de rate limiting baseado no plano com segurança aprimorada
 */
export async function planBasedRateLimiterSecure(
  request: NextRequest,
  timeWindow: 'minute' | 'hour' | 'day' = 'minute'
): Promise<NextResponse | null> {
  try {
    // Verificar autenticação
    const session = await getServerSession();
    if (!session?.user) {
      // Para usuários não autenticados, aplicar limite mais restritivo
      return await applyAnonymousRateLimit(request, timeWindow);
    }

    const userId = (session.user as any).id;

    // Obter plano real do usuário com cache seguro
    const userPlan = await getUserPlanForMiddleware(userId);

    // 🔒 CORREÇÃO: Logs sanitizados para segurança
    logger.info('[PLAN_RATE_LIMIT]', {
      userHash: crypto.createHash('sha256').update(userId).digest('hex').slice(0, 8),
      planTier: userPlan === PLANS.FREE ? 'free' : 'paid',
      path: request.nextUrl.pathname,
      method: request.method,
    });

    // Verificar rate limit
    const rateLimitResult = await checkRateLimit(userId, userPlan, timeWindow);

    if (!rateLimitResult.allowed) {
      // Limite excedido - retornar erro 429
      const response = NextResponse.json(
        {
          error: 'Rate limit exceeded',
          message: `Limite de requisições excedido. Tente novamente em ${rateLimitResult.retryAfter} segundos.`,
          limit: rateLimitResult.limit,
          retryAfter: rateLimitResult.retryAfter,
        },
        { status: 429 }
      );

      // Adicionar headers de rate limiting
      response.headers.set('X-RateLimit-Limit', rateLimitResult.limit.toString());
      response.headers.set('X-RateLimit-Remaining', '0');
      response.headers.set('X-RateLimit-Reset', rateLimitResult.resetTime.toString());
      response.headers.set('Retry-After', rateLimitResult.retryAfter?.toString() || '60');

      return response;
    }

    // Rate limit OK - adicionar headers informativos
    const response = NextResponse.next();
    response.headers.set('X-RateLimit-Limit', rateLimitResult.limit.toString());
    response.headers.set('X-RateLimit-Remaining', rateLimitResult.remaining.toString());
    response.headers.set('X-RateLimit-Reset', rateLimitResult.resetTime.toString());

    return null; // Continuar processamento
  } catch (error) {
    logger.error('[RATE_LIMITER_ERROR]', error);

    // Em caso de erro, permitir requisição mas logar
    logger.warn('[RATE_LIMITER_FALLBACK] Permitindo requisição devido a erro no rate limiter');
    return null;
  }
}

// Funções utilitárias (mantidas iguais)
async function applyAnonymousRateLimit(
  request: NextRequest,
  timeWindow: 'minute' | 'hour' | 'day'
): Promise<NextResponse | null> {
  const ip = request.ip || request.headers.get('x-forwarded-for') || 'unknown';
  const key = `anonymous:${ip}:${timeWindow}`;

  const anonymousLimits = { minute: 10, hour: 100, day: 500 };
  const limit = anonymousLimits[timeWindow];
  const now = Date.now();
  const windowMs = getWindowMs(timeWindow);
  const resetTime = Math.floor(now / windowMs) * windowMs + windowMs;

  const cached = getSecureCache(key);

  if (!cached || cached.resetTime <= now) {
    setSecureCache(key, { count: 1, resetTime });
    return null;
  }

  if (cached.count >= limit) {
    const retryAfter = Math.ceil((cached.resetTime - now) / 1000);
    return NextResponse.json(
      {
        error: 'Rate limit exceeded',
        message: 'Muitas requisições. Faça login para limites maiores.',
        retryAfter,
      },
      { status: 429 }
    );
  }

  setSecureCache(key, { count: cached.count + 1, resetTime: cached.resetTime });
  return null;
}

function getWindowMs(timeWindow: 'minute' | 'hour' | 'day'): number {
  switch (timeWindow) {
    case 'minute':
      return 60 * 1000;
    case 'hour':
      return 60 * 60 * 1000;
    case 'day':
      return 24 * 60 * 60 * 1000;
    default:
      return 60 * 1000;
  }
}

function getLimit(config: any, timeWindow: 'minute' | 'hour' | 'day'): number {
  switch (timeWindow) {
    case 'minute':
      return config.requestsPerMinute;
    case 'hour':
      return config.requestsPerHour;
    case 'day':
      return config.requestsPerDay;
    default:
      return config.requestsPerMinute;
  }
}
