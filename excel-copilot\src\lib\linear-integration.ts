/**
 * Linear MCP Integration - Excel Copilot
 *
 * Cliente e serviços para integração com Linear API
 * Permite monitoramento de issues, projetos e workflow via MCP
 */

import { ENV } from '@/config/unified-environment';

import { logger } from './logger';

// Tipos Linear
export interface LinearIssue {
  id: string;
  identifier: string;
  title: string;
  description?: string;
  state: {
    id: string;
    name: string;
    type: string;
  };
  team: {
    id: string;
    name: string;
    key: string;
  };
  assignee?: {
    id: string;
    name: string;
    email: string;
  };
  labels: {
    nodes: Array<{
      id: string;
      name: string;
      color: string;
    }>;
  };
  createdAt: string;
  updatedAt: string;
  priority?: number;
  estimate?: number;
}

export interface LinearTeam {
  id: string;
  name: string;
  key: string;
  description?: string;
  states: {
    nodes: Array<{
      id: string;
      name: string;
      type: string;
      color: string;
    }>;
  };
}

export interface LinearProject {
  id: string;
  name: string;
  description?: string;
  state: string;
  progress: number;
  targetDate?: string;
  createdAt: string;
  updatedAt: string;
}

export interface LinearWorkflowState {
  id: string;
  name: string;
  type: 'backlog' | 'unstarted' | 'started' | 'completed' | 'canceled';
  color: string;
  team: {
    id: string;
    name: string;
  };
}

export interface LinearHealthStatus {
  configured: boolean;
  apiKeyValid: boolean;
  workspaceAccessible: boolean;
  lastSync?: string;
  issueCount: number;
  teamCount: number;
  recentActivity: number;
}

/**
 * Cliente base para Linear API
 */
export class LinearClient {
  private apiKey: string;
  private baseUrl = 'https://api.linear.app/graphql';

  constructor(apiKey?: string) {
    this.apiKey = apiKey || ENV.LINEAR_API_KEY || '';

    if (!this.apiKey) {
      logger.warn('Linear API key não configurada');
    }
  }

  /**
   * Executa query GraphQL na Linear API ou usa MCP integration
   */
  private async graphqlQuery<T>(query: string, variables?: Record<string, unknown>): Promise<T> {
    // Se não temos API key válida ou estamos usando MCP, usa a integração MCP diretamente
    if (!this.apiKey || this.apiKey === 'mcp_integration_enabled') {
      return this.useMCPIntegration<T>(query, variables);
    }

    const response = await fetch(this.baseUrl, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query,
        variables,
      }),
    });

    if (!response.ok) {
      throw new Error(`Linear API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();

    if (data.errors) {
      throw new Error(
        `Linear GraphQL error: ${data.errors.map((e: { message: string }) => e.message).join(', ')}`
      );
    }

    return data.data;
  }

  /**
   * Usa a integração MCP Linear diretamente
   */
  private async useMCPIntegration<T>(
    query: string,
    variables?: Record<string, unknown>
  ): Promise<T> {
    try {
      logger.info('Executando query via integração MCP Linear');

      // Importa a função linear dinamicamente para evitar problemas de dependência circular
      const { linear } = await import('@/lib/mcp-tools');

      // Converte a query GraphQL para uma consulta em linguagem natural
      const naturalQuery = this.convertGraphQLToNaturalLanguage(query, variables);

      const result = await linear({
        summary: 'Execute Linear query via MCP integration',
        query: naturalQuery,
        is_read_only: !query.includes('mutation'),
      });

      return result as T;
    } catch (error) {
      logger.error('Erro ao usar integração MCP Linear:', error);
      throw new Error('Falha na integração MCP Linear');
    }
  }

  /**
   * Converte query GraphQL para linguagem natural para MCP
   */
  private convertGraphQLToNaturalLanguage(
    query: string,
    variables?: Record<string, unknown>
  ): string {
    // Detecta mutations primeiro
    if (
      query.includes('mutation') ||
      query.includes('CreateIssue') ||
      query.includes('UpdateIssue')
    ) {
      if (query.includes('CreateIssue')) {
        return `Create issue with data: ${JSON.stringify(variables)}`;
      }
      if (query.includes('UpdateIssue')) {
        return `Update issue with data: ${JSON.stringify(variables)}`;
      }
      return `Execute mutation with data: ${JSON.stringify(variables)}`;
    }

    // Detecta queries
    if (query.includes('organization')) {
      return 'Get workspace organization information and teams';
    }
    if (query.includes('issues')) {
      // Passa a query completa para que a MCP possa aplicar os filtros
      return query;
    }
    if (query.includes('teams')) {
      return 'Get all teams with their workflow states';
    }
    if (query.includes('projects')) {
      return 'Get all projects with their details';
    }
    if (query.includes('workflowStates')) {
      return 'Get all workflow states for teams';
    }

    return 'Execute Linear query';
  }

  /**
   * Obtém informações do workspace
   */
  async getWorkspaceInfo() {
    const query = `
      query {
        organization {
          id
          name
          urlKey
          createdAt
        }
        teams {
          nodes {
            id
            name
            key
            description
            states {
              nodes {
                id
                name
                type
                color
              }
            }
          }
        }
      }
    `;

    return this.graphqlQuery(query);
  }

  /**
   * Lista issues com filtros opcionais
   */
  async getIssues(
    options: {
      teamId?: string;
      assigneeId?: string;
      state?: string;
      limit?: number;
      includeArchived?: boolean;
    } = {}
  ): Promise<{ issues: LinearIssue[] }> {
    const { teamId, assigneeId, state, limit = 50, includeArchived = false } = options;

    let filter = '';
    const conditions = [];

    if (teamId) conditions.push(`team: { id: { eq: "${teamId}" } }`);
    if (assigneeId) conditions.push(`assignee: { id: { eq: "${assigneeId}" } }`);
    if (state) conditions.push(`state: { name: { eq: "${state}" } }`);
    if (!includeArchived) conditions.push(`state: { type: { neq: "canceled" } }`);

    if (conditions.length > 0) {
      filter = `filter: { ${conditions.join(', ')} }`;
    }

    const query = `
      query GetIssues {
        issues(first: ${limit}, ${filter}) {
          nodes {
            id
            identifier
            title
            description
            state {
              id
              name
              type
            }
            team {
              id
              name
              key
            }
            assignee {
              id
              name
              email
            }
            labels {
              nodes {
                id
                name
                color
              }
            }
            createdAt
            updatedAt
            priority
            estimate
          }
        }
      }
    `;

    const result = await this.graphqlQuery<{ issues: { nodes: LinearIssue[] } }>(query);
    return { issues: result.issues.nodes };
  }

  /**
   * Obtém detalhes de uma issue específica
   */
  async getIssue(issueId: string): Promise<LinearIssue> {
    const query = `
      query GetIssue($id: String!) {
        issue(id: $id) {
          id
          identifier
          title
          description
          state {
            id
            name
            type
          }
          team {
            id
            name
            key
          }
          assignee {
            id
            name
            email
          }
          labels {
            nodes {
              id
              name
              color
            }
          }
          createdAt
          updatedAt
          priority
          estimate
        }
      }
    `;

    const result = await this.graphqlQuery<{ issue: LinearIssue }>(query, { id: issueId });
    return result.issue;
  }

  /**
   * Lista teams disponíveis
   */
  async getTeams(): Promise<{ teams: LinearTeam[] }> {
    const query = `
      query GetTeams {
        teams {
          nodes {
            id
            name
            key
            description
            states {
              nodes {
                id
                name
                type
                color
              }
            }
          }
        }
      }
    `;

    const result = await this.graphqlQuery<{ teams: { nodes: LinearTeam[] } }>(query);
    return { teams: result.teams.nodes };
  }

  /**
   * Cria nova issue
   */
  async createIssue(data: {
    teamId: string;
    title: string;
    description?: string;
    assigneeId?: string;
    stateId?: string;
    priority?: number;
    labelIds?: string[];
  }): Promise<{ issue: LinearIssue }> {
    const query = `
      mutation CreateIssue($input: IssueCreateInput!) {
        issueCreate(input: $input) {
          success
          issue {
            id
            identifier
            title
            description
            state {
              id
              name
              type
            }
            team {
              id
              name
              key
            }
            assignee {
              id
              name
              email
            }
            createdAt
            updatedAt
          }
        }
      }
    `;

    const input = {
      teamId: data.teamId,
      title: data.title,
      description: data.description,
      assigneeId: data.assigneeId,
      stateId: data.stateId,
      priority: data.priority,
      labelIds: data.labelIds,
    };

    const result = await this.graphqlQuery<{
      issueCreate: { success: boolean; issue: LinearIssue };
    }>(query, { input });

    if (!result.issueCreate.success) {
      throw new Error('Falha ao criar issue no Linear');
    }

    return { issue: result.issueCreate.issue };
  }

  /**
   * Atualiza issue existente
   */
  async updateIssue(
    issueId: string,
    data: {
      title?: string;
      description?: string;
      stateId?: string;
      assigneeId?: string;
      priority?: number;
    }
  ): Promise<{ issue: LinearIssue }> {
    const query = `
      mutation UpdateIssue($id: String!, $input: IssueUpdateInput!) {
        issueUpdate(id: $id, input: $input) {
          success
          issue {
            id
            identifier
            title
            description
            state {
              id
              name
              type
            }
            team {
              id
              name
              key
            }
            assignee {
              id
              name
              email
            }
            updatedAt
          }
        }
      }
    `;

    const result = await this.graphqlQuery<{
      issueUpdate: { success: boolean; issue: LinearIssue };
    }>(query, { id: issueId, input: data });

    if (!result.issueUpdate.success) {
      throw new Error('Falha ao atualizar issue no Linear');
    }

    return { issue: result.issueUpdate.issue };
  }

  /**
   * Lista projetos
   */
  async getProjects(): Promise<{ projects: LinearProject[] }> {
    const query = `
      query GetProjects {
        projects {
          nodes {
            id
            name
            description
            state
            progress
            targetDate
            createdAt
            updatedAt
          }
        }
      }
    `;

    const result = await this.graphqlQuery<{ projects: { nodes: LinearProject[] } }>(query);
    return { projects: result.projects.nodes };
  }

  /**
   * Obtém workflow states
   */
  async getWorkflowStates(): Promise<{ states: LinearWorkflowState[] }> {
    const query = `
      query GetWorkflowStates {
        workflowStates {
          nodes {
            id
            name
            type
            color
            team {
              id
              name
            }
          }
        }
      }
    `;

    const result = await this.graphqlQuery<{ workflowStates: { nodes: LinearWorkflowState[] } }>(
      query
    );
    return { states: result.workflowStates.nodes };
  }

  /**
   * Verifica saúde da conexão Linear
   */
  async checkHealth(): Promise<LinearHealthStatus> {
    try {
      if (!this.apiKey && this.apiKey !== 'mcp_integration_enabled') {
        return {
          configured: false,
          apiKeyValid: false,
          workspaceAccessible: false,
          issueCount: 0,
          teamCount: 0,
          recentActivity: 0,
        };
      }

      // Tenta acessar informações básicas do workspace
      const [_workspaceInfo, issues, teams] = await Promise.all([
        this.getWorkspaceInfo(),
        this.getIssues({ limit: 10 }),
        this.getTeams(),
      ]);

      // Calcula atividade recente (issues atualizadas nas últimas 24h)
      const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
      const recentActivity = issues.issues.filter(
        issue => new Date(issue.updatedAt) > oneDayAgo
      ).length;

      return {
        configured: true,
        apiKeyValid: true,
        workspaceAccessible: true,
        lastSync: new Date().toISOString(),
        issueCount: issues.issues.length,
        teamCount: teams.teams.length,
        recentActivity,
      };
    } catch (error) {
      logger.error('Linear health check failed:', error);

      return {
        configured: !!this.apiKey,
        apiKeyValid: false,
        workspaceAccessible: false,
        issueCount: 0,
        teamCount: 0,
        recentActivity: 0,
      };
    }
  }
}

/**
 * Serviço de monitoramento Linear de alto nível
 */
export class LinearMonitoringService {
  private client: LinearClient;

  constructor(apiKey?: string) {
    this.client = new LinearClient(apiKey);
  }

  /**
   * Obtém resumo do workspace
   */
  async getWorkspaceSummary() {
    try {
      const [workspaceInfo, issues, teams, projects] = await Promise.all([
        this.client.getWorkspaceInfo(),
        this.client.getIssues({ limit: 100 }),
        this.client.getTeams(),
        this.client.getProjects(),
      ]);

      // Estatísticas por estado
      const issuesByState = issues.issues.reduce(
        (acc, issue) => {
          const state = issue.state.name;
          acc[state] = (acc[state] || 0) + 1;
          return acc;
        },
        {} as Record<string, number>
      );

      // Issues por team
      const issuesByTeam = issues.issues.reduce(
        (acc, issue) => {
          const team = issue.team.name;
          acc[team] = (acc[team] || 0) + 1;
          return acc;
        },
        {} as Record<string, number>
      );

      // Issues por assignee
      const issuesByAssignee = issues.issues.reduce(
        (acc, issue) => {
          const assignee = issue.assignee?.name || 'Unassigned';
          acc[assignee] = (acc[assignee] || 0) + 1;
          return acc;
        },
        {} as Record<string, number>
      );

      return {
        workspace: (workspaceInfo as { organization?: string })?.organization || 'Unknown',
        summary: {
          totalIssues: issues.issues.length,
          totalTeams: teams.teams.length,
          totalProjects: projects.projects.length,
          issuesByState,
          issuesByTeam,
          issuesByAssignee,
        },
        recentIssues: issues.issues
          .sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())
          .slice(0, 10),
      };
    } catch (error) {
      logger.error('Erro ao obter resumo do workspace Linear:', error);
      throw error;
    }
  }

  /**
   * Obtém issues relacionadas ao Excel Copilot
   */
  async getExcelCopilotIssues() {
    try {
      const issues = await this.client.getIssues({ limit: 100 });

      // Filtra issues relacionadas ao Excel Copilot
      const excelCopilotIssues = issues.issues.filter(
        issue =>
          issue.title.toLowerCase().includes('excel') ||
          issue.title.toLowerCase().includes('copilot') ||
          issue.title.toLowerCase().includes('mcp') ||
          issue.description?.toLowerCase().includes('excel') ||
          issue.description?.toLowerCase().includes('copilot')
      );

      return {
        issues: excelCopilotIssues,
        summary: {
          total: excelCopilotIssues.length,
          byState: excelCopilotIssues.reduce(
            (acc, issue) => {
              const state = issue.state.name;
              acc[state] = (acc[state] || 0) + 1;
              return acc;
            },
            {} as Record<string, number>
          ),
        },
      };
    } catch (error) {
      logger.error('Erro ao obter issues do Excel Copilot:', error);
      throw error;
    }
  }

  /**
   * Cria issue para bug ou feature do Excel Copilot
   */
  async createExcelCopilotIssue(data: {
    type: 'bug' | 'feature' | 'improvement';
    title: string;
    description: string;
    priority?: number;
    assigneeId?: string;
  }) {
    try {
      const teams = await this.client.getTeams();
      const defaultTeam = teams.teams[0]; // Usa o primeiro team disponível

      if (!defaultTeam) {
        throw new Error('Nenhum team encontrado no workspace Linear');
      }

      const issueData = {
        teamId: defaultTeam.id,
        title: `[Excel Copilot] ${data.title}`,
        description: `**Tipo:** ${data.type}\n\n${data.description}\n\n---\n*Criado automaticamente via MCP Integration*`,
        priority: data.priority || 3,
        ...(data.assigneeId && { assigneeId: data.assigneeId }),
      };

      return await this.client.createIssue(issueData);
    } catch (error) {
      logger.error('Erro ao criar issue do Excel Copilot:', error);
      throw error;
    }
  }

  /**
   * Obtém métricas de desenvolvimento
   */
  async getDevelopmentMetrics() {
    try {
      const issues = await this.client.getIssues({ limit: 200 });
      const now = new Date();
      const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      const oneMonthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

      const recentIssues = issues.issues.filter(issue => new Date(issue.createdAt) > oneWeekAgo);

      const completedThisMonth = issues.issues.filter(
        issue => issue.state.type === 'completed' && new Date(issue.updatedAt) > oneMonthAgo
      );

      const inProgress = issues.issues.filter(issue => issue.state.type === 'started');

      return {
        totalIssues: issues.issues.length,
        recentIssues: recentIssues.length,
        completedThisMonth: completedThisMonth.length,
        inProgress: inProgress.length,
        averageCompletionTime: this.calculateAverageCompletionTime(completedThisMonth),
        velocity: completedThisMonth.length, // Issues completadas no mês
      };
    } catch (error) {
      logger.error('Erro ao obter métricas de desenvolvimento:', error);
      throw error;
    }
  }

  /**
   * Calcula tempo médio de conclusão
   */
  private calculateAverageCompletionTime(completedIssues: LinearIssue[]): number {
    if (completedIssues.length === 0) return 0;

    const totalTime = completedIssues.reduce((acc, issue) => {
      const created = new Date(issue.createdAt).getTime();
      const updated = new Date(issue.updatedAt).getTime();
      return acc + (updated - created);
    }, 0);

    // Retorna em dias
    return Math.round(totalTime / completedIssues.length / (24 * 60 * 60 * 1000));
  }
}
