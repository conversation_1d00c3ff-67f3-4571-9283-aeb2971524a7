# Excel Copilot

Um micro-SaaS que transforma a forma como usuários interagem com planilhas Excel, permitindo comandos em linguagem natural através de uma interface conversacional alimentada por IA avançada.

## 🚀 Funcionalidades Principais

- ✅ Compreensão de linguagem natural (via IA Google Gemini)
- ✅ Execução automática de fórmulas, filtros, gráficos, tabelas dinâmicas
- ✅ Criação e edição de planilhas com comandos por chat
- ✅ Exportação/Download direto em .xlsx
- ✅ Compatível com arquivos locais
- ✅ Autenticação social (Google, GitHub) e acesso como convidado
- ✅ Dashboard de gerenciamento de planilhas
- ✅ Histórico de interações com IA
- ✅ Suporte a múltiplos idiomas com i18n e pluralização contextual
- ✅ Modo offline com sincronização bidirecional e resolução de conflitos
- ✅ Colaboração em tempo real com CRDTs
- ✅ Edição assistida por IA com sugestões contextuais

## 🛠️ Stack Tecnológica Avançada (2025)

- **Frontend**: Next.js 14+ (React) com TypeScript + Tailwind CSS + Qwik Islands
- **Backend**: Next.js API Routes + tRPC + Edge Functions + Resumable Server Components
- **IA**: Google Gemini API (2.0+) via Vertex AI + RAG Frameworks para context enhancement
- **Excel**: ExcelJS v2+ para manipulação de planilhas com Web Workers dedicados
- **Banco de Dados**: PostgreSQL (Supabase Vector) + Prisma ORM + Edge SQL para queries locais
- **Autenticação**: NextAuth.js v5 com fluxos OAuth 2.1 + PKCE + WebAuthn/FIDO2
- **Estilização**: Tailwind CSS + CSS Houdini + View Transitions API
- **Formulários**: React Hook Form + Zod + Schema Adapters + Field-level validação progressiva
- **Estado**: Zustand com imutabilidade via Immer + React Query + Jotai para estado atômico
- **Compilação**: Bun para bundling acelerado + SWC para transpilação ultra-rápida
- **Comunicação**: tRPC + WebSockets + SSE para updates em tempo real + HTTP/3 (QUIC)

## 📋 Arquitetura do Projeto (Clean Architecture)

O projeto adota uma arquitetura limpa com clara separação de responsabilidades:

```
excel-copilot/
├── src/
│   ├── domain/             # Entidades de negócio e regras centrais
│   │   ├── entities/       # Objetos de domínio independentes de frameworks
│   │   ├── value-objects/  # Objetos imutáveis com equivalência por valor
│   │   └── services/       # Serviços de domínio puros
│   ├── application/        # Casos de uso e lógica de aplicação
│   │   ├── use-cases/      # Casos de uso implementando regras de negócio
│   │   ├── ports/          # Interfaces de saída (abstrações de serviços externos)
│   │   └── dto/            # Objetos de transferência de dados
│   ├── adapters/           # Adaptadores para infraestrutura
│   │   ├── primary/        # Adaptadores de entrada (controladores)
│   │   └── secondary/      # Adaptadores de saída (implementações concretas)
│   └── infrastructure/     # Frameworks e ferramentas
│       ├── db/             # Implementações de persistência
│       ├── ai/             # Serviços de IA
│       ├── ui/             # Componentes de interface
│       └── excel/          # Manipulação de Excel
├── public/                 # Assets estáticos
└── ...
```

## 🧰 Princípios de Desenvolvimento

### Clean Code e SOLID

O projeto adota rigorosamente princípios SOLID e Clean Code:

- **Single Responsibility**: Cada classe/módulo tem uma única razão para mudar (<60 linhas)
- **Open/Closed**: Extensão por composição em vez de modificação
- **Liskov Substitution**: Subtipos são substituíveis por seus tipos base
- **Interface Segregation**: Interfaces pequenas e focadas (máx. 5 métodos)
- **Dependency Inversion**: Dependências são injetadas via DI container tipado

### DDD (Domain-Driven Design)

- **Bounded Contexts**: Separação clara entre contextos de domínio
- **Ubiquitous Language**: Glossário compartilhado entre código e documentação
- **Value Objects**: Imutáveis, substituíveis e com validação na construção
- **Aggregates**: Clusters de entidades com consistência transacional
- **Domain Events**: Comunicação entre bounded contexts via eventos

### Arquitetura de Frontend Avançada

#### Micro-Frontends com Module Federation

A aplicação é dividida em micro-frontends independentes:

- **@excel-copilot/shell**: Container principal e orchestração
- **@excel-copilot/auth**: Módulo de autenticação
- **@excel-copilot/editor**: Editor de planilhas
- **@excel-copilot/analytics**: Dashboard e visualizações
- **@excel-copilot/ai-chat**: Interface conversacional

#### Partial Hydration e Renderização Adaptativa

- **Island Architecture**: Componentes interativos hidratados independentemente
- **Progressive Enhancement**: Funcionalidade básica garantida mesmo sem JS
- **Resumable Frameworks**: Preservação de estado entre server/client
- **Edge Rendering**: SSR proximal ao usuário via edge functions

#### Estado Global Distribuído

```typescript
// Arquitetura de estado avançada com atomicidade e persistência
import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { createSelectors } from '@/utils/storeUtils';
import { immer } from 'zustand/middleware/immer';
import { devtools } from 'zustand/middleware/devtools';

// Slice para UI com atomicidade e performance
const createUISlice = (set, get) => ({
  theme: 'system',
  sidebarOpen: false,
  modals: new Map(),
  setTheme: theme =>
    set(state => {
      state.ui.theme = theme;
    }),
  // ...outros métodos
});

// Slice para workbook com transações
const createWorkbookSlice = (set, get) => ({
  current: null,
  selection: null,
  history: [],
  transaction: null,
  startTransaction: name => {
    if (get().workbook.transaction) throw new Error('Transaction already in progress');
    set(state => {
      state.workbook.transaction = { name, operations: [] };
    });
  },
  commitTransaction: async () => {
    const tx = get().workbook.transaction;
    if (!tx) throw new Error('No transaction in progress');

    try {
      // Execute operations as a batch
      await batchOperations(tx.operations);

      // Add to history
      set(state => {
        state.workbook.history.push({
          name: tx.name,
          timestamp: Date.now(),
          operations: tx.operations,
        });
        state.workbook.transaction = null;
      });
    } catch (error) {
      // Rollback
      set(state => {
        state.workbook.transaction = null;
      });
      throw error;
    }
  },
  // ...outros métodos avançados
});

// Store factory com composição de slices
const createStore = () => {
  return create(
    devtools(
      persist(
        immer((set, get) => ({
          ui: createUISlice(set, get),
          workbook: createWorkbookSlice(set, get),
          ai: createAISlice(set, get),
          // ...outros slices
        })),
        { name: 'excel-copilot-store', partialize: state => ({ ui: { theme: state.ui.theme } }) }
      )
    )
  );
};

// Criação de store com selectors tipados
const useStoreBase = createStore();
const useStore = createSelectors(useStoreBase);

export default useStore;
```

### Arquitetura de Backend Avançada

#### API Gateway com tRPC

```typescript
// tRPC Router com validação, autorização e transações aninhadas
import { initTRPC, TRPCError } from '@trpc/server';
import { z } from 'zod';
import superjson from 'superjson';
import { Context } from './context';

// Inicialização com superjson para tipos complexos
const t = initTRPC.context<Context>().create({
  transformer: superjson,
  errorFormatter: ({ shape, error }) => ({
    ...shape,
    data: {
      ...shape.data,
      stackTrace: process.env.NODE_ENV === 'production' ? null : error.stack,
    },
  }),
});

// Middleware avançado com rate limiting, caching e telemetria
const enforceAuth = t.middleware(async ({ ctx, next }) => {
  if (!ctx.session?.user) {
    throw new TRPCError({ code: 'UNAUTHORIZED' });
  }

  // Rate limiting
  const rateLimitResult = await rateLimiter.check(ctx.session.user.id);
  if (!rateLimitResult.success) {
    throw new TRPCError({
      code: 'TOO_MANY_REQUESTS',
      message: `Rate limit exceeded. Try again in ${rateLimitResult.timeRemaining}ms`,
    });
  }

  // Telemetria
  const span = tracer.startSpan('tRPC.request');

  return next({
    ctx: {
      session: ctx.session,
      user: ctx.session.user,
      span,
    },
  }).finally(() => span.end());
});

// Proteção contra operações pesadas
const protectExpensive = t.middleware(async ({ ctx, next, path, rawInput }) => {
  const opMetrics = operationRegistry.get(path);

  if (opMetrics?.complexity > 50) {
    await resourceGuard.checkQuota(ctx.user.id, opMetrics.complexity);
  }

  const result = await next({ ctx });

  // Atualizar métricas
  await operationRegistry.recordExecution(path, {
    userId: ctx.user.id,
    executionTime: result.meta?.executionTime,
    inputSize: JSON.stringify(rawInput).length,
    outputSize: JSON.stringify(result.data).length,
  });

  return result;
});

// Procedimentos com middleware avançado
const publicProcedure = t.procedure;
const protectedProcedure = t.procedure.use(enforceAuth);
const expensiveProcedure = protectedProcedure.use(protectExpensive);

// Router com funcionalidades avançadas
export const appRouter = t.router({
  workbook: t.router({
    getById: protectedProcedure
      .input(z.object({ id: z.string().cuid() }))
      .query(async ({ ctx, input }) => {
        // Cache primeiro
        const cached = await ctx.cache.get(`workbook:${input.id}`);
        if (cached) return cached;

        // Verificação de permissão
        const canAccess = await ctx.permissions.check({
          userId: ctx.user.id,
          resource: 'workbook',
          resourceId: input.id,
          action: 'read',
        });

        if (!canAccess) {
          throw new TRPCError({ code: 'FORBIDDEN' });
        }

        // Obter dados com seleção eficiente de campos
        const workbook = await ctx.prisma.workbook.findUnique({
          where: { id: input.id },
          select: {
            id: true,
            name: true,
            // ... campos relevantes

            // Relações com limitação
            sheets: {
              take: 10,
              orderBy: { lastAccessed: 'desc' },
            },
          },
        });

        if (!workbook) {
          throw new TRPCError({ code: 'NOT_FOUND' });
        }

        // Armazenar em cache
        await ctx.cache.set(`workbook:${input.id}`, workbook, { ttl: 60 });

        // Registro de acesso assíncrono
        ctx.eventBus.publish('workbook.accessed', {
          workbookId: workbook.id,
          userId: ctx.user.id,
          timestamp: new Date(),
        });

        return workbook;
      }),

    // Operações adicionais...
  }),

  // Outros roteadores...
});

export type AppRouter = typeof appRouter;
```

#### Event-Driven Architecture com CQRS

A aplicação separa operações de leitura e escrita:

- **Commands**: Operações de escrita validadas e processadas
- **Queries**: Otimizadas para leitura com projeções específicas
- **Events**: Comunicação assíncrona entre bounded contexts
- **Domain Events**: Alterações no domínio geram eventos

```typescript
// Exemplo de Command Handler
export class CreateWorkbookCommandHandler implements ICommandHandler {
  constructor(
    private readonly workbookRepository: IWorkbookRepository,
    private readonly userRepository: IUserRepository,
    private readonly eventBus: IEventBus,
    private readonly logger: ILogger
  ) {}

  async execute(command: CreateWorkbookCommand): Promise<Result<string>> {
    this.logger.info('Creating workbook', { userId: command.userId, name: command.name });

    // Validação de domínio
    const user = await this.userRepository.findById(command.userId);
    if (!user) {
      return Result.fail('User not found');
    }

    // Verificação de quotas
    const workbooksCount = await this.workbookRepository.countByUserId(command.userId);
    if (workbooksCount >= user.plan.maxWorkbooks) {
      return Result.fail('Workbook quota exceeded');
    }

    // Criação de entidade de domínio
    const workbookId = nanoid();
    const workbook = WorkbookEntity.create({
      id: workbookId,
      name: command.name,
      userId: command.userId,
      createdAt: new Date(),
    });

    // Persistência
    await this.workbookRepository.save(workbook);

    // Publicação de evento de domínio
    await this.eventBus.publish(
      new WorkbookCreatedEvent({
        workbookId,
        userId: command.userId,
        name: command.name,
      })
    );

    return Result.ok(workbookId);
  }
}
```

### Integração com IA Avançada

#### Retrieval-Augmented Generation (RAG)

O sistema implementa RAG para enriquecer as respostas da IA:

```typescript
// Exemplo simplificado de sistema RAG para Excel
export class ExcelRAGService {
  constructor(
    private readonly vectorDb: VectorDatabase,
    private readonly llmService: LLMService,
    private readonly workbookRepository: WorkbookRepository
  ) {}

  async processCommand(input: {
    userId: string;
    workbookId: string;
    command: string;
    sheetName?: string;
    selection?: string;
  }): Promise<CommandResult> {
    // 1. Recuperar estado atual do workbook
    const workbook = await this.workbookRepository.findById(input.workbookId);
    if (!workbook) {
      throw new Error('Workbook not found');
    }

    // 2. Extrair informações contextuais
    const context = this.buildContext(workbook, input.sheetName, input.selection);

    // 3. Realizar embeddings de comando e contexto
    const [commandEmbedding, contextEmbedding] = await Promise.all([
      this.vectorDb.embed(input.command),
      this.vectorDb.embed(JSON.stringify(context)),
    ]);

    // 4. Recuperar exemplos similares de comandos anteriores
    const similarCommands = await this.vectorDb.search({
      embedding: commandEmbedding,
      collection: 'excel_commands',
      limit: 5,
      filters: { workbookType: workbook.type },
    });

    // 5. Recuperar documentação relevante
    const relevantDocs = await this.vectorDb.search({
      embedding: contextEmbedding,
      collection: 'excel_documentation',
      limit: 3,
    });

    // 6. Gerar prompt enriquecido
    const enrichedPrompt = this.buildPrompt({
      command: input.command,
      context,
      similarCommands: similarCommands.results,
      relevantDocs: relevantDocs.results,
    });

    // 7. Chamar LLM com streaming
    const stream = await this.llmService.generateStream(enrichedPrompt);

    // 8. Parsear resultado e validar
    const result = await this.parseAndValidateResponse(stream);

    // 9. Armazenar comando e resultado para aprendizado contínuo
    await this.vectorDb.store({
      collection: 'excel_commands',
      embedding: commandEmbedding,
      metadata: {
        command: input.command,
        result: result.summary,
        workbookType: workbook.type,
        success: result.success,
        timestamp: new Date(),
      },
    });

    return result;
  }

  // Métodos auxiliares
  private buildContext(workbook, sheetName, selection) {
    /* ... */
  }
  private buildPrompt(params) {
    /* ... */
  }
  private async parseAndValidateResponse(stream) {
    /* ... */
  }
}
```

### Segurança Avançada Zero-Trust

O sistema implementa um modelo Zero-Trust completo:

- **Autenticação Multi-fator**: Obrigatória para operações sensíveis
- **JWT com PASETO**: Tokens à prova de manipulação com encriptação
- **Criptografia Fim-a-Fim**: Para dados sensíveis em trânsito e repouso
- **Análise de Comportamento**: Detecção de padrões anômalos
- **Gestão de Segredos**: Rotação automática de credenciais

```typescript
// Exemplo de middleware de segurança avançada
export const securityMiddleware = async (
  req: NextApiRequest,
  res: NextApiResponse,
  next: () => Promise<void>
) => {
  // Headers de segurança obrigatórios
  res.setHeader('Content-Security-Policy', CSP_POLICY);
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  res.setHeader('Permissions-Policy', PERMISSIONS_POLICY);

  // Rate limiting baseado em IP, usuário e recurso
  const rateLimitResult = await rateLimiter.check({
    ip: req.socket.remoteAddress,
    userId: req.session?.user?.id,
    resource: req.url,
    method: req.method,
  });

  if (!rateLimitResult.success) {
    return res.status(429).json({
      error: 'Too many requests',
      retryAfter: rateLimitResult.retryAfter,
    });
  }

  // Verificação de token com rotação e binding
  if (req.session?.token) {
    const tokenValidation = await tokenService.validate({
      token: req.session.token,
      fingerprint: req.headers['x-device-fingerprint'],
      ip: req.socket.remoteAddress,
    });

    if (!tokenValidation.valid) {
      return res.status(401).json({ error: 'Session expired or invalid' });
    }

    // Rotação de token se necessário
    if (tokenValidation.shouldRotate) {
      const newToken = await tokenService.rotate(req.session.token);
      req.session.token = newToken;
    }
  }

  // Análise de comportamento para detecção de anomalias
  const behaviorScore = await behaviorAnalyzer.analyze({
    userId: req.session?.user?.id,
    ip: req.socket.remoteAddress,
    userAgent: req.headers['user-agent'],
    timestamp: new Date(),
    action: `${req.method} ${req.url}`,
  });

  if (behaviorScore.anomaly) {
    // Registrar evento suspeito
    await securityEventLogger.log({
      type: 'SUSPICIOUS_BEHAVIOR',
      userId: req.session?.user?.id,
      details: behaviorScore.details,
      score: behaviorScore.score,
    });

    // Exigir verificação adicional para ações suspeitas
    if (behaviorScore.score > 0.8) {
      return res.status(403).json({
        error: 'Additional verification required',
        verificationId: await createVerificationChallenge(req.session?.user?.id),
      });
    }
  }

  // Continuar com a requisição
  await next();
};
```

## 🚀 Otimizações de Performance de Última Geração

- **Server Components Resumáveis**: Preservação de estado entre server e client
- **Streaming Adaptativo**: SSR progressivo com priorização de conteúdo crítico
- **Partial Hydration**: Hidratação seletiva de ilhas interativas
- **Uso de Comlink**: Web Workers transparentes para operações pesadas
- **Rendering Multi-thread**: Distribuição de rendering entre threads
- **Pré-computação de Fórmulas**: Cálculos complexos em edge functions
- **Persistence Otimizada**: Delta encoding para armazenamento eficiente
- **Request Coalescing**: Agrupamento inteligente de requisições

### Perfil de Desempenho Alvo

- **Core Web Vitals**: LCP < 1.2s, INP < 80ms, CLS < 0.05
- **Time-to-Interactive**: < 1.5s em conexões 4G
- **First Meaningful Paint**: < 0.8s
- **Bundle Size**: < 120KB inicial (comprimido com Brotli)
- **Memory Usage**: Pico < 60MB em dispositivos móveis
- **Idle CPU**: < 5% em background

## 🔬 Experimentação e Feature Flags

- **Sistema de Feature Flags**: Ativação gradual de funcionalidades
- **Testes A/B**: Infraestrutura para experimentos de UX
- **Canary Releases**: Lançamentos graduais por segmento de usuários
- **Analytics**: Coleta de métricas de uso para decisões baseadas em dados
- **Feedback Loop**: Sistema integrado de feedback com categorização automática

## 📱 Experiências Multi-dispositivo

- **PWA Avançado**: Funcionalidade offline completa com sincronização
- **Responsive+**: Adaptação não apenas ao tamanho, mas ao dispositivo e contexto
- **Desktop App**: Versão Tauri com acesso a funcionalidades nativas
- **Integração Mobile**: Hooks para React Native e Flutter
- **Voice UI**: Interface por voz para acessibilidade e uso hands-free

## 🌐 Sustentabilidade Digital

- **Eficiência Energética**: Otimizações para reduzir consumo de bateria
- **Carbon-Aware Computing**: Ajuste de cargas de trabalho intensivas
- **Web Sostenible**: Práticas de desenvolvimento web sustentável
- **Green Hosting**: Infraestrutura em data centers com energia renovável
- **Análise de Impacto**: Monitoramento e redução de pegada de carbono

## 📊 Métricas e Monitoramento

- **OpenTelemetry**: Instrumentação para métricas, traces e logs
- **Real User Monitoring**: Coleta de métricas de experiência real
- **Error Tracking**: Captura e análise detalhada de erros
- **Performance Budgets**: Alertas quando limites são excedidos
- **Dashboards**: Visualizações em tempo real do estado do sistema

## 📜 Licenciamento

Excel Copilot é licenciado sob [License Type]. Consulte o arquivo LICENSE para mais detalhes.

## 👥 Equipe e Contato

Para mais informações, entre em contato pelo email: <EMAIL>
