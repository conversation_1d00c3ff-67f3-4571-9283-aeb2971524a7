#!/usr/bin/env node

/**
 * Script para reconstruir a aplicação desde o início
 * Resolve problemas de módulos não encontrados e erros de build
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🧹 Iniciando reconstrução completa do projeto...');

// Diretórios para remover
const dirsToRemove = ['.next', 'node_modules/.cache', '.swc'];

// Função para executar comandos com log
function runCommand(command, message) {
  console.log(`\n🔄 ${message}...`);
  try {
    execSync(command, { stdio: 'inherit' });
    console.log(`✅ Concluído: ${message}`);
    return true;
  } catch (error) {
    console.error(`❌ Erro: ${message}`, error.message);
    return false;
  }
}

// Remover diretórios de cache
console.log('\n🧹 Removendo diretórios de cache...');
dirsToRemove.forEach(dir => {
  const fullPath = path.resolve(process.cwd(), dir);
  if (fs.existsSync(fullPath)) {
    try {
      fs.rmSync(fullPath, { recursive: true, force: true });
      console.log(`✅ Removido: ${dir}`);
    } catch (error) {
      console.error(`❌ Erro ao remover ${dir}:`, error.message);
    }
  } else {
    console.log(`ℹ️ Não encontrado: ${dir}`);
  }
});

// Limpar cache do npm
runCommand('npm cache clean --force', 'Limpando cache do npm');

// Limpar node_modules e reinstalar dependências
if (runCommand('npm install --no-cache', 'Reinstalando dependências')) {
  // Gerar código Prisma
  runCommand('npx prisma generate', 'Gerando código Prisma');

  // Limpar cache do Next.js
  console.log('\n🧹 Limpando cache do Next.js...');
  try {
    if (fs.existsSync('.next/cache')) {
      fs.rmSync('.next/cache', { recursive: true, force: true });
      console.log('✅ Removido: .next/cache');
    }
  } catch (error) {
    console.error('❌ Erro ao limpar cache do Next.js:', error.message);
  }

  // Compilar o projeto em modo de desenvolvimento
  runCommand('npm run build', 'Compilando projeto');
}

console.log('\n🎉 Reconstrução concluída!');
console.log('Agora você pode executar "npm run dev" para iniciar o servidor de desenvolvimento.');
