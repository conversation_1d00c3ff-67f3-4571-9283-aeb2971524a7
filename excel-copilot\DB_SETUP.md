# Configuração do Banco de Dados para Excel Copilot

Este documento descreve como configurar o banco de dados PostgreSQL para a aplicação Excel Copilot no ambiente Vercel.

## Configuração no Supabase

1. No Supabase, acesse seu projeto e vá para "Settings" (Configurações) > "Database" (Banco de Dados)
2. Copie a string de conexão do Pooler de Transações (Transaction Pooler):
   ```
   postgresql://postgres.eliuoignzzxnjkcmmtml:[YOUR-PASSWORD]@aws-0-sa-east-1.pooler.supabase.com:6543/postgres
   ```
3. Para o `DIRECT_URL`, use a string do "Session Pooler":
   ```
   postgresql://postgres.eliuoignzzxnjkcmmtml:[YOUR-PASSWORD]@aws-0-sa-east-1.pooler.supabase.com:5432/postgres
   ```

## Configuração na Vercel

1. Na Vercel, vá para o seu projeto e acesse "Settings" > "Environment Variables"
2. <PERSON><PERSON><PERSON> as seguintes variáveis de ambiente:

   ```
   DATABASE_URL=postgresql://postgres.eliuoignzzxnjkcmmtml:[PASSWORD]@aws-0-sa-east-1.pooler.supabase.com:6543/postgres?sslmode=require&connection_limit=5
   DIRECT_URL=postgresql://postgres.eliuoignzzxnjkcmmtml:[PASSWORD]@aws-0-sa-east-1.pooler.supabase.com:5432/postgres?sslmode=require
   ```

3. Substitua `[PASSWORD]` pela senha real do seu banco de dados
4. Adicione outras variáveis necessárias:
   ```
   NEXTAUTH_URL=https://excel-copilot-eight.vercel.app
   NEXTAUTH_SECRET=sua_chave_secreta
   ```

## Executando migrações manualmente

Para executar migrações manualmente, siga os passos:

1. Instale a Vercel CLI: `npm i -g vercel`
2. Faça login: `vercel login`
3. Execute o comando: `vercel env pull` para baixar as variáveis de ambiente
4. Execute: `npx prisma migrate deploy` para aplicar as migrações

## Verificando o status do banco de dados

Para verificar se o banco de dados está configurado corretamente:

1. Acesse a URL: `https://excel-copilot-eight.vercel.app/api/db-status`
2. Você verá um JSON com o status e as tabelas do banco de dados

## Solução de problemas

### Tabelas não existem

Se receber o erro `The table 'public.Account' does not exist in the current database`, você precisa executar as migrações:

1. Na Vercel, vá para "Deployments" (Implantações)
2. Clique em "Redeploy" (Reimplantar)
3. Marque a opção "Override" e adicione a variável de ambiente:
   ```
   RUN_MIGRATION=true
   ```

### Erro de conexão

Se houver erros de conexão com o banco de dados, verifique:

1. Se a string de conexão está correta
2. Se o IP da Vercel está na lista de permissões do Supabase (geralmente isso não é necessário com o Pooler)
3. Se a senha do banco de dados está correta

## Estrutura do Banco de Dados

As principais tabelas da aplicação são:

- `Account` - Contas de autenticação dos usuários (NextAuth)
- `Session` - Sessões de usuários (NextAuth)
- `User` - Informações dos usuários
- `VerificationToken` - Tokens de verificação (NextAuth)
- `Subscription` - Assinaturas dos usuários
- `Workbook` - Planilhas dos usuários
