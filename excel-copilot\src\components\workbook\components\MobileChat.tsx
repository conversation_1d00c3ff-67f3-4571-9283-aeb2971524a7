'use client';

import React from 'react';
import { X, MessageSquare, Sparkles } from 'lucide-react';

import { But<PERSON> } from '@/components/ui/button';
import { Sheet, SheetContent, SheetHeader, SheetTitle } from '@/components/ui/sheet';
import { ChatInterface } from '@/components/chat-interface';

interface MobileChatProps {
  // Estados
  isOpen: boolean;
  inputText: string;
  isProcessing: boolean;
  readOnly: boolean;

  // Dados
  spreadsheetData: any;

  // Handlers
  onClose: () => void;
  onInputChange: (text: string) => void;
  onProcessCommand: (command: string) => void;
}

export function MobileChat({
  isOpen,
  inputText,
  isProcessing,
  readOnly,
  spreadsheetData,
  onClose,
  onInputChange,
  onProcessCommand,
}: MobileChatProps) {
  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent side="bottom" className="h-[80vh]">
        <SheetHeader>
          <SheetTitle className="flex items-center space-x-2">
            <Sparkles className="h-5 w-5 text-primary" />
            <span>Assistente IA</span>
          </SheetTitle>
        </SheetHeader>

        <div className="flex flex-col h-full mt-4">
          {/* Interface do Chat */}
          <div className="flex-1">
            <ChatInterface
              data={spreadsheetData}
              onOperations={onProcessCommand}
              disabled={readOnly}
              placeholder={
                readOnly
                  ? 'Modo somente leitura'
                  : 'Digite um comando para a IA...'
              }
              inputValue={inputText}
              onInputChange={onInputChange}
              isProcessing={isProcessing}
              isMobile={true}
            />
          </div>

          {/* Comandos rápidos para mobile */}
          {!readOnly && (
            <div className="mt-4 p-4 bg-muted/30 rounded-lg">
              <h4 className="text-sm font-medium mb-3">Comandos rápidos:</h4>
              <div className="grid grid-cols-2 gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onProcessCommand('Adicione uma nova linha')}
                  className="text-xs"
                >
                  + Linha
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onProcessCommand('Adicione uma nova coluna')}
                  className="text-xs"
                >
                  + Coluna
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onProcessCommand('Calcule a soma')}
                  className="text-xs"
                >
                  Σ Soma
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onProcessCommand('Ordene os dados')}
                  className="text-xs"
                >
                  ↕ Ordenar
                </Button>
              </div>
            </div>
          )}
        </div>
      </SheetContent>
    </Sheet>
  );
}
