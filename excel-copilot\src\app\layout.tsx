import type { Metadata } from 'next';
// Remover completamente qualquer referência a next/font
import Link from 'next/link';
import { ThemeProvider } from 'next-themes';
import { Suspense, lazy } from 'react';

// Importar supressor simples de erros de IA
import '@/lib/ai/simple-error-suppressor';

import { ClientScripts } from '@/components/client-scripts';
import ClientLayoutWrapper from '@/components/ClientLayoutWrapper';
import { siteConfig } from '@/config/site';
import { cn } from '@/lib/utils';

import { Providers } from './providers';
import './globals.css';

// Componentes carregados com lazy loading
const NavBar = lazy(() => import('@/components/nav-bar').then(mod => ({ default: mod.NavBar })));
const UserNav = lazy(() => import('@/components/user-nav').then(mod => ({ default: mod.UserNav })));

// Usar classe CSS padrão para a fonte
const fontClasses = 'font-sans'; // Agora definido no tailwind.config.ts como Inter

export const metadata: Metadata = {
  title: {
    default: 'Excel Copilot - Automatize planilhas com IA e linguagem natural',
    template: '%s | Excel Copilot - Automação inteligente de planilhas',
  },
  description:
    'Excel Copilot transforma comandos em linguagem natural em planilhas poderosas. Crie tabelas, gráficos e análises complexas sem fórmulas ou códigos complicados.',
  keywords: [
    'Excel Copilot',
    'automação de planilhas',
    'IA para Excel',
    'planilhas inteligentes',
    'Excel com IA',
    'análise de dados simplificada',
    'gráficos automáticos',
    'linguagem natural para Excel',
    'transformar texto em planilha',
    'Microsoft Excel assistente',
    'análise de dados sem código',
    'ferramenta produtividade planilhas',
  ],
  applicationName: siteConfig.name,
  authors: [{ name: 'Excel Copilot Team', url: 'https://excel-copilot.vercel.app' }],
  creator: 'Excel Copilot Team',
  publisher: 'Excel Copilot',
  formatDetection: {
    telephone: false,
    email: false,
    address: false,
  },
  alternates: {
    canonical: '/',
    languages: {
      'pt-BR': '/',
      'en-US': '/en',
    },
  },
  openGraph: {
    title: 'Excel Copilot - Automatize suas planilhas com IA e linguagem natural',
    description:
      'Transforme comandos em texto simples em planilhas profissionais com gráficos, análises e cálculos automáticos. Economize tempo e aumente sua produtividade.',
    url: 'https://excel-copilot.app',
    siteName: 'Excel Copilot',
    locale: 'pt_BR',
    type: 'website',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'Excel Copilot - Automatize suas planilhas com IA',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Excel Copilot - Automatize suas planilhas com IA',
    description:
      'Transforme comandos em texto simples em planilhas profissionais. Integrável com Microsoft Excel e Google Sheets.',
    creator: '@excelcopilot',
    images: {
      url: '/twitter-image.jpg',
      alt: 'Excel Copilot - Interface da aplicação mostrando comandos sendo transformados em planilhas',
    },
  },
  icons: {
    icon: [
      { url: '/favicon.ico', sizes: 'any' },
      { url: '/icon.png', sizes: '32x32' },
      { url: '/icon-192.png', sizes: '192x192' },
      { url: '/icon-512.png', sizes: '512x512' },
    ],
    shortcut: '/favicon.ico',
    apple: [{ url: '/apple-touch-icon.png', sizes: '180x180' }],
    other: [
      {
        rel: 'mask-icon',
        url: '/safari-pinned-tab.svg',
        color: '#2563eb',
      },
    ],
  },
  metadataBase: new URL(process.env.NEXT_PUBLIC_APP_URL || 'https://excel-copilot.vercel.app'),
  manifest: '/manifest.json',
  category: 'productivity',
  other: {
    'web-vitals-spec': 'https://wicg.github.io/web-vitals-spec/',
    'apple-mobile-web-app-capable': 'yes',
    'apple-mobile-web-app-status-bar-style': 'default',
    'format-detection': 'telephone=no',
    'mobile-web-app-capable': 'yes',
    'msapplication-TileColor': '#2563eb',
    'msapplication-config': '/browserconfig.xml',
  },
  robots: {
    index: true,
    follow: true,
    'max-image-preview': 'large',
    'max-video-preview': -1,
    'max-snippet': -1,
    nocache: false,
    googleBot: {
      index: true,
      follow: true,
      'max-image-preview': 'large',
      'max-video-preview': -1,
      'max-snippet': -1,
      noimageindex: false,
    },
  },
};

// Exportação separada para viewport conforme recomendado pelo Next.js 14.2+
export const viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 5,
  userScalable: true,
  viewportFit: 'cover',
  themeColor: [
    { media: '(prefers-color-scheme: light)', color: '#ffffff' },
    { media: '(prefers-color-scheme: dark)', color: '#0f172a' },
  ],
};

// Componente para o rodapé do site com lazy loading
const SiteFooter = lazy(() =>
  Promise.resolve({
    default: () => (
      <footer className="py-6 md:py-8 border-t">
        <div className="container flex flex-col items-center justify-between gap-4 md:h-24 md:flex-row">
          <p className="text-center text-sm leading-loose text-muted-foreground md:text-left">
            &copy; {new Date().getFullYear()} Excel Copilot. Todos os direitos reservados.
          </p>
          <div className="flex items-center gap-4 text-sm text-muted-foreground">
            <Link href="/terms" className="hover:underline hover:text-foreground transition-colors">
              Termos
            </Link>
            <Link
              href="/privacy"
              className="hover:underline hover:text-foreground transition-colors"
            >
              Privacidade
            </Link>
          </div>
        </div>
      </footer>
    ),
  })
);

// Componentes de fallback para usar com Suspense
const NavBarFallback = () => <div className="h-10 w-36 bg-muted animate-pulse rounded"></div>;
const UserNavFallback = () => (
  <div className="flex items-center space-x-2">
    <div className="h-8 w-8 bg-muted animate-pulse rounded-full"></div>
  </div>
);
const FooterFallback = () => <div className="h-16 w-full bg-muted/30 animate-pulse"></div>;

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="pt-BR" suppressHydrationWarning>
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0" />
        <meta name="theme-color" content="#2563eb" />

        {/* Script removido - agora usando componente ClientScripts */}

        {/* Metadados estruturados para SEO (Schema.org) */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              '@context': 'https://schema.org',
              '@type': 'SoftwareApplication',
              name: 'Excel Copilot',
              applicationCategory: 'BusinessApplication',
              operatingSystem: 'Web, Windows',
              offers: {
                '@type': 'Offer',
                price: '0',
                priceCurrency: 'BRL',
              },
              description:
                'Excel Copilot transforma comandos em linguagem natural em planilhas poderosas.',
              aggregateRating: {
                '@type': 'AggregateRating',
                ratingValue: '4.8',
                ratingCount: '127',
                reviewCount: '68',
              },
            }),
          }}
        />
      </head>
      <body className={cn('min-h-screen bg-background antialiased', fontClasses)}>
        <ClientScripts />
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          <Providers>
            <ClientLayoutWrapper interClassName={fontClasses}>
              <div className="relative flex min-h-screen flex-col">
                <header className="sticky top-0 z-50 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 w-full shadow-sm">
                  <div className="container flex h-14 items-center justify-between">
                    <div className="flex items-center">
                      <Suspense fallback={<NavBarFallback />}>
                        <NavBar />
                      </Suspense>
                    </div>
                    <div className="flex items-center space-x-3">
                      <Suspense fallback={<UserNavFallback />}>
                        <UserNav />
                      </Suspense>
                    </div>
                  </div>
                </header>
                <main className="flex-1">{children}</main>
                <Suspense fallback={<FooterFallback />}>
                  <SiteFooter />
                </Suspense>
              </div>
            </ClientLayoutWrapper>
          </Providers>
        </ThemeProvider>
      </body>
    </html>
  );
}
