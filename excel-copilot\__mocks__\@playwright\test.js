// Mock do módulo @playwright/test para evitar erros quando executando através do Jest

// Mock da função test
const test = function (name, fn) {
  // Não faz nada, apenas impede erros
};

// Implementação de helpers para evitar erros
test.describe = (name, fn) => {};
test.beforeAll = fn => {};
test.afterAll = fn => {};
test.beforeEach = fn => {};
test.afterEach = fn => {};
test.use = config => {};
test.skip = (name, fn) => {};
test.only = (name, fn) => {};
test.fixme = (name, fn) => {};
test.slow = (name, fn) => {};
test.setTimeout = timeout => {};
test.step = async (name, fn) => await fn();
test.info = () => ({
  annotations: [],
  attachments: [],
  workerIndex: 0,
  project: { name: 'mock' },
});

// Mock de expect
const expect = jest.fn().mockImplementation(received => ({
  toBe: jest.fn(),
  toEqual: jest.fn(),
  toContain: jest.fn(),
  toHaveText: jest.fn(),
  toBeVisible: jest.fn(),
  toBeHidden: jest.fn(),
  toHaveCount: jest.fn(),
  toHaveAttribute: jest.fn(),
  toHaveURL: jest.fn(),
}));

expect.extend = () => {};
expect.assertions = () => {};
expect.hasAssertions = () => {};
expect.poll = async callback => await callback();

// Exportar mocks para tornar os testes do Playwright compatíveis com o Jest
module.exports = {
  test,
  expect,
  devices: {
    'iPhone 12': {},
    'Pixel 5': {},
    'Desktop Chrome': {},
    'Desktop Safari': {},
    'Desktop Firefox': {},
  },
};
