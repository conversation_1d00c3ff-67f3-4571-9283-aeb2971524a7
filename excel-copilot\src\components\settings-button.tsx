'use client';

import { Setting<PERSON> } from 'lucide-react';
import { useRouter } from 'next/navigation';

import { Button } from '@/components/ui/button';

export function SettingsButton() {
  const router = useRouter();

  return (
    <Button
      variant="ghost"
      size="icon"
      className="h-8 w-8"
      onClick={() => router.push('/settings')}
    >
      <Settings className="h-4 w-4" />
      <span className="sr-only">Configurações</span>
    </Button>
  );
}
