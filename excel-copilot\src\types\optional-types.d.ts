/**
 * Utilitários de tipagem para lidar com tipos opcionais
 * Este arquivo resolve problemas relacionados à flag exactOptionalPropertyTypes no tsconfig.json
 */

/**
 * Tipo utilitário que permite undefined como valor para propriedades opcionais
 * Resolve problemas com exactOptionalPropertyTypes: true
 */
export type WithOptionalFields<T> = {
  [K in keyof T]: T[K] extends undefined ? T[K] | undefined : T[K];
};

/**
 * Converte um tipo para permitir propriedades opcionais que aceitem undefined
 * Útil para objetos que são passados como parâmetros para APIs Prisma
 */
export type PrismaOptional<T> = {
  [K in keyof T]: T[K] extends { optional?: boolean } ? T[K] | null | undefined : T[K];
};

/**
 * Tipo utilitário que converte string | undefined para string | null
 * Útil em input para Prisma onde undefined não é permitido
 */
export type NullableString = string | null;

/**
 * Converte undefined para null em strings opcionais para compatibilidade com Prisma
 */
export declare function toNullableString(value: string | undefined): NullableString;

/**
 * Utilitários para trabalhar de forma segura com objetos e arrays que podem ter undefined
 */

/**
 * Acesso seguro a um elemento de array por índice
 * @param arr O array para acessar
 * @param index O índice a acessar
 * @returns O elemento no índice ou undefined se o índice for inválido
 */
export declare function safeArrayAccess<T>(
  arr: T[] | undefined | null,
  index: number
): T | undefined;

/**
 * Acesso seguro a uma propriedade de objeto
 * @param obj O objeto para acessar
 * @param key A chave a acessar
 * @returns O valor da propriedade ou undefined se a chave não existir
 */
export declare function safeObjectAccess<T = any>(
  obj: Record<string, T> | undefined | null,
  key: string
): T | undefined;

/**
 * Acesso seguro a uma propriedade aninhada de objeto
 * @param obj O objeto para acessar
 * @param path Caminho de propriedades separado por pontos (ex: "user.address.street")
 * @returns O valor da propriedade aninhada ou undefined se qualquer parte do caminho não existir
 */
export declare function safeNestedAccess<T = any>(
  obj: Record<string, any> | undefined | null,
  path: string
): T | undefined;

/**
 * Verifica se um valor é undefined ou null
 * @param value O valor a verificar
 * @returns true se o valor for undefined ou null
 */
export declare function isNullOrUndefined(value: unknown): value is null | undefined;

/**
 * Fornece um valor padrão se o valor original for null ou undefined
 * @param value O valor original
 * @param defaultValue O valor padrão a retornar
 * @returns O valor original ou o valor padrão
 */
export declare function withDefault<T>(value: T | null | undefined, defaultValue: T): T;
