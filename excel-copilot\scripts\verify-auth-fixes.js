#!/usr/bin/env node

/**
 * Script para verificar se as correções de autenticação foram aplicadas corretamente
 * Executa verificações sistemáticas para garantir que os erros foram resolvidos
 */

const fs = require('fs');
const path = require('path');

// Cores para output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m',
};

console.log(
  `${colors.blue}${colors.bold}🔍 VERIFICAÇÃO DAS CORREÇÕES DE AUTENTICAÇÃO${colors.reset}\n`
);

let hasErrors = false;
const results = [];

/**
 * Verificar se o interceptador de erros está configurado corretamente
 */
function checkErrorSuppressor() {
  console.log(`${colors.blue}📋 Verificando interceptador de erros...${colors.reset}`);

  const suppressorPath = path.join(__dirname, '../src/lib/ai/simple-error-suppressor.ts');

  if (!fs.existsSync(suppressorPath)) {
    results.push(
      `${colors.red}❌ Arquivo simple-error-suppressor.ts não encontrado${colors.reset}`
    );
    hasErrors = true;
    return;
  }

  const content = fs.readFileSync(suppressorPath, 'utf8');

  // Verificar se contém os padrões de erro expandidos
  const requiredPatterns = [
    'Neither apiKey nor config.authenticator provided',
    'authenticator not provided',
    'API key not provided',
    '@google/generative-ai',
    '@google-cloud/vertexai',
  ];

  let missingPatterns = [];
  requiredPatterns.forEach(pattern => {
    if (!content.includes(pattern)) {
      missingPatterns.push(pattern);
    }
  });

  if (missingPatterns.length > 0) {
    results.push(
      `${colors.red}❌ Padrões de erro faltando no interceptador: ${missingPatterns.join(', ')}${colors.reset}`
    );
    hasErrors = true;
  } else {
    results.push(
      `${colors.green}✅ Interceptador de erros configurado corretamente${colors.reset}`
    );
  }
}

/**
 * Verificar se o layout principal tem o script de bloqueio
 */
function checkLayoutScript() {
  console.log(`${colors.blue}📋 Verificando script de bloqueio no layout...${colors.reset}`);

  const layoutPath = path.join(__dirname, '../src/app/layout.tsx');

  if (!fs.existsSync(layoutPath)) {
    results.push(`${colors.red}❌ Arquivo layout.tsx não encontrado${colors.reset}`);
    hasErrors = true;
    return;
  }

  const content = fs.readFileSync(layoutPath, 'utf8');

  // Verificar se contém o script de bloqueio
  const requiredElements = [
    'AI Blocker',
    'createSilentMock',
    'GoogleGenerativeAI',
    'VertexAI',
    'Neither apiKey nor config.authenticator provided',
  ];

  let missingElements = [];
  requiredElements.forEach(element => {
    if (!content.includes(element)) {
      missingElements.push(element);
    }
  });

  if (missingElements.length > 0) {
    results.push(
      `${colors.red}❌ Elementos faltando no script de bloqueio: ${missingElements.join(', ')}${colors.reset}`
    );
    hasErrors = true;
  } else {
    results.push(
      `${colors.green}✅ Script de bloqueio configurado corretamente no layout${colors.reset}`
    );
  }
}

/**
 * Verificar configuração do ambiente
 */
function checkEnvironmentConfig() {
  console.log(`${colors.blue}📋 Verificando configuração de ambiente...${colors.reset}`);

  const envPath = path.join(__dirname, '../src/config/environment.ts');

  if (!fs.existsSync(envPath)) {
    results.push(`${colors.red}❌ Arquivo environment.ts não encontrado${colors.reset}`);
    hasErrors = true;
    return;
  }

  const content = fs.readFileSync(envPath, 'utf8');

  // Verificar se a configuração de fallback está correta
  if (content.includes("throw new Error('Vertex AI deve estar configurado em produção')")) {
    results.push(
      `${colors.red}❌ Configuração ainda está falhando em produção sem Vertex AI${colors.reset}`
    );
    hasErrors = true;
  } else if (
    content.includes('this.FEATURES.USE_MOCK_AI = true') &&
    content.includes('fallback seguro em produção')
  ) {
    results.push(`${colors.green}✅ Configuração de fallback seguro implementada${colors.reset}`);
  } else {
    results.push(
      `${colors.yellow}⚠️ Configuração de fallback pode precisar de ajustes${colors.reset}`
    );
  }
}

/**
 * Verificar se há referências problemáticas no código
 */
function checkProblematicReferences() {
  console.log(`${colors.blue}📋 Verificando referências problemáticas...${colors.reset}`);

  const srcPath = path.join(__dirname, '../src');

  // Padrões problemáticos que podem causar o erro
  const problematicPatterns = [
    'new GoogleGenerativeAI\\(',
    'new VertexAI\\(',
    'GoogleGenerativeAI\\(',
    'import.*@google/generative-ai',
    'require.*@google/generative-ai',
  ];

  let foundProblems = [];

  function scanDirectory(dir) {
    const files = fs.readdirSync(dir);

    files.forEach(file => {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);

      if (stat.isDirectory() && !file.startsWith('.') && file !== 'node_modules') {
        scanDirectory(filePath);
      } else if (
        file.endsWith('.ts') ||
        file.endsWith('.tsx') ||
        file.endsWith('.js') ||
        file.endsWith('.jsx')
      ) {
        const content = fs.readFileSync(filePath, 'utf8');

        // Pular arquivos do servidor que devem usar VertexAI
        const isServerFile =
          filePath.includes('/server/') ||
          filePath.includes('\\server\\') ||
          filePath.includes('/lib/ai/gemini-service.ts') ||
          filePath.includes('\\lib\\ai\\gemini-service.ts');

        problematicPatterns.forEach(pattern => {
          const regex = new RegExp(pattern, 'gi');
          if (regex.test(content)) {
            // Se é arquivo do servidor e o padrão é VertexAI, não é problema
            if (
              isServerFile &&
              (pattern.includes('VertexAI') || pattern.includes('GoogleGenerativeAI'))
            ) {
              return; // Pular - é uso legítimo no servidor
            }
            foundProblems.push(`${filePath}: ${pattern}`);
          }
        });
      }
    });
  }

  try {
    scanDirectory(srcPath);

    if (foundProblems.length > 0) {
      results.push(`${colors.red}❌ Referências problemáticas encontradas:${colors.reset}`);
      foundProblems.forEach(problem => {
        results.push(`   ${colors.red}• ${problem}${colors.reset}`);
      });
      hasErrors = true;
    } else {
      results.push(`${colors.green}✅ Nenhuma referência problemática encontrada${colors.reset}`);
    }
  } catch (error) {
    results.push(
      `${colors.yellow}⚠️ Erro ao verificar referências: ${error.message}${colors.reset}`
    );
  }
}

/**
 * Executar todas as verificações
 */
function runAllChecks() {
  checkErrorSuppressor();
  checkLayoutScript();
  checkEnvironmentConfig();
  checkProblematicReferences();

  // Mostrar resultados
  console.log(`\n${colors.blue}${colors.bold}📊 RESULTADOS DA VERIFICAÇÃO:${colors.reset}\n`);

  results.forEach(result => {
    console.log(result);
  });

  console.log(`\n${colors.blue}${colors.bold}📋 RESUMO:${colors.reset}`);

  if (hasErrors) {
    console.log(`${colors.red}❌ Algumas correções precisam de ajustes${colors.reset}`);
    console.log(`${colors.yellow}💡 Revise os itens marcados com ❌ acima${colors.reset}`);
    process.exit(1);
  } else {
    console.log(
      `${colors.green}✅ Todas as correções foram aplicadas corretamente!${colors.reset}`
    );
    console.log(`${colors.green}🚀 O site deve funcionar sem erros de autenticação${colors.reset}`);

    console.log(`\n${colors.blue}${colors.bold}🔄 PRÓXIMOS PASSOS:${colors.reset}`);
    console.log(
      `${colors.blue}1.${colors.reset} Faça deploy das correções: ${colors.yellow}vercel --prod${colors.reset}`
    );
    console.log(
      `${colors.blue}2.${colors.reset} Acesse o site: ${colors.yellow}https://excel-copilot-eight.vercel.app/${colors.reset}`
    );
    console.log(
      `${colors.blue}3.${colors.reset} Abra o DevTools (F12) e verifique se não há mais erros no console`
    );
    console.log(
      `${colors.blue}4.${colors.reset} Teste as funcionalidades principais (login, navegação, etc.)`
    );
  }
}

// Executar verificações
runAllChecks();
