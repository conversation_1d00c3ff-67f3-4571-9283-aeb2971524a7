# Instruções para Deploy com Vertex AI no Vercel

Este guia apresenta o processo para configurar e fazer deploy da aplicação Excel Copilot no Vercel, utilizando o Google Vertex AI como provedor de inteligência artificial.

## Pré-requisitos

1. Conta no Vercel
2. CLI do Vercel instalado e configurado
3. Arquivo `vertex-credentials.json` com as credenciais do Google Cloud

## Passos para configuração

### 1. Preparar as credenciais

Execute o script para preparar as credenciais:

```bash
npm run vercel:upload-credentials
```

O script irá criar um arquivo `vertex-credentials-upload.json` com o conteúdo formatado para facilitar o upload.

### 2. Adicionar variáveis de ambiente no Vercel

Execute os seguintes comandos manualmente:

```bash
# Habilitar Vertex AI
vercel env add VERTEX_AI_ENABLED production
# (Digite "true" quando solicitado)

# Desativar modo mock de IA
vercel env add USE_MOCK_AI production
# (Digite "false" quando solicitado)

# Adicionar credenciais do Vertex AI
vercel env add VERTEX_AI_CREDENTIALS production
# (Copie e cole o conteúdo do arquivo vertex-credentials-upload.json quando solicitado)
```

### 3. Realizar o deploy

Após configurar as variáveis, execute:

```bash
vercel --prod
```

## Solução de problemas

### Build falha com erro relacionado a Vertex AI

Certifique-se de que as variáveis de ambiente foram configuradas corretamente. Você pode verificar as variáveis existentes com:

```bash
vercel env ls
```

Se ainda tiver problemas, você pode tentar ativar o modo mock temporariamente:

```bash
vercel env add USE_MOCK_AI production
# (Digite "true" quando solicitado)
```

## Verificação

Após o deploy, verifique se a API de IA está funcionando corretamente acessando:

```
https://[seu-domínio]/api/health
```

O status deve mostrar "VERTEX_AI_ENABLED: true" se tudo estiver configurado corretamente.
