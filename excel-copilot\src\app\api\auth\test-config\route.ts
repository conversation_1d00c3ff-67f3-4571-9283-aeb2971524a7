/**
 * Endpoint para testar configuração OAuth em tempo real
 * Verifica se os providers estão funcionando corretamente
 */

import { NextRequest, NextResponse } from 'next/server';

import { ENV } from '@/config/unified-environment';
import { validateGoogleOAuthConfig, validateGitHubOAuthConfig } from '@/lib/auth/validation';
import { logger } from '@/lib/logger';

interface GoogleOAuthChecks {
  credentials_present: boolean;
  client_id_format: boolean;
  client_secret_length: boolean;
  validation_passed: boolean;
  google_service_reachable?: boolean;
  authorization_endpoint?: boolean;
  token_endpoint?: boolean;
  service_error?: string;
  [key: string]: boolean | string | undefined;
}

interface GitHubOAuthChecks {
  credentials_present: boolean;
  client_id_length: boolean;
  client_secret_length: boolean;
  validation_passed: boolean;
  github_service_reachable?: boolean;
  service_error?: string;
  [key: string]: boolean | string | undefined;
}

interface NextAuthChecks {
  secret_present: boolean;
  secret_length: boolean;
  url_present: boolean;
  url_valid?: boolean;
  url_https?: boolean;
  [key: string]: boolean | string | undefined;
}

interface DatabaseChecks {
  url_present: boolean;
  connection_successful?: boolean;
  query_successful?: boolean;
  connection_error?: string;
  [key: string]: boolean | string | undefined;
}

export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

/**
 * GET /api/auth/test-config
 * Testa a configuração OAuth sem fazer login real
 */
export async function GET(request: NextRequest) {
  try {
    logger.info('🧪 Teste de configuração OAuth solicitado', {
      ip: request.ip,
      userAgent: request.headers.get('user-agent'),
      timestamp: new Date().toISOString(),
    });

    const results = {
      timestamp: new Date().toISOString(),
      environment: ENV.NODE_ENV,
      tests: {
        google: await testGoogleOAuth(),
        github: await testGitHubOAuth(),
        nextauth: testNextAuthConfig(),
        database: await testDatabaseConnection(),
      },
    };

    // Determinar status geral
    const allTestsPass = Object.values(results.tests).every(test => test.status === 'pass');
    const statusCode = allTestsPass ? 200 : 503;

    // Log do resultado
    logger.info(
      allTestsPass ? '✅ Teste de configuração: APROVADO' : '⚠️ Teste de configuração: PROBLEMAS',
      {
        statusCode,
        googleStatus: results.tests.google.status,
        githubStatus: results.tests.github.status,
        nextauthStatus: results.tests.nextauth.status,
        databaseStatus: results.tests.database.status,
      }
    );

    return NextResponse.json(
      {
        status: allTestsPass ? 'healthy' : 'unhealthy',
        ...results,
      },
      { status: statusCode }
    );
  } catch (error) {
    logger.error('❌ Erro no teste de configuração OAuth', {
      error: error instanceof Error ? error.message : 'Erro desconhecido',
      stack: error instanceof Error ? error.stack : undefined,
    });

    return NextResponse.json(
      {
        status: 'error',
        message: 'Erro interno no teste de configuração',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/auth/test-config
 * Executa teste detalhado com tentativa de conexão real
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json().catch(() => ({}));
    const { provider, detailed = false } = body;

    logger.info('🔬 Teste detalhado de configuração OAuth', {
      provider,
      detailed,
      ip: request.ip,
      timestamp: new Date().toISOString(),
    });

    let results;

    if (provider) {
      // Testar provider específico
      switch (provider.toLowerCase()) {
        case 'google':
          results = { google: await testGoogleOAuth(detailed) };
          break;
        case 'github':
          results = { github: await testGitHubOAuth(detailed) };
          break;
        default:
          return NextResponse.json(
            { error: 'Provider não suportado. Use: google, github' },
            { status: 400 }
          );
      }
    } else {
      // Testar todos os providers
      results = {
        google: await testGoogleOAuth(detailed),
        github: await testGitHubOAuth(detailed),
        nextauth: testNextAuthConfig(),
        database: await testDatabaseConnection(),
      };
    }

    const allTestsPass = Object.values(results).every(test => test.status === 'pass');

    return NextResponse.json(
      {
        status: allTestsPass ? 'healthy' : 'unhealthy',
        timestamp: new Date().toISOString(),
        environment: ENV.NODE_ENV,
        tests: results,
      },
      { status: allTestsPass ? 200 : 503 }
    );
  } catch (error) {
    logger.error('❌ Erro no teste detalhado de configuração', {
      error: error instanceof Error ? error.message : 'Erro desconhecido',
    });

    return NextResponse.json(
      {
        status: 'error',
        message: 'Erro interno no teste detalhado',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

/**
 * Testa configuração do Google OAuth
 */
async function testGoogleOAuth(detailed = false) {
  const test = {
    provider: 'google',
    status: 'fail' as 'pass' | 'fail',
    checks: {} as GoogleOAuthChecks,
    message: '',
  };

  try {
    // Verificar variáveis de ambiente
    const clientId = process.env.AUTH_GOOGLE_CLIENT_ID;
    const clientSecret = process.env.AUTH_GOOGLE_CLIENT_SECRET;

    test.checks.credentials_present = !!(clientId && clientSecret);
    test.checks.client_id_format = clientId?.includes('.apps.googleusercontent.com') || false;
    test.checks.client_secret_length = (clientSecret?.length || 0) >= 20;

    // Validação usando função existente
    test.checks.validation_passed = validateGoogleOAuthConfig();

    if (detailed && clientId && clientSecret) {
      // Teste mais detalhado - verificar se as credenciais são válidas
      try {
        const wellKnownUrl = 'https://accounts.google.com/.well-known/openid_configuration';
        const response = await fetch(wellKnownUrl, {
          method: 'GET',
          headers: { 'User-Agent': 'Excel-Copilot-Auth-Test/1.0' },
        });

        test.checks.google_service_reachable = response.ok;

        if (response.ok) {
          const config = await response.json();
          test.checks.authorization_endpoint = !!config.authorization_endpoint;
          test.checks.token_endpoint = !!config.token_endpoint;
        }
      } catch (error) {
        test.checks.google_service_reachable = false;
        test.checks.service_error = error instanceof Error ? error.message : 'Erro desconhecido';
      }
    }

    // Determinar status final
    const requiredChecks = ['credentials_present', 'client_id_format', 'validation_passed'];
    const allRequiredPass = requiredChecks.every(check => test.checks[check]);

    test.status = allRequiredPass ? 'pass' : 'fail';
    test.message = allRequiredPass
      ? 'Configuração Google OAuth válida'
      : 'Problemas na configuração Google OAuth';
  } catch (error) {
    test.status = 'fail';
    test.message = `Erro ao testar Google OAuth: ${error instanceof Error ? error.message : 'Erro desconhecido'}`;
  }

  return test;
}

/**
 * Testa configuração do GitHub OAuth
 */
async function testGitHubOAuth(detailed = false) {
  const test = {
    provider: 'github',
    status: 'fail' as 'pass' | 'fail',
    checks: {} as GitHubOAuthChecks,
    message: '',
  };

  try {
    // Verificar variáveis de ambiente
    const clientId = process.env.AUTH_GITHUB_CLIENT_ID;
    const clientSecret = process.env.AUTH_GITHUB_CLIENT_SECRET;

    test.checks.credentials_present = !!(clientId && clientSecret);
    test.checks.client_id_length = (clientId?.length || 0) >= 16;
    test.checks.client_secret_length = (clientSecret?.length || 0) >= 30;

    // Validação usando função existente
    test.checks.validation_passed = validateGitHubOAuthConfig();

    if (detailed && clientId) {
      // Teste mais detalhado - verificar se o GitHub está acessível
      try {
        const response = await fetch('https://api.github.com/meta', {
          method: 'GET',
          headers: { 'User-Agent': 'Excel-Copilot-Auth-Test/1.0' },
        });

        test.checks.github_service_reachable = response.ok;
      } catch (error) {
        test.checks.github_service_reachable = false;
        test.checks.service_error = error instanceof Error ? error.message : 'Erro desconhecido';
      }
    }

    // Determinar status final
    const requiredChecks = ['credentials_present', 'client_id_length', 'validation_passed'];
    const allRequiredPass = requiredChecks.every(check => test.checks[check]);

    test.status = allRequiredPass ? 'pass' : 'fail';
    test.message = allRequiredPass
      ? 'Configuração GitHub OAuth válida'
      : 'Problemas na configuração GitHub OAuth';
  } catch (error) {
    test.status = 'fail';
    test.message = `Erro ao testar GitHub OAuth: ${error instanceof Error ? error.message : 'Erro desconhecido'}`;
  }

  return test;
}

/**
 * Testa configuração do NextAuth
 */
function testNextAuthConfig() {
  const test = {
    provider: 'nextauth',
    status: 'fail' as 'pass' | 'fail',
    checks: {} as NextAuthChecks,
    message: '',
  };

  try {
    test.checks.secret_present = !!process.env.AUTH_NEXTAUTH_SECRET;
    test.checks.secret_length = (process.env.AUTH_NEXTAUTH_SECRET?.length || 0) >= 32;
    test.checks.url_present = !!process.env.AUTH_NEXTAUTH_URL;

    if (process.env.AUTH_NEXTAUTH_URL) {
      try {
        new URL(process.env.AUTH_NEXTAUTH_URL);
        test.checks.url_valid = true;
        test.checks.url_https =
          process.env.AUTH_NEXTAUTH_URL.startsWith('https://') || ENV.IS_DEVELOPMENT;
      } catch {
        test.checks.url_valid = false;
        test.checks.url_https = false;
      }
    }

    const requiredChecks = ['secret_present', 'secret_length', 'url_present', 'url_valid'];
    const allRequiredPass = requiredChecks.every(check => test.checks[check]);

    test.status = allRequiredPass ? 'pass' : 'fail';
    test.message = allRequiredPass
      ? 'Configuração NextAuth válida'
      : 'Problemas na configuração NextAuth';
  } catch (error) {
    test.status = 'fail';
    test.message = `Erro ao testar NextAuth: ${error instanceof Error ? error.message : 'Erro desconhecido'}`;
  }

  return test;
}

/**
 * Testa conexão com banco de dados
 */
async function testDatabaseConnection() {
  const test = {
    provider: 'database',
    status: 'fail' as 'pass' | 'fail',
    checks: {} as DatabaseChecks,
    message: '',
  };

  try {
    test.checks.url_present = Boolean(process.env.DB_DATABASE_URL);

    if (process.env.DB_DATABASE_URL) {
      const { PrismaClient } = await import('@prisma/client');
      const prisma = new PrismaClient();

      try {
        await prisma.$connect();
        test.checks.connection_successful = true;

        // Testar uma query simples
        await prisma.user.count();
        test.checks.query_successful = true;

        await prisma.$disconnect();
      } catch (error) {
        test.checks.connection_successful = false;
        test.checks.connection_error = error instanceof Error ? error.message : 'Erro desconhecido';
      }
    }

    const requiredChecks = ['url_present', 'connection_successful'];
    const allRequiredPass = requiredChecks.every(check => test.checks[check]);

    test.status = allRequiredPass ? 'pass' : 'fail';
    test.message = allRequiredPass
      ? 'Conexão com banco de dados válida'
      : 'Problemas na conexão com banco de dados';
  } catch (error) {
    test.status = 'fail';
    test.message = `Erro ao testar banco de dados: ${error instanceof Error ? error.message : 'Erro desconhecido'}`;
  }

  return test;
}
