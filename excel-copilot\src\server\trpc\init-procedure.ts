import { initTRPC, TRPCError } from '@trpc/server';
import superjson from 'superjson';
import { ZodError } from 'zod';

import { logger } from '@/lib/logger';

import serverInitializer from '../init';

// Variável para controlar se o servidor já foi inicializado
let serverInitialized = false;

/**
 * Inicializa o tRPC com configurações padrão e garante que o servidor seja inicializado
 */
export const initTRPCProcedure = initTRPC.context().create({
  transformer: superjson,
  errorFormatter: ({ shape, error }) => {
    return {
      ...shape,
      data: {
        ...shape.data,
        zodError: error.cause instanceof ZodError ? error.cause.flatten() : null,
      },
    };
  },
});

/**
 * Middleware para garantir que o servidor está inicializado
 */
export const ensureServerInitialized = initTRPCProcedure.middleware(async ({ next }) => {
  // Se o servidor já foi inicializado, prosseguir
  if (serverInitialized) {
    return next();
  }

  try {
    // Usar initLogger para silenciar em produção
    const { initLogger } = await import('@/lib/logger');
    initLogger.info('Inicializando servidor a partir do middleware tRPC...');

    // Executar a inicialização do servidor
    const success = await serverInitializer.initialize();

    if (!success) {
      logger.error('Falha na inicialização do servidor');
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Erro na inicialização do servidor',
      });
    }

    // Marcar o servidor como inicializado
    serverInitialized = true;
    initLogger.info('Servidor inicializado com sucesso a partir do middleware tRPC');

    return next();
  } catch (error) {
    logger.error('Erro crítico na inicialização do servidor:', error);
    throw new TRPCError({
      code: 'INTERNAL_SERVER_ERROR',
      message: 'Erro na inicialização do servidor',
    });
  }
});

/**
 * Procedimento tRPC base com inicialização garantida
 */
export const baseProcedure = initTRPCProcedure.procedure.use(ensureServerInitialized);

/**
 * Router tRPC com inicialização garantida
 */
export const router = initTRPCProcedure.router;

/**
 * Interface para o contexto com tipagem para a sessão
 */
interface ContextWithSession {
  session?: {
    user?: {
      id: string;
      name?: string;
      email?: string;
      image?: string;
    };
  };
  [key: string]: unknown;
}

/**
 * Middleware para procedimentos que exigem autenticação
 */
export const enforceUserIsAuthed = initTRPCProcedure.middleware(async ({ ctx, next }) => {
  // Garantir que o servidor esteja inicializado
  if (!serverInitialized) {
    // Ao invés de chamar diretamente, invocar o middleware via procedure
    const _result = await baseProcedure.query(() => {
      return 'Server initialization check';
    })({
      // Fornecer um objeto vazio tipado para o tipo esperado por ProcedureCallOptions
      // que deve conter campos relacionados ao contexto da requisição
      ctx: {},
      path: '',
      type: 'query',
      input: undefined,
      rawInput: undefined,
    });
  }

  // Verificar se o usuário está autenticado
  const typedCtx = ctx as ContextWithSession;
  if (!typedCtx.session?.user) {
    throw new TRPCError({
      code: 'UNAUTHORIZED',
      message: 'Não autorizado',
    });
  }

  return next({
    ctx: {
      ...ctx,
      session: {
        ...typedCtx.session,
        user: typedCtx.session.user,
      },
    },
  });
});

/**
 * Procedimento tRPC que exige autenticação
 */
export const protectedProcedure = initTRPCProcedure.procedure.use(enforceUserIsAuthed);

/**
 * Exportação de utilidades
 */
export const mergeRouters = initTRPCProcedure.mergeRouters;
