import { NextRequest } from 'next/server';
import '../../mocks/msw-setup';

// Mock para workbook-operations handler
const mockOperationHandler = jest.fn().mockImplementation(async req => {
  const body = await req.json();

  if (!body.workbookId) {
    return new Response(JSON.stringify({ error: 'ID do workbook não fornecido' }), {
      status: 400,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  if (!body.operations || !Array.isArray(body.operations) || body.operations.length === 0) {
    return new Response(JSON.stringify({ error: 'Operações inválidas ou não fornecidas' }), {
      status: 400,
      headers: { 'Content-Type': 'application/json' },
    });
  }

  // Simular uma resposta de sucesso
  return new Response(
    JSON.stringify({
      success: true,
      resultSummary: 'Operações executadas com sucesso',
      updatedData: {
        headers: ['Produto', 'Quantidade', 'Valor', 'Total'],
        rows: [
          ['Produto A', 10, 150, 1500],
          ['Produto B', 5, 300, 1500],
          ['Produto C', 8, 120, 960],
          ['Total', 23, 570, 3960],
        ],
      },
    }),
    {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
    }
  );
});

// Mock do handler da rota
jest.mock('@/app/api/workbook/operations/route', () => ({
  POST: mockOperationHandler,
}));

describe('API de Operações de Workbook', () => {
  test('deve executar operações na planilha com sucesso', async () => {
    // Criar uma requisição simulada
    const req = new NextRequest('https://example.com/api/workbook/operations', {
      method: 'POST',
      body: JSON.stringify({
        workbookId: 'test-workbook-id',
        operations: [
          {
            type: 'COLUMN_OPERATION',
            data: {
              column: 'Valor',
              operation: 'SUM',
              description: 'Soma da coluna Valor',
            },
          },
          {
            type: 'ADD_COLUMN',
            data: {
              name: 'Total',
              formula: 'Quantidade * Valor',
              description: 'Adicionar coluna de total',
            },
          },
        ],
      }),
    });

    // Chamar o handler da rota (mockado)
    const response = await mockOperationHandler(req);
    const data = await response.json();

    // Verificar a resposta
    expect(response.status).toBe(200);
    expect(data).toHaveProperty('success', true);
    expect(data).toHaveProperty('resultSummary');
    expect(data).toHaveProperty('updatedData');

    // Verificar os dados atualizados
    expect(data.updatedData.headers).toContain('Total');
    expect(data.updatedData.rows.length).toBeGreaterThan(0);
  });

  test('deve retornar erro quando workbookId não é fornecido', async () => {
    // Criar uma requisição sem workbookId
    const req = new NextRequest('https://example.com/api/workbook/operations', {
      method: 'POST',
      body: JSON.stringify({
        operations: [
          {
            type: 'COLUMN_OPERATION',
            data: {
              column: 'Valor',
              operation: 'SUM',
            },
          },
        ],
      }),
    });

    // Chamar o handler da rota (mockado)
    const response = await mockOperationHandler(req);
    const data = await response.json();

    // Verificar a resposta de erro
    expect(response.status).toBe(400);
    expect(data).toHaveProperty('error');
    expect(data.error).toContain('ID do workbook');
  });

  test('deve retornar erro quando as operações não são fornecidas', async () => {
    // Criar uma requisição sem operações
    const req = new NextRequest('https://example.com/api/workbook/operations', {
      method: 'POST',
      body: JSON.stringify({
        workbookId: 'test-workbook-id',
        operations: [],
      }),
    });

    // Chamar o handler da rota (mockado)
    const response = await mockOperationHandler(req);
    const data = await response.json();

    // Verificar a resposta de erro
    expect(response.status).toBe(400);
    expect(data).toHaveProperty('error');
    expect(data.error).toContain('Operações inválidas');
  });
});
