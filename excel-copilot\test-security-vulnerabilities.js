#!/usr/bin/env node

/**
 * Teste de Vulnerabilidades de Segurança - Sistema de Privilégios
 * Identifica possíveis bypasses e falhas de segurança
 */

import fs from 'fs';
import path from 'path';

console.log('🔍 TESTE DE VULNERABILIDADES DE SEGURANÇA');
console.log('==========================================\n');

const vulnerabilities = [];
const securityIssues = [];

// ============================================================================
// 1. ANÁLISE DE BYPASS DE AUTENTICAÇÃO
// ============================================================================

console.log('🚨 1. ANÁLISE DE BYPASS DE AUTENTICAÇÃO');
console.log('========================================\n');

const apiRoutes = [
  'src/app/api/workbook/save/route.ts',
  'src/app/api/chat/route.ts',
  'src/app/api/workbooks/route.ts',
  'src/app/api/workbooks/shared/route.ts',
];

apiRoutes.forEach(route => {
  if (fs.existsSync(route)) {
    const content = fs.readFileSync(route, 'utf8');
    console.log(`📁 Analisando: ${path.basename(route)}`);

    // Verificar se permite acesso sem autenticação
    if (content.includes("userId = 'guest'") || content.includes('allowGuest')) {
      vulnerabilities.push({
        file: route,
        type: 'GUEST_ACCESS_BYPASS',
        severity: 'CRITICAL',
        description: 'Permite acesso como usuário guest, bypass completo de limitações',
        line: getLineNumber(content, 'guest'),
      });
      console.log('  🚨 CRÍTICO: Acesso guest detectado');
    }

    // Verificar se há verificação de sessão
    if (!content.includes('getServerSession') && !content.includes('session?.user')) {
      vulnerabilities.push({
        file: route,
        type: 'MISSING_AUTH_CHECK',
        severity: 'HIGH',
        description: 'Endpoint sem verificação de autenticação',
        line: 1,
      });
      console.log('  ⚠️  ALTO: Sem verificação de autenticação');
    } else {
      console.log('  ✅ Verificação de autenticação presente');
    }

    // Verificar se há verificação de plano
    if (
      !content.includes('canCreateWorkbook') &&
      !content.includes('canUseAdvancedAI') &&
      !content.includes('getUserSubscriptionPlan')
    ) {
      securityIssues.push({
        file: route,
        type: 'MISSING_PLAN_CHECK',
        severity: 'MEDIUM',
        description: 'Endpoint sem verificação de plano de assinatura',
      });
      console.log('  ⚠️  MÉDIO: Sem verificação de plano');
    } else {
      console.log('  ✅ Verificação de plano presente');
    }

    console.log('');
  }
});

// ============================================================================
// 2. ANÁLISE DE BYPASS DE CACHE
// ============================================================================

console.log('💾 2. ANÁLISE DE BYPASS DE CACHE');
console.log('=================================\n');

const cacheFiles = [
  'src/lib/subscription-limits.ts',
  'src/lib/middleware/plan-based-rate-limiter.ts',
];

cacheFiles.forEach(file => {
  if (fs.existsSync(file)) {
    const content = fs.readFileSync(file, 'utf8');
    console.log(`📁 Analisando cache: ${path.basename(file)}`);

    // Verificar se cache pode ser manipulado
    if (content.includes('userPlanCache.set') && !content.includes('PLAN_VERIFICATION_INTERVAL')) {
      vulnerabilities.push({
        file: file,
        type: 'CACHE_MANIPULATION',
        severity: 'HIGH',
        description: 'Cache de planos sem expiração adequada, possível manipulação',
        line: getLineNumber(content, 'userPlanCache.set'),
      });
      console.log('  🚨 ALTO: Cache sem expiração adequada');
    }

    // Verificar se cache é limpo adequadamente
    if (content.includes('cache') && !content.includes('delete') && !content.includes('clear')) {
      securityIssues.push({
        file: file,
        type: 'CACHE_NOT_CLEARED',
        severity: 'MEDIUM',
        description: 'Cache não é limpo adequadamente, possível vazamento de dados',
      });
      console.log('  ⚠️  MÉDIO: Cache não é limpo adequadamente');
    }

    console.log('');
  }
});

// ============================================================================
// 3. ANÁLISE DE VALIDAÇÃO DE ENTRADA
// ============================================================================

console.log('🔍 3. ANÁLISE DE VALIDAÇÃO DE ENTRADA');
console.log('======================================\n');

apiRoutes.forEach(route => {
  if (fs.existsSync(route)) {
    const content = fs.readFileSync(route, 'utf8');
    console.log(`📁 Validação: ${path.basename(route)}`);

    // Verificar se usa validação com schema
    if (
      content.includes('await req.json()') &&
      !content.includes('zod') &&
      !content.includes('safeParse')
    ) {
      securityIssues.push({
        file: route,
        type: 'WEAK_INPUT_VALIDATION',
        severity: 'MEDIUM',
        description: 'Validação de entrada fraca, sem schema de validação',
      });
      console.log('  ⚠️  MÉDIO: Validação de entrada fraca');
    } else if (content.includes('zod') || content.includes('safeParse')) {
      console.log('  ✅ Validação com schema presente');
    }

    // Verificar injeção SQL potencial
    if (content.includes('prisma.$queryRaw') && content.includes('${')) {
      vulnerabilities.push({
        file: route,
        type: 'SQL_INJECTION_RISK',
        severity: 'CRITICAL',
        description: 'Possível injeção SQL com interpolação de string',
        line: getLineNumber(content, 'prisma.$queryRaw'),
      });
      console.log('  🚨 CRÍTICO: Risco de injeção SQL');
    }

    console.log('');
  }
});

// ============================================================================
// 4. ANÁLISE DE RATE LIMITING
// ============================================================================

console.log('⚡ 4. ANÁLISE DE RATE LIMITING');
console.log('==============================\n');

const middlewareFile = 'src/middleware.ts';
if (fs.existsSync(middlewareFile)) {
  const content = fs.readFileSync(middlewareFile, 'utf8');
  console.log('📁 Analisando middleware principal');

  // Verificar se rate limiting está aplicado
  if (!content.includes('planBasedRateLimiter') && !content.includes('rateLimit')) {
    vulnerabilities.push({
      file: middlewareFile,
      type: 'MISSING_RATE_LIMITING',
      severity: 'HIGH',
      description: 'Middleware principal sem rate limiting',
    });
    console.log('  🚨 ALTO: Sem rate limiting no middleware');
  } else {
    console.log('  ✅ Rate limiting presente no middleware');
  }
} else {
  console.log('❌ Middleware principal não encontrado');
}

// ============================================================================
// 5. ANÁLISE DE EXPOSIÇÃO DE DADOS SENSÍVEIS
// ============================================================================

console.log('\n🔐 5. ANÁLISE DE EXPOSIÇÃO DE DADOS SENSÍVEIS');
console.log('==============================================\n');

const configFiles = ['.env.example', 'src/config/unified-environment.ts', 'src/lib/stripe.ts'];

configFiles.forEach(file => {
  if (fs.existsSync(file)) {
    const content = fs.readFileSync(file, 'utf8');
    console.log(`📁 Analisando: ${path.basename(file)}`);

    // Verificar se há credenciais hardcoded
    const sensitivePatterns = [
      /sk_live_[a-zA-Z0-9]+/g, // Stripe live keys
      /pk_live_[a-zA-Z0-9]+/g, // Stripe public keys
      /AIza[a-zA-Z0-9_-]{35}/g, // Google API keys
      /[a-zA-Z0-9]{32,}/g, // Possíveis tokens
    ];

    sensitivePatterns.forEach((pattern, index) => {
      const matches = content.match(pattern);
      if (matches && !file.includes('.example')) {
        vulnerabilities.push({
          file: file,
          type: 'HARDCODED_CREDENTIALS',
          severity: 'CRITICAL',
          description: `Credenciais hardcoded detectadas: ${matches[0].substring(0, 10)}...`,
        });
        console.log(`  🚨 CRÍTICO: Credenciais hardcoded detectadas`);
      }
    });

    console.log('');
  }
});

// ============================================================================
// FUNÇÃO AUXILIAR
// ============================================================================

function getLineNumber(content, searchString) {
  const lines = content.split('\n');
  for (let i = 0; i < lines.length; i++) {
    if (lines[i].includes(searchString)) {
      return i + 1;
    }
  }
  return 1;
}

// ============================================================================
// RELATÓRIO FINAL
// ============================================================================

console.log('📊 RELATÓRIO DE VULNERABILIDADES');
console.log('=================================\n');

console.log(
  `🚨 Vulnerabilidades Críticas: ${vulnerabilities.filter(v => v.severity === 'CRITICAL').length}`
);
console.log(
  `⚠️  Vulnerabilidades Altas: ${vulnerabilities.filter(v => v.severity === 'HIGH').length}`
);
console.log(`⚠️  Problemas Médios: ${securityIssues.filter(s => s.severity === 'MEDIUM').length}`);

if (vulnerabilities.length > 0) {
  console.log('\n🚨 VULNERABILIDADES CRÍTICAS E ALTAS:');
  vulnerabilities.forEach((vuln, index) => {
    console.log(`\n${index + 1}. ${path.basename(vuln.file)} (Linha ${vuln.line || 'N/A'})`);
    console.log(`   Tipo: ${vuln.type}`);
    console.log(`   Severidade: ${vuln.severity}`);
    console.log(`   Descrição: ${vuln.description}`);
  });
}

if (securityIssues.length > 0) {
  console.log('\n⚠️  PROBLEMAS DE SEGURANÇA MÉDIOS:');
  securityIssues.forEach((issue, index) => {
    console.log(`\n${index + 1}. ${path.basename(issue.file)}`);
    console.log(`   Tipo: ${issue.type}`);
    console.log(`   Descrição: ${issue.description}`);
  });
}

// Calcular score de segurança
const totalIssues = vulnerabilities.length + securityIssues.length;
const criticalIssues = vulnerabilities.filter(v => v.severity === 'CRITICAL').length;
const highIssues = vulnerabilities.filter(v => v.severity === 'HIGH').length;

let securityScore = 100;
securityScore -= criticalIssues * 30; // -30 pontos por vulnerabilidade crítica
securityScore -= highIssues * 20; // -20 pontos por vulnerabilidade alta
securityScore -= securityIssues.length * 10; // -10 pontos por problema médio

securityScore = Math.max(0, securityScore);

console.log(`\n📈 SCORE DE SEGURANÇA: ${securityScore}/100`);

if (securityScore >= 90) {
  console.log('🟢 NÍVEL DE SEGURANÇA: EXCELENTE');
} else if (securityScore >= 70) {
  console.log('🟡 NÍVEL DE SEGURANÇA: BOM');
} else if (securityScore >= 50) {
  console.log('🟠 NÍVEL DE SEGURANÇA: MÉDIO');
} else {
  console.log('🔴 NÍVEL DE SEGURANÇA: BAIXO');
}

console.log('\n✅ ANÁLISE DE VULNERABILIDADES CONCLUÍDA');
