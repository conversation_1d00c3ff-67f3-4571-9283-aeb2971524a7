/**
 * Estilos base consolidados para componentes de formulário
 * Este arquivo resolve duplicações entre Input, Textarea e outros componentes de formulário
 */

import React from 'react';

import { cn } from '@/lib/utils';

/**
 * Estilos base compartilhados entre todos os componentes de formulário
 */
export const FORM_FIELD_BASE_STYLES =
  'flex w-full rounded-md border bg-background px-3 py-2 text-sm ring-offset-background ' +
  'file:border-0 file:bg-transparent file:text-sm file:font-medium ' +
  'placeholder:text-muted-foreground ' +
  'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 ' +
  'disabled:cursor-not-allowed disabled:opacity-50';

/**
 * Variantes de estilo para componentes de formulário
 */
export const FORM_FIELD_VARIANTS = {
  default: 'border-input',
  outline: 'border-border bg-transparent',
  ghost: 'border-transparent bg-transparent',
  error: 'border-destructive focus-visible:ring-destructive',
} as const;

/**
 * Tamanhos para componentes de formulário
 */
export const FORM_FIELD_SIZES = {
  sm: 'h-8 text-xs',
  md: 'h-10 text-sm',
  lg: 'h-12 text-base',
} as const;

/**
 * Estilos específicos para textarea
 */
export const TEXTAREA_SPECIFIC_STYLES = 'min-h-[80px] resize-vertical';

/**
 * Tipos para as variantes e tamanhos
 */
export type FormFieldVariant = keyof typeof FORM_FIELD_VARIANTS;
export type FormFieldSize = keyof typeof FORM_FIELD_SIZES;

/**
 * Função utilitária para gerar classes de componentes de formulário
 */
export function getFormFieldClasses(
  variant: FormFieldVariant = 'default',
  size: FormFieldSize = 'md',
  isTextarea: boolean = false,
  additionalClasses?: string
): string {
  return cn(
    FORM_FIELD_BASE_STYLES,
    FORM_FIELD_VARIANTS[variant],
    FORM_FIELD_SIZES[size],
    isTextarea && TEXTAREA_SPECIFIC_STYLES,
    additionalClasses
  );
}

/**
 * Props base para componentes de formulário com wrapper
 */
export interface FormFieldWrapperProps {
  wrapperClassName?: string;
  variant?: FormFieldVariant;
  fieldSize?: FormFieldSize;
}

/**
 * Função para renderizar wrapper condicional
 */
export function renderWithWrapper<T extends React.ReactElement>(
  element: T,
  wrapperClassName?: string
): React.ReactElement {
  if (wrapperClassName) {
    return React.createElement('div', { className: wrapperClassName }, element);
  }
  return element;
}
