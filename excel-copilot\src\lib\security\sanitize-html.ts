/**
 * Módulo específico para sanitização de HTML
 * Separado do módulo geral para melhor organização
 */

import DOMPurify from 'dompurify';

/**
 * Propriedades aceitas para sanitização HTML
 * Baseado na interface Config do DOMPurify
 */
export interface SanitizeOptions {
  /** Permitir elementos específicos */
  ALLOWED_TAGS?: string[];
  /** Permitir atributos específicos */
  ALLOWED_ATTR?: string[];
  /** Preservar as estruturas de dados de entrada */
  KEEP_CONTENT?: boolean;
  /** Atributos proibidos como array de strings */
  FORBID_ATTR?: string[];
  /** Tags proibidas como array de strings */
  FORBID_TAGS?: string[];
  /** Remover todos os atributos de eventos */
  FORBID_EVENTS?: boolean;
  /** Outras opções do DOMPurify */
  [key: string]: boolean | string[] | undefined;
}

/**
 * Configuração padrão segura para sanitização
 */
const DEFAULT_CONFIG: SanitizeOptions = {
  ALLOWED_TAGS: [
    'b',
    'i',
    'u',
    'em',
    'strong',
    'a',
    'p',
    'br',
    'ul',
    'ol',
    'li',
    'h1',
    'h2',
    'h3',
    'h4',
    'h5',
    'h6',
    'hr',
    'pre',
    'code',
    'blockquote',
    'div',
    'span',
    'table',
    'thead',
    'tbody',
    'tr',
    'th',
    'td',
  ],
  ALLOWED_ATTR: ['href', 'target', 'rel', 'style', 'class', 'id'],
  FORBID_EVENTS: true,
  FORBID_TAGS: ['script', 'iframe', 'object', 'embed', 'form', 'input', 'button'],
  FORBID_ATTR: ['onerror', 'onload', 'onclick', 'onmouseover'],
};

/**
 * Sanitiza conteúdo HTML para prevenir XSS
 *
 * @param html Conteúdo HTML a ser sanitizado
 * @param options Opções adicionais de configuração
 * @returns HTML sanitizado
 */
export function sanitizeHtml(html: string, options?: SanitizeOptions): string {
  // Merge das opções com o config padrão (opções fornecidas têm precedência)
  const config = { ...DEFAULT_CONFIG, ...options };

  // Se o DOMPurify não estiver disponível no ambiente (SSR)
  if (typeof DOMPurify === 'undefined') {
    console.warn('DOMPurify não está disponível neste ambiente. Fallback para escape básico.');
    return escapeHtml(html);
  }

  return DOMPurify.sanitize(html, config);
}

/**
 * Fallback simples para escape de HTML quando DOMPurify não está disponível
 */
function escapeHtml(unsafe: string): string {
  return unsafe
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#039;');
}

/**
 * Remove tags HTML de uma string
 * @param input String para limpar
 * @returns String sem tags HTML
 */
export function stripHtml(input: string): string {
  if (!input || typeof input !== 'string') return '';

  // Remover todas as tags HTML
  return input.replace(/<[^>]*>/g, '');
}

// Also export as default for flexibility
export default sanitizeHtml;
