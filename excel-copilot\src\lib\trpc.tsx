'use client';

import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { httpBatchLink } from '@trpc/client';
import { createTRPCReact } from '@trpc/react-query';
import { useMemo, ReactNode } from 'react';
import SuperJSON from 'superjson';

import type { AppRouter } from '@/server/trpc/router';

/**
 * Criação do cliente tRPC fortemente tipado, sem uso de 'any'
 *
 * A versão v10 do tRPC é compatível com React Query v4.
 * Para migrar para React Query v5, seria necessário atualizar para tRPC v11.
 *
 * @see https://github.com/TanStack/query/issues/6186
 */
export const trpc = createTRPCReact<AppRouter>();

// Constantes definindo valores de configuração
const DEFAULT_STALE_TIME = 5 * 60 * 1000; // 5 minutos
const MAX_RETRIES = 1;

/**
 * Provedor tRPC que inicializa o cliente e configurações
 * para ser usado em toda a aplicação
 */
export function TRPCProvider({ children }: { children: ReactNode }) {
  // Criar QueryClient com configurações otimizadas
  const queryClient = useMemo(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            refetchOnWindowFocus: false,
            staleTime: DEFAULT_STALE_TIME,
            retry: MAX_RETRIES,
            // Adiciona cacheTime para evitar re-requisições desnecessárias
            cacheTime: DEFAULT_STALE_TIME * 2,
          },
        },
      }),
    []
  );

  // Criar cliente TRPC
  const trpcClient = useMemo(() => {
    // Determinar URL base segura
    const getBaseUrl = () => {
      // URL específica do ambiente
      if (process.env.NEXT_PUBLIC_APP_URL) {
        return process.env.NEXT_PUBLIC_APP_URL;
      }

      // Browser - usar URL atual
      if (typeof window !== 'undefined') {
        return window.location.origin;
      }

      // Fallback para desenvolvimento local
      return 'http://localhost:3000';
    };

    const baseUrl = getBaseUrl();

    // Criação do cliente tRPC com tipagens corretas
    return trpc.createClient({
      links: [
        httpBatchLink({
          url: `${baseUrl}/api/trpc`,
          // Headers comuns para todas as requisições
          headers() {
            return {
              'x-trpc-source': 'react',
            };
          },
        }),
      ],
      transformer: SuperJSON,
    });
  }, []);

  return (
    <trpc.Provider client={trpcClient} queryClient={queryClient}>
      <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
    </trpc.Provider>
  );
}
