#!/usr/bin/env ts-node
/**
 * Script para executar todos os testes do Excel Copilot em sequência
 *
 * Execução: ts-node scripts/run-all-tests.ts
 */

import { execSync } from 'child_process';
import { appendFileSync, existsSync, mkdirSync } from 'fs';
import { join } from 'path';

// Configurações
const LOG_DIR = join(__dirname, '../logs');
const LOG_FILE = join(LOG_DIR, `test-run-${new Date().toISOString().replace(/[:.]/g, '-')}.log`);

// Criar diretório de logs se não existir
if (!existsSync(LOG_DIR)) {
  mkdirSync(LOG_DIR, { recursive: true });
}

// Função para registrar no log
function log(message: string) {
  const timestamp = new Date().toISOString();
  const entry = `[${timestamp}] ${message}\n`;

  // Registrar no console
  console.log(entry);

  // Registrar no arquivo
  appendFileSync(LOG_FILE, entry);
}

// Função para executar um script e registrar a saída
function runScript(scriptPath: string, description: string) {
  log(`\n---------------------------------------------------`);
  log(`INICIANDO: ${description}`);
  log(`---------------------------------------------------`);

  try {
    // Executar o script e capturar saída
    const output = execSync(`ts-node ${scriptPath}`, { encoding: 'utf8' });
    log(output);
    log(`✅ ${description} concluído com sucesso`);
    return true;
  } catch (error) {
    log(`❌ ERRO ao executar ${description}:`);
    log(error instanceof Error ? error.message : String(error));

    if (error instanceof Error && 'stdout' in error) {
      log((error as any).stdout || '');
    }

    if (error instanceof Error && 'stderr' in error) {
      log((error as any).stderr || '');
    }

    return false;
  }
}

// Função principal
async function main() {
  log('=== INICIANDO SEQUÊNCIA DE TESTES DO EXCEL COPILOT ===\n');

  // Array com os testes a serem executados
  const tests = [
    { path: './scripts/test-ai-flow.ts', description: 'Teste de fluxo de IA' },
    {
      path: './scripts/test-persistence.ts',
      description: 'Teste de persistência no banco de dados',
    },
    { path: './scripts/test-excel-export.ts', description: 'Teste de exportação para Excel' },
  ];

  // Executar testes em sequência
  let successCount = 0;
  let failureCount = 0;

  for (const [index, test] of tests.entries()) {
    log(`\nExecutando teste ${index + 1}/${tests.length}: ${test.description}`);

    const success = runScript(test.path, test.description);
    if (success) {
      successCount++;
    } else {
      failureCount++;
    }
  }

  // Resumo final
  log('\n===================================================');
  log('RESUMO DOS TESTES:');
  log(`Total de testes: ${tests.length}`);
  log(`Testes bem-sucedidos: ${successCount}`);
  log(`Testes com falha: ${failureCount}`);
  log('===================================================');

  if (failureCount === 0) {
    log('\n🎉 TODOS OS TESTES FORAM CONCLUÍDOS COM SUCESSO! 🎉');
  } else {
    log(`\n⚠️ ${failureCount} TESTE(S) FALHOU(ARAM). Verifique o log para mais detalhes. ⚠️`);
  }

  log(`\nLog completo disponível em: ${LOG_FILE}`);
}

// Executar função principal
main().catch(error => {
  log(`\n❌ ERRO FATAL: ${error instanceof Error ? error.message : String(error)}`);
  process.exit(1);
});
