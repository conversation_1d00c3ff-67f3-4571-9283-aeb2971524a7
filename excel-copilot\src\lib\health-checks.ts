/**
 * 🏥 SISTEMA DE HEALTH CHECKS - EXCEL COPILOT
 *
 * Sistema robusto para monitorar a saúde de todos os serviços críticos
 * da aplicação, incluindo banco de dados, autenticação, IA, pagamentos
 * e integrações MCP.
 *
 * <AUTHOR> Copilot Team
 * @version 1.0.0
 */

// import { unifiedEnv } from '@/config/unified-environment';

// ============================================================================
// TIPOS E INTERFACES
// ============================================================================

/**
 * Status possíveis para um health check
 */
export type HealthStatus = 'healthy' | 'unhealthy' | 'degraded' | 'unknown';

/**
 * Severidade do problema detectado
 */
export type HealthSeverity = 'low' | 'medium' | 'high' | 'critical';

/**
 * Resultado de um health check individual
 */
export interface HealthCheckResult {
  service: string;
  status: HealthStatus;
  responseTime: number;
  timestamp: string;
  details?: {
    message?: string;
    error?: string;
    metadata?: Record<string, string | number | boolean | undefined>;
  };
  severity?: HealthSeverity;
}

/**
 * Resultado agregado de múltiplos health checks
 */
export interface HealthReport {
  overall: HealthStatus;
  timestamp: string;
  responseTime: number;
  services: HealthCheckResult[];
  summary: {
    healthy: number;
    unhealthy: number;
    degraded: number;
    total: number;
  };
}

/**
 * Configuração para um health check
 */
export interface HealthCheckConfig {
  timeout: number;
  retries: number;
  interval: number;
  enabled: boolean;
}

// ============================================================================
// CONFIGURAÇÕES
// ============================================================================

/**
 * Configurações padrão para health checks
 */
export const HEALTH_CHECK_CONFIGS: Record<string, HealthCheckConfig> = {
  database: {
    timeout: 5000,
    retries: 2,
    interval: 30000,
    enabled: true,
  },
  auth: {
    timeout: 3000,
    retries: 1,
    interval: 60000,
    enabled: true,
  },
  ai: {
    timeout: 10000,
    retries: 1,
    interval: 120000,
    enabled: true,
  },
  stripe: {
    timeout: 5000,
    retries: 2,
    interval: 60000,
    enabled: true,
  },
  mcp: {
    timeout: 8000,
    retries: 1,
    interval: 300000, // 5 minutos
    enabled: true,
  },
};

/**
 * Timeouts específicos por operação (em ms)
 */
export const TIMEOUTS = {
  DATABASE_QUERY: 5000,
  AUTH_VALIDATION: 3000,
  AI_REQUEST: 10000,
  STRIPE_API: 5000,
  MCP_REQUEST: 8000,
  OVERALL_CHECK: 30000,
} as const;

// ============================================================================
// UTILITÁRIOS
// ============================================================================

/**
 * Executa uma função com timeout
 */
export async function withTimeout<T>(
  promise: Promise<T>,
  timeoutMs: number,
  errorMessage = 'Operation timed out'
): Promise<T> {
  const timeoutPromise = new Promise<never>((_, reject) => {
    setTimeout(() => reject(new Error(errorMessage)), timeoutMs);
  });

  return Promise.race([promise, timeoutPromise]);
}

/**
 * Mede o tempo de execução de uma função
 */
export async function measureTime<T>(fn: () => Promise<T>): Promise<{ result: T; time: number }> {
  const start = Date.now();
  try {
    const result = await fn();
    const time = Date.now() - start;
    return { result, time };
  } catch (error) {
    const time = Date.now() - start;
    throw { error, time };
  }
}

/**
 * Determina a severidade baseada no status e serviço
 */
export function determineSeverity(service: string, status: HealthStatus): HealthSeverity {
  if (status === 'healthy') return 'low';

  const criticalServices = ['database', 'auth'];
  const highPriorityServices = ['stripe', 'ai'];

  if (criticalServices.includes(service)) {
    return status === 'unhealthy' ? 'critical' : 'high';
  }

  if (highPriorityServices.includes(service)) {
    return status === 'unhealthy' ? 'high' : 'medium';
  }

  return status === 'unhealthy' ? 'medium' : 'low';
}

/**
 * Cria um resultado de health check
 */
export function createHealthResult(
  service: string,
  status: HealthStatus,
  responseTime: number,
  details?: HealthCheckResult['details']
): HealthCheckResult {
  const result: HealthCheckResult = {
    service,
    status,
    responseTime,
    timestamp: new Date().toISOString(),
    severity: determineSeverity(service, status),
  };

  if (details !== undefined) {
    result.details = details;
  }

  return result;
}

/**
 * Determina o status geral baseado nos resultados individuais
 */
export function determineOverallStatus(results: HealthCheckResult[]): HealthStatus {
  if (results.length === 0) return 'unknown';

  const statuses = results.map(r => r.status);

  // Se algum serviço crítico está unhealthy, o sistema está unhealthy
  const criticalServices = ['database', 'auth'];
  const criticalResults = results.filter(r => criticalServices.includes(r.service));
  const hasCriticalFailure = criticalResults.some(r => r.status === 'unhealthy');

  if (hasCriticalFailure) return 'unhealthy';

  // Se há serviços degraded ou unhealthy, o sistema está degraded
  if (statuses.some(s => s === 'degraded' || s === 'unhealthy')) {
    return 'degraded';
  }

  // Se todos estão healthy, o sistema está healthy
  if (statuses.every(s => s === 'healthy')) return 'healthy';

  return 'unknown';
}

/**
 * Cria um relatório de saúde agregado
 */
export function createHealthReport(results: HealthCheckResult[], totalTime: number): HealthReport {
  const summary = {
    healthy: results.filter(r => r.status === 'healthy').length,
    unhealthy: results.filter(r => r.status === 'unhealthy').length,
    degraded: results.filter(r => r.status === 'degraded').length,
    total: results.length,
  };

  return {
    overall: determineOverallStatus(results),
    timestamp: new Date().toISOString(),
    responseTime: totalTime,
    services: results,
    summary,
  };
}

// ============================================================================
// HEALTH CHECK BASE CLASS
// ============================================================================

/**
 * Classe base para implementar health checks
 */
export abstract class BaseHealthCheck {
  protected config: HealthCheckConfig;
  protected serviceName: string;

  constructor(serviceName: string, config?: Partial<HealthCheckConfig>) {
    this.serviceName = serviceName;
    this.config = {
      ...(HEALTH_CHECK_CONFIGS[serviceName] || {}),
      ...config,
    } as HealthCheckConfig;
  }

  /**
   * Executa o health check com retry e timeout
   */
  async execute(): Promise<HealthCheckResult> {
    if (!this.config.enabled) {
      return createHealthResult(this.serviceName, 'unknown', 0, {
        message: 'Health check disabled',
      });
    }

    let lastError: Error | null = null;

    for (let attempt = 0; attempt <= this.config.retries; attempt++) {
      try {
        const { result, time } = await measureTime(() =>
          withTimeout(
            this.check(),
            this.config.timeout,
            `${this.serviceName} health check timed out`
          )
        );

        return createHealthResult(this.serviceName, result.status, time, result.details);
      } catch (error: unknown) {
        // Extrair erro da estrutura { error, time } ou usar o erro diretamente
        const actualError =
          error && typeof error === 'object' && 'error' in error
            ? (error as { error: Error }).error
            : error;

        lastError = actualError instanceof Error ? actualError : new Error('Unknown error');

        if (attempt < this.config.retries) {
          // Aguardar antes de tentar novamente
          await new Promise(resolve => setTimeout(resolve, 1000 * (attempt + 1)));
        }
      }
    }

    // Todas as tentativas falharam
    const time = 0; // Tempo não disponível após falhas
    return createHealthResult(this.serviceName, 'unhealthy', time, {
      error: lastError?.message || 'Unknown error',
      message: `Failed after ${this.config.retries + 1} attempts`,
    });
  }

  /**
   * Implementação específica do health check
   */
  protected abstract check(): Promise<{
    status: HealthStatus;
    details?: Record<string, string | number | boolean | undefined>;
  }>;
}

// ============================================================================
// LOGGER PARA HEALTH CHECKS
// ============================================================================

/**
 * Logger específico para health checks
 */
export class HealthCheckLogger {
  private static instance: HealthCheckLogger;

  static getInstance(): HealthCheckLogger {
    if (!HealthCheckLogger.instance) {
      HealthCheckLogger.instance = new HealthCheckLogger();
    }
    return HealthCheckLogger.instance;
  }

  log(level: 'info' | 'warn' | 'error', message: string, data?: Record<string, unknown>) {
    const timestamp = new Date().toISOString();
    const logEntry = {
      timestamp,
      level,
      service: 'health-check',
      message,
      data,
    };

    if (typeof window === 'undefined') {
      // Server-side logging
      // eslint-disable-next-line no-console
      console.log(JSON.stringify(logEntry));
    }
  }

  info(message: string, data?: Record<string, unknown>) {
    this.log('info', message, data);
  }

  warn(message: string, data?: Record<string, unknown>) {
    this.log('warn', message, data);
  }

  error(message: string, data?: Record<string, unknown>) {
    this.log('error', message, data);
  }
}

export const healthLogger = HealthCheckLogger.getInstance();
