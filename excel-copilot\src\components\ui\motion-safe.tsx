'use client';

import { ReactNode } from 'react';

import { useReducedMotion } from '@/hooks/useReducedMotion';
import { cn } from '@/lib/utils';

interface MotionSafeProps {
  /** Conteúdo que deve ser renderizado com animações */
  children: ReactNode;
  /** Conteúdo alternativo a ser renderizado para usuários que preferem movimento reduzido */
  fallback?: ReactNode;
  /** Classes CSS opcionais */
  className?: string;
}

/**
 * Componente que renderiza seu conteúdo com animações apenas para usuários
 * que não têm preferência por movimento reduzido.
 *
 * Para usuários com preferência por movimento reduzido, renderiza o conteúdo
 * de fallback se fornecido, ou o children sem animações.
 */
export function MotionSafe({ children, fallback, className }: MotionSafeProps) {
  const prefersReducedMotion = useReducedMotion();

  // Se o usuário prefere movimento reduzido e temos um fallback, usamos o fallback
  if (prefersReducedMotion && fallback) {
    return <div className={cn(className)}>{fallback}</div>;
  }

  // Caso contrário, renderizamos o children original
  // (que terá animações se o usuário não preferir movimento reduzido)
  return <div className={cn(className)}>{children}</div>;
}
