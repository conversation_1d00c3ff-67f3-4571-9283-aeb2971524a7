/**
 * Definições de tipos para operações seguras com expressões regulares
 */

// Tipos básicos para expressões regulares
export type RegexPattern = string | RegExp;

/**
 * Função para correspondência segura de regex que retorna null em caso de falha
 * @param text O texto a ser pesquisado
 * @param pattern O padrão de regex a ser usado
 * @returns Array de resultados de correspondência ou null se não houver correspondência
 */
export function safeRegexMatch(
  text: string | undefined | null,
  pattern: RegexPattern
): RegExpMatchArray | null;

/**
 * Extrai um grupo capturado de uma correspondência de regex com segurança
 * @param match O resultado da correspondência de regex
 * @param groupIndex O índice do grupo a ser extraído
 * @param defaultValue Valor padrão a ser retornado se o grupo não existir
 * @returns O valor do grupo ou o valor padrão
 */
export function extractGroup(
  match: RegExpMatchArray | null,
  groupIndex: number,
  defaultValue?: string
): string;

/**
 * Divide uma string usando um padrão regex de forma segura
 * @param text O texto a ser dividido
 * @param separator O padrão regex para divisão
 * @returns Array de substrings ou array vazio em caso de falha
 */
export function safeSplit(text: string | undefined | null, separator: RegexPattern): string[];

/**
 * Substitui ocorrências de um padrão em uma string de forma segura
 * @param text O texto original
 * @param pattern O padrão a ser substituído
 * @param replacement A string de substituição
 * @returns A string com substituições ou a string original em caso de falha
 */
export function safeReplace(
  text: string | undefined | null,
  pattern: RegexPattern,
  replacement: string
): string;

/**
 * Testa de forma segura se um padrão corresponde a uma string
 * @param text O texto a ser testado
 * @param pattern O padrão regex para testar
 * @returns true se houver correspondência, false caso contrário
 */
export function safeTest(text: string | undefined | null, pattern: RegexPattern): boolean;
