/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  images: {
    domains: [
      'lh3.googleusercontent.com', // Para imagens de usuários do Google
      'github.com', // Para avatares do GitHub
      'avatars.githubusercontent.com', // Para avatares do GitHub
      'avatar.vercel.sh',
    ],
    // Permitir arquivos SVG como imagens seguras
    dangerouslyAllowSVG: true,
    contentDispositionType: 'attachment',
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
  },
  // Configuração experimental para desempenho
  experimental: {
    // Otimização para carregamento lento
    optimizeCss: true,
    // Restauração de scroll
    scrollRestoration: true,
    // Melhorias para estabilidade
    esmExternals: 'loose',
    // CORREÇÃO: Configuração para RSC no Vercel
    serverComponentsExternalPackages: ['@google-cloud/vertexai', 'googleapis'],
    // Melhorias para a fase de construção
    turbotrace: {
      logLevel: 'error',
      memoryLimit: 4000,
    },
  },,
  // Configuração específica para o compilador
  compiler: {
    // Remover console.log em produção
    removeConsole:
      process.env.NODE_ENV === 'production'
        ? {
            exclude: ['error', 'warn'],
          }
        : false,
  },
  // Ignorar ESLint em build
  eslint: {
    // Ignorar erros de ESLint durante builds para permitir deploys mesmo com warnings
    ignoreDuringBuilds: true,
  },
  typescript: {
    // Ignorar erros de TypeScript durante builds para permitir deploys mesmo com erros
    ignoreBuildErrors: true,
  },
  // Compressão de arquivos estáticos
  compress: true,
  // Configuração para rotas que usam headers e não podem ser estáticas
  excludeDefaultMomentLocales: true,
  // Marcar rotas específicas como dinâmicas para evitar erros de build
  serverRuntimeConfig: {
    dynamicRoutes: ['/api/api-docs', '/api/metrics', '/api/ws', '/api/socket'],
  },
  // Configurações de segurança para headers HTTP
  async headers() {
    return [
      {
        // Aplicar headers em todas as rotas
        source: '/:path*',
        headers: [
          // Headers de segurança padrão
          {
            key: 'X-DNS-Prefetch-Control',
            value: 'on',
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block',
          },
          {
            key: 'X-Frame-Options',
            value: 'SAMEORIGIN',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin',
          },
          // Content Security Policy básica
          {
            key: 'Content-Security-Policy',
            value:
              "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://apis.google.com https://js.stripe.com https://accounts.google.com https://github.com; script-src-elem 'self' 'unsafe-inline' https://js.stripe.com https://cdn.jsdelivr.net https://apis.google.com https://accounts.google.com https://github.com; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://fonts.googleapis.com fonts.gstatic.com; img-src 'self' data: https: blob:; font-src 'self' https://cdn.jsdelivr.net https://fonts.googleapis.com https://fonts.gstatic.com data:; connect-src 'self' https://api.openai.com https://api.stripe.com https://*.vercel.app https://*.googleapis.com https://*.google.com https://github.com https://accounts.google.com fonts.googleapis.com fonts.gstatic.com; frame-src 'self' https://js.stripe.com https://accounts.google.com https://github.com; object-src 'none'; base-uri 'self'; form-action 'self' https://accounts.google.com https://github.com; frame-ancestors 'self'; upgrade-insecure-requests;",
          },
          // Permissões modernas com Permissions-Policy
          {
            key: 'Permissions-Policy',
            value: 'camera=(), microphone=(), geolocation=(), interest-cohort=()',
          },
        ],
      },
    ];
  },
  // Configuração de webpack para suporte a SVG e lidar com módulos Node.js
  webpack: (config, { isServer, webpack }) => {
    // Melhorar configuração de cache
    config.cache = {
      type: 'filesystem',
      buildDependencies: {
        config: [__filename],
      },
    };

    // Verificar se devemos forçar o uso de mocks para o Google
    // CORREÇÃO: Não forçar mocks em produção, mesmo no Vercel
    const forceGoogleMocks =
      process.env.NODE_ENV !== 'production' &&
      (process.env.FORCE_GOOGLE_MOCKS === 'true' ||
        process.env.NEXT_PUBLIC_DISABLE_VERTEX_AI === 'true');

    // Se forçando mocks, adicionar resolvedores para os módulos do Google
    if (forceGoogleMocks) {
      // Caminho para os mocks
      const path = require('path');
      const mockModulesPath = path.resolve(__dirname, 'src/lib/ai/mock-modules.js');

      // Criar um módulo de mock simplificado
      const fs = require('fs');

      // Verificar se o diretório existe
      const mockDir = path.resolve(__dirname, 'src/lib/ai');
      if (!fs.existsSync(mockDir)) {
        fs.mkdirSync(mockDir, { recursive: true });
      }

      // Criar um módulo mock simplificado se não existir
      if (!fs.existsSync(mockModulesPath)) {
        const mockContent = `
// Mock módulo para substituir as APIs do Google
module.exports = {
  VertexAI: class MockVertexAI {
    constructor() {}
    getGenerativeModel() {
      return {
        generateContent: async () => ({ response: { text: () => "Este é um texto gerado pelo mock." }}),
        streamGenerateContent: async () => ({ stream: [] })
      };
    }
  },
  GoogleGenAI: class MockGoogleGenAI {
    constructor() {}
    getGenerativeModel() {
      return {
        generateContent: async () => ({ response: { text: () => "Este é um texto gerado pelo mock." }}),
        streamGenerateContent: async () => ({ stream: [] })
      };
    }
  },
  HarmCategory: {},
  HarmBlockThreshold: {}
};`;
        fs.writeFileSync(mockModulesPath, mockContent);
      }

      // Adicionar aliases para os módulos do Google
      config.resolve.alias = {
        ...config.resolve.alias,
        '@google-cloud/vertexai': mockModulesPath,
        '@google-cloud/aiplatform': mockModulesPath,
        '@google/genai': mockModulesPath,
      };

      // Adicionar regra de substituição de módulo
      config.plugins.push(
        new webpack.NormalModuleReplacementPlugin(/^@google-cloud\/vertexai$/, resource => {
          resource.request = mockModulesPath;
        }),
        new webpack.NormalModuleReplacementPlugin(/^@google-cloud\/aiplatform$/, resource => {
          resource.request = mockModulesPath;
        }),
        new webpack.NormalModuleReplacementPlugin(/^@google\/genai$/, resource => {
          resource.request = mockModulesPath;
        })
      );
    }

    // Definir que estes pacotes são externos apenas no servidor
    if (isServer) {
      config.externals = [
        ...(Array.isArray(config.externals)
          ? config.externals
          : config.externals
            ? [config.externals]
            : []),
        {
          '@google-cloud/aiplatform': 'commonjs @google-cloud/aiplatform',
          '@google-cloud/vertexai': 'commonjs @google-cloud/vertexai',
          '@google/genai': 'commonjs @google/genai',
          'google-gax': 'commonjs google-gax',
        },
      ];
    }

    // Apenas para cliente, não servidor
    if (!isServer) {
      // Fornecer polyfills para os módulos node: que causam problemas
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
        dns: false,
        child_process: false,
        http2: false,
        // Polyfills para módulos Node.js essenciais
        process: require.resolve('process/browser'),
        path: require.resolve('path-browserify'),
        crypto: require.resolve('crypto-browserify'),
        stream: require.resolve('stream-browserify'),
        buffer: require.resolve('buffer'),
        util: require.resolve('util'),
        events: require.resolve('events'),
        url: require.resolve('url'),
        http: require.resolve('stream-http'),
        https: require.resolve('https-browserify'),
        assert: require.resolve('assert'),
        zlib: require.resolve('browserify-zlib'),
        querystring: require.resolve('querystring-es3'),
        os: require.resolve('os-browserify/browser'),
        // Polyfills específicos para módulos node:*
        'node:events': require.resolve('events'),
        'node:process': require.resolve('process/browser'),
        'node:util': require.resolve('util'),
        'node:path': require.resolve('path-browserify'),
        'node:crypto': require.resolve('crypto-browserify'),
        'node:stream': require.resolve('stream-browserify'),
        'node:buffer': require.resolve('buffer'),
        'node:url': require.resolve('url'),
        'node:http': require.resolve('stream-http'),
        'node:https': require.resolve('https-browserify'),
        'node:assert': require.resolve('assert'),
        'node:zlib': require.resolve('browserify-zlib'),
        'node:querystring': require.resolve('querystring-es3'),
        'node:os': require.resolve('os-browserify/browser'),
        'node:fs': false,
        'node:net': false,
        'node:tls': false,
        'node:dns': false,
        'node:child_process': false,
        'node:http2': false,
        // Bloquear completamente módulos de IA no cliente
        '@google-cloud/vertexai': false,
        '@google-cloud/aiplatform': false,
        '@google/genai': false,
        '@google/generative-ai': false,
        'google-generative-ai': false,
        'generative-ai': false,
        'google-auth-library': false,
        'gcp-metadata': false,
        'google-logging-utils': false,
        'google-gax': false,
        '@grpc/grpc-js': false,
        grpc: false,
      };

      // Adicionar plugins para injetar os polyfills
      config.plugins.push(
        new webpack.ProvidePlugin({
          process: 'process/browser',
          Buffer: ['buffer', 'Buffer'],
        }),
        // Plugin personalizado para substituir módulos node:*
        new webpack.NormalModuleReplacementPlugin(/^node:events$/, 'events'),
        new webpack.NormalModuleReplacementPlugin(/^node:process$/, 'process/browser'),
        new webpack.NormalModuleReplacementPlugin(/^node:util$/, 'util'),
        new webpack.NormalModuleReplacementPlugin(/^node:path$/, 'path-browserify'),
        new webpack.NormalModuleReplacementPlugin(/^node:crypto$/, 'crypto-browserify'),
        new webpack.NormalModuleReplacementPlugin(/^node:stream$/, 'stream-browserify'),
        new webpack.NormalModuleReplacementPlugin(/^node:buffer$/, 'buffer'),
        new webpack.NormalModuleReplacementPlugin(/^node:url$/, 'url'),
        new webpack.NormalModuleReplacementPlugin(/^node:http$/, 'stream-http'),
        new webpack.NormalModuleReplacementPlugin(/^node:https$/, 'https-browserify'),
        new webpack.NormalModuleReplacementPlugin(/^node:assert$/, 'assert'),
        new webpack.NormalModuleReplacementPlugin(/^node:zlib$/, 'browserify-zlib'),
        new webpack.NormalModuleReplacementPlugin(/^node:querystring$/, 'querystring-es3'),
        new webpack.NormalModuleReplacementPlugin(/^node:os$/, 'os-browserify/browser'),
        // Bloquear módulos que não têm polyfills
        new webpack.NormalModuleReplacementPlugin(/^node:fs$/, resource => {
          resource.request = require.resolve('./src/lib/utils/empty-module.js');
        }),
        new webpack.NormalModuleReplacementPlugin(/^node:net$/, resource => {
          resource.request = require.resolve('./src/lib/utils/empty-module.js');
        }),
        new webpack.NormalModuleReplacementPlugin(/^node:tls$/, resource => {
          resource.request = require.resolve('./src/lib/utils/empty-module.js');
        }),
        new webpack.NormalModuleReplacementPlugin(/^node:dns$/, resource => {
          resource.request = require.resolve('./src/lib/utils/empty-module.js');
        }),
        new webpack.NormalModuleReplacementPlugin(/^node:child_process$/, resource => {
          resource.request = require.resolve('./src/lib/utils/empty-module.js');
        }),
        new webpack.NormalModuleReplacementPlugin(/^node:http2$/, resource => {
          resource.request = require.resolve('./src/lib/utils/empty-module.js');
        })
      );
    }

    return config;
  },
  // Opções de análise para produção
  productionBrowserSourceMaps: false,
  poweredByHeader: false,
  // Configurações para exportação de builds
  output: 'standalone',
  // Desabilitar exportação estática completamente
  staticPageGenerationTimeout: 180,
  // Configurações para internacionalização
  i18n: {
    locales: ['pt-BR', 'en-US', 'es'],
    defaultLocale: 'pt-BR',
    localeDetection: false,
  },
  // Transpilar apenas pacotes necessários (removidos pacotes Google para evitar problemas no cliente)
  transpilePackages: [
    // Removidos pacotes Google que devem ser server-only
  ],
  // Configuração de redirecionamentos para migração do Pages Router para App Router
  async redirects() {
    return [
      // Redirecionar APIs antigas para o manipulador de redirecionamento
      {
        source: '/api/:path*',
        has: [
          {
            type: 'header',
            key: 'x-legacy-api',
            value: '(?<legacyValue>.*)',
          },
        ],
        destination: '/api/legacy-redirect/:path*',
        permanent: false,
      },
    ];
  },
};

module.exports = nextConfig;
