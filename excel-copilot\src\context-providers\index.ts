/**
 * Exportações centralizadas para os provedores de contexto
 *
 * Este arquivo centraliza a exportação de todos os provedores de contexto
 * da aplicação, para facilitar importações e garantir consistência.
 */

// Exportar o contexto de localização
export { default as LocaleContext, LocaleProvider, useLocale } from './LocaleProvider';

// Exportar o provedor de tema
export { ThemeProvider } from './ThemeProvider';

/**
 * Componente que agrega todos os provedores de contexto em um único wrapper
 */
export { ProviderComposer, SingleProvider } from './ProviderComposer';

// NOTA: As exportações abaixo foram comentadas pois o arquivo './providers' não existe
// Estas exportações serão implementadas quando os respectivos componentes forem criados
/*
export {
  ToastProvider,
  ModalProvider,
  AuthProvider,
  SocketProvider,
  CollaborationProvider,
  WorkbookProvider,
  SettingsProvider,
  FeedbackProvider,
  AIProvider,
} from './providers';
*/
