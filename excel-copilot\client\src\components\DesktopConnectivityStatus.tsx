import React from 'react';

import { useDesktopBridge } from '../lib/desktop-bridge';
import { WebSocketStatus } from '../types/index';

interface StatusDetail {
  color: string;
  text: string;
  icon: string;
}

// Desktop connectivity status indicator component
export function DesktopConnectivityStatus(): JSX.Element {
  const { status, connect, isReady } = useDesktopBridge();

  // Define status details based on connection state
  const statusDetails = (): StatusDetail => {
    switch (status) {
      case WebSocketStatus.Connected:
        return {
          color: 'bg-green-500',
          text: 'Conectado ao Excel Copilot Desktop',
          icon: '✓',
        };
      case WebSocketStatus.Connecting:
        return {
          color: 'bg-yellow-500',
          text: 'Conectando ao Excel Copilot Desktop...',
          icon: '⟳',
        };
      case WebSocketStatus.Error:
        return {
          color: 'bg-red-500',
          text: 'Erro de conexão',
          icon: '⚠',
        };
      default:
        return {
          color: 'bg-gray-400',
          text: 'Excel Copilot Desktop desconectado',
          icon: '×',
        };
    }
  };

  const { color, text, icon } = statusDetails();

  return (
    <div className="flex items-center space-x-2 text-sm p-2 rounded-md bg-opacity-10 hover:bg-opacity-20 transition-all">
      <div
        className={`${color} w-3 h-3 rounded-full flex items-center justify-center text-white text-xs`}
      >
        {status === WebSocketStatus.Connecting && <span className="animate-spin">{icon}</span>}
        {status !== WebSocketStatus.Connecting && icon}
      </div>
      <span>{text}</span>
      {!isReady && (
        <button
          onClick={() => connect()}
          className="text-xs px-2 py-1 rounded bg-blue-500 text-white hover:bg-blue-600 transition-colors"
        >
          Conectar
        </button>
      )}
    </div>
  );
}
