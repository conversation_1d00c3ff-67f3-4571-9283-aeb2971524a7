/**
 * Declarações de tipos para estender o pacote 'ai' da Vercel
 */

declare module 'ai' {
  /**
   * Função que retorna uma resposta streamada como texto
   */
  export class StreamingTextResponse extends Response {
    constructor(stream: ReadableStream<Uint8Array>, init?: ResponseInit);
  }

  /**
   * Representação de uma mensagem básica
   */
  export interface Message {
    id: string;
    role: 'user' | 'assistant' | 'system' | 'function';
    content: string;
    createdAt?: Date;
  }
}

declare module 'ai/react' {
  import { Message } from 'ai';

  /**
   * Hook para gerenciar conversas de chat
   */
  export function useChat(options?: {
    api?: string;
    id?: string;
    initialMessages?: Message[];
    body?: Record<string, any>;
    headers?: Record<string, string>;
    onResponse?: (response: Response) => void | Promise<void>;
    onFinish?: (message: Message) => void | Promise<void>;
    onError?: (error: Error) => void | Promise<void>;
  }): {
    messages: Message[];
    input: string;
    setInput: (input: string) => void;
    handleInputChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
    handleSubmit: (e: React.FormEvent<HTMLFormElement>) => void;
    isLoading: boolean;
    error: Error | undefined;
  };
}
