# Componentes UI

Este diretório contém componentes UI reutilizáveis para o Excel Copilot.

## Componentes de Motion

### MotionSafe

O componente `MotionSafe` permite aplicar animações respeitando as preferências de redução de movimento do usuário.

```tsx
import { MotionSafe } from "@/components/ui/motion";

// Uso básico
<MotionSafe>
  {/* Conteúdo animado aqui */}
</MotionSafe>

// Com fallback para usuários com preferência por redução de movimento
<MotionSafe
  fallback={<div>Conteúdo estático</div>}
>
  {/* Conteúdo animado aqui */}
</MotionSafe>
```

#### Props

- `children`: O conteúdo que será renderizado quando o usuário não tem preferência por redução de movimento.
- `fallback`: (Opcional) Conteúdo alternativo para usuários com preferência por redução de movimento.
- `className`: (Opcional) Classes CSS para o wrapper.

#### Exemplo

Veja um exemplo completo em `/components/examples/MotionSafeExample.tsx`

### AnimationWrapper

O componente `AnimationWrapper` facilita a aplicação de animações utilizando as classes definidas no Tailwind, respeitando as preferências de movimento reduzido do usuário.

```tsx
import { AnimationWrapper } from "@/components/ui/animation-wrapper";

// Uso básico
<AnimationWrapper animation="fade-in">
  {/* Conteúdo animado aqui */}
</AnimationWrapper>

// Com duração e atraso personalizados
<AnimationWrapper
  animation="bounce-in"
  duration="slow"
  delay="medium"
>
  {/* Conteúdo animado aqui */}
</AnimationWrapper>
```

#### Props

- `children`: O conteúdo que será animado.
- `animation`: O tipo de animação a ser aplicada (ex: 'fade-in', 'scale-in', etc.).
- `delay`: (Opcional) Atraso antes de iniciar a animação ('none', 'short', 'medium', 'long').
- `duration`: (Opcional) Duração da animação ('fast', 'normal', 'slow').
- `className`: (Opcional) Classes CSS adicionais.
- `autoPlay`: (Opcional) Se a animação deve iniciar automaticamente (padrão: true).
- `repeat`: (Opcional) Se a animação deve repetir (padrão: false).

#### Exemplo

Veja um exemplo completo em `/components/examples/AnimationWrapperExample.tsx`

## Outros componentes

[Lista de outros componentes aqui]
