/**
 * Extensões de tipos para o Prisma
 * Resolve problemas de compatibilidade entre null e undefined
 */
import { Prisma } from '@prisma/client';

/**
 * Tipos para operações com segurança de tipos quando null/undefined são problemáticos
 */
export namespace PrismaExtensions {
  /**
   * Converte um tipo Prisma para aceitar undefined onde aceita null
   */
  export type NullableToOptional<T> = {
    [K in keyof T]: T[K] extends null | infer U ? U | undefined : T[K];
  };

  /**
   * Converte propriedades opcionais (que podem ser undefined) para aceitar null também
   * Útil para compatibilidade com Prisma ao criar objetos
   */
  export type OptionalToNullable<T> = {
    [K in keyof T]: undefined extends T[K] ? T[K] | null : T[K];
  };

  /**
   * Tipo seguro para inputs de criação do Prisma que funcionam com exactOptionalPropertyTypes
   */
  export type SafeCreateInput<T extends object> = Omit<
    T,
    keyof { [K in keyof T as undefined extends T[K] ? K : never]: T[K] }
  > & {
    [K in keyof T as undefined extends T[K] ? K : never]?: T[K] | null;
  };

  /**
   * Tipo seguro para Where do Prisma
   */
  export type SafeWhereInput<T> = {
    [K in keyof T]?: T[K] | null;
  };

  /**
   * Tipo seguro para ApiUsageCreateInput
   */
  export type SafeApiUsageCreateInput = {
    userId: string;
    endpoint: string;
    count: number;
    workbookId: string | null;
    billable: boolean;
  };

  /**
   * Tipo seguro para WorkbookCreateInput
   */
  export type SafeWorkbookCreateInput = {
    name: string;
    description: string | null;
    userId: string;
    sheets: {
      create: {
        name: string;
        data: string;
      }[];
    };
  };

  /**
   * Tipo seguro para ChatHistoryCreateInput
   */
  export type SafeChatHistoryCreateInput = {
    userId: string;
    message: string;
    response: string;
    workbookId: string | null;
  };
}

/**
 * Interface que adiciona funcionalidades extras aos inputs do Prisma
 */
export interface PrismaExtensions {
  /**
   * Converte undefined para null em strings opcionais
   * para compatibilidade com Prisma
   */
  toNullableString: (value: string | undefined) => string | null;
}

/**
 * Converte valores undefined para null para compatibilidade com o Prisma
 * @param value O valor string ou undefined a converter
 * @returns O valor original ou null (nunca undefined)
 */
export function toNullableString(value: string | undefined): string | null {
  return value === undefined ? null : value;
}

/**
 * Cria um objeto de input para o Workbook, tratando adequadamente valores opcionais
 */
export function createWorkbookInput(
  name: string,
  userId: string,
  description?: string,
  sheetsData?: Array<{ name: string; data: string }>
): Prisma.WorkbookCreateInput {
  const input: Prisma.WorkbookCreateInput = {
    name,
    userId,
    description: description === undefined ? null : description,
  };

  if (sheetsData && sheetsData.length > 0) {
    input.sheets = {
      create: sheetsData,
    };
  }

  return input;
}

/**
 * Cria um objeto de input para o ApiUsage, tratando adequadamente valores opcionais
 */
export function createApiUsageInput(
  userId: string,
  endpoint: string,
  count: number = 1,
  workbookId?: string,
  billable: boolean = true
): Prisma.ApiUsageCreateInput {
  return {
    userId,
    endpoint,
    count,
    billable,
    workbookId: workbookId === undefined ? null : workbookId,
  };
}

/**
 * Cria um objeto de input para o ChatHistory, tratando adequadamente valores opcionais
 */
export function createChatHistoryInput(
  userId: string,
  role: string,
  content: string,
  workbookId?: string
): Prisma.ChatHistoryCreateInput {
  return {
    userId,
    role,
    content,
    workbookId: workbookId === undefined ? null : workbookId,
  };
}
