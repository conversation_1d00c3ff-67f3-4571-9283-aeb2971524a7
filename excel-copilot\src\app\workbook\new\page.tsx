'use client';

import { Loader2 } from 'lucide-react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { Suspense, useEffect, useState } from 'react';
import { toast } from 'sonner';

import { SpreadsheetEditor } from '@/components/workbook/SpreadsheetEditor';

// Dados iniciais para uma planilha em branco
const INITIAL_DATA = {
  headers: ['A', 'B', 'C', 'D', 'E'],
  rows: [
    ['', '', '', '', ''],
    ['', '', '', '', ''],
    ['', '', '', '', ''],
    ['', '', '', '', ''],
    ['', '', '', '', ''],
  ],
  charts: [] as any[],
  name: 'Nova Planilha',
};

// Nota: Metadados não podem ser exportados de um componente client-side
// Os metadados devem ser definidos em um arquivo separado ou em uma página server-side

export default function NewWorkbookPage() {
  const { status } = useSession();
  const searchParams = useSearchParams();
  const router = useRouter();
  const [initialCommand, setInitialCommand] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Verificar autenticação
    if (status === 'unauthenticated') {
      const currentPath = '/workbook/new';
      const commandParam = searchParams?.get('command')
        ? `&command=${encodeURIComponent(searchParams?.get('command') || '')}`
        : '';
      router.push(`/auth/signin?callbackUrl=${encodeURIComponent(currentPath)}${commandParam}`);
      return;
    }

    if (status === 'authenticated') {
      // Capturar comando da URL se existente
      const command = searchParams?.get('command');
      if (command) {
        setInitialCommand(command);
        // Mostrar indicação ao usuário
        toast.info('Comando recebido', {
          description: 'Vamos processar: ' + command,
          duration: 3000,
        });
      }
      setIsLoading(false);
    }
  }, [searchParams, status, router]);

  // Renderizar loader enquanto verifica autenticação
  if (status === 'loading' || isLoading) {
    return (
      <div className="h-full w-full flex items-center justify-center min-h-screen">
        <div className="flex flex-col items-center space-y-4">
          <Loader2 className="h-10 w-10 animate-spin text-primary" />
          <p className="text-muted-foreground">Verificando autenticação...</p>
        </div>
      </div>
    );
  }

  // Não renderiza nada se não estiver autenticado (será redirecionado pelo useEffect)
  if (status === 'unauthenticated') {
    return null;
  }

  return (
    <div className="w-full h-[calc(100vh-64px)] flex flex-col">
      <Suspense
        fallback={
          <div className="h-full w-full flex items-center justify-center">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <span className="ml-2">Preparando nova planilha...</span>
          </div>
        }
      >
        <SpreadsheetEditor
          workbookId="new"
          initialData={INITIAL_DATA}
          initialCommand={initialCommand}
        />
      </Suspense>
    </div>
  );
}
