'use client';

import { ReactNode } from 'react';

import { cn } from '@/lib/utils';

export interface CardGridProps {
  children: ReactNode;
  columns?: 1 | 2 | 3 | 4;
  gap?: 'sm' | 'md' | 'lg';
  className?: string;
}

/**
 * Grid responsivo para exibição de cards
 */
export function CardGrid({ children, columns = 3, gap = 'md', className }: CardGridProps) {
  const gapClasses = {
    sm: 'gap-3',
    md: 'gap-6',
    lg: 'gap-8',
  };

  const columnClasses = {
    1: 'grid-cols-1',
    2: 'grid-cols-1 md:grid-cols-2',
    3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
    4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4',
  };

  return (
    <div className={cn('grid w-full', columnClasses[columns], gapClasses[gap], className)}>
      {children}
    </div>
  );
}
