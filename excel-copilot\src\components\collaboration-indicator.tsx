'use client';

import { Users } from 'lucide-react';
import { useState, useEffect } from 'react';

import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { useCollabStore, type Collaborator } from '@/lib/collaborative-sync';
import { cn } from '@/lib/utils';

/**
 * Componente para exibir indicadores de colaboração em tempo real
 */
export function CollaborationIndicator({ _workbookId }: { _workbookId: string }) {
  const { collaborators, connected, userId } = useCollabStore();
  const [collaboratorsArray, setCollaboratorsArray] = useState<Collaborator[]>([]);
  const [expanded, setExpanded] = useState(false);

  useEffect(() => {
    // Converter objeto de colaboradores em array e remover o usuário atual
    const collabArray = Object.values(collaborators).filter(collab => collab.id !== userId);
    setCollaboratorsArray(collabArray);
  }, [collaborators, userId]);

  // Limitar o número de avatares visíveis
  const visibleCollaborators = expanded ? collaboratorsArray : collaboratorsArray.slice(0, 3);

  const remainingCount = collaboratorsArray.length - visibleCollaborators.length;

  if (!connected) {
    return null;
  }

  return (
    <div className="flex items-center gap-2">
      <div className="flex -space-x-2">
        {visibleCollaborators.map(collaborator => (
          <TooltipProvider key={collaborator.id}>
            <Tooltip>
              <TooltipTrigger asChild>
                <Avatar
                  className="h-8 w-8 border-2 border-background cursor-pointer transition-transform hover:scale-110 hover:z-10"
                  style={{ borderColor: collaborator.color }}
                >
                  <AvatarFallback
                    style={{ backgroundColor: collaborator.color }}
                    className="text-white"
                  >
                    {getInitials(collaborator.name)}
                  </AvatarFallback>
                  {/* Poderia ter uma imagem se houver */}
                </Avatar>
              </TooltipTrigger>
              <TooltipContent side="bottom" align="center">
                <p className="text-sm font-medium">{collaborator.name}</p>
                <p className="text-xs text-muted-foreground">
                  {getTimeSinceLastActivity(collaborator.lastActivity)}
                </p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        ))}

        {remainingCount > 0 && (
          <Avatar
            onClick={() => setExpanded(!expanded)}
            className="h-8 w-8 border-2 border-background bg-muted cursor-pointer"
          >
            <AvatarFallback>+{remainingCount}</AvatarFallback>
          </Avatar>
        )}
      </div>
      <span
        className={cn(
          'inline-flex items-center rounded-full px-2 py-1 text-xs',
          'bg-primary/10 text-primary dark:bg-primary/20'
        )}
      >
        <Users className="h-3 w-3 mr-1" />
        {collaboratorsArray.length + 1} online
      </span>
    </div>
  );
}

// Função auxiliar para obter iniciais do nome
function getInitials(name: string): string {
  return name
    .split(' ')
    .map(n => n[0])
    .join('')
    .toUpperCase()
    .substring(0, 2);
}

// Função para formatar o tempo desde a última atividade
function getTimeSinceLastActivity(date: Date): string {
  const now = new Date();
  const lastActivity = new Date(date);
  const diffInSeconds = Math.floor((now.getTime() - lastActivity.getTime()) / 1000);

  if (diffInSeconds < 60) {
    return 'Agora mesmo';
  } else if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60);
    return `Há ${minutes} min${minutes > 1 ? 's' : ''}`;
  } else if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600);
    return `Há ${hours} hora${hours > 1 ? 's' : ''}`;
  } else {
    const days = Math.floor(diffInSeconds / 86400);
    return `Há ${days} dia${days > 1 ? 's' : ''}`;
  }
}
