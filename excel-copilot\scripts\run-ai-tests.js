/**
 * Script para executar testes do Vertex AI com controle adequado do encerramento
 */

const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

// Cores para output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  red: '\x1b[31m',
  cyan: '\x1b[36m',
};

function runTests() {
  console.log(`${colors.bright}${colors.cyan}Executando testes do Vertex AI${colors.reset}\n`);

  // Verificar arquivo de credenciais
  const credentialsPath = path.join(process.cwd(), 'vertex-credentials.json');

  if (!fs.existsSync(credentialsPath)) {
    console.log(
      `${colors.yellow}Atenção: Arquivo de credenciais não encontrado: ${credentialsPath}${colors.reset}`
    );
    console.log(
      `Os testes de integração serão ignorados. Os testes unitários ainda serão executados.\n`
    );
  } else {
    console.log(`${colors.green}✓ Arquivo de credenciais encontrado${colors.reset}\n`);
  }

  try {
    // Executar teste manual primeiro
    console.log(`${colors.bright}Executando teste manual do Vertex AI...${colors.reset}`);
    execSync('node scripts/test-vertex-ai.js', { stdio: 'inherit' });
    console.log(`\n${colors.green}✓ Teste manual concluído com sucesso!${colors.reset}\n`);
  } catch (error) {
    console.error(`\n${colors.red}❌ Falha no teste manual: ${error.message}${colors.reset}\n`);
  }

  try {
    // Executar testes unitários
    console.log(`${colors.bright}Executando testes unitários...${colors.reset}`);
    execSync(
      'jest --testPathPattern=__tests__/unit/server/vertex-ai.test.ts --forceExit --detectOpenHandles --no-cache',
      { stdio: 'inherit' }
    );
    console.log(`\n${colors.green}✓ Testes unitários concluídos com sucesso!${colors.reset}\n`);
  } catch (error) {
    console.error(
      `\n${colors.red}❌ Falha nos testes unitários: ${error.message}${colors.reset}\n`
    );
  }

  // Executar testes de integração se as credenciais existirem
  if (fs.existsSync(credentialsPath)) {
    try {
      console.log(`${colors.bright}Executando testes de integração...${colors.reset}`);
      execSync(
        'jest --testPathPattern=__tests__/integration/vertex-ai.integration.test.ts --forceExit --detectOpenHandles --no-cache',
        { stdio: 'inherit' }
      );
      console.log(
        `\n${colors.green}✓ Testes de integração concluídos com sucesso!${colors.reset}\n`
      );
    } catch (error) {
      console.error(
        `\n${colors.red}❌ Falha nos testes de integração: ${error.message}${colors.reset}\n`
      );
    }
  } else {
    console.log(
      `${colors.yellow}Testes de integração ignorados devido à falta de credenciais.${colors.reset}\n`
    );
  }

  console.log(`${colors.bright}${colors.green}Processo de testes concluído!${colors.reset}`);
}

// Executar os testes
runTests();
