��#   G u i a   d e   C o n f i g u r a � � o   d o   A m b i e n t e   d e   P r o d u � � o   p a r a   E x c e l   C o p i l o t 
 
 
 
 E s t e   g u i a   a p r e s e n t a   o s   p a s s o s   n e c e s s � r i o s   p a r a   c o n f i g u r a r   o   E x c e l   C o p i l o t   e m   u m   a m b i e n t e   d e   p r o d u � � o . 
 
 
 
 # #   1 .   P r � - r e q u i s i t o s 
 
 
 
 -   <PERSON> e r   e   D o c k e r   C o m p o s e   i n s t a l a d o s 
 
 -   U m a   i n s t � n c i a   d e   s e r v i d o r   c o m   p e l o   m e n o s   2 G B   d e   R A M 
 
 -   D o m � n i o   c o n f i g u r a d o   p a r a   o   s e r v i � o   ( p a r a   H T T P S ) 
 
 -   C o n t a   G o o g l e   C l o u d   c o m   V e r t e x   A I   h a b i l i t a d o 
 
 
 
 # #   2 .   C o n f i g u r a � � o   d o   A r q u i v o   . e n v . p r o d u c t i o n 
 
 
 
 1 .   E d i t e   o   a r q u i v o   ` . e n v . p r o d u c t i o n `   c o m   s u a s   i n f o r m a � � e s   r e a i s : 
 
 
 
 ` ` ` b a s h 
 
 #   C o n f i g u r a � � e s   d e   b a n c o   d e   d a d o s 
 
 D A T A B A S E _ U R L = " m y s q l : / / e x c e l _ u s e r : s t r o n g _ p a s s w o r d _ p r o d @ m y s q l : 3 3 0 6 / e x c e l _ c o p i l o t _ p r o d " 
 
 D A T A B A S E _ P R O V I D E R = " m y s q l " 
 
 M Y S Q L _ R O O T _ P A S S W O R D = s u a _ s e n h a _ r o o t _ s e g u r a 
 
 M Y S Q L _ P A S S W O R D = s u a _ s e n h a _ u s u a r i o _ s e g u r a 
 
 
 
 #   N e x t A u t h   e   d o m � n i o 
 
 N E X T A U T H _ S E C R E T = " s u a _ c h a v e _ s e c r e t a _ a l e a t o r i a " 
 
 N E X T A U T H _ U R L = " h t t p s : / / s u a - a p p . e x e m p l o . c o m " 
 
 
 
 #   P r o v e d o r e s   O A u t h 
 
 G O O G L E _ C L I E N T _ I D = " s e u _ c l i e n t _ i d _ d o _ g o o g l e " 
 
 G O O G L E _ C L I E N T _ S E C R E T = " s e u _ c l i e n t _ s e c r e t _ d o _ g o o g l e " 
 
 G I T H U B _ C L I E N T _ I D = " s e u _ g i t h u b _ c l i e n t _ i d " 
 
 G I T H U B _ C L I E N T _ S E C R E T = " s e u _ g i t h u b _ c l i e n t _ s e c r e t " 
 
 
 
 #   S t r i p e   ( P a g a m e n t o s ) 
 
 S T R I P E _ S E C R E T _ K E Y = " s k _ l i v e _ s u a _ c h a v e _ r e a l " 
 
 N E X T _ P U B L I C _ S T R I P E _ P U B L I S H A B L E _ K E Y = " p k _ l i v e _ s u a _ c h a v e _ r e a l " 
 
 S T R I P E _ W E B H O O K _ S E C R E T = " w h s e c _ s e u _ w e b h o o k _ r e a l " 
 
 N E X T _ P U B L I C _ S T R I P E _ P R I C E _ M O N T H L Y = " p r i c e _ s e u _ i d _ m e n s a l " 
 
 N E X T _ P U B L I C _ S T R I P E _ P R I C E _ A N N U A L = " p r i c e _ s e u _ i d _ a n u a l " 
 
 
 
 #   C o n f i g u r a � � o   d o   V e r t e x   A I   ( o b r i g a t � r i o   q u a n d o   U S E _ M O C K _ A I = f a l s e ) 
 
 V E R T E X _ A I _ E N A B L E D = t r u e 
 
 V E R T E X _ A I _ P R O J E C T _ I D = " s e u - p r o j e t o - g c p " 
 
 V E R T E X _ A I _ L O C A T I O N = " u s - c e n t r a l 1 " 
 
 V E R T E X _ A I _ M O D E L _ N A M E = " g e m i n i - 1 . 5 - p r o " 
 
 
 
 #   M o d o   d e   p r o d u � � o 
 
 N O D E _ E N V = " p r o d u c t i o n " 
 
 U S E _ M O C K _ A I = f a l s e   #   D e s a t i v e   o s   m o c k s   e m   p r o d u � � o 
 
 ` ` ` 
 
 
 
 # #   3 .   C o n f i g u r a � � o   d o   V e r t e x   A I 
 
 
 
 1 .   N o   c o n s o l e   d o   G o o g l e   C l o u d   ( h t t p s : / / c o n s o l e . c l o u d . g o o g l e . c o m ) ,   a c e s s e   " I A M   &   A d m i n "   >   " S e r v i c e   A c c o u n t s " . 
 
 2 .   C r i e   u m a   n o v a   c o n t a   d e   s e r v i � o   p a r a   o   E x c e l   C o p i l o t . 
 
 3 .   A t r i b u a   a s   s e g u i n t e s   p e r m i s s � e s :   " V e r t e x   A I   U s e r "   e   " S e r v i c e   A c c o u n t   U s e r " . 
 
 4 .   C r i e   u m a   c h a v e   J S O N   p a r a   e s t a   c o n t a   d e   s e r v i � o . 
 
 5 .   S a l v e   o   a r q u i v o   J S O N   c o m o   ` v e r t e x - c r e d e n t i a l s . j s o n `   n a   r a i z   d o   p r o j e t o . 
 
 
 
 E s t a   c o n t a   d e   s e r v i � o   p e r m i t e   q u e   o   E x c e l   C o p i l o t   f a � a   c h a m a d a s   a u t e n t i c a d a s   p a r a   a   A P I   d o   V e r t e x   A I . 
 
 
 
 A l t e r n a t i v a m e n t e ,   v o c �   p o d e   c o n f i g u r a r   o   c a m i n h o   p a r a   o   a r q u i v o   d e   c r e d e n c i a i s   u s a n d o   a   v a r i � v e l : 
 
 ` ` ` 
 
 V E R T E X _ A I _ C R E D E N T I A L S _ P A T H = " / c a m i n h o / p a r a / s e u - a r q u i v o - c r e d e n c i a i s . j s o n " 
 
 ` ` ` 
 
 
 
 # #   4 .   M i g r a � � o   d o   B a n c o   d e   D a d o s 
 
 
 
 E x e c u t e   o   s e g u i n t e   c o m a n d o   p a r a   a p l i c a r   a s   m i g r a � � e s   a o   M y S Q L : 
 
 
 
 ` ` ` b a s h 
 
 #   I n i c i e   a p e n a s   o   c o n t a i n e r   M y S Q L   p r i m e i r o 
 
 d o c k e r - c o m p o s e   u p   - d   m y s q l 
 
 
 
 #   A g u a r d e   o   M y S Q L   i n i c i a r   c o m p l e t a m e n t e   ( v e r i f i q u e   c o m   d o c k e r - c o m p o s e   l o g s   m y s q l ) 
 
 #   E m   s e g u i d a ,   e x e c u t e   a s   m i g r a � � e s 
 
 d o c k e r - c o m p o s e   r u n   - - r m   a p p   n p x   p r i s m a   m i g r a t e   d e p l o y 
 
 ` ` ` 
 
 
 
 # #   5 .   I n i c i a r   e m   M o d o   d e   P r o d u � � o 
 
 
 
 ` ` ` b a s h 
 
 #   C o n s t r u i r   e   i n i c i a r   t o d o s   o s   s e r v i � o s 
 
 d o c k e r - c o m p o s e   u p   - d   - - b u i l d 
 
 ` ` ` 
 
 
 
 # #   6 .   C o n f i g u r a � � o   d e   S t r i p e   ( P a g a m e n t o s ) 
 
 
 
 1 .   C r i e   u m a   c o n t a   n o   S t r i p e   ( h t t p s : / / s t r i p e . c o m ) 
 
 2 .   O b t e n h a   a s   c h a v e s   d e   A P I   ( n o   p a i n e l   D e v e l o p e r s   >   A P I   K e y s ) 
 
 3 .   C o n f i g u r e   o s   p r o d u t o s   e   p r e � o s   n o   p a i n e l   S t r i p e 
 
 4 .   C o n f i g u r e   o   e n d p o i n t   w e b h o o k : 
 
       -   U R L :   h t t p s : / / s e u - d o m i n i o . c o m / a p i / w e b h o o k s / s t r i p e 
 
       -   E v e n t o s :   p a y m e n t * i n t e n t . s u c c e e d e d ,   c h e c k o u t . s e s s i o n . c o m p l e t e d ,   c u s t o m e r . s u b s c r i p t i o n . u p d a t e d ,   c u s t o m e r . s u b s c r i p t i o n . d e l e t e d 
 
 
 
 # #   7 .   C o n f i g u r a � � o   d e   H T T P S 
 
 
 
 R e c o m e n d a m o s   u s a r   N g i n x   c o m o   p r o x y   r e v e r s o   c o m   L e t ' s   E n c r y p t : 
 
 
 
 ` ` ` b a s h 
 
 #   E x e m p l o   d e   c o n f i g u r a � � o   p a r a   N g i n x 
 
 s e r v e r   { 
 
         l i s t e n   8 0 ; 
 
         s e r v e r * n a m e   s e u - d o m i n i o . c o m ; 
 
         
 
         l o c a t i o n   /   { 
 
                 r e t u r n   3 0 1   h t t p s : / / $ h o s t $ r e q u e s t * u r i ; 
 
         } 
 
 } 
 
 
 
 s e r v e r   { 
 
         l i s t e n   4 4 3   s s l ; 
 
         s e r v e r * n a m e   s e u - d o m i n i o . c o m ; 
 
         
 
         s s l * c e r t i f i c a t e   / e t c / l e t s e n c r y p t / l i v e / s e u - d o m i n i o . c o m / f u l l c h a i n . p e m ; 
 
         s s l * c e r t i f i c a t e * k e y   / e t c / l e t s e n c r y p t / l i v e / s e u - d o m i n i o . c o m / p r i v k e y . p e m ; 
 
         
 
         l o c a t i o n   /   { 
 
                 p r o x y * p a s s   h t t p : / / l o c a l h o s t : 3 0 0 0 ; 
 
                 p r o x y * s e t * h e a d e r   H o s t   $ h o s t ; 
 
                 p r o x y _ s e t _ h e a d e r   X - R e a l - I P   $ r e m o t e * a d d r ; 
 
                 p r o x y * s e t * h e a d e r   X - F o r w a r d e d - F o r   $ p r o x y * a d d * x * f o r w a r d e d * f o r ; 
 
                 p r o x y * s e t * h e a d e r   X - F o r w a r d e d - P r o t o   $ s c h e m e ; 
 
         } 
 
 } 
 
 ` ` ` 
 
 
 
 # #   8 .   B a c k u p   d o   B a n c o   d e   D a d o s 
 
 
 
 C o n f i g u r e   b a c k u p s   a u t o m � t i c o s   d o   M y S Q L : 
 
 
 
 ` ` ` b a s h 
 
 #   S c r i p t   d e   b a c k u p   a   s e r   e x e c u t a d o   p o r   c r o n 
 
 d o c k e r - c o m p o s e   e x e c   m y s q l   m y s q l d u m p   - u   r o o t   - p $ { M Y S Q L * R O O T * P A S S W O R D }   e x c e l * c o p i l o t * p r o d   >   b a c k u p * $ ( d a t e   + % Y % m % d ) . s q l 
 
 ` ` ` 
 
 
 
 # #   9 .   M o n i t o r a m e n t o 
 
 
 
 O s   s e r v i � o s   P r o m e t h e u s   e   G r a f a n a   j �   e s t � o   c o n f i g u r a d o s .   A c e s s e : 
 
 
 
 -   G r a f a n a :   h t t p s : / / s e u - d o m i n i o . c o m : 3 0 0 1   ( c r e d e n c i a i s :   a d m i n   /   s e n h a   c o n f i g u r a d a   e m   G R A F A N A _ A D M I N _ P A S S W O R D ) 
 
 -   O   d a s h b o a r d   p a d r � o   j �   i n c l u i   m � t r i c a s   d e   u s o   d a   A P I ,   c h a m a d a s   d a   I A ,   e   u t i l i z a � � o   d o   s e r v i d o r 
 
 
 
 # #   1 0 .   V e r i f i c a � � o   F i n a l 
 
 
 
 1 .   V e r i f i q u e   o   s t a t u s   d o s   s e r v i � o s :   ` d o c k e r - c o m p o s e   p s ` 
 
 2 .   V e r i f i q u e   o s   l o g s :   ` d o c k e r - c o m p o s e   l o g s   - f   a p p ` 
 
 3 .   T e s t e   a   a p l i c a � � o   n a v e g a n d o   p a r a   h t t p s : / / s e u - d o m i n i o . c o m 
 
 4 .   C o n f i r m e   q u e   a s   a u t e n t i c a � � e s   s o c i a i s   e s t � o   f u n c i o n a n d o 
 
 5 .   V e r i f i q u e   o   p r o c e s s a m e n t o   d e   p a g a m e n t o s   r e a l i z a n d o   u m a   c o m p r a   d e   t e s t e 
 
 
 
 # #   S o l u � � o   d e   P r o b l e m a s 
 
 
 
 -   * * E r r o   d e   c o n e x � o   c o m   b a n c o   d e   d a d o s * * :   V e r i f i q u e   a s   c r e d e n c i a i s   e m   D A T A B A S E * U R L 
 
 -   * * A u t e n t i c a � � o   s o c i a l   n � o   f u n c i o n a * * :   V e r i f i q u e   o s   r e d i r e c i o n a m e n t o s   a u t o r i z a d o s   n a s   p l a t a f o r m a s   O A u t h 
 
 -   * * I A   n � o   r e s p o n d e * * :   V e r i f i q u e   s e   o   a r q u i v o   v e r t e x - c r e d e n t i a l s . j s o n   e x i s t e   o u   s e   a s   v a r i � v e i s   V E R T E X * A I * P R O J E C T * I D ,   V E R T E X * A I * L O C A T I O N   e s t � o   c o n f i g u r a d a s   c o r r e t a m e n t e .   V e r i f i q u e   t a m b � m   s e   o   s e r v i � o   V e r t e x   A I   e s t �   a t i v o   n a   s u a   c o n t a   G o o g l e   C l o u d . 
 
 -   * * P a g a m e n t o s   n � o   p r o c e s s a d o s * * :   V e r i f i q u e   c o n f i g u r a � � o   d o   w e b h o o k   e   l o g s   d o   S t r i p e 
 
 
