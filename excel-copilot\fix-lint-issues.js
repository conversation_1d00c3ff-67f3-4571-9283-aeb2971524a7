#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Lista de arquivos e suas correções
const fixes = [
  // Variáveis não utilizadas - remover parâmetro
  {
    file: 'src/components/dashboard/WorkbooksTable.tsx',
    replacements: [
      { from: '} catch (_e) {', to: '} catch {' },
      { from: '} catch (_error) {', to: '} catch {' },
    ],
  },
  {
    file: 'src/context-providers/LocaleProvider.tsx',
    replacements: [
      {
        from: 'const [localeState, setLocaleState] = useState',
        to: 'const [localeState] = useState',
      },
    ],
  },
  {
    file: 'src/contexts/LocaleContext.tsx',
    replacements: [
      {
        from: 'const [localeState, setLocaleState] = useState',
        to: 'const [localeState] = useState',
      },
    ],
  },
  {
    file: 'src/hooks/use-media-query.ts',
    replacements: [{ from: '} catch (_error) {', to: '} catch {' }],
  },
  {
    file: 'src/hooks/useBidirectionalVirtualizer.ts',
    replacements: [
      { from: 'RowVirtualItem,', to: '' },
      { from: 'ColumnVirtualItem,', to: '' },
    ],
  },
  {
    file: 'src/hooks/useErrorHandler.ts',
    replacements: [{ from: '} catch (_e) {', to: '} catch {' }],
  },
  {
    file: 'src/hooks/useExcelWorker.ts',
    replacements: [{ from: 'ExcelWorkerRequest,', to: '' }],
  },
  {
    file: 'src/hooks/useI18n.tsx',
    replacements: [
      {
        from: 'const [localeState, setLocaleState] = useState',
        to: 'const [localeState] = useState',
      },
    ],
  },
];

// Função para aplicar correções
function applyFixes() {
  console.log('🔧 Aplicando correções de linting...\n');

  fixes.forEach(({ file, replacements }) => {
    const filePath = path.join(__dirname, file);

    if (!fs.existsSync(filePath)) {
      console.log(`⚠️  Arquivo não encontrado: ${file}`);
      return;
    }

    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;

    replacements.forEach(({ from, to }) => {
      if (content.includes(from)) {
        content = content.replace(new RegExp(from.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), to);
        modified = true;
        console.log(`✅ ${file}: ${from} → ${to}`);
      }
    });

    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
    }
  });

  console.log('\n🎉 Correções aplicadas com sucesso!');
}

// Executar correções
applyFixes();
