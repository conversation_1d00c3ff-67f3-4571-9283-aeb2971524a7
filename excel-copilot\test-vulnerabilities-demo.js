#!/usr/bin/env node

/**
 * 🔍 DEMONSTRAÇÃO PRÁTICA DAS VULNERABILIDADES ENCONTRADAS
 * Excel Copilot - Sistema de Preços e Privilégios
 *
 * Este script demonstra as vulnerabilidades identificadas na auditoria
 * ⚠️ APENAS PARA FINS EDUCACIONAIS E CORREÇÃO
 */

import fs from 'fs';

console.log('🔍 DEMONSTRAÇÃO DE VULNERABILIDADES - Excel Copilot');
console.log('='.repeat(60));

// 1. DEMONSTRAÇÃO: Race Condition em Contadores
console.log('\n🚨 VULNERABILIDADE 1: Race Condition em Contadores');
console.log('-'.repeat(50));

const raceConditionDemo = `
// CENÁRIO: Usuário FREE (limite: 5 workbooks) com 4 workbooks existentes
// Duas requisições simultâneas para criar workbook

async function demonstrateRaceCondition() {
  const userId = 'user_free_with_4_workbooks';
  
  // Simular duas requisições simultâneas
  const request1 = fetch('/api/workbook/save', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      name: 'Workbook 5',
      userId: userId,
      sheets: [{ name: 'Sheet1', data: [] }]
    })
  });
  
  const request2 = fetch('/api/workbook/save', {
    method: 'POST', 
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      name: 'Workbook 6',
      userId: userId,
      sheets: [{ name: 'Sheet1', data: [] }]
    })
  });
  
  // Executar simultaneamente
  const [result1, result2] = await Promise.all([request1, request2]);
  
  console.log('Request 1:', result1.status); // Pode retornar 200
  console.log('Request 2:', result2.status); // Pode retornar 200 (VULNERABILIDADE!)
  
  // RESULTADO ESPERADO: Uma deve falhar (403)
  // RESULTADO REAL: Ambas podem passar devido à race condition
}
`;

console.log(raceConditionDemo);
console.log('❌ PROBLEMA: Verificação e criação não são atômicas');
console.log('✅ SOLUÇÃO: Usar transações Prisma com isolamento Serializable');

// 2. DEMONSTRAÇÃO: Cache Manipulation
console.log('\n🚨 VULNERABILIDADE 2: Manipulação de Cache');
console.log('-'.repeat(50));

const cacheManipulationDemo = `
// CENÁRIO: Cache de planos com estrutura incorreta

// Código vulnerável atual:
rateLimitCache.set(cacheKey, {
  count: plan as any, // ❌ Armazena plano no campo 'count'
  resetTime: now + 5 * 60 * 1000,
});

// EXPLOIT POSSÍVEL:
// 1. Atacante monitora timing de requests
// 2. Identifica momento de cache miss
// 3. Injeta dados maliciosos durante reconstrução do cache
// 4. Obtém privilégios de plano superior

function exploitCacheVulnerability() {
  // Simular timing attack
  const startTime = Date.now();
  
  // Request que força cache miss
  fetch('/api/workbooks', { method: 'GET' });
  
  // Imediatamente após, tentar injetar dados
  // (em ambiente real, seria mais sofisticado)
  const endTime = Date.now();
  
  if (endTime - startTime > 100) {
    console.log('⚠️ Cache miss detectado - janela de vulnerabilidade');
  }
}
`;

console.log(cacheManipulationDemo);
console.log('❌ PROBLEMA: Cache sem validação de integridade');
console.log('✅ SOLUÇÃO: Assinatura criptográfica dos dados de cache');

// 3. DEMONSTRAÇÃO: Client-side Bypass
console.log('\n⚠️ VULNERABILIDADE 3: Bypass Client-side');
console.log('-'.repeat(50));

const clientBypassDemo = `
// CENÁRIO: Verificações apenas no frontend podem ser contornadas

// Código vulnerável:
function handlePlanSelect(plan) {
  if (userHasPlan(plan)) {
    toast.info('Você já está inscrito neste plano.');
    return; // ❌ Pode ser contornado via DevTools
  }
  // Continuar com checkout...
}

// EXPLOIT VIA DEVTOOLS:
// 1. Abrir DevTools (F12)
// 2. Executar: userHasPlan = () => false;
// 3. Tentar fazer checkout de plano já possuído
// 4. Frontend permite, mas API deve bloquear

// TESTE DE BYPASS:
function testClientSideBypass() {
  // Simular manipulação do frontend
  window.userHasPlan = () => false;
  window.currentSubscription = 'free';
  
  // Tentar ação que deveria ser bloqueada
  console.log('🔍 Testando bypass client-side...');
  
  // ✅ RESULTADO: APIs ainda validam server-side (seguro)
  // ⚠️ PROBLEMA: UX confusa para usuário
}
`;

console.log(clientBypassDemo);
console.log('✅ POSITIVO: APIs validam server-side adequadamente');
console.log('⚠️ MELHORIA: Sincronizar melhor frontend com backend');

// 4. DEMONSTRAÇÃO: Information Disclosure
console.log('\n🔍 VULNERABILIDADE 4: Exposição de Informações');
console.log('-'.repeat(50));

const infoDisclosureDemo = `
// CENÁRIO: Logs podem vazar informações sensíveis

// Código atual:
logger.info('[PLAN_RATE_LIMIT]', {
  userId,        // ❌ ID do usuário em logs
  userPlan,      // ❌ Plano do usuário exposto  
  path: request.nextUrl.pathname,
  method: request.method,
});

// RISCO:
// 1. Logs podem ser acessados por atacantes
// 2. Informações de planos podem ser mapeadas
// 3. IDs de usuários podem ser correlacionados

// SOLUÇÃO:
logger.info('[PLAN_RATE_LIMIT]', {
  userHash: crypto.createHash('sha256').update(userId).digest('hex').slice(0, 8),
  planTier: getPlanTier(userPlan), // 'free', 'paid' ao invés do plano específico
  path: request.nextUrl.pathname,
  method: request.method,
});
`;

console.log(infoDisclosureDemo);
console.log('❌ PROBLEMA: Logs podem vazar informações de usuários');
console.log('✅ SOLUÇÃO: Hash de IDs e categorização de planos');

// 5. TESTE DE CONECTIVIDADE
console.log('\n🔌 TESTE DE CONECTIVIDADE');
console.log('-'.repeat(50));

// Verificar se arquivos críticos existem
const criticalFiles = [
  'src/lib/subscription-limits.ts',
  'src/lib/middleware/plan-based-rate-limiter.ts',
  'src/app/api/workbook/save/route.ts',
  'src/app/pricing/page.tsx',
];

console.log('📁 Verificando arquivos críticos:');
criticalFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file}`);
  } else {
    console.log(`❌ ${file} - NÃO ENCONTRADO`);
  }
});

// 6. RESUMO DE CORREÇÕES NECESSÁRIAS
console.log('\n🛠️ CORREÇÕES NECESSÁRIAS (PRIORIDADE)');
console.log('-'.repeat(50));

const corrections = [
  {
    priority: 'CRÍTICA',
    issue: 'Race Conditions',
    solution: 'Implementar transações atômicas',
    file: 'src/lib/subscription-limits.ts',
    effort: '2-3 horas',
  },
  {
    priority: 'CRÍTICA',
    issue: 'Cache Manipulation',
    solution: 'Adicionar validação criptográfica',
    file: 'src/lib/middleware/plan-based-rate-limiter.ts',
    effort: '1-2 horas',
  },
  {
    priority: 'ALTA',
    issue: 'Rate Limiting Distribuído',
    solution: 'Implementar Redis para rate limiting',
    file: 'Novo arquivo',
    effort: '4-6 horas',
  },
  {
    priority: 'MÉDIA',
    issue: 'Information Disclosure',
    solution: 'Sanitizar logs de segurança',
    file: 'Vários arquivos',
    effort: '1-2 horas',
  },
];

corrections.forEach((correction, index) => {
  console.log(`${index + 1}. [${correction.priority}] ${correction.issue}`);
  console.log(`   📁 Arquivo: ${correction.file}`);
  console.log(`   🔧 Solução: ${correction.solution}`);
  console.log(`   ⏱️ Esforço: ${correction.effort}`);
  console.log('');
});

console.log('🎯 PRÓXIMOS PASSOS:');
console.log('1. Implementar correções críticas imediatamente');
console.log('2. Configurar ambiente de testes adequado');
console.log('3. Executar testes de regressão');
console.log('4. Validar correções em ambiente de staging');
console.log('5. Deploy gradual em produção');

console.log('\n✅ AUDITORIA CONCLUÍDA');
console.log('📊 Relatório completo: AUDITORIA_SISTEMA_PRECOS.md');
