name: 'CodeQL Analysis'

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]
  schedule:
    - cron: '0 3 * * 1' # Executa às 3h da manhã toda segunda-feira

jobs:
  analyze:
    name: <PERSON><PERSON><PERSON><PERSON> de Segurança
    runs-on: ubuntu-latest
    permissions:
      actions: read
      contents: read
      security-events: write

    strategy:
      fail-fast: false
      matrix:
        language: ['javascript']

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Inicializar CodeQL
        uses: github/codeql-action/init@v3
        with:
          languages: ${{ matrix.language }}

      - name: Realizar build automático
        uses: github/codeql-action/autobuild@v3

      - name: Realizar análise
        uses: github/codeql-action/analyze@v3
        with:
          category: '/language:${{ matrix.language }}'
