import { ExcelOperationType } from '@/types/index';

import type { CommandParserResult, ExcelOperation } from './types';

/**
 * Wrapper para facilitar a criação de resultados de parser em testes
 * Evita problemas de tipagem com null ou incompatibilidade de interfaces
 * @param success Indica se a operação foi bem-sucedida
 * @param operations Array de operações Excel
 * @param message Mensagem descritiva do resultado
 * @param error Mensagem de erro (se houver)
 * @returns Objeto CommandParserResult para uso em testes
 */
export function createTestParserResult(
  success: boolean,
  operations: ExcelOperation[],
  message: string = '',
  error: string | null = null
): CommandParserResult {
  return {
    success,
    operations,
    message,
    error,
  };
}

/**
 * Cria uma operação Excel para uso em testes
 * @param type Tipo de operação (como string ou enum)
 * @param data Dados da operação
 * @returns Objeto ExcelOperation para uso em testes
 */
export function createTestOperation(type: string | ExcelOperationType, data: any): ExcelOperation {
  // Converter string para enum se necessário
  let operationType: ExcelOperationType;

  if (typeof type === 'string') {
    // Tentar encontrar o valor do enum
    const enumValue = Object.entries(ExcelOperationType).find(
      ([key, value]) => value === type.toUpperCase() || key === type.toUpperCase()
    );

    if (!enumValue) {
      throw new Error(`Tipo de operação inválido para teste: ${type}`);
    }

    operationType = enumValue[1] as ExcelOperationType;
  } else {
    operationType = type;
  }

  return {
    type: operationType,
    data,
  };
}

/**
 * Cria uma promessa resolvida com um resultado de parser para uso em testes
 * Útil para os casos de teste onde há await no resultado do parser
 * @param success Indica se a operação foi bem-sucedida
 * @param operations Array de operações Excel
 * @param message Mensagem descritiva do resultado
 * @param error Mensagem de erro (se houver)
 * @returns Promise que resolve para um CommandParserResult
 */
export function createAsyncTestParserResult(
  success: boolean,
  operations: ExcelOperation[],
  message: string = '',
  error: string | null = null
): Promise<CommandParserResult> {
  return Promise.resolve(createTestParserResult(success, operations, message, error));
}
