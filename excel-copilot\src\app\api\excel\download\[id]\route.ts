import ExcelJS from 'exceljs';
import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';

import { logger } from '@/lib/logger';
import { prisma } from '@/server/db/client';

// Configuração do segmento de rota (formato do Next.js 14)
export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

/**
 * Sanitiza o nome do arquivo para evitar injeção de caracteres especiais
 * no cabeçalho Content-Disposition, seguindo RFC 6266
 * @see https://tools.ietf.org/html/rfc6266
 */
function sanitizeFilename(filename: string): string {
  if (!filename) return 'planilha';

  // Remove caracteres inválidos para nomes de arquivo
  const sanitized = filename
    // Remove caracteres de controle e caracteres que não são permitidos em sistemas de arquivos
    .replace(/[\\/:*?"<>|]/g, '')
    // Substitui espaços por underscores
    .replace(/\s+/g, '_')
    // Limita o tamanho
    .substring(0, 255);

  // Retorna um nome padrão se o resultado for vazio
  return sanitized || 'planilha';
}

/**
 * Gera o cabeçalho Content-Disposition com suporte a caracteres não-ASCII
 * Implementa RFC 6266 para maior compatibilidade entre navegadores
 */
function getContentDisposition(filename: string): string {
  // Sanitiza o nome do arquivo
  const sanitizedName = sanitizeFilename(filename);

  // Verifica se o nome contém apenas caracteres ASCII
  const isAscii = /^[!-~]*$/.test(sanitizedName);

  if (isAscii) {
    // Para nomes ASCII, usamos o formato simples
    return `attachment; filename="${sanitizedName}.xlsx"`;
  } else {
    // Para nomes não-ASCII, codificamos em UTF-8 e usamos filename* conforme RFC 6266
    const encodedName = encodeURIComponent(sanitizedName).replace(/%20/g, '_');
    return `attachment; filename="${sanitizedName}.xlsx"; filename*=UTF-8''${encodedName}.xlsx`;
  }
}

export async function GET(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    // Verificar autenticação
    const session = await getServerSession();
    if (!session?.user) {
      return NextResponse.json({ error: 'Não autorizado' }, { status: 401 });
    }

    const workbookId = params.id;

    // Buscar workbook do banco de dados
    const workbook = await prisma.workbook.findUnique({
      where: {
        id: workbookId,
      },
      include: {
        sheets: true,
      },
    });

    if (!workbook) {
      return NextResponse.json({ error: 'Planilha não encontrada' }, { status: 404 });
    }

    // Verificar se o usuário tem acesso a este workbook
    if (workbook.userId !== (session.user as { id?: string })?.id && !workbook.isPublic) {
      return NextResponse.json({ error: 'Acesso negado a esta planilha' }, { status: 403 });
    }

    // Criar arquivo Excel
    const excelWorkbook = new ExcelJS.Workbook();
    excelWorkbook.creator = 'Excel Copilot';
    excelWorkbook.lastModifiedBy = session.user.name || 'Usuário';
    excelWorkbook.created = new Date(workbook.createdAt);
    excelWorkbook.modified = new Date(workbook.updatedAt);

    // Adicionar cada sheet ao arquivo Excel
    for (const sheet of workbook.sheets) {
      const worksheet = excelWorkbook.addWorksheet(sheet.name);

      // Se tiver dados na sheet, adicionar ao worksheet
      if (sheet.data) {
        const sheetData = sheet.data as unknown;

        // Verificar o formato dos dados de forma segura
        if (
          sheetData &&
          typeof sheetData === 'object' &&
          'headers' in sheetData &&
          Array.isArray(sheetData.headers)
        ) {
          // Configurar cabeçalhos
          worksheet.columns = (sheetData.headers as string[]).map((header: string) => ({
            header,
            key: header,
            width: Math.max(header.length, 10),
          }));

          // Adicionar linhas de dados com verificação segura
          if ('rows' in sheetData && Array.isArray(sheetData.rows)) {
            worksheet.addRows(sheetData.rows);
          }

          // Estilizar cabeçalhos
          worksheet.getRow(1).font = { bold: true };
          worksheet.getRow(1).fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'FFE6F0FF' },
          };
        }
      }
    }

    // Gerar buffer do arquivo Excel
    const buffer = await excelWorkbook.xlsx.writeBuffer();

    // Gerar o cabeçalho Content-Disposition seguro
    const contentDisposition = getContentDisposition(workbook.name);

    // Retornar o arquivo para download
    return new NextResponse(buffer, {
      headers: {
        'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'Content-Disposition': contentDisposition,
        'Cache-Control': 'no-store, max-age=0',
      },
    });
  } catch (error) {
    logger.error('[EXCEL_DOWNLOAD_ERROR]', error);
    return NextResponse.json({ error: 'Erro ao gerar o arquivo Excel' }, { status: 500 });
  }
}
