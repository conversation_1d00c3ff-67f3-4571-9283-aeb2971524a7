/**
 * Sistema de cache para otimizar repetição de operações custosas
 *
 * Este módulo implementa um sistema de cache em memória com suporte
 * a TTL (Time-To-Live) para reduzir a carga no banco de dados
 * e melhorar a performance de operações frequentes.
 */

import { ENV } from '@/config/unified-environment';

import { logger, toError, toMetadata } from './logger';

interface CacheConfig {
  maxSize: number;
  defaultTTL: number;
  storageMode?: 'memory' | 'localStorage' | 'sessionStorage' | 'redis';
  enableMetrics?: boolean;
  enableStaleWhileRevalidate?: boolean;
  namespace?: string;
}

type CacheItem<T> = {
  data: T;
  expiry: number;
  staleUntil: number | undefined; // Expiry + stale time
  createdAt: number;
  lastAccessed: number;
};

type CacheMetrics = {
  hits: number;
  misses: number;
  evictions: number;
  expirations: number;
  size: number;
  maxSize: number;
};

// Singleton para uso no modo distribuído/Redis (implementação futura)
const _redisClient: unknown = null;

/**
 * Gerenciador de cache em memória com suporte a TTL e políticas de expiração
 */
export class CacheManager<T = unknown> {
  private cache: Map<string, CacheItem<T>>;
  private config: CacheConfig;
  private usageCount: Map<string, number>;
  private metrics: CacheMetrics;
  private storageAvailable: boolean;
  private namespace: string;

  constructor(config: Partial<CacheConfig> = {}) {
    this.cache = new Map();
    this.usageCount = new Map();
    this.namespace = config.namespace || 'app-cache';
    this.config = {
      maxSize: config.maxSize || 100, // Valor padrão de 100 itens
      defaultTTL: config.defaultTTL || ENV.CACHE?.DEFAULT_TTL * 1000 || 5 * 60 * 1000, // 5 minutos por padrão
      storageMode: config.storageMode || 'memory',
      enableMetrics: config.enableMetrics !== undefined ? config.enableMetrics : true,
      enableStaleWhileRevalidate: config.enableStaleWhileRevalidate || false,
    };

    this.metrics = {
      hits: 0,
      misses: 0,
      evictions: 0,
      expirations: 0,
      size: 0,
      maxSize: this.config.maxSize,
    };

    // Detectar se localStorage/sessionStorage estão disponíveis
    this.storageAvailable = this.isStorageAvailable();

    // Se estiver usando storage persistente, carregar o cache
    if (
      this.storageAvailable &&
      (this.config.storageMode === 'localStorage' || this.config.storageMode === 'sessionStorage')
    ) {
      this.loadFromStorage();
    }

    // Iniciar limpeza periódica
    this.scheduleCleanup();
  }

  /**
   * Verifica se o armazenamento está disponível
   */
  private isStorageAvailable(): boolean {
    // No lado do servidor, storage não está disponível
    if (typeof window === 'undefined') {
      return false;
    }

    try {
      const storage =
        this.config.storageMode === 'localStorage'
          ? window.localStorage
          : this.config.storageMode === 'sessionStorage'
            ? window.sessionStorage
            : null;

      if (!storage) return false;

      const testKey = '__storage_test__';
      storage.setItem(testKey, testKey);
      storage.removeItem(testKey);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Carrega o cache do armazenamento persistente
   */
  private loadFromStorage(): void {
    try {
      const storage =
        this.config.storageMode === 'localStorage' ? window.localStorage : window.sessionStorage;

      const cachedData = storage.getItem(`${this.namespace}_data`);
      const cachedUsage = storage.getItem(`${this.namespace}_usage`);
      const cachedMetrics = storage.getItem(`${this.namespace}_metrics`);

      if (cachedData) {
        const parsed = JSON.parse(cachedData);
        for (const [key, item] of Object.entries(parsed)) {
          this.cache.set(key, item as CacheItem<T>);
        }
      }

      if (cachedUsage) {
        const parsed = JSON.parse(cachedUsage);
        for (const [key, count] of Object.entries(parsed)) {
          this.usageCount.set(key, count as number);
        }
      }

      if (cachedMetrics && this.config.enableMetrics) {
        this.metrics = JSON.parse(cachedMetrics);
      }

      logger.debug(`Cache carregado do ${this.config.storageMode}: ${this.cache.size} itens`);
    } catch (e) {
      logger.warn(`Erro ao carregar cache do ${this.config.storageMode}:`, toMetadata(e));
    }
  }

  /**
   * Salva o cache no armazenamento persistente
   */
  private saveToStorage(): void {
    if (!this.storageAvailable) return;

    try {
      const storage =
        this.config.storageMode === 'localStorage' ? window.localStorage : window.sessionStorage;

      // Converter Map para objeto
      const cacheObj = Array.from(this.cache.entries()).reduce(
        (obj, [key, value]) => {
          obj[key] = value;
          return obj;
        },
        {} as Record<string, CacheItem<T>>
      );

      const usageObj = Array.from(this.usageCount.entries()).reduce(
        (obj, [key, value]) => {
          obj[key] = value;
          return obj;
        },
        {} as Record<string, number>
      );

      storage.setItem(`${this.namespace}_data`, JSON.stringify(cacheObj));
      storage.setItem(`${this.namespace}_usage`, JSON.stringify(usageObj));

      if (this.config.enableMetrics) {
        storage.setItem(`${this.namespace}_metrics`, JSON.stringify(this.metrics));
      }
    } catch (e) {
      logger.warn(`Erro ao salvar cache no ${this.config.storageMode}:`, toMetadata(e));
    }
  }

  /**
   * Define um item no cache
   * @param key Chave do item
   * @param value Valor a armazenar
   * @param ttl Tempo de vida em ms (opcional)
   * @param staleTime Tempo adicional para manter item "obsoleto" enquanto revalida
   */
  set(key: string, value: T, ttl?: number, staleTime?: number): void {
    // Verificar limite de tamanho antes de inserir
    if (!this.cache.has(key) && this.cache.size >= this.config.maxSize) {
      this.evictOne();
    }

    const now = Date.now();
    const expiry = now + (ttl || this.config.defaultTTL);
    const staleUntil =
      this.config.enableStaleWhileRevalidate && staleTime ? expiry + staleTime : undefined;

    this.cache.set(key, {
      data: value,
      expiry,
      staleUntil,
      createdAt: now,
      lastAccessed: now,
    });

    this.usageCount.set(key, 0);
    this.metrics.size = this.cache.size;

    // Se estiver usando storage persistente, salvar o cache
    if (
      this.storageAvailable &&
      (this.config.storageMode === 'localStorage' || this.config.storageMode === 'sessionStorage')
    ) {
      this.saveToStorage();
    }
  }

  /**
   * Obtém um item do cache
   * @param key Chave do item
   * @returns O valor armazenado ou null se não existir/expirado
   */
  get<R = T>(key: string): R | null {
    const item = this.cache.get(key);
    const now = Date.now();

    if (!item) {
      if (this.config.enableMetrics) this.metrics.misses++;
      return null;
    }

    // Atualizar timestamp de último acesso
    item.lastAccessed = now;

    // Verificar expiração
    if (item.expiry < now) {
      // Verificar se está no período stale-while-revalidate
      if (item.staleUntil && item.staleUntil > now) {
        // Se estiver no período stale, enviar a versão obsoleta enquanto revalida em background
        if (this.config.enableMetrics) this.metrics.hits++;
        logger.debug(`Cache hit (stale): ${key}`);
        return item.data as unknown as R;
      }

      this.cache.delete(key);
      this.usageCount.delete(key);

      if (this.config.enableMetrics) {
        this.metrics.misses++;
        this.metrics.expirations++;
        this.metrics.size = this.cache.size;
      }

      return null;
    }

    // Incrementar contador de uso
    const count = (this.usageCount.get(key) || 0) + 1;
    this.usageCount.set(key, count);

    if (this.config.enableMetrics) this.metrics.hits++;

    // Corrigir tipo com cast seguro
    return item.data as unknown as R;
  }

  /**
   * Obtém um item do cache com suporte a stale-while-revalidate
   * @param key Chave do item
   * @param fetcher Função para buscar dados se estiverem expirados
   * @param ttl Tempo de vida do cache em ms
   * @param staleTime Tempo para manter item stale após expiração
   */
  async getWithRevalidate<R = T>(
    key: string,
    fetcher: () => Promise<R>,
    ttl?: number,
    staleTime: number = 60000 // 1 minuto de stale por padrão
  ): Promise<R> {
    const item = this.cache.get(key);
    const now = Date.now();

    // Caso 1: Não está em cache
    if (!item) {
      if (this.config.enableMetrics) this.metrics.misses++;

      try {
        const freshData = await fetcher();
        this.set(key, freshData as unknown as T, ttl, staleTime);
        return freshData;
      } catch (error) {
        logger.error(`Erro ao buscar dados para cache (${key}):`, toError(error));
        throw error;
      }
    }

    // Atualizar timestamp de último acesso
    item.lastAccessed = now;

    // Caso 2: Item em cache, mas expirado (revalidar em background)
    if (item.expiry < now) {
      if (this.config.enableMetrics) this.metrics.hits++;

      // Se o item estiver dentro do período stale, usar versão obsoleta
      if (item.staleUntil && item.staleUntil > now) {
        // Revalidar em background
        setTimeout(async () => {
          try {
            const freshData = await fetcher();
            this.set(key, freshData as unknown as T, ttl, staleTime);
            logger.debug(`Revalidação em background concluída para: ${key}`);
          } catch (error) {
            logger.warn(`Erro na revalidação em background (${key}):`, toMetadata(error));
          }
        }, 0);

        // Retornar versão obsoleta
        logger.debug(`Retornando versão stale para: ${key}`);
        return item.data as unknown as R;
      }

      // Sem stale ou período stale excedido, buscar nova versão síncronamente
      this.cache.delete(key);
      this.usageCount.delete(key);

      if (this.config.enableMetrics) {
        this.metrics.expirations++;
        this.metrics.size = this.cache.size;
      }

      try {
        const freshData = await fetcher();
        this.set(key, freshData as unknown as T, ttl, staleTime);
        return freshData;
      } catch (error) {
        logger.error(`Erro ao buscar dados para cache (${key}):`, toError(error));
        throw error;
      }
    }

    // Caso 3: Item em cache e válido
    const count = (this.usageCount.get(key) || 0) + 1;
    this.usageCount.set(key, count);

    if (this.config.enableMetrics) this.metrics.hits++;

    return item.data as unknown as R;
  }

  /**
   * Remove um item do cache
   */
  delete(key: string): boolean {
    this.usageCount.delete(key);
    const deleted = this.cache.delete(key);

    if (deleted && this.config.enableMetrics) {
      this.metrics.size = this.cache.size;
    }

    // Se estiver usando storage persistente, salvar o cache
    if (
      deleted &&
      this.storageAvailable &&
      (this.config.storageMode === 'localStorage' || this.config.storageMode === 'sessionStorage')
    ) {
      this.saveToStorage();
    }

    return deleted;
  }

  /**
   * Limpa todo o cache
   */
  clear(): void {
    this.cache.clear();
    this.usageCount.clear();

    if (this.config.enableMetrics) {
      this.metrics.size = 0;
    }

    // Se estiver usando storage persistente, limpar o cache
    if (
      this.storageAvailable &&
      (this.config.storageMode === 'localStorage' || this.config.storageMode === 'sessionStorage')
    ) {
      try {
        const storage =
          this.config.storageMode === 'localStorage' ? window.localStorage : window.sessionStorage;

        storage.removeItem(`${this.namespace}_data`);
        storage.removeItem(`${this.namespace}_usage`);
        storage.removeItem(`${this.namespace}_metrics`);
      } catch (e) {
        logger.warn(`Erro ao limpar cache do ${this.config.storageMode}:`, toMetadata(e));
      }
    }
  }

  /**
   * Programa a limpeza periódica do cache
   */
  private scheduleCleanup(): void {
    // No servidor não podemos usar setInterval diretamente
    if (typeof window === 'undefined') {
      // Não limpar automaticamente no servidor, já que o cache é efêmero
      return;
    }

    const CLEANUP_INTERVAL = 60 * 1000; // 1 minuto

    setInterval(() => {
      this.cleanupExpired();
    }, CLEANUP_INTERVAL);
  }

  /**
   * Remove itens expirados do cache
   */
  private cleanupExpired(): void {
    const now = Date.now();
    let expiredCount = 0;

    // Usar Array.from para compatibilidade com todos os targets
    Array.from(this.cache.entries()).forEach(([key, item]) => {
      // Se tiver staleUntil, considerar esse tempo ao invés de expiry
      const expiryTime = item.staleUntil || item.expiry;

      if (expiryTime < now) {
        this.cache.delete(key);
        this.usageCount.delete(key);
        expiredCount++;
      }
    });

    if (expiredCount > 0) {
      logger.debug(`Cache cleanup: removidos ${expiredCount} itens expirados`);

      if (this.config.enableMetrics) {
        this.metrics.expirations += expiredCount;
        this.metrics.size = this.cache.size;
      }

      // Se estiver usando storage persistente, salvar o cache após limpeza
      if (
        this.storageAvailable &&
        (this.config.storageMode === 'localStorage' || this.config.storageMode === 'sessionStorage')
      ) {
        this.saveToStorage();
      }
    }
  }

  /**
   * Remove um item do cache usando política LFU (Least Frequently Used)
   */
  private evictOne(): void {
    let leastUsed: string | null = null;
    let lowestCount = Infinity;
    let oldestTime = Infinity;

    // Encontrar o item menos usado e mais antigo como desempate
    Array.from(this.cache.entries()).forEach(([key, item]) => {
      const count = this.usageCount.get(key) || 0;

      // Priorizar o menos usado
      if (count < lowestCount) {
        lowestCount = count;
        leastUsed = key;
        oldestTime = item.createdAt;
      }
      // Em caso de empate, escolher o mais antigo
      else if (count === lowestCount && item.createdAt < oldestTime) {
        leastUsed = key;
        oldestTime = item.createdAt;
      }
    });

    // Remover o item menos usado
    if (leastUsed) {
      logger.debug(`Cache eviction: removido item "${leastUsed}" (usado ${lowestCount} vezes)`);
      this.cache.delete(leastUsed);
      this.usageCount.delete(leastUsed);

      if (this.config.enableMetrics) {
        this.metrics.evictions++;
        this.metrics.size = this.cache.size;
      }
    }
  }

  /**
   * Obtém estatísticas do cache
   */
  getStats(): {
    size: number;
    maxSize: number;
    hitRate: number;
    hits: number;
    misses: number;
    evictions: number;
    expirations: number;
  } {
    const { hits, misses, evictions, expirations, size, maxSize } = this.metrics;
    const totalAccesses = hits + misses;
    const hitRate = totalAccesses > 0 ? hits / totalAccesses : 0;

    return {
      size,
      maxSize,
      hitRate,
      hits,
      misses,
      evictions,
      expirations,
    };
  }

  /**
   * Pré-aquece o cache com dados iniciais
   * @param entries Array de entradas [chave, valor]
   * @param ttl TTL para os itens (opcional)
   */
  prefill(entries: Array<[string, T]>, ttl?: number): void {
    for (const [key, value] of entries) {
      this.set(key, value, ttl);
    }

    logger.info(`Cache prefill: ${entries.length} itens adicionados`);
  }
}

// Cache singleton para diferentes tipos de conteúdo
export const excelCache = new CacheManager<Blob | ArrayBuffer>({
  maxSize: ENV.CACHE?.EXCEL_CACHE_SIZE || 50,
  defaultTTL: (ENV.CACHE?.EXCEL_CACHE_TTL || 1800) * 1000,
  namespace: 'excel-files',
});

export const aiResponseCache = new CacheManager<string>({
  maxSize: ENV.CACHE?.AI_CACHE_SIZE || 200,
  defaultTTL: (ENV.CACHE?.AI_CACHE_TTL || 86400) * 1000,
  enableStaleWhileRevalidate: true,
  namespace: 'ai-responses',
});

export const dataCache = new CacheManager({
  maxSize: 500, // Valor padrão de 500 itens
  defaultTTL: (ENV.CACHE?.DEFAULT_TTL || 300) * 1000,
  namespace: 'app-data',
});

/**
 * Implementação server-side do cache distribuído
 * Para uso em API routes e server components
 */
export class ServerCache {
  private static instance: ServerCache;
  private isReady: boolean = false;

  private constructor() {
    // Inicializar cache do servidor
    // Em uma implementação real, isso conectaria ao Redis ou outro cache distribuído
    this.isReady = true;
  }

  public static getInstance(): ServerCache {
    if (!ServerCache.instance) {
      ServerCache.instance = new ServerCache();
    }
    return ServerCache.instance;
  }

  /**
   * Armazena um valor no cache do servidor
   */
  public async set<T>(key: string, value: T, ttlSeconds = 300): Promise<void> {
    // Usar redis ou outro cache distribuído em produção
    if (typeof window !== 'undefined') {
      throw new Error('ServerCache só pode ser usado no lado do servidor');
    }

    // Implementação futura com Redis ou outro cache distribuído
    logger.debug(`[ServerCache] Cache set: ${key} (${ttlSeconds}s)`);
  }

  /**
   * Busca um valor no cache do servidor
   */
  public async get<T>(key: string): Promise<T | null> {
    if (typeof window !== 'undefined') {
      throw new Error('ServerCache só pode ser usado no lado do servidor');
    }

    // Implementação futura com Redis ou outro cache distribuído
    logger.debug(`[ServerCache] Cache get: ${key}`);
    return null;
  }

  /**
   * Remove um valor do cache do servidor
   */
  public async delete(key: string): Promise<void> {
    if (typeof window !== 'undefined') {
      throw new Error('ServerCache só pode ser usado no lado do servidor');
    }

    // Implementação futura com Redis ou outro cache distribuído
    logger.debug(`[ServerCache] Cache delete: ${key}`);
  }
}

// API genérica para uso simplificado do cache
export function withCache<T>(
  key: string,
  fetcher: () => Promise<T>,
  ttlSeconds = ENV.CACHE?.DEFAULT_TTL || 300
): Promise<T> {
  // Verificar cache primeiro
  const cached = dataCache.get<T>(key);
  if (cached !== null) return Promise.resolve(cached);

  // Se não estiver em cache, buscar dados
  return fetcher().then(data => {
    dataCache.set(key, data, ttlSeconds * 1000);
    return data;
  });
}

/**
 * API com suporte a stale-while-revalidate
 */
export function withSWRCache<T>(
  key: string,
  fetcher: () => Promise<T>,
  ttlSeconds = ENV.CACHE?.DEFAULT_TTL || 300,
  staleSeconds = 60 // 1 minuto stale por padrão
): Promise<T> {
  return dataCache.getWithRevalidate(key, fetcher, ttlSeconds * 1000, staleSeconds * 1000);
}

/**
 * Cache para o servidor
 */
export async function withServerCache<T>(
  key: string,
  fetcher: () => Promise<T>,
  ttlSeconds = ENV.CACHE?.DEFAULT_TTL || 300
): Promise<T> {
  if (typeof window !== 'undefined') {
    return withCache(key, fetcher, ttlSeconds);
  }

  const serverCache = ServerCache.getInstance();
  const cachedData = await serverCache.get<T>(key);

  if (cachedData !== null) {
    return cachedData;
  }

  const data = await fetcher();
  await serverCache.set(key, data, ttlSeconds);
  return data;
}

export function setCache<T>(
  key: string,
  value: T,
  ttlSeconds = ENV.CACHE?.DEFAULT_TTL || 300
): void {
  dataCache.set(key, value, ttlSeconds * 1000);
}

export function getCache<T>(key: string): T | null {
  return dataCache.get<T>(key);
}

export function invalidateCache(key: string): void {
  dataCache.delete(key);
}

export function clearCache(): void {
  dataCache.clear();
}

// Exportar instância singleton para uso global
export const serverCache = ServerCache.getInstance();
