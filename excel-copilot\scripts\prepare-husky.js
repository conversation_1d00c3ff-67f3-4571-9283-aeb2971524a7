#!/usr/bin/env node

/**
 * Script para configurar <PERSON><PERSON> condicionalmente
 * Pula a instalação em ambientes onde git não está disponível (como Vercel)
 */

const { execSync } = require('child_process');

// Função para colorir texto sem dependência do chalk
function colorText(text, color) {
  const colors = {
    blue: '\x1b[34m',
    yellow: '\x1b[33m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    gray: '\x1b[90m',
    reset: '\x1b[0m',
  };
  return `${colors[color] || ''}${text}${colors.reset}`;
}

function isGitAvailable() {
  try {
    execSync('git --version', { stdio: 'ignore' });
    return true;
  } catch {
    return false;
  }
}

function isGitRepository() {
  try {
    execSync('git rev-parse --git-dir', { stdio: 'ignore' });
    return true;
  } catch {
    return false;
  }
}

function isVercelEnvironment() {
  return !!(
    process.env.VERCEL ||
    process.env.VERCEL_ENV ||
    process.env.NOW_REGION ||
    process.env.VERCEL_URL
  );
}

function isCIEnvironment() {
  return !!(
    process.env.CI ||
    process.env.CONTINUOUS_INTEGRATION ||
    process.env.BUILD_NUMBER ||
    process.env.GITHUB_ACTIONS ||
    process.env.GITLAB_CI ||
    process.env.CIRCLECI
  );
}

function main() {
  console.log(colorText('🐶 Verificando se Husky deve ser instalado...', 'blue'));

  // Verificar se estamos em ambiente de CI/CD
  if (isVercelEnvironment()) {
    console.log(colorText('⚠️  Ambiente Vercel detectado. Pulando instalação do Husky.', 'yellow'));
    console.log(colorText('   Git hooks não são necessários durante o build de produção.', 'gray'));
    return;
  }

  if (isCIEnvironment()) {
    console.log(colorText('⚠️  Ambiente CI/CD detectado. Pulando instalação do Husky.', 'yellow'));
    console.log(
      colorText('   Git hooks não são necessários durante builds automatizados.', 'gray')
    );
    return;
  }

  // Verificar se git está disponível
  if (!isGitAvailable()) {
    console.log(colorText('❌ Git não está disponível. Pulando instalação do Husky.', 'red'));
    console.log(colorText('   Instale o Git para usar hooks de pre-commit.', 'gray'));
    return;
  }

  // Verificar se estamos em um repositório git
  if (!isGitRepository()) {
    console.log(colorText('⚠️  Não é um repositório Git. Pulando instalação do Husky.', 'yellow'));
    console.log(colorText('   Execute "git init" para inicializar um repositório.', 'gray'));
    return;
  }

  // Tudo ok, instalar Husky
  try {
    console.log(colorText('✅ Instalando Husky...', 'green'));
    execSync('husky install', { stdio: 'inherit' });
    console.log(colorText('🎉 Husky instalado com sucesso!', 'green'));
  } catch (error) {
    console.error(colorText('❌ Erro ao instalar Husky:', 'red'), error.message);
    process.exit(1);
  }
}

main();
