import crypto from 'crypto';

import { NextRequest, NextResponse } from 'next/server';

import { ENV } from '@/config/unified-environment';
import { logger } from '@/lib/logger';
import { VercelClient } from '@/lib/vercel-integration';

// Variáveis críticas para resolver o problema de autenticação
const CRITICAL_ENV_VARS = [
  {
    key: 'AUTH_NEXTAUTH_SECRET',
    value: crypto.randomBytes(32).toString('base64'),
    type: 'encrypted' as const,
    target: ['production', 'preview'] as const,
    description: 'Chave secreta para NextAuth',
  },
  {
    key: 'AUTH_NEXTAUTH_URL',
    value: 'https://excel-copilot-eight.vercel.app',
    type: 'plain' as const,
    target: ['production', 'preview'] as const,
    description: 'URL base para NextAuth',
  },
  {
    key: 'AUTH_GOOGLE_CLIENT_ID',
    value: '217111050148-1gocm6a0sa9jcrk8s08dubqn8n2001lv.apps.googleusercontent.com',
    type: 'plain' as const,
    target: ['production', 'preview'] as const,
    description: 'Google OAuth Client ID',
  },
  {
    key: 'AUTH_GITHUB_CLIENT_ID',
    value: 'c5d97a325b78e452d671',
    type: 'plain' as const,
    target: ['production', 'preview'] as const,
    description: 'GitHub OAuth Client ID',
  },
  {
    key: 'AI_USE_MOCK',
    value: 'false',
    type: 'plain' as const,
    target: ['production', 'preview'] as const,
    description: 'Desativar mocks de IA em produção',
  },
  {
    key: 'NEXT_PUBLIC_USE_MOCK_AI',
    value: 'false',
    type: 'plain' as const,
    target: ['production', 'preview'] as const,
    description: 'Desativar mocks de IA no cliente',
  },
  {
    key: 'AUTH_SKIP_PROVIDERS',
    value: 'false',
    type: 'plain' as const,
    target: ['production', 'preview'] as const,
    description: 'Não pular providers OAuth',
  },
  {
    key: 'AI_USE_MOCK',
    value: 'false',
    type: 'plain' as const,
    target: ['production', 'preview'] as const,
    description: 'Não forçar mocks do Google',
  },
  {
    key: 'NODE_ENV',
    value: 'production',
    type: 'plain' as const,
    target: ['production'] as const,
    description: 'Ambiente de produção',
  },
  {
    key: 'DEV_FORCE_PRODUCTION',
    value: 'true',
    type: 'plain' as const,
    target: ['production', 'preview'] as const,
    description: 'Forçar modo produção',
  },
  {
    key: 'AI_ENABLED',
    value: 'true',
    type: 'plain' as const,
    target: ['production', 'preview'] as const,
    description: 'Habilitar Vertex AI',
  },
  {
    key: 'AI_VERTEX_PROJECT_ID',
    value: 'excel-copilot',
    type: 'plain' as const,
    target: ['production', 'preview'] as const,
    description: 'ID do projeto Vertex AI',
  },
  {
    key: 'AI_VERTEX_LOCATION',
    value: 'us-central1',
    type: 'plain' as const,
    target: ['production', 'preview'] as const,
    description: 'Localização do Vertex AI',
  },
  {
    key: 'AI_VERTEX_MODEL',
    value: 'gemini-1.5-pro',
    type: 'plain' as const,
    target: ['production', 'preview'] as const,
    description: 'Modelo do Vertex AI',
  },
  {
    key: 'NEXT_PUBLIC_APP_URL',
    value: 'https://excel-copilot-eight.vercel.app',
    type: 'plain' as const,
    target: ['production', 'preview'] as const,
    description: 'URL da aplicação',
  },
  {
    key: 'APP_VERSION',
    value: '1.0.0',
    type: 'plain' as const,
    target: ['production', 'preview'] as const,
    description: 'Versão da aplicação',
  },
];

// Variáveis que precisam ser configuradas manualmente
const MANUAL_VARS = [
  'AUTH_GOOGLE_CLIENT_SECRET',
  'AUTH_GITHUB_CLIENT_SECRET',
  'STRIPE_SECRET_KEY',
  'NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY',
  'STRIPE_WEBHOOK_SECRET',
  'VERTEX_AI_CREDENTIALS',
];

/**
 * GET /api/vercel/env - Lista variáveis de ambiente
 */
export async function GET() {
  try {
    // Verificar se temos as credenciais necessárias
    const vercelToken = ENV.VERCEL_API_TOKEN;
    const projectId = ENV.VERCEL_PROJECT_ID || 'excel-copilot-eight';

    if (!vercelToken) {
      return NextResponse.json(
        {
          success: false,
          error: 'VERCEL_API_TOKEN não configurado',
          message: 'Configure o token da Vercel para usar esta funcionalidade',
        },
        { status: 400 }
      );
    }

    const clientConfig: { apiToken: string; projectId: string; teamId?: string } = {
      apiToken: vercelToken,
      projectId: projectId,
    };

    if (ENV.VERCEL_TEAM_ID) {
      clientConfig.teamId = ENV.VERCEL_TEAM_ID;
    }

    const client = new VercelClient(clientConfig);

    const envVars = await client.getEnvironmentVariables();
    const existingKeys = envVars.map(env => env.key);

    // Verificar quais variáveis críticas estão faltando
    const missingCritical = CRITICAL_ENV_VARS.filter(v => !existingKeys.includes(v.key));
    const missingManual = MANUAL_VARS.filter(key => !existingKeys.includes(key));

    return NextResponse.json({
      success: true,
      data: {
        total: envVars.length,
        existing: existingKeys,
        missing: {
          critical: missingCritical.map(v => ({ key: v.key, description: v.description })),
          manual: missingManual,
        },
        status: {
          criticalConfigured: CRITICAL_ENV_VARS.length - missingCritical.length,
          criticalTotal: CRITICAL_ENV_VARS.length,
          manualConfigured: MANUAL_VARS.length - missingManual.length,
          manualTotal: MANUAL_VARS.length,
          readyForAuth: missingCritical.length === 0 && missingManual.length <= 2, // Permitir até 2 manuais faltando
        },
      },
    });
  } catch (error) {
    logger.error('Erro ao listar variáveis de ambiente', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Erro interno',
        message: error instanceof Error ? error.message : 'Erro desconhecido',
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/vercel/env - Configura variáveis de ambiente críticas
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, variables } = body;

    // Verificar se temos as credenciais necessárias
    const vercelToken = ENV.VERCEL_API_TOKEN;
    const projectId = ENV.VERCEL_PROJECT_ID || 'excel-copilot-eight';

    if (!vercelToken) {
      return NextResponse.json(
        {
          success: false,
          error: 'VERCEL_API_TOKEN não configurado',
          message: 'Configure o token da Vercel para usar esta funcionalidade',
        },
        { status: 400 }
      );
    }

    const clientConfig2: { apiToken: string; projectId: string; teamId?: string } = {
      apiToken: vercelToken,
      projectId: projectId,
    };

    if (ENV.VERCEL_TEAM_ID) {
      clientConfig2.teamId = ENV.VERCEL_TEAM_ID;
    }

    const client = new VercelClient(clientConfig2);

    if (action === 'configure-critical') {
      // Configurar todas as variáveis críticas
      logger.info('Configurando variáveis críticas para resolver problema de autenticação');

      const result = await client.setMultipleEnvironmentVariables(
        CRITICAL_ENV_VARS.map(v => ({
          key: v.key,
          value: v.value,
          target: [...v.target] as ('production' | 'preview' | 'development')[], // Converter readonly array para mutable array
          type: v.type,
        }))
      );

      return NextResponse.json({
        success: result.success,
        data: {
          summary: result.summary,
          results: result.results,
          nextSteps: [
            'Configure GOOGLE_CLIENT_SECRET no painel da Vercel',
            'Configure GITHUB_CLIENT_SECRET no painel da Vercel',
            'Atualize URLs de callback nos consoles OAuth',
            'Faça redeploy do projeto',
          ],
        },
        message: `Configuração concluída: ${result.summary.configured} configuradas, ${result.summary.skipped} puladas, ${result.summary.failed} falharam`,
      });
    } else if (action === 'configure-custom' && variables) {
      // Configurar variáveis customizadas
      const result = await client.setMultipleEnvironmentVariables(variables);

      return NextResponse.json({
        success: result.success,
        data: {
          summary: result.summary,
          results: result.results,
        },
        message: `Configuração customizada: ${result.summary.configured} configuradas, ${result.summary.failed} falharam`,
      });
    } else if (action === 'update-existing' && variables) {
      // Atualizar variáveis existentes (remover e recriar)
      const results: Array<{ key: string; success: boolean; message: string }> = [];
      let updated = 0;
      let failed = 0;

      // Obter variáveis existentes
      const existingVars = await client.getEnvironmentVariables();

      for (const variable of variables) {
        try {
          // Encontrar a variável existente
          const existingVar = existingVars.find(env => env.key === variable.key);

          if (existingVar && existingVar.id) {
            // Remover a variável existente
            const deleteResult = await client.deleteEnvironmentVariable(existingVar.id);

            if (deleteResult.success) {
              // Aguardar um pouco antes de recriar
              await new Promise(resolve => setTimeout(resolve, 500));

              // Recriar com novo valor
              const createResult = await client.setEnvironmentVariable(
                variable.key,
                variable.value,
                variable.target || ['production', 'preview'],
                variable.type || 'plain'
              );

              if (createResult.success) {
                results.push({
                  key: variable.key,
                  success: true,
                  message: 'Atualizada com sucesso',
                });
                updated++;
              } else {
                results.push({
                  key: variable.key,
                  success: false,
                  message: `Erro ao recriar: ${createResult.message}`,
                });
                failed++;
              }
            } else {
              results.push({
                key: variable.key,
                success: false,
                message: `Erro ao remover: ${deleteResult.message}`,
              });
              failed++;
            }
          } else {
            // Variável não existe, criar nova
            const createResult = await client.setEnvironmentVariable(
              variable.key,
              variable.value,
              variable.target || ['production', 'preview'],
              variable.type || 'plain'
            );

            results.push({
              key: variable.key,
              success: createResult.success,
              message: createResult.success ? 'Criada com sucesso' : createResult.message,
            });

            if (createResult.success) {
              updated++;
            } else {
              failed++;
            }
          }

          // Delay para evitar rate limiting
          await new Promise(resolve => setTimeout(resolve, 300));
        } catch (error) {
          results.push({
            key: variable.key,
            success: false,
            message: `Erro: ${error instanceof Error ? error.message : 'Erro desconhecido'}`,
          });
          failed++;
        }
      }

      return NextResponse.json({
        success: failed === 0,
        data: {
          summary: { updated, failed, total: variables.length },
          results,
        },
        message: `Atualização concluída: ${updated} atualizadas, ${failed} falharam`,
      });
    } else {
      return NextResponse.json(
        {
          success: false,
          error: 'Ação inválida',
          message: 'Use action: "configure-critical" ou "configure-custom" com variables',
        },
        { status: 400 }
      );
    }
  } catch (error) {
    logger.error('Erro ao configurar variáveis de ambiente', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Erro interno',
        message: error instanceof Error ? error.message : 'Erro desconhecido',
      },
      { status: 500 }
    );
  }
}
