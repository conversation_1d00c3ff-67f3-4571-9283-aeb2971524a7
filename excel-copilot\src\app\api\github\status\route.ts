import { NextRequest } from 'next/server';

import { GitHubMonitoringService } from '@/lib/github-integration';
import { logger } from '@/lib/logger';
import { ApiResponse } from '@/utils/api-response';

// Forçar o modo dinâmico para essa rota
export const dynamic = 'force-dynamic';

/**
 * GET /api/github/status
 * Obtém status geral da integração GitHub
 */
export async function GET(_request: NextRequest) {
  try {
    // Verificar se temos as credenciais necessárias
    const token = process.env.MCP_GITHUB_TOKEN;
    const owner = process.env.MCP_GITHUB_OWNER;
    const repo = process.env.MCP_GITHUB_REPO;

    if (!token) {
      return ApiResponse.error('GITHUB_TOKEN não configurado', 'GITHUB_NOT_CONFIGURED', 500);
    }

    // Criar instância do serviço de monitoramento
    const githubService = new GitHubMonitoringService({
      token,
      ...(owner && { owner }),
      ...(repo && { repo }),
    });

    // Obter status do repositório se owner/repo estão configurados
    let repositoryDashboard = null;
    if (owner && repo) {
      try {
        repositoryDashboard = await githubService.getRepositoryDashboard(owner, repo);
      } catch (error) {
        logger.warn('Erro ao obter dashboard do repositório:', error);
      }
    }

    // Obter métricas de CI/CD se disponível
    let cicdMetrics = null;
    if (owner && repo) {
      try {
        cicdMetrics = await githubService.getCICDMetrics(owner, repo);
      } catch (error) {
        logger.warn('Erro ao obter métricas de CI/CD:', error);
      }
    }

    const response = {
      status: 'healthy',
      configured: {
        token: !!token,
        owner: !!owner,
        repo: !!repo,
      },
      repository: repositoryDashboard
        ? {
            name: repositoryDashboard.repository.full_name,
            description: repositoryDashboard.repository.description,
            language: repositoryDashboard.repository.language,
            stars: repositoryDashboard.repository.stargazers_count,
            forks: repositoryDashboard.repository.forks_count,
            openIssues: repositoryDashboard.openIssues,
            openPullRequests: repositoryDashboard.openPullRequests,
            healthStatus: repositoryDashboard.healthStatus,
            lastUpdate: repositoryDashboard.repository.updated_at,
          }
        : null,
      cicd: cicdMetrics
        ? {
            totalRuns: cicdMetrics.totalRuns,
            successRate: cicdMetrics.successRate,
            averageDuration: cicdMetrics.averageDuration,
            recentFailures: cicdMetrics.recentFailures.length,
          }
        : null,
      timestamp: new Date().toISOString(),
    };

    logger.info('Status GitHub obtido com sucesso', {
      hasRepository: !!repositoryDashboard,
      hasCICD: !!cicdMetrics,
    });

    return ApiResponse.success(response);
  } catch (error) {
    logger.error('Erro ao obter status do GitHub', { error });

    if (error instanceof Error) {
      return ApiResponse.error(
        `Erro ao conectar com GitHub: ${error.message}`,
        'GITHUB_API_ERROR',
        500
      );
    }

    return ApiResponse.error('Erro interno do servidor', 'INTERNAL_ERROR', 500);
  }
}

/**
 * POST /api/github/status
 * Força uma verificação de status (útil para debugging)
 */
export async function POST(_request: NextRequest) {
  try {
    const token = process.env.MCP_GITHUB_TOKEN;
    const owner = process.env.MCP_GITHUB_OWNER;
    const repo = process.env.MCP_GITHUB_REPO;

    if (!token) {
      return ApiResponse.error('GITHUB_TOKEN não configurado', 'GITHUB_NOT_CONFIGURED', 500);
    }

    const githubService = new GitHubMonitoringService({
      token,
      ...(owner && { owner }),
      ...(repo && { repo }),
    });

    // Verificar health check detalhado
    const healthCheck = await githubService.getRepositoryDashboard();

    const response = {
      status: 'forced_check_completed',
      healthCheck,
      timestamp: new Date().toISOString(),
    };

    logger.info('Verificação forçada de status GitHub concluída');

    return ApiResponse.success(response);
  } catch (error) {
    logger.error('Erro na verificação forçada do GitHub', { error });

    if (error instanceof Error) {
      return ApiResponse.error(`Erro na verificação: ${error.message}`, 'GITHUB_CHECK_ERROR', 500);
    }

    return ApiResponse.error('Erro interno do servidor', 'INTERNAL_ERROR', 500);
  }
}
