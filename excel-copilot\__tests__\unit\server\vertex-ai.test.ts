/**
 * @jest-environment node
 */

// Usando import com caminho relativo para evitar problemas de resolução
import { GeminiService } from '../../../src/server/ai/gemini-service';

// Chave falsa para testes
const FAKE_API_KEY = 'fake-api-key';

// Mocks para streams que são compatíveis com o Node.js
const mockNodeCallback = jest.fn();
const mockNodeStream = {
  setEncoding: jest.fn(),
  on: jest.fn().mockImplementation((event, callback) => {
    if (event === 'data') {
      callback(
        JSON.stringify({
          candidates: [
            {
              content: {
                parts: [{ text: 'Mock response from stream' }],
              },
            },
          ],
        })
      );
      // Executa o callback mockado para testes
      mockNodeCallback('Mock response from stream');
    }
    if (event === 'end') {
      callback();
    }
    return { on: jest.fn() };
  }),
};

// Mock do módulo do Google Vertex AI
const mockSendMessage = jest.fn();
const mockChatStream = jest.fn();
const mockGenerateContent = jest.fn();

// Mock do stream para streamable responses
const mockReadableFromReadableStreamReadable = jest.fn().mockReturnValue(mockNodeStream);

// Mock do módulo readable-stream
jest.mock('readable-stream', () => ({
  Readable: {
    fromWeb: mockReadableFromReadableStreamReadable,
  },
}));

// Mock simples do módulo @google-cloud/vertexai
jest.mock('@google-cloud/vertexai', () => ({
  VertexAI: jest.fn().mockImplementation(() => ({
    preview: {
      getGenerativeModel: jest.fn().mockImplementation(() => ({
        startChat: jest.fn().mockImplementation(() => ({
          sendMessage: mockSendMessage,
          sendMessageStream: mockChatStream,
        })),
        generateContent: mockGenerateContent,
      })),
    },
  })),
}));

// Mock para fs
jest.mock('fs', () => ({
  existsSync: jest.fn().mockReturnValue(true),
  readFileSync: jest.fn().mockReturnValue(
    JSON.stringify({
      project_id: 'teste-project',
      client_email: '<EMAIL>',
      private_key: '-----BEGIN PRIVATE KEY-----\nMock Key\n-----END PRIVATE KEY-----',
    })
  ),
}));

// Mock para path
jest.mock('path', () => ({
  join: jest.fn().mockReturnValue('/mock/path/vertex-credentials.json'),
}));

// Mock para ambiente
jest.mock('../../../src/config/environment', () => ({
  ENV: {
    FEATURES: {
      USE_MOCK_AI: false,
    },
    VERTEX_AI: {
      ENABLED: true,
      CREDENTIALS_PATH: '',
      PROJECT_ID: 'test-project-id',
      LOCATION: 'us-central1',
      MODEL_NAME: 'gemini-2.0-flash-001',
    },
    CACHE: {
      AI_CACHE_TTL: 3600,
    },
  },
}));

// Mock para logger
jest.mock('../../../src/lib/logger', () => ({
  logger: {
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
    debug: jest.fn(),
  },
}));

describe('GeminiService com Vertex AI', () => {
  // Teste streams e leitores para limpar
  const streamsToClean: any[] = [];
  const readersToClean: any[] = [];

  beforeEach(() => {
    jest.clearAllMocks();
  });

  // Limpar recursos após cada teste
  afterEach(async () => {
    // Fechar todos os readers abertos
    for (const reader of readersToClean) {
      try {
        if (reader && typeof reader.cancel === 'function') {
          await reader.cancel();
        }
      } catch (e) {
        // Ignorar erros ao limpar
      }
    }
    readersToClean.length = 0;
    streamsToClean.length = 0;
  });

  // Limpar todos os recursos após os testes
  afterAll(async () => {
    // Desligar o serviço GeminiService
    try {
      const service = GeminiService.getInstance();
      await service.shutdown();
    } catch (e) {
      // Ignorar erros ao desligar
    }

    // Limpar todos os timers
    jest.useRealTimers();
  });

  describe('Inicialização', () => {
    it('deve inicializar corretamente o serviço', () => {
      const geminiService = GeminiService.getInstance();
      expect(geminiService).toBeDefined();
    });
  });

  describe('Envio de mensagens', () => {
    it('deve enviar mensagem e receber resposta', async () => {
      const geminiService = GeminiService.getInstance();

      // Definir propriedades privadas para testes
      const geminiServicePrivate = geminiService as any;

      // Forçar inicialização do vertexModel se necessário
      if (!geminiServicePrivate.vertexModel) {
        // Criar mock do modelo generativo
        const mockGenerativeModel = {
          generateContent: jest.fn().mockResolvedValue({
            response: {
              candidates: [
                {
                  content: {
                    parts: [{ text: 'Resposta simulada do Vertex AI' }],
                  },
                },
              ],
            },
          }),
          generateContentStream: jest.fn().mockResolvedValue({
            stream: (async function* () {
              yield { candidates: [{ content: { parts: [{ text: 'Chunk test' }] } }] };
            })(),
          }),
        };

        // Mock do VertexAI
        geminiServicePrivate.vertexAI = {
          preview: {
            getGenerativeModel: jest.fn().mockReturnValue(mockGenerativeModel),
          },
        };

        geminiServicePrivate.vertexModel =
          geminiServicePrivate.vertexAI.preview.getGenerativeModel();
      }

      const resposta = await geminiService.sendMessage('Olá, como você está?');
      expect(resposta).toBeTruthy();
    });
  });

  describe('Streaming', () => {
    it('deve retornar um stream válido', async () => {
      const geminiService = GeminiService.getInstance();

      // Definir propriedades privadas para testes
      const geminiServicePrivate = geminiService as any;

      // Forçar inicialização do vertexModel se necessário
      if (!geminiServicePrivate.vertexModel) {
        // Criar um mockStream explícito para simular o comportamento real
        const mockStreamGenerator = async function* () {
          yield { candidates: [{ content: { parts: [{ text: 'Chunk 1 ' }] } }] };
          yield { candidates: [{ content: { parts: [{ text: 'Chunk 2' }] } }] };
        };

        // Criar o modelo generativo mockado
        const mockGenerativeModel = {
          generateContent: jest.fn().mockResolvedValue({
            response: {
              candidates: [{ content: { parts: [{ text: 'Resposta padrão' }] } }],
            },
          }),
          generateContentStream: jest.fn().mockResolvedValue({
            stream: mockStreamGenerator(),
          }),
        };

        geminiServicePrivate.vertexAI = {
          preview: {
            getGenerativeModel: jest.fn().mockReturnValue(mockGenerativeModel),
          },
        };

        geminiServicePrivate.vertexModel =
          geminiServicePrivate.vertexAI.preview.getGenerativeModel();
      }

      const stream = await geminiService.streamMessage('Olá, como você está?');
      expect(stream).toBeInstanceOf(ReadableStream);

      // Registrar stream para limpeza
      streamsToClean.push(stream);

      // Ler e verificar conteúdo rapidamente
      const reader = stream.getReader();
      readersToClean.push(reader);

      // Ler apenas o primeiro chunk para confirmar funcionamento
      const result = await reader.read();
      // Verificamos se o resultado existe, sem acessar propriedades específicas para evitar erros de tipo
      expect(result).toBeDefined();

      // Cancelar o reader para evitar problemas de recursos não liberados
      await reader.cancel();
    });
  });
});
