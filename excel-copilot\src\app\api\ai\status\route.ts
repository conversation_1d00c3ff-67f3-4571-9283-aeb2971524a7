import { NextResponse } from 'next/server';

import { ENV } from '@/config/unified-environment';

/**
 * GET /api/ai/status
 * Retorna o status atual da integração com a IA
 */
export async function GET() {
  try {
    const useMockAI = ENV.FEATURES.USE_MOCK_AI;
    const vertexEnabled = ENV.VERTEX_AI.ENABLED;
    const vertexProjectId = ENV.VERTEX_AI.PROJECT_ID;

    return NextResponse.json({
      status: 'success',
      useMockAI,
      vertexEnabled,
      hasVertexConfig: !!vertexProjectId,
      // Fornecer informações adicionais para ajudar na configuração
      aiProvider: 'Google Vertex AI',
      aiModel: ENV.VERTEX_AI.MODEL_NAME || 'gemini-1.5-pro',
      mockEnabled: useMockAI,
      // Adicionar timestamp para evitar cache
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Erro ao obter status da IA:', error);

    return NextResponse.json(
      {
        status: 'error',
        message:
          error instanceof Error ? error.message : 'Erro desconhecido ao verificar o status da IA',
      },
      { status: 500 }
    );
  }
}
