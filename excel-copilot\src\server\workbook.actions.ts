'use server';

import { revalidatePath } from 'next/cache';

// Log e serviços
import { logger } from '@/lib/logger';
import { getTypedSession } from '@/lib/session-helpers';
import { createWorkbookValidator, deleteWorkbookValidator } from '@/lib/validators/workbook';
import { prisma } from '@/server/db/client';
import { createWorkbookInput } from '@/server/db/utils';
// Definindo os tipos diretamente para evitar problemas de importação
interface SheetWithData {
  id?: string;
  name: string;
  data: Record<string, unknown>;
  createdAt?: Date;
  updatedAt?: Date;
}

interface WorkbookWithSheets {
  id: string;
  name: string;
  description?: string;
  userId: string;
  sheets: SheetWithData[];
  isPublic: boolean;
  createdAt: Date;
  updatedAt: Date;
  lastAccessedAt?: Date;
}

/**
 * Server Action para criar uma nova planilha
 */
export async function createWorkbookAction(formData: FormData) {
  try {
    const session = await getTypedSession();
    if (!session?.user?.id) {
      return { success: false, error: 'Não autenticado' };
    }

    const data = {
      name: formData.get('name') as string,
      description: (formData.get('description') as string) || '',
    };

    // Validar dados
    const validatedData = createWorkbookValidator.safeParse(data);
    if (!validatedData.success) {
      return { success: false, error: validatedData.error.message };
    }

    // Criar workbook
    const workbook = await prisma.workbook.create({
      data: createWorkbookInput(
        validatedData.data.name,
        session.user.id,
        validatedData.data.description,
        [
          {
            name: 'Planilha 1',
            data: JSON.stringify({
              headers: [],
              rows: [],
            }),
          },
        ]
      ),
      include: {
        sheets: true,
      },
    });

    // Revalidar caminhos relacionados para atualizar SSR/SSG
    revalidatePath('/dashboard');
    revalidatePath(`/workbook/${workbook.id}`);

    return { success: true, workbook };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
    logger.error('Erro ao criar planilha:', error);
    return { success: false, error: 'Erro ao criar planilha: ' + errorMessage };
  }
}

/**
 * Server Action para excluir uma planilha
 */
export async function deleteWorkbookAction(workbookId: string) {
  try {
    const session = await getTypedSession();
    if (!session?.user?.id) {
      return { success: false, error: 'Não autenticado' };
    }

    // Validar ID
    const validatedData = deleteWorkbookValidator.safeParse({ id: workbookId });
    if (!validatedData.success) {
      return { success: false, error: validatedData.error.message };
    }

    // Verificar se o usuário é dono do workbook
    const workbook = await prisma.workbook.findUnique({
      where: { id: workbookId },
      select: { userId: true },
    });

    if (!workbook) {
      return { success: false, error: 'Planilha não encontrada' };
    }

    if (workbook.userId !== session.user.id) {
      return { success: false, error: 'Você não tem permissão para excluir esta planilha' };
    }

    // Excluir workbook e suas planilhas (cascade delete configurado no Prisma)
    await prisma.workbook.delete({
      where: { id: workbookId },
    });

    // Revalidar caminho relacionado
    revalidatePath('/dashboard');

    return { success: true };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
    logger.error('Erro ao excluir planilha:', error);
    return { success: false, error: 'Erro ao excluir planilha: ' + errorMessage };
  }
}

/**
 * Server Action para buscar planilhas do usuário
 */
export async function getWorkbooksAction() {
  try {
    const session = await getTypedSession();
    if (!session?.user?.id) {
      return { success: false, error: 'Não autenticado' };
    }

    const workbooks = await prisma.workbook.findMany({
      where: { userId: session.user.id },
      include: {
        sheets: true,
      },
      orderBy: { updatedAt: 'desc' },
    });

    return { success: true, workbooks };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
    logger.error('Erro ao buscar planilhas:', error);
    return { success: false, error: 'Erro ao buscar planilhas: ' + errorMessage };
  }
}

/**
 * Server Action para buscar uma planilha específica
 */
export async function getWorkbookAction(workbookId: string): Promise<{
  success: boolean;
  workbook?: WorkbookWithSheets;
  error?: string;
}> {
  try {
    const session = await getTypedSession();
    if (!session?.user?.id) {
      return { success: false, error: 'Não autenticado' };
    }

    const workbook = await prisma.workbook.findUnique({
      where: { id: workbookId },
      include: {
        sheets: true,
      },
    });

    if (!workbook) {
      return { success: false, error: 'Planilha não encontrada' };
    }

    if (workbook.userId !== session.user.id) {
      return { success: false, error: 'Você não tem permissão para acessar esta planilha' };
    }

    // Precisamos transformar os dados de string para objeto
    const workbookWithParsedSheets = {
      ...workbook,
      sheets: workbook.sheets.map(sheet => ({
        ...sheet,
        data: sheet.data ? JSON.parse(sheet.data) : {},
      })) as SheetWithData[],
    };

    return { success: true, workbook: workbookWithParsedSheets as WorkbookWithSheets };
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
    logger.error('Erro ao buscar planilha:', error);
    return { success: false, error: 'Erro ao buscar planilha: ' + errorMessage };
  }
}
