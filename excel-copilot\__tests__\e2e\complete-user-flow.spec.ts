import { test, expect } from '@playwright/test';

/**
 * Teste E2E completo: Registro → Dashboard → Criação → Comando IA → Colaboração
 */
test.describe('Fluxo Completo do Usuário', () => {
  test.beforeEach(async ({ page }) => {
    // Navegar para o dashboard (usuário já autenticado via setup)
    await page.goto('/dashboard');
    await page.waitForLoadState('networkidle');
  });

  test('deve completar fluxo: dashboard → criar planilha → comando IA → colaboração', async ({ page }) => {
    console.log('🚀 Iniciando teste de fluxo completo...');

    // === ETAPA 1: VERIFICAR DASHBOARD ===
    console.log('📊 Verificando dashboard...');
    
    await expect(page).toHaveTitle(/Excel Copilot/);
    await expect(page.locator('h1')).toContainText(/Bem-vindo|Dashboard/);
    
    // Verificar métricas do dashboard
    await expect(page.locator('[data-testid="total-workbooks"]')).toBeVisible();
    await expect(page.locator('[data-testid="recent-activity"]')).toBeVisible();
    
    // Verificar botão de criar planilha
    const createButton = page.locator('[data-testid="create-workbook-button"]');
    await expect(createButton).toBeVisible();
    await expect(createButton).toBeEnabled();

    // === ETAPA 2: CRIAR NOVA PLANILHA ===
    console.log('📝 Criando nova planilha...');
    
    await createButton.click();
    
    // Aguardar modal ou página de criação
    await page.waitForSelector('[data-testid="create-workbook-form"]');
    
    // Preencher formulário
    const workbookName = `E2E Test Workbook ${Date.now()}`;
    const workbookDescription = 'Planilha criada durante teste E2E automatizado';
    const aiCommand = 'Criar planilha de controle financeiro com categorias de gastos e receitas';
    
    await page.fill('[data-testid="workbook-name"]', workbookName);
    await page.fill('[data-testid="workbook-description"]', workbookDescription);
    await page.fill('[data-testid="ai-command"]', aiCommand);
    
    // Verificar se os campos foram preenchidos
    await expect(page.locator('[data-testid="workbook-name"]')).toHaveValue(workbookName);
    await expect(page.locator('[data-testid="ai-command"]')).toHaveValue(aiCommand);
    
    // Submeter formulário
    const submitButton = page.locator('[data-testid="create-workbook-submit"]');
    await expect(submitButton).toBeEnabled();
    await submitButton.click();

    // === ETAPA 3: VERIFICAR REDIRECIONAMENTO PARA EDITOR ===
    console.log('🔄 Verificando redirecionamento...');
    
    // Aguardar redirecionamento para o editor
    await page.waitForURL(/\/workbook\/[a-zA-Z0-9]+/, { timeout: 30000 });
    
    // Verificar se o comando foi passado na URL
    const currentUrl = page.url();
    expect(currentUrl).toContain('command=');
    
    // Aguardar carregamento do editor
    await page.waitForLoadState('networkidle');
    await page.waitForSelector('[data-testid="spreadsheet-editor"]', { timeout: 30000 });

    // === ETAPA 4: VERIFICAR PROCESSAMENTO DO COMANDO IA ===
    console.log('🤖 Verificando processamento de IA...');
    
    // Verificar se há indicação de processamento de IA
    const aiProcessing = page.locator('[data-testid="ai-processing"]');
    if (await aiProcessing.isVisible()) {
      console.log('⏳ Aguardando processamento de IA...');
      await aiProcessing.waitFor({ state: 'hidden', timeout: 60000 });
    }
    
    // Verificar se dados foram gerados
    const spreadsheetGrid = page.locator('[data-testid="spreadsheet-grid"]');
    await expect(spreadsheetGrid).toBeVisible();
    
    // Verificar se há células com dados (resultado da IA)
    const cellsWithData = page.locator('[data-testid^="cell-"]').filter({
      hasNotText: '',
    });
    
    const cellCount = await cellsWithData.count();
    console.log(`📊 Encontradas ${cellCount} células com dados`);
    expect(cellCount).toBeGreaterThan(0);

    // === ETAPA 5: TESTAR EDIÇÃO DE CÉLULAS ===
    console.log('✏️ Testando edição de células...');
    
    // Clicar em uma célula específica
    const targetCell = page.locator('[data-testid="cell-A1"]');
    await targetCell.click();
    
    // Verificar se a célula está selecionada
    await expect(targetCell).toHaveClass(/selected|active/);
    
    // Editar valor da célula
    const testValue = 'Teste E2E';
    await targetCell.fill(testValue);
    await page.keyboard.press('Enter');
    
    // Verificar se o valor foi salvo
    await expect(targetCell).toHaveValue(testValue);

    // === ETAPA 6: TESTAR FUNCIONALIDADES COLABORATIVAS ===
    console.log('👥 Testando recursos colaborativos...');
    
    // Verificar se há indicadores de colaboração
    const collaborationPanel = page.locator('[data-testid="collaboration-panel"]');
    if (await collaborationPanel.isVisible()) {
      await expect(collaborationPanel).toBeVisible();
      
      // Verificar presença do usuário atual
      const currentUserIndicator = page.locator('[data-testid="current-user-presence"]');
      await expect(currentUserIndicator).toBeVisible();
    }
    
    // Testar compartilhamento (se disponível)
    const shareButton = page.locator('[data-testid="share-workbook"]');
    if (await shareButton.isVisible()) {
      await shareButton.click();
      
      // Verificar modal de compartilhamento
      await expect(page.locator('[data-testid="share-modal"]')).toBeVisible();
      
      // Fechar modal
      await page.locator('[data-testid="close-share-modal"]').click();
    }

    // === ETAPA 7: SALVAR E VERIFICAR PERSISTÊNCIA ===
    console.log('💾 Verificando salvamento...');
    
    // Aguardar salvamento automático
    await page.waitForTimeout(2000);
    
    // Verificar indicador de salvamento
    const saveIndicator = page.locator('[data-testid="save-status"]');
    if (await saveIndicator.isVisible()) {
      await expect(saveIndicator).toContainText(/salvo|saved/i);
    }
    
    // Recarregar página para verificar persistência
    await page.reload();
    await page.waitForLoadState('networkidle');
    
    // Verificar se os dados persistiram
    await expect(page.locator('[data-testid="cell-A1"]')).toHaveValue(testValue);

    // === ETAPA 8: RETORNAR AO DASHBOARD ===
    console.log('🏠 Retornando ao dashboard...');
    
    // Navegar de volta ao dashboard
    await page.locator('[data-testid="back-to-dashboard"]').click();
    await page.waitForURL('/dashboard');
    
    // Verificar se a nova planilha aparece na lista
    const workbookList = page.locator('[data-testid="workbook-list"]');
    await expect(workbookList).toContainText(workbookName);

    // === ETAPA 9: LIMPEZA ===
    console.log('🧹 Limpando dados de teste...');
    
    // Encontrar e excluir a planilha de teste
    const testWorkbook = page.locator('[data-testid="workbook-item"]').filter({
      hasText: workbookName,
    });
    
    await testWorkbook.locator('[data-testid="workbook-actions"]').click();
    await page.locator('[data-testid="delete-workbook"]').click();
    await page.locator('[data-testid="confirm-delete"]').click();
    
    // Verificar se foi removida
    await expect(workbookList).not.toContainText(workbookName);
    
    console.log('✅ Teste de fluxo completo concluído com sucesso!');
  });

  test('deve lidar com erros de IA graciosamente', async ({ page }) => {
    console.log('🚨 Testando tratamento de erros de IA...');
    
    // Criar planilha com comando inválido
    await page.locator('[data-testid="create-workbook-button"]').click();
    
    const invalidCommand = 'comando completamente inválido que deveria falhar';
    await page.fill('[data-testid="workbook-name"]', 'Teste Erro IA');
    await page.fill('[data-testid="ai-command"]', invalidCommand);
    
    await page.locator('[data-testid="create-workbook-submit"]').click();
    
    // Aguardar redirecionamento
    await page.waitForURL(/\/workbook\/[a-zA-Z0-9]+/);
    
    // Verificar se há tratamento de erro
    const errorMessage = page.locator('[data-testid="ai-error"]');
    if (await errorMessage.isVisible()) {
      await expect(errorMessage).toContainText(/erro|falha|error/i);
    }
    
    // Verificar se a planilha ainda é utilizável
    await expect(page.locator('[data-testid="spreadsheet-editor"]')).toBeVisible();
    
    console.log('✅ Tratamento de erros funcionando corretamente');
  });

  test('deve funcionar em dispositivos móveis', async ({ page, isMobile }) => {
    if (!isMobile) {
      test.skip('Teste específico para mobile');
    }
    
    console.log('📱 Testando responsividade mobile...');
    
    // Verificar se o dashboard é responsivo
    await expect(page.locator('[data-testid="mobile-menu"]')).toBeVisible();
    
    // Testar criação de planilha em mobile
    await page.locator('[data-testid="mobile-create-button"]').click();
    
    // Verificar se o formulário é adequado para mobile
    const form = page.locator('[data-testid="create-workbook-form"]');
    await expect(form).toBeVisible();
    
    // Verificar se os campos são acessíveis em mobile
    await expect(page.locator('[data-testid="workbook-name"]')).toBeVisible();
    
    console.log('✅ Interface mobile funcionando corretamente');
  });
});
