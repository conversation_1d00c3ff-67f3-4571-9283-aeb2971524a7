import { NextRequest, NextResponse } from 'next/server';

export const dynamic = 'force-dynamic';

/**
 * Endpoint para debugar callbacks OAuth
 * Captura os parâmetros da URL para diagnóstico
 * NOTA: Este é um endpoint legado. O fluxo correto usa /api/auth/callback/{provider}
 */
export async function GET(request: NextRequest) {
  try {
    // Imprimir todos os parâmetros da URL
    const { searchParams } = new URL(request.url);
    const params: Record<string, string> = {};

    searchParams.forEach((value, key) => {
      params[key] = value;
    });

    // Verificar erros comuns
    const error = searchParams.get('error');
    const errorDescription = searchParams.get('error_description');

    if (error) {
      return NextResponse.redirect(
        new URL(
          `/auth/signin?error=${error}&error_description=${encodeURIComponent(
            errorDescription || ''
          )}&note=callback_path_deprecated`,
          request.url
        )
      );
    }

    // Se não houver erro, redirecionar para a página de sucesso com aviso
    return NextResponse.redirect(new URL('/dashboard?callback_path=deprecated', request.url));
  } catch {
    return NextResponse.redirect(
      new URL('/auth/signin?error=callback_error&note=callback_path_deprecated', request.url)
    );
  }
}
