#!/usr/bin/env node

/**
 * Script para atualizar a configuração do Next.js
 * Esta correção é necessária para resolver problemas de timeout na geração de páginas estáticas
 */

const fs = require('fs');
const path = require('path');

// Cores para mensagens de terminal
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
};

// Função principal
async function main() {
  console.log(
    `${colors.cyan}Excel Copilot - Atualização da Configuração do Next.js${colors.reset}`
  );
  console.log(
    `${colors.yellow}Este script irá atualizar a configuração do Next.js para resolver problemas de timeout${colors.reset}\n`
  );

  const rootDir = path.resolve(__dirname, '..');
  const configFile = path.join(rootDir, 'next.config.js');

  // Verifica se o arquivo existe
  if (!fs.existsSync(configFile)) {
    console.log(
      `${colors.red}Arquivo next.config.js não encontrado. Verifique se você está executando este script na pasta raiz do projeto.${colors.reset}`
    );
    process.exit(1);
  }

  try {
    // Lê o conteúdo do arquivo
    const content = fs.readFileSync(configFile, 'utf8');

    // Verifica se já contém a configuração de timeout
    const timeoutPattern = /staticPageGenerationTimeout:\s*\d+/;
    const hasTimeout = timeoutPattern.test(content);

    if (hasTimeout) {
      // Atualiza o valor de timeout existente
      const updatedContent = content.replace(timeoutPattern, 'staticPageGenerationTimeout: 180');

      fs.writeFileSync(configFile, updatedContent);
      console.log(`${colors.green}Valor de timeout atualizado para 180 segundos.${colors.reset}`);
    } else {
      // Adiciona a configuração de timeout
      const updatedContent = content.replace(
        /module\.exports\s*=\s*nextConfig;/,
        `// Configuração de timeout para geração de páginas estáticas
nextConfig.staticPageGenerationTimeout = 180;

module.exports = nextConfig;`
      );

      fs.writeFileSync(configFile, updatedContent);
      console.log(
        `${colors.green}Configuração de timeout adicionada com valor de 180 segundos.${colors.reset}`
      );
    }

    // Verifica e adiciona/atualiza a configuração experimental
    if (!/experimental:\s*{/.test(content)) {
      // Adiciona a seção experimental se não existir
      const updatedContent = content.replace(
        /const nextConfig\s*=\s*{/,
        `const nextConfig = {
  // Configuração experimental para desempenho
  experimental: {
    // Otimização para carregamento lento
    optimizeCss: true,
    // Restauração de scroll
    scrollRestoration: true,
    // Melhorias para estabilidade
    esmExternals: 'loose',
    // Melhorias para a fase de construção
    turbotrace: {
      logLevel: 'error',
      memoryLimit: 4000,
    },
  },`
      );

      fs.writeFileSync(configFile, updatedContent);
      console.log(
        `${colors.green}Configurações experimentais adicionadas para melhorar o desempenho.${colors.reset}`
      );
    } else {
      console.log(
        `${colors.yellow}Configurações experimentais já existem no arquivo.${colors.reset}`
      );
    }

    console.log(`\n${colors.green}Processo concluído!${colors.reset}`);
    console.log(
      `${colors.cyan}As alterações foram aplicadas com sucesso. Lembre-se de reiniciar o servidor de desenvolvimento para que as alterações tenham efeito.${colors.reset}`
    );
  } catch (error) {
    console.error(`${colors.red}Erro ao processar o arquivo next.config.js:${colors.reset}`, error);
    process.exit(1);
  }
}

// Executa a função principal
main().catch(error => {
  console.error(`${colors.red}Erro ao executar o script:${colors.reset}`, error);
  process.exit(1);
});
