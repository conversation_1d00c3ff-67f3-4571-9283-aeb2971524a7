/**
 * Testes unitários para a biblioteca de integração Vercel
 * Testa as classes VercelClient e VercelMonitoringService
 */

// Mock do fetch global
global.fetch = jest.fn();

// Mock do logger
jest.mock('@/lib/logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  },
}));

import { VercelClient, VercelMonitoringService } from '@/lib/vercel-integration';

describe('VercelClient', () => {
  let vercelClient: VercelClient;
  const mockFetch = fetch as jest.MockedFunction<typeof fetch>;

  beforeEach(() => {
    jest.clearAllMocks();
    vercelClient = new VercelClient({
      apiToken: 'test_token',
      teamId: 'team_test123',
      projectId: 'prj_test123',
    });
  });

  describe('getProject', () => {
    it('deve obter informações do projeto com sucesso', async () => {
      // Arrange
      const mockProject = {
        id: 'prj_test123',
        name: 'excel-copilot',
        accountId: 'team_test123',
        framework: 'nextjs',
        createdAt: *************,
        updatedAt: *************,
        directoryListing: false,
        env: [],
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockProject,
      } as Response);

      // Act
      const result = await vercelClient.getProject();

      // Assert
      expect(result).toEqual(mockProject);
      expect(mockFetch).toHaveBeenCalledWith('https://api.vercel.com/v9/projects/prj_test123', {
        headers: {
          Authorization: 'Bearer test_token',
          'Content-Type': 'application/json',
          'X-Vercel-Team-Id': 'team_test123',
        },
      });
    });

    it('deve lançar erro quando API retornar erro', async () => {
      // Arrange
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 404,
        statusText: 'Not Found',
        text: async () => 'Project not found',
      } as Response);

      // Act & Assert
      await expect(vercelClient.getProject()).rejects.toThrow('Vercel API error: 404');
    });
  });

  describe('getDeployments', () => {
    it('deve obter lista de deployments com sucesso', async () => {
      // Arrange
      const mockDeployments = {
        deployments: [
          {
            uid: 'dpl_test123',
            name: 'excel-copilot',
            url: 'https://excel-copilot.vercel.app',
            state: 'READY',
            type: 'LAMBDAS',
            created: *************,
            ready: 1640995300000,
            projectId: 'prj_test123',
          },
        ],
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockDeployments,
      } as Response);

      // Act
      const result = await vercelClient.getDeployments(10);

      // Assert
      expect(result).toEqual(mockDeployments.deployments);
      expect(mockFetch).toHaveBeenCalledWith(
        'https://api.vercel.com/v6/deployments?projectId=prj_test123&limit=10',
        {
          headers: {
            Authorization: 'Bearer test_token',
            'Content-Type': 'application/json',
            'X-Vercel-Team-Id': 'team_test123',
          },
        }
      );
    });
  });
});

describe('VercelMonitoringService', () => {
  let vercelService: VercelMonitoringService;

  beforeEach(() => {
    jest.clearAllMocks();
    vercelService = new VercelMonitoringService('test_token', 'team_test123', 'prj_test123');
  });

  describe('constructor', () => {
    it('deve criar instância do serviço corretamente', () => {
      // Act & Assert
      expect(vercelService).toBeInstanceOf(VercelMonitoringService);
    });
  });

  describe('getProjectStatus', () => {
    it('deve retornar status down quando há erro na API', async () => {
      // Arrange - Simular erro na API
      const mockFetch = fetch as jest.MockedFunction<typeof fetch>;
      mockFetch.mockRejectedValue(new Error('API Error'));

      // Act
      const result = await vercelService.getProjectStatus();

      // Assert
      expect(result.status).toBe('down');
      expect(result.message).toBe('Erro ao conectar com Vercel API');
    });
  });

  describe('getPerformanceMetrics', () => {
    it('deve retornar métricas zeradas quando há erro na API', async () => {
      // Arrange - Simular erro na API
      const mockFetch = fetch as jest.MockedFunction<typeof fetch>;
      mockFetch.mockRejectedValue(new Error('API Error'));

      // Act
      const result = await vercelService.getPerformanceMetrics();

      // Assert
      expect(result.requests).toBe(0);
      expect(result.errors).toBe(0);
      expect(result.errorRate).toBe(0);
      expect(result.averageResponseTime).toBe(0);
      expect(result.bandwidth).toBe(0);
      expect(result.cacheHitRate).toBe(0);
    });
  });

  describe('getFilteredLogs', () => {
    it('deve retornar array vazio quando há erro na API', async () => {
      // Arrange - Simular erro na API
      const mockFetch = fetch as jest.MockedFunction<typeof fetch>;
      mockFetch.mockRejectedValue(new Error('API Error'));

      // Act
      const result = await vercelService.getFilteredLogs();

      // Assert
      expect(result).toEqual([]);
    });
  });
});
