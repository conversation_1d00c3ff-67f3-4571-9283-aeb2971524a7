# Implementação Real do Stripe no Excel Copilot

Este documento fornece instruções detalhadas para implementar o Stripe em ambiente de produção, substituindo os mocks por funcionalidades reais.

## Pré-requisitos

Antes de começar, certifique-se de ter:

1. Uma conta no Stripe (crie em https://dashboard.stripe.com/register)
2. <PERSON>ves de API do Stripe (obtidas no Dashboard do Stripe)
3. Compreensão do modelo de preços para seu SaaS

## Etapa 1: Configurar Produtos e Preços no Stripe

1. Acesse o [Dashboard do Stripe](https://dashboard.stripe.com/products)
2. Clique em "Adicionar produto" para criar os produtos correspondentes aos planos:

   **Plano Free:**

   - Nome: Excel Copilot Free
   - Preço: $0 (gratuito)
   - Intervalo de cobrança: N/A
   - Descrição: Plano gratuito com funcionalidades limitadas

   **Plano Pro Mensal:**

   - Nome: Excel Copilot Pro Monthly
   - Preço: R$ 20,00
   - Intervalo de cobrança: mensal
   - Descrição: Acesso completo às funcionalidades premium do Excel Copilot

   **Plano Pro Anual:**

   - Nome: Excel Copilot Pro Annual
   - Preço: R$ 200,00
   - Intervalo de cobrança: anual
   - Descrição: Acesso completo às funcionalidades premium com desconto

3. Anote os IDs dos preços gerados pelo Stripe (começam com "price\_")

## Etapa 2: Configurar Webhook do Stripe

1. No Dashboard do Stripe, acesse [Webhooks](https://dashboard.stripe.com/webhooks)
2. Clique em "Adicionar endpoint"
3. Configure o endpoint:
   - URL: `https://seu-dominio.com/api/webhooks/stripe`
   - Selecione os eventos:
     - `checkout.session.completed`
     - `customer.subscription.created`
     - `customer.subscription.updated`
     - `customer.subscription.deleted`
     - `invoice.payment_succeeded`
     - `invoice.payment_failed`
4. Clique em "Adicionar endpoint"
5. Anote a "Signing secret" gerada (começa com "whsec\_")

## Etapa 3: Atualizar Variáveis de Ambiente

1. Edite o arquivo `.env.production` e adicione as seguintes variáveis:

```
# Stripe Configuration
STRIPE_SECRET_KEY="sk_live_xxxxxxxxxxxxxxxxxxxxxxxx"
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY="pk_live_xxxxxxxxxxxxxxxxxxxxxxxx"
STRIPE_WEBHOOK_SECRET="whsec_xxxxxxxxxxxxxxxxxxxxxxxx"
NEXT_PUBLIC_STRIPE_PRICE_MONTHLY="price_xxxxxxxxxxxxxxxx"
NEXT_PUBLIC_STRIPE_PRICE_ANNUAL="price_xxxxxxxxxxxxxxxx"
```

## Etapa 4: Desativar Mocks no Código

1. Certifique-se de que `USE_MOCK_AI="false"` está configurado no arquivo `.env.production`
2. Atualize o arquivo `/src/config/stripe.ts` para usar as chaves reais do Stripe

## Etapa 5: Implementar Manipulação de Webhooks do Stripe

Verifique se o endpoint de webhook do Stripe está implementado corretamente:

```typescript
// Função para verificar a assinatura do webhook
function constructEvent(body: string, signature: string): Stripe.Event {
  try {
    return stripe.webhooks.constructEvent(body, signature, process.env.STRIPE_WEBHOOK_SECRET!);
  } catch (error) {
    console.error('Erro ao verificar assinatura do webhook:', error);
    throw new Error('Assinatura de webhook inválida');
  }
}

// Manipular eventos do webhook
export async function handleStripeWebhook(
  body: string,
  signature: string
): Promise<{ success: boolean; message: string }> {
  try {
    const event = constructEvent(body, signature);

    switch (event.type) {
      case 'checkout.session.completed':
        await handleCheckoutCompleted(event.data.object);
        break;
      case 'customer.subscription.created':
      case 'customer.subscription.updated':
        await handleSubscriptionUpdated(event.data.object);
        break;
      case 'customer.subscription.deleted':
        await handleSubscriptionDeleted(event.data.object);
        break;
      case 'invoice.payment_succeeded':
        await handleInvoicePaymentSucceeded(event.data.object);
        break;
      case 'invoice.payment_failed':
        await handleInvoicePaymentFailed(event.data.object);
        break;
      default:
        console.log(`Evento não tratado: ${event.type}`);
    }

    return { success: true, message: `Evento processado: ${event.type}` };
  } catch (error) {
    console.error('Erro ao processar webhook:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Erro desconhecido',
    };
  }
}
```

## Etapa 6: Implementar Página de Checkout

1. Certifique-se de que o componente de checkout está usando a configuração correta:

```typescript
import { loadStripe } from '@stripe/stripe-js';

// Carregar Stripe com a chave publicável
const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!);

// Função para criar sessão de checkout
async function createCheckoutSession(priceId: string, userId: string) {
  const response = await fetch('/api/create-checkout-session', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      priceId,
      userId,
    }),
  });

  const { sessionId } = await response.json();
  const stripe = await stripePromise;

  // Redirecionar para o checkout do Stripe
  stripe?.redirectToCheckout({ sessionId });
}
```

## Etapa 7: Testar a Integração

1. Use o modo de teste do Stripe para verificar toda a integração antes de ir para produção
2. Use os [cartões de teste do Stripe](https://stripe.com/docs/testing#cards) para simular pagamentos
3. Verifique se os webhooks estão sendo recebidos e processados corretamente
4. Confirme que as assinaturas são criadas e atualizadas no banco de dados
5. Teste o acesso às funcionalidades premium após o pagamento

## Etapa 8: Ativar o Modo de Produção

Quando estiver pronto para ir para produção:

1. No Dashboard do Stripe, alterne de "Modo de teste" para "Modo de produção"
2. Atualize as chaves de API do Stripe em `.env.production` para usar as chaves de produção
3. Implante o código atualizado

## Perguntas Frequentes

### Como testar webhooks localmente?

Use o [Stripe CLI](https://stripe.com/docs/stripe-cli) para encaminhar eventos do Stripe para seu ambiente de desenvolvimento:

```bash
stripe listen --forward-to localhost:3000/api/webhooks/stripe
```

### Como lidar com falhas de pagamento?

Implemente uma lógica para notificar os usuários sobre falhas de pagamento e oferecer opções para atualizar o método de pagamento.

### Como gerenciar o acesso às funcionalidades premium?

Use o status da assinatura do usuário para controlar o acesso às funcionalidades premium. Consulte a tabela `Subscription` no banco de dados antes de permitir o acesso a recursos restritos.

## Recursos Adicionais

- [Documentação oficial do Stripe](https://stripe.com/docs)
- [Guia de integração com Next.js](https://vercel.com/guides/getting-started-with-nextjs-typescript-stripe)
- [Webhook Events API](https://stripe.com/docs/api/events/types)
- [Stripe Testing](https://stripe.com/docs/testing)

## Solução de Problemas

| Problema                        | Solução                                                     |
| ------------------------------- | ----------------------------------------------------------- |
| Webhook não está sendo recebido | Verifique a URL do endpoint e se o servidor está acessível  |
| Erro de assinatura de webhook   | Confirme se a chave de assinatura está correta              |
| Pagamento recusado              | Use um dos cartões de teste válidos do Stripe               |
| Sessão de checkout expira       | Aumente o tempo de expiração ou melhore o fluxo de checkout |
