import { test, expect } from '@playwright/test';

test.describe('Edição de Planilhas', () => {
  // Configuração para autenticar antes dos testes
  test.beforeEach(async ({ page }) => {
    // Simular autenticação bem-sucedida
    await page.context().addCookies([
      {
        name: 'next-auth.session-token',
        value: 'mock-session-token',
        domain: 'localhost',
        path: '/',
        httpOnly: true,
        secure: false,
      },
    ]);

    // Configurar dados do usuário no localStorage
    await page.evaluate(() => {
      localStorage.setItem(
        'user',
        JSON.stringify({
          id: 'test-user-id',
          name: 'Test User',
          email: '<EMAIL>',
        })
      );
    });

    // Navegar para o dashboard
    await page.goto('/dashboard');

    // Criar uma nova planilha para testes
    await page.getByText('Criar Nova Planilha').click();
    await page.getByLabel('Nome da Planilha').fill('Planilha de Testes E2E');
    await page.getByRole('button', { name: /Criar/i }).click();

    // Verificar se fomos redirecionados para a nova planilha
    await expect(page.url()).toContain('/workbook/');
  });

  test('deve permitir edição manual de células da planilha', async ({ page }) => {
    // Verificar se a planilha está visível
    await expect(page.locator('.excel-grid')).toBeVisible();

    // Clicar na primeira célula editável (A1)
    await page.locator('.excel-grid .cell').first().dblclick();

    // Digitar um valor
    await page.keyboard.type('Teste E2E');
    await page.keyboard.press('Enter');

    // Verificar se o valor foi aplicado
    await expect(page.locator('.excel-grid .cell').first()).toContainText('Teste E2E');

    // Editar outra célula (B1)
    await page.locator('.excel-grid .cell').nth(1).dblclick();
    await page.keyboard.type('123');
    await page.keyboard.press('Enter');

    // Verificar se o valor foi aplicado
    await expect(page.locator('.excel-grid .cell').nth(1)).toContainText('123');
  });

  test('deve aplicar formatação através de comandos de chat', async ({ page }) => {
    // Navegar para a aba de chat
    await page.getByRole('tab', { name: 'Chat' }).click();

    // Enviar comando para formatar células
    await page
      .getByPlaceholder('Digite sua mensagem...')
      .fill('Aplique formatação negrito na célula A1');
    await page.getByRole('button', { name: 'Enviar' }).click();

    // Verificar resposta do assistente
    await expect(page.getByText(/Formatação aplicada/i)).toBeVisible({ timeout: 10000 });

    // Voltar para a visualização de dados
    await page.getByRole('tab', { name: 'Dados' }).click();

    // Verificar se a formatação foi aplicada (verificando a classe de estilo)
    await expect(page.locator('.excel-grid .cell').first()).toHaveClass(/font-bold/);
  });

  test('deve criar e configurar gráficos via comandos', async ({ page }) => {
    // Preparar dados para o gráfico
    // Preencher algumas células com dados
    await page.locator('.excel-grid .cell').first().dblclick();
    await page.keyboard.type('Categoria');
    await page.keyboard.press('Tab');
    await page.keyboard.type('Valor');
    await page.keyboard.press('Enter');

    // Segunda linha
    await page.keyboard.type('A');
    await page.keyboard.press('Tab');
    await page.keyboard.type('10');
    await page.keyboard.press('Enter');

    // Terceira linha
    await page.keyboard.type('B');
    await page.keyboard.press('Tab');
    await page.keyboard.type('20');
    await page.keyboard.press('Enter');

    // Navegar para a aba de chat
    await page.getByRole('tab', { name: 'Chat' }).click();

    // Enviar comando para criar gráfico
    await page
      .getByPlaceholder('Digite sua mensagem...')
      .fill('Crie um gráfico de barras com dados das colunas A e B');
    await page.getByRole('button', { name: 'Enviar' }).click();

    // Verificar resposta do assistente
    await expect(page.getByText(/Gráfico criado/i)).toBeVisible({ timeout: 10000 });

    // Verificar se o gráfico é exibido
    await expect(page.locator('.chart-container canvas')).toBeVisible();
  });

  test('deve permitir exportar a planilha', async ({ page }) => {
    // Verificar se o botão de exportação está visível
    await expect(page.getByRole('button', { name: /Exportar/i })).toBeVisible();

    // Clicar no botão de exportação
    const downloadPromise = page.waitForEvent('download');
    await page.getByRole('button', { name: /Exportar/i }).click();

    // Aguardar o download iniciar
    const download = await downloadPromise;

    // Verificar se o nome do arquivo é correto
    expect(download.suggestedFilename()).toContain('.xlsx');
  });
});
