import { ThumbsUp, ThumbsDown, Send, X } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { cn } from '@/lib/utils';

interface CommandFeedbackProps {
  commandId: string;
  command: string;
  onDismiss: () => void;
  onFeedbackSubmit: (feedback: CommandFeedbackData) => Promise<void>;
}

export interface CommandFeedbackData {
  commandId: string;
  command: string;
  successful: boolean;
  feedbackText?: string;
}

export function CommandFeedback({
  commandId,
  command,
  onDismiss,
  onFeedbackSubmit,
}: CommandFeedbackProps) {
  const [successful, setSuccessful] = useState<boolean | null>(null);
  const [feedbackText, setFeedbackText] = useState('');
  const [showTextarea, setShowTextarea] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleFeedback = async (isSuccessful: boolean) => {
    setSuccessful(isSuccessful);
    setShowTextarea(true);

    // Se for um feedback positivo sem comentários, enviamos diretamente
    if (isSuccessful && !showTextarea) {
      await submitFeedback(isSuccessful, '');
    }
  };

  const submitFeedback = async (isSuccessful: boolean, text: string) => {
    try {
      setIsSubmitting(true);
      await onFeedbackSubmit({
        commandId,
        command,
        successful: isSuccessful,
        feedbackText: text,
      });

      toast.success('Feedback enviado', {
        description: 'Obrigado por ajudar a melhorar nosso sistema!',
      });

      onDismiss();
    } catch (error) {
      console.error('Erro ao enviar feedback:', error);
      toast.error('Não foi possível enviar o feedback');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleTextSubmit = async () => {
    if (successful !== null) {
      await submitFeedback(successful, feedbackText);
    }
  };

  return (
    <Card className="p-3 mb-3 border border-gray-200 dark:border-gray-800">
      <div className="flex flex-col space-y-2">
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium text-slate-600 dark:text-slate-300">
            O comando funcionou como esperado?
          </span>
          <Button
            variant="ghost"
            size="sm"
            onClick={onDismiss}
            className="h-6 w-6 p-0 rounded-full"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        <div className="flex space-x-2">
          <Button
            variant={successful === true ? 'default' : 'outline'}
            size="sm"
            onClick={() => handleFeedback(true)}
            className={successful === true ? 'bg-green-600 hover:bg-green-700' : ''}
            disabled={isSubmitting}
          >
            <ThumbsUp className="h-4 w-4 mr-1" />
            Sim
          </Button>
          <Button
            variant={successful === false ? 'default' : 'outline'}
            size="sm"
            onClick={() => handleFeedback(false)}
            className={successful === false ? 'bg-red-600 hover:bg-red-700' : ''}
            disabled={isSubmitting}
          >
            <ThumbsDown className="h-4 w-4 mr-1" />
            Não
          </Button>
        </div>

        {showTextarea && (
          <div className="mt-2 space-y-2">
            <textarea
              className={cn(
                'flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
                'resize-vertical'
              )}
              value={feedbackText}
              onChange={e => setFeedbackText(e.target.value)}
              placeholder="Descreva o que você gostaria que o comando fizesse..."
              id="feedback-text"
            />
            <div className="flex justify-end">
              <Button
                variant="default"
                size="sm"
                onClick={handleTextSubmit}
                disabled={isSubmitting}
                className="flex items-center"
              >
                {isSubmitting ? 'Enviando...' : 'Enviar'}
                <Send className="h-3 w-3 ml-1" />
              </Button>
            </div>
          </div>
        )}
      </div>
    </Card>
  );
}
