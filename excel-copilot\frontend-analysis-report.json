{"summary": {"totalFiles": 417, "totalLines": 91327, "totalCodeLines": 61689, "averageLinesPerFile": 148}, "categories": {"WORKBOOK_COMPONENTS": {"count": 5, "lines": 1243}, "PÁGINAS": {"count": 14, "lines": 1839}, "CHAT_COMPONENTS": {"count": 10, "lines": 1353}, "LAYOUT": {"count": 1, "lines": 173}, "OTHER": {"count": 43, "lines": 4518}, "LIBRARIES": {"count": 144, "lines": 31638}, "TYPES": {"count": 39, "lines": 3225}, "CONFIG": {"count": 10, "lines": 2467}, "COMPONENTS": {"count": 53, "lines": 5690}, "HOOKS": {"count": 26, "lines": 3745}, "DASHBOARD_COMPONENTS": {"count": 6, "lines": 1362}, "UTILITIES": {"count": 17, "lines": 1544}, "UI_COMPONENTS": {"count": 44, "lines": 2612}, "STYLES": {"count": 5, "lines": 280}}, "importance": {"CRÍTICO": {"count": 28, "lines": 4314}, "ALTO": {"count": 107, "lines": 36840}, "MÉDIO": {"count": 76, "lines": 10739}, "BAIXO": {"count": 206, "lines": 9796}}, "files": [{"path": "src/components/workbook/SpreadsheetEditor.tsx", "category": "WORKBOOK_COMPONENTS", "importance": "CRÍTICO", "complexity": "MUITO ALTA", "lines": {"total": 1586, "code": 925, "comments": 498, "blank": 163}}, {"path": "src/app/dashboard/page.tsx", "category": "PÁGINAS", "importance": "CRÍTICO", "complexity": "MUITO ALTA", "lines": {"total": 966, "code": 513, "comments": 372, "blank": 81}}, {"path": "src/components/chat-interface/empty-state.tsx", "category": "CHAT_COMPONENTS", "importance": "CRÍTICO", "complexity": "MUITO ALTA", "lines": {"total": 388, "code": 347, "comments": 10, "blank": 31}}, {"path": "src/components/chat-interface/chat-interface.tsx", "category": "CHAT_COMPONENTS", "importance": "CRÍTICO", "complexity": "ALTA", "lines": {"total": 490, "code": 291, "comments": 157, "blank": 42}}, {"path": "src/components/chat-interface/ChatInput.tsx", "category": "CHAT_COMPONENTS", "importance": "CRÍTICO", "complexity": "ALTA", "lines": {"total": 350, "code": 286, "comments": 26, "blank": 38}}, {"path": "src/app/pricing/page.tsx", "category": "PÁGINAS", "importance": "CRÍTICO", "complexity": "ALTA", "lines": {"total": 644, "code": 240, "comments": 356, "blank": 48}}, {"path": "src/components/chat-interface/CommandPalette.tsx", "category": "CHAT_COMPONENTS", "importance": "CRÍTICO", "complexity": "ALTA", "lines": {"total": 298, "code": 205, "comments": 69, "blank": 24}}, {"path": "src/app/sentry-example-page/page.tsx", "category": "PÁGINAS", "importance": "CRÍTICO", "complexity": "ALTA", "lines": {"total": 221, "code": 193, "comments": 0, "blank": 28}}, {"path": "src/app/dashboard/analytics/page.tsx", "category": "PÁGINAS", "importance": "CRÍTICO", "complexity": "ALTA", "lines": {"total": 200, "code": 178, "comments": 5, "blank": 17}}, {"path": "src/app/layout.tsx", "category": "LAYOUT", "importance": "CRÍTICO", "complexity": "ALTA", "lines": {"total": 258, "code": 173, "comments": 72, "blank": 13}}, {"path": "src/app/terms/page.tsx", "category": "PÁGINAS", "importance": "CRÍTICO", "complexity": "MÉDIA", "lines": {"total": 123, "code": 109, "comments": 0, "blank": 14}}, {"path": "src/app/workbook/[id]/page.tsx", "category": "PÁGINAS", "importance": "CRÍTICO", "complexity": "MÉDIA", "lines": {"total": 127, "code": 107, "comments": 5, "blank": 15}}, {"path": "src/app/admin/health/page.tsx", "category": "PÁGINAS", "importance": "CRÍTICO", "complexity": "MÉDIA", "lines": {"total": 287, "code": 106, "comments": 149, "blank": 32}}, {"path": "src/app/privacy/page.tsx", "category": "PÁGINAS", "importance": "CRÍTICO", "complexity": "MÉDIA", "lines": {"total": 91, "code": 79, "comments": 0, "blank": 12}}, {"path": "src/components/chat-interface/message-content.tsx", "category": "CHAT_COMPONENTS", "importance": "CRÍTICO", "complexity": "MÉDIA", "lines": {"total": 95, "code": 79, "comments": 5, "blank": 11}}, {"path": "src/app/workbook/new/page.tsx", "category": "PÁGINAS", "importance": "CRÍTICO", "complexity": "MÉDIA", "lines": {"total": 97, "code": 78, "comments": 8, "blank": 11}}, {"path": "src/app/dashboard/account/page.tsx", "category": "PÁGINAS", "importance": "CRÍTICO", "complexity": "MÉDIA", "lines": {"total": 126, "code": 74, "comments": 38, "blank": 14}}, {"path": "src/app/page.tsx", "category": "PÁGINAS", "importance": "CRÍTICO", "complexity": "MÉDIA", "lines": {"total": 306, "code": 72, "comments": 219, "blank": 15}}, {"path": "src/components/chat-interface/chat-interface.test.tsx", "category": "CHAT_COMPONENTS", "importance": "CRÍTICO", "complexity": "MÉDIA", "lines": {"total": 82, "code": 60, "comments": 6, "blank": 16}}, {"path": "src/app/auth/signin/page.tsx", "category": "PÁGINAS", "importance": "CRÍTICO", "complexity": "MÉDIA", "lines": {"total": 225, "code": 54, "comments": 151, "blank": 20}}, {"path": "src/components/chat-interface/chat-message.tsx", "category": "CHAT_COMPONENTS", "importance": "CRÍTICO", "complexity": "BAIXA", "lines": {"total": 79, "code": 33, "comments": 33, "blank": 13}}, {"path": "src/app/examples/page.tsx", "category": "PÁGINAS", "importance": "CRÍTICO", "complexity": "BAIXA", "lines": {"total": 36, "code": 32, "comments": 0, "blank": 4}}, {"path": "src/components/chat-interface/types.ts", "category": "CHAT_COMPONENTS", "importance": "CRÍTICO", "complexity": "BAIXA", "lines": {"total": 57, "code": 28, "comments": 21, "blank": 8}}, {"path": "src/components/chat-interface/operations-indicator.tsx", "category": "CHAT_COMPONENTS", "importance": "CRÍTICO", "complexity": "BAIXA", "lines": {"total": 23, "code": 18, "comments": 1, "blank": 4}}, {"path": "src/app/pricing/layout.tsx", "category": "OTHER", "importance": "CRÍTICO", "complexity": "BAIXA", "lines": {"total": 20, "code": 16, "comments": 0, "blank": 4}}, {"path": "src/app/workbook/new/layout.tsx", "category": "OTHER", "importance": "CRÍTICO", "complexity": "BAIXA", "lines": {"total": 11, "code": 8, "comments": 0, "blank": 3}}, {"path": "src/components/chat-interface/index.tsx", "category": "CHAT_COMPONENTS", "importance": "CRÍTICO", "complexity": "BAIXA", "lines": {"total": 9, "code": 6, "comments": 0, "blank": 3}}, {"path": "src/app/templates/page.tsx", "category": "PÁGINAS", "importance": "CRÍTICO", "complexity": "BAIXA", "lines": {"total": 6, "code": 4, "comments": 0, "blank": 2}}, {"path": "src/lib/excel.ts", "category": "LIBRARIES", "importance": "ALTO", "complexity": "MUITO ALTA", "lines": {"total": 1468, "code": 1033, "comments": 264, "blank": 171}}, {"path": "src/server/ai/gemini-service.ts", "category": "OTHER", "importance": "ALTO", "complexity": "MUITO ALTA", "lines": {"total": 1266, "code": 876, "comments": 233, "blank": 157}}, {"path": "src/lib/operations/conditionalFormattingOperations.ts", "category": "LIBRARIES", "importance": "ALTO", "complexity": "MUITO ALTA", "lines": {"total": 841, "code": 696, "comments": 55, "blank": 90}}, {"path": "src/lib/stripe-integration.ts", "category": "LIBRARIES", "importance": "ALTO", "complexity": "MUITO ALTA", "lines": {"total": 833, "code": 692, "comments": 64, "blank": 77}}, {"path": "src/types/interfaces.ts", "category": "TYPES", "importance": "ALTO", "complexity": "MUITO ALTA", "lines": {"total": 837, "code": 669, "comments": 77, "blank": 91}}, {"path": "src/lib/linear-integration.ts", "category": "LIBRARIES", "importance": "ALTO", "complexity": "MUITO ALTA", "lines": {"total": 806, "code": 652, "comments": 77, "blank": 77}}, {"path": "src/lib/github-integration.ts", "category": "LIBRARIES", "importance": "ALTO", "complexity": "MUITO ALTA", "lines": {"total": 786, "code": 623, "comments": 70, "blank": 93}}, {"path": "src/lib/app-initializer.ts", "category": "LIBRARIES", "importance": "ALTO", "complexity": "MUITO ALTA", "lines": {"total": 873, "code": 597, "comments": 163, "blank": 113}}, {"path": "src/lib/ai/gemini-service.ts", "category": "LIBRARIES", "importance": "ALTO", "complexity": "MUITO ALTA", "lines": {"total": 849, "code": 583, "comments": 147, "blank": 119}}, {"path": "src/lib/health-checker.ts", "category": "LIBRARIES", "importance": "ALTO", "complexity": "MUITO ALTA", "lines": {"total": 711, "code": 560, "comments": 79, "blank": 72}}, {"path": "src/lib/operations/advancedChartOperations.ts", "category": "LIBRARIES", "importance": "ALTO", "complexity": "MUITO ALTA", "lines": {"total": 697, "code": 555, "comments": 70, "blank": 72}}, {"path": "src/lib/subscription-limits.ts", "category": "LIBRARIES", "importance": "ALTO", "complexity": "MUITO ALTA", "lines": {"total": 759, "code": 549, "comments": 119, "blank": 91}}, {"path": "src/config/diagnostic-system.ts", "category": "CONFIG", "importance": "ALTO", "complexity": "MUITO ALTA", "lines": {"total": 777, "code": 546, "comments": 140, "blank": 91}}, {"path": "src/lib/vercel-integration.ts", "category": "LIBRARIES", "importance": "ALTO", "complexity": "MUITO ALTA", "lines": {"total": 678, "code": 537, "comments": 66, "blank": 75}}, {"path": "src/lib/desktop-bridge-connector.ts", "category": "LIBRARIES", "importance": "ALTO", "complexity": "MUITO ALTA", "lines": {"total": 745, "code": 531, "comments": 116, "blank": 98}}, {"path": "src/lib/supabase-integration.ts", "category": "LIBRARIES", "importance": "ALTO", "complexity": "MUITO ALTA", "lines": {"total": 652, "code": 521, "comments": 60, "blank": 71}}, {"path": "src/lib/alerts/alert-manager.ts", "category": "LIBRARIES", "importance": "ALTO", "complexity": "MUITO ALTA", "lines": {"total": 656, "code": 501, "comments": 82, "blank": 73}}, {"path": "src/config/unified-environment.ts", "category": "CONFIG", "importance": "ALTO", "complexity": "MUITO ALTA", "lines": {"total": 829, "code": 492, "comments": 223, "blank": 114}}, {"path": "src/lib/monitoring/spreadsheet-logger.ts", "category": "LIBRARIES", "importance": "ALTO", "complexity": "MUITO ALTA", "lines": {"total": 604, "code": 491, "comments": 62, "blank": 51}}, {"path": "src/lib/cache-manager.ts", "category": "LIBRARIES", "importance": "ALTO", "complexity": "MUITO ALTA", "lines": {"total": 701, "code": 470, "comments": 124, "blank": 107}}, {"path": "src/config/validation-system.ts", "category": "CONFIG", "importance": "ALTO", "complexity": "MUITO ALTA", "lines": {"total": 614, "code": 444, "comments": 109, "blank": 61}}, {"path": "src/lib/bridge/manager.ts", "category": "LIBRARIES", "importance": "ALTO", "complexity": "MUITO ALTA", "lines": {"total": 654, "code": 429, "comments": 137, "blank": 88}}, {"path": "src/lib/operations/pivotTableOperations.ts", "category": "LIBRARIES", "importance": "ALTO", "complexity": "MUITO ALTA", "lines": {"total": 549, "code": 419, "comments": 78, "blank": 52}}, {"path": "src/components/desktop-bridge-diagnostics.tsx", "category": "COMPONENTS", "importance": "ALTO", "complexity": "MUITO ALTA", "lines": {"total": 536, "code": 407, "comments": 83, "blank": 46}}, {"path": "src/hooks/useAIChat.ts", "category": "HOOKS", "importance": "ALTO", "complexity": "MUITO ALTA", "lines": {"total": 507, "code": 405, "comments": 48, "blank": 54}}, {"path": "src/hooks/useLogging.ts", "category": "HOOKS", "importance": "ALTO", "complexity": "MUITO ALTA", "lines": {"total": 470, "code": 385, "comments": 36, "blank": 49}}, {"path": "src/components/dashboard/WorkbooksTable.tsx", "category": "DASHBOARD_COMPONENTS", "importance": "ALTO", "complexity": "MUITO ALTA", "lines": {"total": 453, "code": 382, "comments": 30, "blank": 41}}, {"path": "src/lib/errors.ts", "category": "LIBRARIES", "importance": "ALTO", "complexity": "MUITO ALTA", "lines": {"total": 488, "code": 382, "comments": 41, "blank": 65}}, {"path": "src/server/services/workbook-service.ts", "category": "OTHER", "importance": "ALTO", "complexity": "MUITO ALTA", "lines": {"total": 475, "code": 376, "comments": 50, "blank": 49}}, {"path": "src/lib/middleware/withServerValidation.ts", "category": "LIBRARIES", "importance": "ALTO", "complexity": "MUITO ALTA", "lines": {"total": 492, "code": 363, "comments": 66, "blank": 63}}, {"path": "src/lib/monitoring/ai-command-logger.ts", "category": "LIBRARIES", "importance": "ALTO", "complexity": "MUITO ALTA", "lines": {"total": 479, "code": 363, "comments": 60, "blank": 56}}, {"path": "src/server/ai/vertex-ai-service.ts", "category": "OTHER", "importance": "ALTO", "complexity": "MUITO ALTA", "lines": {"total": 508, "code": 363, "comments": 86, "blank": 59}}, {"path": "src/lib/logger.ts", "category": "LIBRARIES", "importance": "ALTO", "complexity": "MUITO ALTA", "lines": {"total": 531, "code": 362, "comments": 110, "blank": 59}}, {"path": "src/lib/auth-monitoring.ts", "category": "LIBRARIES", "importance": "ALTO", "complexity": "MUITO ALTA", "lines": {"total": 483, "code": 360, "comments": 72, "blank": 51}}, {"path": "src/lib/operations/formatOperations.ts", "category": "LIBRARIES", "importance": "ALTO", "complexity": "MUITO ALTA", "lines": {"total": 529, "code": 360, "comments": 94, "blank": 75}}, {"path": "src/components/desktop-bridge-status-indicator.tsx", "category": "COMPONENTS", "importance": "ALTO", "complexity": "MUITO ALTA", "lines": {"total": 394, "code": 356, "comments": 10, "blank": 28}}, {"path": "src/lib/security/sanitization.ts", "category": "LIBRARIES", "importance": "ALTO", "complexity": "MUITO ALTA", "lines": {"total": 853, "code": 353, "comments": 408, "blank": 92}}, {"path": "src/lib/monitoring/client-logger.ts", "category": "LIBRARIES", "importance": "ALTO", "complexity": "MUITO ALTA", "lines": {"total": 497, "code": 346, "comments": 92, "blank": 59}}, {"path": "src/components/dashboard/ActivityCharts.tsx", "category": "DASHBOARD_COMPONENTS", "importance": "ALTO", "complexity": "MUITO ALTA", "lines": {"total": 402, "code": 343, "comments": 27, "blank": 32}}, {"path": "src/lib/monitoring/collaboration-logger.ts", "category": "LIBRARIES", "importance": "ALTO", "complexity": "MUITO ALTA", "lines": {"total": 446, "code": 343, "comments": 62, "blank": 41}}, {"path": "src/lib/middleware/withErrorHandling.ts", "category": "LIBRARIES", "importance": "ALTO", "complexity": "MUITO ALTA", "lines": {"total": 490, "code": 333, "comments": 103, "blank": 54}}, {"path": "src/lib/operations/filterOperations.ts", "category": "LIBRARIES", "importance": "ALTO", "complexity": "MUITO ALTA", "lines": {"total": 493, "code": 329, "comments": 86, "blank": 78}}, {"path": "src/config/mcp-config.ts", "category": "CONFIG", "importance": "ALTO", "complexity": "MUITO ALTA", "lines": {"total": 550, "code": 326, "comments": 151, "blank": 73}}, {"path": "src/lib/offline-storage.ts", "category": "LIBRARIES", "importance": "ALTO", "complexity": "MUITO ALTA", "lines": {"total": 456, "code": 323, "comments": 47, "blank": 86}}, {"path": "src/components/chart-display.tsx", "category": "COMPONENTS", "importance": "ALTO", "complexity": "MUITO ALTA", "lines": {"total": 379, "code": 321, "comments": 24, "blank": 34}}, {"path": "src/lib/desktop-bridge-core.ts", "category": "LIBRARIES", "importance": "ALTO", "complexity": "MUITO ALTA", "lines": {"total": 488, "code": 321, "comments": 103, "blank": 64}}, {"path": "src/server/trpc/router.ts", "category": "OTHER", "importance": "ALTO", "complexity": "MUITO ALTA", "lines": {"total": 374, "code": 320, "comments": 24, "blank": 30}}, {"path": "src/hooks/useErrorHandler.ts", "category": "HOOKS", "importance": "ALTO", "complexity": "MUITO ALTA", "lines": {"total": 423, "code": 309, "comments": 54, "blank": 60}}, {"path": "src/lib/error-handler.ts", "category": "LIBRARIES", "importance": "ALTO", "complexity": "MUITO ALTA", "lines": {"total": 428, "code": 307, "comments": 63, "blank": 58}}, {"path": "src/types/global.d.ts", "category": "TYPES", "importance": "ALTO", "complexity": "MUITO ALTA", "lines": {"total": 380, "code": 306, "comments": 30, "blank": 44}}, {"path": "src/lib/services.ts", "category": "LIBRARIES", "importance": "ALTO", "complexity": "MUITO ALTA", "lines": {"total": 483, "code": 301, "comments": 121, "blank": 61}}, {"path": "src/lib/middleware/plan-based-rate-limiter-secure.ts", "category": "LIBRARIES", "importance": "ALTO", "complexity": "MUITO ALTA", "lines": {"total": 414, "code": 300, "comments": 58, "blank": 56}}, {"path": "src/components/workbook-templates.tsx", "category": "COMPONENTS", "importance": "ALTO", "complexity": "ALTA", "lines": {"total": 336, "code": 294, "comments": 15, "blank": 27}}, {"path": "src/components/collaboration-panel/index.tsx", "category": "COMPONENTS", "importance": "ALTO", "complexity": "ALTA", "lines": {"total": 539, "code": 289, "comments": 204, "blank": 46}}, {"path": "src/lib/fallback-handlers.ts", "category": "LIBRARIES", "importance": "ALTO", "complexity": "ALTA", "lines": {"total": 375, "code": 287, "comments": 41, "blank": 47}}, {"path": "src/lib/monitoring/metrics-collector.ts", "category": "LIBRARIES", "importance": "ALTO", "complexity": "ALTA", "lines": {"total": 391, "code": 287, "comments": 54, "blank": 50}}, {"path": "src/components/dashboard/RealtimeStatus.tsx", "category": "DASHBOARD_COMPONENTS", "importance": "ALTO", "complexity": "ALTA", "lines": {"total": 321, "code": 279, "comments": 16, "blank": 26}}, {"path": "src/lib/excel/types.ts", "category": "LIBRARIES", "importance": "ALTO", "complexity": "ALTA", "lines": {"total": 347, "code": 277, "comments": 38, "blank": 32}}, {"path": "src/lib/mcp-tools.ts", "category": "LIBRARIES", "importance": "ALTO", "complexity": "ALTA", "lines": {"total": 324, "code": 277, "comments": 31, "blank": 16}}, {"path": "src/lib/rate-limiter.ts", "category": "LIBRARIES", "importance": "ALTO", "complexity": "ALTA", "lines": {"total": 422, "code": 277, "comments": 88, "blank": 57}}, {"path": "src/lib/operations/formulaOperations.ts", "category": "LIBRARIES", "importance": "ALTO", "complexity": "ALTA", "lines": {"total": 414, "code": 273, "comments": 86, "blank": 55}}, {"path": "src/lib/security/enhanced-rate-limiter.ts", "category": "LIBRARIES", "importance": "ALTO", "complexity": "ALTA", "lines": {"total": 403, "code": 270, "comments": 69, "blank": 64}}, {"path": "src/lib/health-checks/ai.ts", "category": "LIBRARIES", "importance": "ALTO", "complexity": "ALTA", "lines": {"total": 408, "code": 265, "comments": 86, "blank": 57}}, {"path": "src/lib/auth-audit-logger.ts", "category": "LIBRARIES", "importance": "ALTO", "complexity": "ALTA", "lines": {"total": 322, "code": 264, "comments": 37, "blank": 21}}, {"path": "src/lib/security/rate-limiter.ts", "category": "LIBRARIES", "importance": "ALTO", "complexity": "ALTA", "lines": {"total": 495, "code": 263, "comments": 158, "blank": 74}}, {"path": "src/lib/chartOperations.ts", "category": "LIBRARIES", "importance": "ALTO", "complexity": "ALTA", "lines": {"total": 370, "code": 261, "comments": 64, "blank": 45}}, {"path": "src/lib/health-checks/stripe.ts", "category": "LIBRARIES", "importance": "ALTO", "complexity": "ALTA", "lines": {"total": 393, "code": 260, "comments": 79, "blank": 54}}, {"path": "src/config/i18n.ts", "category": "CONFIG", "importance": "ALTO", "complexity": "ALTA", "lines": {"total": 270, "code": 258, "comments": 5, "blank": 7}}, {"path": "src/lib/monitoring/auth-alerts.ts", "category": "LIBRARIES", "importance": "ALTO", "complexity": "ALTA", "lines": {"total": 357, "code": 258, "comments": 56, "blank": 43}}, {"path": "src/lib/supabase/realtime.ts", "category": "LIBRARIES", "importance": "ALTO", "complexity": "ALTA", "lines": {"total": 332, "code": 258, "comments": 45, "blank": 29}}, {"path": "src/lib/ai/dynamic-import.ts", "category": "LIBRARIES", "importance": "ALTO", "complexity": "ALTA", "lines": {"total": 344, "code": 254, "comments": 48, "blank": 42}}, {"path": "src/lib/health-checks/auth.ts", "category": "LIBRARIES", "importance": "ALTO", "complexity": "ALTA", "lines": {"total": 383, "code": 254, "comments": 75, "blank": 54}}, {"path": "src/lib/monitoring/alerts.ts", "category": "LIBRARIES", "importance": "ALTO", "complexity": "ALTA", "lines": {"total": 308, "code": 254, "comments": 22, "blank": 32}}, {"path": "src/lib/operations/dataTransformations.ts", "category": "LIBRARIES", "importance": "ALTO", "complexity": "ALTA", "lines": {"total": 362, "code": 254, "comments": 65, "blank": 43}}, {"path": "src/lib/health-checks/mcp.ts", "category": "LIBRARIES", "importance": "ALTO", "complexity": "ALTA", "lines": {"total": 367, "code": 253, "comments": 68, "blank": 46}}, {"path": "src/lib/excel/parserOperations.ts", "category": "LIBRARIES", "importance": "ALTO", "complexity": "ALTA", "lines": {"total": 379, "code": 252, "comments": 68, "blank": 59}}, {"path": "src/hooks/useExcelOperations.ts", "category": "HOOKS", "importance": "ALTO", "complexity": "ALTA", "lines": {"total": 340, "code": 248, "comments": 45, "blank": 47}}, {"path": "src/hooks/useDashboardMetrics.ts", "category": "HOOKS", "importance": "ALTO", "complexity": "ALTA", "lines": {"total": 328, "code": 246, "comments": 34, "blank": 48}}, {"path": "src/components/hero-section.tsx", "category": "COMPONENTS", "importance": "ALTO", "complexity": "ALTA", "lines": {"total": 719, "code": 244, "comments": 431, "blank": 44}}, {"path": "src/lib/ai/prompts.ts", "category": "LIBRARIES", "importance": "ALTO", "complexity": "ALTA", "lines": {"total": 277, "code": 244, "comments": 3, "blank": 30}}, {"path": "src/lib/operations/advancedVisualizationOperations.ts", "category": "LIBRARIES", "importance": "ALTO", "complexity": "ALTA", "lines": {"total": 345, "code": 241, "comments": 52, "blank": 52}}, {"path": "src/lib/monitoring/auth-logger.ts", "category": "LIBRARIES", "importance": "ALTO", "complexity": "ALTA", "lines": {"total": 328, "code": 240, "comments": 50, "blank": 38}}, {"path": "src/lib/telemetry.ts", "category": "LIBRARIES", "importance": "ALTO", "complexity": "ALTA", "lines": {"total": 365, "code": 239, "comments": 74, "blank": 52}}, {"path": "src/types/index.ts", "category": "TYPES", "importance": "ALTO", "complexity": "ALTA", "lines": {"total": 295, "code": 238, "comments": 24, "blank": 33}}, {"path": "src/lib/operations/columnOperations.ts", "category": "LIBRARIES", "importance": "ALTO", "complexity": "ALTA", "lines": {"total": 310, "code": 233, "comments": 49, "blank": 28}}, {"path": "src/lib/health-checks.ts", "category": "LIBRARIES", "importance": "ALTO", "complexity": "ALTA", "lines": {"total": 369, "code": 232, "comments": 85, "blank": 52}}, {"path": "src/lib/middleware/plan-based-rate-limiter.ts", "category": "LIBRARIES", "importance": "ALTO", "complexity": "ALTA", "lines": {"total": 323, "code": 230, "comments": 44, "blank": 49}}, {"path": "src/hooks/useDashboardRealtime.ts", "category": "HOOKS", "importance": "ALTO", "complexity": "ALTA", "lines": {"total": 313, "code": 226, "comments": 41, "blank": 46}}, {"path": "src/lib/design-tokens.ts", "category": "LIBRARIES", "importance": "ALTO", "complexity": "ALTA", "lines": {"total": 297, "code": 224, "comments": 38, "blank": 35}}, {"path": "src/lib/operations/tableOperations.ts", "category": "LIBRARIES", "importance": "ALTO", "complexity": "ALTA", "lines": {"total": 339, "code": 224, "comments": 66, "blank": 49}}, {"path": "src/lib/security-monitor.ts", "category": "LIBRARIES", "importance": "ALTO", "complexity": "ALTA", "lines": {"total": 315, "code": 224, "comments": 52, "blank": 39}}, {"path": "src/config/security-validator.ts", "category": "CONFIG", "importance": "ALTO", "complexity": "ALTA", "lines": {"total": 301, "code": 221, "comments": 44, "blank": 36}}, {"path": "src/types/dashboard.ts", "category": "TYPES", "importance": "ALTO", "complexity": "ALTA", "lines": {"total": 258, "code": 218, "comments": 9, "blank": 31}}, {"path": "src/components/AppInitializer.tsx", "category": "COMPONENTS", "importance": "ALTO", "complexity": "ALTA", "lines": {"total": 403, "code": 217, "comments": 150, "blank": 36}}, {"path": "src/lib/monitoring/web-vitals.ts", "category": "LIBRARIES", "importance": "ALTO", "complexity": "ALTA", "lines": {"total": 288, "code": 217, "comments": 32, "blank": 39}}, {"path": "src/components/command-examples.tsx", "category": "COMPONENTS", "importance": "ALTO", "complexity": "ALTA", "lines": {"total": 296, "code": 216, "comments": 61, "blank": 19}}, {"path": "src/lib/operations/chartOperations.ts", "category": "LIBRARIES", "importance": "ALTO", "complexity": "ALTA", "lines": {"total": 328, "code": 216, "comments": 76, "blank": 36}}, {"path": "src/lib/ai/ai-adapter.ts", "category": "LIBRARIES", "importance": "ALTO", "complexity": "ALTA", "lines": {"total": 284, "code": 214, "comments": 40, "blank": 30}}, {"path": "src/lib/health-checks/index.ts", "category": "LIBRARIES", "importance": "ALTO", "complexity": "ALTA", "lines": {"total": 325, "code": 210, "comments": 67, "blank": 48}}, {"path": "src/components/user-onboarding/TourProvider.tsx", "category": "COMPONENTS", "importance": "ALTO", "complexity": "ALTA", "lines": {"total": 247, "code": 209, "comments": 17, "blank": 21}}, {"path": "src/lib/env-validator.ts", "category": "LIBRARIES", "importance": "ALTO", "complexity": "ALTA", "lines": {"total": 684, "code": 209, "comments": 400, "blank": 75}}, {"path": "src/lib/monitoring/security-alerts.ts", "category": "LIBRARIES", "importance": "ALTO", "complexity": "ALTA", "lines": {"total": 278, "code": 209, "comments": 44, "blank": 25}}, {"path": "src/server/auth/options.ts", "category": "OTHER", "importance": "ALTO", "complexity": "ALTA", "lines": {"total": 251, "code": 206, "comments": 30, "blank": 15}}, {"path": "src/hooks/useChunkedSpreadsheet.ts", "category": "HOOKS", "importance": "ALTO", "complexity": "ALTA", "lines": {"total": 287, "code": 205, "comments": 39, "blank": 43}}, {"path": "src/hooks/useWorkbookRealtime.ts", "category": "HOOKS", "importance": "ALTO", "complexity": "ALTA", "lines": {"total": 271, "code": 204, "comments": 32, "blank": 35}}, {"path": "src/lib/supabase/storage.ts", "category": "LIBRARIES", "importance": "ALTO", "complexity": "ALTA", "lines": {"total": 282, "code": 204, "comments": 38, "blank": 40}}, {"path": "src/server/ai/base-ai-service.ts", "category": "OTHER", "importance": "ALTO", "complexity": "ALTA", "lines": {"total": 361, "code": 203, "comments": 103, "blank": 55}}, {"path": "src/components/workbook/OptimizedTableComponents.tsx", "category": "WORKBOOK_COMPONENTS", "importance": "MÉDIO", "complexity": "ALTA", "lines": {"total": 344, "code": 198, "comments": 107, "blank": 39}}, {"path": "src/lib/bridge/connection.ts", "category": "LIBRARIES", "importance": "MÉDIO", "complexity": "ALTA", "lines": {"total": 269, "code": 198, "comments": 38, "blank": 33}}, {"path": "src/lib/circuit-breaker.ts", "category": "LIBRARIES", "importance": "MÉDIO", "complexity": "ALTA", "lines": {"total": 296, "code": 185, "comments": 77, "blank": 34}}, {"path": "src/hooks/useSupabaseStorage.ts", "category": "HOOKS", "importance": "MÉDIO", "complexity": "ALTA", "lines": {"total": 241, "code": 181, "comments": 24, "blank": 36}}, {"path": "src/hooks/useTranslation.ts", "category": "HOOKS", "importance": "MÉDIO", "complexity": "ALTA", "lines": {"total": 230, "code": 181, "comments": 26, "blank": 23}}, {"path": "src/lib/auth/security.ts", "category": "LIBRARIES", "importance": "MÉDIO", "complexity": "ALTA", "lines": {"total": 264, "code": 179, "comments": 48, "blank": 37}}, {"path": "src/lib/monitoring/index.ts", "category": "LIBRARIES", "importance": "MÉDIO", "complexity": "ALTA", "lines": {"total": 247, "code": 178, "comments": 39, "blank": 30}}, {"path": "src/utils/performance-monitor.tsx", "category": "UTILITIES", "importance": "MÉDIO", "complexity": "ALTA", "lines": {"total": 251, "code": 176, "comments": 40, "blank": 35}}, {"path": "src/components/bridge/DeviceSelector.tsx", "category": "COMPONENTS", "importance": "MÉDIO", "complexity": "ALTA", "lines": {"total": 207, "code": 174, "comments": 12, "blank": 21}}, {"path": "src/components/dashboard/QuickActions.tsx", "category": "DASHBOARD_COMPONENTS", "importance": "MÉDIO", "complexity": "ALTA", "lines": {"total": 399, "code": 173, "comments": 193, "blank": 33}}, {"path": "src/lib/monitoring/sentry-logger.ts", "category": "LIBRARIES", "importance": "MÉDIO", "complexity": "ALTA", "lines": {"total": 259, "code": 172, "comments": 55, "blank": 32}}, {"path": "src/utils/data-access.ts", "category": "UTILITIES", "importance": "MÉDIO", "complexity": "ALTA", "lines": {"total": 368, "code": 171, "comments": 149, "blank": 48}}, {"path": "src/utils/error-utils.ts", "category": "UTILITIES", "importance": "MÉDIO", "complexity": "ALTA", "lines": {"total": 246, "code": 170, "comments": 53, "blank": 23}}, {"path": "src/components/ui/dropdown-menu.tsx", "category": "UI_COMPONENTS", "importance": "MÉDIO", "complexity": "ALTA", "lines": {"total": 188, "code": 169, "comments": 0, "blank": 19}}, {"path": "src/lib/health-checks/database.ts", "category": "LIBRARIES", "importance": "MÉDIO", "complexity": "ALTA", "lines": {"total": 266, "code": 169, "comments": 59, "blank": 38}}, {"path": "src/components/desktop-bridge-status.tsx", "category": "COMPONENTS", "importance": "MÉDIO", "complexity": "ALTA", "lines": {"total": 184, "code": 167, "comments": 1, "blank": 16}}, {"path": "src/hooks/useExcelWorker.ts", "category": "HOOKS", "importance": "MÉDIO", "complexity": "ALTA", "lines": {"total": 246, "code": 166, "comments": 44, "blank": 36}}, {"path": "src/lib/middleware/payment-limiter.ts", "category": "LIBRARIES", "importance": "MÉDIO", "complexity": "ALTA", "lines": {"total": 238, "code": 163, "comments": 41, "blank": 34}}, {"path": "src/lib/collaborative-sync.ts", "category": "LIBRARIES", "importance": "MÉDIO", "complexity": "ALTA", "lines": {"total": 197, "code": 162, "comments": 8, "blank": 27}}, {"path": "src/lib/rate-limiting/oauth-rate-limiter.ts", "category": "LIBRARIES", "importance": "MÉDIO", "complexity": "ALTA", "lines": {"total": 230, "code": 161, "comments": 32, "blank": 37}}, {"path": "src/components/ui/use-toast.ts", "category": "UI_COMPONENTS", "importance": "MÉDIO", "complexity": "ALTA", "lines": {"total": 196, "code": 158, "comments": 5, "blank": 33}}, {"path": "src/lib/excel/analytics.ts", "category": "LIBRARIES", "importance": "MÉDIO", "complexity": "ALTA", "lines": {"total": 251, "code": 158, "comments": 55, "blank": 38}}, {"path": "src/lib/middleware/rate-limiter.ts", "category": "LIBRARIES", "importance": "MÉDIO", "complexity": "ALTA", "lines": {"total": 229, "code": 158, "comments": 39, "blank": 32}}, {"path": "src/lib/operations/cellOperations.ts", "category": "LIBRARIES", "importance": "MÉDIO", "complexity": "ALTA", "lines": {"total": 253, "code": 155, "comments": 53, "blank": 45}}, {"path": "src/types/excel-unified.ts", "category": "TYPES", "importance": "MÉDIO", "complexity": "ALTA", "lines": {"total": 313, "code": 155, "comments": 133, "blank": 25}}, {"path": "src/hooks/useExcelFile.ts", "category": "HOOKS", "importance": "MÉDIO", "complexity": "ALTA", "lines": {"total": 217, "code": 154, "comments": 34, "blank": 29}}, {"path": "src/server/workbook.actions.ts", "category": "OTHER", "importance": "MÉDIO", "complexity": "ALTA", "lines": {"total": 203, "code": 154, "comments": 22, "blank": 27}}, {"path": "src/server/db/query-cache.ts", "category": "OTHER", "importance": "MÉDIO", "complexity": "ALTA", "lines": {"total": 226, "code": 153, "comments": 38, "blank": 35}}, {"path": "src/utils/excel-utils.ts", "category": "UTILITIES", "importance": "MÉDIO", "complexity": "ALTA", "lines": {"total": 272, "code": 152, "comments": 71, "blank": 49}}, {"path": "src/lib/auth/validation.ts", "category": "LIBRARIES", "importance": "MÉDIO", "complexity": "MÉDIA", "lines": {"total": 204, "code": 148, "comments": 24, "blank": 32}}, {"path": "src/hooks/useChunkedData.ts", "category": "HOOKS", "importance": "MÉDIO", "complexity": "MÉDIA", "lines": {"total": 216, "code": 146, "comments": 41, "blank": 29}}, {"path": "src/lib/ai/feedback-service.ts", "category": "LIBRARIES", "importance": "MÉDIO", "complexity": "MÉDIA", "lines": {"total": 202, "code": 146, "comments": 28, "blank": 28}}, {"path": "src/hooks/useBidirectionalVirtualizer.ts", "category": "HOOKS", "importance": "MÉDIO", "complexity": "MÉDIA", "lines": {"total": 193, "code": 144, "comments": 25, "blank": 24}}, {"path": "src/lib/excel/fileOperations.ts", "category": "LIBRARIES", "importance": "MÉDIO", "complexity": "MÉDIA", "lines": {"total": 214, "code": 143, "comments": 45, "blank": 26}}, {"path": "src/lib/middleware/oauth-limiter.ts", "category": "LIBRARIES", "importance": "MÉDIO", "complexity": "MÉDIA", "lines": {"total": 221, "code": 142, "comments": 44, "blank": 35}}, {"path": "src/lib/utils.ts", "category": "LIBRARIES", "importance": "MÉDIO", "complexity": "MÉDIA", "lines": {"total": 243, "code": 142, "comments": 59, "blank": 42}}, {"path": "src/lib/ai/client-polyfill.ts", "category": "LIBRARIES", "importance": "MÉDIO", "complexity": "MÉDIA", "lines": {"total": 180, "code": 141, "comments": 17, "blank": 22}}, {"path": "src/components/ui/theme-toggle.tsx", "category": "UI_COMPONENTS", "importance": "MÉDIO", "complexity": "MÉDIA", "lines": {"total": 172, "code": 140, "comments": 14, "blank": 18}}, {"path": "src/components/ui/select.tsx", "category": "UI_COMPONENTS", "importance": "MÉDIO", "complexity": "MÉDIA", "lines": {"total": 154, "code": 139, "comments": 0, "blank": 15}}, {"path": "src/lib/ai/ExcelAIProcessor.ts", "category": "LIBRARIES", "importance": "MÉDIO", "complexity": "MÉDIA", "lines": {"total": 220, "code": 139, "comments": 55, "blank": 26}}, {"path": "src/utils/route-migration.ts", "category": "UTILITIES", "importance": "MÉDIO", "complexity": "MÉDIA", "lines": {"total": 232, "code": 138, "comments": 64, "blank": 30}}, {"path": "src/lib/ai/simple-error-suppressor.ts", "category": "LIBRARIES", "importance": "MÉDIO", "complexity": "MÉDIA", "lines": {"total": 182, "code": 137, "comments": 25, "blank": 20}}, {"path": "src/middleware/auth.ts", "category": "OTHER", "importance": "MÉDIO", "complexity": "MÉDIA", "lines": {"total": 188, "code": 136, "comments": 26, "blank": 26}}, {"path": "src/components/enhanced-chat-input.tsx", "category": "COMPONENTS", "importance": "MÉDIO", "complexity": "MÉDIA", "lines": {"total": 208, "code": 135, "comments": 47, "blank": 26}}, {"path": "src/components/trpc-demo.tsx", "category": "COMPONENTS", "importance": "MÉDIO", "complexity": "MÉDIA", "lines": {"total": 147, "code": 134, "comments": 3, "blank": 10}}, {"path": "src/lib/chunk-manager.ts", "category": "LIBRARIES", "importance": "MÉDIO", "complexity": "MÉDIA", "lines": {"total": 234, "code": 134, "comments": 67, "blank": 33}}, {"path": "src/components/workbook-actions.tsx", "category": "COMPONENTS", "importance": "MÉDIO", "complexity": "MÉDIA", "lines": {"total": 160, "code": 131, "comments": 9, "blank": 20}}, {"path": "src/lib/mock-db.ts", "category": "LIBRARIES", "importance": "MÉDIO", "complexity": "MÉDIA", "lines": {"total": 179, "code": 130, "comments": 23, "blank": 26}}, {"path": "src/lib/supabase/client.ts", "category": "LIBRARIES", "importance": "MÉDIO", "complexity": "MÉDIA", "lines": {"total": 179, "code": 130, "comments": 28, "blank": 21}}, {"path": "src/components/command-feedback.tsx", "category": "COMPONENTS", "importance": "MÉDIO", "complexity": "MÉDIA", "lines": {"total": 143, "code": 128, "comments": 1, "blank": 14}}, {"path": "src/lib/performance-monitor.ts", "category": "LIBRARIES", "importance": "MÉDIO", "complexity": "MÉDIA", "lines": {"total": 202, "code": 127, "comments": 46, "blank": 29}}, {"path": "src/components/ui/button.tsx", "category": "UI_COMPONENTS", "importance": "MÉDIO", "complexity": "MÉDIA", "lines": {"total": 146, "code": 126, "comments": 7, "blank": 13}}, {"path": "src/types/custom.d.ts", "category": "TYPES", "importance": "MÉDIO", "complexity": "MÉDIA", "lines": {"total": 169, "code": 125, "comments": 21, "blank": 23}}, {"path": "src/lib/stripe.ts", "category": "LIBRARIES", "importance": "MÉDIO", "complexity": "MÉDIA", "lines": {"total": 178, "code": 124, "comments": 33, "blank": 21}}, {"path": "src/types/global-types.ts", "category": "TYPES", "importance": "MÉDIO", "complexity": "MÉDIA", "lines": {"total": 167, "code": 122, "comments": 17, "blank": 28}}, {"path": "src/components/ai-status-indicator.tsx", "category": "COMPONENTS", "importance": "MÉDIO", "complexity": "MÉDIA", "lines": {"total": 134, "code": 120, "comments": 2, "blank": 12}}, {"path": "src/hooks/useDesktopBridge.ts", "category": "HOOKS", "importance": "MÉDIO", "complexity": "MÉDIA", "lines": {"total": 164, "code": 119, "comments": 25, "blank": 20}}, {"path": "src/hooks/useSocket.ts", "category": "HOOKS", "importance": "MÉDIO", "complexity": "MÉDIA", "lines": {"total": 159, "code": 119, "comments": 17, "blank": 23}}, {"path": "src/components/nav-bar.tsx", "category": "COMPONENTS", "importance": "MÉDIO", "complexity": "MÉDIA", "lines": {"total": 302, "code": 118, "comments": 157, "blank": 27}}, {"path": "src/styles/excel.css", "category": "STYLES", "importance": "MÉDIO", "complexity": "MÉDIA", "lines": {"total": 176, "code": 118, "comments": 31, "blank": 27}}, {"path": "src/components/ui/motion.tsx", "category": "UI_COMPONENTS", "importance": "MÉDIO", "complexity": "MÉDIA", "lines": {"total": 166, "code": 115, "comments": 28, "blank": 23}}, {"path": "src/lib/error-reporting.ts", "category": "LIBRARIES", "importance": "MÉDIO", "complexity": "MÉDIA", "lines": {"total": 177, "code": 113, "comments": 40, "blank": 24}}, {"path": "src/components/ui/toast.tsx", "category": "UI_COMPONENTS", "importance": "MÉDIO", "complexity": "MÉDIA", "lines": {"total": 125, "code": 111, "comments": 0, "blank": 14}}, {"path": "src/server/api-usage.ts", "category": "OTHER", "importance": "MÉDIO", "complexity": "MÉDIA", "lines": {"total": 154, "code": 111, "comments": 24, "blank": 19}}, {"path": "src/components/create-workbook-form.tsx", "category": "COMPONENTS", "importance": "MÉDIO", "complexity": "MÉDIA", "lines": {"total": 129, "code": 110, "comments": 6, "blank": 13}}, {"path": "src/components/dashboard/MetricCards.tsx", "category": "DASHBOARD_COMPONENTS", "importance": "MÉDIO", "complexity": "MÉDIA", "lines": {"total": 259, "code": 109, "comments": 126, "blank": 24}}, {"path": "src/lib/security/edge-csrf.ts", "category": "LIBRARIES", "importance": "MÉDIO", "complexity": "MÉDIA", "lines": {"total": 161, "code": 109, "comments": 26, "blank": 26}}, {"path": "src/server/db/client.ts", "category": "OTHER", "importance": "MÉDIO", "complexity": "MÉDIA", "lines": {"total": 159, "code": 109, "comments": 31, "blank": 19}}, {"path": "src/lib/bridge/intentDetector.ts", "category": "LIBRARIES", "importance": "MÉDIO", "complexity": "MÉDIA", "lines": {"total": 144, "code": 108, "comments": 20, "blank": 16}}, {"path": "src/lib/collaboration/store.ts", "category": "LIBRARIES", "importance": "MÉDIO", "complexity": "MÉDIA", "lines": {"total": 194, "code": 107, "comments": 62, "blank": 25}}, {"path": "src/components/client-scripts.tsx", "category": "COMPONENTS", "importance": "MÉDIO", "complexity": "MÉDIA", "lines": {"total": 137, "code": 106, "comments": 15, "blank": 16}}, {"path": "src/components/ui/sheet.tsx", "category": "UI_COMPONENTS", "importance": "MÉDIO", "complexity": "MÉDIA", "lines": {"total": 122, "code": 106, "comments": 0, "blank": 16}}, {"path": "src/components/icons/index.ts", "category": "COMPONENTS", "importance": "MÉDIO", "complexity": "MÉDIA", "lines": {"total": 150, "code": 105, "comments": 25, "blank": 20}}, {"path": "src/server/db/edge-client.ts", "category": "OTHER", "importance": "MÉDIO", "complexity": "MÉDIA", "lines": {"total": 134, "code": 105, "comments": 14, "blank": 15}}, {"path": "src/components/ui/alert-dialog.tsx", "category": "UI_COMPONENTS", "importance": "MÉDIO", "complexity": "MÉDIA", "lines": {"total": 116, "code": 102, "comments": 0, "blank": 14}}, {"path": "src/utils/type-helpers.ts", "category": "UTILITIES", "importance": "MÉDIO", "complexity": "MÉDIA", "lines": {"total": 178, "code": 102, "comments": 52, "blank": 24}}, {"path": "src/components/examples/AnimationWrapperExample.tsx", "category": "COMPONENTS", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 116, "code": 100, "comments": 5, "blank": 11}}, {"path": "src/lib/security/csrf-protection.ts", "category": "LIBRARIES", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 196, "code": 100, "comments": 68, "blank": 28}}, {"path": "src/utils/regex-utils.ts", "category": "UTILITIES", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 188, "code": 100, "comments": 68, "blank": 20}}, {"path": "src/types/ui-components.d.ts", "category": "TYPES", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 153, "code": 99, "comments": 22, "blank": 32}}, {"path": "src/lib/ai/provider.ts", "category": "LIBRARIES", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 167, "code": 98, "comments": 49, "blank": 20}}, {"path": "src/types/sheet.ts", "category": "TYPES", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 151, "code": 98, "comments": 40, "blank": 13}}, {"path": "src/types/socket.d.ts", "category": "TYPES", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 117, "code": 97, "comments": 8, "blank": 12}}, {"path": "src/components/storage/StorageManager.tsx", "category": "COMPONENTS", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 234, "code": 96, "comments": 114, "blank": 24}}, {"path": "src/components/upload-button.tsx", "category": "COMPONENTS", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 117, "code": 96, "comments": 7, "blank": 14}}, {"path": "src/components/providers/csrf-provider.tsx", "category": "COMPONENTS", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 121, "code": 95, "comments": 7, "blank": 19}}, {"path": "src/components/ui/card.tsx", "category": "UI_COMPONENTS", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 120, "code": 95, "comments": 7, "blank": 18}}, {"path": "src/lib/ai/excel-desktop-connector.ts", "category": "LIBRARIES", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 153, "code": 94, "comments": 39, "blank": 20}}, {"path": "src/server/trpc/trpc.ts", "category": "OTHER", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 138, "code": 94, "comments": 24, "blank": 20}}, {"path": "src/types/prisma-extensions.d.ts", "category": "TYPES", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 159, "code": 94, "comments": 50, "blank": 15}}, {"path": "src/lib/utils/rate-limit.ts", "category": "LIBRARIES", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 144, "code": 93, "comments": 27, "blank": 24}}, {"path": "src/server/db/utils.ts", "category": "OTHER", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 142, "code": 92, "comments": 33, "blank": 17}}, {"path": "src/components/billing/stripe-script-provider.tsx", "category": "COMPONENTS", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 116, "code": 91, "comments": 6, "blank": 19}}, {"path": "src/components/ui/dialog.tsx", "category": "UI_COMPONENTS", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 105, "code": 91, "comments": 0, "blank": 14}}, {"path": "src/components/user-onboarding/TourStep.tsx", "category": "COMPONENTS", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 188, "code": 88, "comments": 74, "blank": 26}}, {"path": "src/server/trpc/init-procedure.ts", "category": "OTHER", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 141, "code": 88, "comments": 34, "blank": 19}}, {"path": "src/lib/api-usage.ts", "category": "LIBRARIES", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 126, "code": 87, "comments": 23, "blank": 16}}, {"path": "src/lib/excel/operationUtils.ts", "category": "LIBRARIES", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 160, "code": 87, "comments": 52, "blank": 21}}, {"path": "src/lib/ai/error-interceptor.ts", "category": "LIBRARIES", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 137, "code": 86, "comments": 24, "blank": 27}}, {"path": "src/lib/operations/utils.ts", "category": "LIBRARIES", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 148, "code": 86, "comments": 39, "blank": 23}}, {"path": "src/server/test-utils/mock-server.ts", "category": "OTHER", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 132, "code": 85, "comments": 27, "blank": 20}}, {"path": "src/types/ai-processing.d.ts", "category": "TYPES", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 136, "code": 85, "comments": 35, "blank": 16}}, {"path": "src/components/ui/animation-wrapper.tsx", "category": "UI_COMPONENTS", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 113, "code": 84, "comments": 17, "blank": 12}}, {"path": "src/config/ai-prompts.ts", "category": "CONFIG", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 113, "code": 84, "comments": 9, "blank": 20}}, {"path": "src/components/billing/customer-portal.tsx", "category": "COMPONENTS", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 176, "code": 83, "comments": 81, "blank": 12}}, {"path": "src/components/ui/table.tsx", "category": "UI_COMPONENTS", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 95, "code": 83, "comments": 0, "blank": 12}}, {"path": "src/server/init.ts", "category": "OTHER", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 135, "code": 83, "comments": 28, "blank": 24}}, {"path": "src/lib/ai/webpack-interceptor.ts", "category": "LIBRARIES", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 116, "code": 81, "comments": 17, "blank": 18}}, {"path": "src/lib/ai/gemini-api.ts", "category": "LIBRARIES", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 208, "code": 80, "comments": 91, "blank": 37}}, {"path": "src/schemas/socket.ts", "category": "OTHER", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 107, "code": 79, "comments": 15, "blank": 13}}, {"path": "src/styles/typography.css", "category": "STYLES", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 97, "code": 79, "comments": 3, "blank": 15}}, {"path": "src/components/rsc-error-suppressor.tsx", "category": "COMPONENTS", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 123, "code": 78, "comments": 21, "blank": 24}}, {"path": "src/components/ui/optimized-button.tsx", "category": "UI_COMPONENTS", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 131, "code": 78, "comments": 29, "blank": 24}}, {"path": "src/lib/edge-logger.ts", "category": "LIBRARIES", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 109, "code": 77, "comments": 14, "blank": 18}}, {"path": "src/components/dashboard/RecentActivity.tsx", "category": "DASHBOARD_COMPONENTS", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 304, "code": 76, "comments": 204, "blank": 24}}, {"path": "src/components/debug/PerformanceDebugPanel.tsx", "category": "COMPONENTS", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 253, "code": 75, "comments": 154, "blank": 24}}, {"path": "src/components/export-button.tsx", "category": "COMPONENTS", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 89, "code": 75, "comments": 6, "blank": 8}}, {"path": "src/components/ui/error-message.tsx", "category": "UI_COMPONENTS", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 100, "code": 75, "comments": 13, "blank": 12}}, {"path": "src/components/ui/virtual-table.tsx", "category": "UI_COMPONENTS", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 182, "code": 75, "comments": 89, "blank": 18}}, {"path": "src/lib/critical-css.ts", "category": "LIBRARIES", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 106, "code": 75, "comments": 15, "blank": 16}}, {"path": "src/app/sitemap.ts", "category": "OTHER", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 82, "code": 74, "comments": 3, "blank": 5}}, {"path": "src/server/websocket.ts", "category": "OTHER", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 123, "code": 74, "comments": 30, "blank": 19}}, {"path": "src/app/providers.tsx", "category": "OTHER", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 153, "code": 73, "comments": 66, "blank": 14}}, {"path": "src/components/user-nav.tsx", "category": "COMPONENTS", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 85, "code": 73, "comments": 3, "blank": 9}}, {"path": "src/lib/security/secrets-manager.ts", "category": "LIBRARIES", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 105, "code": 73, "comments": 13, "blank": 19}}, {"path": "src/lib/animations.ts", "category": "LIBRARIES", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 120, "code": 72, "comments": 36, "blank": 12}}, {"path": "src/lib/bridge/types.ts", "category": "LIBRARIES", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 93, "code": 72, "comments": 9, "blank": 12}}, {"path": "src/middleware/metrics.ts", "category": "OTHER", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 109, "code": 72, "comments": 21, "blank": 16}}, {"path": "src/types/collaboration.d.ts", "category": "TYPES", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 89, "code": 71, "comments": 10, "blank": 8}}, {"path": "src/hooks/useKeyboardShortcut.ts", "category": "HOOKS", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 99, "code": 70, "comments": 15, "blank": 14}}, {"path": "src/middleware/core.ts", "category": "OTHER", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 98, "code": 69, "comments": 18, "blank": 11}}, {"path": "src/types/vertex-ai.d.ts", "category": "TYPES", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 93, "code": 69, "comments": 13, "blank": 11}}, {"path": "src/lib/security/sanitize-html.ts", "category": "LIBRARIES", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 116, "code": 68, "comments": 37, "blank": 11}}, {"path": "src/server/db/universal-client.ts", "category": "OTHER", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 99, "code": 68, "comments": 16, "blank": 15}}, {"path": "src/components/monitoring/performance-monitor.tsx", "category": "COMPONENTS", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 91, "code": 67, "comments": 9, "blank": 15}}, {"path": "src/lib/ai/constants.ts", "category": "LIBRARIES", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 94, "code": 66, "comments": 14, "blank": 14}}, {"path": "src/lib/operations/processor.ts", "category": "LIBRARIES", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 92, "code": 66, "comments": 18, "blank": 8}}, {"path": "src/utils/data/json.ts", "category": "UTILITIES", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 117, "code": 66, "comments": 39, "blank": 12}}, {"path": "src/components/collaboration-panel.tsx", "category": "COMPONENTS", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 197, "code": 65, "comments": 114, "blank": 18}}, {"path": "src/utils/api-response.ts", "category": "UTILITIES", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 80, "code": 65, "comments": 1, "blank": 14}}, {"path": "src/components/command-preview.tsx", "category": "COMPONENTS", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 71, "code": 64, "comments": 0, "blank": 7}}, {"path": "src/utils/http/api-response.ts", "category": "UTILITIES", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 127, "code": 64, "comments": 49, "blank": 14}}, {"path": "src/lib/excel/executionOperations.ts", "category": "LIBRARIES", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 128, "code": 63, "comments": 50, "blank": 15}}, {"path": "src/components/DesktopBridgeProvider.tsx", "category": "COMPONENTS", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 92, "code": 62, "comments": 11, "blank": 19}}, {"path": "src/types/component-extensions.d.ts", "category": "TYPES", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 87, "code": 62, "comments": 11, "blank": 14}}, {"path": "src/components/ui/action-button.tsx", "category": "UI_COMPONENTS", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 75, "code": 61, "comments": 6, "blank": 8}}, {"path": "src/types/events.d.ts", "category": "TYPES", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 87, "code": 61, "comments": 15, "blank": 11}}, {"path": "src/components/ui/loading-optimization.tsx", "category": "UI_COMPONENTS", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 90, "code": 60, "comments": 14, "blank": 16}}, {"path": "src/lib/security/pattern-detection.ts", "category": "LIBRARIES", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 96, "code": 59, "comments": 30, "blank": 7}}, {"path": "src/lib/ai/client-safe.ts", "category": "LIBRARIES", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 86, "code": 58, "comments": 15, "blank": 13}}, {"path": "src/components/ClientLayoutWrapper.tsx", "category": "COMPONENTS", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 86, "code": 57, "comments": 16, "blank": 13}}, {"path": "src/lib/security/sanitization-excel.ts", "category": "LIBRARIES", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 100, "code": 57, "comments": 29, "blank": 14}}, {"path": "src/middleware/rate-limit.ts", "category": "OTHER", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 92, "code": 57, "comments": 20, "blank": 15}}, {"path": "src/utils/logger-utils.ts", "category": "UTILITIES", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 129, "code": 57, "comments": 53, "blank": 19}}, {"path": "src/config/worker-config.ts", "category": "CONFIG", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 102, "code": 56, "comments": 30, "blank": 16}}, {"path": "src/hooks/useFileOperation.ts", "category": "HOOKS", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 76, "code": 56, "comments": 6, "blank": 14}}, {"path": "src/lib/ai/ai-factory.ts", "category": "LIBRARIES", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 89, "code": 56, "comments": 20, "blank": 13}}, {"path": "src/lib/operations/index.ts", "category": "LIBRARIES", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 79, "code": 56, "comments": 16, "blank": 7}}, {"path": "src/lib/trpc.tsx", "category": "LIBRARIES", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 90, "code": 56, "comments": 22, "blank": 12}}, {"path": "src/providers/toast-wrapper.tsx", "category": "OTHER", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 75, "code": 56, "comments": 10, "blank": 9}}, {"path": "src/schemas/workbook.ts", "category": "OTHER", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 75, "code": 56, "comments": 10, "blank": 9}}, {"path": "src/components/examples/MotionSafeExample.tsx", "category": "COMPONENTS", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 66, "code": 55, "comments": 4, "blank": 7}}, {"path": "src/lib/ai/mock-vertex-service.ts", "category": "LIBRARIES", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 88, "code": 53, "comments": 22, "blank": 13}}, {"path": "src/utils/functions/index.ts", "category": "UTILITIES", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 120, "code": 53, "comments": 53, "blank": 14}}, {"path": "src/utils/safe-access.ts", "category": "UTILITIES", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 93, "code": 53, "comments": 30, "blank": 10}}, {"path": "src/types/shared.d.ts", "category": "TYPES", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 99, "code": 52, "comments": 35, "blank": 12}}, {"path": "src/types/bridge.d.ts", "category": "TYPES", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 73, "code": 51, "comments": 12, "blank": 10}}, {"path": "src/components/ui/form-field-styles.ts", "category": "UI_COMPONENTS", "importance": "BAIXO", "complexity": "MÉDIA", "lines": {"total": 89, "code": 50, "comments": 28, "blank": 11}}, {"path": "src/utils/usage-example.ts", "category": "UTILITIES", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 105, "code": 49, "comments": 37, "blank": 19}}, {"path": "src/components/ui/tabs.tsx", "category": "UI_COMPONENTS", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 56, "code": 48, "comments": 0, "blank": 8}}, {"path": "src/types/api.d.ts", "category": "TYPES", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 65, "code": 48, "comments": 7, "blank": 10}}, {"path": "src/components/workbook/QuickCommands.tsx", "category": "WORKBOOK_COMPONENTS", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 61, "code": 47, "comments": 6, "blank": 8}}, {"path": "src/styles/base.css", "category": "STYLES", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 153, "code": 47, "comments": 88, "blank": 18}}, {"path": "src/utils/json.ts", "category": "UTILITIES", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 78, "code": 47, "comments": 22, "blank": 9}}, {"path": "src/components/theme-provider.tsx", "category": "COMPONENTS", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 54, "code": 45, "comments": 1, "blank": 8}}, {"path": "src/components/ui/alert.tsx", "category": "UI_COMPONENTS", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 52, "code": 45, "comments": 0, "blank": 7}}, {"path": "src/components/ui/tab-button.tsx", "category": "UI_COMPONENTS", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 52, "code": 45, "comments": 0, "blank": 7}}, {"path": "src/lib/utils/error-utils.ts", "category": "LIBRARIES", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 86, "code": 45, "comments": 35, "blank": 6}}, {"path": "src/types/errors.d.ts", "category": "TYPES", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 61, "code": 45, "comments": 9, "blank": 7}}, {"path": "src/components/feature-card.tsx", "category": "COMPONENTS", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 54, "code": 44, "comments": 6, "blank": 4}}, {"path": "src/components/ui/input.tsx", "category": "UI_COMPONENTS", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 53, "code": 43, "comments": 1, "blank": 9}}, {"path": "src/components/workbook-list-item.tsx", "category": "COMPONENTS", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 51, "code": 43, "comments": 3, "blank": 5}}, {"path": "src/hooks/use-media-query.ts", "category": "HOOKS", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 76, "code": 43, "comments": 20, "blank": 13}}, {"path": "src/lib/session-helpers.ts", "category": "LIBRARIES", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 62, "code": 43, "comments": 9, "blank": 10}}, {"path": "src/components/workbook/AIPanel.tsx", "category": "WORKBOOK_COMPONENTS", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 202, "code": 42, "comments": 141, "blank": 19}}, {"path": "src/lib/ai/types.ts", "category": "LIBRARIES", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 103, "code": 42, "comments": 34, "blank": 27}}, {"path": "src/types/trpc.d.ts", "category": "TYPES", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 58, "code": 42, "comments": 8, "blank": 8}}, {"path": "src/utils/optional-helpers.ts", "category": "UTILITIES", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 89, "code": 42, "comments": 36, "blank": 11}}, {"path": "src/components/ui/avatar.tsx", "category": "UI_COMPONENTS", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 48, "code": 41, "comments": 0, "blank": 7}}, {"path": "src/components/ui/scroll-area.tsx", "category": "UI_COMPONENTS", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 47, "code": 41, "comments": 0, "blank": 6}}, {"path": "src/lib/excel/testUtils.ts", "category": "LIBRARIES", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 76, "code": 41, "comments": 26, "blank": 9}}, {"path": "src/lib/memory-monitor.ts", "category": "LIBRARIES", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 55, "code": 40, "comments": 6, "blank": 9}}, {"path": "src/contexts/LocaleContext.tsx", "category": "OTHER", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 56, "code": 39, "comments": 7, "blank": 10}}, {"path": "src/utils/index.ts", "category": "UTILITIES", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 91, "code": 39, "comments": 36, "blank": 16}}, {"path": "src/components/ui/spinner.tsx", "category": "UI_COMPONENTS", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 49, "code": 38, "comments": 3, "blank": 8}}, {"path": "src/hooks/useI18n.tsx", "category": "HOOKS", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 58, "code": 38, "comments": 9, "blank": 11}}, {"path": "src/types/next-auth.d.ts", "category": "TYPES", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 56, "code": 38, "comments": 13, "blank": 5}}, {"path": "src/components/collaboration-indicator.tsx", "category": "COMPONENTS", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 115, "code": 37, "comments": 65, "blank": 13}}, {"path": "src/types/component-fixes.d.ts", "category": "TYPES", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 53, "code": 36, "comments": 7, "blank": 10}}, {"path": "src/context-providers/LocaleProvider.tsx", "category": "OTHER", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 59, "code": 35, "comments": 14, "blank": 10}}, {"path": "src/types/test-types.d.ts", "category": "TYPES", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 49, "code": 35, "comments": 7, "blank": 7}}, {"path": "src/types/ui-components.ts", "category": "TYPES", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 44, "code": 35, "comments": 2, "blank": 7}}, {"path": "src/components/ui/index.ts", "category": "UI_COMPONENTS", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 42, "code": 34, "comments": 3, "blank": 5}}, {"path": "src/components/ui/textarea.tsx", "category": "UI_COMPONENTS", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 45, "code": 34, "comments": 2, "blank": 9}}, {"path": "src/lib/auth-config.ts", "category": "LIBRARIES", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 68, "code": 34, "comments": 24, "blank": 10}}, {"path": "src/lib/ai/conditional-import.ts", "category": "LIBRARIES", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 50, "code": 32, "comments": 12, "blank": 6}}, {"path": "src/lib/excel/typeUtils.ts", "category": "LIBRARIES", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 67, "code": 32, "comments": 25, "blank": 10}}, {"path": "src/styles/utilities.css", "category": "STYLES", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 95, "code": 32, "comments": 49, "blank": 14}}, {"path": "src/types/ai.d.ts", "category": "TYPES", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 49, "code": 32, "comments": 12, "blank": 5}}, {"path": "src/components/ImageWithFallback.tsx", "category": "COMPONENTS", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 41, "code": 31, "comments": 3, "blank": 7}}, {"path": "src/components/user-onboarding/ActiveTour.tsx", "category": "COMPONENTS", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 49, "code": 31, "comments": 6, "blank": 12}}, {"path": "src/components/workbook/SpreadsheetToolbar.tsx", "category": "WORKBOOK_COMPONENTS", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 114, "code": 31, "comments": 69, "blank": 14}}, {"path": "src/components/ui/empty-state.tsx", "category": "UI_COMPONENTS", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 39, "code": 30, "comments": 3, "blank": 6}}, {"path": "src/components/ui/toaster.tsx", "category": "UI_COMPONENTS", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 34, "code": 30, "comments": 0, "blank": 4}}, {"path": "src/hooks/use-media-query.tsx", "category": "HOOKS", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 55, "code": 30, "comments": 16, "blank": 9}}, {"path": "src/components/mode-toggle.tsx", "category": "COMPONENTS", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 37, "code": 29, "comments": 3, "blank": 5}}, {"path": "src/components/ui/badge.tsx", "category": "UI_COMPONENTS", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 35, "code": 29, "comments": 0, "blank": 6}}, {"path": "src/app/not-found.tsx", "category": "OTHER", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 34, "code": 28, "comments": 0, "blank": 6}}, {"path": "src/config/auth-flags.ts", "category": "CONFIG", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 71, "code": 28, "comments": 31, "blank": 12}}, {"path": "src/context-providers/ProviderComposer.tsx", "category": "OTHER", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 43, "code": 28, "comments": 7, "blank": 8}}, {"path": "src/lib/excel/index.ts", "category": "LIBRARIES", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 37, "code": 28, "comments": 5, "blank": 4}}, {"path": "src/types/component-props.ts", "category": "TYPES", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 36, "code": 28, "comments": 3, "blank": 5}}, {"path": "src/components/ui/card-grid.tsx", "category": "UI_COMPONENTS", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 37, "code": 27, "comments": 3, "blank": 7}}, {"path": "src/hooks/useReducedMotion.ts", "category": "HOOKS", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 54, "code": 27, "comments": 18, "blank": 9}}, {"path": "src/types/optional-types.ts", "category": "TYPES", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 40, "code": 27, "comments": 5, "blank": 8}}, {"path": "src/components/theme-toggle.tsx", "category": "COMPONENTS", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 35, "code": 26, "comments": 2, "blank": 7}}, {"path": "src/context-providers/ThemeProvider.tsx", "category": "OTHER", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 43, "code": 26, "comments": 13, "blank": 4}}, {"path": "src/lib/utils/request.ts", "category": "LIBRARIES", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 44, "code": 26, "comments": 12, "blank": 6}}, {"path": "src/components/ui/popover.tsx", "category": "UI_COMPONENTS", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 32, "code": 25, "comments": 0, "blank": 7}}, {"path": "src/components/ui/switch.tsx", "category": "UI_COMPONENTS", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 30, "code": 25, "comments": 0, "blank": 5}}, {"path": "src/types/form-events.d.ts", "category": "TYPES", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 46, "code": 25, "comments": 10, "blank": 11}}, {"path": "src/types/regex-types.ts", "category": "TYPES", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 35, "code": 25, "comments": 5, "blank": 5}}, {"path": "src/components/ui/checkbox.tsx", "category": "UI_COMPONENTS", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 29, "code": 24, "comments": 0, "blank": 5}}, {"path": "src/lib/validators/workbook.ts", "category": "LIBRARIES", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 49, "code": 24, "comments": 18, "blank": 7}}, {"path": "src/components/chart-display-lazy.tsx", "category": "COMPONENTS", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 32, "code": 22, "comments": 3, "blank": 7}}, {"path": "src/components/ui/tooltip.tsx", "category": "UI_COMPONENTS", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 29, "code": 22, "comments": 0, "blank": 7}}, {"path": "src/types/optional-types.d.ts", "category": "TYPES", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 84, "code": 22, "comments": 51, "blank": 11}}, {"path": "src/types/three-environment.ts", "category": "TYPES", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 26, "code": 22, "comments": 2, "blank": 2}}, {"path": "src/app/robots.ts", "category": "OTHER", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 25, "code": 21, "comments": 1, "blank": 3}}, {"path": "src/components/ui/loading-indicator.tsx", "category": "UI_COMPONENTS", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 30, "code": 21, "comments": 5, "blank": 4}}, {"path": "src/components/ui/progress.tsx", "category": "UI_COMPONENTS", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 26, "code": 21, "comments": 0, "blank": 5}}, {"path": "src/components/ui/separator.tsx", "category": "UI_COMPONENTS", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 25, "code": 21, "comments": 0, "blank": 4}}, {"path": "src/lib/api-tracker.ts", "category": "LIBRARIES", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 47, "code": 21, "comments": 20, "blank": 6}}, {"path": "src/middleware/auth-callback-fix.ts", "category": "OTHER", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 39, "code": 21, "comments": 9, "blank": 9}}, {"path": "src/lib/desktop-bridge.ts", "category": "LIBRARIES", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 55, "code": 20, "comments": 25, "blank": 10}}, {"path": "src/components/settings-button.tsx", "category": "COMPONENTS", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 23, "code": 18, "comments": 0, "blank": 5}}, {"path": "src/hooks/useSSRSafeSession.ts", "category": "HOOKS", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 30, "code": 18, "comments": 6, "blank": 6}}, {"path": "src/lib/auth.ts", "category": "LIBRARIES", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 45, "code": 18, "comments": 20, "blank": 7}}, {"path": "src/components/realtime/OnlineUsers.tsx", "category": "COMPONENTS", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 224, "code": 17, "comments": 188, "blank": 19}}, {"path": "src/types/gtag.d.ts", "category": "TYPES", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 23, "code": 17, "comments": 3, "blank": 3}}, {"path": "src/types/regex-types.d.ts", "category": "TYPES", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 60, "code": 17, "comments": 36, "blank": 7}}, {"path": "src/app/global-error.tsx", "category": "OTHER", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 24, "code": 16, "comments": 4, "blank": 4}}, {"path": "src/components/ClientOnly.tsx", "category": "COMPONENTS", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 27, "code": 16, "comments": 4, "blank": 7}}, {"path": "src/components/ui/label.tsx", "category": "UI_COMPONENTS", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 22, "code": 16, "comments": 0, "blank": 6}}, {"path": "src/components/ui/motion-safe.tsx", "category": "UI_COMPONENTS", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 36, "code": 16, "comments": 13, "blank": 7}}, {"path": "src/types/form-event-extensions.d.ts", "category": "TYPES", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 31, "code": 16, "comments": 8, "blank": 7}}, {"path": "src/context-providers/index.ts", "category": "OTHER", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 34, "code": 14, "comments": 15, "blank": 5}}, {"path": "src/types/jest.d.ts", "category": "TYPES", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 23, "code": 13, "comments": 6, "blank": 4}}, {"path": "src/types/service-extensions.d.ts", "category": "TYPES", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 23, "code": 13, "comments": 6, "blank": 4}}, {"path": "src/components/ui/visually-hidden.tsx", "category": "UI_COMPONENTS", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 19, "code": 12, "comments": 3, "blank": 4}}, {"path": "src/config/site.ts", "category": "CONFIG", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 13, "code": 12, "comments": 0, "blank": 1}}, {"path": "src/hooks/useAnalytics.ts", "category": "HOOKS", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 26, "code": 12, "comments": 11, "blank": 3}}, {"path": "src/lib/prisma.ts", "category": "LIBRARIES", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 20, "code": 12, "comments": 2, "blank": 6}}, {"path": "src/types/analytics.ts", "category": "TYPES", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 23, "code": 11, "comments": 9, "blank": 3}}, {"path": "src/components/command-examples-wrapper.tsx", "category": "COMPONENTS", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 16, "code": 10, "comments": 0, "blank": 6}}, {"path": "src/components/command-selector.tsx", "category": "COMPONENTS", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 18, "code": 10, "comments": 2, "blank": 6}}, {"path": "src/instrumentation-client.ts", "category": "OTHER", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 29, "code": 10, "comments": 10, "blank": 9}}, {"path": "src/instrumentation.ts", "category": "OTHER", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 14, "code": 10, "comments": 0, "blank": 4}}, {"path": "src/hooks/useCleanup.ts", "category": "HOOKS", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 14, "code": 8, "comments": 4, "blank": 2}}, {"path": "src/lib/ai/regex-test.ts", "category": "LIBRARIES", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 21, "code": 8, "comments": 9, "blank": 4}}, {"path": "src/server/trpc/react.tsx", "category": "OTHER", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 12, "code": 8, "comments": 0, "blank": 4}}, {"path": "src/app/globals.css", "category": "OTHER", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 634, "code": 6, "comments": 537, "blank": 91}}, {"path": "src/components/providers.tsx", "category": "COMPONENTS", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 9, "code": 6, "comments": 0, "blank": 3}}, {"path": "src/types/chart-extensions.ts", "category": "TYPES", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 9, "code": 6, "comments": 2, "blank": 1}}, {"path": "src/components/ui/skeleton.tsx", "category": "UI_COMPONENTS", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 8, "code": 5, "comments": 0, "blank": 3}}, {"path": "src/hooks/index.ts", "category": "HOOKS", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 6, "code": 5, "comments": 0, "blank": 1}}, {"path": "src/components/locale-switcher.tsx", "category": "COMPONENTS", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 8, "code": 4, "comments": 2, "blank": 2}}, {"path": "src/styles/index.css", "category": "STYLES", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 14, "code": 4, "comments": 5, "blank": 5}}, {"path": "src/components/ui/theme-toggle/index.ts", "category": "UI_COMPONENTS", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 2, "code": 1, "comments": 0, "blank": 1}}, {"path": "src/lib/ai/index.ts", "category": "LIBRARIES", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 8, "code": 1, "comments": 5, "blank": 2}}, {"path": "src/lib/index.ts", "category": "LIBRARIES", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 2, "code": 1, "comments": 0, "blank": 1}}, {"path": "src/workers/excel-operations.worker.ts", "category": "OTHER", "importance": "BAIXO", "complexity": "BAIXA", "lines": {"total": 1, "code": 0, "comments": 0, "blank": 1}}]}