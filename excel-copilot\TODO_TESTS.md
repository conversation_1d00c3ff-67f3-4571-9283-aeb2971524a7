# Tarefas Pendentes para Testes

## Correções Prioritárias

1. **Correção do arquivo `sanitization.ts`** ✅

   - ~~Remover duplicação de funções~~
   - ~~Garantir que todos os padrões de segurança sejam aplicados corretamente~~
   - ~~Verificar cobertura de testes após as correções~~

2. **Migração para MSW v2** ⚠️

   - Atualizar configuração do mock server
   - Adaptar handlers existentes
   - Verificar compatibilidade com Next.js

3. **Correção de Importações em Testes** ⚠️
   - Resolver problemas com módulos não encontrados
   - Verificar configuração de alias no Jest

## Novos Testes a Implementar

1. **Testes de Integração para API** ⚠️

   - Testar endpoints de workbook
   - Testar endpoints de autenticação
   - Testar endpoints de análise

2. **Testes de E2E** ⚠️
   - Implementar fluxo de upload de arquivo
   - Implementar fluxo de processamento de dados
   - Implementar fluxo de exportação

## Melhorias na Infraestrutura de Testes

1. **Melhorias para CI/CD** ⚠️

   - Otimizar tempo de execução de testes
   - Implementar paralelização de testes
   - Configurar relatórios automatizados

2. **Documentação de Testes** ⚠️
   - Criar guia de boas práticas
   - Documentar padrões para novos testes
   - Atualizar README com instruções de testes

## Lista de Verificação para Concluir

- [ ] Cobertura geral de testes atingiu 80%
- [ ] Não há erros de linting nos testes
- [ ] MSW configurado corretamente
- [ ] CI/CD executando todos os testes sem falhas
- [ ] Documentação atualizada
