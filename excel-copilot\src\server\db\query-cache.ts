import { ENV } from '@/config/unified-environment';
import { logger } from '@/lib/logger';

// Não usamos diretamente o NodeCache para evitar importar dependências extras
// que podem não estar no projeto, então implementamos um cache simples

// Definir TTL padrão usando um valor seguro já que ENV.CACHE.QUERY_CACHE_TTL pode não existir
const DEFAULT_TTL = ENV.CACHE?.DEFAULT_TTL || 60; // 60 segundos padrão
const MAX_CACHE_SIZE = 1000;

interface CacheEntry<T> {
  value: T;
  expiry: number;
}

interface CacheOptions {
  ttl?: number;
  tags?: string[];
}

class SimpleCache {
  private cache: Map<string, CacheEntry<unknown>> = new Map();
  private checkInterval: NodeJS.Timeout | null = null;
  private stats = {
    hits: 0,
    misses: 0,
    sets: 0,
    evictions: 0,
  };

  constructor() {
    // Configurar limpeza periódica
    this.checkInterval = setInterval(() => this.cleanup(), 30000); // a cada 30 segundos
  }

  set<T>(key: string, value: T, ttl: number = DEFAULT_TTL): void {
    // Se o cache estiver cheio, remover itens antigos
    if (this.cache.size >= MAX_CACHE_SIZE) {
      this.evictOldest();
    }

    this.cache.set(key, {
      value,
      expiry: Date.now() + ttl * 1000,
    });

    this.stats.sets++;
  }

  get<T>(key: string): T | null {
    const entry = this.cache.get(key);

    if (!entry) {
      this.stats.misses++;
      return null;
    }

    // Verificar expiração
    if (Date.now() > entry.expiry) {
      this.cache.delete(key);
      this.stats.misses++;
      return null;
    }

    this.stats.hits++;
    return entry.value as T;
  }

  delete(key: string): boolean {
    return this.cache.delete(key);
  }

  clear(): void {
    this.cache.clear();
  }

  // Invalida todos os itens que correspondem a um prefixo de chave
  invalidateByPrefix(prefix: string): number {
    let count = 0;
    for (const key of this.cache.keys()) {
      if (key.startsWith(prefix)) {
        this.cache.delete(key);
        count++;
      }
    }
    return count;
  }

  private cleanup(): void {
    const now = Date.now();
    for (const [key, entry] of this.cache.entries()) {
      if (now > entry.expiry) {
        this.cache.delete(key);
      }
    }
  }

  private evictOldest(): void {
    // Encontrar a entrada mais antiga
    let oldestKey: string | null = null;
    let oldestTime = Date.now();

    for (const [key, entry] of this.cache.entries()) {
      if (entry.expiry < oldestTime) {
        oldestKey = key;
        oldestTime = entry.expiry;
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey);
      this.stats.evictions++;
    }
  }

  /**
   * Desliga o cache e libera recursos
   */
  shutdown(): void {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
      this.checkInterval = null;
      logger.debug('Cache de consultas desligado');
    }
    this.clear();
  }

  getStats() {
    return {
      ...this.stats,
      size: this.cache.size,
      maxSize: MAX_CACHE_SIZE,
      hitRatio: this.stats.hits / (this.stats.hits + this.stats.misses) || 0,
    };
  }
}

// Instância global do cache
const queryCache = new SimpleCache();

/**
 * Wrapper para consultas com cache
 * @param queryFn Função que executa a consulta ao banco de dados
 * @param keyParts Partes que compõem a chave do cache (serão serializadas)
 * @param options Opções de cache (TTL, tags)
 * @returns Resultado da consulta, possivelmente do cache
 */
export async function cachifyQuery<T>(
  queryFn: () => Promise<T>,
  keyParts: unknown[],
  options: CacheOptions = {}
): Promise<T> {
  // Só usar cache em ambiente de produção
  // Em ambiente de desenvolvimento, executar sem cache
  if (!ENV.IS_PRODUCTION) {
    return queryFn();
  }

  // Criar chave do cache
  const cacheKey = createCacheKey(keyParts);

  // Tentar obter do cache
  const cachedResult = queryCache.get<T>(cacheKey);
  if (cachedResult !== null) {
    return cachedResult;
  }

  // Executar consulta
  const result = await queryFn();

  // Armazenar no cache
  queryCache.set(cacheKey, result, options.ttl);

  return result;
}

/**
 * Cria uma chave de cache a partir de partes
 */
function createCacheKey(parts: unknown[]): string {
  return parts
    .map(part => {
      if (part === null || part === undefined) {
        return 'null';
      }
      if (typeof part === 'object') {
        try {
          return JSON.stringify(part);
        } catch {
          return String(part);
        }
      }
      return String(part);
    })
    .join(':');
}

/**
 * Invalida caches por prefixo
 */
export function invalidateCache(prefix: string): number {
  return queryCache.invalidateByPrefix(prefix);
}

/**
 * Limpa todo o cache
 */
export function clearCache(): void {
  queryCache.clear();
}

/**
 * Retorna estatísticas do cache
 */
export function getQueryCacheStats() {
  return queryCache.getStats();
}

// Garantir limpeza ao desligar o processo
if (typeof process !== 'undefined') {
  process.on('beforeExit', () => {
    queryCache.shutdown();
    logger.debug('Cache de queries desligado');
  });
}
