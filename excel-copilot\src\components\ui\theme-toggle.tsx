'use client';

import { <PERSON>, <PERSON>, Loader2 } from 'lucide-react';
import { useTheme } from 'next-themes';
import { useEffect, useState, useRef } from 'react';

import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';

export function ThemeToggle({
  className,
  _variant = 'ghost',
  isMini = false,
}: {
  className?: string;
  _variant?: 'outline' | 'ghost' | 'default';
  isMini?: boolean;
}) {
  const [mounted, setMounted] = useState(false);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const observerRef = useRef<MutationObserver | null>(null);
  const { theme, setTheme, resolvedTheme } = useTheme();

  // Gerenciar a transição de tema de forma unificada
  useEffect(() => {
    // Inicializar o componente
    setMounted(true);

    // Função para adicionar a classe de transição
    const addTransitionClass = () => {
      setIsTransitioning(true);
      document.documentElement.classList.add('transitioning-theme');
      // Limpar classe após transição
      return setTimeout(() => {
        document.documentElement.classList.remove('transitioning-theme');
        setIsTransitioning(false);
      }, 300);
    };

    // Desconectar observador anterior (limpeza)
    if (observerRef.current) {
      observerRef.current.disconnect();
    }

    // Adicionar classe na montagem inicial
    const initialTimerId = addTransitionClass();

    // Criar e conectar novo observador com otimização
    const observer = new MutationObserver(mutations => {
      // Verificar se alguma mutação é relevante antes de aplicar transição
      const isThemeChange = mutations.some(
        mutation =>
          mutation.attributeName === 'class' &&
          (mutation.target as Element).classList.contains('dark') !==
            (mutation.oldValue || '').includes('dark')
      );

      if (isThemeChange) {
        addTransitionClass();
      }
    });

    observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['class'],
      attributeOldValue: true,
    });

    // Salvar observador para limpeza posterior
    observerRef.current = observer;

    // Limpeza ao desmontar
    return () => {
      clearTimeout(initialTimerId);
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, []);

  // Mostrar estado de carregamento enquanto o tema não está resolvido
  if (!mounted) {
    return (
      <div className={cn('flex items-center justify-center w-12 h-6', className)}>
        <Loader2 className="h-4 w-4 animate-spin" />
        <span className="sr-only">Carregando tema</span>
      </div>
    );
  }

  // Usar resolvedTheme para melhor detecção do tema atual
  const actualTheme = resolvedTheme || theme;
  const isDark = actualTheme === 'dark';

  const toggleTheme = () => {
    setTheme(isDark ? 'light' : 'dark');
  };

  // Versão simplificada para uso em espaços pequenos
  if (isMini) {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <button
              onClick={() => toggleTheme()}
              className={cn(
                'rounded-full w-8 h-8 flex items-center justify-center transition-all',
                'bg-muted/50 hover:bg-muted border border-border/50',
                'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring',
                className
              )}
              disabled={isTransitioning}
              aria-label={isDark ? 'Mudar para tema claro' : 'Mudar para tema escuro'}
            >
              {isTransitioning ? (
                <Loader2 className="h-4 w-4 animate-spin text-foreground/70" />
              ) : isDark ? (
                <Moon className="h-4 w-4 text-foreground/70" />
              ) : (
                <Sun className="h-4 w-4 text-foreground/70" />
              )}
              <span className="sr-only">
                {isDark ? 'Mudar para tema claro' : 'Mudar para tema escuro'}
              </span>
            </button>
          </TooltipTrigger>
          <TooltipContent side="bottom">
            <p>Alternar tema</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  // Versão completa apenas com o interruptor
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <button
            onClick={() => toggleTheme()}
            className={cn(
              'rounded-full w-9 h-9 p-2 transition-all',
              'bg-background hover:bg-muted/80',
              'border border-border shadow-sm',
              'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring',
              className
            )}
            disabled={isTransitioning}
            aria-label={isDark ? 'Mudar para tema claro' : 'Mudar para tema escuro'}
          >
            {isTransitioning ? (
              <Loader2 className="h-full w-full animate-spin text-foreground/70" />
            ) : isDark ? (
              <Moon className="h-full w-full text-foreground/70" />
            ) : (
              <Sun className="h-full w-full text-foreground/70" />
            )}
            <span className="sr-only">
              {isDark ? 'Mudar para tema claro' : 'Mudar para tema escuro'}
            </span>
          </button>
        </TooltipTrigger>
        <TooltipContent>
          <p>Alternar entre tema claro e escuro</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
