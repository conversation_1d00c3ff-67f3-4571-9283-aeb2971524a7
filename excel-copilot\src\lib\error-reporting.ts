'use client';

/**
 * Sistema de rastreamento de erros para Excel Copilot
 *
 * Centraliza o registro e análise de erros do aplicativo, facilitando diagnóstico
 * remoto e resolução de problemas no modo Desktop.
 */

// Tipos de erros
export enum ErrorScope {
  DESKTOP_BRIDGE = 'desktop-bridge',
  WEB_EXCEL = 'web-excel',
  AI_INTEGRATION = 'ai-integration',
  AUTHENTICATION = 'auth',
  UI = 'ui',
  STORAGE = 'storage',
  NETWORK = 'network',
  UNKNOWN = 'unknown',
}

// Interface para erros estruturados
export interface StructuredError {
  id: string;
  scope: ErrorScope;
  message: string;
  timestamp: number;
  code: string | undefined;
  context: Record<string, unknown> | undefined;
  userInfo?: {
    platform: string;
    browser: string;
    userId?: string | undefined;
  };
  stack: string | undefined;
}

// Histórico em memória de erros
const ERROR_HISTORY_SIZE = 50;
const errorHistory: StructuredError[] = [];

// Callbacks de notificação
const errorListeners: Array<(error: StructuredError) => void> = [];

/**
 * Registrar erro estruturado
 */
export function reportError(
  scope: ErrorScope,
  message: string,
  options?: {
    code?: string;
    context?: Record<string, unknown>;
    error?: Error;
    userId?: string;
  }
): StructuredError {
  // Detectar plataforma
  const userAgent = typeof navigator !== 'undefined' ? navigator.userAgent : 'unknown';

  const browserInfo = detectBrowser(userAgent);

  // Criar erro estruturado
  const structuredError: StructuredError = {
    id: generateErrorId(),
    scope,
    message,
    timestamp: Date.now(),
    code: options?.code,
    context: options?.context,
    userInfo: {
      platform: detectPlatform(userAgent),
      browser: browserInfo,
      userId: options?.userId,
    },
    stack: options?.error?.stack,
  };

  // Armazenar no histórico (limitado)
  errorHistory.unshift(structuredError);
  if (errorHistory.length > ERROR_HISTORY_SIZE) {
    errorHistory.pop();
  }

  // Salvar no localStorage para persistência
  try {
    const savedErrors = JSON.parse(localStorage.getItem('excel_copilot_errors') || '[]');
    savedErrors.unshift(structuredError);

    // Limitar a 100 erros persistidos
    if (savedErrors.length > 100) {
      savedErrors.length = 100;
    }

    localStorage.setItem('excel_copilot_errors', JSON.stringify(savedErrors));
  } catch (e) {
    console.error('Erro ao salvar histórico de erros:', e);
  }

  // Notificar listeners
  errorListeners.forEach(listener => {
    try {
      listener(structuredError);
    } catch (e) {
      console.error('Erro em listener de erros:', e);
    }
  });

  // Enviar para o console para debugging
  console.error(`[${scope}] ${message}`, options?.context || '', options?.error || '');

  return structuredError;
}

/**
 * Obter histórico de erros
 */
export function getErrorHistory(): StructuredError[] {
  return [...errorHistory];
}

/**
 * Adicionar listener para novos erros
 */
export function addErrorListener(callback: (error: StructuredError) => void): () => void {
  errorListeners.push(callback);

  // Retornar função para remover listener
  return () => {
    const index = errorListeners.indexOf(callback);
    if (index !== -1) {
      errorListeners.splice(index, 1);
    }
  };
}

/**
 * Limpar histórico de erros
 */
export function clearErrorHistory(): void {
  errorHistory.length = 0;
  localStorage.removeItem('excel_copilot_errors');
}

// Funções auxiliares

/**
 * Gerar ID único para erro
 */
function generateErrorId(): string {
  return `err_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
}

/**
 * Detectar plataforma do usuário
 */
function detectPlatform(userAgent: string): string {
  if (/windows/i.test(userAgent)) return 'Windows';
  if (/macintosh|mac os x/i.test(userAgent)) return 'macOS';
  if (/linux/i.test(userAgent)) return 'Linux';
  if (/android/i.test(userAgent)) return 'Android';
  if (/iphone|ipad|ipod/i.test(userAgent)) return 'iOS';
  return 'Desconhecido';
}

/**
 * Detectar navegador do usuário
 */
function detectBrowser(userAgent: string): string {
  if (/edge|edg/i.test(userAgent)) return 'Edge';
  if (/chrome/i.test(userAgent)) return 'Chrome';
  if (/firefox/i.test(userAgent)) return 'Firefox';
  if (/safari/i.test(userAgent)) return 'Safari';
  if (/msie|trident/i.test(userAgent)) return 'Internet Explorer';
  return 'Desconhecido';
}
