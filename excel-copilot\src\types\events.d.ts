/**
 * Tipos personalizados para eventos de formulário
 */

import type {
  ChangeEvent,
  FormEvent,
  FocusEvent as ReactFocusEvent,
  KeyboardEvent as ReactKeyboardEvent,
  HTMLAttributes,
  InputHTMLAttributes as ReactInputHTMLAttributes,
  DetailedHTMLProps,
} from 'react';

// Tipagem básica para eventos de formulário
export interface FormInputEvent extends ChangeEvent<HTMLInputElement> {
  // Propriedades específicas para formulários Excel Copilot
  excelValidated?: boolean;
}

export interface FormTextareaEvent extends ChangeEvent<HTMLTextAreaElement> {
  // Propriedades específicas para formulários Excel Copilot
  excelValidated?: boolean;
}

export interface FormSelectEvent extends ChangeEvent<HTMLSelectElement> {
  // Propriedades específicas para formulários Excel Copilot
  excelValidated?: boolean;
}

// Evento genérico para input com value
export interface GenericChangeEvent {
  target: {
    value: string;
    name?: string;
    type?: string;
    checked?: boolean;
  };
}

// Tipagem para eventos de formulário com validação
export interface ValidatedFormEvent extends FormEvent<HTMLFormElement> {
  currentTarget: HTMLFormElement & {
    elements: {
      [key: string]: HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement;
    };
  };
}

// Tipagem para eventos de teclas
export interface KeyboardEvent extends ReactKeyboardEvent<HTMLElement> {
  key: string;
  target: EventTarget & {
    value?: string;
  };
}

// Tipagem para eventos de foco
export interface FocusEvent extends ReactFocusEvent<HTMLElement> {
  target: EventTarget & {
    value?: string;
  };
}

// Declaração para estender o React namespace
declare namespace React {
  interface InputHTMLAttributes<T> extends HTMLAttributes<T> {
    // Adicionar propriedades opcionais para inputs
    onValueChange?: (value: string) => void;
  }
}

// Adicionar à declaração global para que o TypeScript entenda em todos os componentes
declare global {
  namespace JSX {
    interface IntrinsicElements {
      // Estender elementos existentes
      input: DetailedHTMLProps<
        ReactInputHTMLAttributes<HTMLInputElement> & {
          onValueChange?: (value: string) => void;
        },
        HTMLInputElement
      >;
    }
  }
}
