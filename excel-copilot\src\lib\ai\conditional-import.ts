/**
 * Este arquivo lida com importação das bibliotecas do Google Vertex AI
 * Usa exclusivamente @google-cloud/vertexai para evitar conflitos de autenticação
 */

import { ENV } from '@/config/unified-environment';

const _isVertexAIDisabled = ENV.FEATURES.USE_MOCK_AI || !ENV.VERTEX_AI.ENABLED;

// Importação para @google-cloud/vertexai
export const getVertexAIModule = async () => {
  try {
    // Importação dinâmica com dynamic import (melhor suporte a ESM)
    if (typeof window === 'undefined') {
      const vertexai = await import('@google-cloud/vertexai');
      return vertexai;
    } else {
      // No cliente, retornamos uma implementação vazia
      return { VertexAI: class EmptyVertexAI {} };
    }
  } catch (error) {
    console.error('Erro ao importar @google-cloud/vertexai:', error);
    throw new Error(
      'Falha ao carregar o módulo Vertex AI. Verifique se a biblioteca está instalada.'
    );
  }
};

// REMOVIDO: getGenAIModule - não usamos mais @google/genai para evitar conflitos
// Todas as funcionalidades de IA agora usam exclusivamente Vertex AI

// Importação para @google-cloud/aiplatform
export const getAIPlatformModule = async () => {
  try {
    // Importação dinâmica com dynamic import
    if (typeof window === 'undefined') {
      const aiplatform = await import('@google-cloud/aiplatform');
      return aiplatform;
    } else {
      // No cliente, retornamos uma implementação vazia
      return { v1: { PredictionServiceClient: class EmptyPredictionServiceClient {} } };
    }
  } catch (error) {
    console.error('Erro ao importar @google-cloud/aiplatform:', error);
    throw new Error(
      'Falha ao carregar o módulo AI Platform. Verifique se a biblioteca está instalada.'
    );
  }
};
