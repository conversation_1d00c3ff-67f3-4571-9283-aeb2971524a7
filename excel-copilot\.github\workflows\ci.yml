name: Excel Copilot CI

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  lint-test:
    name: Lint e testes unitários
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Instalar dependências
        run: npm ci

      - name: Executar ESLint
        run: npm run lint

      - name: Executar testes unitários
        run: npm test

      - name: Gerar relatório de cobertura
        run: npm run test:coverage

      - name: Upload relatório de cobertura
        uses: actions/upload-artifact@v4
        with:
          name: coverage-report
          path: coverage/

  integration-tests:
    name: Testes de integração
    runs-on: ubuntu-latest
    needs: lint-test

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Instalar dependências
        run: npm ci

      - name: Executar testes de integração
        run: npm run test:integration

  e2e-tests:
    name: Testes E2E
    runs-on: ubuntu-latest
    needs: integration-tests

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Instalar dependências
        run: npm ci

      - name: Instalar Playwright
        run: npx playwright install --with-deps

      - name: Criar arquivo Excel de teste
        run: |
          mkdir -p __tests__/mocks
          node -e "
            const ExcelJS = require('exceljs');
            const path = require('path');
            
            async function createTestFile() {
              const workbook = new ExcelJS.Workbook();
              const sheet = workbook.addWorksheet('Dados');
              
              // Adicionar cabeçalhos
              sheet.columns = [
                { header: 'ID', key: 'id' },
                { header: 'Produto', key: 'produto' },
                { header: 'Vendas', key: 'vendas' },
                { header: 'Data', key: 'data' }
              ];
              
              // Adicionar linhas de dados
              sheet.addRow({ id: 1, produto: 'Produto A', vendas: 1500, data: '2023-01-15' });
              sheet.addRow({ id: 2, produto: 'Produto B', vendas: 2800, data: '2023-01-20' });
              sheet.addRow({ id: 3, produto: 'Produto C', vendas: 950, data: '2023-01-25' });
              sheet.addRow({ id: 4, produto: 'Produto D', vendas: 3200, data: '2023-01-30' });
              sheet.addRow({ id: 5, produto: 'Produto E', vendas: 1100, data: '2023-02-05' });
              
              // Salvar arquivo
              await workbook.xlsx.writeFile(path.join('__tests__/mocks', 'sample-data.xlsx'));
              console.log('Arquivo Excel de teste criado com sucesso');
            }
            
            createTestFile().catch(err => {
              console.error('Erro ao criar arquivo de teste:', err);
              process.exit(1);
            });
          "

      - name: Iniciar servidor de desenvolvimento
        run: npm run dev &
        env:
          DATABASE_URL: 'file:./dev.db'
          NEXTAUTH_SECRET: 'test-secret-for-ci'
          NEXTAUTH_URL: 'http://localhost:3000'

      - name: Aguardar inicialização do servidor
        run: |
          echo "Aguardando inicialização do servidor..."
          sleep 10

      - name: Executar testes E2E
        run: npm run test:e2e

      - name: Upload relatórios de teste E2E
        uses: actions/upload-artifact@v4
        with:
          name: playwright-report
          path: playwright-report/

  visualize-tests:
    name: Visualizar resultados dos testes
    runs-on: ubuntu-latest
    needs: [lint-test, integration-tests, e2e-tests]
    if: always()

    steps:
      - name: Download relatório de cobertura
        uses: actions/download-artifact@v4
        with:
          name: coverage-report
          path: coverage

      - name: Download relatório Playwright
        uses: actions/download-artifact@v4
        with:
          name: playwright-report
          path: playwright-report

      - name: Publicar relatório de cobertura
        uses: coverallsapp/github-action@v2
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          path-to-lcov: ./coverage/lcov.info

      - name: Gerar comentário com resumo
        uses: actions/github-script@v7
        if: github.event_name == 'pull_request'
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const fs = require('fs');
            try {
              const summary = fs.readFileSync('./coverage/coverage-summary.json', 'utf8');
              const coverageData = JSON.parse(summary);
              const totalCoverage = coverageData.total.statements.pct;
              
              const commentBody = `## Resumo dos testes ✅
              
              - **Cobertura total**: ${totalCoverage}%
              - **Testes unitários**: Concluídos
              - **Testes de integração**: Concluídos
              - **Testes E2E**: Concluídos
              
              [Veja o relatório completo de cobertura](${process.env.GITHUB_SERVER_URL}/${process.env.GITHUB_REPOSITORY}/actions/runs/${process.env.GITHUB_RUN_ID})
              `;
              
              github.rest.issues.createComment({
                issue_number: context.issue.number,
                owner: context.repo.owner,
                repo: context.repo.repo,
                body: commentBody
              });
            } catch (error) {
              console.error('Erro ao gerar comentário:', error);
            }
