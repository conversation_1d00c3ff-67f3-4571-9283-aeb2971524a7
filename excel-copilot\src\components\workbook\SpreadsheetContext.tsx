'use client';

import React, { createContext, useContext, useReducer, useCallback, ReactNode } from 'react';

// Tipos para o contexto do SpreadsheetEditor
export interface SpreadsheetData {
  headers: string[];
  rows: any[][];
  charts?: any[];
  name: string;
}

export interface UIState {
  isSaving: boolean;
  showCommandPalette: boolean;
  isMobileView: boolean;
  showMobileChat: boolean;
  aiPanelCollapsed: boolean;
  showAiIndicator: boolean;
  showTutorial: boolean;
  tutorialStep: number;
  isFirstVisit: boolean;
  showSuggestions: boolean;
  inputText: string;
  showKeyboardShortcuts: boolean;
  isFullScreen: boolean;
  showUpgradeModal: boolean;
  showFeedback: boolean;
}

export interface SpreadsheetState {
  data: SpreadsheetData;
  history: SpreadsheetData[];
  historyIndex: number;
  lastModifiedCell: { row: number; col: number } | null;
  ui: UIState;
  readOnly: boolean;
  workbookId: string;
}

// Actions para o reducer
export type SpreadsheetAction =
  | { type: 'SET_DATA'; payload: SpreadsheetData }
  | { type: 'ADD_TO_HISTORY'; payload: SpreadsheetData }
  | { type: 'UNDO' }
  | { type: 'REDO' }
  | { type: 'SET_LAST_MODIFIED_CELL'; payload: { row: number; col: number } | null }
  | { type: 'UPDATE_UI'; payload: Partial<UIState> }
  | { type: 'RESET_STATE'; payload: Partial<SpreadsheetState> };

// Estado inicial
const initialUIState: UIState = {
  isSaving: false,
  showCommandPalette: false,
  isMobileView: false,
  showMobileChat: false,
  aiPanelCollapsed: false,
  showAiIndicator: false,
  showTutorial: false,
  tutorialStep: 0,
  isFirstVisit: false,
  showSuggestions: true,
  inputText: '',
  showKeyboardShortcuts: false,
  isFullScreen: false,
  showUpgradeModal: false,
  showFeedback: false,
};

const initialData: SpreadsheetData = {
  headers: ['A', 'B', 'C'],
  rows: [
    ['', '', ''],
    ['', '', ''],
    ['', '', ''],
  ],
  charts: [],
  name: 'Nova Planilha',
};

// Reducer para gerenciar o estado
function spreadsheetReducer(state: SpreadsheetState, action: SpreadsheetAction): SpreadsheetState {
  switch (action.type) {
    case 'SET_DATA':
      return {
        ...state,
        data: action.payload,
      };

    case 'ADD_TO_HISTORY': {
      // Evitar adicionar ao histórico se os dados são iguais ao último item
      if (state.history.length > 0) {
        const lastItem = state.history[state.history.length - 1];
        if (JSON.stringify(lastItem) === JSON.stringify(action.payload)) {
          return state;
        }
      }

      // Limitar histórico a 20 entradas
      const newHistory = state.history.slice(0, state.historyIndex + 1).slice(-19);
      return {
        ...state,
        history: [...newHistory, JSON.parse(JSON.stringify(action.payload))],
        historyIndex: newHistory.length,
      };
    }

    case 'UNDO':
      if (state.historyIndex > 0) {
        return {
          ...state,
          historyIndex: state.historyIndex - 1,
          data: JSON.parse(JSON.stringify(state.history[state.historyIndex - 1])),
        };
      }
      return state;

    case 'REDO':
      if (state.historyIndex < state.history.length - 1) {
        return {
          ...state,
          historyIndex: state.historyIndex + 1,
          data: JSON.parse(JSON.stringify(state.history[state.historyIndex + 1])),
        };
      }
      return state;

    case 'SET_LAST_MODIFIED_CELL':
      return {
        ...state,
        lastModifiedCell: action.payload,
      };

    case 'UPDATE_UI':
      return {
        ...state,
        ui: {
          ...state.ui,
          ...action.payload,
        },
      };

    case 'RESET_STATE':
      return {
        ...state,
        ...action.payload,
      };

    default:
      return state;
  }
}

// Context
interface SpreadsheetContextType {
  state: SpreadsheetState;
  dispatch: React.Dispatch<SpreadsheetAction>;
  actions: {
    setData: (data: SpreadsheetData) => void;
    addToHistory: (data: SpreadsheetData) => void;
    undo: () => void;
    redo: () => void;
    setLastModifiedCell: (cell: { row: number; col: number } | null) => void;
    updateUI: (updates: Partial<UIState>) => void;
    updateCellValue: (rowIndex: number, colIndex: number, value: string) => void;
    addColumn: () => void;
    addRow: () => void;
    removeRow: (index: number) => void;
    removeColumn: (index: number) => void;
  };
}

const SpreadsheetContext = createContext<SpreadsheetContextType | undefined>(undefined);

// Provider
interface SpreadsheetProviderProps {
  children: ReactNode;
  initialData?: SpreadsheetData;
  workbookId: string;
  readOnly?: boolean;
}

export function SpreadsheetProvider({
  children,
  initialData: providedInitialData,
  workbookId,
  readOnly = false,
}: SpreadsheetProviderProps) {
  const initialState: SpreadsheetState = {
    data: providedInitialData || initialData,
    history: [providedInitialData || initialData],
    historyIndex: 0,
    lastModifiedCell: null,
    ui: initialUIState,
    readOnly,
    workbookId,
  };

  const [state, dispatch] = useReducer(spreadsheetReducer, initialState);

  // Actions memoizadas
  const actions = {
    setData: useCallback((data: SpreadsheetData) => {
      dispatch({ type: 'SET_DATA', payload: data });
    }, []),

    addToHistory: useCallback((data: SpreadsheetData) => {
      dispatch({ type: 'ADD_TO_HISTORY', payload: data });
    }, []),

    undo: useCallback(() => {
      dispatch({ type: 'UNDO' });
    }, []),

    redo: useCallback(() => {
      dispatch({ type: 'REDO' });
    }, []),

    setLastModifiedCell: useCallback((cell: { row: number; col: number } | null) => {
      dispatch({ type: 'SET_LAST_MODIFIED_CELL', payload: cell });
    }, []),

    updateUI: useCallback((updates: Partial<UIState>) => {
      dispatch({ type: 'UPDATE_UI', payload: updates });
    }, []),

    updateCellValue: useCallback((rowIndex: number, colIndex: number, value: string) => {
      if (readOnly || typeof rowIndex !== 'number' || typeof colIndex !== 'number') return;
      if (rowIndex < 0 || colIndex < 0) return;

      // Adicionar ao histórico antes de modificar
      dispatch({ type: 'ADD_TO_HISTORY', payload: state.data });

      // Atualizar dados
      const newData = { ...state.data };
      newData.rows = Array.isArray(state.data.rows) ? [...state.data.rows] : [];

      // Garantir que a linha existe e é um array
      if (!Array.isArray(newData.rows[rowIndex])) {
        newData.rows[rowIndex] = Array(newData.headers.length).fill('');
      } else {
        newData.rows[rowIndex] = [...newData.rows[rowIndex]];
      }

      // Verificar se o índice da coluna é válido
      if (colIndex < newData.headers.length) {
        newData.rows[rowIndex][colIndex] = value;
      }

      dispatch({ type: 'SET_DATA', payload: newData });
      dispatch({ type: 'SET_LAST_MODIFIED_CELL', payload: { row: rowIndex, col: colIndex } });
    }, [readOnly, state.data]),

    addColumn: useCallback(() => {
      if (readOnly) return;

      dispatch({ type: 'ADD_TO_HISTORY', payload: state.data });

      const lastHeader = state.data.headers.length > 0 ? state.data.headers[state.data.headers.length - 1] : null;

      // Gerar novo nome de coluna
      let nextHeader: string;
      if (lastHeader && /^[A-Z]$/.test(lastHeader)) {
        nextHeader = String.fromCharCode((lastHeader.charCodeAt(0) || 64) + 1);
      } else {
        nextHeader = `Coluna ${state.data.headers.length + 1}`;
      }

      const newData = { ...state.data };
      newData.headers = [...state.data.headers, nextHeader];
      newData.rows = Array.isArray(state.data.rows)
        ? state.data.rows.map(row =>
            Array.isArray(row) ? [...row, ''] : Array(newData.headers.length).fill('')
          )
        : [];

      dispatch({ type: 'SET_DATA', payload: newData });
    }, [readOnly, state.data]),

    addRow: useCallback(() => {
      if (readOnly) return;

      dispatch({ type: 'ADD_TO_HISTORY', payload: state.data });

      const newRow = Array(state.data.headers.length).fill('');
      const newData = { ...state.data };
      newData.rows = Array.isArray(state.data.rows) ? [...state.data.rows, newRow] : [newRow];

      dispatch({ type: 'SET_DATA', payload: newData });
    }, [readOnly, state.data]),

    removeRow: useCallback((rowIndex: number) => {
      if (readOnly) return;

      dispatch({ type: 'ADD_TO_HISTORY', payload: state.data });

      const newRows = [...state.data.rows];
      newRows.splice(rowIndex, 1);

      dispatch({ type: 'SET_DATA', payload: { ...state.data, rows: newRows } });
    }, [readOnly, state.data]),

    removeColumn: useCallback((colIndex: number) => {
      if (readOnly) return;

      dispatch({ type: 'ADD_TO_HISTORY', payload: state.data });

      const newHeaders = [...state.data.headers];
      newHeaders.splice(colIndex, 1);

      const newRows = state.data.rows.map(row => {
        const newRow = [...row];
        newRow.splice(colIndex, 1);
        return newRow;
      });

      dispatch({ type: 'SET_DATA', payload: { ...state.data, headers: newHeaders, rows: newRows } });
    }, [readOnly, state.data]),
  };

  const contextValue: SpreadsheetContextType = {
    state,
    dispatch,
    actions,
  };

  return (
    <SpreadsheetContext.Provider value={contextValue}>
      {children}
    </SpreadsheetContext.Provider>
  );
}

// Hook para usar o contexto
export function useSpreadsheetContext() {
  const context = useContext(SpreadsheetContext);
  if (context === undefined) {
    throw new Error('useSpreadsheetContext must be used within a SpreadsheetProvider');
  }
  return context;
}
