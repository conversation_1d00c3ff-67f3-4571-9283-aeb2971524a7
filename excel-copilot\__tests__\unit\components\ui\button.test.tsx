import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { Button } from '@/components/ui/button';

describe('Button Component', () => {
  test('renderiza o botão com texto padrão', () => {
    render(<Button>Clique Aqui</Button>);
    expect(screen.getByRole('button', { name: /Clique Aqui/i })).toBeDefined();
  });

  test('aplica a variante correta', () => {
    const { rerender } = render(<Button variant="default">Botão Padrão</Button>);
    const defaultButton = screen.getByRole('button', { name: /Botão Padrão/i });
    expect(defaultButton).toBeInTheDocument();

    rerender(<Button variant="destructive">Botão Destrutivo</Button>);
    const destructiveButton = screen.getByRole('button', { name: /Botão Destrutivo/i });
    expect(destructiveButton).toBeInTheDocument();
  });

  test('aplica o tamanho correto', () => {
    const { rerender } = render(<Button size="default">Botão Médio</Button>);
    const defaultButton = screen.getByRole('button', { name: /Botão Médio/i });
    expect(defaultButton).toBeInTheDocument();

    rerender(<Button size="sm">Botão Pequeno</Button>);
    const smallButton = screen.getByRole('button', { name: /Botão Pequeno/i });
    expect(smallButton).toBeInTheDocument();
  });

  test('desabilita o botão quando a propriedade disabled é true', () => {
    render(<Button disabled>Botão Desabilitado</Button>);
    const disabledButton = screen.getByRole('button', { name: /Botão Desabilitado/i });
    expect(disabledButton).toBeDisabled();
  });

  test('chama a função onClick quando clicado', () => {
    const handleClick = jest.fn();
    render(<Button onClick={handleClick}>Botão Clicável</Button>);

    const button = screen.getByRole('button', { name: /Botão Clicável/i });
    fireEvent.click(button);

    expect(handleClick).toHaveBeenCalledTimes(1);
  });
});
