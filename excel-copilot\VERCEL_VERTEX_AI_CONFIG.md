# 🚀 Configuração Vertex AI na Vercel

## 🎯 **Problema Resolvido**

O erro `"Neither apiKey nor config.authenticator provided"` foi causado por tentativas de usar Google AI Studio em vez de Vertex AI. Esta configuração resolve o problema definitivamente.

## ⚙️ **Variáveis de Ambiente na Vercel**

### **1. Configurações Obrigatórias**

```bash
# ===== CONTROLE DE IA =====
USE_MOCK_AI=false
VERTEX_AI_ENABLED=true
VERTEX_AI_PROJECT_ID=seu-projeto-gcp
VERTEX_AI_LOCATION=us-central1
VERTEX_AI_MODEL_NAME=gemini-1.5-pro

# ===== AUTENTICAÇÃO =====
NODE_ENV=production
NEXTAUTH_URL=https://excel-copilot-eight.vercel.app
NEXTAUTH_SECRET=sua-chave-secreta-forte

# ===== OAUTH =====
GOOGLE_CLIENT_ID=seu-client-id-google
GOOGLE_CLIENT_SECRET=seu-client-secret-google
GITHUB_CLIENT_ID=seu-client-id-github
GITHUB_CLIENT_SECRET=seu-client-secret-github

# ===== BANCO DE DADOS =====
DATABASE_URL=sua-url-postgresql-supabase
```

### **2. Credenciais do Vertex AI**

**Opção A: Via Variável de Ambiente (Recomendado)**

```bash
VERTEX_AI_CREDENTIALS='{"type":"service_account","project_id":"seu-projeto","private_key_id":"...","private_key":"-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----\n","client_email":"*******","client_id":"...","auth_uri":"https://accounts.google.com/o/oauth2/auth","token_uri":"https://oauth2.googleapis.com/token","auth_provider_x509_cert_url":"https://www.googleapis.com/oauth2/v1/certs"}'
```

**Opção B: Via API Key (Menos Seguro)**

```bash
VERTEX_AI_API_KEY=sua-api-key-vertex-ai
```

### **3. Configurações de Debug (Opcional)**

```bash
# Para debug em produção (remover após resolver problemas)
NEXT_PUBLIC_DEBUG_MODE=false
AUTH_DEBUG=false
VERTEX_AI_DEBUG=false
```

## 🔧 **Passos para Configurar na Vercel**

### **1. Acessar Dashboard**

1. Vá para https://vercel.com/dashboard
2. Selecione o projeto Excel Copilot
3. Clique em "Settings"
4. Vá para "Environment Variables"

### **2. Adicionar Variáveis**

1. Clique em "Add New"
2. Adicione cada variável listada acima
3. Selecione "Production" como ambiente
4. Clique em "Save"

### **3. Redeploy**

1. Vá para "Deployments"
2. Clique nos três pontos da última deployment
3. Selecione "Redeploy"
4. Aguarde o deploy completar

## 🔍 **Verificação**

### **1. Logs da Vercel**

Procure por estas mensagens nos logs:

```
✅ "Cliente Vertex AI inicializado com sucesso"
✅ "GeminiService inicializado com sucesso"
❌ "Erro ao inicializar cliente Vertex AI" (se houver problema)
```

### **2. Teste Manual**

1. Acesse https://excel-copilot-eight.vercel.app
2. Abra o F12 (DevTools)
3. Verifique se não há erros relacionados a IA
4. Teste uma funcionalidade de IA

### **3. Health Check**

```bash
curl https://excel-copilot-eight.vercel.app/api/health
```

## 🚫 **Variáveis que NÃO Devem Existir**

Remova estas variáveis se existirem:

```bash
# ❌ REMOVER - Relacionadas ao Google AI Studio
GOOGLE_AI_API_KEY
GOOGLE_GENERATIVE_AI_API_KEY

# ❌ REMOVER - Configurações incorretas
NODE_ENV=development (em produção)
SKIP_AUTH_PROVIDERS=true (em produção)
```

## 🔒 **Segurança**

### **Permissões Mínimas no GCP**

1. **Criar Service Account**:

   ```bash
   gcloud iam service-accounts create excel-copilot \
     --description="Service account for Excel Copilot" \
     --display-name="Excel Copilot"
   ```

2. **Adicionar Permissões**:

   ```bash
   gcloud projects add-iam-policy-binding SEU_PROJECT_ID \
     --member="serviceAccount:excel-copilot@SEU_PROJECT_ID.iam.gserviceaccount.com" \
     --role="roles/aiplatform.user"
   ```

3. **Gerar Chave**:
   ```bash
   gcloud iam service-accounts keys create vertex-credentials.json \
     --iam-account=excel-copilot@SEU_PROJECT_ID.iam.gserviceaccount.com
   ```

### **Rotação de Credenciais**

1. Gere nova chave no GCP Console
2. Atualize `VERTEX_AI_CREDENTIALS` na Vercel
3. Faça redeploy
4. Delete a chave antiga

## 🐛 **Troubleshooting**

### **Erro: "Project ID not found"**

```bash
# Verificar se está definido
VERTEX_AI_PROJECT_ID=seu-projeto-gcp
```

### **Erro: "Permission denied"**

1. Verificar permissões do service account
2. Confirmar que Vertex AI API está habilitada
3. Verificar quotas do projeto

### **Erro: "Invalid credentials"**

1. Verificar formato JSON das credenciais
2. Confirmar que não há caracteres especiais quebrados
3. Testar credenciais localmente primeiro

### **Modo Mock Ativado Inesperadamente**

```bash
# Verificar estas configurações
USE_MOCK_AI=false
VERTEX_AI_ENABLED=true
VERTEX_AI_PROJECT_ID=valor-correto
```

## 📊 **Monitoramento**

### **Logs Importantes**

- `"Vertex AI inicializado com sucesso"` ✅
- `"Usando modo mock para respostas de IA"` ⚠️
- `"Neither apiKey nor config.authenticator provided"` ❌

### **Métricas no GCP**

1. Acesse Cloud Console
2. Vá para "AI Platform" > "Vertex AI"
3. Monitore uso e quotas
4. Configure alertas de billing

## 🎯 **Resultado Esperado**

Após a configuração correta:

- ✅ Erro "Neither apiKey nor config.authenticator provided" resolvido
- ✅ Aplicação carrega sem erros no console
- ✅ Funcionalidades de IA funcionam corretamente
- ✅ Logs mostram inicialização bem-sucedida do Vertex AI
- ✅ Navegação entre páginas funciona normalmente

## 📞 **Suporte**

Se ainda houver problemas:

1. Execute `npm run verify:vertex-ai` localmente
2. Verifique logs da Vercel
3. Confirme configurações no GCP Console
4. Teste com modo mock primeiro (`USE_MOCK_AI=true`)
