#!/usr/bin/env node

/**
 * Script para corrigir problemas de Edge Runtime no Vercel
 * Aplica todas as correções necessárias para resolver erros 500
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 Iniciando correções para Edge Runtime...\n');

// Lista de arquivos de rotas da API que precisam de runtime nodejs
const apiRoutes = [
  'src/app/api/auth/[...nextauth]/route.ts',
  'src/app/api/csrf/route.ts',
  'src/app/api/trpc/[trpc]/route.ts',
  'src/app/api/workbooks/route.ts',
  'src/app/api/workbooks/shared/route.ts',
  'src/app/api/db-status/route.ts',
  'src/app/api/chat/route.ts',
  'src/app/api/user/api-usage/route.ts',
  'src/app/api/user/subscription/route.ts',
  'src/app/api/webhooks/stripe/route.ts',
  'src/app/api/health/db/route.ts',
  'src/app/api/metrics/route.ts',
  'src/app/api/workbooks/[id]/route.ts',
  'src/app/api/workbooks/[id]/export/route.ts',
  'src/app/api/workbooks/recent/route.ts',
];

// Função para adicionar runtime nodejs a uma rota
function addNodejsRuntime(filePath) {
  const fullPath = path.join(__dirname, filePath);

  if (!fs.existsSync(fullPath)) {
    console.log(`⚠️  Arquivo não encontrado: ${filePath}`);
    return false;
  }

  let content = fs.readFileSync(fullPath, 'utf8');

  // Verificar se já tem runtime configurado
  if (content.includes('export const runtime')) {
    console.log(`✅ ${filePath} - Runtime já configurado`);
    return true;
  }

  // Verificar se tem dynamic configurado
  if (content.includes('export const dynamic')) {
    // Adicionar runtime após dynamic
    content = content.replace(
      /export const dynamic = ['"]force-dynamic['"];/,
      `export const dynamic = 'force-dynamic';\nexport const runtime = 'nodejs';`
    );
  } else {
    // Adicionar ambos no início do arquivo após imports
    const lines = content.split('\n');
    let insertIndex = 0;

    // Encontrar onde inserir (após imports)
    for (let i = 0; i < lines.length; i++) {
      if (lines[i].trim() === '' && i > 0 && !lines[i - 1].startsWith('import')) {
        insertIndex = i;
        break;
      }
    }

    lines.splice(
      insertIndex,
      0,
      '',
      "export const dynamic = 'force-dynamic';",
      "export const runtime = 'nodejs';"
    );
    content = lines.join('\n');
  }

  fs.writeFileSync(fullPath, content);
  console.log(`✅ ${filePath} - Runtime nodejs adicionado`);
  return true;
}

// Aplicar correções nas rotas da API
console.log('📁 Corrigindo rotas da API...');
let successCount = 0;
let totalCount = apiRoutes.length;

apiRoutes.forEach(route => {
  if (addNodejsRuntime(route)) {
    successCount++;
  }
});

console.log(`\n📊 Resultado: ${successCount}/${totalCount} rotas corrigidas\n`);

// Verificar se o schema do Prisma está correto
console.log('🗄️  Verificando schema do Prisma...');
const schemaPath = path.join(__dirname, 'prisma/schema.prisma');
if (fs.existsSync(schemaPath)) {
  const schemaContent = fs.readFileSync(schemaPath, 'utf8');

  if (schemaContent.includes('driverAdapters') && schemaContent.includes('engineType')) {
    console.log('✅ Schema do Prisma configurado para Edge Runtime');
  } else {
    console.log('⚠️  Schema do Prisma pode precisar de ajustes para Edge Runtime');
  }
} else {
  console.log('❌ Schema do Prisma não encontrado');
}

// Verificar variáveis de ambiente
console.log('\n🔧 Verificando variáveis de ambiente...');
const envPath = path.join(__dirname, '.env');
if (fs.existsSync(envPath)) {
  const envContent = fs.readFileSync(envPath, 'utf8');

  const checks = [
    {
      key: 'NODE_ENV',
      expected: 'production',
      current: envContent.match(/NODE_ENV="([^"]+)"/)?.[1],
    },
    {
      key: 'NEXTAUTH_URL',
      expected: 'https://excel-copilot-eight.vercel.app',
      current: envContent.match(/NEXTAUTH_URL="([^"]+)"/)?.[1],
    },
    {
      key: 'SKIP_AUTH_PROVIDERS',
      expected: 'false',
      current: envContent.match(/SKIP_AUTH_PROVIDERS="([^"]+)"/)?.[1],
    },
  ];

  checks.forEach(check => {
    if (check.current === check.expected) {
      console.log(`✅ ${check.key}="${check.current}"`);
    } else {
      console.log(`⚠️  ${check.key}="${check.current}" (esperado: "${check.expected}")`);
    }
  });
} else {
  console.log('❌ Arquivo .env não encontrado');
}

console.log('\n🚀 Correções aplicadas! Próximos passos:');
console.log('1. Fazer commit das alterações');
console.log('2. Fazer push para o repositório');
console.log('3. Aguardar o deploy automático na Vercel');
console.log('4. Testar o site em produção');

console.log('\n📋 Comandos para executar:');
console.log('git add .');
console.log('git commit -m "fix: Corrigir Edge Runtime e Prisma para produção"');
console.log('git push');

console.log('\n✨ Correções concluídas!');
