<!doctype html>
<html lang="pt-BR">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Excel Copilot - Modo Offline</title>
    <style>
      :root {
        --primary-color: #0059b3;
        --secondary-color: #0068cc;
        --text-color: #333;
        --bg-color: #f6f9fc;
        --error-color: #d13438;
        --success-color: #107c10;
        --warning-color: #ff8c00;
      }

      @media (prefers-color-scheme: dark) {
        :root {
          --primary-color: #3a96ff;
          --secondary-color: #5facff;
          --text-color: #f0f0f0;
          --bg-color: #1a1a1a;
          --error-color: #ff4343;
          --success-color: #4cce4a;
          --warning-color: #ffaa44;
        }
      }

      body {
        font-family:
          'Segoe UI',
          -apple-system,
          BlinkMacSystemFont,
          Roboto,
          Oxygen,
          <PERSON>buntu,
          sans-serif;
        margin: 0;
        padding: 0;
        background-color: var(--bg-color);
        color: var(--text-color);
        display: flex;
        flex-direction: column;
        min-height: 100vh;
        line-height: 1.6;
      }

      .container {
        max-width: 800px;
        margin: 0 auto;
        padding: 2rem;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        flex: 1;
        text-align: center;
      }

      header {
        width: 100%;
        background-color: var(--primary-color);
        color: white;
        padding: 1rem 0;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      header .logo-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 0 2rem;
        display: flex;
        align-items: center;
      }

      header img {
        height: 40px;
        margin-right: 1rem;
      }

      header h1 {
        margin: 0;
        font-size: 1.5rem;
        font-weight: 500;
      }

      h2 {
        color: var(--primary-color);
        margin-bottom: 1.5rem;
      }

      p {
        margin-bottom: 1.5rem;
        font-size: 1.1rem;
      }

      .offline-icon {
        font-size: 5rem;
        margin-bottom: 1rem;
        color: var(--warning-color);
      }

      .card {
        background-color: white;
        border-radius: 0.5rem;
        padding: 2rem;
        margin-bottom: 2rem;
        width: 100%;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
      }

      @media (prefers-color-scheme: dark) {
        .card {
          background-color: #2a2a2a;
        }
      }

      .button {
        display: inline-block;
        background-color: var(--primary-color);
        color: white;
        padding: 0.75rem 1.5rem;
        border-radius: 0.25rem;
        text-decoration: none;
        font-weight: 500;
        transition: background-color 0.2s;
        border: none;
        cursor: pointer;
        font-size: 1rem;
      }

      .button:hover {
        background-color: var(--secondary-color);
      }

      .button-secondary {
        background-color: transparent;
        color: var(--primary-color);
        border: 1px solid var(--primary-color);
        margin-left: 1rem;
      }

      .button-secondary:hover {
        background-color: rgba(0, 89, 179, 0.1);
        color: var(--primary-color);
      }

      .features {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
        margin-top: 2rem;
        width: 100%;
      }

      .feature {
        background-color: white;
        padding: 1.5rem;
        border-radius: 0.5rem;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
      }

      @media (prefers-color-scheme: dark) {
        .feature {
          background-color: #2a2a2a;
        }
      }

      .feature-icon {
        font-size: 2rem;
        margin-bottom: 1rem;
        color: var(--primary-color);
      }

      .feature h3 {
        margin-top: 0;
        color: var(--primary-color);
      }

      footer {
        padding: 1.5rem;
        text-align: center;
        font-size: 0.9rem;
        color: #666;
        background-color: white;
        border-top: 1px solid #eee;
      }

      @media (prefers-color-scheme: dark) {
        footer {
          background-color: #2a2a2a;
          border-top: 1px solid #444;
          color: #aaa;
        }
      }

      #connection-status {
        background-color: var(--warning-color);
        color: white;
        text-align: center;
        padding: 0.5rem;
        font-weight: 500;
        transition: background-color 0.3s;
      }

      #connection-status.online {
        background-color: var(--success-color);
      }
    </style>
  </head>
  <body>
    <div id="connection-status">
      Você está offline. Algumas funcionalidades podem estar limitadas.
    </div>

    <header>
      <div class="logo-container">
        <h1>Excel Copilot</h1>
      </div>
    </header>

    <div class="container">
      <div class="offline-icon">📶</div>
      <h2>Você está sem conexão com a internet</h2>
      <div class="card">
        <p>
          Não foi possível estabelecer conexão com o servidor neste momento. O Excel Copilot
          continua disponível no modo offline com funcionalidades limitadas.
        </p>
        <p>
          Você pode acessar planilhas que foram previamente carregadas e continuar trabalhando com
          elas. Suas alterações serão sincronizadas automaticamente quando a conexão for restaurada.
        </p>

        <button id="retry-button" class="button">Tentar novamente</button>
        <a href="/" class="button button-secondary">Ir para planilhas offline</a>
      </div>

      <div class="features">
        <div class="feature">
          <div class="feature-icon">💼</div>
          <h3>Acesso offline</h3>
          <p>Acesse suas planilhas recentes mesmo sem internet.</p>
        </div>
        <div class="feature">
          <div class="feature-icon">✏️</div>
          <h3>Edição completa</h3>
          <p>Continue editando com todas as funcionalidades básicas.</p>
        </div>
        <div class="feature">
          <div class="feature-icon">🔄</div>
          <h3>Sincronização automática</h3>
          <p>Suas alterações serão sincronizadas quando voltar online.</p>
        </div>
      </div>
    </div>

    <footer>
      <p>&copy; 2023 Excel Copilot. Todos os direitos reservados.</p>
    </footer>

    <script>
      // Verificar conexão e atualizar status
      function updateConnectionStatus() {
        const statusEl = document.getElementById('connection-status');

        if (navigator.onLine) {
          statusEl.textContent = 'Conexão restaurada! Redirecionando...';
          statusEl.classList.add('online');

          // Tentar redirecionar após um pequeno delay
          setTimeout(() => {
            window.location.href = '/';
          }, 2000);
        } else {
          statusEl.textContent =
            'Você está offline. Algumas funcionalidades podem estar limitadas.';
          statusEl.classList.remove('online');
        }
      }

      // Monitorar eventos de conexão
      window.addEventListener('online', updateConnectionStatus);
      window.addEventListener('offline', updateConnectionStatus);

      // Verificar status inicial
      updateConnectionStatus();

      // Configurar botão de retry
      document.getElementById('retry-button').addEventListener('click', () => {
        window.location.reload();
      });
    </script>
  </body>
</html>
