'use client';

import { AnimationWrapper, AnimationType } from '../ui/animation-wrapper';

/**
 * Exemplo que demonstra o uso do componente AnimationWrapper
 * com diferentes tipos de animações, durações e atrasos
 */
export function AnimationWrapperExample() {
  // Lista de todas as animações disponíveis
  const animationTypes: AnimationType[] = [
    'fade-in',
    'fade-in-up',
    'fade-in-down',
    'fade-in-left',
    'fade-in-right',
    'scale-in',
    'scale-in-up',
    'bounce-in',
    'spin-in',
  ];

  return (
    <div className="space-y-8 py-8">
      <h2 className="text-2xl font-bold">Exemplos de AnimationWrapper</h2>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {animationTypes.map(animation => (
          <AnimationWrapper
            key={animation}
            animation={animation}
            className="p-6 bg-card rounded-lg shadow border"
          >
            <h3 className="font-medium mb-2">{animation}</h3>
            <p className="text-sm text-muted-foreground">
              Esta caixa usa a animação <code>{animation}</code>
            </p>
          </AnimationWrapper>
        ))}
      </div>

      <h3 className="text-xl font-medium mt-8">Exemplos com diferentes durações</h3>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <AnimationWrapper
          animation="fade-in-up"
          duration="fast"
          className="p-6 bg-card rounded-lg shadow border"
        >
          <h3 className="font-medium mb-2">Duração: Rápida</h3>
          <p className="text-sm text-muted-foreground">
            Com <code>duration=&quot;fast&quot;</code>
          </p>
        </AnimationWrapper>

        <AnimationWrapper
          animation="fade-in-up"
          duration="normal"
          className="p-6 bg-card rounded-lg shadow border"
        >
          <h3 className="font-medium mb-2">Duração: Normal</h3>
          <p className="text-sm text-muted-foreground">
            Com <code>duration=&quot;normal&quot;</code>
          </p>
        </AnimationWrapper>

        <AnimationWrapper
          animation="fade-in-up"
          duration="slow"
          className="p-6 bg-card rounded-lg shadow border"
        >
          <h3 className="font-medium mb-2">Duração: Lenta</h3>
          <p className="text-sm text-muted-foreground">
            Com <code>duration=&quot;slow&quot;</code>
          </p>
        </AnimationWrapper>
      </div>

      <h3 className="text-xl font-medium mt-8">Exemplos com diferentes atrasos</h3>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <AnimationWrapper
          animation="bounce-in"
          delay="none"
          className="p-6 bg-card rounded-lg shadow border"
        >
          <h3 className="font-medium mb-2">Sem atraso</h3>
          <p className="text-sm text-muted-foreground">
            Com <code>delay=&quot;none&quot;</code>
          </p>
        </AnimationWrapper>

        <AnimationWrapper
          animation="bounce-in"
          delay="medium"
          className="p-6 bg-card rounded-lg shadow border"
        >
          <h3 className="font-medium mb-2">Atraso médio</h3>
          <p className="text-sm text-muted-foreground">
            Com <code>delay=&quot;medium&quot;</code>
          </p>
        </AnimationWrapper>

        <AnimationWrapper
          animation="bounce-in"
          delay="long"
          className="p-6 bg-card rounded-lg shadow border"
        >
          <h3 className="font-medium mb-2">Atraso longo</h3>
          <p className="text-sm text-muted-foreground">
            Com <code>delay=&quot;long&quot;</code>
          </p>
        </AnimationWrapper>
      </div>
    </div>
  );
}
