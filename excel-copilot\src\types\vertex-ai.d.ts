/**
 * Tipos para a API do Google Vertex AI
 */

// Tipos genéricos do Vertex AI
export interface VertexAIConfig {
  projectId: string;
  location: string;
  apiEndpoint?: string;
}

// Modelo de configuração
export interface VertexAIModelConfig extends VertexAIConfig {
  model: string;
  temperature?: number;
  maxOutputTokens?: number;
  topK?: number;
  topP?: number;
}

// Parte de conteúdo
export interface ContentPart {
  text: string;
}

// Função de partes para um modelo Gemini
export interface GeminiPart {
  text?: string;
  inlineData?: {
    mimeType: string;
    data: string;
  };
}

// Estrutura de uma mensagem
export interface VertexMessage {
  role: 'user' | 'assistant' | 'system';
  content: string | ContentPart[];
}

// Resposta do modelo Vertex AI
export interface VertexGenerationResponse {
  candidates: {
    content: {
      parts: {
        text: string;
      }[];
    };
    finishReason: string;
  }[];
  usageMetadata: {
    promptTokenCount: number;
    candidatesTokenCount: number;
    totalTokenCount: number;
  };
}

// Stream de resposta
export interface VertexStreamResponse {
  stream: AsyncGenerator<
    {
      candidates: {
        content: {
          parts: {
            text: string;
          }[];
        };
      }[];
    },
    void,
    unknown
  >;
}

// Parâmetros de geração
export interface GenerationParams {
  messages: VertexMessage[];
  temperature?: number;
  maxOutputTokens?: number;
  topK?: number;
  topP?: number;
}

// Função de geração de conteúdo
export interface GenerateContentFunction {
  (params: GenerationParams): Promise<VertexGenerationResponse>;
}

// Função de geração de conteúdo por stream
export interface GenerateContentStreamFunction {
  (params: GenerationParams): Promise<VertexStreamResponse>;
}
