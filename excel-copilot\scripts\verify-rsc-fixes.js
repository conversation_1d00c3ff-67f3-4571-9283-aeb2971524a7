#!/usr/bin/env node

/**
 * Script para verificar se as correções de RSC foram aplicadas corretamente
 * Executa verificações no código e configurações
 */

const fs = require('fs');
const path = require('path');

// Cores para output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m',
};

console.log(`${colors.bold}${colors.blue}🔍 Verificando Correções de RSC...${colors.reset}\n`);

let hasErrors = false;
const results = [];

/**
 * Verificar se RSCErrorSuppressor está sendo usado no layout
 */
function checkRSCErrorSuppressor() {
  console.log(`${colors.blue}📋 Verificando RSCErrorSuppressor...${colors.reset}`);

  const layoutPath = path.join(__dirname, '../src/app/layout.tsx');

  if (!fs.existsSync(layoutPath)) {
    results.push(`${colors.red}❌ Layout não encontrado${colors.reset}`);
    hasErrors = true;
    return;
  }

  const content = fs.readFileSync(layoutPath, 'utf8');

  // Verificar import
  if (!content.includes("import { RSCErrorSuppressor } from '@/components/rsc-error-suppressor'")) {
    results.push(`${colors.red}❌ RSCErrorSuppressor não está sendo importado${colors.reset}`);
    hasErrors = true;
  } else {
    results.push(`${colors.green}✅ RSCErrorSuppressor importado corretamente${colors.reset}`);
  }

  // Verificar uso
  if (!content.includes('<RSCErrorSuppressor />')) {
    results.push(`${colors.red}❌ RSCErrorSuppressor não está sendo usado${colors.reset}`);
    hasErrors = true;
  } else {
    results.push(`${colors.green}✅ RSCErrorSuppressor sendo usado no layout${colors.reset}`);
  }
}

/**
 * Verificar configuração NODE_ENV
 */
function checkNodeEnv() {
  console.log(`${colors.blue}📋 Verificando NODE_ENV...${colors.reset}`);

  const envPath = path.join(__dirname, '../.env.local');

  if (!fs.existsSync(envPath)) {
    results.push(`${colors.yellow}⚠️ .env.local não encontrado${colors.reset}`);
    return;
  }

  const content = fs.readFileSync(envPath, 'utf8');

  // Verificar se NODE_ENV não tem aspas duplas extras
  if (content.includes('NODE_ENV="production"')) {
    results.push(`${colors.red}❌ NODE_ENV tem aspas duplas extras${colors.reset}`);
    hasErrors = true;
  } else if (content.includes('NODE_ENV=production')) {
    results.push(`${colors.green}✅ NODE_ENV configurado corretamente${colors.reset}`);
  } else {
    results.push(`${colors.yellow}⚠️ NODE_ENV não encontrado${colors.reset}`);
  }
}

/**
 * Verificar configuração Next.js para RSC
 */
function checkNextConfigRSC() {
  console.log(`${colors.blue}📋 Verificando configuração Next.js para RSC...${colors.reset}`);

  const configPath = path.join(__dirname, '../next.config.js');

  if (!fs.existsSync(configPath)) {
    results.push(`${colors.red}❌ next.config.js não encontrado${colors.reset}`);
    hasErrors = true;
    return;
  }

  const content = fs.readFileSync(configPath, 'utf8');

  // Verificar serverComponentsExternalPackages
  if (!content.includes('serverComponentsExternalPackages')) {
    results.push(`${colors.red}❌ serverComponentsExternalPackages não configurado${colors.reset}`);
    hasErrors = true;
  } else {
    results.push(`${colors.green}✅ serverComponentsExternalPackages configurado${colors.reset}`);
  }

  // Verificar esmExternals
  if (!content.includes('esmExternals')) {
    results.push(`${colors.yellow}⚠️ esmExternals não configurado${colors.reset}`);
  } else {
    results.push(`${colors.green}✅ esmExternals configurado${colors.reset}`);
  }

  // Verificar turbotrace
  if (!content.includes('turbotrace')) {
    results.push(`${colors.yellow}⚠️ turbotrace não configurado${colors.reset}`);
  } else {
    results.push(`${colors.green}✅ turbotrace configurado${colors.reset}`);
  }
}

/**
 * Verificar se o componente RSCErrorSuppressor existe
 */
function checkRSCErrorSuppressorComponent() {
  console.log(`${colors.blue}📋 Verificando componente RSCErrorSuppressor...${colors.reset}`);

  const componentPath = path.join(__dirname, '../src/components/rsc-error-suppressor.tsx');

  if (!fs.existsSync(componentPath)) {
    results.push(`${colors.red}❌ Componente RSCErrorSuppressor não encontrado${colors.reset}`);
    hasErrors = true;
    return;
  }

  const content = fs.readFileSync(componentPath, 'utf8');

  // Verificar se tem os padrões de erro corretos
  const requiredPatterns = [
    '_rsc=',
    'Failed to load resource: the server responded with a status of 404',
    'Rejeição de Promise não tratada',
    'Erro da aplicação',
  ];

  let missingPatterns = [];
  requiredPatterns.forEach(pattern => {
    if (!content.includes(pattern)) {
      missingPatterns.push(pattern);
    }
  });

  if (missingPatterns.length > 0) {
    results.push(
      `${colors.red}❌ Padrões de erro faltando: ${missingPatterns.join(', ')}${colors.reset}`
    );
    hasErrors = true;
  } else {
    results.push(`${colors.green}✅ Componente RSCErrorSuppressor completo${colors.reset}`);
  }
}

/**
 * Executar todas as verificações
 */
function runAllChecks() {
  checkRSCErrorSuppressor();
  checkNodeEnv();
  checkNextConfigRSC();
  checkRSCErrorSuppressorComponent();

  // Mostrar resultados
  console.log(`\n${colors.bold}📊 Resultados das Verificações:${colors.reset}\n`);
  results.forEach(result => console.log(`  ${result}`));

  // Status final
  if (hasErrors) {
    console.log(
      `\n${colors.red}${colors.bold}❌ Algumas correções ainda precisam ser aplicadas${colors.reset}`
    );
    process.exit(1);
  } else {
    console.log(
      `\n${colors.green}${colors.bold}✅ Todas as correções de RSC foram aplicadas com sucesso!${colors.reset}`
    );
    console.log(`\n${colors.blue}🚀 Próximos passos:${colors.reset}`);
    console.log(`  1. Execute: ${colors.yellow}npm run dev${colors.reset}`);
    console.log(`  2. Abra o F12 e verifique se os erros RSC diminuíram`);
    console.log(`  3. Faça deploy na Vercel para testar em produção`);
  }
}

// Executar verificações
runAllChecks();
