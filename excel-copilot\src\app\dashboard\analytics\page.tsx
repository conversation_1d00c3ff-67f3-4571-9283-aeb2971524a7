'use client';

import { ThumbsUp, MessageSquare, Sparkles } from 'lucide-react';
import { useEffect, useState } from 'react';
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  Pie,
  Cell,
} from 'recharts';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { feedbackService } from '@/lib/ai/feedback-service';

// Cores para uso futuroconst __COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8', '#82ca9d', '#ffc658'];

export default function FeedbackAnalyticsPage() {
  const [analytics, setAnalytics] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Carregar dados de analytics
    const data = feedbackService.getAnalytics();
    setAnalytics(data);
    setLoading(false);
  }, []);

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin h-10 w-10 border-4 border-primary border-t-transparent rounded-full"></div>
      </div>
    );
  }

  // Preparar dados para os gráficos
  const issuesData = analytics?.commonIssues || [];
  const patternData = analytics?.commandPatterns || [];

  // Dados para o gráfico de pizza de sucesso/falha
  const successRateData = [
    { name: 'Sucesso', value: analytics?.successRate || 0 },
    { name: 'Falha', value: 100 - (analytics?.successRate || 0) },
  ];

  return (
    <div className="container mx-auto p-4 space-y-6">
      <div className="flex flex-col space-y-2">
        <h1 className="text-3xl font-bold">Analytics de Comandos IA</h1>
        <p className="text-muted-foreground">
          Análise de desempenho dos comandos de IA no Excel Copilot
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Total de Comandos</CardTitle>
            <CardDescription>Número total de comandos processados</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2">
              <MessageSquare className="h-5 w-5 text-muted-foreground" />
              <span className="text-2xl font-bold">{analytics?.totalCommands || 0}</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Taxa de Sucesso</CardTitle>
            <CardDescription>Porcentagem de comandos bem-sucedidos</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2">
              <ThumbsUp className="h-5 w-5 text-green-500" />
              <span className="text-2xl font-bold">{analytics?.successRate?.toFixed(1) || 0}%</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Tipos de Comando</CardTitle>
            <CardDescription>Padrões de comando identificados</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2">
              <Sparkles className="h-5 w-5 text-blue-500" />
              <span className="text-2xl font-bold">{patternData.length || 0}</span>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="issues">
        <TabsList className="mb-4">
          <TabsTrigger value="issues">Problemas Comuns</TabsTrigger>
          <TabsTrigger value="patterns">Padrões de Comando</TabsTrigger>
          <TabsTrigger value="success">Taxa de Sucesso</TabsTrigger>
        </TabsList>

        <TabsContent value="issues" className="h-80">
          <Card>
            <CardHeader>
              <CardTitle>Problemas Comuns Reportados</CardTitle>
              <CardDescription>Principais problemas reportados pelos usuários</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={issuesData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="issue" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="count" fill="#8884d8" name="Ocorrências" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="patterns" className="h-80">
          <Card>
            <CardHeader>
              <CardTitle>Padrões de Comando</CardTitle>
              <CardDescription>Tipos de comando mais utilizados e taxa de sucesso</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={patternData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="pattern" />
                  <YAxis yAxisId="left" orientation="left" stroke="#8884d8" />
                  <YAxis yAxisId="right" orientation="right" stroke="#82ca9d" />
                  <Tooltip />
                  <Legend />
                  <Bar yAxisId="left" dataKey="count" fill="#8884d8" name="Frequência" />
                  <Bar
                    yAxisId="right"
                    dataKey="successRate"
                    fill="#82ca9d"
                    name="Taxa de Sucesso (%)"
                  />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="success" className="h-80">
          <Card>
            <CardHeader>
              <CardTitle>Taxa de Sucesso</CardTitle>
              <CardDescription>Proporção de comandos bem-sucedidos vs. falhas</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={successRateData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    outerRadius={100}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(1)}%`}
                  >
                    {successRateData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={index === 0 ? '#4ade80' : '#ef4444'} />
                    ))}
                  </Pie>
                  <Tooltip
                    formatter={value => {
                      // Converter para número antes de usar toFixed
                      const numValue =
                        typeof value === 'string' ? parseFloat(value) : (value as number);
                      return `${numValue.toFixed(1)}%`;
                    }}
                  />
                </PieChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
