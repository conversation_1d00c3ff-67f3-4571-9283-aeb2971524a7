import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { ExportButton } from '@/components/export-button';
import '../../mocks/msw-setup';

// Precisamos simular a API URL.createObjectURL para downloads
global.URL.createObjectURL = jest.fn(() => 'mock-url');

describe('ExportButton Component', () => {
  const workbookId = 'test-workbook-123';
  const workbookName = 'Test Workbook';
  const sheets = [
    {
      name: 'Sheet1',
      data: [
        ['A1', 'B1'],
        ['A2', 'B2'],
      ],
    },
  ];

  // Mock da funcionalidade fetch
  beforeEach(() => {
    global.fetch = jest.fn().mockImplementation(() =>
      Promise.resolve({
        ok: true,
        blob: () =>
          Promise.resolve(
            new Blob(['test data'], {
              type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            })
          ),
      })
    );
  });

  test('renderiza o botão de exportação', () => {
    render(<ExportButton workbookId={workbookId} workbookName={workbookName} sheets={sheets} />);
    expect(screen.getByRole('button')).toBeDefined();
    expect(screen.getByText(/Exportar/)).toBeDefined();
  });

  test('inicia o download quando clicado', async () => {
    render(<ExportButton workbookId={workbookId} workbookName={workbookName} sheets={sheets} />);

    // Simular elementos DOM com mock simplificado
    const mockLink = document.createElement('a');

    // Sobrescrever as funções do elemento para uso de spies
    mockLink.setAttribute = jest.fn();
    mockLink.click = jest.fn();
    mockLink.remove = jest.fn();

    // Modificar o mock para createElement
    const originalCreateElement = document.createElement;
    document.createElement = jest.fn().mockImplementation(tag => {
      if (tag === 'a') return mockLink;
      return originalCreateElement.call(document, tag);
    });

    // Clicar no botão de exportar
    fireEvent.click(screen.getByRole('button'));

    // Verificar se a API foi chamada com o workbookId correto
    expect(global.fetch).toHaveBeenCalledWith(`/api/workbook/export?workbookId=${workbookId}`);

    // Permitir que a promessa resolva
    await new Promise(resolve => setTimeout(resolve, 0));

    // Verificar se o link foi criado e clicado
    expect(document.createElement).toHaveBeenCalledWith('a');
    expect(mockLink.click).toHaveBeenCalled();

    // Restaurar o createElement original
    document.createElement = originalCreateElement;
  });
});
