/**
 * Utilitários consolidados para acesso seguro a dados
 * Centraliza todas as funções de acesso seguro para evitar duplicações
 */

// ===== TIPOS UTILITÁRIOS =====

/**
 * Tipo para valores que podem ser null ou undefined
 */
export type Nullable<T> = T | null | undefined;

/**
 * Tipo para arrays que podem ser null ou undefined
 */
export type NullableArray<T> = Nullable<T[]>;

/**
 * Tipo para objetos que podem ser null ou undefined
 */
export type NullableObject<T> = Nullable<T>;

// ===== FUNÇÕES DE ACESSO SEGURO A ARRAYS =====

/**
 * Acessa um elemento de array de forma segura
 * @param array Array para acessar
 * @param index Índice do elemento
 * @returns Elemento ou undefined se não existir
 */
export function safeArrayGet<T>(array: NullableArray<T>, index: number): T | undefined {
  if (!Array.isArray(array) || index < 0 || index >= array.length) {
    return undefined;
  }
  return array[index];
}

/**
 * Acessa múltiplos elementos de array de forma segura
 * @param array Array para acessar
 * @param indices Índices dos elementos
 * @returns Array com elementos encontrados (pode ter undefined)
 */
export function safeArrayGetMultiple<T>(
  array: NullableArray<T>,
  indices: number[]
): (T | undefined)[] {
  return indices.map(index => safeArrayGet(array, index));
}

/**
 * Retorna o primeiro elemento de um array de forma segura
 * @param array Array para acessar
 * @returns Primeiro elemento ou undefined
 */
export function safeArrayFirst<T>(array: NullableArray<T>): T | undefined {
  return safeArrayGet(array, 0);
}

/**
 * Retorna o último elemento de um array de forma segura
 * @param array Array para acessar
 * @returns Último elemento ou undefined
 */
export function safeArrayLast<T>(array: NullableArray<T>): T | undefined {
  if (!Array.isArray(array) || array.length === 0) {
    return undefined;
  }
  return array[array.length - 1];
}

/**
 * Verifica se um array tem elementos
 * @param array Array para verificar
 * @returns true se o array tem elementos
 */
export function arrayHasElements<T>(array: NullableArray<T>): array is T[] {
  return Array.isArray(array) && array.length > 0;
}

// ===== FUNÇÕES DE ACESSO SEGURO A OBJETOS =====

/**
 * Acessa uma propriedade de objeto de forma segura
 * @param obj Objeto para acessar
 * @param key Chave da propriedade
 * @returns Valor da propriedade ou undefined
 */
export function safeObjectGet<T, K extends keyof T>(
  obj: NullableObject<T>,
  key: K
): T[K] | undefined {
  if (!obj || typeof obj !== 'object') {
    return undefined;
  }
  return obj[key];
}

/**
 * Acessa propriedade aninhada de forma segura
 * @param obj Objeto para acessar
 * @param path Caminho da propriedade (ex: 'user.profile.name')
 * @returns Valor da propriedade ou undefined
 */
export function safeNestedGet(obj: unknown, path: string): unknown {
  if (!obj || typeof obj !== 'object') {
    return undefined;
  }

  const keys = path.split('.');
  let current = obj as Record<string, unknown>;

  for (const key of keys) {
    if (!current || typeof current !== 'object' || !(key in current)) {
      return undefined;
    }
    current = current[key] as Record<string, unknown>;
  }

  return current;
}

/**
 * Verifica se um objeto tem uma propriedade específica
 * @param obj Objeto para verificar
 * @param key Chave da propriedade
 * @returns true se a propriedade existe
 */
export function objectHasProperty<T>(
  obj: NullableObject<T>,
  key: string | number | symbol
): obj is T & Record<typeof key, unknown> {
  return obj != null && typeof obj === 'object' && key in obj;
}

// ===== FUNÇÕES DE REGEX SEGURAS =====

/**
 * Executa match de regex de forma segura
 * @param text Texto para fazer match
 * @param pattern Padrão regex
 * @returns Array de matches ou null
 */
export function safeRegexMatch(text: Nullable<string>, pattern: RegExp): RegExpMatchArray | null {
  if (typeof text !== 'string') {
    return null;
  }

  try {
    return text.match(pattern);
  } catch (error) {
    console.warn('Erro ao executar regex match:', error);
    return null;
  }
}

/**
 * Extrai grupo específico de regex match
 * @param text Texto para fazer match
 * @param pattern Padrão regex
 * @param groupIndex Índice do grupo (padrão: 1)
 * @returns Grupo extraído ou undefined
 */
export function extractGroup(
  text: Nullable<string>,
  pattern: RegExp,
  groupIndex: number = 1
): string | undefined {
  const match = safeRegexMatch(text, pattern);
  return safeArrayGet(match, groupIndex);
}

/**
 * Divide string de forma segura
 * @param text Texto para dividir
 * @param separator Separador
 * @param limit Limite de divisões
 * @returns Array de strings ou array vazio
 */
export function safeSplit(
  text: Nullable<string>,
  separator: string | RegExp,
  limit?: number
): string[] {
  if (typeof text !== 'string') {
    return [];
  }

  try {
    return text.split(separator, limit);
  } catch (error) {
    console.warn('Erro ao dividir string:', error);
    return [];
  }
}

/**
 * Substitui texto de forma segura
 * @param text Texto original
 * @param searchValue Valor a ser substituído
 * @param replaceValue Valor de substituição
 * @returns Texto substituído ou string vazia
 */
export function safeReplace(
  text: Nullable<string>,
  searchValue: string | RegExp,
  replaceValue: string
): string {
  if (typeof text !== 'string') {
    return '';
  }

  try {
    return text.replace(searchValue, replaceValue);
  } catch (error) {
    console.warn('Erro ao substituir texto:', error);
    return text;
  }
}

/**
 * Testa regex de forma segura
 * @param text Texto para testar
 * @param pattern Padrão regex
 * @returns true se o padrão corresponde
 */
export function safeTest(text: Nullable<string>, pattern: RegExp): boolean {
  if (typeof text !== 'string') {
    return false;
  }

  try {
    return pattern.test(text);
  } catch (error) {
    console.warn('Erro ao testar regex:', error);
    return false;
  }
}

// ===== FUNÇÕES DE CONVERSÃO SEGURA =====

/**
 * Converte valor para string de forma segura
 * @param value Valor para converter
 * @param defaultValue Valor padrão se conversão falhar
 * @returns String convertida ou valor padrão
 */
export function toSafeString(value: unknown, defaultValue: string = ''): string {
  if (value === null || value === undefined) {
    return defaultValue;
  }

  try {
    return String(value);
  } catch (error) {
    console.warn('Erro ao converter para string:', error);
    return defaultValue;
  }
}

/**
 * Converte valor para número de forma segura
 * @param value Valor para converter
 * @param defaultValue Valor padrão se conversão falhar
 * @returns Número convertido ou valor padrão
 */
export function toSafeNumber(value: unknown, defaultValue: number = 0): number {
  if (value === null || value === undefined) {
    return defaultValue;
  }

  const num = Number(value);
  return isNaN(num) ? defaultValue : num;
}

/**
 * Converte valor para boolean de forma segura
 * @param value Valor para converter
 * @param defaultValue Valor padrão se conversão falhar
 * @returns Boolean convertido ou valor padrão
 */
export function toSafeBoolean(value: unknown, defaultValue: boolean = false): boolean {
  if (value === null || value === undefined) {
    return defaultValue;
  }

  if (typeof value === 'boolean') {
    return value;
  }

  if (typeof value === 'string') {
    const lower = value.toLowerCase().trim();
    if (lower === 'true' || lower === '1' || lower === 'yes') {
      return true;
    }
    if (lower === 'false' || lower === '0' || lower === 'no') {
      return false;
    }
  }

  if (typeof value === 'number') {
    return value !== 0;
  }

  return defaultValue;
}

// ===== FUNÇÕES DE VALIDAÇÃO =====

/**
 * Verifica se valor é null ou undefined
 * @param value Valor para verificar
 * @returns true se é null ou undefined
 */
export function isNullOrUndefined(value: unknown): value is null | undefined {
  return value === null || value === undefined;
}

/**
 * Verifica se valor é uma string não vazia
 * @param value Valor para verificar
 * @returns true se é string não vazia
 */
export function isNonEmptyString(value: unknown): value is string {
  return typeof value === 'string' && value.trim().length > 0;
}

/**
 * Verifica se valor é um número válido
 * @param value Valor para verificar
 * @returns true se é número válido
 */
export function isValidNumber(value: unknown): value is number {
  return typeof value === 'number' && !isNaN(value) && isFinite(value);
}

/**
 * Retorna valor ou padrão se for null/undefined
 * @param value Valor para verificar
 * @param defaultValue Valor padrão
 * @returns Valor original ou padrão
 */
export function withDefault<T>(value: T | null | undefined, defaultValue: T): T {
  return isNullOrUndefined(value) ? defaultValue : value;
}

// ===== EXPORTS PARA COMPATIBILIDADE =====

/**
 * @deprecated Use safeArrayGet instead
 */
export const safeArrayAccess = safeArrayGet;

/**
 * @deprecated Use safeObjectGet instead
 */
export const safeObjectAccess = safeObjectGet;

/**
 * @deprecated Use safeNestedGet instead
 */
export const safeNestedAccess = safeNestedGet;

/**
 * @deprecated Use toSafeString instead
 */
export const toNullableString = toSafeString;
