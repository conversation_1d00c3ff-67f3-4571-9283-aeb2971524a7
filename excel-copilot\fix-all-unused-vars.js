const { exec } = require('child_process');
const fs = require('fs');
const path = require('path');

// Expressões regulares para encontrar variáveis não utilizadas
const UNUSED_VAR_REGEX =
  /'([a-zA-Z0-9_]+)' is (assigned a value but never used|defined but never used)\./g;
const ALLOWED_UNUSED_REGEX = /Allowed unused args must match \/\^_\/u\./;

// Processa o resultado do ESLint para encontrar arquivos com variáveis não utilizadas
function processLintOutput(output) {
  const fileIssues = {};

  // Extrair informações do output
  const lines = output.split('\n');
  let currentFile = null;

  for (const line of lines) {
    // Verifica se é uma linha de arquivo
    if (line.startsWith('./')) {
      currentFile = line.split(':')[0].substring(2); // Remove './'
    } else if (
      currentFile &&
      line.includes('Warning:') &&
      (line.includes('is defined but never used') ||
        line.includes('is assigned a value but never used'))
    ) {
      // Extrai o número da linha e a variável
      const match = line.match(/(\d+):(\d+)\s+Warning:.*?'([a-zA-Z0-9_]+)'/);
      if (match) {
        const [_, lineNum, colNum, varName] = match;

        if (!fileIssues[currentFile]) {
          fileIssues[currentFile] = [];
        }

        fileIssues[currentFile].push({
          line: parseInt(lineNum),
          column: parseInt(colNum),
          variableName: varName,
        });
      }
    }
  }

  return fileIssues;
}

// Corrige as variáveis não utilizadas adicionando '_' como prefixo
function fixUnusedVariables(fileIssues) {
  for (const [filePath, issues] of Object.entries(fileIssues)) {
    try {
      const fullPath = path.join(process.cwd(), filePath);
      const content = fs.readFileSync(fullPath, 'utf8');
      const lines = content.split('\n');

      // Ordenar problemas por linha em ordem decrescente
      // para evitar que alterações em linhas anteriores afetem índices posteriores
      issues.sort((a, b) => b.line - a.line);

      let modified = false;

      for (const issue of issues) {
        const line = lines[issue.line - 1];

        // Verificar se a variável já tem o prefixo '_'
        if (issue.variableName.startsWith('_')) {
          continue;
        }

        // Tratar com diferentes padrões para capturar mais casos
        let newLine = line;

        // Caso 1: Parâmetros de função ou declarações
        newLine = newLine.replace(
          new RegExp(`\\b${issue.variableName}\\b(?=\\s*[,:)=])`, 'g'),
          `_${issue.variableName}`
        );

        // Caso 2: Destructuring assignment
        newLine = newLine.replace(
          new RegExp(`\\{\\s*([^}]*)\\b${issue.variableName}\\b([^}]*)\\}`, 'g'),
          (match, before, after) => {
            return `{ ${before}_${issue.variableName}${after}}`;
          }
        );

        // Caso 3: Declarações de interface/type com nome de parâmetro
        newLine = newLine.replace(
          new RegExp(`(\\w+):\\s*\\b${issue.variableName}\\b\\s*[,)]`, 'g'),
          `$1: _${issue.variableName} `
        );

        if (newLine !== line) {
          lines[issue.line - 1] = newLine;
          modified = true;
        }
      }

      if (modified) {
        fs.writeFileSync(fullPath, lines.join('\n'), 'utf8');
        console.log(`✓ Corrigido: ${filePath}`);
      }
    } catch (error) {
      console.error(`Erro ao processar ${filePath}:`, error.message);
    }
  }
}

// Executa o lint e processa os resultados
exec('npm run lint', (error, stdout, stderr) => {
  if (error) {
    console.error(`Erro ao executar lint: ${error.message}`);
    return;
  }

  console.log('Processando resultados do lint...');
  const fileIssues = processLintOutput(stdout);

  console.log(`Encontrados problemas em ${Object.keys(fileIssues).length} arquivos.`);
  fixUnusedVariables(fileIssues);

  console.log('Concluído! Execute npm run lint novamente para verificar as correções.');
});
