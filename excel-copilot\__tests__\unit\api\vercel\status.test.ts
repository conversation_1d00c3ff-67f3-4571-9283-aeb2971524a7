/**
 * Testes unitários para o endpoint /api/vercel/status
 * Testa a funcionalidade de status do projeto Vercel
 */

// Mock do VercelMonitoringService
const mockGetProjectStatus = jest.fn();
const mockGetPerformanceMetrics = jest.fn();

jest.mock('@/lib/vercel-integration', () => ({
  VercelMonitoringService: jest.fn().mockImplementation(() => ({
    getProjectStatus: mockGetProjectStatus,
    getPerformanceMetrics: mockGetPerformanceMetrics,
  })),
}));

// Mock do logger
jest.mock('@/lib/logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
  },
}));

// Mock do ApiResponse
jest.mock('@/utils/api-response', () => ({
  ApiResponse: {
    success: jest.fn(data => ({
      json: () => Promise.resolve({ success: true, data }),
    })),
    error: jest.fn((message, code, status) => ({
      json: () =>
        Promise.resolve({
          success: false,
          error: { message, code },
          status,
        }),
    })),
  },
}));

// Importar após os mocks
import { GET } from '@/app/api/vercel/status/route';

describe('/api/vercel/status', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Mock das variáveis de ambiente
    process.env.VERCEL_API_TOKEN = 'test_token';
    process.env.VERCEL_PROJECT_ID = 'prj_test123';
    process.env.VERCEL_TEAM_ID = 'team_test123';
  });

  describe('GET', () => {
    it('deve retornar status do projeto com sucesso', async () => {
      // Arrange
      const mockProjectStatus = {
        status: 'healthy' as const,
        lastDeployment: {
          uid: 'dpl_test456',
          name: 'excel-copilot',
          url: 'https://excel-copilot.vercel.app',
          state: 'READY' as const,
          type: 'LAMBDAS' as const,
          created: 1640995200000,
          ready: 1640995300000,
          projectId: 'prj_test123',
          target: 'production' as const,
        },
        recentErrors: 0,
        uptime: 86400000, // 24 horas
        message: 'Sistema funcionando normalmente',
      };

      const mockPerformanceMetrics = {
        requests: 1250,
        errors: 5,
        errorRate: 0.4,
        averageResponseTime: 120,
        bandwidth: 1024000,
        cacheHitRate: 85.5,
      };

      mockGetProjectStatus.mockResolvedValue(mockProjectStatus);
      mockGetPerformanceMetrics.mockResolvedValue(mockPerformanceMetrics);

      // Mock simples de request
      const request = {} as any;

      // Act
      const response = await GET(request);
      const result = await response.json();

      // Assert
      expect(result.success).toBe(true);
      expect(result.data).toHaveProperty('project');
      expect(result.data).toHaveProperty('metrics');
      expect(result.data.project.name).toBe('excel-copilot');
      expect(result.data.project.status).toBe('healthy');
      expect(result.data.metrics.requests24h).toBe(1250);
      expect(result.data.metrics.errorRate).toBe(0.4);
    });

    it('deve retornar erro quando serviço falhar', async () => {
      // Arrange
      mockGetProjectStatus.mockRejectedValue(new Error('API Error'));

      const request = {} as any;

      // Act
      const response = await GET(request);
      const result = await response.json();

      // Assert
      expect(result.success).toBe(false);
      expect(result.error.code).toBe('VERCEL_API_ERROR');
      expect(result.error.message).toContain('Erro ao conectar com Vercel');
    });

    it('deve retornar erro quando token não estiver configurado', async () => {
      // Arrange
      delete process.env.VERCEL_API_TOKEN;

      const request = {} as any;

      // Act
      const response = await GET(request);
      const result = await response.json();

      // Assert
      expect(result.success).toBe(false);
      expect(result.error.code).toBe('VERCEL_CONFIG_ERROR');
      expect(result.error.message).toContain('VERCEL_API_TOKEN não configurado');
    });

    it('deve identificar status unhealthy quando há muitos erros', async () => {
      // Arrange
      const mockProjectStatus = {
        status: 'degraded' as const,
        lastDeployment: null,
        recentErrors: 5,
        uptime: 0,
        message: '5 erros nas últimas 24h',
      };

      const mockPerformanceMetrics = {
        requests: 1000,
        errors: 100,
        errorRate: 10.0,
        averageResponseTime: 5000,
        bandwidth: 1024000,
        cacheHitRate: 45.0,
      };

      mockGetProjectStatus.mockResolvedValue(mockProjectStatus);
      mockGetPerformanceMetrics.mockResolvedValue(mockPerformanceMetrics);

      const request = {} as any;

      // Act
      const response = await GET(request);
      const result = await response.json();

      // Assert
      expect(result.data.project.status).toBe('degraded');
      expect(result.data.metrics.errorRate).toBe(10.0);
    });
  });
});
