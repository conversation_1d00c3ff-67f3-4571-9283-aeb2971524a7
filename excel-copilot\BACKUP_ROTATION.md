# Estratégia de Backup e Rotação - Excel Copilot

Este documento descreve a estratégia de backup e rotação implementada para o Excel Copilot, garantindo a integridade e disponibilidade dos dados.

## Estratégia de Backup

O Excel Copilot implementa uma estratégia multi-nível de backup para proteger contra diferentes tipos de falhas:

### 1. Backups Automáticos

Os backups são executados automaticamente com as seguintes frequências:

- **Diário**: Executado todos os dias às 3:00 da manhã, com retenção de 7 dias
- **Semanal**: Executado aos domingos às 2:00 da manhã, com retenção de 30 dias
- **Mensal**: <PERSON><PERSON><PERSON> pelo menos um backup por mês por até 1 ano (através da rotação)

### 2. Política de Retenção

A política implementada segue as melhores práticas:

- Backups recentes (até 7 dias): preservação completa
- Backups intermediários (8-30 dias): um por dia
- Backups antigos (31+ dias): um por semana
- Backups muito antigos (90+ dias): um por mês

### 3. Formato de Backup

Dependendo do tipo de banco de dados, são utilizados diferentes formatos:

- **MySQL/PlanetScale**: Arquivo SQL (.sql)
- **SQLite**: Cópia completa do arquivo (.db)
- **Fallback**: Exportação JSON de todas as tabelas principais

## Comandos Disponíveis

| Comando                          | Descrição                                                             |
| -------------------------------- | --------------------------------------------------------------------- |
| `npm run db:backup`              | Backup manual padrão com retenção de 30 dias                          |
| `npm run db:backup:daily`        | Backup otimizado para uso diário, retenção de 7 dias                  |
| `npm run db:backup:weekly`       | Backup semanal, armazenado em diretório separado, retenção de 30 dias |
| `npm run db:backup:clean`        | Limpeza padrão (mantém 50 backups, 30 dias)                           |
| `npm run db:backup:clean:strict` | Limpeza mais rigorosa (mantém 20 backups, 15 dias)                    |

## Configuração de Agendamento

### Linux/macOS (cron)

Adicione o seguinte ao crontab (`crontab -e`):

```bash
# Backup diário às 3:00 AM
0 3 * * * cd /caminho/para/excel-copilot && npm run db:backup:daily >> logs/backup-daily.log 2>&1

# Backup semanal aos domingos às 2:00 AM
0 2 * * 0 cd /caminho/para/excel-copilot && npm run db:backup:weekly >> logs/backup-weekly.log 2>&1

# Limpeza de backups aos sábados às 4:00 AM
0 4 * * 6 cd /caminho/para/excel-copilot && npm run db:backup:clean >> logs/backup-clean.log 2>&1
```

### Windows (Agendador de Tarefas)

1. Abra o Agendador de Tarefas do Windows
2. Crie novas tarefas para cada comando de backup
3. Configure o gatilho apropriado (diário, semanal)
4. Em "Ação", configure:
   - Programa: `cmd.exe`
   - Argumentos: `/c cd /d C:\caminho\para\excel-copilot && npm run db:backup:daily >> logs\backup-daily.log 2>&1`

## Armazenamento Externo

Para máxima proteção, backups semanais e mensais devem ser armazenados externamente:

### Opções recomendadas:

1. **Armazenamento em nuvem**: Use AWS S3, Google Cloud Storage ou Azure Blob Storage

   ```bash
   # Exemplo para AWS S3 (adicione ao crontab)
   30 4 * * 0 aws s3 sync /caminho/para/excel-copilot/backups/weekly s3://meu-bucket/excel-copilot/backups/
   ```

2. **Backup para outro servidor**: Use rsync ou scp

   ```bash
   # Exemplo com rsync (adicione ao crontab)
   30 4 * * 0 rsync -avz /caminho/para/excel-copilot/backups/weekly usuario@servidor-remoto:/backup/excel-copilot/
   ```

## Verificação de Integridade

Recomendamos verificar periodicamente a integridade dos backups:

```bash
# Verificar se o backup mais recente é válido
npm run db:backup:verify
```

## Plano de Recuperação de Desastres

1. **Falha de servidor**: Restaure o backup mais recente no novo servidor
2. **Corrupção de dados**: Restaure a partir do último backup válido
3. **Exclusão acidental**: Recupere dos backups externos

## Monitoramento

O sistema está configurado para enviar alertas nos seguintes casos:

- Falha ao criar backup
- Espaço em disco abaixo de 10% no servidor de backup
- Backup não criado por mais de 36 horas

## Considerações de Segurança

- Os backups são protegidos com as mesmas políticas de acesso que o banco de dados de produção
- Dados sensíveis nos backups estão sujeitos às mesmas políticas de proteção do ambiente de produção
- Os backups externos são criptografados em trânsito e em repouso

---

Última atualização: 2024-07-25
