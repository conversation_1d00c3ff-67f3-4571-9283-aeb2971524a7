#!/usr/bin/env node

/**
 * Script para limpar todas as variáveis de ambiente do projeto Vercel
 * e configurar as novas variáveis do .env.production
 */

const https = require('https');
const fs = require('fs');
const path = require('path');

// Configurações do Vercel
const VERCEL_TOKEN = '************************';
const PROJECT_ID = 'prj_IQemY1bXbDdiiQmHDfRAYLUqMZIg';
const TEAM_ID = 'team_BLCIn3CF09teqBeBn8u0fLqp';

// Função para fazer requisições HTTP
function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = https.request(options, res => {
      let body = '';
      res.on('data', chunk => (body += chunk));
      res.on('end', () => {
        try {
          const result = JSON.parse(body);
          resolve({ status: res.statusCode, data: result });
        } catch (e) {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });

    req.on('error', reject);

    if (data) {
      req.write(JSON.stringify(data));
    }

    req.end();
  });
}

// Função para listar variáveis de ambiente
async function listEnvVars() {
  const options = {
    hostname: 'api.vercel.com',
    path: `/v9/projects/${PROJECT_ID}/env`,
    method: 'GET',
    headers: {
      Authorization: `Bearer ${VERCEL_TOKEN}`,
      'Content-Type': 'application/json',
    },
  };

  return makeRequest(options);
}

// Função para deletar uma variável de ambiente
async function deleteEnvVar(envId) {
  const options = {
    hostname: 'api.vercel.com',
    path: `/v9/projects/${PROJECT_ID}/env/${envId}`,
    method: 'DELETE',
    headers: {
      Authorization: `Bearer ${VERCEL_TOKEN}`,
      'Content-Type': 'application/json',
    },
  };

  return makeRequest(options);
}

// Função para criar uma variável de ambiente
async function createEnvVar(key, value, target = ['production', 'preview', 'development']) {
  const options = {
    hostname: 'api.vercel.com',
    path: `/v9/projects/${PROJECT_ID}/env`,
    method: 'POST',
    headers: {
      Authorization: `Bearer ${VERCEL_TOKEN}`,
      'Content-Type': 'application/json',
    },
  };

  const data = {
    key,
    value,
    target,
    type: 'encrypted',
  };

  return makeRequest(options, data);
}

// Função principal
async function main() {
  try {
    console.log('🔍 Listando variáveis de ambiente atuais...');

    // Listar variáveis atuais
    const listResult = await listEnvVars();
    if (listResult.status !== 200) {
      throw new Error(
        `Erro ao listar variáveis: ${listResult.status} - ${JSON.stringify(listResult.data)}`
      );
    }

    const envVars = listResult.data.envs || [];
    console.log(`📋 Encontradas ${envVars.length} variáveis de ambiente`);

    // Deletar todas as variáveis existentes
    console.log('🗑️ Deletando variáveis existentes...');
    let deletedCount = 0;

    for (const envVar of envVars) {
      try {
        console.log(`   Deletando: ${envVar.key} (ID: ${envVar.id})`);
        const deleteResult = await deleteEnvVar(envVar.id);

        if (deleteResult.status === 200) {
          deletedCount++;
          console.log(`   ✅ ${envVar.key} deletada com sucesso`);
        } else {
          console.log(`   ⚠️ Erro ao deletar ${envVar.key}: ${deleteResult.status}`);
        }

        // Pequena pausa para evitar rate limiting
        await new Promise(resolve => setTimeout(resolve, 100));
      } catch (error) {
        console.log(`   ❌ Erro ao deletar ${envVar.key}: ${error.message}`);
      }
    }

    console.log(`\n✅ Limpeza concluída! ${deletedCount}/${envVars.length} variáveis deletadas`);
    console.log('\n🔄 Agora você pode executar o script de configuração das novas variáveis');
  } catch (error) {
    console.error('❌ Erro durante a execução:', error.message);
    process.exit(1);
  }
}

// Executar script
if (require.main === module) {
  main();
}

module.exports = { main, createEnvVar };
