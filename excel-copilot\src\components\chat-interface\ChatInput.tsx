'use client';

import { <PERSON><PERSON><PERSON>, <PERSON>ader2, <PERSON><PERSON><PERSON>, ArrowR<PERSON>, Search, History } from 'lucide-react';
import React, { useState, useRef, useEffect, FormEvent, KeyboardEvent } from 'react';
import { toast } from 'sonner';

import { Button } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { BORDERS } from '@/lib/design-tokens';
import { cn } from '@/lib/utils';

import { CommandPalette } from './CommandPalette';

interface ChatInputProps {
  onSendMessage: (message: string) => Promise<any>;
  isLoading?: boolean;
  placeholder?: string;
  disabled?: boolean;
  showExamples?: boolean;
  autoFocus?: boolean;
  className?: string;
  onChange?: (text: string) => void;
}

export function ChatInput({
  onSendMessage,
  isLoading = false,
  placeholder = 'Digite um comando...',
  disabled = false,
  showExamples = true,
  autoFocus = true,
  className = '',
  onChange,
}: ChatInputProps) {
  const [message, setMessage] = useState('');
  const [showCommandPalette, setShowCommandPalette] = useState(false);
  const [animateInput, setAnimateInput] = useState(false);
  const [showRecentCommands, setShowRecentCommands] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  // Lista de comandos recentes
  const [recentCommands, setRecentCommands] = useState<string[]>(() => {
    // Carregar do localStorage se disponível
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('recentCommands');
      return saved ? JSON.parse(saved) : [];
    }
    return [];
  });

  // Índice para navegar pelos comandos recentes
  const [commandIndex, setCommandIndex] = useState(-1);

  // Salvar comandos recentes no localStorage
  useEffect(() => {
    if (recentCommands.length > 0) {
      localStorage.setItem('recentCommands', JSON.stringify(recentCommands));
    }
  }, [recentCommands]);

  // Efeito para animar o input quando estiver processando
  useEffect(() => {
    if (isLoading) {
      const interval = setInterval(() => {
        setAnimateInput(prev => !prev);
      }, 1000);
      return () => clearInterval(interval);
    } else {
      setAnimateInput(false);
    }
  }, [isLoading]);

  // Handler de envio do formulário
  const handleSubmit = async (e?: FormEvent) => {
    if (e) e.preventDefault();

    const trimmedMessage = message.trim();
    if (!trimmedMessage || isLoading || disabled) return;

    // Armazenar comando atual no histórico (evitando duplicatas)
    setRecentCommands(prev => {
      if (!Array.isArray(prev)) return [trimmedMessage];

      const newCommands = prev.filter(cmd => cmd !== trimmedMessage);
      return [trimmedMessage, ...newCommands].slice(0, 10); // Limitar a 10 comandos
    });

    // Limpar o input
    setMessage('');

    // Notificar componente pai sobre o input vazio
    if (onChange && typeof onChange === 'function') {
      onChange('');
    }

    // Fechar a paleta de comandos se estiver aberta
    if (showCommandPalette) {
      setShowCommandPalette(false);
    }

    // Enviar mensagem
    try {
      await onSendMessage(trimmedMessage);
    } catch (error) {
      console.error('Erro ao enviar mensagem:', error);
      toast.error('Erro ao enviar comando', {
        description: 'Não foi possível processar seu comando. Tente novamente.',
      });
    }

    // Voltar o foco para o input
    if (inputRef.current) {
      inputRef.current.focus();
    }

    // Resetar índice de navegação
    setCommandIndex(-1);
  };

  // Função atualizada de KeyDown para melhorar UX
  const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
    // Enter sem Shift - enviar mensagem
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit();
      return;
    }

    // Seta para cima - navegar para comandos anteriores
    if (e.key === 'ArrowUp' && !showCommandPalette) {
      // Se o input estiver vazio ou cursor no início, navegar no histórico
      if (
        (message === '' || e.currentTarget.selectionStart === 0) &&
        Array.isArray(recentCommands) &&
        recentCommands.length > 0
      ) {
        e.preventDefault();

        const newIndex =
          commandIndex < recentCommands.length - 1 ? commandIndex + 1 : recentCommands.length - 1;

        // Navegar para cima no histórico de comandos
        if (newIndex >= 0 && newIndex < recentCommands.length) {
          setCommandIndex(newIndex);
          // Adicionar verificação nula antes de usar o valor
          if (recentCommands[newIndex]) {
            setMessage(recentCommands[newIndex]);
          }
        }
      }
      return;
    }

    // Seta para baixo - navegar para comandos mais recentes
    if (e.key === 'ArrowDown' && !showCommandPalette && Array.isArray(recentCommands)) {
      e.preventDefault();

      if (commandIndex > 0) {
        const newIndex = commandIndex - 1;
        setCommandIndex(newIndex);
        // Adicionar verificação nula antes de usar o valor
        if (newIndex >= 0 && newIndex < recentCommands.length && recentCommands[newIndex]) {
          setMessage(recentCommands[newIndex]);
        }
      } else if (commandIndex === 0) {
        setCommandIndex(-1);
        setMessage('');
      }
      return;
    }

    // Tecla "/" - abrir paleta de comandos
    if (e.key === '/' && message === '') {
      e.preventDefault();
      setShowCommandPalette(true);
      return;
    }

    // Escape - fechar paleta de comandos
    if (e.key === 'Escape' && showCommandPalette) {
      e.preventDefault();
      setShowCommandPalette(false);
      return;
    }
  };

  // Selecionar comando da paleta
  const handleCommandSelect = (command: string) => {
    setMessage(command);
    setShowCommandPalette(false);

    if (inputRef.current) {
      inputRef.current.focus();
    }
  };

  // Selecionar comando do histórico
  const handleHistorySelect = (command: string) => {
    setMessage(command);
    setShowRecentCommands(false);

    if (inputRef.current) {
      inputRef.current.focus();
    }
  };

  // Exemplos populares para sugestões rápidas - ampliando a lista
  const popularExamples = [
    {
      text: 'Somar valores da coluna B',
      icon: <ArrowRight className="h-3 w-3" />,
      category: 'calc',
    },
    {
      text: 'Criar gráfico de vendas por região',
      icon: <Sparkles className="h-3 w-3" />,
      category: 'visual',
    },
    {
      text: 'Filtrar valores maiores que 100',
      icon: <Search className="h-3 w-3" />,
      category: 'filter',
    },
    {
      text: 'Formatar células como moeda',
      icon: <Sparkles className="h-3 w-3" />,
      category: 'format',
    },
    {
      text: 'Ordenar coluna A em ordem alfabética',
      icon: <ArrowRight className="h-3 w-3" />,
      category: 'order',
    },
  ];

  return (
    <div className={`relative w-full ${className}`}>
      {showCommandPalette && (
        <CommandPalette
          onSelect={handleCommandSelect}
          onClose={() => setShowCommandPalette(false)}
        />
      )}

      <form onSubmit={handleSubmit} className="flex items-center gap-2 w-full">
        <div className="relative flex-1">
          <input
            ref={inputRef}
            className={cn(
              'flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
              'pr-10', // Espaço para o botão de envio
              BORDERS.radius.md,
              animateInput ? 'border-primary' : undefined
            )}
            placeholder={isLoading ? 'Processando comando...' : placeholder}
            value={message}
            onChange={e => {
              setMessage(e.target.value);
              if (onChange) {
                onChange(e.target.value);
              }
            }}
            onKeyDown={handleKeyDown}
            disabled={isLoading || disabled}
            autoFocus={autoFocus}
            aria-label="Digite seu comando para a planilha"
          />
          {isLoading && (
            <div className="absolute right-3 top-1/2 -translate-y-1/2 text-primary">
              <Loader2 className="h-4 w-4 animate-spin" />
            </div>
          )}
        </div>

        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="flex gap-1">
                <Popover open={showRecentCommands} onOpenChange={setShowRecentCommands}>
                  <PopoverTrigger asChild>
                    <Button
                      type="button"
                      size="icon"
                      variant="outline"
                      disabled={recentCommands.length === 0}
                      className="shrink-0"
                    >
                      <History className="h-4 w-4" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-72 p-0" align="end">
                    <div className="text-sm font-medium p-3 border-b">Comandos recentes</div>
                    <div className="max-h-[200px] overflow-y-auto">
                      {recentCommands.map((cmd, index) => (
                        <div
                          key={index}
                          onClick={() => handleHistorySelect(cmd)}
                          className="p-2 hover:bg-muted cursor-pointer text-sm truncate px-3"
                        >
                          {cmd}
                        </div>
                      ))}
                    </div>
                  </PopoverContent>
                </Popover>

                <Button
                  type="submit"
                  size="icon"
                  variant={message.trim() ? 'default' : 'secondary'}
                  disabled={!message.trim() || isLoading || disabled}
                  aria-label="Enviar comando"
                  className="transition-all duration-300 shrink-0"
                >
                  {isLoading ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <SendIcon className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </TooltipTrigger>
            <TooltipContent>
              <p>Enviar comando (Enter)</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </form>

      {showExamples && !message && !showCommandPalette && recentCommands.length === 0 && (
        <div className="flex flex-wrap gap-1 mt-2">
          {popularExamples.map((example, index) => (
            <Button
              key={index}
              variant="outline"
              size="sm"
              className="h-7 text-xs"
              onClick={() => setMessage(example.text)}
            >
              {example.icon}
              <span className="ml-1">{example.text}</span>
            </Button>
          ))}
        </div>
      )}
    </div>
  );
}
