// WebSocket Status Enum
export enum WebSocketStatus {
  Disconnected = 'disconnected',
  Connecting = 'connecting',
  Connected = 'connected',
  Error = 'error',
  Failed = 'failed',
}

// WebSocket Message Interface
export interface WebSocketMessage {
  id: string; // Mandatory unique identifier for each message
  type: string;
  operation?: string;
  data?: unknown;
  error?: string;
}

// Operation Result Interface
export interface OperationResult {
  success: boolean;
  data?: unknown;
  error?: string;
}

// Workbook Information Interface
export interface WorkbookInfo {
  id: string;
  name: string;
  path: string;
  sheets: string[];
  lastModified: string;
}
