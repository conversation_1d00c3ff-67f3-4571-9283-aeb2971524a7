'use client';

import { motion, useReducedMotion } from 'framer-motion';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Check } from 'lucide-react';
import Link from 'next/link';
import { useState, useEffect, useCallback, useMemo } from 'react';
import { TypeAnimation } from 'react-type-animation';
import {
  Bar<PERSON>hart,
  Bar,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { ExcelOperationType } from '@/types';

// Cores para gráficos
const CHART_COLORS = [
  '#0088FE',
  '#00C49F',
  '#FFBB28',
  '#FF8042',
  '#8884D8',
  '#82CA9D',
  '#FFC658',
  '#8DD1E1',
];

// Tipos de demo
interface DemoResponse {
  type: 'message' | 'table' | 'chart' | ExcelOperationType;
  command?: string;
  content?: string | (string | number)[][];
  chartType?: string;
  data?: any;
  highlightCells?: { row: number; col: number }[];
}

// Exemplos de demo para a página inicial
const demoResponses: DemoResponse[] = [
  // Demo de resposta de mensagem
  {
    type: 'message',
    content:
      'Posso ajudar você a trabalhar com suas planilhas usando comandos em linguagem natural. Por exemplo, tente "Crie uma tabela de vendas por região" ou "Calcule a média da coluna B".',
  },
  // Demo da transformação de dados em tabela
  {
    type: ExcelOperationType.TABLE,
    command: 'Crie uma tabela com vendas por região',
    content: [
      ['Região', 'Vendas', 'Meta', '% Atingimento'],
      ['Norte', '12500', '15000', '83%'],
      ['Sul', '18200', '16000', '114%'],
      ['Leste', '14800', '14000', '106%'],
      ['Oeste', '9300', '12000', '78%'],
      ['Centro', '11700', '10000', '117%'],
    ],
  },
  // Demo de criação de gráfico
  {
    type: ExcelOperationType.CHART,
    command: 'Crie um gráfico de vendas por região',
    chartType: 'bar',
    data: {
      labels: ['Norte', 'Sul', 'Leste', 'Oeste', 'Centro'],
      datasets: [
        {
          label: 'Vendas',
          data: [12500, 18200, 14800, 9300, 11700],
          backgroundColor: 'rgba(53, 162, 235, 0.5)',
        },
        {
          label: 'Meta',
          data: [15000, 16000, 14000, 12000, 10000],
          backgroundColor: 'rgba(255, 99, 132, 0.5)',
        },
      ],
    },
  },
  // Demo de análise de dados
  {
    type: ExcelOperationType.TABLE,
    command: 'Faça uma análise das vendas',
    content: [
      ['Métrica', 'Valor', 'Status'],
      ['Total de Vendas', 'R$ 66.500,00', '↑ 12%'],
      ['Média por Região', 'R$ 13.300,00', '-'],
      ['Maior Desempenho', 'Sul (114%)', '★★★'],
      ['Menor Desempenho', 'Oeste (78%)', '⚠️'],
      ['Regiões Acima da Meta', '3', '↑ 1'],
    ],
  },
];

export function HeroSection() {
  // Estados para animação
  const [currentCommandIndex, setCurrentCommandIndex] = useState(0);
  const [showingAll, setShowingAll] = useState(false);
  const [isAutoPlaying, setIsAutoPlaying] = useState(false);
  const [isTypingComplete, setIsTypingComplete] = useState(false);
  const [visibleResponses, setVisibleResponses] = useState<DemoResponse[]>([]);
  const [isMobile, setIsMobile] = useState(false);

  // Verificar se o usuário prefere animações reduzidas
  const prefersReducedMotion = useReducedMotion();

  // Detectar se é dispositivo móvel
  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    // Verificar inicialmente
    checkIfMobile();

    // Adicionar listener para mudanças de tamanho
    window.addEventListener('resize', checkIfMobile);

    // Cleanup
    return () => window.removeEventListener('resize', checkIfMobile);
  }, []);

  // Configurações de animação otimizadas para dispositivos móveis e preferência do usuário
  const animationConfig = useMemo(() => {
    // Se o usuário prefere animações reduzidas ou está em dispositivo móvel, simplificamos as animações
    if (prefersReducedMotion || isMobile) {
      return {
        initial: { opacity: 0 },
        animate: { opacity: 1 },
        transition: { duration: 0.2 },
        disableMotion: true,
      };
    }

    // Animações completas para desktop
    return {
      initial: { opacity: 0, y: 10 },
      animate: { opacity: 1, y: 0 },
      transition: { duration: 0.3 },
      disableMotion: false,
    };
  }, [prefersReducedMotion, isMobile]);

  // Processar comando atual
  const processCommand = useCallback(
    (_command: string) => {
      // Mostrar as respostas para o comando atual
      setShowingAll(true);

      // Atualizar respostas visíveis baseado no comando atual
      if (currentCommandIndex === 0) {
        setVisibleResponses(demoResponses.slice(0, 2));
      } else if (currentCommandIndex === 1) {
        setVisibleResponses(demoResponses.slice(0, 4));
      } else if (currentCommandIndex === 2) {
        setVisibleResponses(demoResponses);
      }
    },
    [currentCommandIndex]
  );

  // Função para mostrar próximo comando da demo
  const showNextCommand = useCallback(() => {
    if (currentCommandIndex < demoResponses.length - 1) {
      setIsTypingComplete(true);
      const nextIndex = currentCommandIndex + 1;
      setCurrentCommandIndex(nextIndex);

      // Simular processamento
      setTimeout(() => {
        setIsTypingComplete(false);
        const nextCommand = demoResponses[nextIndex]?.command || '';
        processCommand(nextCommand);
      }, 1500);
    } else {
      // Reset para começar de novo
      setCurrentCommandIndex(0);
      setShowingAll(false);
      setVisibleResponses([]);
    }
  }, [currentCommandIndex, processCommand]);

  // Auto-play da demo
  useEffect(() => {
    if (isAutoPlaying) {
      const timer = setTimeout(() => {
        showNextCommand();
      }, 4000);

      return () => clearTimeout(timer);
    }
  }, [currentCommandIndex, isAutoPlaying, showNextCommand]);

  // Iniciar a demo quando o componente montar
  useEffect(() => {
    // Iniciar a exibição do primeiro comando após um delay
    const timer = setTimeout(() => {
      const firstCommand = demoResponses[0]?.command || '';
      processCommand(firstCommand);
    }, 1000);

    return () => clearTimeout(timer);
  }, [processCommand]);

  // Adicionando um efeito para simular o onComplete da TypeAnimation
  useEffect(() => {
    // Tempo médio para digitar o comando
    if (!isTypingComplete && demoResponses[currentCommandIndex]?.command) {
      const typingTime = (demoResponses[currentCommandIndex]?.command?.length || 0) * 50;
      const timer = setTimeout(() => {
        setIsTypingComplete(true);
        processCommand(demoResponses[currentCommandIndex]?.command || '');
      }, typingTime + 500); // Adicionando 500ms como buffer

      return () => clearTimeout(timer);
    }
  }, [isTypingComplete, currentCommandIndex, processCommand]);

  // Função para retroceder ao comando anterior
  const _showPreviousCommand = useCallback(() => {
    if (currentCommandIndex > 0) {
      setIsTypingComplete(true);
      const prevIndex = currentCommandIndex - 1;
      setCurrentCommandIndex(prevIndex);

      // Simular processamento
      setTimeout(() => {
        setIsTypingComplete(false);
        const prevCommand = demoResponses[prevIndex]?.command || '';
        processCommand(prevCommand);
      }, 500);
    }
  }, [currentCommandIndex, processCommand]);

  // Dentro do componente HeroSection, adicionar keydown handler para navegação por teclado
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Navegar entre comandos com setas esquerda/direita
      if (e.key === 'ArrowRight') {
        showNextCommand();
      } else if (e.key === 'ArrowLeft' && currentCommandIndex > 0) {
        setCurrentCommandIndex(prev => prev - 1);
        setIsTypingComplete(true);
        const prevCommand = demoResponses[currentCommandIndex - 1]?.command || '';
        processCommand(prevCommand);
      }
      // Ativar/desativar autoplay com espaço quando focado no botão de autoplay
      else if (e.key === ' ' && document.activeElement?.id === 'autoplay-button') {
        e.preventDefault(); // Prevenir scroll da página
        setIsAutoPlaying(!isAutoPlaying);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [currentCommandIndex, isAutoPlaying, processCommand, showNextCommand]);

  return (
    <div className="flex flex-col md:flex-row items-center gap-8 py-8">
      <div className="text-left md:w-1/2">
        <div className="inline-flex items-center rounded-full bg-gradient-to-r from-blue-500/10 to-indigo-500/10 backdrop-blur-sm px-3 py-1 mb-4 shadow-sm">
          <Sparkles className="h-3 w-3 mr-1 text-blue-600 dark:text-blue-400" />
          <span className="text-xs font-medium text-blue-700 dark:text-blue-300">
            IA para Excel
          </span>
        </div>

        <h1 className="text-4xl md:text-5xl font-bold tracking-tight mb-4 bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-indigo-600 dark:from-blue-400 dark:to-indigo-400 leading-tight">
          Planilhas inteligentes com linguagem natural
        </h1>

        <p className="text-lg text-gray-600 dark:text-gray-300 mb-6 max-w-xl">
          Excel Copilot transforma comandos em texto simples em planilhas poderosas.
        </p>

        <div className="flex flex-wrap gap-3">
          <Button
            size="lg"
            className="rounded-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white border-0 px-6 py-2 text-sm shadow-md font-medium transition-all"
            asChild
          >
            <Link href="/dashboard">
              Começar agora <ArrowRight className="ml-1 h-4 w-4" />
            </Link>
          </Button>
          <Button
            variant="outline"
            size="lg"
            className="rounded-full border border-blue-200 dark:border-blue-800 hover:border-blue-300 dark:hover:border-blue-700 px-6 py-2 text-sm font-medium transition-all"
            asChild
          >
            <Link href="#exemplos">Ver exemplos</Link>
          </Button>
        </div>

        {/* Key benefits - Versão compacta */}
        <div className="mt-6 space-y-2">
          <div className="flex items-start gap-2">
            <Check className="h-4 w-4 text-green-600 dark:text-green-400 mt-0.5" />
            <p className="text-sm text-gray-700 dark:text-gray-300">
              Comandos em linguagem natural para criar planilhas
            </p>
          </div>
          <div className="flex items-start gap-2">
            <Check className="h-4 w-4 text-green-600 dark:text-green-400 mt-0.5" />
            <p className="text-sm text-gray-700 dark:text-gray-300">
              Fórmulas complexas geradas automaticamente
            </p>
          </div>
          <div className="flex items-start gap-2">
            <Check className="h-4 w-4 text-green-600 dark:text-green-400 mt-0.5" />
            <p className="text-sm text-gray-700 dark:text-gray-300">
              Visualizações e gráficos em segundos
            </p>
          </div>
        </div>
      </div>

      {/* Demo interativo */}
      <div className="md:w-1/2 w-full mt-8 md:mt-0">
        <div className="rounded-xl border shadow-md bg-white dark:bg-gray-900/60 overflow-hidden backdrop-blur-sm">
          {/* Barra superior */}
          <div className="bg-gray-100 dark:bg-gray-800 p-3 border-b flex items-center justify-between">
            <div className="flex space-x-1.5">
              <div className="w-3 h-3 rounded-full bg-gray-300 dark:bg-gray-600"></div>
              <div className="w-3 h-3 rounded-full bg-gray-300 dark:bg-gray-600"></div>
              <div className="w-3 h-3 rounded-full bg-gray-300 dark:bg-gray-600"></div>
            </div>
            <Badge variant="outline" className="text-xs font-normal hidden xs:inline">
              Demo Interativa
            </Badge>
            <Button
              id="autoplay-button"
              variant="ghost"
              size="sm"
              onClick={() => setIsAutoPlaying(!isAutoPlaying)}
              className="text-xs"
              aria-pressed={isAutoPlaying}
              aria-label={
                isAutoPlaying ? 'Desativar execução automática' : 'Ativar execução automática'
              }
            >
              {isAutoPlaying ? 'Pausar' : 'Auto-play'}
            </Button>
          </div>

          {/* Área de visualização da planilha */}
          <div className="p-2 sm:p-4 min-h-[250px] sm:min-h-[300px] max-h-[350px] sm:max-h-[400px] overflow-y-auto">
            {/* Respostas já processadas */}
            {showingAll &&
              visibleResponses.map((response, index) => (
                <motion.div
                  key={index}
                  initial={animationConfig.initial}
                  animate={animationConfig.animate}
                  transition={animationConfig.transition}
                  className="mb-4"
                >
                  {response.type === 'message' ? (
                    <div className="text-sm text-gray-600 dark:text-gray-400 italic">
                      {response.content}
                    </div>
                  ) : response.type === ExcelOperationType.TABLE ? (
                    <div className="overflow-auto max-h-[200px] sm:max-h-[250px]">
                      {/* Renderização de tabela com melhor responsividade */}
                      <table
                        className="min-w-full divide-y divide-gray-300 dark:divide-gray-700 border border-gray-300 dark:border-gray-700 text-xs sm:text-sm"
                        role="table"
                        aria-label="Dados tabulares de exemplo"
                      >
                        <thead className="bg-gray-50 dark:bg-gray-800">
                          <tr>
                            {Array.isArray(response.content) &&
                            (response.content as any[][]).length > 0
                              ? (response.content as any[][])[0]?.map((header, i) => (
                                  <th
                                    key={`header-${i}`}
                                    scope="col"
                                    className="px-2 py-2 sm:px-3 sm:py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider"
                                  >
                                    {header}
                                  </th>
                                ))
                              : null}
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200 dark:bg-gray-900 dark:divide-gray-800">
                          {Array.isArray(response.content) &&
                          (response.content as any[][]).length > 0
                            ? (response.content as any[][]).slice(1).map((row, rowIndex) => (
                                <tr
                                  key={rowIndex}
                                  className={
                                    rowIndex % 2 === 0
                                      ? 'bg-white dark:bg-gray-900/80'
                                      : 'bg-gray-50 dark:bg-gray-900/50'
                                  }
                                >
                                  {row.map((cell, cellIndex) => (
                                    <td
                                      key={cellIndex}
                                      className={cn(
                                        'px-2 py-2 sm:px-3 sm:py-3 whitespace-nowrap',
                                        response.highlightCells?.some(
                                          hc => hc.row === rowIndex + 1 && hc.col === cellIndex
                                        )
                                          ? 'bg-blue-100 dark:bg-blue-900/30'
                                          : ''
                                      )}
                                    >
                                      {cell}
                                    </td>
                                  ))}
                                </tr>
                              ))
                            : null}
                        </tbody>
                      </table>
                    </div>
                  ) : response.type === ExcelOperationType.CHART ? (
                    <div className="w-full h-[150px] sm:h-[200px] md:h-[250px] bg-white dark:bg-gray-800 p-2 rounded-md">
                      {/* Renderização simplificada de gráfico para dispositivos móveis */}
                      {isMobile || prefersReducedMotion ? (
                        <SimpleChartView data={response.data} chartType={response.chartType} />
                      ) : (
                        /* Renderização de gráfico responsivo para desktop */
                        <ResponsiveContainer width="100%" height="100%">
                          {response.chartType === 'bar' ? (
                            <BarChart
                              data={response.data.datasets[0].data.map(
                                (value: number, index: number) => ({
                                  name: response.data.labels[index],
                                  value,
                                  meta: response.data.datasets[0].label,
                                  value2: response.data.datasets[1]?.data[index],
                                  meta2: response.data.datasets[1]?.label,
                                })
                              )}
                            >
                              <CartesianGrid strokeDasharray="3 3" opacity={0.2} />
                              <XAxis
                                dataKey="name"
                                fontSize={10}
                                tick={{ fill: 'currentColor' }}
                                tickFormatter={value =>
                                  value.length > 5 ? `${value.substring(0, 5)}...` : value
                                }
                              />
                              <YAxis fontSize={10} tick={{ fill: 'currentColor' }} />
                              <Tooltip
                                contentStyle={{
                                  backgroundColor: 'rgba(255, 255, 255, 0.9)',
                                  border: '1px solid #ccc',
                                  borderRadius: '4px',
                                  fontSize: '11px',
                                }}
                              />
                              <Legend wrapperStyle={{ fontSize: '10px' }} />
                              <Bar
                                dataKey="value"
                                name={response.data.datasets[0].label}
                                fill={response.data.datasets[0].backgroundColor}
                                radius={[4, 4, 0, 0]}
                              />
                              {response.data.datasets[1] && (
                                <Bar
                                  dataKey="value2"
                                  name={response.data.datasets[1].label}
                                  fill={response.data.datasets[1].backgroundColor}
                                  radius={[4, 4, 0, 0]}
                                />
                              )}
                            </BarChart>
                          ) : response.chartType === 'line' ? (
                            <LineChart
                              data={response.data.datasets[0].data.map(
                                (value: number, index: number) => ({
                                  name: response.data.labels[index],
                                  value,
                                  meta: response.data.datasets[0].label,
                                  value2: response.data.datasets[1]?.data[index],
                                  meta2: response.data.datasets[1]?.label,
                                })
                              )}
                            >
                              <CartesianGrid strokeDasharray="3 3" opacity={0.2} />
                              <XAxis
                                dataKey="name"
                                fontSize={10}
                                tick={{ fill: 'currentColor' }}
                                tickFormatter={value =>
                                  value.length > 5 ? `${value.substring(0, 5)}...` : value
                                }
                              />
                              <YAxis fontSize={10} tick={{ fill: 'currentColor' }} />
                              <Tooltip
                                contentStyle={{
                                  backgroundColor: 'rgba(255, 255, 255, 0.9)',
                                  border: '1px solid #ccc',
                                  borderRadius: '4px',
                                  fontSize: '11px',
                                }}
                              />
                              <Legend wrapperStyle={{ fontSize: '10px' }} />
                              <Line
                                type="monotone"
                                dataKey="value"
                                name={response.data.datasets[0].label}
                                stroke={response.data.datasets[0].backgroundColor}
                                activeDot={{ r: 8 }}
                                strokeWidth={2}
                              />
                              {response.data.datasets[1] && (
                                <Line
                                  type="monotone"
                                  dataKey="value2"
                                  name={response.data.datasets[1].label}
                                  stroke={response.data.datasets[1].backgroundColor}
                                  activeDot={{ r: 8 }}
                                  strokeWidth={2}
                                />
                              )}
                            </LineChart>
                          ) : (
                            <PieChart>
                              <Pie
                                data={response.data.datasets[0].data.map(
                                  (value: number, index: number) => ({
                                    name: response.data.labels[index],
                                    value,
                                  })
                                )}
                                cx="50%"
                                cy="50%"
                                labelLine={false}
                                outerRadius={60}
                                fill="#8884d8"
                                dataKey="value"
                                nameKey="name"
                                label={({ name, percent }) =>
                                  `${name}: ${(percent * 100).toFixed(0)}%`
                                }
                              >
                                {response.data.datasets[0].data.map((_: any, index: number) => (
                                  <Cell
                                    key={`cell-${index}`}
                                    fill={
                                      index < CHART_COLORS.length
                                        ? CHART_COLORS[index]
                                        : CHART_COLORS[index % CHART_COLORS.length]
                                    }
                                  />
                                ))}
                              </Pie>
                              <Tooltip
                                formatter={value => [`${value}`, '']}
                                contentStyle={{
                                  backgroundColor: 'rgba(255, 255, 255, 0.9)',
                                  border: '1px solid #ccc',
                                  borderRadius: '4px',
                                  fontSize: '11px',
                                }}
                              />
                              <Legend wrapperStyle={{ fontSize: '10px' }} />
                            </PieChart>
                          )}
                        </ResponsiveContainer>
                      )}
                    </div>
                  ) : null}
                </motion.div>
              ))}

            {/* Comando atual a ser digitado (efeito de digitação) */}
            {!isTypingComplete && demoResponses[currentCommandIndex]?.command && (
              <div className="mb-6">
                <div className="flex items-center gap-2 mb-2">
                  <div className="h-6 w-6 rounded-full bg-blue-500 flex items-center justify-center">
                    <span className="text-xs text-white font-medium">
                      {currentCommandIndex + 1}
                    </span>
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">Digite um comando:</div>
                </div>
                <div className="flex items-center">
                  <div className="flex-1 p-3 bg-gray-100 dark:bg-gray-800 rounded-md text-sm font-mono">
                    <TypeAnimation
                      cursor={true}
                      sequence={[demoResponses[currentCommandIndex]?.command || '']}
                      wrapper="span"
                      speed={50}
                      style={{ display: 'inline-block' }}
                      repeat={0}
                      className="text-gray-800 dark:text-gray-200"
                    />
                  </div>
                  <button
                    className="ml-2 p-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                    onClick={() => showNextCommand()}
                    aria-label="Próximo comando"
                  >
                    <ArrowRight className="h-4 w-4" aria-hidden="true" />
                  </button>
                </div>
              </div>
            )}

            {/* Indicador de digitação concluída para acessibilidade */}
            <div aria-live="polite" className="sr-only">
              {isTypingComplete ? 'Comando digitado. Processando resultado.' : ''}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Componente de gráfico simplificado para dispositivos móveis
function SimpleChartView({ data, chartType }: { data: any; chartType: string | undefined }) {
  // Verificar se temos dados para renderizar
  if (!data || !data.datasets || !data.datasets[0]) {
    return (
      <div className="flex h-full items-center justify-center">
        <p className="text-xs text-gray-500">Dados do gráfico indisponíveis</p>
      </div>
    );
  }

  // Extrair dados principais
  const { labels, datasets } = data;
  const mainDataset = datasets[0];
  const secondaryDataset = datasets[1];

  // Para otimização em mobile, vamos usar uma visualização simplificada
  return (
    <div className="h-full w-full flex flex-col" aria-label={`Gráfico de ${chartType}`} role="img">
      <div className="text-xs font-medium mb-2 text-center">
        {chartType === 'bar'
          ? 'Gráfico de Barras'
          : chartType === 'line'
            ? 'Gráfico de Linha'
            : 'Gráfico de Pizza'}
      </div>

      <div className="flex-1 flex items-end space-x-1 px-2 overflow-hidden">
        {labels.map((label: string, index: number) => {
          // Calcular altura relativa com base no valor máximo
          const maxValue = Math.max(...mainDataset.data);
          const heightPercentage = Math.max(
            10, // Mínimo 10% para visibilidade
            (mainDataset.data[index] / maxValue) * 100
          );

          return (
            <div key={index} className="flex flex-col items-center flex-1 min-w-0">
              <div
                className="w-full relative rounded-t-sm focus:outline-none focus:ring-2 focus:ring-primary"
                style={{
                  height: `${heightPercentage}%`,
                  backgroundColor: mainDataset.backgroundColor,
                }}
                aria-label={`${label}: ${mainDataset.data[index]}`}
                tabIndex={0}
                role="button"
                onKeyDown={e => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    // Ativar alguma ação ao pressionar Enter ou Espaço, como mostrar detalhes
                    e.preventDefault();
                    alert(`${label}: ${mainDataset.data[index]}`);
                  }
                }}
              />
              <div className="text-[8px] mt-1 truncate w-full text-center" title={label}>
                {label}
              </div>
            </div>
          );
        })}
      </div>

      <div className="flex justify-center mt-2 gap-3">
        <div className="flex items-center gap-1">
          <div
            className="w-2 h-2"
            style={{ backgroundColor: mainDataset.backgroundColor }}
            aria-hidden="true"
          />
          <span className="text-[9px]">{mainDataset.label}</span>
        </div>

        {secondaryDataset && (
          <div className="flex items-center gap-1">
            <div
              className="w-2 h-2"
              style={{ backgroundColor: secondaryDataset.backgroundColor }}
              aria-hidden="true"
            />
            <span className="text-[9px]">{secondaryDataset.label}</span>
          </div>
        )}
      </div>
    </div>
  );
}
