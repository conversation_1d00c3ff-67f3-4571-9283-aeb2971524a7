import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';

import { logger } from '@/lib/logger';
import { API_CALL_LIMITS, PLANS } from '@/lib/stripe';
import { prisma } from '@/server/db/client';

// Interface para tipagem do usuário da sessão
interface SessionUser {
  id: string;
  name?: string;
  email?: string;
}

// Forçar o modo dinâmico para essa rota
export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

export async function GET(_req: NextRequest) {
  try {
    // Verificar autenticação
    const session = await getServerSession();
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Não autorizado. Faça login para continuar.' },
        { status: 401 }
      );
    }

    const userId = (session.user as SessionUser).id;

    // Buscar assinatura ativa do usuário
    const subscriptions = await prisma.subscription.findMany({
      where: {
        userId,
        OR: [{ status: 'active' }, { status: 'trialing' }, { status: 'past_due' }],
      },
      orderBy: { createdAt: 'desc' },
      take: 1,
    });

    // Calcular uso de API
    const currentMonthStart = new Date();
    currentMonthStart.setDate(1);
    currentMonthStart.setHours(0, 0, 0, 0);

    const apiUsage = await prisma.apiUsage.aggregate({
      where: {
        userId,
        billable: true,
        createdAt: {
          gte: currentMonthStart,
        },
      },
      _sum: {
        count: true,
      },
    });

    const totalApiCalls = apiUsage._sum.count || 0;

    // Se o usuário tem uma assinatura ativa
    if (subscriptions.length > 0) {
      const subscription = subscriptions[0];

      if (subscription) {
        // Atualizar contador de uso na assinatura
        await prisma.subscription.update({
          where: { id: subscription.id },
          data: {
            apiCallsUsed: totalApiCalls,
          },
        });

        // Garantir que apiCallsLimit seja um número
        const apiCallsLimit = subscription.apiCallsLimit || API_CALL_LIMITS[subscription.plan];

        return NextResponse.json({
          subscription: {
            id: subscription.id,
            plan: subscription.plan,
            status: subscription.status,
            currentPeriodEnd: subscription.currentPeriodEnd,
            apiCallsUsed: totalApiCalls,
            apiCallsLimit,
            cancelAtPeriodEnd: subscription.cancelAtPeriodEnd || false,
          },
        });
      }
    }

    // Se o usuário não tem assinatura, retornar plano gratuito
    return NextResponse.json({
      subscription: {
        id: 'free',
        plan: PLANS.FREE,
        status: 'active',
        currentPeriodEnd: null,
        apiCallsUsed: totalApiCalls,
        apiCallsLimit: API_CALL_LIMITS[PLANS.FREE],
        cancelAtPeriodEnd: false,
      },
    });
  } catch (error) {
    logger.error('[SUBSCRIPTION_ERROR]', error);
    return NextResponse.json(
      {
        error: 'Erro ao obter informações de assinatura.',
        details: process.env.NODE_ENV === 'development' ? String(error) : undefined,
      },
      { status: 500 }
    );
  }
}
