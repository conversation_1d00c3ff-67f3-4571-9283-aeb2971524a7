import { NextRequest } from 'next/server';

import { GitHubClient, GitHubIssue, GitHubPullRequest } from '@/lib/github-integration';
import { logger } from '@/lib/logger';
import { ApiResponse } from '@/utils/api-response';

// Forçar o modo dinâmico para essa rota
export const dynamic = 'force-dynamic';

/**
 * GET /api/github/issues
 * Lista issues e pull requests de um repositório
 */
export async function GET(request: NextRequest) {
  try {
    // Verificar se temos as credenciais necessárias
    const token = process.env.MCP_GITHUB_TOKEN;
    const defaultOwner = process.env.MCP_GITHUB_OWNER;
    const defaultRepo = process.env.MCP_GITHUB_REPO;

    if (!token) {
      return ApiResponse.error('GITHUB_TOKEN não configurado', 'GITHUB_NOT_CONFIGURED', 500);
    }

    // Obter parâmetros da query
    const { searchParams } = new URL(request.url);
    const owner = searchParams.get('owner') || defaultOwner;
    const repo = searchParams.get('repo') || defaultRepo;
    const type = (searchParams.get('type') as 'issues' | 'pulls' | 'all') || 'all';
    const state = (searchParams.get('state') as 'open' | 'closed' | 'all') || 'open';
    const labels = searchParams.get('labels');
    const sort = (searchParams.get('sort') as 'created' | 'updated' | 'comments') || 'updated';
    const direction = (searchParams.get('direction') as 'asc' | 'desc') || 'desc';
    const per_page = parseInt(searchParams.get('per_page') || '30');
    const page = parseInt(searchParams.get('page') || '1');

    if (!owner || !repo) {
      return ApiResponse.badRequest('owner e repo são obrigatórios');
    }

    // Validar parâmetros
    if (per_page < 1 || per_page > 100) {
      return ApiResponse.badRequest('Parâmetro per_page deve estar entre 1 e 100');
    }

    if (page < 1) {
      return ApiResponse.badRequest('Parâmetro page deve ser maior que 0');
    }

    // Criar cliente GitHub
    const githubClient = new GitHubClient({ token });

    let issues: GitHubIssue[] = [];
    let pullRequests: GitHubPullRequest[] = [];

    // Obter dados baseado no tipo solicitado
    if (type === 'issues' || type === 'all') {
      const issuesResult = await githubClient.getIssues({
        owner,
        repo,
        state,
        ...(labels && { labels }),
        sort: sort as 'created' | 'updated' | 'comments',
        direction,
        per_page,
        page,
      });
      issues = issuesResult.issues;
    }

    if (type === 'pulls' || type === 'all') {
      const pullsResult = await githubClient.getPullRequests({
        owner,
        repo,
        state,
        sort: sort as 'created' | 'updated' | 'popularity' | 'long-running',
        direction,
        per_page,
        page,
      });
      pullRequests = pullsResult.pullRequests;
    }

    // Formatar issues
    const formattedIssues = issues.map(issue => ({
      id: issue.id,
      number: issue.number,
      title: issue.title,
      body: issue.body,
      state: issue.state,
      htmlUrl: issue.html_url,
      user: {
        login: issue.user.login,
        id: issue.user.id,
        avatarUrl: issue.user.avatar_url,
      },
      assignees: issue.assignees.map(assignee => ({
        login: assignee.login,
        id: assignee.id,
        avatarUrl: assignee.avatar_url,
      })),
      labels: issue.labels.map(label => ({
        id: label.id,
        name: label.name,
        color: label.color,
        description: label.description,
      })),
      milestone: issue.milestone
        ? {
            id: issue.milestone.id,
            title: issue.milestone.title,
            description: issue.milestone.description,
            state: issue.milestone.state,
            dueOn: issue.milestone.due_on,
          }
        : null,
      createdAt: issue.created_at,
      updatedAt: issue.updated_at,
      closedAt: issue.closed_at,
    }));

    // Formatar pull requests
    const formattedPullRequests = pullRequests.map(pr => ({
      id: pr.id,
      number: pr.number,
      title: pr.title,
      body: pr.body,
      state: pr.state,
      draft: pr.draft,
      merged: pr.merged,
      mergedAt: pr.merged_at,
      htmlUrl: pr.html_url,
      user: {
        login: pr.user.login,
        id: pr.user.id,
        avatarUrl: pr.user.avatar_url,
      },
      assignees: pr.assignees.map(assignee => ({
        login: assignee.login,
        id: assignee.id,
        avatarUrl: assignee.avatar_url,
      })),
      labels: pr.labels.map(label => ({
        id: label.id,
        name: label.name,
        color: label.color,
        description: label.description,
      })),
      head: {
        ref: pr.head.ref,
        sha: pr.head.sha,
        repo: pr.head.repo ? pr.head.repo.full_name : null,
      },
      base: {
        ref: pr.base.ref,
        sha: pr.base.sha,
        repo: pr.base.repo ? pr.base.repo.full_name : null,
      },
      mergeable: pr.mergeable,
      mergeableState: pr.mergeable_state,
      reviewComments: pr.review_comments,
      commits: pr.commits,
      additions: pr.additions,
      deletions: pr.deletions,
      changedFiles: pr.changed_files,
      createdAt: pr.created_at,
      updatedAt: pr.updated_at,
      closedAt: pr.closed_at,
    }));

    const response = {
      repository: `${owner}/${repo}`,
      type,
      issues: formattedIssues,
      pullRequests: formattedPullRequests,
      pagination: {
        page,
        perPage: per_page,
        hasNext: formattedIssues.length + formattedPullRequests.length === per_page,
      },
      filters: {
        state,
        labels,
        sort,
        direction,
      },
      timestamp: new Date().toISOString(),
    };

    logger.info('Issues/PRs GitHub obtidos com sucesso', {
      repository: `${owner}/${repo}`,
      type,
      issuesCount: formattedIssues.length,
      pullRequestsCount: formattedPullRequests.length,
    });

    return ApiResponse.success(response);
  } catch (error) {
    logger.error('Erro ao obter issues/PRs do GitHub', { error });

    if (error instanceof Error) {
      return ApiResponse.error(
        `Erro ao conectar com GitHub: ${error.message}`,
        'GITHUB_API_ERROR',
        500
      );
    }

    return ApiResponse.error('Erro interno do servidor', 'INTERNAL_ERROR', 500);
  }
}
