import { useCallback, useState, useEffect, useRef } from 'react';

import { useToast } from '@/components/ui/use-toast';

// Define the ErrorType enum directly if it doesn't import correctly
enum ErrorType {
  // Erros de autenticação
  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR',
  AUTHORIZATION_ERROR = 'AUTHORIZATION_ERROR',
  UNAUTHORIZED = 'UNAUTHORIZED',
  FORBIDDEN = 'FORBIDDEN',

  // Erros de recurso
  NOT_FOUND = 'NOT_FOUND',
  ALREADY_EXISTS = 'ALREADY_EXISTS',

  // Erros de validação
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  INVALID_INPUT = 'INVALID_INPUT',
  INVALID_REQUEST = 'INVALID_REQUEST',

  // Erros de serviço
  SERVICE_UNAVAILABLE = 'SERVICE_UNAVAILABLE',
  EXTERNAL_API_ERROR = 'EXTERNAL_API_ERROR',
  EXTERNAL_SERVICE = 'EXTERNAL_SERVICE',
  DATABASE_ERROR = 'DATABASE_ERROR',

  // Erros de operação
  OPERATION_NOT_ALLOWED = 'OPERATION_NOT_ALLOWED',
  CONFLICT_ERROR = 'CONFLICT_ERROR',

  // Erros de limite
  RATE_LIMIT_ERROR = 'RATE_LIMIT_ERROR',
  QUOTA_EXCEEDED = 'QUOTA_EXCEEDED',

  // Erros de configuração
  CONFIGURATION_ERROR = 'CONFIGURATION_ERROR',

  // Erros específicos da aplicação
  EXCEL_OPERATION_ERROR = 'EXCEL_OPERATION_ERROR',
  EXCEL_PARSE_ERROR = 'EXCEL_PARSE_ERROR',
  AI_REQUEST_ERROR = 'AI_REQUEST_ERROR',
  AI_RESPONSE_ERROR = 'AI_RESPONSE_ERROR',
  NETWORK_ERROR = 'NETWORK_ERROR',

  // Erro genérico
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
  INTERNAL = 'INTERNAL',
  API_ERROR = 'API_ERROR',
}

// Interface que define a estrutura normalizada de erro
interface NormalizedError {
  message: string;
  type: ErrorType;
  details: Record<string, unknown> | undefined; // Tornando compatible com exactOptionalPropertyTypes
  id: string;
  status: number;
  originalError?: unknown; // Propriedade que armazena o erro original
}

// Implementação da função normalizeError com tipagem correta
function normalizeError(error: unknown, context?: string): NormalizedError {
  const normalizedError: NormalizedError = {
    message: error instanceof Error ? error.message : String(error),
    type: ErrorType.UNKNOWN_ERROR,
    details: context ? { context } : undefined, // Tornando details opcional
    id: `err_${Date.now()}`,
    status: 500,
    originalError: error, // Salvando o erro original
  };
  return normalizedError;
}

// Define UserFriendlyMessages as a simple Record
const UserFriendlyMessages: Record<ErrorType, string> = {
  [ErrorType.UNKNOWN_ERROR]: 'Ocorreu um erro inesperado. Por favor, tente novamente.',
  [ErrorType.EXTERNAL_API_ERROR]:
    'Ocorreu um erro ao comunicar com um serviço externo. Tente novamente mais tarde.',
  [ErrorType.EXTERNAL_SERVICE]: 'Um serviço externo está indisponível no momento.',
  [ErrorType.DATABASE_ERROR]: 'Erro ao acessar os dados. Tente novamente mais tarde.',
  [ErrorType.AUTHENTICATION_ERROR]: 'Erro de autenticação. Por favor, faça login novamente.',
  [ErrorType.UNAUTHORIZED]: 'Erro de autenticação. Por favor, faça login novamente.',
  [ErrorType.AUTHORIZATION_ERROR]: 'Você não tem permissão para acessar este recurso.',
  [ErrorType.FORBIDDEN]: 'Você não tem permissão para acessar este recurso.',
  [ErrorType.NOT_FOUND]: 'O recurso solicitado não foi encontrado.',
  [ErrorType.VALIDATION_ERROR]: 'Dados inválidos. Por favor, verifique as informações fornecidas.',
  [ErrorType.SERVICE_UNAVAILABLE]:
    'Serviço temporariamente indisponível. Por favor, tente novamente mais tarde.',
  [ErrorType.INVALID_INPUT]: 'Entrada inválida. Verifique os dados enviados.',
  [ErrorType.INVALID_REQUEST]: 'Requisição inválida. Verifique os parâmetros enviados.',
  [ErrorType.OPERATION_NOT_ALLOWED]:
    'Operação não permitida. Verifique as permissões e tente novamente.',
  [ErrorType.CONFLICT_ERROR]:
    'Conflito ao processar a solicitação. Verifique os dados e tente novamente.',
  [ErrorType.RATE_LIMIT_ERROR]: 'Muitas solicitações. Por favor, tente novamente mais tarde.',
  [ErrorType.QUOTA_EXCEEDED]:
    'Limite de requisições excedido. Por favor, tente novamente mais tarde.',
  [ErrorType.CONFIGURATION_ERROR]:
    'Erro de configuração do sistema. Entre em contato com o suporte.',
  [ErrorType.ALREADY_EXISTS]: 'Recurso já existe. Verifique os dados e tente novamente.',
  [ErrorType.API_ERROR]: 'Ocorreu um erro ao processar sua solicitação na API.',
  [ErrorType.INTERNAL]: 'Ocorreu um erro interno no servidor. Tente novamente mais tarde.',
  [ErrorType.EXCEL_OPERATION_ERROR]:
    'Erro ao executar operação no Excel. Verifique os dados e tente novamente.',
  [ErrorType.EXCEL_PARSE_ERROR]:
    'Erro ao processar o arquivo Excel. Verifique se o formato é válido.',
  [ErrorType.AI_REQUEST_ERROR]: 'Erro ao processar requisição para o serviço de IA.',
  [ErrorType.AI_RESPONSE_ERROR]:
    'Erro ao receber resposta do serviço de IA. Tente novamente mais tarde.',
  [ErrorType.NETWORK_ERROR]: 'Erro de conexão de rede. Verifique sua internet e tente novamente.',
};

// Define ErrorRetryTimes as a simple Partial Record
const ErrorRetryTimes: Partial<Record<ErrorType, number>> = {
  [ErrorType.EXTERNAL_API_ERROR]: 2000,
  [ErrorType.EXTERNAL_SERVICE]: 3000,
  [ErrorType.DATABASE_ERROR]: 5000,
  [ErrorType.RATE_LIMIT_ERROR]: 30000,
  [ErrorType.NETWORK_ERROR]: 1500,
  [ErrorType.AI_RESPONSE_ERROR]: 3000,
  [ErrorType.SERVICE_UNAVAILABLE]: 5000,
};

// Interface para erros retornados da API
export interface ApiError {
  error: string;
  type?: ErrorType;
  details?: Record<string, unknown>;
  id?: string;
  status?: number;
  retryAfter?: number;
}

// Interface para resultado do hook
export interface ErrorHandlerResult {
  handleError: (error: unknown, customMessage?: string) => void;
  clearError: () => void;
  error: ApiError | null;
  isLoading: boolean;
  setIsLoading: (loading: boolean) => void;
  retry: () => void;
  canRetry: boolean;
  retryCount: number;
  retryIn: number | null;
}

/**
 * Hook para tratamento de erros no cliente
 * @param options Opções de configuração
 */
export function useErrorHandler(
  options: {
    maxRetries?: number;
    retryableErrors?: ErrorType[];
    onRetry?: () => Promise<void>;
    autoRetryNetworkErrors?: boolean;
  } = {}
): ErrorHandlerResult {
  const [error, setError] = useState<ApiError | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [retryCount, setRetryCount] = useState<number>(0);
  const [retryIn, setRetryIn] = useState<number | null>(null);
  const { toast } = useToast();

  const retryTimerRef = useRef<NodeJS.Timeout | null>(null);
  const retryCallbackRef = useRef(options.onRetry);

  // Atualizar referência ao callback de retry quando mudar
  useEffect(() => {
    retryCallbackRef.current = options.onRetry;
  }, [options.onRetry]);

  // Limpar o temporizador quando o componente for desmontado
  useEffect(() => {
    return () => {
      if (retryTimerRef.current) {
        clearTimeout(retryTimerRef.current);
      }
    };
  }, []);

  // Array de tipos de erro que podem ser repetidos
  const defaultRetryableErrors = [
    ErrorType.NETWORK_ERROR,
    ErrorType.AI_REQUEST_ERROR,
    ErrorType.API_ERROR,
    ErrorType.RATE_LIMIT_ERROR,
  ];

  const retryableErrors = options.retryableErrors || defaultRetryableErrors;
  const maxRetries = options.maxRetries || 3;
  const autoRetryNetworkErrors = options.autoRetryNetworkErrors ?? false;

  // Determinar se podemos tentar novamente
  const canRetry = Boolean(
    error && retryableErrors.includes(error.type as ErrorType) && retryCount < maxRetries
  );

  // Função para diminuir o temporizador de retry
  const startRetryCountdown = useCallback((seconds: number) => {
    setRetryIn(seconds);

    const updateTimer = () => {
      setRetryIn(prev => {
        if (prev === null || prev <= 0) {
          return null;
        }

        const newValue = prev - 1;

        if (newValue <= 0) {
          return null;
        }

        retryTimerRef.current = setTimeout(updateTimer, 1000);
        return newValue;
      });
    };

    retryTimerRef.current = setTimeout(updateTimer, 1000);
  }, []);

  // Função para extrair Retry-After de respostas HTTP
  const getRetryAfter = (response: Response): number | null => {
    const retryAfter = response.headers.get('retry-after');
    if (!retryAfter) return null;

    // Pode ser um número de segundos ou uma data
    if (/^\d+$/.test(retryAfter)) {
      return parseInt(retryAfter, 10);
    }

    // Tenta interpretar como data
    try {
      const retryDate = new Date(retryAfter);
      const now = new Date();
      const diffSeconds = Math.ceil((retryDate.getTime() - now.getTime()) / 1000);
      return diffSeconds > 0 ? diffSeconds : null;
    } catch {
      return null;
    }
  };

  // Refs para evitar dependências circulares
  const handleErrorRef = useRef<(error: unknown, message?: string) => void>();
  const clearErrorRef = useRef<() => void>();

  // Função para limpar o erro
  const clearError = useCallback(() => {
    setError(null);
    setRetryCount(0);
    setRetryIn(null);

    // Limpar timer existente se houver
    if (retryTimerRef.current) {
      clearTimeout(retryTimerRef.current);
      retryTimerRef.current = null;
    }
  }, []);

  // Manter a referência atualizada
  useEffect(() => {
    clearErrorRef.current = clearError;
  }, [clearError]);

  // Função para tentar novamente
  const retry = useCallback(async () => {
    if (!canRetry || !retryCallbackRef.current) return;

    // Limpar timer existente
    if (retryTimerRef.current) {
      clearTimeout(retryTimerRef.current);
      retryTimerRef.current = null;
    }

    setIsLoading(true);
    setRetryCount(prev => prev + 1);
    setRetryIn(null);

    try {
      await retryCallbackRef.current();
      // Usar a ref para evitar dependência circular
      if (clearErrorRef.current) clearErrorRef.current();
    } catch (newError) {
      // Usar a ref para evitar dependência circular
      if (handleErrorRef.current) handleErrorRef.current(newError, 'Falha ao tentar novamente');
    } finally {
      setIsLoading(false);
    }
  }, [canRetry, retryCallbackRef]);

  // Função para tentar automaticamente
  const autoRetry = useCallback(
    (delaySeconds?: number) => {
      const delay = delaySeconds || 3; // Padrão 3 segundos

      startRetryCountdown(delay);

      // Definir um timer para tentar novamente
      retryTimerRef.current = setTimeout(() => {
        retry();
      }, delay * 1000);
    },
    [retry, startRetryCountdown]
  );

  // Função para tratar erros
  const handleError = useCallback(
    (error: unknown, customMessage?: string) => {
      // Reset loading state
      setIsLoading(false);

      // Limpar timer existente se houver
      if (retryTimerRef.current) {
        clearTimeout(retryTimerRef.current);
        retryTimerRef.current = null;
      }

      // Normalizar erro usando a função da lib/errors.ts
      const normalizedError = normalizeError(error);

      // Configurar o erro para o estado
      // Garantindo que normalizedError.type é uma chave válida de UserFriendlyMessages
      const errorType = normalizedError.type || ErrorType.UNKNOWN_ERROR;
      const errorMessage = UserFriendlyMessages[errorType] || 'Erro desconhecido';

      // Criar o objeto ApiError apenas com as propriedades necessárias
      const apiError: ApiError = {
        error: customMessage || normalizedError.message || errorMessage,
        type: errorType,
      };

      // Adicionar as propriedades opcionais apenas se estiverem definidas
      if (normalizedError.details) {
        apiError.details = normalizedError.details;
      }

      if (normalizedError.id) {
        apiError.id = normalizedError.id;
      }

      if (normalizedError.status) {
        apiError.status = normalizedError.status;
      }

      setError(apiError);

      // Mostrar toast de erro
      // Usando o errorType já validado acima
      toast({
        title: UserFriendlyMessages[errorType] || 'Erro',
        description: customMessage || normalizedError.message,
        variant: 'destructive',
      });

      // Auto-retry para erros de rede se configurado
      if (
        autoRetryNetworkErrors &&
        normalizedError.type === ErrorType.NETWORK_ERROR &&
        retryCount < maxRetries
      ) {
        const retryTime = ErrorRetryTimes[ErrorType.NETWORK_ERROR] || 3000;
        autoRetry(Math.floor(retryTime / 1000));
      }

      // Verificar se é um erro de rate limit e tem retry-after
      if (
        normalizedError.type === ErrorType.RATE_LIMIT_ERROR &&
        normalizedError.originalError instanceof Response
      ) {
        const retryAfter = getRetryAfter(normalizedError.originalError);
        if (retryAfter && retryAfter > 0) {
          apiError.retryAfter = retryAfter;
          autoRetry(retryAfter);
        }
      }
    },
    [autoRetry, toast, retryCount, maxRetries, autoRetryNetworkErrors]
  );

  // Manter a referência atualizada
  useEffect(() => {
    handleErrorRef.current = handleError;
  }, [handleError]);

  return {
    handleError,
    clearError,
    error,
    isLoading,
    setIsLoading,
    retry,
    canRetry,
    retryCount,
    retryIn,
  };
}

/**
 * Função auxiliar para envolver uma função assíncrona com tratamento de erro
 */
export function withErrorHandling<T>(
  fn: (...args: unknown[]) => Promise<T>,
  errorHandler: (error: unknown, customMessage?: string) => void,
  setIsLoading?: (loading: boolean) => void,
  customErrorMessage?: string
) {
  return async (...args: unknown[]): Promise<T | undefined> => {
    if (setIsLoading) setIsLoading(true);

    try {
      const result = await fn(...args);
      return result;
    } catch (error) {
      errorHandler(error, customErrorMessage);
      return undefined;
    } finally {
      if (setIsLoading) setIsLoading(false);
    }
  };
}
